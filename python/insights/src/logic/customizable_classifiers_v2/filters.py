from common.clients.client_factory import get_copilot_v3_client
from copilot_client_v3 import CustomizableClassifierV2, FilterRule, Submission
from infrastructure_common.logging import get_logger
from static_common.enums.classification import FilterRuleType
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>


def can_run_classifier_on_sub(
    classifier: CustomizableClassifierV2,
    submission: Submission,
    organization_id: int,
    log: BoundLogger = get_logger(),
    copilot_client_v3=get_copilot_v3_client(),
) -> bool:
    log = log.bind(classifier_id=classifier.id, submission_id=submission.id)
    partial_results = []
    for filter_rule in classifier.filter_rules:
        match filter_rule.filter_type:
            case FilterRuleType.NAICS:
                partial_results.append(run_naics_rule_on_submission(filter_rule, submission, log))
            case FilterRuleType.COVERAGE:
                partial_results.append(run_coverages_rule_on_submission(filter_rule, submission, organization_id, log, copilot_client_v3))
            case _:
                log.error("Unknown filter rule type", filter_rule_type=filter_rule.filter_type)

    return all(partial_results)


def run_coverages_rule_on_submission(
    rule: FilterRule,
    submission: Submission,
    organization_id: int,
    log: BoundLogger = get_logger(),
    copilot_client_v3=get_copilot_v3_client(),
) -> bool:
    try:
        log = log.bind(rule_id=rule.id, rule_values=rule.values)
        if not rule.filter_type == FilterRuleType.COVERAGE:
            log.error("Incompatible filter rule, cannot run the filter")
            return False

        coverages = copilot_client_v3.get_coverages(org_id=organization_id)
        coverages_by_id = {c.id: c for c in coverages}

        for value in rule.values:
            split = value.split(":")

            if len(split) == 1:
                coverage_id = split[0]
                coverage_type = None
            elif len(split) == 2:
                coverage_id = split[0]
                coverage_type = split[1].lower()
            else:
                log.warning(
                    "Invalid coverage filter rule value, cannot run the filter",
                    rule_value=value,
                )
                continue

            if not (coverage := coverages_by_id.get(coverage_id)):
                log.warning(
                    "Coverage not found, cannot run the filter for value",
                    rule_value=value,
                )
                continue

            coverage_name = coverage.name.lower()

            if any(
                sc.coverage.name.lower() == coverage_name and (sc.coverage_type.lower() if sc.coverage_type else None) == coverage_type
                for sc in submission.coverages or []
            ):
                if rule.negated:
                    log.warning(
                        "Submission coverage is not allowed, cannot run classifier for the submission",
                        coverage_name=coverage_name,
                        coverage_type=coverage_type,
                    )
                    return False
                else:
                    log.info(
                        "Filter condition met for submission coverage",
                        coverage_name=coverage_name,
                        coverage_type=coverage_type,
                    )
                    return True

        # if the rule is negated and we didn't find any coverage matching the rule then we can run the classifier
        # if the rule isn't negated and we didn't find any coverage matching the rule then we can't run the classifier
        return rule.negated
    except:
        log.exception(
            "Failed to run coverage rule on submission",
            submission_id=submission.id,
            rule_id=rule.id,
        )
        return False


def run_naics_rule_on_submission(rule: FilterRule, submission: Submission, log: BoundLogger = get_logger()) -> bool:
    try:
        log = log.bind(rule_id=rule.id, rule_values=rule.values)
        if not rule.filter_type == FilterRuleType.NAICS:
            log.error("Incompatible filter rule, cannot run the filter")
            return False

        if any(submission.primary_naics_code.startswith(val) for val in rule.values):
            if rule.negated:
                log.warning(
                    "Submission NAICS code is not allowed, cannot run classifier for the submission",
                    naics_code=submission.primary_naics_code,
                )
                return False
            else:
                log.info(
                    "Filter condition met for submission NAICS code",
                    naics_code=submission.primary_naics_code,
                )
                return True

        # if the rule is negated and we didn't find any NAICS code matching the rule then we can run the classifier
        # if the rule isn't negated and we didn't find any NAICS code matching the rule then we can't run the classifier
        return rule.negated
    except Exception as e:
        log.exception(
            "Failed to run NAICS rule on submission",
            submission_id=submission.id,
            rule_id=rule.id,
            error=str(e),
        )
        return False
