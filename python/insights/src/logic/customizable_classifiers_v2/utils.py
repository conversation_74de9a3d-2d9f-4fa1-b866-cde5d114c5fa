from uuid import UUID

from common.factories.observations import FACT_TYPE_ID_TO_FACT_FACTORY
from datascience_common.factories import ClassProbabilitiesFactory
from datascience_common.logic.customizable_classifiers import (
    get_predicted_class_and_probability_from_class_probabilities,
)
from datascience_common.models.classifications import BinaryClassification
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)
from datascience_common.models.customizable_classifiers.tasks import (
    ClassifierTaskOutput,
    ClassifierTaskOutputValue,
)
from datascience_common.models.explanations import ExplanationBuilder
from datascience_common.utils.dataset import get_document_object
from facts_client.model.inference_source import InferenceSource
from facts_client.model.observation import Observation
from static_common.enums.classification import (
    BinaryClassificationClassType,
    ClassifierOutputType,
    ClassifierUnits,
)
from static_common.enums.explanation import ExplanationTypeID
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID
from static_common.enums.fields import FieldType
from static_common.enums.parent import ParentType
from static_common.enums.probability import ProbabilityInterpretation
from static_common.enums.source_types import SourceTypeID
from static_common.enums.units import Units
from static_common.mappings.probability import get_probability_interpretation
from static_common.models.file_onboarding import (
    CustomizableClassifierEvidence,
    ResolvedDataField,
    ResolvedDataValue,
)

from src.models.customizable_classifiers_v2 import ClassifierData


def _classifier_output_to_field(
    classifier_data: ClassifierData,
    output: ClassifierTaskOutput,
    resources: CustomizableClassifierPipelineResources,
) -> ResolvedDataField | None:
    fact_subtype = (
        next(
            (fs for fs in resources.fact_subtypes if fs.id == classifier_data.fact_subtype_id),
            None,
        )
        if classifier_data.fact_subtype_id
        else None
    )

    value_type, unit = _get_field_type_and_unit(classifier_data)
    field = ResolvedDataField(
        name=fact_subtype.display_name if fact_subtype else classifier_data.classifier_name,
        values=[],
        fact_subtype_id=FactSubtypeID.try_parse_str(fact_subtype.id if fact_subtype else classifier_data.fact_subtype_id),
        value_type=value_type,
        unit=unit,
    )

    for output_value in output.values:
        if value_type == FieldType.BOOLEAN and output_value.probability is not None:
            # For boolean classifiers, we need to convert to representation including probability
            class_id = BinaryClassificationClassType.YES if output_value.value else BinaryClassificationClassType.NO
            binary_classification = BinaryClassification(
                class_id=class_id,
                probability=output_value.probability,
                explanation_builders=[],
            )
            class_probabilities = ClassProbabilitiesFactory.create_from_binary_classification(binary_classification)
            (
                predicted_class,
                probability,
            ) = get_predicted_class_and_probability_from_class_probabilities(class_probabilities)
            probability_interpretation = get_probability_interpretation(predicted_class, probability)
            if probability_interpretation == ProbabilityInterpretation.UNKNOWN:
                continue
            output_value.value = probability_interpretation.value
        evidences = [
            CustomizableClassifierEvidence(
                document_snippet=e.document_snippet,
                observed_value=e.observed_value,
                snippet_bbox=e.snippet_bbox,
                page=e.page,
                confidence=e.confidence,
                label=classifier_data.classifier_name,
                classifier_id=classifier_data.classifier_id,
            )
            for e in output_value.file_evidences
        ]
        value = ResolvedDataValue(
            value=output_value.value,
            evidences=evidences,
        )
        field.values.append(value)

    if field.values:
        return field
    else:
        return None


def _classifier_output_to_observation_and_explanation_builders(
    classifier_data: ClassifierData,
    output_value: ClassifierTaskOutputValue,
    resources: CustomizableClassifierPipelineResources,
) -> tuple[Observation | None, list[ExplanationBuilder]]:
    fact_subtype = (
        next(
            (fs for fs in resources.fact_subtypes if fs.id == classifier_data.fact_subtype_id),
            None,
        )
        if classifier_data.fact_subtype_id
        else None
    )
    if not fact_subtype:
        resources.log.warning("No fact subtype found for classifier data", classifier_data=classifier_data)
        return None, []

    if not (factory := FACT_TYPE_ID_TO_FACT_FACTORY.get(fact_subtype.fact_type_id)):
        resources.log.warning(
            "No factory found for fact type, cannot create observation",
            fact_subtype_id=fact_subtype.fact_type_id,
        )
        return None, []

    value = output_value.value
    if fact_subtype.fact_type_id == FactTypeID.INTEGER:
        # we always return float for numeric classifiers, but the fact can be integer and this doesn't support float values
        value = int(value)

    observation = factory.create_from_args(
        parent_type=ParentType.BUSINESS,
        parent_id=UUID(classifier_data.input_id),
        fact_subtype_id=classifier_data.fact_subtype_id,
        value=value,
        source=InferenceSource(
            source_type_id=SourceTypeID.INFERENCE.value,
            model_name="Customizable Classifier",
            model_version=f"{classifier_data.classifier_id}",
        ),
        organization_id=classifier_data.task_input.organization_id,
        probability=output_value.probability,
    )

    explanation_builders = []
    if classifier_data.output_type == ClassifierOutputType.BOOLEAN:
        class_id = BinaryClassificationClassType.YES if value else BinaryClassificationClassType.NO
        for document_evidence in output_value.document_evidences:
            if not (doc_type := get_document_object(document_evidence.document_type)):
                continue
            document = doc_type(
                id=document_evidence.document_id,
                document_type_id=document_evidence.document_type,
            )
            explanation_builders.append(
                ExplanationBuilder(
                    document=document,
                    class_id=class_id,
                    explanation_type=ExplanationTypeID.DOCUMENT_SNIPPET.value,
                    formatted_body=document_evidence.formatted_body,
                    snippet=document_evidence.document_snippet,
                    probability=document_evidence.confidence,
                )
            )

    return observation, explanation_builders


def _get_field_type_and_unit(
    classifier: ClassifierData,
) -> tuple[FieldType, Units]:
    if classifier.output_unit == ClassifierUnits.USD:
        return FieldType.NUMBER, Units.USD

    field_type = None
    unit = _get_matching_unit(classifier)
    if classifier.output_type == ClassifierOutputType.NUMERIC:
        field_type = FieldType.NUMBER
    elif classifier.output_type == ClassifierOutputType.DATE:
        field_type = FieldType.DATETIME
    elif classifier.output_type in [
        ClassifierOutputType.TAGS,
        ClassifierOutputType.TEXT,
    ]:
        field_type = FieldType.TEXT
    elif classifier.output_type == ClassifierOutputType.BOOLEAN:
        field_type = FieldType.BOOLEAN

    return field_type, unit


def _get_matching_unit(classifier: ClassifierData) -> Units | None:
    if classifier.output_unit:
        return Units.try_parse_str(classifier.output_unit)
