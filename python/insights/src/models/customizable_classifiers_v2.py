from uuid import UUID

from pydantic import BaseModel
from static_common.enums.classification import InputProcessingType


class ClassifierTaskInput(BaseModel):
    task_definition_id: str
    organization_id: int
    submission_id: str | None = None
    file_id: str | None = None
    business_id: str | None = None
    context: dict | None = None


class ClassifierData(BaseModel):
    classifier_id: str
    fact_subtype_id: str | None
    classifier_name: str
    task_definition_id: str
    output_type: str
    output_unit: str | None
    input_processing_types: list[InputProcessingType]
    input_id: str | None = None
    task_input: ClassifierTaskInput | None = None


class FirstPartyInsightsPayload(BaseModel):
    submission_id: UUID
    organization_id: int
    classifiers: list[ClassifierData]
    tasks_outputs: list[dict | None] | None = None


class ThirdPartyInsightsPayload(BaseModel):
    business_id: UUID
    classifiers: list[ClassifierData]
    tasks_outputs: list[dict | None] | None = None
