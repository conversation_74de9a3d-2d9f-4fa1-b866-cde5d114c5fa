from collections import defaultdict
from uuid import UUID

from common.clients.facts import AnyDocument
from copilot_client_v3 import (
    ClassifierTaskDefinition,
    CustomizableClassifierV2,
    File,
    Submission,
)
from datascience_common.logic.customizable_classifiers_v2.dataset_providers.factory import (
    get_dataset_creator,
)
from datascience_common.logic.facts_api import write_classification_to_facts_api
from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
    InputSource,
)
from datascience_common.models.customizable_classifiers.dataset import (
    CustomizableClassifierDataset,
)
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)
from datascience_common.models.customizable_classifiers.tasks import (
    ClassifierTaskOutput,
)
from datascience_common.models.dataset import EntityDataset
from datascience_common.utils.dataset import create_entity_dataset
from entity_resolution_service_client_v3 import Entity
from facts_client.model.multi_label_facts import MultiLabelFacts
from infrastructure_common.logging import bind_lambda_logging_context
from more_itertools import flatten
from sentry_sdk.integrations.serverless import serverless_function
from static_common.enums.classification import InputProcessingType
from static_common.enums.file_type import FileType
from static_common.enums.parent import ParentType
from static_common.models.file_onboarding import OnboardedFile
from static_common.models.first_party_insights import FirstPartyInsightsBulkRequest
from static_common.models.insights import BulkThirdPartyInsightsRequest
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema

from src.logic.customizable_classifiers_v2.filters import can_run_classifier_on_sub
from src.logic.customizable_classifiers_v2.utils import (
    _classifier_output_to_field,
    _classifier_output_to_observation_and_explanation_builders,
)
from src.logic.file_insights import assign_unassigned_fields
from src.models.customizable_classifiers_v2 import (
    ClassifierData,
    ClassifierTaskInput,
    FirstPartyInsightsPayload,
    ThirdPartyInsightsPayload,
)

REDIS_EXPIRATION_SECONDS = 60 * 60  # 1 hour


@bind_lambda_logging_context
@serverless_function
def prepare_first_party_worker(event, context=None):
    request = FirstPartyInsightsBulkRequest.model_validate(event)

    resources = CustomizableClassifierPipelineResources()
    resources.log = resources.log.bind(submission_id=request.submission_id, organization_id=request.organization_id)

    submission: Submission = resources.copilot_client.get_submission(
        submission_id=request.submission_id,
        expand=["files", "files.s3_key", "coverages"],
    )

    if not submission:
        resources.log.warning("No submission found, cannot infer first party insights")
        return FirstPartyInsightsPayload(
            submission_id=request.submission_id,
            organization_id=request.organization_id,
            classifiers=[],
        ).model_dump(mode="json")

    files_by_input_type = defaultdict(list)

    for f in submission.files:
        if not (file_type := FileType.try_parse_str(f.file_type)):
            resources.log.warning(
                "File type not found, cannot infer first party insights",
                file_id=f.id,
                file_type=f.file_type,
            )
            continue

        if file_type == FileType.CUSTOM and (input_type := FileType.try_parse_str(f.custom_classification)):
            files_by_input_type[input_type.value].append(f)
        else:
            files_by_input_type[file_type.value].append(f)

    resources.log.info("Files by input type", files_by_input_type=files_by_input_type)

    classifiers = resources.copilot_client.get_customizable_classifiers_v2(
        input_types=list(files_by_input_type.keys()),
        org_id=request.organization_id,
        pds_only=True,
        active_only=True,
    )
    classifiers = [c for c in classifiers if can_run_classifier_on_sub(c, submission, request.organization_id)]

    classifiers_data_by_input = _get_classifiers_data_by_input(classifiers, resources)

    resources.log.info(
        "Classifiers data",
        classifiers_data_by_input=classifiers_data_by_input,
        classifiers=classifiers,
    )

    cached_datasets = set()
    classifiers_with_task_input = []
    for input_type, classifiers in classifiers_data_by_input.items():
        for file in files_by_input_type[input_type]:
            resources.log.info(
                "File for classifiers",
                file=file,
                input_type=input_type,
                classifiers=classifiers,
            )
            input_processing_types = set(flatten([c.input_processing_types for c in classifiers]))
            file_datasets = _create_file_datasets(
                file,
                list(input_processing_types),
                resources,
            )
            if not file_datasets:
                resources.log.warning("No datasets created for file, skipping", file_id=file.id)
                continue
            for classifier in classifiers:
                input_processing_types_str = "_".join([it.name for it in classifier.input_processing_types])
                datasets_key = f"{file.id}_{input_processing_types_str}"
                if datasets_key not in cached_datasets:
                    datasets = [
                        ds
                        for input_processing_type, ds in file_datasets.items()
                        if input_processing_type in classifier.input_processing_types
                    ]
                    resources.cache_client.add_to_cache(datasets_key, datasets, REDIS_EXPIRATION_SECONDS)
                    cached_datasets.add(datasets_key)
                classifier_with_task_input = classifier.model_copy(
                    update={
                        "input_id": file.id,
                        "task_input": ClassifierTaskInput(
                            task_definition_id=classifier.task_definition_id,
                            submission_id=submission.id,
                            organization_id=request.organization_id,
                            file_id=file.id,
                            context={"datasets_key": datasets_key},
                        ),
                    }
                )
                classifiers_with_task_input.append(classifier_with_task_input)

    payload = FirstPartyInsightsPayload(
        submission_id=request.submission_id,
        organization_id=request.organization_id,
        classifiers=classifiers_with_task_input,
    )
    return payload.model_dump(mode="json")


@bind_lambda_logging_context
@serverless_function
def prepare_third_party_worker(event, context=None):
    request = BulkThirdPartyInsightsRequest.model_validate(event)

    resources = CustomizableClassifierPipelineResources()
    resources.log = resources.log.bind(business_id=request.business_id)

    dataset = create_entity_dataset(
        entity_id=request.business_id,
        ers_client=resources.ers_client,
        facts_client=resources.facts_client,
    )

    if not dataset:
        resources.log.warning("No entity dataset created, cannot infer third party insights")
        return request.model_dump(mode="json")

    reports = resources.copilot_client.get_reports_including_business(
        business_id=request.business_id,
        expand=[
            "organization_id",
            "submissions.coverages",
            "submissions.primary_naics_code",
        ],
    )

    submissions_by_org_id = defaultdict(list)
    for report in reports:
        if len(report.submissions) == 0:
            continue

        submissions_by_org_id[report.organization_id].append(report.submissions[0])

    resources.log.info(
        "Reports for business",
        reports=reports,
        submissions_by_org_id=submissions_by_org_id,
    )
    documents_by_type = defaultdict(list)
    for d in dataset.documents:
        documents_by_type[d.document_type_id].append(d)

    classifiers_with_task_input = []
    cached_datasets = set()
    for org_id, submissions in submissions_by_org_id.items():
        classifiers = resources.copilot_client.get_customizable_classifiers_v2(
            org_id=org_id,
            input_types=list(documents_by_type.keys()),
            pds_only=False,
            active_only=True,
        )
        classifiers = [c for c in classifiers if any(can_run_classifier_on_sub(c, sub, org_id) for sub in submissions)]

        classifiers_data_by_input = _get_classifiers_data_by_input(classifiers, resources)
        resources.log.info(
            "Classifiers for business documents",
            classifiers=classifiers,
            classifiers_data_by_input=classifiers_data_by_input,
            documents_by_type=documents_by_type,
        )
        for input_type, classifiers in classifiers_data_by_input.items():
            if input_type not in documents_by_type:
                continue
            datasets_key = f"{dataset.entity.id}_{input_type}"
            if datasets_key not in cached_datasets:
                datasets = _create_entity_datasets(dataset.entity, documents_by_type[input_type])
                resources.cache_client.add_to_cache(datasets_key, datasets, REDIS_EXPIRATION_SECONDS)
                cached_datasets.add(datasets_key)

            for classifier in classifiers:
                classifier_with_task_input = classifier.model_copy(
                    update={
                        "input_id": str(request.business_id),
                        "task_input": ClassifierTaskInput(
                            task_definition_id=classifier.task_definition_id,
                            business_id=str(request.business_id),
                            organization_id=org_id,
                            context={"datasets_key": datasets_key},
                        ),
                    }
                )
                classifiers_with_task_input.append(classifier_with_task_input)

    payload = ThirdPartyInsightsPayload(business_id=request.business_id, classifiers=classifiers_with_task_input)
    return payload.model_dump(mode="json")


@bind_lambda_logging_context
@serverless_function
def merge_and_process_results(event, context=None):
    payload = FirstPartyInsightsPayload.model_validate(event)
    resources = CustomizableClassifierPipelineResources()

    resources.log = resources.log.bind(submission_id=payload.submission_id, organization_id=payload.organization_id)

    new_fields_by_file_id = defaultdict(list)
    for classifier_data, output in zip(payload.classifiers, payload.tasks_outputs):
        if not output or "error" in output:
            resources.log.warning(
                "No output found, cannot merge and process results for classifier",
                classifier_data=classifier_data,
            )
            continue
        file_id = classifier_data.task_input.file_id
        task_output = ClassifierTaskOutput.model_validate(output)
        if new_field := _classifier_output_to_field(classifier_data, task_output, resources):
            new_fields_by_file_id[file_id].append(new_field)
    for file_id, new_fields in new_fields_by_file_id.items():
        processed_data = _get_processed_data(file_id, resources) or OnboardedFile(files=[file_id])
        processed_data.fields.extend(new_fields)
        assign_unassigned_fields(processed_data, new_fields)
        processed_data_dict = LeanOnboardedFileSchema().dump(processed_data)
        resources.copilot_client.push_processed_file(file_id=file_id, data=processed_data_dict)

    return {file_id: [f.name for f in fields] for file_id, fields in new_fields_by_file_id.items()}


@bind_lambda_logging_context
@serverless_function
def upload_classifier_results_to_facts(event, context=None):
    payload = ThirdPartyInsightsPayload.model_validate(event)
    resources = CustomizableClassifierPipelineResources()

    resources.log = resources.log.bind(business_id=payload.business_id)

    for classifier_data, output in zip(payload.classifiers, payload.tasks_outputs):
        if not output or "error" in output:
            resources.log.warning(
                "No output found, cannot merge and process results for classifier",
                classifier_data=classifier_data,
            )
            continue

        task_output = ClassifierTaskOutput.model_validate(output)
        for output_value in task_output.values:
            (
                observation,
                explanation_builders,
            ) = _classifier_output_to_observation_and_explanation_builders(
                classifier_data=classifier_data,
                output_value=output_value,
                resources=resources,
            )
            if observation:
                try:
                    write_classification_to_facts_api(observation, explanation_builders)
                except:
                    resources.log.exception("Failed to write to facts")

    org_ids = {c.task_input.organization_id for c in payload.classifiers}
    for org_id in org_ids:
        multi_label_facts = MultiLabelFacts(
            parent_id=str(payload.business_id),
            parent_type=ParentType.BUSINESS.value,
            organization_id=org_id,
        )
        resources.facts_client.create_multi_label_facts(multi_label_facts)

    return event


def _get_classifiers_data_by_input(
    classifiers: list[CustomizableClassifierV2],
    resources: CustomizableClassifierPipelineResources,
) -> dict[str, list[ClassifierData]]:
    classifiers_data_by_input = defaultdict(list)
    for classifier in classifiers:
        id_to_config_map = {config.id: config for config in classifier.active_version.configs}

        for task_definition in classifier.active_version.classifier_task_definitions:
            task_definition: ClassifierTaskDefinition
            config = id_to_config_map.get(task_definition.classifier_config_id)
            if not config:
                continue
            input_processing_types = set()
            for config_version in config.active_versions:
                if not (input_processing_type := InputProcessingType.try_parse_str(config_version.input_processing_type)):
                    resources.log.error(
                        "Unsupported input_processing_type",
                        input_processing_type=config_version.input_processing_type,
                    )
                    continue
                input_processing_types.add(input_processing_type)
            classifiers_data_by_input[config.input_type].append(
                ClassifierData(
                    classifier_id=classifier.id,
                    fact_subtype_id=classifier.fact_subtype_id,
                    classifier_name=classifier.name,
                    task_definition_id=task_definition.task_definition_id,
                    input_processing_types=list(input_processing_types),
                    output_unit=classifier.output_unit,
                    output_type=classifier.output_type,
                )
            )
    return classifiers_data_by_input


def _build_file_processing_context(file: File, resources: CustomizableClassifierPipelineResources) -> CopilotFileProcessingContext:
    processed_data = _get_processed_data(file.id, resources)
    file_classification = file.custom_classification if file.file_type == FileType.CUSTOM else file.file_type

    return CopilotFileProcessingContext(
        input_id=UUID(file.id),
        file_classification=file_classification,
        organization_id=file.organization_id,
        submission_id=UUID(file.submission_id),
        file=file,
        input_source=InputSource.COPILOT_FILE,
        processed_data=processed_data,
    )


def _create_file_datasets(
    file: File,
    input_processing_types: list[InputProcessingType],
    resources: CustomizableClassifierPipelineResources,
) -> dict[InputProcessingType, CustomizableClassifierDataset]:
    datasets = {}
    context = _build_file_processing_context(file, resources)
    for input_processing_type in input_processing_types:
        if not (dataset_creator := get_dataset_creator(input_processing_type, context)):
            continue

        if not (dataset := dataset_creator.create_dataset(context, input_processing_type, resources)):
            continue
        dataset.dataset.make_serializable()
        datasets[input_processing_type] = dataset
    return datasets


def _get_processed_data(file_id: str, resources: CustomizableClassifierPipelineResources) -> OnboardedFile | None:
    processed_file = resources.copilot_client.get_processed_file_by_file_id(file_id=file_id)

    processed_data: OnboardedFile | None = None
    if processed_file and processed_file.processed_data:
        processed_data = LeanOnboardedFileSchema().load(processed_file.processed_data)
    return processed_data


def _create_entity_datasets(entity: Entity, documents: list[AnyDocument]) -> list[CustomizableClassifierDataset]:
    entity_dataset = EntityDataset(
        entity=entity,
        documents=documents,
    )
    entity_dataset.make_serializable()
    datasets = [
        CustomizableClassifierDataset(
            input_processing_type=InputProcessingType.OCR_TEXT,
            dataset=entity_dataset,
        )
    ]
    return datasets
