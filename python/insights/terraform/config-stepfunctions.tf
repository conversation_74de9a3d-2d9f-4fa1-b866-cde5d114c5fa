locals {
  custom_step_functions_config = {
  }

  sf_config = {
    inferTwoDigitNAICSCodesBlueARN : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:insights-scikit-112-infer-naics-two-digit"
    inferNaicsConstruction23 : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:insights-scikit-112-infer-naics-construction23"
    inferNaicsFleet4849 : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:insights-scikit-112-infer-naics-fleet4849"
    inferNaicsFoodAccommodation72 : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:insights-scikit-112-infer-naics-food-accommodation72"
    inferConstructionServices : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:insights-scikit-112-infer-construction-services"
    inferHasDeliveryARN : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:insights-images-infer-has-delivery-blue"
    inferUsesDangerousMaterialsARN : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:insights-images-infer-dangerous-materials"
    inferHasEntertainmentARN : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:insights-images-infer-has-entertainment-blue"
    inferContractorServicesOfferedTypesARN : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:contractor-services-infer-contractor-services"
    sendKalepaEventsLambdaARN : "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:infrastructure-events-event-sender"
    executeTaskARN : "arn:aws:states:us-east-1:${local.workflow_parameters.account_id}:stateMachine:mda-engine-execute-task"
  }
}
