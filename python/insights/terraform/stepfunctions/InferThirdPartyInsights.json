{"Comment": "Step Function for Third Party Customizable Classifiers V2 Pipeline", "StartAt": "PrepareThirdPartyWorker", "States": {"PrepareThirdPartyWorker": {"Type": "Task", "Resource": "${prepareThirdPartyWorker_lambda_arn}", "ResultPath": "$", "Next": "ProcessClassifiersMap", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "FailState", "ResultPath": "$.error"}]}, "ProcessClassifiersMap": {"Type": "Map", "ItemsPath": "$.classifiers", "MaxConcurrency": 10, "ResultPath": "$.tasks_outputs", "Next": "UploadResultToFacts", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "RunClassifier", "States": {"RunClassifier": {"Type": "Task", "Resource": "arn:aws:states:::states:startExecution.sync:2", "Parameters": {"Input.$": "$.task_input", "StateMachineArn": "${sf_config.executeTaskARN}"}, "ResultSelector": {"taskOutput.$": "$.Output"}, "OutputPath": "$.taskOutput", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleClassifierError", "ResultPath": "$.error"}], "End": true}, "HandleClassifierError": {"Type": "Pass", "Result": null, "End": true}}}, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "FailState", "ResultPath": "$.error"}]}, "UploadResultToFacts": {"Type": "Task", "Resource": "${uploadClassifierResultsToFacts_lambda_arn}", "ResultPath": "$", "Next": "SuccessState", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "FailState", "ResultPath": "$.error"}]}, "SuccessState": {"Type": "Succeed"}, "FailState": {"Type": "Fail", "Error": "ThirdPartyInsightsPipelineError", "Cause": "Error in Third Party Insights Pipeline"}}}