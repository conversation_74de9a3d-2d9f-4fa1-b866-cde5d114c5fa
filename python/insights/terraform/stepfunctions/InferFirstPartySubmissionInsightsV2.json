{"Comment": "Step Function for Customizable Classifiers V2 Pipeline", "StartAt": "PrepareFirstPartyWorker", "States": {"PrepareFirstPartyWorker": {"Type": "Task", "Resource": "${prepareFirstPartyWorker_lambda_arn}", "ResultPath": "$", "Next": "ProcessClassifiersMap", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "SendFirstPartyInsightsFailedEvent", "ResultPath": "$.error"}]}, "ProcessClassifiersMap": {"Type": "Map", "ItemsPath": "$.classifiers", "MaxConcurrency": 10, "ResultPath": "$.tasks_outputs", "Next": "MergeAndProcessResults", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "RunClassifier", "States": {"RunClassifier": {"Type": "Task", "Resource": "arn:aws:states:::states:startExecution.sync:2", "Parameters": {"Input.$": "$.task_input", "StateMachineArn": "${sf_config.executeTaskARN}"}, "ResultSelector": {"taskOutput.$": "$.Output"}, "OutputPath": "$.taskOutput", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleClassifierError", "ResultPath": "$.error"}], "End": true}, "HandleClassifierError": {"Type": "Pass", "Result": null, "End": true}}}, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "SendFirstPartyInsightsFailedEvent", "ResultPath": "$.error"}]}, "MergeAndProcessResults": {"Type": "Task", "Resource": "${mergeAndProcessResults_lambda_arn}", "ResultPath": "$.results", "Next": "SendFirstPartyInsightsFinishedEvent", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "SendFirstPartyInsightsFailedEvent", "ResultPath": "$.error"}]}, "SendFirstPartyInsightsFinishedEvent": {"Type": "Task", "Resource": "${sf_config.sendKalepaEventsLambdaARN}", "Parameters": {"event_type": "SUBMISSION_FILE_INSIGHTS_FINISHED", "submission_id.$": "$$.Execution.Input.submission_id", "organization_id.$": "$$.Execution.Input.organization_id", "step_function_execution_id.$": "$$.Execution.Id", "step_function_status": "SUCCESS"}, "End": true, "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}]}, "SendFirstPartyInsightsFailedEvent": {"Type": "Task", "Resource": "${sf_config.sendKalepaEventsLambdaARN}", "Parameters": {"event_type": "SUBMISSION_FILE_INSIGHTS_FINISHED", "submission_id.$": "$$.Execution.Input.submission_id", "organization_id.$": "$$.Execution.Input.organization_id", "step_function_execution_id.$": "$$.Execution.Id", "step_function_status": "FAILURE"}, "End": true, "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}]}}}