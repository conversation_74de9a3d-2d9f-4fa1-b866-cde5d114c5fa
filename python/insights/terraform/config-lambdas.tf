locals {
  common_lambdas_env = {
    INGESTION_DATA_BUCKET              = local.workflow_parameters.ingestion_data_bucket_names_by_region["us-east-1"]
    COPILOT_DATA_BUCKET                = local.workflow_parameters.copilot_uploads_bucket_names_by_region["us-east-1"]
    EMBEDDING_LAMBDA_NAME              = "insights-torch-default-inferQAClassifierRoberta"
    SEND_KALEPA_EVENTS_LAMBDA_ARN      = "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:infrastructure-events-event-sender"
    GET_RELEVANT_SCRAPING_METADATA_LAMBDA_ARN = "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:ingestion-get-relevant-scraping-metadata-lambda"
    INFER_GPT_NAICS_CODES_LAMBDA_ARN = "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:insights-infer-naics-codes-gpt"
    FACTS_API_URL                      = "http://facts-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
    FACTS_API_V2_URL                   = "http://facts-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v2.0"
    COPILOT_API_URL                    = "http://copilot-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v2.0"
    COPILOT_API_V3_URL                 = "http://copilot-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
    RISK_SERVICE_URL                   = "http://locations-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
    ERS_API_URL                        = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
    ERS_API_URL_V3                     = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
    ENTITY_RESOLUTION_SERVICE_V3_URL   = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
    NEWS_API_URL                       = "http://news-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v2.0"
    REDSHIFT_HOST_SECRET_ARN           = "${local.region_specific_parameters.runtime_secret_arns.redshift-secret}~host"
    REDSHIFT_PORT_SECRET_ARN           = "${local.region_specific_parameters.runtime_secret_arns.redshift-secret}~port"
    REDSHIFT_USER_SECRET_ARN           = "${local.region_specific_parameters.runtime_secret_arns.redshift-secret}~username"
    REDSHIFT_PASSWORD_SECRET_ARN       = "${local.region_specific_parameters.runtime_secret_arns.redshift-secret}~password"
    REDSHIFT_DATABASE_NAME_SECRET_ARN  = "${local.region_specific_parameters.runtime_secret_arns.redshift-secret}~dbname"
    REDSHIFT_SSH_TUNNEL_KEY_SECRET_ARN = "${local.region_specific_parameters.runtime_secret_arns.redshift-secret}~ssh_tunnel_key"
    DD_TRACE_SAMPLE_RATE               = lookup({ dev = "0.8", stage = "0.1", prod = "0.4" }, local.workflow_parameters.environment_key, "1")
    DD_VERSION                         = "1"
    SAFE_INIT_RESOLVE_SECRETS          = "true"
    SAFE_INIT_CACHE_SECRETS            = "true"
    SAFE_INIT_SECRET_CACHE_REDIS_HOST  = local.redis_cache_host
    SAFE_INIT_SECRET_CACHE_REDIS_PORT  = local.redis_cache_port
    LABEL_STUDIO_BASE_URL              = local.env_config.label_studio_base_url
    REDIS_CACHE_HOST                   = local.redis_cache_host
    REDIS_CACHE_PORT                   = local.redis_cache_port
    DD_PATCH_MODULES                   = "flask:false"
    DD_TRACE_FLASK_ENABLED             = "false"
    STRUCTLOG_SKIP_LOG_TRUNCATION      = "true"
  }

  lambdas = {
    createDatasetAndSendEvents = {
      handler              = "src.handlers.datasets.create_dataset_and_send_events",
      memory_size          = 1280
      ephemeralStorageSize = 1024
      timeout              = 900
    }
    emitException = {
      handler              = "src.handlers.execution_events.emit_exception"
      memory_size          = 1280
      timeout              = 300
      ephemeralStorageSize = 1024
    }
    determineInsights = {
      handler              = "src.handlers.flow_control.determine_insights"
      memory_size          = 1280
      timeout              = 450
      ephemeralStorageSize = 1024
    }
    inferCustomClassifierBatch = {
      handler              = "src.handlers.custom_classifier.infer_custom_classifier_batch"
      memory_size          = 1024
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    createCustomClassifierRequestBatches = {
      handler              = "src.handlers.insights.create_custom_classifier_request_batches"
      memory_size          = 768
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    inferSubmissionNaicsCodes = {
      handler              = "src.handlers.submission_insights.infer_naics_codes"
      memory_size          = 1024
      timeout              = 900
      ephemeralStorageSize = 1024
      environment = {
        SUBMISSION_ISO_GL_CLASSIFICATION_NAME = "insights-infer-submission-isogl-codes"
      }
    }
    inferSubmissionISOGLCodes = {
      handler              = "src.handlers.submission_insights.infer_iso_gl_codes"
      memory_size          = 1024
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    inferHasExposureToFlammables = {
      handler              = "src.handlers.insights.infer_has_exposure_to_flammables"
      memory_size          = 512
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    inferServesAlcohol = {
      handler              = "src.handlers.insights.infer_serves_alcohol"
      memory_size          = 1280
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    inferResidentialOwnershipTypes = {
      handler              = "src.handlers.insights.infer_residential_ownership_types"
      memory_size          = 1280
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    inferTransportation = {
      handler              = "src.handlers.insights.infer_transportation"
      memory_size          = 1280
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    inferIsoCodes = {
      handler              = "src.handlers.iso_classifier.infer_iso_codes"
      memory_size          = 1280
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    inferOtherCertificates = {
      handler              = "src.handlers.other_certificates_classifier.infer_other_certificates"
      memory_size          = 1280
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    inferConstructionServicesGPT = {
      handler              = "src.handlers.construction_services.infer"
      memory_size          = 2048
      timeout              = 900
      ephemeralStorageSize = 1024
    }
    createTemporaryFirstPartyDatasetV2 = {
      handler              = "src.handlers.datasets.create_temporary_first_party_dataset_v2"
      memory_size          = 1280
      ephemeralStorageSize = 1024
      timeout              = 900
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
    }
    createMultiLabelFacts = {
      handler              = "src.handlers.flow_control.create_multi_label_facts"
      memory_size          = 1280
      timeout              = 150
      ephemeralStorageSize = 1024
    }
    taxonomyBusinessCategoryMapper = {
      handler              = "src.handlers.taxonomy_handlers.map_business_categories"
      memory_size          = 1280
      ephemeralStorageSize = 1024
      timeout              = 300
    }
    inferMissingNAICSCodes = {
      handler              = "src.handlers.insights.infer_missing_naics_codes"
      memory_size          = 1280
      ephemeralStorageSize = 1024
      timeout              = 900
    }
    redshiftTunnelConnectionTest = {
      handler              = "src.handlers.redshift_test_handler.test_connection"
      memory_size          = 1024
      ephemeralStorageSize = 1024
      timeout              = 300
    }
    sampleNewsForLabeling = {
      handler              = "src.handlers.statistics.sample_news_for_labeling"
      memory_size          = 1024
      timeout              = 900
      ephemeralStorageSize = 1024
      environment = {
        LABEL_STUDIO_API_TOKEN_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.label-studio-token
      }
    }
    fetchNewsFromLabeling = {
      handler              = "src.handlers.statistics.save_labelled_news"
      memory_size          = 1024
      timeout              = 900
      ephemeralStorageSize = 1024
      environment = {
        LABEL_STUDIO_API_TOKEN_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.label-studio-token
      }
    }
    inferNaicsCodesGPT = {
      handler              = "src.handlers.naics_classifier.infer_gpt_naics_codes"
      memory_size          = 1280
      timeout              = 900
    }
    inferFileInsights = {
      handler              = "src.handlers.file_insights.infer_file_insights"
      memory_size          = 1280
      timeout              = 300
    }
    inferAndSaveFieldMatchingClassifiers = {
      handler              = "src.handlers.file_insights.infer_field_matching_classifiers_and_save_results"
      memory_size          = 2000
      timeout              = 900
    }
    inferEmailClassification = {
      handler              = "src.handlers.email_classification.infer_email_classification"
      memory_size          = 1280
      timeout              = 300
    }
    testRunCreateClassifierDataset = {
      handler              = "src.handlers.test_runs.dataset.create_classifier_dataset"
      memory_size          = 1280
      timeout              = 900
      environment = {
        TEST_RUN_DATASET_BUCKET = local.workflow_parameters.ingestion_data_bucket_names_by_region["us-east-1"]
        TEST_RUN_DATASET_PREFIX = "classifier_test_runs/datasets"
      }
    }
    testRunExecuteAndSaveResults = {
      handler              = "src.handlers.test_runs.execution.execute_and_save_test_run_result"
      memory_size          = 1280
      timeout              = 900
      environment = {
        TEST_RUN_DATASET_BUCKET = local.workflow_parameters.ingestion_data_bucket_names_by_region["us-east-1"]
        TEST_RUN_DATASET_PREFIX = "classifier_test_runs/datasets"
      }
    }
    testRunHandleFailure = {
      handler              = "src.handlers.test_runs.execution.handle_classifier_test_run_failure"
      memory_size          = 1280
      timeout              = 900
    }
    inferFileInsightsV2 = {
      handler              = "src.handlers.file_insights.infer_file_insights_v2"
      memory_size          = 1280
      timeout              = 300
    }
    prepareFirstPartyWorker = {
      handler              = "src.handlers.customizable_classifiers_v2.prepare_first_party_worker"
      memory_size          = 1280
      timeout              = 300
    }
    mergeAndProcessResults = {
      handler              = "src.handlers.customizable_classifiers_v2.merge_and_process_results"
      memory_size          = 1280
      timeout              = 300
    }
    prepareThirdPartyWorker = {
      handler              = "src.handlers.customizable_classifiers_v2.prepare_third_party_worker"
      memory_size          = 1280
      timeout              = 300
    }
    uploadClassifierResultsToFacts = {
      handler              = "src.handlers.customizable_classifiers_v2.upload_classifier_results_to_facts"
      memory_size          = 1280
      timeout              = 300
    }
  }
}
