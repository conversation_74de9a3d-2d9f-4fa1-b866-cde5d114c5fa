def test_historical_bind_rates(db_session, app_client):
    db_session.execute("DROP TABLE IF EXISTS historical_bind_rates")
    db_session.execute(
        "CREATE TABLE historical_bind_rates(organization_id int, parameter_name varchar, parameter_value"
        " varchar, bind_rate float, naics_filter varchar, is_negated_filter boolean)"
    )
    db_session.execute(
        "INSERT INTO historical_bind_rates (organization_id, parameter_name, parameter_value, bind_rate, naics_filter,"
        " is_negated_filter) VALUES (1, 'broker_name', 'broker A', 0.5, '23', false)"
    )
    db_session.execute(
        "INSERT INTO historical_bind_rates (organization_id, parameter_name, parameter_value, bind_rate, naics_filter,"
        " is_negated_filter) VALUES (1, 'broker_name', 'broker A', 0.5, null, false)"
    )
    db_session.execute(
        "INSERT INTO historical_bind_rates (organization_id, parameter_name, parameter_value, bind_rate, naics_filter,"
        " is_negated_filter) VALUES (1, 'broker_name', 'broker b', 1, '23', false)"
    )
    db_session.execute(
        "INSERT INTO historical_bind_rates (organization_id, parameter_name, parameter_value, bind_rate, naics_filter,"
        " is_negated_filter) VALUES (1, 'broker_name', 'Broker c', 0.6666, '55', false)"
    )
    db_session.execute(
        "INSERT INTO historical_bind_rates (organization_id, parameter_name, parameter_value, bind_rate, naics_filter,"
        " is_negated_filter) VALUES (1, 'broker_name', 'Broker c', 0.6666, null, false)"
    )

    db_session.commit()

    response = app_client.post(
        "/api/v1.0/query/organization/1/historical_bind_rates",
        json={"parameter_name": "broker_name", "parameter_values": ["broker a", "broker c"]},
    )
    response_json = response.json()

    assert response.status_code == 200
    assert len(response_json["parameter_values"]) == 2
    assert response_json["parameter_values"]["broker a"] == 0.5
    assert response_json["parameter_values"]["broker c"] == 0.6666


def test_historical_bind_rates_with_naics(db_session, app_client):
    db_session.execute("DROP TABLE IF EXISTS historical_bind_rates")
    db_session.execute(
        "CREATE TABLE historical_bind_rates(organization_id int, parameter_name varchar, parameter_value"
        " varchar, bind_rate float, naics_filter varchar, is_negated_filter boolean)"
    )
    db_session.execute(
        "INSERT INTO historical_bind_rates (organization_id, parameter_name, parameter_value, bind_rate, naics_filter,"
        " is_negated_filter) VALUES (1, 'broker_name', 'broker A', 0.5, '23', true)"
    )
    db_session.execute(
        "INSERT INTO historical_bind_rates (organization_id, parameter_name, parameter_value, bind_rate, naics_filter,"
        " is_negated_filter) VALUES (1, 'broker_name', 'broker b', 1, '25', false)"
    )
    db_session.execute(
        "INSERT INTO historical_bind_rates (organization_id, parameter_name, parameter_value, bind_rate, naics_filter,"
        " is_negated_filter) VALUES (1, 'broker_name', 'Broker c', 0.6666, '55', false)"
    )

    db_session.commit()

    response = app_client.post(
        "/api/v1.0/query/organization/1/historical_bind_rates",
        json={
            "parameter_name": "broker_name",
            "parameter_values": ["broker a", "broker c"],
            "naics_filter": "23",
            "filter_negated": True,
        },
    )
    response_json = response.json()

    assert response.status_code == 200
    assert len(response_json["parameter_values"]) == 1
    assert response_json["parameter_values"]["broker a"] == 0.5
