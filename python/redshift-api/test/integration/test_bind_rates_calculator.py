from datetime import datetime, timedelta

from src.warehouse.historical_bind_rates.bind_rates_calculator import (
    BindRatesCalculator,
)


def test_calculate_bind_rates(db_session, app_client):
    db_session.execute("CREATE TABLE IF NOT EXISTS historical_bind_rates(id uuid)")
    db_session.commit()
    db_session.execute("DROP TABLE IF EXISTS submissions")
    db_session.execute(
        "CREATE TABLE submissions(id uuid, organization_id int, broker_name varchar, brokerage_name varchar, stage"
        " varchar, created_date date, two_digits_primary_naics varchar)"
    )
    today = datetime.today().strftime("%Y-%m-%d")
    two_months_ago = (datetime.today() - timedelta(60)).strftime("%Y-%m-%d")
    year_ago = (datetime.today() - timedelta(365)).strftime("%Y-%m-%d")
    db_session.execute(
        "INSERT INTO submissions (id, organization_id, broker_name, brokerage_name, stage, created_date,"
        " two_digits_primary_naics) VALUES (gen_random_uuid(), 1, 'broker a', 'brokerage a', 'QUOTED_BOUND',"
        f" '{two_months_ago}', '23')"
    )
    db_session.execute(
        "INSERT INTO submissions (id, organization_id, broker_name, brokerage_name, stage, created_date,"
        " two_digits_primary_naics) VALUES (gen_random_uuid(), 1, 'broker a', 'brokerage a', 'QUOTED',"
        f" '{two_months_ago}', '23')"
    )
    db_session.execute(
        "INSERT INTO submissions (id, organization_id, broker_name, brokerage_name, stage, created_date,"
        " two_digits_primary_naics) VALUES (gen_random_uuid(), 1, 'broker a', 'brokerage a', 'QUOTED_LOST',"
        f" '{year_ago}', '53')"
    )
    db_session.execute(
        "INSERT INTO submissions (id, organization_id, broker_name, brokerage_name, stage, created_date,"
        f" two_digits_primary_naics) VALUES (gen_random_uuid(), 1, 'broker a', 'brokerage a', 'QUOTED', '{today}',"
        " '53')"
    )
    db_session.execute(
        "INSERT INTO submissions (id, organization_id, broker_name, brokerage_name, stage, created_date,"
        " two_digits_primary_naics) VALUES (gen_random_uuid(), 1, 'broker b', 'brokerage a', 'QUOTED_BOUND',"
        f" '{two_months_ago}', '23')"
    )
    db_session.execute(
        "INSERT INTO submissions (id, organization_id, broker_name, brokerage_name, stage, created_date,"
        " two_digits_primary_naics) VALUES (gen_random_uuid(), 1, 'broker c', 'brokerage b', 'QUOTED_BOUND',"
        f" '{two_months_ago}', '23')"
    )
    db_session.execute(
        "INSERT INTO submissions (id, organization_id, broker_name, brokerage_name, stage, created_date,"
        " two_digits_primary_naics) VALUES (gen_random_uuid(), 1, 'broker c', 'brokerage b', 'QUOTED_LOST',"
        f" '{two_months_ago}', '53')"
    )
    db_session.execute(
        "INSERT INTO submissions (id, organization_id, broker_name, brokerage_name, stage, created_date,"
        " two_digits_primary_naics) VALUES (gen_random_uuid(), 1, 'broker c', 'brokerage b', 'QUOTED_BOUND',"
        f" '{two_months_ago}', '23')"
    )
    db_session.commit()

    BindRatesCalculator(db_session).calculate_bind_rates()

    result = db_session.execute(
        "SELECT * FROM historical_bind_rates order by parameter_value, naics_filter, is_negated_filter"
    ).fetchall()
    assert len(result) == 16
    assert result[0]["parameter_value"] == "broker a"
    assert result[0]["bind_rate"] == 0.5
    assert result[0]["naics_filter"] == "23"
    assert result[0]["is_negated_filter"] is False

    assert result[2]["parameter_value"] == "broker a"
    assert float(round(result[4]["bind_rate"], 5)) == 0.5
    assert result[2]["naics_filter"] == "72"
    assert result[2]["is_negated_filter"] is True

    assert result[3]["parameter_value"] == "broker a"
    assert result[3]["bind_rate"] == 0.5
    assert result[3]["naics_filter"] is None
    assert result[3]["is_negated_filter"] is False

    assert result[9]["parameter_value"] == "broker b"
    assert float(round(result[9]["bind_rate"], 5)) == 1.0
    assert result[9]["naics_filter"] is None
    assert result[9]["is_negated_filter"] is False

    assert result[4]["parameter_value"] == "brokerage a"
    assert float(round(result[4]["bind_rate"], 5)) == 0.5
    assert result[5]["parameter_value"] == "brokerage b"
    assert float(round(result[5]["bind_rate"], 5)) == 0.66667

    assert result[15]["parameter_value"] == "broker c"
    assert float(round(result[15]["bind_rate"], 5)) == 0.66667
    assert result[15]["naics_filter"] is None
    assert result[15]["is_negated_filter"] is False
