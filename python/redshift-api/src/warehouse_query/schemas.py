import uuid
from datetime import datetime
from typing import Union

from pydantic import BaseModel
from static_common.enums.fact_subtype import FactSubtypeID

from src.dashboard_charts.models import AggregationType


class QueryFilter(BaseModel):
    field: str
    operator: str
    value: str
    function_name: str | None = None


class QueryFilterExpression(BaseModel):
    operator: str
    filters: list[Union[QueryFilter, "QueryFilterExpression"]]


class QueryMappingThen(BaseModel):
    field: str | None = None
    constant_value: str | None = None


class QueryMapping(BaseModel):
    when: QueryFilter
    then: QueryMappingThen


class DimensionTransformation(BaseModel):
    function_name: str | None = None
    function_args: list[str] | None = None
    suffix_args: list[str] | None = None


class MeasureTransformation(BaseModel):
    function_name: str | None = None
    prefix_args: list[str] | None = None
    suffix_args: list[str] | None = None


class DimensionHaving(BaseModel):
    operator: str
    value: str


class QueryDimensionRequest(BaseModel):
    field: str
    display_name: str | None = None
    transformation: DimensionTransformation | None = None
    having: list[DimensionHaving] | None = None
    mappings: list[QueryMapping] | None = None


class QueryMeasureRequest(BaseModel):
    field: str
    aggregation_type: AggregationType | None = None
    as_group_percentage: bool = False
    as_filter_percentage: bool = False
    display_name: str | None = None
    filter: QueryFilter | None = None  # Deprecated
    filters: list[QueryFilter] | None = None
    or_filters: list[QueryFilter] | None = None
    transformation: MeasureTransformation | None = None


class QueryDataRequest(BaseModel):
    measures: list[QueryMeasureRequest]
    dimensions: list[QueryDimensionRequest] | None = None
    filters: list[QueryFilter] | None = None
    or_filters: list[QueryFilter] | None = None
    filter_expression: QueryFilterExpression | None = None


class PercentilesRequest(BaseModel):
    percentiles: list[int]
    fact_subtype_ids: list[FactSubtypeID] | None = None
    columns: list[str] | None = None


class PercentileColumnResponse(BaseModel):
    percentile: int
    value: float | None = None
    column: str
    fact_subtype_id: FactSubtypeID | None = None


class PercentilesResponse(BaseModel):
    columns: list[PercentileColumnResponse]


class IdentifiableQueryDataRequest(QueryDataRequest):
    id: str


class QueryDataMeasureResponse(BaseModel):
    display_name: str
    value: float
    unit: str | None = None
    data_type: str | None = None
    as_group_percentage: bool | None = None
    as_filter_percentage: bool | None = None


class QueryDataDimensionResponse(BaseModel):
    display_name: str
    measures: list[QueryDataMeasureResponse]
    dimensions: list["QueryDataDimensionResponse"] | None = None
    unit: str | None = None
    data_type: str | None = None


class QueryDataResponse(BaseModel):
    measures: list[QueryDataMeasureResponse]
    dimensions: list[QueryDataDimensionResponse]


class IdentifiableQueryDataResponse(QueryDataResponse):
    id: str


class IdentifiableQueryTableResponse(BaseModel):
    id: str
    data: list[list[str]] | None = None


class HistoricalBindRatesRequest(BaseModel):
    parameter_name: str
    parameter_values: list[str]
    naics_filter: str | None = None
    filter_negated: bool = False


class HistoricalBindRatesResponse(BaseModel):
    parameter_values: dict[str, float]


class ProfitabilityScoreRequest(BaseModel):
    sic_code: str
    coverages: list[str]
    user_group: str


class ProfitabilityScoreEntry(BaseModel):
    sic_code: str
    fni_state: str
    profit_per_policy_2025_rating: str | None
    profit_per_policy_2025: float | None
    remaining_premium_rating_numerical: float | None
    remaining_premium_rating: str | None


class ProfitabilityScoreResponse(BaseModel):
    scores: list[ProfitabilityScoreEntry]


class AllowedPortfolioColumnResponse(BaseModel):
    column_name: str
    supported_methods: list[str]
    is_available: bool
    is_available_after_full_refresh: bool


class CoverageTypeItem(BaseModel):
    coverage_type: str
    name: str


class SubmissionInfoForSimilarity(BaseModel):
    id: uuid.UUID
    coverages: list[CoverageTypeItem] | None
    total_sales: dict[str, int] | None
    payroll: dict[str, int] | None
    broker_bound_rate: float | None

    created_date: datetime | None
    broker_id: uuid.UUID | None
    two_digits_primary_naics: str | None
    organization_id: int
    tiv_sum: float | None
    fni_state: str | None
    number_of_locations_sum: int | None
    recommendation_action: str | None
    number_of_units_sum: int | None
    vehicles_count: int | None
    losses: float | None
    stage: str | None


class LookalikeCandidatesResponse(BaseModel):
    original_submission: SubmissionInfoForSimilarity
    lookalike_candidates: list[SubmissionInfoForSimilarity]
