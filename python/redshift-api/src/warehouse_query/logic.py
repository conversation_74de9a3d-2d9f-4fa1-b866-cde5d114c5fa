import hashlib
import json
import random
from collections import defaultdict
from datetime import datetime
from decimal import Decimal
from operator import attrgetter

from infrastructure_common.logging import get_logger
from sqlalchemy.engine import Row
from sqlalchemy.orm import Session

from src.clients.features import FeatureFlagsClient, FeatureType
from src.clients.redshift import RedshiftApiClient
from src.clients.utils import get_redis_cache_client, get_redshift_api_session
from src.warehouse.builders.submissions_base_builder import ColumnConfig
from src.warehouse_query.query import (
    RedshiftFilter,
    RedshiftFilterExpression,
    RedshiftQuery,
    RedshiftTable,
)

from ..dashboard_charts.models import AggregationType
from ..utils import measured_execute
from .schemas import (
    DimensionTransformation,
    HistoricalBindRatesRequest,
    HistoricalBindRatesResponse,
    LookalikeCandidatesResponse,
    PercentileColumnResponse,
    PercentilesRequest,
    PercentilesResponse,
    ProfitabilityScoreEntry,
    ProfitabilityScoreRequest,
    ProfitabilityScoreResponse,
    QueryDataDimensionResponse,
    QueryDataMeasureResponse,
    QueryDataRequest,
    QueryDataResponse,
    QueryDimensionRequest,
    QueryFilter,
    QueryFilterExpression,
    QueryMeasureRequest,
    SubmissionInfoForSimilarity,
)
from .utils import (
    DATA_TYPE_BY_AGGREGATION,
    QUERY_TABLE_VALUE_BY_COLUMN_CONFIG_PARENT,
    QueryBuildingException,
    QueryTable,
    adjust_fields_to_main_table,
    detect_filter_tables,
    detect_main_query_table,
)

logger = get_logger()

__submission_table = RedshiftTable("warehouse", "submissions", "sub")
__locations_table = RedshiftTable("warehouse", "locations", "loc")

__submission_table_for_postgres = RedshiftTable("public", "submissions", "sub")
__locations_table_for_postgres = RedshiftTable("public", "locations", "loc")


__coverages_sub_table = RedshiftTable(__submission_table.alias, "coverages", "cov", True)
__underwriters_sub_table = RedshiftTable(__submission_table.alias, "underwriters", "uw", True)
__user_groups_sub_table = RedshiftTable(__submission_table.alias, "user_groups", "ug", True)

__coverages_sub_table_for_postgres = RedshiftTable(
    __submission_table_for_postgres.alias, "coverages", "cov", True, use_postgres=True
)
__underwriters_sub_table_for_postgres = RedshiftTable(
    __submission_table_for_postgres.alias, "underwriters", "uw", True, use_postgres=True
)
__user_groups_sub_table_for_postgres = RedshiftTable(
    __submission_table_for_postgres.alias, "user_groups", "ug", True, use_postgres=True
)


__SUBMISSION_ARRAYS_MAP = {
    "coverages": {"redshift": __coverages_sub_table, "postgres": __coverages_sub_table_for_postgres},
    "underwriters": {"redshift": __underwriters_sub_table, "postgres": __underwriters_sub_table_for_postgres},
    "user_groups": {"redshift": __user_groups_sub_table, "postgres": __user_groups_sub_table_for_postgres},
}

TIME_SERIES_FIELDS = {"total_sales", "payroll"}
TIME_FILTER_FIELDS = {"created_date", "effective_date"}


def __get_filtered_years(filters: list[RedshiftFilter]) -> list[int]:
    years = set()
    for query_filter in filters:
        if query_filter.field.name in TIME_FILTER_FIELDS:
            years.add(int(query_filter.value.replace("'", "").split("-")[0]))
    if len(years) < 1:
        return []
    min = sorted(years)[0]
    max = sorted(years)[-1]
    return list(range(min - 1, max))


def __get_sub_arrays(
    query_filters_or_dimensions: list[QueryFilter | QueryDimensionRequest | QueryMeasureRequest],
    use_postgres: bool = False,
) -> list[RedshiftTable]:
    table_names = set()
    for query_filter in query_filters_or_dimensions:
        table_name = query_filter.field.split(".")[0]
        if table_name in __SUBMISSION_ARRAYS_MAP:
            table_names.add(table_name)

    table_names = list(table_names)
    table_names.sort()
    return [
        __SUBMISSION_ARRAYS_MAP[table_name]["postgres" if use_postgres else "redshift"] for table_name in table_names
    ]


def __create_filter(query_filter: QueryFilter, use_postgres: bool = False) -> RedshiftFilter:
    field = query_filter.field
    table_name, table_field = field.split(".")
    if table_name in __SUBMISSION_ARRAYS_MAP:
        table = __SUBMISSION_ARRAYS_MAP[table_name]["postgres" if use_postgres else "redshift"]
    elif table_name.startswith("location"):
        table = __locations_table_for_postgres if use_postgres else __locations_table
    else:
        table = __submission_table_for_postgres if use_postgres else __submission_table
    function_name = "COALESCE" if query_filter.function_name == "NVL" else query_filter.function_name
    return RedshiftFilter(table[table_field], query_filter.operator, query_filter.value, function_name)


def __prepare_measure_function_argument(argument: str, table: RedshiftTable) -> str:
    if argument.startswith(QueryTable.Submissions + ".") and table in [
        __submission_table,
        __submission_table_for_postgres,
    ]:
        return __create_field_name(table, argument)
    elif argument.startswith(QueryTable.Locations + ".") and table in [
        __locations_table,
        __locations_table_for_postgres,
    ]:
        return __create_field_name(table, argument)
    elif argument.startswith(table.raw_name + "."):
        return __create_field_name(table, argument)
    return argument


def __create_transformation(transformation: DimensionTransformation, field_name: str) -> str:
    if transformation.function_name:
        fnc_args = []
        if transformation.function_args:
            fnc_args.extend(transformation.function_args)
        fnc_args.append(field_name)
        if transformation.suffix_args:
            fnc_args.extend(transformation.suffix_args)
        args_str = ", ".join(fnc_args)
        function_name = "COALESCE" if transformation.function_name == "NVL" else transformation.function_name
        return f"{function_name}({args_str})"
    return field_name


def __create_field_name(table: RedshiftTable, field: str, array_sub_tables: list[RedshiftTable] | None = None) -> str:
    use_table = table
    for sub_table in array_sub_tables or []:
        if field.startswith(sub_table.raw_name + "."):
            use_table = sub_table
    field_name = field.split(".")[1]
    return str(use_table[field_name])


def __extend_with_aggregation(
    aggregation_type: AggregationType, field_name: str, should_be_distinct_count: bool
) -> str:
    is_distinct = should_be_distinct_count and aggregation_type in {AggregationType.COUNT}
    distinct_str = "distinct " if is_distinct else ""
    return f"{aggregation_type}({distinct_str}{field_name})"


def __build_measure_select(
    table: RedshiftTable,
    measure: QueryMeasureRequest,
    time_series_years: list[int],
    should_be_distinct_for_count: bool,
    array_sub_tables: list[RedshiftTable] | None = None,
    use_postgres: bool = False,
) -> str:
    field_name = __create_field_name(table, measure.field, array_sub_tables)
    if measure.transformation:
        args = []
        args.extend(
            [__prepare_measure_function_argument(arg, table) for arg in measure.transformation.prefix_args or []]
        )
        args.append(field_name)
        args.extend(
            [__prepare_measure_function_argument(arg, table) for arg in measure.transformation.suffix_args or []]
        )
        function_name = (
            "COALESCE" if measure.transformation.function_name == "NVL" else measure.transformation.function_name
        )
        field_name = f"{function_name}({', '.join(args)})"

    if measure.filter:
        field_name = f"CASE WHEN {__create_filter(measure.filter).to_sql()} THEN {field_name} END"
    elif measure.filters:
        conditions = [__create_filter(f).to_sql() for f in measure.filters]
        field_name = f"CASE WHEN {' AND '.join(conditions)} THEN {field_name} END"
    elif measure.or_filters:
        or_conditions = [__create_filter(f).to_sql() for f in measure.or_filters]
        field_name = f"CASE WHEN {' OR '.join(or_conditions)} THEN {field_name} END"

    if measure.field.split(".")[-1] in TIME_SERIES_FIELDS and time_series_years:
        year_checks = []
        for year in time_series_years:
            year_checks.append(
                f"({field_name}->>'{year}')::numeric"
                if use_postgres
                else (
                    f"NULLIF(JSON_EXTRACT_PATH_TEXT(JSON_SERIALIZE(coalesce({field_name}, JSON_PARSE('{'{}'}'))),"
                    f" '{year}'),'')"
                )
            )
        field_name = f"COALESCE({', '.join(year_checks)})"
    if measure.aggregation_type:
        field_name = __extend_with_aggregation(measure.aggregation_type, field_name, should_be_distinct_for_count)
    if measure.as_filter_percentage and measure.filter and measure.aggregation_type:
        divider_field_name = __extend_with_aggregation(
            measure.aggregation_type, __create_field_name(table, measure.field), should_be_distinct_for_count
        )
        field_name = f"({field_name} / CAST({divider_field_name} AS DECIMAL(10, 0)) * 100)"
    return field_name


def __build_dimension_select(
    table: RedshiftTable,
    dimension: QueryDimensionRequest,
    idx: int,
    array_sub_tables: list[RedshiftTable] | None = None,
) -> str:
    field_name = __create_field_name(table, dimension.field, array_sub_tables)
    if dimension.mappings:
        case_str = "CASE "
        for mapping in dimension.mappings:
            case_str += (  # ruff: noqa: E501
                f"WHEN {__create_filter(mapping.when).to_sql()} THEN"
                f" {__create_field_name(table, mapping.then.field) if mapping.then.field else mapping.then.constant_value} "
            )
        case_str += f"ELSE {field_name} END"
        field_name = case_str
    if dimension.transformation:
        return f"{__create_transformation(dimension.transformation, field_name)} AS d{idx}"

    return f"{field_name} AS d{idx}"


def __build_dimension_group(dimensions: list[QueryDimensionRequest]) -> str:
    return [f"d{idx}" for idx, _ in enumerate(dimensions)]


def __build_main_select(
    main_table: QueryTable,
    dimensions: list[QueryDimensionRequest],
    measures: list[QueryMeasureRequest],
    time_series_years: list[int],
    array_sub_tables: list[RedshiftTable] | None = None,
    use_postgres: bool = False,
) -> str:
    select_table = (
        (__submission_table_for_postgres if use_postgres else __submission_table)
        if main_table == QueryTable.Submissions
        else (__locations_table_for_postgres if use_postgres else __locations_table)
    )
    dimension_part = [
        __build_dimension_select(select_table, dimension, idx, array_sub_tables)
        for idx, dimension in enumerate(dimensions)
    ]
    should_be_distinct_for_count = len(array_sub_tables or []) > 0 or main_table == QueryTable.Locations
    measure_part = [
        __build_measure_select(
            select_table, measure, time_series_years, should_be_distinct_for_count, array_sub_tables, use_postgres
        )
        for measure in measures
    ]

    result = f"""SELECT {", ".join(dimension_part)}{", " if dimensions else ""}{", ".join(measure_part)} FROM {select_table.aliased_name}"""  # ruff: noqa: E501
    if array_sub_tables:
        sub_tables_str = ", ".join(table.sub_array_name for table in array_sub_tables)
        result += f", {sub_tables_str}"
    return result


def __build_main_query(
    main_table: QueryTable,
    dimensions: list[QueryDimensionRequest],
    measures: list[QueryMeasureRequest],
    filter_expression: RedshiftFilterExpression | None = None,
    array_sub_tables: list[RedshiftTable] | None = None,
    use_postgres: bool = False,
) -> RedshiftQuery:
    result = RedshiftQuery()
    all_filters = __get_all_redshift_filters(filter_expression)
    time_series_years = __get_filtered_years(all_filters)
    result.select(
        __build_main_select(main_table, dimensions, measures, time_series_years, array_sub_tables, use_postgres)
    ).filter_expression(filter_expression)
    if dimensions:
        group_by_fields = __build_dimension_group(dimensions)
        result.group_by(*group_by_fields)
        result.order_by(*group_by_fields)
    return result


def __build_main_query_with_subquery(
    main_table: QueryTable,
    dimensions: list[QueryDimensionRequest],
    measures: list[QueryMeasureRequest],
    filter_expression: RedshiftFilterExpression,
    array_sub_tables: list[RedshiftTable] | None = None,
    use_postgres: bool = False,
) -> RedshiftQuery:
    locations_table = __locations_table_for_postgres if use_postgres else __locations_table
    submissions_table = __submission_table_for_postgres if use_postgres else __submission_table
    sub_table = locations_table
    select_table = submissions_table
    select_table_aliases = [
        submissions_table.alias,
        __coverages_sub_table.alias,
        __underwriters_sub_table.alias,
        __user_groups_sub_table.alias,
    ]

    if main_table == QueryTable.Locations:
        sub_table = submissions_table
        select_table = locations_table
        select_table_aliases = [locations_table.alias]

    sub_field = "submission_id" if main_table == QueryTable.Submissions else "id"

    # Look out! We are filtering out all filters for subquery that may loose your structure of WHERE statements
    # E.g: WHERE (A1 AND A2) OR (A3 AND B1)
    # Will be translated to:
    # WHERE ((A1 AND A2) OR A3) AND <Subquery WHERE B1>
    # This is a bug, but this construct before adding `filter_expression` was not possible so it did not introduce any regerssion
    # But if we start to utilize complex filter expressions, this will be a problem and we will need to address it
    sub_filter_expression = filter_expression.copy() if filter_expression else None
    filter_expression.keep_filters_matching(
        lambda field: any(f"{alias}." in field.field_name for alias in select_table_aliases)
    )
    sub_filter_expression.keep_filters_matching(
        lambda field: not (any(f"{alias}." in field.field_name for alias in select_table_aliases))
    )

    sub_query = RedshiftQuery()
    should_be_distinct = len(array_sub_tables or []) > 0 or sub_table == QueryTable.Locations
    is_connecting_sub_to_locations = (
        main_table == QueryTable.Locations and sub_table.raw_name == __submission_table.raw_name
    )
    sub_query_select = (
        f"SELECT {'distinct ' if should_be_distinct else ''}{sub_table[sub_field]} AS submission_id from"
        f" {sub_table.aliased_name}"
    )
    if main_table == QueryTable.Locations and array_sub_tables:
        sub_tables_str = ", ".join(table.sub_array_name for table in array_sub_tables)
        sub_query_select += f", {sub_tables_str}"
    sub_query.select(sub_query_select)
    sub_query.filter_expression(sub_filter_expression)

    main_query = __build_main_query(
        main_table,
        dimensions,
        measures,
        filter_expression,
        array_sub_tables if main_table == QueryTable.Submissions else None,
        use_postgres,
    )

    sub_query_filter_field = "submission_id" if main_table == QueryTable.Locations else "id"
    subquery_filter = RedshiftFilter(
        select_table[sub_query_filter_field + ("::uuid" if is_connecting_sub_to_locations and use_postgres else "")],
        "IN",
        sub_query,
    )
    main_query.filter_expression(RedshiftFilterExpression("AND", [filter_expression, subquery_filter]))
    return main_query


def __build_filters(filters: list[QueryFilter], use_postgres: bool = False) -> list[RedshiftFilter]:
    return [__create_filter(query_filter, use_postgres) for query_filter in filters]


def __build_filter_expression(
    filter_expression: QueryFilterExpression | None, use_postgres: bool = False
) -> RedshiftFilterExpression | None:
    if not filter_expression:
        return RedshiftFilterExpression("AND", [])
    filters = []
    for filter_item in filter_expression.filters:
        if isinstance(filter_item, QueryFilter):
            filters.append(__create_filter(filter_item, use_postgres))
        else:
            filters.append(__build_filter_expression(filter_item, use_postgres))
    return RedshiftFilterExpression(filter_expression.operator, filters)


def __get_all_filters(
    filter_expression: QueryFilterExpression | None,
) -> list[QueryFilter]:
    if not filter_expression:
        return []
    filters = []
    for filter_item in filter_expression.filters:
        if isinstance(filter_item, QueryFilter):
            filters.append(filter_item)
        else:
            filters.extend(__get_all_filters(filter_item))
    return filters


def __get_all_redshift_filters(
    filter_expression: RedshiftFilterExpression | None,
) -> list[RedshiftFilter]:
    if not filter_expression:
        return []
    filters = []
    for filter_item in filter_expression.filters:
        if isinstance(filter_item, RedshiftFilter):
            filters.append(filter_item)
        else:
            filters.extend(__get_all_redshift_filters(filter_item))
    return filters


def _build_query(
    dimensions: list[QueryDimensionRequest],
    measures: list[QueryMeasureRequest],
    filters: list[QueryFilter],
    or_filters: list[QueryFilter],
    filter_expression: QueryFilterExpression | None = None,
    use_postgres: bool = False,
) -> RedshiftQuery | None:
    all_filters = [*filters, *or_filters, *__get_all_filters(filter_expression)]
    main_table = detect_main_query_table(dimensions, measures, all_filters)

    if not main_table:
        raise QueryBuildingException("Main table not detected")

    filter_tables = detect_filter_tables(all_filters)

    adjust_fields_to_main_table(main_table, dimensions)
    adjust_fields_to_main_table(main_table, measures)
    adjust_fields_to_main_table(main_table, all_filters)

    query_filters = __build_filters(filters, use_postgres)
    or_query_filters = __build_filters(or_filters, use_postgres)
    sub_arrays = __get_sub_arrays(dimensions + all_filters + measures, use_postgres)
    query_filter_expression = __build_filter_expression(filter_expression, use_postgres)
    combined_filters = query_filter_expression
    if query_filters or or_query_filters:
        inner_filters = [*query_filters]
        if or_query_filters:
            inner_filters.append(RedshiftFilterExpression("OR", or_query_filters))
        if query_filter_expression:
            inner_filters.append(query_filter_expression)
        combined_filters = RedshiftFilterExpression("AND", inner_filters)

    if len(filter_tables) == 0 or len(filter_tables) == 1 and main_table in filter_tables:
        return __build_main_query(main_table, dimensions, measures, combined_filters, sub_arrays, use_postgres)
    else:
        return __build_main_query_with_subquery(
            main_table, dimensions, measures, combined_filters, sub_arrays, use_postgres
        )


def __execute_query(db: Session, query_config: QueryDataRequest) -> list[Row]:
    cache_client = get_redis_cache_client()

    dimensions = query_config.dimensions or []
    filters = query_config.filters or []
    or_filters = query_config.or_filters or []
    measures = query_config.measures or []

    organization_id = next(
        (
            int(filter.value)
            for filter in query_config.filters
            if filter.field.endswith(".organization_id") and filter.operator == "="
        ),
        None,
    )
    use_postgres = FeatureFlagsClient.is_feature_enabled(
        FeatureType.USE_POSTGRES_WAREHOUSE, organization_id=organization_id
    )

    query = _build_query(dimensions, measures, filters, or_filters, query_config.filter_expression, use_postgres)
    if not measures:
        raise QueryBuildingException("Measures are required to build query")
    if not query:
        raise Exception("Expected query to be built")

    if use_postgres:
        query_sql = query.to_sql(as_subquery=False)
        return measured_execute(db, query_sql).fetchall()

    parameter_values = [] if cache_client else None
    parameter_types = [] if cache_client else None
    query_sql = query.to_sql(
        as_subquery=False,
        parameter_values=parameter_values,
        parameter_types=parameter_types,
    )
    if cache_client:
        if "UNKNOWN" in query_sql:
            logger.warning("UNKNOWN parameter type in query_sql", query_sql=query_sql, query=query)
            query_sql = query.to_sql(as_subquery=False)
            return RedshiftApiClient.execute(query_sql)
        with get_redshift_api_session() as db_session:
            session_id = measured_execute(db_session, "SELECT pg_backend_pid()").fetchone()[0]
            plan_name = f"rq_{hashlib.md5(query_sql.encode()).hexdigest()}"
            key = f"{plan_name}_{session_id}"
            if not cache_client.get_from_cache(key, cls=None, configuration=None):
                prepare_statement = f'PREPARE {plan_name}({",".join(parameter_types)}) AS {query_sql}'
                logger.info("Preparing query", prepare_statement=prepare_statement)
                try:
                    measured_execute(db_session, prepare_statement)
                    day_in_seconds = 60 * 60 * 24
                    cache_client.add_to_cache(key, True, day_in_seconds)
                except Exception as e:
                    logger.warning(
                        "Error preparing query",
                        prepare_statement=prepare_statement,
                        exc_info=e,
                    )
            try:
                logger.info(
                    "Executing query",
                    plan_name=plan_name,
                    parameter_values=parameter_values,
                )
                result = measured_execute(db_session, f'EXECUTE {plan_name}({", ".join(parameter_values)})').fetchall()
                return result
            except Exception as e:
                logger.warning("Error executing query", exc_info=e)
                query_sql = query.to_sql(as_subquery=False)

    return RedshiftApiClient.execute(query_sql)


def __get_column_configs(db: Session) -> list[ColumnConfig]:
    return db.query(ColumnConfig).all()


def __get_type_info_by_field(
    measures: list[QueryMeasureRequest], dimensions: list[QueryDimensionRequest], column_configs: list[ColumnConfig]
) -> dict[str, tuple[str | None, str | None]]:
    fields: set[str] = {dimension.field for dimension in dimensions}
    for measure in measures:
        fields.add(measure.field)

    type_info_by_field: dict[str, tuple[str, str | None]] = {}
    for column_config in column_configs:
        table = QUERY_TABLE_VALUE_BY_COLUMN_CONFIG_PARENT.get(column_config.parent, None)
        if table is None:
            logger.error(
                "Failed to look up query table by column config parent",
                column_config_parent=column_config.parent,
            )
            continue

        field: str = f"{table}.{column_config.column_name}"
        type_info_by_field[field] = column_config.column_type, column_config.units

    return type_info_by_field


def __get_data_type_for_measure_with_dimension(
    measure: QueryMeasureRequest,
) -> str | None:
    if measure.as_group_percentage or measure.as_filter_percentage:
        return "DECIMAL"

    return DATA_TYPE_BY_AGGREGATION.get(measure.aggregation_type, None)


def calculate_query_data(db: Session, query_config: QueryDataRequest) -> QueryDataResponse | None:
    dimensions = query_config.dimensions or []
    measures = sorted(query_config.measures or [], key=lambda d: (d.display_name or d.field))
    query_config.measures = measures
    query_config.filters = sorted(query_config.filters or [], key=attrgetter("field", "operator"))
    query_config.or_filters = sorted(query_config.or_filters or [], key=attrgetter("field", "operator"))

    data = __execute_query(db, query_config)
    if not data:
        return None

    result = QueryDataResponse(measures=[], dimensions=[])
    dimensions_count = len(dimensions)
    measures_count = len(measures)
    dimensions_idx = range(dimensions_count)
    measures_idx = range(measures_count)

    column_configs = __get_column_configs(db)
    type_info_by_field: dict[str, tuple[str, str | None]] = __get_type_info_by_field(
        measures, dimensions, column_configs
    )
    if dimensions_count == 0:
        row = data[0]
        for idx, measure in enumerate(measures):
            type_info = type_info_by_field.get(measure.field, (None, None))
            result.measures.append(
                QueryDataMeasureResponse(
                    display_name=measure.display_name or "",
                    value=round(row[idx] or 0, 2),
                    data_type=type_info[0],
                    unit=type_info[1],
                )
            )

        return result

    aggregations = {}
    total_measures = defaultdict(float)

    for row in data:
        for measure_idx in measures_idx:
            total_measures[measure_idx] += float(row[dimensions_count + measure_idx] or 0)

    for row in data:
        agg_names = [f"d{dimension_idx}!#{row[dimension_idx]}" for dimension_idx in dimensions_idx]
        for dimension_idx in dimensions_idx:
            agg_name = "!#".join(agg_names[dimension_idx::-1])
            if agg_name not in aggregations:
                aggregations[agg_name] = {
                    "values": [0 for _ in measures_idx],
                    "parent": None if dimension_idx == 0 else "!#".join(agg_names[dimension_idx::-1][1:]),
                }

            for measure_idx in measures_idx:
                value = row[dimensions_count + measure_idx] or 0
                if measures[measure_idx].as_group_percentage:
                    value = 100.0 * float(value) / float(total_measures[measure_idx] or value)
                aggregations[agg_name]["values"][measure_idx] = round(
                    aggregations[agg_name]["values"][measure_idx] + value, 2
                )

    dimensions_agg = {}

    for idx, dimension in enumerate(dimensions):
        dimension_type_info = type_info_by_field.get(dimension.field, (None, None))
        for key, agg_dimension in aggregations.items():
            _, agg_key = key.split("!#")[:2]
            if not key.startswith(f"d{idx}"):
                continue

            query_dimension = QueryDataDimensionResponse(
                display_name=agg_key,
                dimensions=[],
                measures=[],
                data_type=dimension_type_info[0],
                unit=dimension_type_info[1],
            )
            query_dimension.measures = []
            for measure_idx, measure in enumerate(measures):
                data_type_for_measure, unit = type_info_by_field.get(measure.field, (None, None))
                data_type_for_measure_dimension = __get_data_type_for_measure_with_dimension(measure)

                response = QueryDataMeasureResponse(
                    display_name=measure.display_name,
                    value=round(agg_dimension["values"][measure_idx], 2),
                    data_type=data_type_for_measure_dimension or data_type_for_measure,
                    unit=unit,
                )
                query_dimension.measures.append(response)

            dimensions_agg[key] = query_dimension
            parent = agg_dimension["parent"]
            if parent:
                dimensions_agg[parent].dimensions.append(query_dimension)

            else:
                result.dimensions.append(query_dimension)

    return result


def calculate_query_table(db: Session, query_config: QueryDataRequest) -> list[list[str]] | None:
    data = __execute_query(db, query_config)
    if not data:
        return None

    result = []
    for row in data:
        result.append([str(round(item, 2)) if isinstance(item, float) else str(item) for item in row])

    return result


def calculate_query_dict(db: Session, query_config: QueryDataRequest) -> str:
    data = __execute_query(db, query_config)

    result = []
    for row in data:
        dict_row = dict(row)
        for key, value in dict_row.items():
            if isinstance(value, float):
                dict_row[key] = round(value, 2)
            elif isinstance(value, datetime):
                dict_row[key] = value.isoformat()
            elif isinstance(value, dict | list):
                dict_row[key] = json.dumps(value, default=str)
            elif isinstance(value, Decimal):
                if value % 1 == 0:
                    dict_row[key] = int(value)
                else:
                    dict_row[key] = round(float(value), 2)

        result.append(dict_row)

    return json.dumps(result, default=str)


def retrieve_lookalike_candidates(db: Session, submission_id: str) -> LookalikeCandidatesResponse | None:
    lookalike_candidates_threshold = 10
    lookalike_intervals_months = [3, 6, 12, 24]

    submission = measured_execute(
        db,
        """
            SELECT id, coverages, created_date, two_digits_primary_naics, organization_id, tiv_sum,
            total_sales, payroll, fni_state, number_of_locations_sum, recommendation_action,
            number_of_units_sum, vehicles_count, losses, broker_id, stage FROM submissions WHERE id = :id
        """,
        {"id": submission_id},
    ).fetchone()

    if not submission:
        return None

    coverages = submission["coverages"] or []

    lookalike_filter_params = {
        "id": submission["id"],  # To exclude the source submission
        "organization_id": submission["organization_id"],
        "two_digits_primary_naics": submission["two_digits_primary_naics"],
    }

    coverage_conditions = []
    for coverage in coverages:
        # Have to do it this way because of the way jsonb_path_exists works (requires " instead of ')
        coverage_conditions.append(
            f""" jsonb_path_exists(
                    coverages::jsonb,
                    '$[*] ? (@.coverage_type == "{coverage['coverage_type']}" && @.name == "{coverage['name']}")'
                 )
            """
        )

    query_suffix = "" if not coverage_conditions else f" AND ({' OR '.join(coverage_conditions)}) "

    # Keep interval for the next query
    last_used_interval = None
    lookalike_candidates = None

    for interval in lookalike_intervals_months:
        last_used_interval = interval

        # Here we are initially trying to get candidates for the small amount of time,
        # if there are not enough candidates we go to the next interval

        lookalike_candidates = measured_execute(
            db,
            f"""
                SELECT id, coverages, created_date,two_digits_primary_naics, organization_id, tiv_sum,
                    total_sales, payroll, fni_state, number_of_locations_sum, recommendation_action,
                    number_of_units_sum, vehicles_count, losses, broker_id, stage FROM submissions
                WHERE id != :id
                AND organization_id = :organization_id
                AND two_digits_primary_naics = :two_digits_primary_naics
                AND is_verified = true
                AND created_date >= (NOW() - INTERVAL '{interval} month')
                {query_suffix}
                ORDER BY created_date DESC
            """,
            lookalike_filter_params,
        ).fetchall()

        if len(lookalike_candidates) >= lookalike_candidates_threshold:
            break

    brokers_bound_rates = measured_execute(
        db,
        f"""
            SELECT broker_id,
            ROUND(
                COUNT(*) FILTER (WHERE stage = 'QUOTED_BOUND') * 100.0 / NULLIF(COUNT(*), 0),
                2
            ) AS bound_rate
            FROM submissions
            WHERE organization_id = :organization_id
            AND broker_id IN :brokers
            AND stage IN ('QUOTED_BOUND', 'QUOTED_LOST', 'QUOTED')
            AND created_date >= (NOW() - INTERVAL '{last_used_interval} month')
            GROUP BY broker_id;
        """,
        {
            "organization_id": submission["organization_id"],
            "brokers": tuple(
                {str(s["broker_id"]) for s in [submission, *lookalike_candidates] if s["broker_id"] is not None}
            ),
        },
    ).fetchall()

    submission_dicts = [dict(row) for row in [submission, *lookalike_candidates]]

    broker_to_bind_rate = {broker_id: bound_rate for broker_id, bound_rate in brokers_bound_rates}
    for s in submission_dicts:
        s["broker_bound_rate"] = broker_to_bind_rate.get(s["broker_id"])

    [original_submission, *lookalike_candidates] = submission_dicts
    return LookalikeCandidatesResponse(
        original_submission=SubmissionInfoForSimilarity(**original_submission),
        lookalike_candidates=[SubmissionInfoForSimilarity(**candidate) for candidate in lookalike_candidates],
    )


def retrieve_historical_bind_rates(
    db: Session, organization_id: int, request: HistoricalBindRatesRequest
) -> HistoricalBindRatesResponse:
    naics_condition = (
        f"AND naics_filter = '{request.naics_filter}'" if request.naics_filter else "AND naics_filter is null"
    )
    query_sql = f"""SELECT LOWER(parameter_value) as parameter_value, bind_rate FROM historical_bind_rates
WHERE organization_id = {organization_id} AND parameter_name = '{request.parameter_name}'
{naics_condition} AND is_negated_filter is {str(request.filter_negated).lower()}
AND LOWER(parameter_value) in ('{"','".join(request.parameter_values)}')"""
    data = measured_execute(db, query_sql).fetchall()
    parameter_values = {row["parameter_value"]: row["bind_rate"] for row in data}
    return HistoricalBindRatesResponse(parameter_values=parameter_values)  # , bind_rates=bind_rates)


def retrieve_profitability_data(db: Session, request: ProfitabilityScoreRequest) -> ProfitabilityScoreResponse:
    requested_coverages = ", ".join([f"'{coverage}'" for coverage in request.coverages])
    query_sql = f"""
    SELECT sic_code, fni_state, profit_per_policy_2025_rating, profit_per_policy_2025,
    remaining_premium_rating_numerical_x, remaining_premium_rating
    FROM secura_profitability_data
    WHERE sic_code like '{request.sic_code}%' AND user_group = '{request.user_group}' AND coverage in ({requested_coverages})
    """
    data = measured_execute(db, query_sql).fetchall()
    scores = []
    for row in data:
        scores.append(
            ProfitabilityScoreEntry(
                sic_code=row["sic_code"],
                fni_state=row["fni_state"],
                profit_per_policy_2025_rating=row["profit_per_policy_2025_rating"],
                profit_per_policy_2025=row["profit_per_policy_2025"],
                remaining_premium_rating_numerical=row["remaining_premium_rating_numerical_x"],
                remaining_premium_rating=row["remaining_premium_rating"],
            )
        )
    return ProfitabilityScoreResponse(scores=scores)  # , bind_rates=bind_rates)


def retrieve_percentiles_data(organization_id: int, request: PercentilesRequest, db: Session) -> PercentilesResponse:
    cache_client = get_redis_cache_client()
    json_request = json.dumps(request, default=str)
    cache_key = f"percentiles_{organization_id}_{hashlib.md5(json_request.encode()).hexdigest()}"

    if cache_client:
        if cache_data := cache_client.get_from_cache(cache_key, cls=PercentilesResponse, configuration=None):
            return cache_data

    response = __retrieve_percentiles_data_from_db(organization_id, request, db)
    if cache_client:
        random_seconds_3_days = random.randint(1, 3 * 24 * 3600)
        expire_time_in_seconds = 7 * 24 * 3600 + random_seconds_3_days
        cache_client.add_to_cache(cache_key, response, expire_time_in_seconds)
    return response


def __retrieve_percentiles_data_from_db(
    organization_id: int, request: PercentilesRequest, db: Session
) -> PercentilesResponse:
    columns = request.columns or []
    columns_to_fact_subtype_id = dict()
    if request.fact_subtype_ids:
        column_configs = (
            db.query(ColumnConfig)
            .filter(ColumnConfig.builder_type == "facts_agg", ColumnConfig.parent == "locations")
            .filter(ColumnConfig.available_for_incremental_refresh.is_(True))
            .all()
        )
        fact_subtype_to_column = dict()
        for column_config in column_configs:
            column_name_suffix = None
            if column_config.builder_config.get("avg", False):
                column_name_suffix = "avg"
            elif column_config.builder_config.get("sum", False):
                column_name_suffix = "sum"
            if column_name_suffix is None:
                continue
            fact_subtype_to_column[column_config.builder_config["fact_subtype_id"]] = (
                f"{column_config.column_name}_{column_name_suffix}"
            )
        for fact_subtype_id in request.fact_subtype_ids:
            if column_name := fact_subtype_to_column.get(fact_subtype_id):
                columns.append(column_name)
                columns_to_fact_subtype_id[column_name] = fact_subtype_id

    select_columns = []
    for column in columns:
        for p in request.percentiles:
            select_columns.append(f"percentile_cont({p / 100}) within group (order by {column}) as {column}__p{p}")

    query = f"select {', '.join(select_columns)} from locations where organization_id = {organization_id}"
    result = measured_execute(db, query).fetchone()
    response_columns = []
    if result is not None:
        for column_alias, value in dict(result).items():
            column_alias_split = column_alias.split("__p")
            column = column_alias_split[0]
            percentile = int(column_alias_split[1])
            if value is not None:
                response_columns.append(
                    PercentileColumnResponse(
                        percentile=percentile,
                        value=value,
                        column=column,
                        fact_subtype_id=columns_to_fact_subtype_id.get(column),
                    )
                )

    return PercentilesResponse(columns=response_columns)
