from dataclasses import dataclass
from datetime import datetime, timedelta

from sqlalchemy.orm import Session

from src.utils import measured_execute


@dataclass
class BindRateConfig:
    parameter: str
    time_delta: timedelta
    end_date_delta: timedelta
    naics_filter: str | None = None
    negated_filter: bool = False


HISTORICAL_BIND_RATES_CONFIG = [
    BindRateConfig(parameter="broker_name", time_delta=timedelta(days=180), end_date_delta=timedelta(days=30)),
    BindRateConfig(
        parameter="broker_name",
        time_delta=timedelta(days=180),
        end_date_delta=timedelta(days=30),
        naics_filter="23",
        negated_filter=False,
    ),
    BindRateConfig(
        parameter="broker_name",
        time_delta=timedelta(days=180),
        end_date_delta=timedelta(days=30),
        naics_filter="23",
        negated_filter=True,
    ),
    BindRateConfig(
        parameter="broker_name",
        time_delta=timedelta(days=180),
        end_date_delta=timedelta(days=30),
        naics_filter="53",
        negated_filter=False,
    ),
    BindRateConfig(
        parameter="broker_name",
        time_delta=timedelta(days=180),
        end_date_delta=timedelta(days=30),
        naics_filter="53",
        negated_filter=True,
    ),
    BindRateConfig(
        parameter="broker_name",
        time_delta=timedelta(days=180),
        end_date_delta=timedelta(days=30),
        naics_filter="72",
        negated_filter=False,
    ),
    BindRateConfig(
        parameter="broker_name",
        time_delta=timedelta(days=180),
        end_date_delta=timedelta(days=30),
        naics_filter="72",
        negated_filter=True,
    ),
    BindRateConfig(parameter="brokerage_name", time_delta=timedelta(days=180), end_date_delta=timedelta(days=0)),
]

bind_rates_table = "historical_bind_rates"
bind_rates_new_table = "historical_bind_rates_new"
bind_rates_old_table = "historical_bind_rates_old"


class BindRatesCalculator:
    def __init__(
        self,
        db_session: Session,
    ):
        self.db_session = db_session

    @staticmethod
    def _get_select_query(config: BindRateConfig):
        # we don't want to take latest submissions because we don't know if they will be bound
        end_date = datetime.today() - config.end_date_delta
        start_date = end_date - config.time_delta
        naics_condition = " "
        naics_filter_value = f"'{config.naics_filter}'" if config.naics_filter else "NULL"
        if config.naics_filter:
            naics_condition = (
                f"AND two_digits_primary_naics != '{config.naics_filter}' "
                if config.negated_filter
                else f"AND two_digits_primary_naics = '{config.naics_filter}' "
            )
        return f"""
            SELECT organization_id,
                   '{config.parameter}' as parameter_name,
                   CAST({config.parameter} as varchar) as parameter_value,
                   {naics_filter_value} as naics_filter,
                   CAST({config.negated_filter} as bool) as is_negated_filter,
                   CAST(SUM(CASE
                            WHEN stage = 'QUOTED_BOUND' THEN 1
                            ELSE 0 END) as decimal) / CAST(COUNT(DISTINCT id) as decimal) as bind_rate
            FROM submissions
            WHERE created_date BETWEEN '{start_date.strftime('%Y-%m-%d')}' AND '{end_date.strftime('%Y-%m-%d')}'
            {naics_condition}
            GROUP BY 1, 3
            """

    def calculate_bind_rates(self):
        select_queries = [self._get_select_query(config) for config in HISTORICAL_BIND_RATES_CONFIG]
        create_table_query = f"""
DROP TABLE IF EXISTS {bind_rates_new_table};
CREATE TABLE {bind_rates_new_table} AS
SELECT * FROM ({" UNION ALL ".join(select_queries)}) as bind_rates;
"""
        measured_execute(self.db_session, create_table_query)
        self.db_session.commit()

        replace_table_query = f"""
DROP TABLE IF EXISTS {bind_rates_old_table};
LOCK TABLE {bind_rates_table};
ALTER TABLE {bind_rates_table} RENAME TO {bind_rates_old_table};
ALTER TABLE {bind_rates_new_table} RENAME TO {bind_rates_table};
"""
        measured_execute(self.db_session, replace_table_query)
        self.db_session.commit()
