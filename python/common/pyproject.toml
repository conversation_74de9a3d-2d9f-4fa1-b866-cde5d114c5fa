[project]
name = "common"
version = "25.5.26.23927.dev0"
description = "Kalepa common package"
authors = [{ name = "Kalepa Tech" }]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "marshmallow-enum~=1.5.1",
    "jsonpath-ng~=1.5",
    "business-rules~=1.0",
    "dataclasses-json~=0.5.4",
    "launchdarkly-server-sdk>=8.1.6",
    "measurement==4.0a8",
    "dateparser~=1.1",
    "regex>2021.8.21",
    "retrying~=1.3.3",
    "redis>=4.5.3",
    "boto3~=1.21",
    "botocore~=1.24",
    "pint>0.19.2",
    "requests>=2.25.0",
    "marshmallow>=3.3.0",
    "marshmallow-oneofschema>=2.0.1",
    "usaddress-scourgify==0.6.0",
    "usaddress<=0.5.13",
    "sendgrid~=6.8",
    "censusgeocode~=0.5.2",
    "slack-sdk~=3.19",
    "pymupdf>=1.22.5,!=1.23.7,<2.0.0",
    "intervaltree~=3.1",
    "ratio~=0.4.0",
    "levenshtein~=0.25.1",
    "cachetools~=5.5.0",
    "publicsuffix2~=2.20191221",
    "setuptools>=75.6.0",
    "fuzzywuzzy>=0.18.0",
    "rapidfuzz>=3.11.0",
    # remove after all projects are migrated to uv — Poetry fails to resolve the dependency correctly when version is only specified in the optional-dependencies section
    "copilot-client-v3",
    "entity-resolution-service-client-v3",
    "facts-client",
    "facts-client-v2",
    "report-transformer-service-client",
    "infrastructure-common",
    "llm-common",
    "static-common",
    "number-parser>=0.3.2",
    "google-cloud-documentai>=3.2.0",
    "reductoai",
]

[project.optional-dependencies]
boss_api_client = ["lxml>=5.0.0", "signxml>=4.0.0"]
paragon_ims_utils = ["paragon-ims-api-client"]

[dependency-groups]
dev = [
    "pytest~=7.2.0",
    "pytest-mock~=3.14.0",
    "fakeredis~=2.10",
    "types-boto3[essential]>=1.37.10",
]
kalepa = [
    "copilot-client-v3",
    "entity-resolution-service-client-v3",
    "facts-client",
    "facts-client-v2",
    "report-transformer-service-client",
    "infrastructure-common",
    "llm-common",
    "static-common",
]

[tool.uv.sources]
copilot-client-v3 = { index = "kalepi" }
entity-resolution-service-client-v3 = { index = "kalepi" }
facts-client = { index = "kalepi" }
facts-client-v2 = { index = "kalepi" }
infrastructure-common = { index = "kalepi" }
llm-common = { index = "kalepi" }
report-transformer-service-client = { index = "kalepi" }
static-common = { index = "kalepi" }
paragon-ims-api-client = { index = "kalepi" }
intervaltree = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[tool.uv]
package = true
default-groups = ["dev", "kalepa"]

[tool.black]
line-length = 120
preview = true
target-version = ['py311']

[tool.isort]
profile = "black"
skip = ["__init__.py"]

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
allowed-confusables = ["–"]
line-length = 120
target-version = "py311"
extend-exclude = [
    "test/",
    "scripts/",
    "common/enums/class_code.py",
    "common/mappings/categories_naics.py",
    "**/__init__.py",
]

[tool.ruff.per-file-ignores]
"common/logic/identification/legal_entity.py" = ["E501"]
"common/enums/fact_subtype.py" = ["E501"]
"common/enums/document_type.py" = ["E501"]
