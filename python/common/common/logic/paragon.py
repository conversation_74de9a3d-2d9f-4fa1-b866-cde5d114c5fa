import re
from abc import abstractmethod
from dataclasses import dataclass, field
from enum import Enum, auto
from functools import cached_property
from typing import TYPE_CHECKING, Optional, Protocol
from uuid import UUID

from static_common.models.address import Address

if TYPE_CHECKING:
    from paragon_ims_api_client.models.insured import Insured
    from paragon_ims_api_client.paragon_ims_client import ParagonIMSAPIClient
    from structlog.stdlib import BoundLogger


ZERO_VALUE_NAME_WORDS = {
    "llc",
    "inc",
    "corp",
    "corporation",
    "incorporated",
    "company",
    "corp.",
    "holdings",
    "management",
}


@dataclass
class IMSSearchStepCandidate:
    insured: "Insured"
    score: float | None


@dataclass
class IMSSearchStepResult:
    step_name: str
    description: str | None = None
    picked_ims_guid: str | None = None
    candidates: list[IMSSearchStepCandidate] = field(default_factory=list)

    @property
    def is_conclusive(self) -> bool:
        return self.picked_ims_guid is not None

    @property
    def picked_insured(self) -> Optional["Insured"]:
        if not self.picked_ims_guid:
            return None

        return next(
            candidate.insured for candidate in self.candidates if candidate.insured.insured_guid == self.picked_ims_guid
        )

    def __add__(self, other: Optional["IMSSearchStepResult"]) -> "IMSSearchStepResult":
        if other is None:
            return self

        return IMSSearchStepResult(
            step_name=f"Combined {self.step_name} and {other.step_name}",
            description=f"{self.description} and {other.description}",
            picked_ims_guid=self.picked_ims_guid or other.picked_ims_guid,
            candidates=self.candidates + other.candidates,
        )


@dataclass
class IMSSearchInput:
    dba_name: str | None = None
    legal_name: str | None = None
    all_names: list[str] | None = None

    entity_addresses: list[Address] | None = None

    requested_name: str | None = None
    requested_address: Address | None = None

    @property
    def all_aliases(self) -> list[str]:
        aliases = set()
        if self.dba_name:
            aliases.add(self.dba_name)
        if self.legal_name:
            aliases.add(self.legal_name)
        if self.requested_name:
            aliases.add(self.requested_name)

        aliases.update(self.all_names or [])

        return list(aliases)


@dataclass(frozen=True)
class IMSSearchResult:
    search_input: IMSSearchInput
    steps: list[IMSSearchStepResult] = field(default_factory=list)

    @cached_property
    def conclusive_step(self) -> IMSSearchStepResult | None:
        return next((step for step in self.steps if step.is_conclusive), None)

    @property
    def is_conclusive(self) -> bool:
        return self.conclusive_step is not None

    @property
    def picked_ims_guid(self) -> str | None:
        return self.conclusive_step.picked_ims_guid if self.conclusive_step else None

    @property
    def picked_insured(self) -> Optional["Insured"]:
        return self.conclusive_step.picked_insured if self.conclusive_step else None


@dataclass
class IMSSearchPipelinePayload:
    search_input: IMSSearchInput
    ims_client: "ParagonIMSAPIClient"
    log: "BoundLogger"


class ISearchStep(Protocol):
    @abstractmethod
    def execute_step(self, payload: IMSSearchPipelinePayload) -> IMSSearchStepResult:
        ...


class ParagonLines(Enum):
    PSP_E3_PROPERTY = auto()
    WORKERS_COMPENSATION = auto()

    def get_line_guid(self) -> UUID:
        match self:
            case ParagonLines.PSP_E3_PROPERTY:
                return UUID("27fdd869-bcaf-44e0-87d3-abac050d640c")
            case ParagonLines.WORKERS_COMPENSATION:
                return UUID("ca3d0f36-6d25-4d7b-a086-439161fc0ab2")
            case _:
                raise NotImplementedError(f"Paragon line {self} is not yet supported")


class ParagonEmails(Enum):
    EXCESS_AND_SURPLUS = auto()
    EXCESS_AND_SURPLUS_UNSUPPORTED = auto()
    WORKERS_COMPENSATION = auto()
    PSP_E3_PROPERTY_QUOTE = auto()
    PSP_E3_PROPERTY = auto()
    ALLY_AUTO = auto()
    TRIDENT_PUBLIC_RISK = auto()

    def paragon_program_email(self):
        """
        Paragon source and target email for various programs.
        """
        match self:
            case ParagonEmails.EXCESS_AND_SURPLUS:
                return "<EMAIL>"
            case ParagonEmails.EXCESS_AND_SURPLUS_UNSUPPORTED:
                return "<EMAIL>"
            case ParagonEmails.WORKERS_COMPENSATION:
                return "<EMAIL>"
            case ParagonEmails.PSP_E3_PROPERTY_QUOTE:
                return "<EMAIL>"
            case ParagonEmails.PSP_E3_PROPERTY:
                return "<EMAIL>"
            case ParagonEmails.TRIDENT_PUBLIC_RISK:
                raise NotImplementedError("Paragon Trident Public Risk program doesn't have an inbox")
            case ParagonEmails.ALLY_AUTO:
                raise NotImplementedError("Paragon Ally Auto program is not yet supported")
            case _:
                raise NotImplementedError(f"Paragon program {self} is not yet supported")

    def kalepa_forwarding_email(self):
        """
        Kalepa email that is used to forward Paragon submissions.
        """
        match self:
            case ParagonEmails.EXCESS_AND_SURPLUS:
                return "<EMAIL>"
            case ParagonEmails.EXCESS_AND_SURPLUS_UNSUPPORTED:
                return "<EMAIL>"
            case ParagonEmails.WORKERS_COMPENSATION:
                return "<EMAIL>"
            case ParagonEmails.PSP_E3_PROPERTY_QUOTE | ParagonEmails.PSP_E3_PROPERTY:
                return "<EMAIL>"
            case ParagonEmails.ALLY_AUTO:
                return "<EMAIL>"
            case ParagonEmails.TRIDENT_PUBLIC_RISK:
                return "<EMAIL>"
            case _:
                raise NotImplementedError(f"Paragon program {self} is not yet supported")

    @staticmethod
    def kalepa_group_email():
        """
        Kalepa Google Group for Paragon communications.
        """
        return "<EMAIL>"

    @staticmethod
    def kalepa_comms_out_group_email():
        """
        Kalepa Google Group for CC on Paragon outgoing communications.
        """
        return "<EMAIL>"

    @staticmethod
    def kalepa_file_intake_group_email():
        """
        Kalepa Google Group for incoming Paragon files.
        """
        return "<EMAIL>"

    @staticmethod
    def paragon_agency_appointments_email():
        """
        Paragon agency appointments which is responsible for adding new brokers.
        """
        return "<EMAIL>"

    @staticmethod
    def paragon_excess_and_surplus_autoforward_email():
        """
        Paragon Excess and Surplus auto-forward email.
        """
        return "<EMAIL>"


def normalize_name_for_search(name: str | None) -> str | None:
    if name is None:
        return None

    name_words = name.split()
    name_words = [word.lower() for word in name_words]

    name_words = [word for word in name_words if word not in ZERO_VALUE_NAME_WORDS]

    return " ".join(name_words)


def normalize_address1_for_search(
    address: str | None,
    aggressive_normalization: bool = False,
) -> str | None:
    if address is None:
        return None

    if aggressive_normalization:
        address = re.sub(r"\s+(north|south|east|west|n|s|e|w)\s+", " ", address, count=1, flags=re.IGNORECASE)

    return address


def split_requested_name(name: str) -> list[str]:
    if not name:
        return [name]

    from common.logic.entity_resolution.name import NameSplitter

    entity_names = NameSplitter.proces_name_into_entities_names(name)

    all_names = set()
    for entity_name in entity_names:
        all_names.update(en.value for en in entity_name.names)

    return list(all_names) or [name]
