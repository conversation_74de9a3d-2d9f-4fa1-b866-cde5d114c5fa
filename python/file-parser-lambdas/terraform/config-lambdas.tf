locals {
  common_lambdas_env = {
    DD_TRACE_ENABLED                       = false
    DD_REMOTE_CONFIGURATION_ENABLED        = true
    DD_TRACE_URLLIB3_ENABLED               = false
    SAFE_INIT_RESOLVE_SECRETS              = "true"
    SAFE_INIT_CACHE_SECRETS                = "true"
    SAFE_INIT_SECRET_CACHE_REDIS_HOST      = local.redis_cache_host
    SAFE_INIT_SECRET_CACHE_REDIS_PORT      = local.redis_cache_port
    AWS_REGION_DEPLOYED_TO                 = local.aws_region_deployed_to
    AWS_ACCOUNT_DEPLOYED_TO                = local.aws_account_deployed_to
    COPILOT_API_V3_URL                     = "http://copilot-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
    COPILOT_API_V3_PUBLIC_URL              = "${local.env_config.kalepa_public_domain}/copilot/api/v3.0"
    UPLOADS_S3_BUCKET                      = local.workflow_parameters.copilot_uploads_bucket_names_by_region["us-east-1"]
    REDIS_CACHE_HOST                       = local.redis_cache_host
    REDIS_CACHE_PORT                       = local.redis_cache_port
    AZURE_READ_ANALYZE_ENDPOINT_SECRET_ARN = local.regional_secret_arns["azure-read-endpoint"]
    AZURE_READ_SUBSCRIPTION_KEY_SECRET_ARN = local.regional_secret_arns["azure-read-api-key"]
    LAUNCH_DARKLY_API_KEY_SECRET_ARN       = local.regional_secret_arns["launch-darkly-api-key"]
    # Lambda Names
    PROCESS_ACORD_FILE_LAMBDA_NAME    = "${local.region_specific_parameters.lambda.function_name_prefix}process-acord-file"
    PROCESS_LOSS_RUN_FILE_LAMBDA_NAME = "${local.region_specific_parameters.lambda.function_name_prefix}process-loss-run-file"
  }

  lambdas = { # please use camelCase for Lambda names
    processAcordFile = {
      handler     = "file_parser_lambdas.acords.process_acord_file"
      memory_size = 2048
      timeout     = 300
      environment = {
        SENSIBLE_API_KEY_SECRET_ARN  = local.regional_secret_arns["sensible-api-key"]
        SENSIBLE_FILE_KEY_SECRET_ARN = local.regional_secret_arns["sensible-file-key"]
        REDUCTO_API_KEY_SECRET_ARN = local.regional_secret_arns["reducto-api-key"]
        SENSIBLE_BASE_URL            = "https://api.sensible.so/v0"
        KALEPA_SENSIBLE_WEBHOOK_PATH = "/webhooks/sensible/acords"
      }
    }
    processLossRunFile = {
      handler     = "file_parser_lambdas.loss_run.process_loss_run_file"
      memory_size = 8192
      timeout     = 900
      environment = {
        SENSIBLE_API_KEY_SECRET_ARN  = local.regional_secret_arns["sensible-api-key"]
        SENSIBLE_FILE_KEY_SECRET_ARN = local.regional_secret_arns["sensible-file-key"]
        SENSIBLE_BASE_URL            = "https://api.sensible.so/v0"
        KALEPA_SENSIBLE_WEBHOOK_PATH = "/webhooks/sensible/lossruns"
      }
    }
  }
}
