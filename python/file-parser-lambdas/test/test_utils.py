import pytest

from file_parser_lambdas.processor.acords import _replace_none_value_dicts
from file_parser_lambdas.utils import file_is_spreadsheet


def test_is_spreadsheet():
    for name in [
        "UB009R185374.xls.html",
        "UB009R185374.xls",
        "UB009R185374.xlsx",
        "UB009R185374.xlsm",
        "UB009R185374.xlsb",
        "UB009R185374.csv",
        "UB009R185374.xlsm",
        "x.csv.html",
        "x.xml",
        "x.xml.html",
    ]:
        assert file_is_spreadsheet(name)


def test_is_not_spreadsheet():
    for name in [
        "UB009R185374.html",
        "UB009R185374.pdf",
        "UB009R185374.doc",
        "UB009R185374.docx",
        "UB009R185374.png",
        "UB009R185374",
    ]:
        assert not file_is_spreadsheet(name)


@pytest.mark.parametrize(
    "input_data, expected_output",
    [
        ({"value": None}, None),
        ({"value": "Valid"}, {"value": "Valid"}),
        (
            {
                "field_1": {"value": None},
                "field_2": {"value": "Non-null"},
                "nested": {"inner_field": {"value": None}, "another_field": [{"value": None}, {"value": 123}]},
                "list_of_dicts": [{"value": None}, {"value": "OK"}, {"another_key": {"value": None}}],
            },
            {
                "field_1": None,
                "field_2": {"value": "Non-null"},
                "nested": {"inner_field": None, "another_field": [None, {"value": 123}]},
                "list_of_dicts": [None, {"value": "OK"}, {"another_key": None}],
            },
        ),
        (["text", {"value": None}, {"key": {"value": None}}], ["text", None, {"key": None}]),
    ],
)
def test_replace_none_value_dicts(input_data, expected_output):
    assert _replace_none_value_dicts(input_data) == expected_output
