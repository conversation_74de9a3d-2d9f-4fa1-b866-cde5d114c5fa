[project]
name = "file-parser-lambdas"
version = "1.0.0"
description = "Lambda functions dedicated for parsing files"
authors = [{ name = "Kalepa Tech" }]
requires-python = ">=3.11,<3.12"
readme = "README.md"
dependencies = [
    "en-core-web-sm",
    "measurement==4.0a8",
    "dataclasses-json>=0.5.13,<0.6",
    "datadog-lambda",
    "ddtrace",
    "html5lib~=1.1",
    "boto3-type-annotations>=0.3.1,<0.4",
    "sentry-sdk>=1.9.2,<2",
    "pillow>=10.2.0,<11",
    "werkzeug>=2.0.1,<3",
    "lxml>=5.0.0,<6",
    "pymupdf>=1.22.5,!=1.23.7,<2.0.0",
    "common",
    "datascience_common",
    "infrastructure-services-common",
    "infrastructure-common",
    "events-common",
    "xlrd",
    "file-processing",
    "copilot-client-v3",
    "llm-common",
    "redis>=5.1.0,<6",
    "reductoai"
]

[dependency-groups]
dev = ["pytest>=5.4.2", "boto3>=1.26.18,<2", "botocore>=1.29.18,<2"]
kalepa = [
    "common",
    "datascience_common",
    "infrastructure-services-common",
    "infrastructure-common",
    "events-common",
    "xlrd",
    "file-processing",
    "copilot-client-v3",
    "llm-common",
    "static-common",
]

[tool.uv.sources]
common = { index = "kalepi" }
copilot-client-v3 = { index = "kalepi" }
datascience_common = { index = "kalepi" }
events-common = { index = "kalepi" }
file-processing = { index = "kalepi" }
infrastructure-common = { index = "kalepi" }
infrastructure-services-common = { index = "kalepi" }
llm-common = { index = "kalepi" }
static-common = { index = "kalepi" }
xlrd = { index = "kalepi" }
en-core-web-sm = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[tool.uv]
default-groups = ["dev", "kalepa"]


[tool.hatch.build.targets.sdist]
include = ["file_parser_lambdas"]

[tool.hatch.build.targets.wheel]
include = ["file_parser_lambdas"]

[tool.hatch.metadata]
allow-direct-references = true

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 120
target-version = ['py311']

[tool.isort]
profile = "black"
skip = ["__init__.py"]

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
line-length = 120
target-version = "py311"
extend-exclude = ["test/", "**/__init__.py"]
