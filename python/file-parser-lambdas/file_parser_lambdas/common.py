import os
from functools import partial
from typing import Callable, Dict, List, Optional, Tuple, Union
from uuid import UUID

from file_processing.acords.postprocessing import get_inhouse_processing_enabled
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.sensible import SensibleStatus, SensibleUploadStatus
from static_common.enums.step_function import StepFunctionStatus
from structlog import BoundLogger
from werkzeug.datastructures import FileStorage

from file_parser_lambdas.clients.copilot import CopilotV3Client
from file_parser_lambdas.clients.s3 import S3Client
from file_parser_lambdas.clients.sensible import SensibleClient
from file_parser_lambdas.models.copilot_file_processing_state import (
    CopilotFileProcessingState,
)
from file_parser_lambdas.models.error import Error
from file_parser_lambdas.models.file_info import FileInfo
from file_parser_lambdas.models.parsing_result import ParsingResult
from file_parser_lambdas.processor.acords import process_acord, reducto_process_acord
from file_parser_lambdas.schemas.file_info_schema import FileInfoSchema
from file_parser_lambdas.schemas.parsing_result_schema import ParsingResultSchema


def create_sensible_client(sensible_api_key: str, sensible_file_key: str, s3_client: Optional[S3Client] = None):
    capi_public_domain = os.environ["COPILOT_API_V3_PUBLIC_URL"]
    sensible_webhook_path = os.environ["KALEPA_SENSIBLE_WEBHOOK_PATH"]
    full_webhook_url = f"{capi_public_domain}{sensible_webhook_path}"
    sensible_client = SensibleClient(
        os.environ["SENSIBLE_BASE_URL"],
        full_webhook_url,
        sensible_api_key,
        sensible_file_key,
        os.environ["KALEPA_ENV"],
        s3_client,
    )
    return sensible_client


def prepare_return_result(status: StepFunctionStatus, error: Error, file_info: FileInfo) -> Dict:
    """
    Prepares result for calling StepFunction
    """
    result = ParsingResult(
        file_info.file_id,
        file_info.submission_id,
        status,
        error=error,
        processing_finished=False,
        organization_id=file_info.organization_id,
        classification=file_info.classification,
    )
    return ParsingResultSchema().dump(result)


def _get_file_processing_state_from_capi(
    copilot_v3_client: CopilotV3Client, submission_id: UUID, file_id: UUID, log: BoundLogger, file_info
) -> Tuple[Optional[CopilotFileProcessingState], Optional[Dict]]:
    try:
        file_processing_state = copilot_v3_client.get_file_processing_state(
            submission_id=submission_id, file_id=file_id
        )
        if file_processing_state:
            log.info("Got file processing state from CAPI", file_processing_state=file_processing_state)
            return file_processing_state, None
        error = Error(Error="CAPI File is missing, possibly has been deleted")
        return None, prepare_return_result(StepFunctionStatus.SUCCESS, error, file_info)
    except Exception:
        log.exception("Failed to get file processing state from CAPI")
        error = Error(Error="Failed to get file processing state from CAPI")
        return None, prepare_return_result(StepFunctionStatus.FAILURE, error, file_info)


def _processing_not_enabled_for_this_file(
    capi_file_processing_state: CopilotFileProcessingState,
    file_info: FileInfo,
    update_processing_state: Callable,
    log: BoundLogger,
) -> Optional[Dict]:
    if not capi_file_processing_state.is_enabled:
        error = (
            f"{file_info.file_type} processing is not enabled for this organization, "
            f"updating status in Copilot API and finishing here"
        )
        log.info(error)
        if error_result := update_processing_state(
            was_processing_successful=True,
            file_processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
            sensible_status=None,
            send_event=True,
        ):
            return error_result
        return prepare_return_result(StepFunctionStatus.SUCCESS, Error(Error=error), file_info)
    return None


def _not_eligible_for_processing(
    capi_file_processing_state: CopilotFileProcessingState,
    file_info: FileInfo,
    update_processing_state: Callable,
    log: BoundLogger,
) -> Optional[Dict]:
    if not capi_file_processing_state.eligible_for_processing:
        error = (
            f"{file_info.file_type} file is not eligible for processing, "
            f"updating status in Copilot API and finishing here"
        )
        log.info(error)
        if error_result := update_processing_state(
            was_processing_successful=True,
            file_processing_state=FileProcessingState.PROCESSED,
            sensible_status=capi_file_processing_state.sensible_status,
            send_event=True,
        ):
            return error_result
        return prepare_return_result(StepFunctionStatus.SUCCESS, Error(Error=error), file_info)
    return None


def _grab_file_from_s3(
    s3_client: S3Client, file_info: FileInfo, log: BoundLogger
) -> Tuple[Optional[FileStorage], Optional[Dict]]:
    try:
        log.info(f"Downloading {file_info.file_type} file from S3...")
        file: FileStorage = s3_client.get_file_as_file_storage(file_info.s3_key)
        log.info(f"Downloaded {file_info.file_type} file from S3. Parsing here in lambda")
        return file, None
    except Exception:
        log.exception(f"Failed to download {file_info.file_type} file from S3", s3_key=file_info.s3_key)
        error = Error(Error=f"Failed to download {file_info.file_type} file from S3")
        return None, prepare_return_result(StepFunctionStatus.FAILURE, error, file_info)


def _check_if_file_is_empty(
    file: FileStorage,
    file_info: FileInfo,
    capi_file_processing_state: CopilotFileProcessingState,
    update_processing_state: Callable,
    log: BoundLogger,
) -> Optional[Dict]:
    log.info(f"Checking if {file_info.file_type} file is empty...")
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)
    if file_size == 0:
        log.warning(f"{file_info.file_type} file is empty", s3_key=file_info.s3_key)
        if error_result := update_processing_state(
            was_processing_successful=True,
            file_processing_state=FileProcessingState.PROCESSED,
            sensible_status=capi_file_processing_state.sensible_status,
            send_event=True,
        ):
            return error_result
        error = Error(Error=f"{file_info.file_type} file is empty")
        return prepare_return_result(StepFunctionStatus.SUCCESS, error, file_info)

    log.info(f"{file_info.file_type} file is not empty, continuing with parsing", file_size=file_size)
    return None


def _update_processing_state_in_capi(
    log: BoundLogger,
    copilot_v3_client: CopilotV3Client,
    file_info: FileInfo,
    submission_id: Union[str, UUID],
    file_id: Union[str, UUID],
    organization_id: int,
    was_processing_successful: bool,
    parsed_result_cache_key: Optional[str] = None,
    file_processing_state: Optional[FileProcessingState] = None,
    sensible_status: Optional[SensibleStatus] = None,
    update_sensible_extraction: Optional[bool] = False,
    sensible_upload_status: Optional[SensibleUploadStatus] = None,
    sensible_extraction_id: Optional[UUID] = None,
    sensible_call_made: Optional[bool] = False,
    send_event: Optional[bool] = True,
    error_message: Optional[str] = None,
) -> Optional[Dict]:
    """
    Calls CAPI to update all necessary data related to file and Sensible processing
    """
    try:
        copilot_v3_client.update_file_processing_state(
            submission_id=submission_id,
            organization_id=organization_id,
            file_id=file_id,
            parsed_result_cache_key=parsed_result_cache_key,
            was_processing_successful=was_processing_successful,
            file_processing_state=file_processing_state,
            sensible_status=sensible_status,
            update_sensible_extraction=update_sensible_extraction,
            sensible_upload_status=sensible_upload_status,
            sensible_extraction_id=sensible_extraction_id,
            sensible_call_made=sensible_call_made,
            send_event=send_event,
            error_message=error_message,
        )
        return None
    except Exception as error:
        log.exception("Failed to update file processing state in CAPI")
        error = Error(Error=f"Failed to update file processing state in CAPI {error!s}")
        return prepare_return_result(StepFunctionStatus.FAILURE, error, file_info)


def prepare_processing(
    event: Dict,
    logger: BoundLogger,
    copilot_v3_client: CopilotV3Client,
    capi_uploads_bucket_s3_client: S3Client,
    expected_file_type: FileType,
) -> Tuple[Optional[FileStorage], FileInfo, Optional[Dict], CopilotFileProcessingState, BoundLogger, Callable]:
    file_info: FileInfo = FileInfoSchema().load(event)
    logger.info("Parsed event into model", file_info=file_info)
    submission_id: UUID = file_info.submission_id
    file_id: UUID = file_info.file_id
    log = logger.bind(
        submission_id=str(submission_id), file_id=str(file_id), file_name=file_info.name, file_s3_key=file_info.s3_key
    )

    # -- Get CAPI file processing state for this sub and file
    log.info(f"Calling CAPI to get {file_info.file_type} file status")
    capi_file_processing_state, error = _get_file_processing_state_from_capi(
        copilot_v3_client, submission_id, file_id, log, file_info
    )
    if error:
        return None, file_info, error, capi_file_processing_state, log, None

    # -- Check if file type is the one expected by the lambda
    if capi_file_processing_state.file_type != expected_file_type:
        error = Error(Error=f"File type is {capi_file_processing_state.file_type}, expected {expected_file_type}")
        return None, file_info, error, capi_file_processing_state, log, None

    update_processing_state = partial(
        _update_processing_state_in_capi,
        log=log,
        copilot_v3_client=copilot_v3_client,
        file_info=file_info,
        submission_id=submission_id,
        file_id=file_id,
        organization_id=capi_file_processing_state.organization_id,
    )

    # -- Processing not enabled for this file
    processing_not_enabled = _processing_not_enabled_for_this_file(
        capi_file_processing_state, file_info, update_processing_state, log
    )
    if processing_not_enabled:
        return None, file_info, processing_not_enabled, capi_file_processing_state, log, None

    # -- File is not eligible for processing (but enabled)
    not_eligible = _not_eligible_for_processing(capi_file_processing_state, file_info, update_processing_state, log)
    if not_eligible:
        return None, file_info, not_eligible, capi_file_processing_state, log, None

    file, error = _grab_file_from_s3(capi_uploads_bucket_s3_client, file_info, log)
    if error:
        return None, file_info, error, capi_file_processing_state, log, None

    # -- Quick deal with empty files
    empty_file = _check_if_file_is_empty(file, file_info, capi_file_processing_state, update_processing_state, log)
    if empty_file:
        return None, file_info, empty_file, capi_file_processing_state, log, None

    return file, file_info, None, capi_file_processing_state, log, update_processing_state


def process_pdf_file(
    file: FileStorage,
    file_info: FileInfo,
    capi_file_processing_state: CopilotFileProcessingState,
    log: BoundLogger,
    update_processing_state: Callable,
    capi_webhook_method: Callable,
    sensible_client: SensibleClient,
    sensible_types: List[str],
    copilot_client_v3: CopilotV3Client,
) -> Dict:
    log.info("File is a PDF, starting processing...")
    # This is Kalepa's custom key passed and then returned by Sensible in webhook.
    # Key is composed of organization_id, submission_id, file_id and Kalepa's secret key so that
    # we can verify in the webhook that it's coming from Sensible as result of our request.
    # (Webhook is not protected by Auth0 JWT token check)
    sensible_payload_key = sensible_client.create_payload_key(
        file_info.submission_id, capi_file_processing_state.organization_id, file_info.file_id
    )

    processing_error = None
    step_function_status = StepFunctionStatus.SUCCESS
    # -- PDF already processed
    if capi_file_processing_state.file_already_processed:
        error = "File already processed, skipping processing"
        log.info(error)
        processing_error = Error(Error=error)
        if error_result := update_processing_state(
            was_processing_successful=True,
            file_processing_state=FileProcessingState.PROCESSED,
            sensible_status=capi_file_processing_state.sensible_status,
            send_event=True,
        ):
            return error_result
    # -- PDF already uploaded to Sensible and we are waiting for Sensible results
    elif capi_file_processing_state.file_already_uploaded_to_sensible:
        error = "File already uploaded to Sensible, skipping processing"
        log.info(error)
        processing_error = Error(Error=error)
        if error_result := update_processing_state(
            was_processing_successful=False,
            file_processing_state=FileProcessingState.PROCESSING_FAILED,
            sensible_status=None,
            send_event=True,
            error_message="The same file was already uploaded to sensible",
        ):
            return error_result
    # -- We already have Sensible response for file with the same hash - let's use it without Sensible roundtrip
    elif capi_file_processing_state.is_sensible_response_cached:
        log.info("Sensible response is in cache, calling webhook with cache key")
        # We have sensible response cached with file_hash == let's call CAPI Webhook just like Sensible would
        # but passing cache key instead of JSON body. Code will fetch cache and do all the work that
        # webhook should do.
        try:
            capi_webhook_method(capi_file_processing_state.file_hash, file_info.file_id, sensible_payload_key)
        except Exception:
            log.exception("Failed to call Sensible webhook with cache")
        # no update of any status as analysed from original CAPI implementation (strange!!??)
    # -- We can process the document in-house without calling sensible
    elif get_inhouse_processing_enabled(file_info.file_version):
        log.info("Processing in-house")
        inhouse_processing_successful = process_acord(file, file_info, copilot_client_v3, log)

        if not inhouse_processing_successful:
            log.warning("In-house processing failed, falling back to Sensible")
            return _call_sensible(
                file,
                sensible_client,
                sensible_payload_key,
                sensible_types,
                update_processing_state,
                file_info,
                capi_file_processing_state,
                log,
            )
    elif file_info.file_version in ClassificationDocumentType._supported_acords_with_versions().get(
        ClassificationDocumentType.ACORD_139.value
    ):
        log.info("Processing ACORD using Reducto")
        return reducto_process_acord(file_info, copilot_client_v3, log)
    else:
        return _call_sensible(
            file,
            sensible_client,
            sensible_payload_key,
            sensible_types,
            update_processing_state,
            file_info,
            capi_file_processing_state,
            log,
        )

    return prepare_return_result(step_function_status, processing_error, file_info)


def _call_sensible(
    file: FileStorage,
    sensible_client: SensibleClient,
    sensible_payload_key: str,
    sensible_types: list[str],
    update_processing_state: Callable,
    file_info: FileInfo,
    capi_file_processing_state: CopilotFileProcessingState,
    log: BoundLogger,
) -> dict:
    processing_error = None
    step_function_status = StepFunctionStatus.SUCCESS
    # -- We should call Sensible with this PDF, but monthly call quota for this client (org_id) is exhausted
    if capi_file_processing_state.sensible_quota_exhausted:
        error = (
            f"Need to pass file to Sensible but quota is exhausted for this "
            f"client(org_id={capi_file_processing_state.organization_id}), skipping processing"
        )
        log.error(error)
        processing_error = Error(Error=error)
        if error_result := update_processing_state(
            was_processing_successful=False,
            file_processing_state=FileProcessingState.PROCESSING_FAILED,
            sensible_status=capi_file_processing_state.sensible_status,
            send_event=True,
            error_message=f"Sensible quota exhausted for this client({capi_file_processing_state.organization_id})",
        ):
            return error_result

    # -- We need to call Sensible with this PDF: no cache, no quota exhausted, no file already processed
    log.info("Need to pass file to Sensible; Quota not exhausted yet for this client so sending it over")
    # -- Ask Sensible to prepare S3 bucket for upload for us
    try:
        log.info("Generating upload url from Sensible")
        upload_url, upload_id = sensible_client.get_sensible_upload_url(sensible_payload_key, sensible_types)
    except Exception as error:
        log.exception("Failed to get upload url from Sensible")
        if error_result := update_processing_state(
            was_processing_successful=False,
            file_processing_state=FileProcessingState.PROCESSING_FAILED,
            sensible_status=capi_file_processing_state.sensible_status,
            update_sensible_extraction=True,
            sensible_upload_status=SensibleUploadStatus.CANNOT_CREATE_BUCKET,
            send_event=True,
            error_message=f"Failed to get upload url from Sensible: {error!s}",
        ):
            return error_result
        processing_error = Error(Error=f"Failed to get upload url from Sensible: {error!s}")
        step_function_status = StepFunctionStatus.FAILURE
        return prepare_return_result(step_function_status, processing_error, file_info)

    # -- Finally upload to Sensible S3 - they will start processing after upload is finished and eventually
    # -- call our webhook with results.
    try:
        log.info("Uploading file to Sensible", upload_id=upload_id, upload_url=upload_url)
        sensible_client.upload_file_to_sensible(file, upload_url, file_info.file_id)
        log.info("File successfully uploaded to Sensible for parsing", upload_id=upload_id, upload_url=upload_url)
        if error_result := update_processing_state(
            was_processing_successful=True,
            file_processing_state=FileProcessingState.PROCESSING,
            sensible_status=capi_file_processing_state.sensible_status,
            update_sensible_extraction=True,
            sensible_upload_status=SensibleUploadStatus.AWAITING_RESPONSE,
            sensible_extraction_id=upload_id,
            sensible_call_made=True,
            send_event=False,
        ):
            return error_result
    except Exception as error:
        log.exception("Failed to upload file to Sensible", upload_id=upload_id)
        if error_result := update_processing_state(
            was_processing_successful=False,
            file_processing_state=FileProcessingState.PROCESSING_FAILED,
            sensible_status=capi_file_processing_state.sensible_status,
            update_sensible_extraction=True,
            sensible_upload_status=SensibleUploadStatus.UPLOAD_FAILED,
            sensible_extraction_id=upload_id,
            send_event=True,
            error_message=f"Failed to to upload file to Sensible: {error!s}",
        ):
            return error_result
        processing_error = Error(Error=f"Failed to upload file to Sensible: {error!s}")
        step_function_status = StepFunctionStatus.FAILURE

    return prepare_return_result(step_function_status, processing_error, file_info)
