REDUCTO_SCHEMAS_AND_SYSTEM_PROMPTS = {
    "ACORD_139": {
        "ACORD_139_SCHEMA": {
            "type": "object",
            "properties": {
                "agency_information": {
                    "type": "object",
                    "properties": {
                        "agency": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "string",
                                },
                            },
                            "description": "Agency name or identifier.",
                        },
                        "policy_number": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "string",
                                },
                            },
                            "description": "Policy number for the insurance.",
                        },
                        "carrier": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "string",
                                },
                            },
                            "description": "Insurance carrier name.",
                        },
                        "effective_date": {
                            "type": ["object", "null"],
                            "description": "Effective date of the policy, return in ISO datetime format.",
                            "properties": {
                                "value": {"type": "string", "format": "date-time"},
                            },
                        },
                        "naic_code": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "string",
                                },
                            },
                            "description": "NAIC code of the insurance carrier.",
                        },
                        "date": {
                            "type": ["object", "null"],
                            "description": "Date of the document or transaction, return in i_datetime format.",
                            "properties": {
                                "value": {"type": ["string", "null"], "format": "date-time"},
                            },
                        },
                        "named_insured": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "string",
                                },
                            },
                            "description": "Name of the insured party.",
                        },
                        "agency_customer_id": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "string",
                                },
                            },
                            "description": "Customer ID within the agency.",
                        },
                    },
                },
                "loss_info_section": {
                    "type": "object",
                    "properties": {
                        "coinsurance_percentage": {
                            "type": ["array", "null"],
                            "items": {
                                "type": "object",
                                "properties": {
                                    "value": {
                                        "type": ["number", "null"],
                                    },
                                },
                            },
                            "description": """
                                Percentage values list (80, 90, 100) where the checkbox directly to the LEFT is marked.
                                Do NOT include values with unchecked or right-side checkboxes.

                                For the example below we should return coinsurance_percentage as empty list:

                                [ ] 80%  [ ] Something
                                [ ] 90%  [X] Something
                                [ ] 100% [X] Something

                                If empty return null
                            """,
                        },
                        "applicable_causes_of_loss": {
                            "type": ["array", "null"],
                            "items": {
                                "type": "object",
                                "properties": {
                                    "value": {
                                        "type": "string",
                                    },
                                },
                            },
                            "description": "List of values indicating whether special causes of loss apply.",
                        },
                        "earthquake_cov": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "boolean",
                                },
                            },
                            "description": "Indicates if earthquake coverage is included.",
                        },
                        "flood": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "boolean",
                                },
                            },
                            "description": "Indicates if flood coverage is included.",
                        },
                        "sprinkler_leakage_excl": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "boolean",
                                },
                            },
                            "description": "Indicates if sprinkler leakage exclusion applies.",
                        },
                        "vandalism_excl": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "boolean",
                                },
                            },
                            "description": "Indicates if vandalism exclusion applies.",
                        },
                        "specific_average_rate_requested": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "boolean",
                                },
                            },
                            "description": "Specifies whether a specific average rate is requested.",
                        },
                        "blanket_rate_requested": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "boolean",
                                },
                            },
                            "description": "Specifies whether a blanket rate is requested.",
                        },
                    },
                },
                "statement_of_values_table": {
                    "type": ["array", "null"],
                    "description": (
                        "List of all property entries with valuation details. Do not deduplicate rows even if"
                        "they are the same."
                    ),
                    "items": {
                        "type": "object",
                        "properties": {
                            "class_code": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "string",
                                    },
                                },
                                "required": ["value"],
                                "description": "The classification code for the property.",
                            },
                            "location_number": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "integer",
                                    },
                                },
                                "description": "The location number associated with the property, LOC #.",
                            },
                            "building_number": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "integer",
                                    },
                                },
                                "description": "The building number at the specified location, BLDG #.",
                            },
                            "description_of_property": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "string",
                                    },
                                },
                                "description": "Description of the insured property.",
                            },
                            "street_address": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "string",
                                    },
                                },
                                "description": "Street address of the property.",
                            },
                            "city": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "string",
                                    },
                                },
                                "description": "City where the property is located.",
                            },
                            "state": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "string",
                                    },
                                },
                                "description": "State where the property is located.",
                            },
                            "zip_code": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "string",
                                    },
                                },
                                "description": "ZIP code of the property location.",
                            },
                            "valuation": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "string",
                                    },
                                },
                                "description": "Valuation method (e.g., RC, ACV).",
                            },
                            "subject": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "string",
                                    },
                                },
                                "description": "The subject of insurance for this property (e.g., BPP, Stock).",
                            },
                            "value_100_percent": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "number",
                                    },
                                },
                                "description": "The 100% insurable value of the property, 100% VALUES.",
                            },
                            "rate_or_loss_cost": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "number",
                                    },
                                },
                                "description": "The rate or loss cost associated with the property.",
                            },
                            "premium": {
                                "type": ["object", "null"],
                                "properties": {
                                    "value": {
                                        "type": "number",
                                    },
                                },
                                "description": "The premium charged for this property.",
                            },
                        },
                    },
                },
                "total_section": {
                    "type": "object",
                    "properties": {
                        "total_value": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "number",
                                },
                            },
                            "description": (
                                "Total 100% insurable value for all properties. "
                                "If no value return null for the whole object."
                            ),
                        },
                        "total_rate_or_loss_cost": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "number",
                                },
                            },
                            "description": (
                                "Total rate or loss cost as applicable. "
                                "If no value return null for the whole object."
                            ),
                        },
                        "total_premium": {
                            "type": ["object", "null"],
                            "properties": {
                                "value": {
                                    "type": "number",
                                },
                            },
                            "description": (
                                "Total premium for all listed properties."
                                "If no value return null for the whole object."
                            ),
                        },
                    },
                },
            },
            "required": ["statement_of_values_table"],
        },
        "ACORD_139_SYSTEM_PROMPT": """
                      Extract property level values, rates, and premiums from the document.
                      The document is an ACORD 139 Statement of Values used in commercial property insurance.

                      Document is structured in the following way:
                      In the upper part you're going to have table with Agency Information and Loss Info.
                      Below that, there is a table with Statement of Values, with columns CLASS CODE, LOC #, BLD #,
                      (Description of Property/ Address of Property).
                      VALUATION, SUBJECT, 100% VALUES, RATE OR LOSS COST, PREMIUM.

                      The last part of the document is a summary table with TOTALS. Make sure not to mix it with the
                      Statement of Values table.

                      It is EXTREMELY important to extract ALL rows from the Statement of Values table, even if some
                      fields are blank, or the rows are duplicated.
                      Also, notice (Description of Property/ Address of Property) is one field, not two!

                      COINS % section:
                      - Only include percentage values where the checkbox immediately to the LEFT is clearly marked
                      with an X, or visible fill.
                      - If all boxes are blank (empty squares), return an empty list.
                      - Do not infer anything based on checkboxes on the right of %.

                      For the example below we should return coinsurance_percentage as empty list:

                      [ ] 80%  [ ] Something
                      [ ] 90%  [X] Something
                      [ ] 100% [X] Something

                      Double check you didn't include any values with unchecked or right-side checkboxes.
                      If there is only small mark, do not treat it as checked.

                      For 'Applicable Causes of Loss', extract all checked options (Special, Broad, Basic).
                      If additional options appear, include them as strings too.

                      Extract ALL ROWS (row begins with columns CLASS CODE, LOC #, BLD #) from the Statement of Values
                      table in the document.
                      Ensure no rows are skipped, even if some fields are blank.
                      Skip only rows that have all fields as None.
                      Do not deduplicate rows if they are the same.
                      Return null for fields that are missing or empty.
                      """,
    }
}
