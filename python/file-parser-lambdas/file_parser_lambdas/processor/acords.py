import os

import sentry_sdk
from common.clients.azure import AzureCustomModelClient
from common.clients.reducto_client import ReductoClient
from file_processing.acords.parsing import AcordParser, PageMetadata
from file_processing.acords.postprocessing import get_acord_postprocessor
from file_processing.acords.sensible_format_converter import SensibleFormatConverter
from reducto.resources.extract import AdvancedProcessingOptions, BaseProcessingOptions
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from structlog import BoundLogger
from werkzeug.datastructures.file_storage import FileStorage

from file_parser_lambdas.clients.copilot import CopilotV3Client
from file_parser_lambdas.models.file_info import FileInfo
from file_parser_lambdas.schemas.reducto_schemas_and_prompts import (
    REDUCTO_SCHEMAS_AND_SYSTEM_PROMPTS,
)

sentry_sdk.init(
    os.environ.get("SENTRY_DSN"),
    integrations=[AwsLambdaIntegration(timeout_warning=True)],
    environment=os.environ.get("KALEPA_ENV", "dev"),
)

_azure_layout_client: AzureCustomModelClient = None


def get_azure_layout_client() -> AzureCustomModelClient:
    global _azure_layout_client
    if not _azure_layout_client:
        _azure_layout_client = AzureCustomModelClient(
            base_url=os.getenv("AZURE_READ_ANALYZE_ENDPOINT"),
            subscription_key=os.getenv("AZURE_READ_SUBSCRIPTION_KEY"),
            model_id="prebuilt-document",
        )
    return _azure_layout_client


EXTRACTION_SCORE_THRESHOLD = 0.6


def process_acord(
    file: FileStorage, file_info: FileInfo, copilot_v3_client: CopilotV3Client, logger: BoundLogger
) -> bool:
    try:
        parser = AcordParser()
        page_metadata = []
        parsing_results = parser.parse_acord(
            classification=file_info.classification,
            acord_version=file_info.file_version,
            file=file,
            azure_layout_client=get_azure_layout_client(),
            copilot_client=copilot_v3_client,
            file_id=file_info.file_id,
        )
        if hasattr(parsing_results, "template_result"):
            page_metadata: list[PageMetadata] = parsing_results.page_metadata
            parsing_results = parsing_results.template_result
        parsing_data = _convert_to_sensible_format(parsing_results, file_info.file_version, logger)
        if extraction_score := copilot_v3_client.score_acord_extraction(parsing_data, file_info.file_version.lower()):
            if extraction_score < EXTRACTION_SCORE_THRESHOLD:
                logger.warning(
                    "Extraction score is below threshold, falling back to sensible", extraction_score=extraction_score
                )
                return False
            else:
                logger.info("Extraction score is above threshold", extraction_score=extraction_score)
                copilot_v3_client.ingest_acord_form(
                    _format_processor_results(
                        parsing_data, file_info.file_version.lower(), page_metadata=page_metadata
                    ),
                    file_info.file_id,
                )
                return True
        else:
            logger.warning("Failed to get extraction score, falling back to sensible")
            return False
    except Exception as e:
        logger.exception("Failed to parse ACORD", exc_info=e)
        return False


def _convert_to_sensible_format(parsing_results: dict, acord_version: str, logger: BoundLogger) -> dict | None:
    try:
        if postprocessor := get_acord_postprocessor(acord_version):
            converter = SensibleFormatConverter(postprocessor=postprocessor)
            sensible_format = converter.convert(parsing_results, acord_version=acord_version, postprocessing=True)
            return sensible_format
        else:
            logger.error("Postprocessor not found for acord version, cannot process ACORD", acord_version=acord_version)
            return None
    except Exception as e:
        logger.exception("Failed to convert to sensible format", exc_info=e)
        return None


def _format_processor_results(results: dict, acord_version: str, page_metadata: list[PageMetadata]) -> dict:
    return {
        "documents": [
            {
                "documentType": "acord_forms",
                "configuration": acord_version,
                "output": {
                    "parsedDocument": results,
                    "text": {
                        "pages": [
                            {
                                "width": page.width,
                                "height": page.height,
                            }
                            for page in page_metadata
                        ]
                    },
                },
            }
        ]
    }


def _replace_none_value_dicts(data):
    if isinstance(data, dict):
        if set(data.keys()) == {"value"} and data["value"] is None:
            return None
        return {k: _replace_none_value_dicts(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [_replace_none_value_dicts(item) for item in data]
    else:
        return data


def reducto_process_acord(file_info: FileInfo, copilot_v3_client: CopilotV3Client, logger: BoundLogger) -> bool:
    try:
        reducto_client = ReductoClient(reducto_api_key=os.environ["REDUCTO_API_KEY"])
        acord_139_schema = REDUCTO_SCHEMAS_AND_SYSTEM_PROMPTS[file_info.classification]["ACORD_139_SCHEMA"]
        acord_139_system_prompt = REDUCTO_SCHEMAS_AND_SYSTEM_PROMPTS[file_info.classification][
            "ACORD_139_SYSTEM_PROMPT"
        ]
        result = reducto_client.extract_data(
            document_url=file_info.s3_url,
            schema=acord_139_schema,
            system_prompt=acord_139_system_prompt,
            advanced_options=AdvancedProcessingOptions(ocr_system="highres"),
            experimental_options={
                "enable_checkboxes": True,
            },
            generate_citations=True,
            options=BaseProcessingOptions(ocr_mode="agentic"),
        )

        parsing_result = _replace_none_value_dicts(result.result[0])

        if extraction_score := copilot_v3_client.score_acord_extraction(parsing_result, file_info.file_version.lower()):
            if extraction_score < EXTRACTION_SCORE_THRESHOLD:
                logger.warning("Reducto extraction score is below threshold", extraction_score=extraction_score)
            else:
                logger.info("Reducto extraction score is above threshold", extraction_score=extraction_score)
            page_metadata = [PageMetadata(width=1, height=1) for _ in range(result.usage.num_pages)]
            copilot_v3_client.ingest_acord_form(
                _format_processor_results(parsing_result, file_info.file_version.lower(), page_metadata=page_metadata),
                file_info.file_id,
            )
            return True
        else:
            logger.warning("Failed to get extraction score, falling back to sensible")
            return False
    except Exception as e:
        logger.exception("Failed to parse ACORD", exc_info=e)
        return False
