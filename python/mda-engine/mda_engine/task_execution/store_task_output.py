from typing import Any

from common.clients.client_factory import get_cache_client, get_copilot_v3_client
from common.utils.logging import log_function_inputs
from copilot_client_v3 import Task
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from sentry_sdk.integrations.serverless import serverless_function

from mda_engine.models.errors import StoreTaskOutputException
from mda_engine.task_execution.task_execution_state import (
    OUTPUT_KEY,
    TaskExecutionState,
)

logger = get_logger()


# noinspection PyUnusedLocal
@serverless_function
@log_function_inputs
@bind_lambda_logging_context
def store_task_output(event: dict, context: Any = None) -> Any:
    copilot_client = get_copilot_v3_client()

    state = TaskExecutionState.schema().load(event)  # type: ignore

    try:
        if state.error:
            if state.task_id:
                copilot_client.update_task(
                    state.task_id,
                    Task(
                        status="FAILED",
                        is_valid_output=False,
                    ),
                )
            return {"error": state.error}
        if state.has_large_state and state.output:
            output_key = state.output[OUTPUT_KEY]
            state.output = get_cache_client().get_from_cache(output_key, cls=None, configuration=None)
        updated_task = copilot_client.update_task(
            state.task_id,
            Task(
                status="COMPLETED",
                processed_output=state.output,
                is_valid_output=state.is_valid_output,
                output_task_execution_id=state.output_task_execution_id,
            ),
        )
    except Exception as e:
        raise StoreTaskOutputException(
            "Failed to update task output", task_id=state.task_id, task_output=state.output
        ) from e
    return updated_task.processed_output if updated_task else None
