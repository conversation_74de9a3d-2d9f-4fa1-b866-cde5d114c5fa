from __future__ import annotations

import dataclasses
from collections import defaultdict
from dataclasses import dataclass

from common.clients.client_factory import get_cache_client
from dataclasses_json import Undefined, dataclass_json
from static_common.enums.enum import StrEnum
from static_common.models.task_execution_output_evaluation import (
    TaskExecutionOutputEvaluation,
)

from mda_engine.task_execution.generic_models import (
    ConsolidationInput,
    ConsolidationInputModel,
    SelfReflectionInput,
    ValidationInput,
)

MAX_ORDER = 9999
TASK_IO_EXPIRE_TIME = 60 * 15
INPUT_KEY = "input_key"
REFERENCED_INPUT_KEY = "referenced_input_key"
OUTPUT_KEY = "output_key"
DEFAULT_TIMEOUT = 300


class ModelType(StrEnum):
    lambda_model = "lambda_model"
    multi_lambda_models = "multi_lambda_models"
    step_function_model = "step_function_model"
    multi_step_function_models = "multi_step_function_models"


@dataclass_json(undefined=Undefined.EXCLUDE)
@dataclass
class TaskExecutionOutput:
    output: dict | None
    is_valid_output: bool
    validation_explanation: str | None
    processing_time: int
    is_error: bool = False
    error_message: str | None = None
    can_self_reflect: bool | None = None
    used_input_tokens: int = 0
    used_output_tokens: int = 0

    @staticmethod
    def create_from_error(error_message: str) -> TaskExecutionOutput:
        return TaskExecutionOutput(
            output=None,
            is_valid_output=False,
            validation_explanation=None,
            processing_time=0,
            is_error=True,
            error_message=error_message,
            can_self_reflect=None,
        )

    def get_output_evaluation(self) -> TaskExecutionOutputEvaluation:
        return TaskExecutionOutputEvaluation(
            is_valid_output=self.is_valid_output,
            validation_explanation=self.validation_explanation,
            is_error=self.is_error,
            error_message=self.error_message,
            can_self_reflect=self.can_self_reflect if self.can_self_reflect is not None else False,
        )


@dataclass_json(undefined=Undefined.EXCLUDE)
@dataclass
class TaskHandlerState:
    handler_class: str | None
    model_id: str
    input: dict
    task_id: str
    organization_id: int | None = None
    submission_id: str | None = None
    config: dict | None = None
    file_id: str | None = None
    output: dict | None = None
    is_self_reflection_run: bool | None = None
    is_benchmark_run: bool | None = None
    is_validation_run: bool | None = None
    has_large_state: bool | None = False

    @property
    def run_type(self) -> str:
        return (
            "self_reflection_validation"
            if self.is_self_reflection_run and self.is_validation_run
            else "self_reflection"
            if self.is_self_reflection_run
            else "validation"
            if self.is_validation_run
            else "regular"
        )

    def get_redis_input_key(self, referenced_id: str | None) -> str:
        return f"mda_{self.task_id}_{self.model_id}_{self.run_type}_{referenced_id if referenced_id else ''}_input"

    def get_redis_output_key(self, input_key: str) -> str:
        if "input" in input_key:
            return input_key.replace("input", "output")
        return f"mda_{self.task_id}_{self.model_id}_{self.run_type}_output"

    @staticmethod
    def create(
        task_execution_state: TaskExecutionState, model: ModelState, input: dict, referenced_id: str | None = None
    ) -> TaskHandlerState:
        if not model.id:
            raise ValueError("Model ID is required to create TaskHandlerState")
        task_handler_state = TaskHandlerState(
            handler_class=model.handler_class,
            model_id=model.id,
            input=input,
            config=model.config,
            task_id=task_execution_state.task_id,  # type: ignore
            organization_id=task_execution_state.organization_id,
            submission_id=task_execution_state.submission_id,
            file_id=task_execution_state.file_id,
            is_self_reflection_run=model.is_self_reflection_run,
            is_benchmark_run=model.is_benchmark_run,
            is_validation_run=model.is_validation_run,
            has_large_state=task_execution_state.has_large_state,
        )
        if task_handler_state.has_large_state:
            referenced_input_key = task_handler_state.get_redis_input_key(referenced_id)
            if all(key in (INPUT_KEY, REFERENCED_INPUT_KEY) for key in input):
                task_handler_state.input = input | {REFERENCED_INPUT_KEY: referenced_input_key}
            else:
                get_cache_client().add_to_cache(referenced_input_key, input, TASK_IO_EXPIRE_TIME)
                task_handler_state.input = {INPUT_KEY: referenced_input_key}
        return task_handler_state


@dataclass_json(undefined=Undefined.EXCLUDE)
@dataclass
class ModelState:
    type: ModelType
    order: int | None = None
    id: str | None = None
    execution_arn: str | None = None
    handler_class: str | None = None
    is_always_run: bool | None = None
    is_validation_run: bool | None = None
    is_consolidation_run: bool | None = None
    is_preprocessing_input_run: bool | None = None
    is_self_reflection_run: bool | None = None
    is_benchmark_run: bool | None = None
    input: TaskHandlerState | None = None
    output: dict | None = None
    outputs: list[dict] | None = None
    is_valid: bool | None = None
    config: dict | None = None
    validation_model_id: str | None = None
    models: list[ModelState] | None = None
    task_execution_id: str | None = None
    validated_task_execution_id: str | None = None
    can_self_reflect: bool | None = None
    validation_explanation: str | None = None
    error_info: dict | None = None
    timeout: int = DEFAULT_TIMEOUT

    @property
    def is_applicable_for_consolidation(self) -> bool:
        return self.is_valid == True and not self.is_validation_run and not self.is_preprocessing_input_run


@dataclass_json(undefined=Undefined.EXCLUDE)
@dataclass
class TaskExecutionState:
    task_code: str | None = None
    task_definition_id: str | None = None
    organization_id: int | None = None
    submission_id: str | None = None
    business_id: str | None = None
    input: dict | None = None
    context: dict | None = None
    output: dict | None = None
    is_valid_output: bool | None = None
    task_id: str | None = None
    file_id: str | None = None
    all_models: list[ModelState] | None = None
    current_model: ModelState | None = None
    output_task_execution_id: str | None = None
    error: str | None = None
    task_description: str | None = None
    consolidation_model: ModelState | None = None
    validation_model: ModelState | None = None
    has_large_state: bool | None = None

    def get_next_model(self) -> ModelState | None:
        if not self.all_models or (self.current_model and self.current_model.is_consolidation_run):
            return None

        if validation_models := self._get_validation_models_for_current_model():
            return self._get_model_from_models_list(validation_models)

        if self_reflection_models := self._get_self_reflection_models_for_current_model():
            return self._get_model_from_models_list(self_reflection_models)

        if next_models := self._get_models_for_execution_after_current_model():
            return self._get_model_from_models_list(next_models)

        if self.consolidation_model:
            return self._get_consolidation_model()

        return None

    @property
    def all_execution_models_failed(self) -> bool:
        if not self.all_models:
            return False
        return all(
            model.error_info
            for model in self.all_models
            if model.is_validation_run is not True and model.is_consolidation_run is not True
        )

    def _get_validation_models_for_current_model(self) -> list[ModelState]:
        if self.current_model is None or self.current_model.is_validation_run:
            return []
        models_for_validation = self.current_model.models or [self.current_model]
        validation_models: list[ModelState] = []
        for model in models_for_validation:
            if (
                (not model.validation_model_id and not self.validation_model)
                or model.is_validation_run
                or not model.is_valid
            ):
                continue
            validation_model = (
                next(
                    m
                    for m in self.all_models  # type: ignore
                    if m.id == model.validation_model_id and m.order == model.order
                )
                if model.validation_model_id
                else self.validation_model
            )

            validation_input = ValidationInput(
                input=model.input.input, output=model.output, task_description=self.task_description  # type: ignore
            ).to_dict()

            validation_model_copy: ModelState = dataclasses.replace(
                validation_model,
                input=TaskHandlerState.create(
                    self, validation_model, validation_input, model.task_execution_id  # type: ignore
                ),
                validated_task_execution_id=model.task_execution_id,
                order=model.order,
                is_self_reflection_run=model.is_self_reflection_run,
                can_self_reflect=model.can_self_reflect,
                is_benchmark_run=model.is_benchmark_run,
            )
            validation_models.append(validation_model_copy)
        return validation_models

    def _get_self_reflection_models_for_current_model(self) -> list[ModelState]:
        if not self.current_model or self.current_model.is_consolidation_run:
            return []

        self_reflection_models = []
        models_for_self_reflection = self.current_model.models or [self.current_model]
        for model in models_for_self_reflection:
            if (
                not model.is_validation_run
                or model.is_valid
                or not model.can_self_reflect
                or model.is_self_reflection_run
            ):
                continue

            self_reflection_model = next(
                m for m in self.all_models if m.task_execution_id == model.validated_task_execution_id  # type: ignore
            )

            self_reflection_model.is_self_reflection_run = True
            if model.is_benchmark_run:
                self_reflection_model.is_benchmark_run = True

            self_reflection_input = SelfReflectionInput(
                input=self_reflection_model.input.input,  # type: ignore
                output=model.output,
                validation_explanation=model.validation_explanation,
                task_description=self.task_description,
            ).to_dict()

            self_reflection_model.input = TaskHandlerState.create(
                self, self_reflection_model, self_reflection_input, model.task_execution_id
            )
            self_reflection_models.append(self_reflection_model)

        return self_reflection_models

    def _get_models_for_execution_after_current_model(self) -> list[ModelState]:
        order_to_models = defaultdict(list)
        min_order = MAX_ORDER
        applicable_models = [m for m in self.all_models if self._is_model_applicable_for_execution(m)]  # type: ignore
        for model in applicable_models:
            min_order = min(min_order, model.order)  # type: ignore
            order_to_models[model.order].append(model)
        if min_order == MAX_ORDER:
            return []
        next_models = order_to_models[min_order]
        for model in next_models:
            assert self.input is not None
            model.input = TaskHandlerState.create(self, model, self.input)
        return next_models

    def _is_model_applicable_for_execution(self, model: ModelState) -> bool:
        current_order = self.current_model.order if self.current_model is not None else -1
        return (
            current_order is not None
            and model.order is not None
            and model.order > current_order
            and not model.is_validation_run
            and (model.is_always_run or not self.is_valid_output)
            and model.task_execution_id is None
        )

    @staticmethod
    def _get_model_from_models_list(models: list[ModelState]) -> ModelState | None:
        if len(models) == 1:
            return models[0]
        multi_model_type = (
            ModelType.multi_lambda_models
            if models[0].type == ModelType.lambda_model
            else ModelType.multi_step_function_models
        )
        return ModelState(type=multi_model_type, models=models, order=models[0].order)

    def _get_consolidation_model(self) -> ModelState | None:
        if not self.consolidation_model or not self.task_description:
            return None

        applicable_models = [m for m in self.all_models if m.is_applicable_for_consolidation]  # type: ignore
        if len(applicable_models) < 2:
            return None

        consolidation_input = ConsolidationInput(
            input=self.input,  # type: ignore
            models=[
                ConsolidationInputModel(
                    output=m.output,  # type: ignore
                    task_execution_id=m.task_execution_id,  # type: ignore
                    is_benchmark_model=m.is_benchmark_run or False,
                )
                for m in applicable_models
            ],
            task_description=self.task_description,
        )
        if self.has_large_state:
            cache_client = get_cache_client()
            input_key = consolidation_input.input[INPUT_KEY]
            consolidation_input.input = cache_client.get_from_cache(input_key, cls=None, configuration=None)
            for model in consolidation_input.models:
                output_key = model.output[OUTPUT_KEY]
                model.output = cache_client.get_from_cache(output_key, cls=None, configuration=None)

        consolidation_model = dataclasses.replace(
            self.consolidation_model,
            input=TaskHandlerState.create(
                self, self.consolidation_model, consolidation_input.to_dict()  # type: ignore
            ),
        )
        return consolidation_model
