from functools import partial
from typing import Any

from common.clients.client_factory import get_cache_client, get_copilot_v3_client
from common.clients.copilot_v3_client import CopilotV3Client
from common.utils.aws import (
    build_lambda_arn,
    build_step_function_arn,
    detect_current_aws_account_id,
    detect_current_aws_region,
)
from common.utils.logging import log_function_inputs
from copilot_client_v3 import Task, TaskDefinitionModel, TaskExecution, TaskModel
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from llm_common.models.llm_model import LLMModel
from sentry_sdk.integrations.serverless import serverless_function
from static_common.enums.task_cost_limit_exceeded_actions import (
    TaskCostLimitExceededActions,
)
from static_common.enums.task_model_execution_type import TaskModelExecutionType

from mda_engine.task_execution.task_cost_limit_handler import TaskCostLimitHandler
from mda_engine.task_execution.task_execution_state import (
    DEFAULT_TIMEOUT,
    INPUT_KEY,
    OUTPUT_KEY,
    TASK_IO_EXPIRE_TIME,
    ModelState,
    ModelType,
    TaskExecutionState,
)

logger = get_logger()

EXECUTION_ARN_KEY = "execution_arn"
HANDLER_CLASS_KEY = "handler_class"
GENERIC_LLM_PROMPT_HANDLER_KEYS = ["full_prompt_template", "system_prompt"]


# noinspection PyUnusedLocal
@serverless_function
@log_function_inputs
@bind_lambda_logging_context
def init_task_execution(event: dict, context: Any = None) -> Any:
    copilot_client: CopilotV3Client = get_copilot_v3_client()

    state = TaskExecutionState.schema().load(event)  # type: ignore
    task_dataset_input_id = state.context.get("task_dataset_input_id") if state.context else None

    if state.task_definition_id:
        task_definition = copilot_client.get_task_definition_by_id(
            state.task_definition_id, state.organization_id, task_dataset_input_id
        )
    else:
        task_definition = copilot_client.get_task_definition(
            state.task_code, state.organization_id, task_dataset_input_id
        )
    is_test_run = task_dataset_input_id is not None
    cost_limit_handler = TaskCostLimitHandler(state.organization_id, task_definition.group, is_test_run)
    cost_limit_exceeded_action = cost_limit_handler.check_task_cost_limits()
    if cost_limit_exceeded_action == TaskCostLimitExceededActions.BLOCK_EXECUTION:
        state.error = "Task cost limit exceeded"
        return TaskExecutionState.to_dict(state)  # type: ignore
    elif cost_limit_exceeded_action == TaskCostLimitExceededActions.USE_COST_FALLBACK:
        task_definition = copilot_client.get_task_definition(
            state.task_code, state.organization_id, use_cost_fallback=True
        )
        if not any(not (m.is_consolidation_run or m.is_validation_run) for m in task_definition.task_definition_models):
            logger.error(
                "USE_COST_FALLBACK configured as an action when exceeded cost limit, but no fallback models found",
                task_code=task_definition.code,
            )
            state.error = "Task cost limit exceeded and no fallback models found"
            return TaskExecutionState.to_dict(state)  # type: ignore

    if state.has_large_state is None:
        state.has_large_state = task_definition.has_large_state
    if task := copilot_client.init_task(
        Task(
            task_definition_id=task_definition.id,
            submission_id=state.submission_id,
            file_id=state.file_id,
            business_id=state.business_id,
            organization_id=state.organization_id,
            context=state.context,
        )
    ):
        if state.has_large_state:
            input_key = f"mda_{task.id}_input"
            get_cache_client().add_to_cache(input_key, task.input, TASK_IO_EXPIRE_TIME)
            state.input = {INPUT_KEY: input_key}
        else:
            state.input = task.input
        state.task_id = task.id
        state.all_models = []
        state.task_description = task_definition.llm_task_description

        # Check if the input is invalid
        if not task.is_valid_input:
            state.is_valid_output = False
            return TaskExecutionState.to_dict(state)  # type: ignore

        for task_definition_model in task_definition.task_definition_models:
            if task_definition_model.task_dataset_model_outcome_id:
                state.all_models.append(_store_task_execution_and_get_model_state(state, task, task_definition_model))
                continue
            model_states = _get_model_states(task_definition_model)
            if task_definition_model.is_consolidation_run:
                state.consolidation_model = model_states[0]
                continue
            if task_definition_model.is_validation_run:
                state.validation_model = model_states[0]
                continue
            state.all_models.extend(model_states)

        state.current_model = state.get_next_model()
    else:
        state.error = "Failed to initialize task"

    return TaskExecutionState.to_dict(state)  # type: ignore


def _get_model_states(task_definition_model: TaskDefinitionModel) -> list[ModelState]:
    model: TaskModel = task_definition_model.task_model
    validation_model: TaskModel | None = task_definition_model.validation_task_model

    model_states = [
        _get_model_state_from_task_model(
            model,
            task_definition_model,
        )
    ]
    if validation_model:
        model_states[0].validation_model_id = validation_model.id
        model_states.append(
            _get_model_state_from_task_model(validation_model, task_definition_model, is_validation_run=True)
        )

    return model_states


def _store_task_execution_and_get_model_state(
    state: TaskExecutionState, task: Task, task_definition_model: TaskDefinitionModel
) -> ModelState:
    copilot_client: CopilotV3Client = get_copilot_v3_client()
    model = task_definition_model.task_model

    task_execution = TaskExecution(
        task_id=task.id,
        task_model_id=model.id,
        input=task.input,
        is_benchmark_run=False,
        is_self_reflection_run=False,
        is_validation_run=False,
        is_consolidation_run=False,
        task_dataset_model_outcome_id=task_definition_model.task_dataset_model_outcome_id,
    )

    task_execution = copilot_client.store_task_execution(task.id, task_execution)
    output_for_state = task_execution.processed_output

    if state.has_large_state:
        output_key = f"mda_{task.id}_{model.id}_output"
        get_cache_client().add_to_cache(output_key, output_for_state, TASK_IO_EXPIRE_TIME)
        output_for_state = {OUTPUT_KEY: output_key}

    model_state = ModelState(
        id=model.id,
        type=(
            ModelType.lambda_model
            if model.execution_type == TaskModelExecutionType.LAMBDA
            else ModelType.step_function_model
        ),
        order=task_definition_model.order,
        is_preprocessing_input_run=task_definition_model.is_preprocessing_input_run,
        is_always_run=task_definition_model.is_always_run,
        is_validation_run=task_definition_model.is_validation_run,
        is_consolidation_run=task_definition_model.is_consolidation_run,
        can_self_reflect=task_definition_model.can_self_reflect,
        is_benchmark_run=task_definition_model.is_benchmark_run,
        task_execution_id=task_execution.id,
        output=output_for_state,
        is_valid=task_execution.output_evaluation.get("is_valid_output", False)
        if task_execution.output_evaluation
        else False,
    )

    return model_state


def _get_model_state_from_task_model(
    model: TaskModel,
    task_definition_model: TaskDefinitionModel,
    is_validation_run: bool = False,
) -> ModelState:
    lambda_arn = partial(build_lambda_arn, detect_current_aws_region(), detect_current_aws_account_id())
    sf_arn = partial(build_step_function_arn, detect_current_aws_region(), detect_current_aws_account_id())

    config = model.execution_config or {}
    if not any(key in config for key in [EXECUTION_ARN_KEY, HANDLER_CLASS_KEY]) and any(
        key in config for key in GENERIC_LLM_PROMPT_HANDLER_KEYS
    ):
        config[HANDLER_CLASS_KEY] = "GenericLLMPromptHandler"

    handler_class = None
    if model.execution_type == TaskModelExecutionType.LAMBDA and HANDLER_CLASS_KEY in config:
        handler_class = config[HANDLER_CLASS_KEY]
        execution_arn = (
            lambda_arn("mda-engine-execute-task-model-xl")
            if config.get("size") == "xl"
            else lambda_arn("mda-engine-execute-task-model")
        )
    else:
        config_execution_arn = config[EXECUTION_ARN_KEY]
        execution_arn = (
            lambda_arn(config_execution_arn)
            if model.execution_type == TaskModelExecutionType.LAMBDA
            else sf_arn(config_execution_arn)
        )

    model_timeout = model.timeout or DEFAULT_TIMEOUT
    if model.llm_model is not None:
        config["llm_model"] = LLMModel(model.llm_model)

    model_state = ModelState(
        id=model.id,
        type=(
            ModelType.lambda_model
            if model.execution_type == TaskModelExecutionType.LAMBDA
            else ModelType.step_function_model
        ),
        order=task_definition_model.order,
        execution_arn=execution_arn,
        handler_class=handler_class,
        config=config,
        is_preprocessing_input_run=task_definition_model.is_preprocessing_input_run,
        is_always_run=task_definition_model.is_always_run,
        is_validation_run=task_definition_model.is_validation_run or is_validation_run,
        is_consolidation_run=task_definition_model.is_consolidation_run,
        can_self_reflect=task_definition_model.can_self_reflect,
        is_benchmark_run=task_definition_model.is_benchmark_run,
        timeout=model_timeout,
    )

    return model_state
