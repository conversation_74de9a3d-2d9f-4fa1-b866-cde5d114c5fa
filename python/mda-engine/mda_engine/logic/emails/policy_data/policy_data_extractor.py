import json
from copy import deepcopy

from common.utils.date import parse_effective_date
from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import (
    ClaudeRequestParams,
    GPTRequestParams,
    LLMRequestParams,
)
from marshmallow import ValidationError
from static_common.enums.entity import EntityInformation
from static_common.enums.fields import FieldType
from static_common.enums.organization import ExistingOrganizations
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
)
from static_common.models.openai import ChatCompletionPrompt
from static_common.schemas.openai import ChatCompletionPromptSchema

from mda_engine.logic.emails.entities.validators.entity_data_validator import (
    EntityDataValidator,
)
from mda_engine.logic.emails.extractor import EmailDatapointExtractor
from mda_engine.models.emails import PolicyData
from mda_engine.schemas.emails import PolicyDataSchema

ORGANIZATIONS_TO_POPULATE_RENEWAL_FROM_EMAIL = {
    ExistingOrganizations.MerchantsGroup.value,
    ExistingOrganizations.BishopConifer.value,
}


class PolicyDataExtractor(EmailDatapointExtractor):
    def extract(self) -> PolicyData | None:
        prompt = self.get_policy_information_extraction_prompt()

        try:
            with self._llm_client as llm_client:
                response = llm_client.get_llm_response(
                    self.llm_params, prompt, call_origin="email_processing::policy_data_extraction"
                )
                if not response:
                    self._log.warning("Failed to extract policy data from email")
                    return None
                result: PolicyData = PolicyDataSchema().load(response)

                self._log.info("Extracted policy data", policy_data=result)

                self._maybe_adjust_effective_date(result)

                return result
        except ValidationError:
            self._log.exception("Failed to parse policy data response")
            return None
        except Exception:
            self._log.exception("Failed to extract policy data from email")
            return None

    def add_policy_data(self, processed_data: OnboardedFile, policy_data: PolicyData | None) -> OnboardedFile:
        if not policy_data:
            return processed_data

        new_processed_data = deepcopy(processed_data)

        try:
            submission_entity_idx = new_processed_data.get_or_create_submission_entity_idx(
                self._email_context.submission_id
            )
            fields = self._create_fields(policy_data, submission_entity_idx)

            for field in fields:
                new_processed_data.add_or_create_entity_field(field)

            return new_processed_data
        except Exception as e:
            self._log.exception("Failed to add policy data to processed data", exc_info=e)
            return processed_data

    def _create_fields(self, policy_data: PolicyData, submission_entity_idx: int) -> list[ResolvedDataField]:
        fields = []
        if policy_data.effective_date:
            fields.append(
                ResolvedDataField(
                    name=EntityInformation.POLICY_EFFECTIVE_START_DATE,
                    values=[ResolvedDataValue(str(policy_data.effective_date), entity_idx=submission_entity_idx)],
                    value_type=FieldType.DATETIME,
                )
            )

        if self._email_context.organization_id in ORGANIZATIONS_TO_POPULATE_RENEWAL_FROM_EMAIL:
            fields.append(
                ResolvedDataField(
                    name=EntityInformation.IS_RENEWAL,
                    value_type=FieldType.BOOLEAN,
                    values=[ResolvedDataValue(value=policy_data.is_renewal, entity_idx=submission_entity_idx)],
                )
            )

        return fields

    def get_policy_information_extraction_prompt(self) -> ChatCompletionPrompt:
        gpt_prompt = f"""
        You are reading information from a text file that is saved from an email thread.
        The email thread are messages from brokers that are sent to an underwriting office
        requesting insurance coverage for an applicant.
        Your task is to extract the following information from the email:
        1. Effective date of the requested policy - as a date in format YYYY-MM-DD.
           If date is incomplete, eg. missing day choose the first day of the month.
        2. Whether the policy is a renewal - as a boolean, if not specified assume it is not a renewal
        The effective date might be specified such as:
        - Effective: [Date Start]-[Date End]
        - Eff date: [Date Start]-[Date End]
        - Term: [Date Start] - [Date End]
        - In subject any date [Date Start]
        If no year is given for the effective date, infer it from the year the email was sent.
        If the inferred date falls after the email sent date, consider it from the next year.
        Start by examining the subject of the email.
        If the effective date is specified as 'ASAP' - leave the field empty.
        Include a brief explanation with evidence of the extracted information.
        Return the extracted information as a JSON in following format:
        {PolicyData.format()}
        Make sure explanation does not contain double quotes so the result is valid JSON.
        """

        prompt_sequence = {
            "messages": [
                {
                    "role": "system",
                    "content": gpt_prompt,
                },
                {"role": "user", "content": json.dumps(self._email_context.email_text)},
            ]
        }
        return ChatCompletionPromptSchema().load(prompt_sequence)  # type:ignore[no-any-return]

    def _get_default_validators(self) -> list[EntityDataValidator]:
        return []

    def _maybe_adjust_effective_date(self, policy_data: PolicyData) -> None:
        if not self._email_context.submission_created_at or not policy_data.effective_date:
            return

        if adjusted_date := parse_effective_date(
            str(policy_data.effective_date),
            self._email_context.submission_created_at,
            self._email_context.is_pre_renewal,
            organization_id=self._email_context.organization_id,
        ):
            policy_data.effective_date = adjusted_date.date()
        else:
            self._log.warning("Failed to adjust effective date", effecive_date=str(policy_data.effective_date))

    @property
    def llm_params(self) -> list[LLMRequestParams]:
        return [
            GPTRequestParams(
                model=LLMModel.OPENAI_GPT_4O,
                max_tokens=1500,
                temperature=0.0,
                return_json=True,
                raise_exceptions=True,
            ),
            GPTRequestParams(
                model=LLMModel.AZURE_GPT_4O,
                max_tokens=1500,
                temperature=0.0,
                return_json=True,
                raise_exceptions=True,
            ),
            ClaudeRequestParams(
                model=LLMModel.CLAUDE_3_5_SONNET,
                max_tokens=1500,
                temperature=0.0,
                return_json=True,
                raise_exceptions=True,
            ),
        ]
