from copy import copy, deepcopy
from dataclasses import dataclass
from typing import Iterable, Optional, Type

from common.clients.client_factory import get_copilot_v3_client
from common.clients.copilot_v3_client import CopilotV3Client
from common.logic.emails import EmailProcessingContext, ThreadMessageData
from copilot_client_v3 import Brokerage, BrokerageEmployee
from infrastructure_common.logging import get_logger
from llm_common.clients import LLMClient
from llm_common.clients.llm_client import Abstract<PERSON><PERSON>lient
from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import (
    ClaudeRequestParams,
    GPTRequestParams,
    LLMRequestParams,
)
from more_itertools import flatten
from pydantic import BaseModel
from static_common.enums.brokerage_employee_roles import BrokerageEmployeeRoles
from static_common.enums.entity import EntityFieldID, EntityInformation
from static_common.enums.fields import FieldType
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
)
from static_common.models.openai import Chat<PERSON>ompletion<PERSON>rompt
from static_common.models.types import StrUUID
from static_common.schemas.openai import ChatCompletionPromptSchema
from static_common.utils.brokers import (
    ALLOWED_TOP_LEVEL_DOMAINS,
    KALEPA_DOMAINS,
    InvalidAgencyDomainException,
    get_broker_domain_from_email,
)

from mda_engine.logic.emails.brokers.utils import create_possible_broker_names
from mda_engine.logic.emails.underwriters.models.resolving_context import (
    UWResolvingContext,
)
from mda_engine.logic.emails.underwriters.rule_builders.default_rule_builder import (
    DefaultRuleBuilder,
)
from mda_engine.logic.emails.underwriters.uw_resolver import UWResolver
from mda_engine.logic.emails.utils import (
    contains_comparison,
    get_org_email_domains,
    recalculate_entities_ids,
)
from mda_engine.models.emails import BrokerExtractionResult


@dataclass
class BrokerResolutionData:
    brokerage_name: str | None
    broker_name: str | None
    broker_email: str | None
    cc_name: str | None
    cc_email: str | None


class LLMBrokerResponse(BaseModel):
    broker_name: str | None
    broker_email: str | None
    brokerage_name: str | None
    explanation: str


class BrokerExtractor:
    def __init__(
        self,
        context: EmailProcessingContext,
        copilot_client: CopilotV3Client | None = None,
        llm_client: AbstractLLMClient | None = None,
        uw_resolver: UWResolver | None = None,
        can_modify_data: bool = True,
    ):
        self.log = get_logger()
        self.log = self.log.bind(
            submission_id=context.submission_id,
            organization_id=context.organization_id,
            file_id=context.file_id,
            extractor_name=self.__class__.__name__,
        )

        self._context: EmailProcessingContext = context
        self._copilot_client = copilot_client or get_copilot_v3_client()
        self._llm_client = self._init_llm_client(llm_client)

        uw_resolving_context = UWResolvingContext.create(
            context.organization_id, context.submission_id, copilot_client=self._copilot_client
        )
        self._uw_resolver = uw_resolver or UWResolver(
            resolving_context=uw_resolving_context,
            copilot_client=self._copilot_client,
            rule_builder=DefaultRuleBuilder(),
        )

        self._brokerages = self._copilot_client.get_brokerages()
        self._brokers = self._copilot_client.get_brokers()
        self._correspondence_contacts = self._copilot_client.get_correspondence_contacts()

        self._client_domains = get_org_email_domains(self._context.organization_id, self._copilot_client).union(
            KALEPA_DOMAINS
        )

        if can_modify_data:
            self._get_brokerage_method = self._copilot_client.get_or_create_brokerage
            self._get_brokerage_employee_method = self._copilot_client.get_or_create_brokerage_employee
        else:
            self._get_brokerage_method = self._copilot_client.find_similar_brokerage
            self._get_brokerage_employee_method = self._copilot_client.find_similar_brokerage_employee

    def _init_llm_client(self, llm_client: LLMClient | None = None) -> LLMClient:
        llm_client = llm_client or LLMClient()
        llm_client.add_context_param("file_id", self._context.file_id)
        llm_client.add_context_param("submission_id", self._context.submission_id)
        return llm_client

    def extract_with_processed_data(self, processed_data: OnboardedFile) -> BrokerExtractionResult | None:
        entity_names = {
            v.value
            for v in flatten([ei.values for ei in processed_data.entity_information if ei.name == EntityFieldID.NAME])
        }
        return self.extract(entity_names)

    def extract(self, entity_names: set[str]) -> BrokerExtractionResult | None:
        try:
            broker_names = {name for name in entity_names if self.is_broker_name(name)}
            entity_names -= broker_names
            broker_extraction_resolution = self.match(entity_names)
            broker_extraction_resolution.broker_names = broker_names
            if broker_extraction_resolution.brokerage:
                if broker_on_behalf := self.get_broker_on_behalf_information(
                    self._context.email_text, broker_extraction_resolution.brokerage.id  # type: ignore
                ):
                    broker_extraction_resolution.broker = broker_on_behalf
            return broker_extraction_resolution
        except Exception as e:
            self.log.exception("Failed to extract brokers data", exc_info=e)
            return None

    @staticmethod
    def add_brokers_data(
        processed_data: OnboardedFile, brokers_data: BrokerExtractionResult | None, submission_id: str
    ) -> OnboardedFile:
        if not brokers_data:
            return processed_data

        new_processed_data = deepcopy(processed_data)
        log = get_logger().bind(submission_id=submission_id)
        try:
            submission_entity_idx = new_processed_data.get_or_create_submission_entity_idx(submission_id)

            if brokers_data.broker_names:
                entity_ids_to_recalculate = set()
                for ei in new_processed_data.entity_information:
                    if ei.name == EntityFieldID.NAME:
                        for value in copy(ei.values):
                            if value.value in brokers_data.broker_names:
                                if value.entity_idx is not None:
                                    entity_ids_to_recalculate.add(value.entity_idx)
                                ei.values.remove(value)
                        if not ei.values:
                            new_processed_data.entity_information.remove(ei)
                recalculate_entities_ids(new_processed_data, entity_ids_to_recalculate)

            if brokers_data.brokerage:
                new_processed_data.entity_information.append(
                    ResolvedDataField(
                        name=EntityInformation.BROKERAGE_ID,
                        value_type=FieldType.TEXT,
                        values=[ResolvedDataValue(value=brokers_data.brokerage.id, entity_idx=submission_entity_idx)],
                    )
                )
            if brokers_data.broker:
                new_processed_data.entity_information.append(
                    ResolvedDataField(
                        name=EntityInformation.BROKER_ID,
                        value_type=FieldType.TEXT,
                        values=[ResolvedDataValue(value=brokers_data.broker.id, entity_idx=submission_entity_idx)],
                    )
                )
            if brokers_data.correspondence_contact:
                new_processed_data.entity_information.append(
                    ResolvedDataField(
                        name=EntityInformation.CORRESPONDENCE_CONTACT_ID,
                        value_type=FieldType.TEXT,
                        values=[
                            ResolvedDataValue(
                                value=brokers_data.correspondence_contact.id, entity_idx=submission_entity_idx
                            )
                        ],
                    )
                )
            return new_processed_data
        except Exception as e:
            log.exception("Failed to add brokers data to processed data", exc_info=e)
            return processed_data

    def match(self, entity_names: set[str]) -> BrokerExtractionResult:
        if resolution_data := self._get_broker_data_for_resolution(entity_names):
            return self._resolve_broker(resolution_data)
        else:
            return BrokerExtractionResult()

    def get_broker_on_behalf_information(
        self,
        text: str,
        brokerage_id: str | None,
    ) -> BrokerageEmployee | None:
        prompt = self._create_broker_name_on_behalf_detection_chat_completion_prompt(text)
        if gpt_response := self._llm_client.get_llm_response(
            self._llm_params(),
            prompt,
            call_origin="email_processing::broker_name_on_behalf_extraction",
        ):
            if gpt_response and isinstance(gpt_response, list):
                self.log.info("GPT response received for broker name on behalf prompt", gpt_response=gpt_response)
                gpt_response = gpt_response[0]
            if not isinstance(gpt_response, dict):
                self.log.warning("Wrong format returned from LLM for broker name on behalf resolution")
                return None
            broker_name = gpt_response.get("name")
            if not broker_name:
                self.log.info("No broker name (on behalf) extracted by GPT")
                return None

            return self.get_broker_by_name(broker_name, brokerage_id)  # type: ignore
        return None

    def _get_entities_and_broker_names(self, processed_data: OnboardedFile) -> tuple[set[str], set[str]]:
        entity_names = {
            v.value
            for v in flatten([ei.values for ei in processed_data.entity_information if ei.name == EntityFieldID.NAME])
        }
        broker_names = {name for name in entity_names if self.is_broker_name(name)}
        entity_names -= broker_names
        return entity_names, broker_names

    def _get_broker_data_for_resolution(self, entity_names: set[str]) -> BrokerResolutionData | None:
        for message in reversed(self._context.messages):
            is_message_from_broker, email_address, email_name = self._is_message_from_broker(message)
            if is_message_from_broker:
                self.log.info(
                    "Detected message from potential broker", from_email=email_address, from_email_name=email_name
                )

                is_message_to_client, to_email = self._is_message_to_client(message)
                if is_message_to_client:
                    self.log.info("Detected message to client inbox, using it to match broker", to_email=to_email)

                    broker_data = self._get_broker_data_from_message(message, email_address, email_name, entity_names)
                    return self._additional_checks(broker_data)

        self.log.warning("No email found from non-client domain to client domain, cannot resolve brokers")
        return None

    def _get_broker_data_from_message(
        self,
        message: ThreadMessageData,
        email_address: str | None,
        email_name: str | None,
        entity_names: set[str],
    ) -> BrokerResolutionData | None:
        broker_data = LLMBrokerResponse(
            broker_name=None, broker_email=None, brokerage_name=None, explanation="Failed to run LLM"
        )
        cc_email = email_address or None
        cc_name = email_name or None

        if message.body:
            if gpt_response := self._llm_client.get_llm_response(
                self._llm_params(output_model=LLMBrokerResponse),
                self._create_broker_detection_chat_completion_prompt(entity_names),
                call_origin="email_processing::broker_resolution",
            ):
                if isinstance(gpt_response, dict):
                    self.log.info("GPT response received for broker prompt", gpt_response=gpt_response)
                    broker_data = LLMBrokerResponse.parse_obj(gpt_response)
                elif isinstance(gpt_response, LLMBrokerResponse):
                    broker_data = gpt_response
                else:
                    broker_data.explanation = "Wrong format returned from LLM for broker resolution"
                    self.log.warning("Wrong format returned fro LLM for broker resolution", gpt_response=gpt_response)

        self.log.info("Explanation for broker resolution", explanation=broker_data.explanation)

        validated_email = self._validate_broker_email(broker_data.broker_email, cc_email)

        if validated_email and validated_email != broker_data.broker_email:
            self.log.warning(
                "Broker email was changed during validation",
                old_email=broker_data.broker_email,
                new_email=validated_email,
            )
            broker_data.brokerage_name = None
            broker_data.broker_name = cc_name
            broker_data.broker_email = validated_email

        broker_data.broker_name = self._validate_broker_name(broker_data.broker_name, entity_names, message)

        if not broker_data.broker_email and not broker_data.broker_name:
            self.log.warning("No valid broker email or name detected from GPT response, defaulting to email sender")
            broker_data.broker_email = self._validate_broker_email(cc_email, None)
            broker_data.broker_name = self._validate_broker_name(cc_name, entity_names, message)

        return BrokerResolutionData(
            broker_data.brokerage_name, broker_data.broker_name, broker_data.broker_email, cc_name, cc_email
        )

    def _additional_checks(self, resolution_data: BrokerResolutionData | None) -> BrokerResolutionData | None:
        return resolution_data

    def _is_message_from_broker(self, message: ThreadMessageData) -> tuple[bool, str | None, str | None]:
        non_client_from_email = (
            message.from_email_address
            and all(kd not in message.from_email_address for kd in KALEPA_DOMAINS)
            and all(cd not in message.from_email_address for cd in self._client_domains)
        )
        non_client_reply_to_email = (
            message.reply_to_email_address
            and all(kd not in message.reply_to_email_address for kd in KALEPA_DOMAINS)
            and all(cd not in message.reply_to_email_address for cd in self._client_domains)
        )

        if non_client_from_email and any(
            message.from_email_address.endswith(d) for d in ALLOWED_TOP_LEVEL_DOMAINS  # type: ignore
        ):
            return True, message.from_email_address, message.from_email_name
        elif non_client_reply_to_email and any(
            message.reply_to_email_address.endswith(d) for d in ALLOWED_TOP_LEVEL_DOMAINS  # type: ignore
        ):
            return True, message.reply_to_email_address, message.reply_to_email_name
        else:
            return False, None, None

    def _is_message_to_client(self, message: ThreadMessageData) -> tuple[bool, str | None]:
        recipient_addresses = message.to_email_addresses + message.cc_email_addresses

        for to_email in recipient_addresses:
            if any(cd in to_email for cd in self._client_domains):
                return True, to_email

        return False, None

    def _resolve_broker(self, resolution_data: BrokerResolutionData) -> BrokerExtractionResult:
        self.log = self.log.bind(
            brokerage_name=resolution_data.brokerage_name,
            broker_name=resolution_data.broker_name,
            broker_email=resolution_data.broker_email,
            cc_name=resolution_data.cc_name,
            cc_email=resolution_data.cc_email,
        )

        brokerage_domain = None
        result = BrokerExtractionResult()

        broker = None
        cc = None

        try:
            if resolution_data.broker_email:
                brokerage_domain = get_broker_domain_from_email(
                    resolution_data.broker_email, self._context.organization_id
                )
            elif resolution_data.cc_email:
                brokerage_domain = get_broker_domain_from_email(resolution_data.cc_email, self._context.organization_id)

            self.log = self.log.bind(brokerage_domain=brokerage_domain)
            self.log.info("Brokerage domain detected")
        except InvalidAgencyDomainException:
            self.log.warning("Email contains one of invalid domains")
            return result
        except Exception:
            self.log.exception("Exception occurred during processing broker information")

        brokerage = self._find_brokerage(resolution_data.brokerage_name, brokerage_domain)

        if brokerage and brokerage.id:
            self.log = self.log.bind(brokerage_id=brokerage.id, brokage_name=brokerage.name)

            broker = self._get_brokerage_employee_method(
                brokerage.id,
                resolution_data.broker_name,
                resolution_data.broker_email,
                [BrokerageEmployeeRoles.AGENT.value],
                self._context.organization_id,
            )

            if resolution_data.cc_email:
                cc = self._get_brokerage_employee_method(
                    brokerage.id,
                    resolution_data.cc_name,
                    resolution_data.cc_email,
                    [BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT.value],
                    self._context.organization_id,
                )

            elif broker and resolution_data.broker_email:
                if BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT.value not in broker.roles:
                    self.log.info(
                        "Detected broker doesn't have correspondence contact role and no other cc was detected"
                    )
                    # if no correspondence contact was found, add CORRESPONDENCE_CONTACT role to broker
                    cc = self._get_brokerage_employee_method(
                        broker.brokerage_id,
                        broker.name,
                        broker.email,
                        [BrokerageEmployeeRoles.AGENT.value, BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT.value],
                        self._context.organization_id,
                    )
                else:
                    cc = broker
        else:
            self._handle_missing_brokerage(resolution_data.brokerage_name, brokerage_domain)

        return BrokerExtractionResult(set(), broker, brokerage, cc)

    def _handle_missing_brokerage(self, brokerage_name: str | None, brokerage_domain: str | None) -> None:
        ...

    def _find_brokerage(self, brokerage_name: str | None, brokerage_domain: str | None) -> Brokerage | None:
        if brokerage_name or brokerage_domain:
            return self._get_brokerage_method(brokerage_name, brokerage_domain, self._context.organization_id)
        else:
            self.log.warning("Neither brokerage name nor domain was found, cannot resolve brokers")
            return None

    def _get_employee_in_brokerage(
        self, brokerage_id: StrUUID, broker_email: str | None, broker_name: str | None, roles: list[str]
    ) -> BrokerageEmployee | None:
        candidates = []

        if BrokerageEmployeeRoles.AGENT.value in roles:
            candidates.extend(self._brokers)
        if BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT.value in roles:
            candidates.extend(self._correspondence_contacts)

        for broker in candidates:
            if str(broker.brokerage_id) != str(brokerage_id):
                continue

            if broker_email and broker.email and broker_email.lower() == broker.email.lower():
                return broker

            if broker_name:
                potential_broker_names = create_possible_broker_names(broker)

                if any(n == broker_name.lower() for n in potential_broker_names):
                    return broker

        return None

    def is_broker_name(self, name: str) -> bool:
        all_names: set[str] = set()
        for b in self._brokers:
            all_names.update(create_possible_broker_names(b))

        return any(name.lower() == n.lower() for n in all_names)

    def get_broker_by_name(self, name: str, brokerage_id: str) -> BrokerageEmployee | None:
        broker = self._get_employee_in_brokerage(brokerage_id, None, name, [BrokerageEmployeeRoles.AGENT.value])
        if broker:
            return broker

        brokers = []
        for broker in self._brokers:
            new_name = " ".join([broker.name, *broker.aliases]).replace(", ", " ").replace(";", " ")
            if brokerage_id == broker.brokerage_id and contains_comparison(name, new_name):
                brokers.append(broker)

        if len(brokers) == 1:
            return brokers[0]
        elif len(brokers) > 1:
            self.log.warning(f"Found {len(brokers)} brokers with name {name} in brokerage {brokerage_id}")

        self.log.warning(f"Broker {name} not found in brokerage {brokerage_id}")
        return None

    def _validate_broker_name(
        self, broker_name: str | None, entity_names: Iterable[str], message: ThreadMessageData
    ) -> str | None:
        log = self.log.bind(broker_name=broker_name)

        if not broker_name:
            return None

        if self._uw_resolver.match_uws(broker_name, set(), [message]):
            log.warning("Detected broker name matches underwriter")
            return None

        if "@" in broker_name:
            log.warning("Detected broker name contains '@'")
            return None

        if len(broker_name.split(" ")) < 2:
            log.warning("Detected broker name contains less than 2 words")
            return None

        if any(en.lower() == broker_name.lower() for en in entity_names):
            log.warning("Detected broker name matches entity name")
            return None

        if self._context.copilot_email and broker_name.lower() in self._context.copilot_email.email_subject.lower():
            log.warning("Detected broker name found in email subject")
            return None

        return broker_name

    def _validate_broker_email(self, broker_email: str | None, cc_email: str | None) -> str | None:
        log = self.log.bind(broker_name=broker_email, cc_email=cc_email)

        if not broker_email:
            return None

        broker_email = broker_email.encode("utf-8").decode("utf-8")

        broker_email_split = broker_email.split("@")

        if len(broker_email_split) != 2:
            log.warning("Detected broker email does not contain '@'")
            return None

        broker_email_name, broker_email_domain = broker_email_split

        if cc_email:
            cc_email_split = cc_email.split("@")

            if len(cc_email_split) == 2:
                cc_email_name, cc_email_domain = cc_email_split

                if cc_email_domain.lower() != broker_email_domain.lower():
                    log.warning("Broker email domain does not match cc email domain")
                    return self._validate_broker_email(cc_email, None)

        broker_email = f"{broker_email_name}@{broker_email_domain}"

        if any(cd in broker_email for cd in self._client_domains):
            log.warning("Detected broker email is in client domain")
            return None

        return broker_email

    def _llm_params(self, output_model: Type[BaseModel] | None = None) -> list[LLMRequestParams]:
        return [
            GPTRequestParams(
                model=LLMModel.OPENAI_GPT_4O,
                raise_exceptions=True,
                return_json=True,
                output_model_json=output_model,
                max_tokens=1500,
                use_cache=True,
            ),
            ClaudeRequestParams(
                model=LLMModel.CLAUDE_3_7_SONNET,
                raise_exceptions=True,
                return_json=True,
                max_tokens=1500,
                use_cache=True,
            ),
            GPTRequestParams(
                model=LLMModel.AZURE_GPT_4O, raise_exceptions=True, return_json=True, max_tokens=1500, use_cache=True
            ),
        ]

    def _create_broker_detection_chat_completion_prompt(self, entity_names: Iterable[str]) -> ChatCompletionPrompt:
        uws = get_copilot_v3_client().get_users_by_organization(organization_id=self._context.organization_id)
        uw_names = [uw.name for uw in uws if uw.name]
        uw_domains = self._get_uw_domains_for_prompt()

        gpt_prompt = f"""
                    You are reading information from a text file that is saved from an email thread.
                    The email thread are messages from brokers that are sent to an underwriting office, requesting insurance coverage for an applicant.
                    Your task is to detect the broker responsible for the submission.
                    The broker might be explicitly assigned in the email content or can be inferred from phrases such as but not limited to:
                    - please clear for broker_name
                    - clear for broker_name
                    - submission for broker_name
                    The email might also be sent on behalf of another person - in this case this person is the broker.
                    If a person is indicated as an assistant then they are not the broker. Instead the other individual they are assistant to is.
                    broker_name must always contain both first and last name.
                    broker_name is never one of: {", ".join(uw_names)}
                    broker_name is never one of {", ".join(list(entity_names))}
                    broker_email is never in one of the following domains: {", ".join(uw_domains)}
                    If the broker's email is mentioned in the email, extract it as well, otherwise leave the field empty.
                    Also extract brokerage name.
                    Provide a brief explanation of your decision.
                    Return the data as JSON in format:
                    {{"broker_name": str, "brokerage_name": str, "broker_email": str, "explanation": str}}
                    Make sure explanation does not contain double quotes so the result is valid JSON.
                """

        prompt_sequence = {
            "messages": [
                {"role": "system", "content": gpt_prompt},
                {"role": "user", "content": self._context.email_text},
            ]
        }

        return ChatCompletionPromptSchema().load(prompt_sequence)  # type:ignore

    def _create_broker_name_on_behalf_detection_chat_completion_prompt(
        self, email_text: str
    ) -> Optional[ChatCompletionPrompt]:
        gpt_prompt = """
            You are reading information from a text file that is saved from an email thread.
            The email thread contains messages from brokers sent to an underwriting office, requesting clearance for a specific individual.
            Your task is to identify and extract the name of the individual for whom clearance is requested using patterns like:
            - Please clear for [Name]
            - Please Clear on Behalf of [Name]
            - Clear for [Name]
            - Please clear and log this for [Name]
            - PLEASE CLEAR FOR [Name]
            - Please clear this for [Name].
            - Inside Underwriter for [Name].
            - On behalf of [Name].
            - Please clear for [Somebody else] on behalf of [Name].

            Output the extracted name as JSON in the format: {"name": "<First Last>"}. If no name can be inferred, return an empty JSON.
            Do not generate a script, wait for text input, and output only the resulting JSON.
        """

        prompt_sequence = {
            "messages": [
                {"role": "system", "content": gpt_prompt},
                {"role": "user", "content": email_text},
            ]
        }

        return ChatCompletionPromptSchema().load(prompt_sequence)  # type: ignore

    def _get_uw_domains_for_prompt(self) -> set[str]:
        return get_org_email_domains(self._context.organization_id)
