from copy import deepcopy

from common.clients.client_factory import get_copilot_v3_client
from common.clients.copilot_v3_client import CopilotV3Client
from common.logic.emails import EmailProcessingContext
from llm_common.clients import <PERSON><PERSON>lient
from llm_common.clients.llm_client import <PERSON>bs<PERSON><PERSON><PERSON>lient
from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import (
    ClaudeRequestParams,
    GPTRequestParams,
    LLMRequestParams,
)
from marshmallow import ValidationError
from static_common.enums.coverage_names import CoverageName
from static_common.enums.entity import EntityInformation
from static_common.enums.fields import FieldType
from static_common.enums.submission import SubmissionCoverageType
from static_common.enums.units import Units
from static_common.models.coverages import (
    LIMIT_ATTACHMENT_POINT_SUPPORTED_COVERAGES,
    CoverageDetails,
)
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
)
from static_common.models.openai import ChatCompletionPrompt
from static_common.schemas.coverages import CoverageDetailsSchema
from static_common.schemas.openai import Chat<PERSON><PERSON>pletionPromptSchema
from structlog import BoundLogger, get_logger

from mda_engine.models.coverages import LLMCoverageResponse
from mda_engine.schemas.coverages import LLMCoverageResponseSchema


class CoverageExtractor:
    EXCLUDED_COVERAGES = [CoverageName.Other, CoverageName.Package]

    def __init__(
        self,
        email_context: EmailProcessingContext,
        llm_client: AbstractLLMClient | None = None,
        copilot_client: CopilotV3Client | None = None,
    ):
        self._email_context = email_context
        self._log = self._setup_logger()
        self._llm_client = self._init_llm_client(llm_client)
        self._copilot_client = copilot_client or get_copilot_v3_client()
        self._coverage_mappings = self._create_mappings()

    def extract(self) -> list[CoverageDetails] | None:
        try:
            coverage_names = list(self._coverage_mappings.keys())
            prompt = self._get_extraction_prompt(coverage_names)
            response = self._get_llm_response(prompt)

            self._log.info("LLM explanation for coverage extraction", explanation=response.explanation)

            if response.coverages:
                return self._postprocess_llm_response(response)
            else:
                return []

        except Exception:
            self._log.exception("Error occurred during email coverage extraction")
            return None

    def add_coverages_data(self, processed_data: OnboardedFile, coverages: list[CoverageDetails]) -> OnboardedFile:
        if not coverages:
            return processed_data

        new_processed_data = deepcopy(processed_data)
        try:
            [(c.coverage_name, c.coverage_type) for c in coverages]

            submission_entity_idx = new_processed_data.get_or_create_submission_entity_idx(
                self._email_context.submission_id
            )

            expiring_premium_total = 0
            target_premium_total = 0

            for coverage in coverages:
                if coverage.target_premium is not None:
                    target_premium_total += coverage.target_premium

                if coverage.expiring_premium is not None:
                    expiring_premium_total += coverage.expiring_premium

            if target_premium_total:
                new_processed_data.add_or_create_entity_field(
                    ResolvedDataField(
                        name=EntityInformation.TARGET_PREMIUM,
                        values=[ResolvedDataValue(target_premium_total, entity_idx=submission_entity_idx)],
                        value_type=FieldType.INTEGER,
                        unit=Units.USD,
                    )
                )

            if expiring_premium_total:
                new_processed_data.add_or_create_entity_field(
                    ResolvedDataField(
                        name=EntityInformation.EXPIRING_PREMIUM,
                        values=[ResolvedDataValue(expiring_premium_total, entity_idx=submission_entity_idx)],
                        value_type=FieldType.INTEGER,
                        unit=Units.USD,
                    )
                )

            value = ResolvedDataValue(
                value=CoverageDetailsSchema().dumps(coverages, many=True),
                entity_idx=submission_entity_idx,
            )
            coverage_details_field = ResolvedDataField(
                name=EntityInformation.COVERAGES_DETAILS,
                values=[value],
                value_type=FieldType.TEXT,
            )
            new_processed_data.entity_information.append(coverage_details_field)
            return new_processed_data
        except Exception:
            self._log.exception("Error occurred during adding coverages data to processed data")
            return processed_data

    def _get_llm_response(self, prompt: ChatCompletionPrompt) -> LLMCoverageResponse:
        response = self._llm_client.get_llm_response(
            self.llm_params, prompt, call_origin="email_processing::coverages_extraction"
        )

        if not response:
            self._log.warning("No response from LLM for email coverage extraction")
            return LLMCoverageResponse(coverages=[], explanation="No response for email coverage extraction")
        try:
            result: LLMCoverageResponse = LLMCoverageResponseSchema().load(response)
            return result
        except ValidationError:
            self._log.warning("Invalid response format from LLM for email coverage extraction", exc_info=True)
            return LLMCoverageResponse(
                coverages=[], explanation="Invalid response format from LLM for email coverage extraction"
            )

    def _setup_logger(self) -> BoundLogger:
        log = get_logger().bind(
            file_id=self._email_context.file_id,
            submission_id=self._email_context.submission_id,
            organization_id=self._email_context.organization_id,
        )
        return log  # type:ignore[no-any-return]

    def _postprocess_llm_response(self, llm_response: LLMCoverageResponse) -> list[CoverageDetails] | None:
        processed_coverages = []
        try:
            for coverage_response in llm_response.coverages:
                if mapping := self._coverage_mappings.get(coverage_response.coverage_name, []):
                    if coverage_response.coverage_name not in self.EXCLUDED_COVERAGES:
                        for coverage_name, coverage_type in mapping:
                            if (
                                coverage_response.excess_attachment_point
                            ) and coverage_name in LIMIT_ATTACHMENT_POINT_SUPPORTED_COVERAGES:
                                actual_coverage_type = SubmissionCoverageType.EXCESS
                            else:
                                actual_coverage_type = coverage_type

                            coverage_details = CoverageDetails(
                                coverage_name=coverage_name,
                                coverage_type=actual_coverage_type,
                                self_insurance_retention=coverage_response.self_insurance_retention,
                                limit=coverage_response.aggregate_limit,
                                excess_attachment_point=coverage_response.excess_attachment_point,
                                target_premium=coverage_response.target_premium,
                                expiring_premium=coverage_response.expiring_premium,
                                each_occurrence=coverage_response.each_occurrence_limit,
                            )
                            processed_coverages.append(coverage_details)
                else:
                    self._log.warning(
                        "Unknown coverage returned from LLM", coverage_name=coverage_response.coverage_name
                    )
                    return None
        except Exception:
            self._log.exception("Failed to process GPT coverages")
            return None

        return processed_coverages

    def _create_mappings(self) -> dict[str, list[tuple[CoverageName, SubmissionCoverageType]]]:
        existing_coverages = self._copilot_client.get_coverages(org_id=self._email_context.organization_id)

        coverage_mappings = {}
        for c in existing_coverages:
            if not (coverage_name := CoverageName.try_parse_str(c.name)):
                self._log.warning("Unknown coverage name", coverage_name=c.name)
                continue

            # we need to treat this as separate coverages for GL and XS
            if coverage_name == CoverageName.Liability:
                coverage_mappings["General Liability"] = [(coverage_name, SubmissionCoverageType.PRIMARY)]
                coverage_mappings["Excess Liability"] = [(coverage_name, SubmissionCoverageType.EXCESS)]
            elif coverage_name in self.EXCLUDED_COVERAGES:
                continue
            else:
                # if there is no type or there is exactly one type we can use that, otherwise we default to PRIMARY
                if not c.coverage_types:
                    coverage_type = None
                elif len(c.coverage_types) == 1:
                    coverage_type = SubmissionCoverageType.try_parse_str(c.coverage_types[0])
                else:
                    coverage_type = SubmissionCoverageType.PRIMARY

                coverage_mappings[c.display_name] = [(coverage_name, coverage_type)]  # type:ignore[list-item]

        return coverage_mappings  # type:ignore[return-value]

    def _init_llm_client(self, llm_client: AbstractLLMClient | None = None) -> AbstractLLMClient:
        llm_client = llm_client or LLMClient()
        llm_client.add_context_param("file_id", self._email_context.file_id)
        llm_client.add_context_param("submission_id", self._email_context.submission_id)
        return llm_client

    def _get_extraction_prompt(self, coverage_names: list[str]) -> ChatCompletionPrompt:
        gpt_prompt = f"""
                You are reading information from a text file that is saved from an email thread.
                The email thread are messages from brokers that are sent to an underwriting office, requesting insurance coverage for an applicant.
                Your task is to detect what type of coverage the applicant requests out of list of provided coverages:
                {coverage_names}
                In addition for each coverage found in the email, extract the following information:
                - self_insurance_retention - also known as SIR, the amount of money that the insured must pay before the insurance company starts to pay for the loss
                - aggregate limit - is the amount paid for coverage beyond the basic liability limits in an insurance contract.
                - excess_attachment_point - the amount of money that the insured must pay before the insurance company starts to pay for the loss
                - target_premium - the premium amount that the applicant is willing to pay for the coverage
                - expiring_premium - the premium amount that the applicant is currently paying for the coverage
                The above values should be individual for each coverage and can be empty if not present in the email.
                If a value is not defined for a specific coverage, e.g. expiring premium is defined for the whole submission - assign it to just one coverage.
                Excess_attachment_point should only be extracted for excess coverages. If the coverage is primary, leave the field empty.
                Limit and attachment point may be represented or referred to as an excess tower. Examples may look like but are not limited to:
                - Excess Tower: $1M/$2M - $1M limit, $2M excess attachment point
                - 1x2 - $1M limit, $2M excess attachment point
                - 15xP - $15M limit, $1M excess attachment point
                - 20x30xP - $20M limit, $30M excess attachment point
                If any of the information is not present in the email, leave the field empty.
                In case when no coverages can be inferred, return an empty list.
                Provide a brief explanation of your decision.
                Return the results as a JSON in following format: {LLMCoverageResponse.format()}
                Make sure explanation does not contain double quotes so the result is valid JSON.
            """  # noqa: E501

        prompt_sequence = {
            "messages": [
                {"role": "system", "content": gpt_prompt},
                {"role": "user", "content": self._email_context.email_text},
            ]
        }

        return ChatCompletionPromptSchema().load(prompt_sequence)  # type:ignore[no-any-return]

    @property
    def llm_params(self) -> list[LLMRequestParams]:
        return [
            ClaudeRequestParams(
                model=LLMModel.CLAUDE_3_7_SONNET,
                raise_exceptions=True,
                return_json=True,
                max_tokens=1500,
                use_cache=True,
            ),
            GPTRequestParams(
                model=LLMModel.OPENAI_GPT_4O,
                max_tokens=1500,
                temperature=0.0,
                return_json=True,
                raise_exceptions=True,
            ),
            GPTRequestParams(
                model=LLMModel.AZURE_GPT_4O,
                max_tokens=1500,
                temperature=0.0,
                return_json=True,
                raise_exceptions=True,
            ),
        ]
