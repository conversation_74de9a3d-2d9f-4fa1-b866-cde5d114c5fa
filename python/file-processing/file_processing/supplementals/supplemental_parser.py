import itertools
import os
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Any
from uuid import UUID

from common.clients.copilot import CopilotV3Client
from common.clients.feature_flags import FeatureFlagsClient
from common.utils.value_cleanup import clean_non_negative_numerical
from datascience_common.classifiers.fact_identification.supplementals.field_to_subtype_mapping import (
    FIELD_TO_SUBTYPE_MAPPING,
)
from datascience_common.classifiers.fact_identification.supplementals.supp_fields import (
    Supp<PERSON>ield,
)
from facts_client.model.fact_subtype import FactSubtype
from infrastructure_common.logging import get_logger
from marshmallow import ValidationError
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.coverage_names import CoverageName
from static_common.enums.entity import EntityInformation
from static_common.enums.fields import FieldType
from static_common.enums.file_metadata_type import FileMetadataType
from static_common.enums.submission_business import SubmissionBusinessEntityRole
from static_common.models.coverages import CoverageDetails
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
)
from static_common.schemas.coverages import CoverageDetailsSchema
from werkzeug.datastructures import FileStorage

from file_processing.key_value_parser import KeyValuePair, contains
from file_processing.models.feature_flags import FeatureFlags
from file_processing.pdf_documents.model.pdf_document import PdfDocument
from file_processing.supplemental_parsing import OnboardedFileLoader, parse_supplemental
from file_processing.supplementals.generic_supplemental_parser_v2 import (
    GenericSupplementalParserV2,
)
from file_processing.supplementals.generic_supplemental_parser_v3 import (
    GenericSupplementalParserV3,
)
from file_processing.supplementals.models import SupplementalParsingResult
from file_processing.supplementals.utils import is_na

MAX_WORKERS = 8


class SupplementalParser:
    def __init__(
        self,
        copilot_client: CopilotV3Client,
        fact_subtypes: list[FactSubtype],
        ff_client: FeatureFlagsClient | None = None,
    ) -> None:
        self._copilot_client = copilot_client
        self._fact_subtypes = fact_subtypes

        self.logger = get_logger()
        self._ff_client = ff_client or FeatureFlagsClient(
            os.getenv("LAUNCH_DARKLY_API_KEY"), default_context="FILE_PROCESSING"
        )
        self._use_generic_name_templates = self._ff_client.is_feature_enabled(
            FeatureFlags.ENABLE_GENERIC_NAME_TEMPLATES
        )
        self._onboarded_data_loader = OnboardedFileLoader(ff_client=self._ff_client)
        if self._ff_client.is_feature_enabled(FeatureFlags.ENABLE_GENERIC_PARSER_V3):
            self.logger.info("Using Generic Supplemental Parser V3")
            self._generic_supplemental_parser = GenericSupplementalParserV3(ff_client=self._ff_client)
        else:
            self.logger.info("Using Generic Supplemental Parser V2")
            self._generic_supplemental_parser = GenericSupplementalParserV2(ff_client=self._ff_client)

    @property
    def generic_supplemental_parser(self) -> GenericSupplementalParserV3 | GenericSupplementalParserV2:
        return self._generic_supplemental_parser

    def get_layout_result(self, file_id: UUID) -> dict:
        return self._copilot_client.get_file_extracted_metadata_content(
            file_id=str(file_id), metadata_type=FileMetadataType.AZURE_FR_PREBUILT_GENERAL_DOCUMENT
        )

    def _get_template_matching_results(
        self,
        file: FileStorage,
        file_id: UUID,
        classification: ClassificationDocumentType,
        pdf_document: PdfDocument,
        hashes_of_tables_to_parse: set[str] | None = None,
    ) -> list[SupplementalParsingResult]:
        results: list[SupplementalParsingResult] = parse_supplemental(
            classification=classification,
            file_id=file_id,
            pdf_document=pdf_document,
            hashes_of_tables_to_parse=hashes_of_tables_to_parse,
            ff_client=self._ff_client,
        )

        # If we didn't match any templates, return a generic name fields template
        if self._use_generic_name_templates:
            if not sum(len(result.groups_found) for result in results):
                self.logger.info("No template matching results found, adding generic name fields", file_id=file_id)
                name_results = self._generic_supplemental_parser.get_name_fields(
                    pdf_document=pdf_document, file=file, classification=classification
                )
                if name_results:
                    self.logger.info("Found supplemental generic name fields", results=name_results, file_id=file_id)
                    results.append(
                        SupplementalParsingResult(
                            template_name="GENERIC_NAME_FIELDS",
                            results=name_results,
                            template_config=[],
                            groups_found={"1_applicant_name"},
                            labels_found=[sf.value for sf in name_results],
                        )
                    )
                else:
                    self.logger.info("No supplemental generic name fields found", file_id=file_id)

        return results

    def _get_generic_parsing_results(
        self,
        file_id: UUID,
        pdf_document: PdfDocument,
        hashes_of_tables_to_parse: set[str],
    ) -> list[KeyValuePair]:
        generic_parsing_results = self.generic_supplemental_parser.generic_parsing(
            file_id=file_id, pdf_document=pdf_document, hashes_of_tables_to_parse=hashes_of_tables_to_parse
        )
        return generic_parsing_results

    def _is_overlapping_pair(self, pair_1: KeyValuePair, pair_2: KeyValuePair) -> bool:
        return pair_1.page_num == pair_2.page_num and (
            contains(pair_1.key, pair_2.key, 0.6)
            or contains(pair_2.key, pair_1.key, 0.6)
            or contains(pair_1.value, pair_2.value, 0.6)
            or contains(pair_2.value, pair_1.value, 0.6)
        )

    def _filter_generic_parsing_results(
        self, generic_parsing_results: list[KeyValuePair], template_parsing_results: list[KeyValuePair]
    ) -> list[KeyValuePair]:
        filtered = [
            result
            for result in generic_parsing_results
            if not any(
                self._is_overlapping_pair(result, template_result) for template_result in template_parsing_results
            )
        ]
        return filtered

    def _merge_template_and_generic_results(
        self,
        template_matching_results: list[SupplementalParsingResult],
        generic_parsing_results: list[KeyValuePair],
        pdf_document: PdfDocument,
        classification: ClassificationDocumentType,
        file_id: UUID,
    ) -> tuple[OnboardedFile, list[OnboardedFile]]:
        onboarded_data = OnboardedFile(files=[file_id])
        onboarded_data_list = []

        additional_fields = self._generic_supplemental_parser.get_additional_fields(
            pdf_document=pdf_document, classification=classification
        )

        template_pairs = []
        for parsing_result in template_matching_results:
            onboarded_data_to_merge = self._onboarded_data_loader.results_to_onboarded_file(
                results=parsing_result.results,
                additional_fields=additional_fields,
                file_id=file_id,
                fact_subtypes=self._fact_subtypes,
                reset_metric_counter=False,
            )
            onboarded_data_list.append(onboarded_data_to_merge)
            onboarded_data = self._onboarded_data_loader.merge_onboarded_files(onboarded_data, onboarded_data_to_merge)
            template_pairs.extend(list(parsing_result.results.values()))

        generic_parsing_results = self._filter_generic_parsing_results(generic_parsing_results, template_pairs)
        generic_parsing_onboarded_file = self._onboarded_data_loader.key_value_pairs_to_onboarded_file(
            key_value_pairs=generic_parsing_results,
            additional_fields=additional_fields,
            file_id=file_id,
            fact_subtypes=self._fact_subtypes,
        )
        onboarded_data_list.append(generic_parsing_onboarded_file)
        onboarded_data = self._onboarded_data_loader.merge_onboarded_files(
            onboarded_data, generic_parsing_onboarded_file
        )

        if onboarded_data.has_fni:
            for field in onboarded_data.fields:
                # Map fields that go to the FNI to the current file FNI entity
                for value in field.values:
                    if value.entity_idx is None:
                        value.entity_idx = onboarded_data.fni_idx

            # Remap FNI roles for certain classifications
            if classification == ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_PDF:
                onboarded_data.entities[onboarded_data.fni_idx].entity_role = (
                    SubmissionBusinessEntityRole.GENERAL_CONTRACTOR
                )

        if classification in [
            ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_PDF,
        ]:
            for entity in onboarded_data.entities:
                if entity.entity_role in [
                    SubmissionBusinessEntityRole.GENERAL_CONTRACTOR,
                    SubmissionBusinessEntityRole.PROJECT,
                ]:
                    entity.entity_role = None

        if self._ff_client.is_feature_enabled(FeatureFlags.ENABLE_SUPPLEMENTAL_POSTPROCESSING):
            onboarded_data, onboarded_data_list = self._extract_postprocess_fields(
                onboarded_file=onboarded_data,
                onboarded_file_list=onboarded_data_list,
                fact_subtypes=self._fact_subtypes,
            )

        return onboarded_data, onboarded_data_list

    def _extract_entity_postprocess_fields(
        self,
        onboarded_file: OnboardedFile,
        fact_subtype_id_to_subtype: dict,
        edx: int,
        postprocessing_fields: dict[str, str],
    ) -> list[ResolvedDataField]:
        entity_fields = {}
        for field in onboarded_file.fields:
            for value in field.values:
                if value.entity_idx == edx:
                    entity_fields[field.name] = value.value
                    break
        if not entity_fields:
            return []

        fields_rdf = []
        pairs = self._generic_supplemental_parser.extract_postprocess_fields(
            fields=postprocessing_fields, values=entity_fields
        )

        for key, (keys, value) in pairs.items():
            try:
                supp_field = SuppField[key]
            except KeyError:
                self.logger.warning(
                    "Unknown supplemental field in postprocessing",
                    field_name=key,
                    field_value=value,
                    matched_keys=keys,
                )
                continue
            fact_subtype_id = FIELD_TO_SUBTYPE_MAPPING.get(supp_field)
            fact_subtype = fact_subtype_id_to_subtype.get(fact_subtype_id)
            display_name, value_type, cleaned_value = OnboardedFileLoader.get_supp_field_values(
                supp_field, key, value, fact_subtype
            )

            nested = OnboardedFileLoader.get_field_values_for(
                onboarded_file=onboarded_file,
                edx=edx,
                keys=keys,
            )

            field = ResolvedDataField(
                name=display_name,
                value_type=value_type,
                fact_subtype_id=fact_subtype_id,
                values=[
                    ResolvedDataValue(
                        entity_idx=edx,
                        value=cleaned_value,
                        observed_name=";".join(keys),
                        observed_value=";".join(str(v.observed_value) for v in nested),
                        evidences=list(itertools.chain.from_iterable(v.evidences for v in nested)),
                    )
                ],
            )
            fields_rdf.append(field)

        return fields_rdf

    @classmethod
    def _extract_coverage_name(cls, name: str) -> CoverageName | None:
        remaps = {
            "generalliability": CoverageName.Liability,
            "building": CoverageName.Property,
            "buildingliability": CoverageName.Property,
            "cyberliability": CoverageName.CyberPrivacy,
            "cyberprivacy": CoverageName.CyberPrivacy,
        }

        coverage_name = CoverageName.try_parse_str(name.strip())
        if coverage_name:
            return coverage_name

        coverage_name = name.strip().lower().replace(" ", "")
        coverage = CoverageName.try_parse_str(coverage_name)
        if coverage:
            return coverage

        if coverage_name in remaps:
            return remaps[coverage_name]
        else:
            coverage_name = coverage_name.replace("liability", "")

        return CoverageName.try_parse_str(coverage_name)

    def _extract_entity_coverages_fields(
        self,
        onboarded_file: OnboardedFile,
    ) -> list[ResolvedDataField]:
        entity_fields = {}
        for field in onboarded_file.fields:
            for val in field.values:
                if val.entity_idx == onboarded_file.fni_idx:
                    entity_fields[field.name] = val.value
                    break
        if not entity_fields:
            return []

        coverage_fields = {}
        for coverage in CoverageName:
            if coverage.value.lower().endswith("temp"):
                continue
            if coverage.value.lower() == "other":
                continue

            coverage_fields[coverage.name] = f"Insurance coverage information for {coverage.name}"

        coverages = self._generic_supplemental_parser.extract_coverages_fields(
            fields=coverage_fields, values=entity_fields
        )

        coverage_values: list[CoverageDetails] = []
        for key, value in coverages.items():
            if not value or not isinstance(value, dict):
                if isinstance(value, (int, float)):
                    value: dict[str, Any] = {"aggregate_limit": value}  # noqa: PLW2901
                else:
                    continue

            coverage_name = self._extract_coverage_name(key)
            if not coverage_name:
                self.logger.warning(
                    "Unknown coverage name in postprocessing",
                    field_name=key,
                    field_value=value,
                )
                continue

            value["coverage_name"] = coverage_name.name
            for number_field in ["self_insurance_retention", "aggregate_limit", "each_occurrence_limit"]:
                if number_field in value:
                    num_val = value[number_field]
                    if isinstance(num_val, str):
                        value[number_field] = clean_non_negative_numerical(num_val)
                else:
                    value[number_field] = None
            for string_field in ["period", "other_terms", "retention"]:
                if string_field in value:
                    str_val = value[string_field].strip() if value[string_field] else None
                    if str_val and is_na(str_val):
                        str_val = None
                    value[string_field] = str_val
                else:
                    value[string_field] = None

            try:
                value["limit"] = value.pop("aggregate_limit", None)
                value["each_occurrence"] = value.pop("each_occurrence_limit", None)
                value_obj = CoverageDetailsSchema().load(value)
            except ValidationError:
                self.logger.warning(
                    "Error loading coverage details from LLM data",
                    coverage=value,
                )
                continue

            coverage_values.append(value_obj)

        rdv = ResolvedDataValue(
            value=CoverageDetailsSchema().dumps(coverage_values, many=True),
            entity_idx=onboarded_file.get_or_create_submission_entity_idx(),
        )
        fields = [
            ResolvedDataField(
                name=EntityInformation.COVERAGES_DETAILS,
                values=[rdv],
                value_type=FieldType.TEXT,
            )
        ]
        return fields

    def _extract_postprocess_fields(
        self,
        onboarded_file: OnboardedFile,
        onboarded_file_list: list[OnboardedFile],
        fact_subtypes: list[FactSubtype],
    ) -> tuple[OnboardedFile, list[OnboardedFile]]:
        fact_subtype_id_to_subtype = {fact_subtype.id: fact_subtype for fact_subtype in fact_subtypes}

        postprocessing_fields = {
            SuppField.DESCRIPTION_OF_OPERATIONS.name: "Short Description of Operations for the entity",
        }

        extract_coverages = True
        for field in onboarded_file.fields:
            if field.name in [
                EntityInformation.COVERAGES_DETAILS,
                EntityInformation.COVERAGES_DETAILS.name,
                EntityInformation.COVERAGES_DETAILS.value,
            ]:
                extract_coverages = False
                break

        futures = []
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            for edx, entity in enumerate(onboarded_file.entities):
                future = executor.submit(
                    self._extract_entity_postprocess_fields,
                    onboarded_file,
                    fact_subtype_id_to_subtype,
                    edx,
                    postprocessing_fields,
                )
                futures.append(future)

            if extract_coverages:
                future = executor.submit(
                    self._extract_entity_coverages_fields,
                    onboarded_file,
                )
                futures.append(future)

            desc_rdf = []
            for future in futures:
                desc_rdf.extend(future.result())

        for field in desc_rdf:
            if field.name == EntityInformation.COVERAGES_DETAILS:
                onboarded_file.add_or_create_entity_field(field, entity_information=True)
            else:
                onboarded_file.add_or_create_entity_field(field, entity_information=False)

        return onboarded_file, onboarded_file_list

    def parse_and_get_template_matching_results(
        self,
        file: FileStorage,
        file_id: UUID,
        classification: ClassificationDocumentType,
        pdf_document: PdfDocument,
        hashes_of_tables_to_parse: set[str],
        organization_id: int | None = None,
    ) -> tuple[OnboardedFile, list[OnboardedFile], list[SupplementalParsingResult]]:
        template_matching_results = self._get_template_matching_results(
            file=file,
            file_id=file_id,
            classification=classification,
            pdf_document=pdf_document,
            hashes_of_tables_to_parse=hashes_of_tables_to_parse,
        )
        generic_parsing_results = self._get_generic_parsing_results(
            file_id=file_id, pdf_document=pdf_document, hashes_of_tables_to_parse=hashes_of_tables_to_parse
        )
        onboarded_data, onboarded_data_list = self._merge_template_and_generic_results(
            template_matching_results=template_matching_results,
            generic_parsing_results=generic_parsing_results,
            pdf_document=pdf_document,
            classification=classification,
            file_id=file_id,
        )

        return onboarded_data, onboarded_data_list, template_matching_results

    def parse(
        self, file: FileStorage, file_id: UUID, classification: ClassificationDocumentType
    ) -> tuple[OnboardedFile, list[OnboardedFile]]:
        onboarded_data, onboarded_data_list, _ = self.parse_and_get_template_matching_results(
            file=file, file_id=file_id, classification=classification
        )
        return onboarded_data, onboarded_data_list
