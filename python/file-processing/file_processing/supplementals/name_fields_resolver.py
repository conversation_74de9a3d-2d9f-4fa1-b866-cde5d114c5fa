import dataclasses
import json

from common.utils.value_cleanup import clean_numerical
from datascience_common.classifiers.fact_identification.supplementals.supp_fields import (
    SuppField,
)
from infrastructure_common.logging import get_logger
from llm_common.clients import LLMClient, get_llm_client
from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from PIL import Image
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.models.openai import ChatCompletionPrompt
from static_common.schemas.openai import ChatCompletionPromptSchema
from structlog import BoundLogger

from file_processing.key_value_parser import KeyValuePair
from file_processing.ocr_utils import TextItem, match_to_item
from file_processing.pdf_documents.model.pdf_document import PdfDocument, PdfPage
from file_processing.supplemental_parsing import merge_address_fields
from file_processing.supplementals.config.common import (
    _LOCATION_ADDRESS_FIELDS,
    ADDRESS_FIELDS,
)
from file_processing.tables.utils import base64_encode_image
from file_processing.utils import clean_text

FIELDS = {
    SuppField.APPLICANT_NAME.name: "Name of the individual or company applying",
    SuppField.APPLICANT_ADDRESS.name: "Address of the applicant",
    SuppField.APPLICANT_ADDRESS_2.name: "Secondary address line for the applicant",
    SuppField.APPLICANT_CITY.name: "City where the applicant is located",
    SuppField.APPLICANT_STATE.name: "State where the applicant is located",
    SuppField.APPLICANT_ZIP.name: "ZIP code where the applicant is located",
    SuppField.EMAIL.name: "Contact email for the applicant",
    SuppField.WEBSITE.name: "Website URL of the applicant",
    SuppField.LOCATION_NAME.name: "The name of a location",
    SuppField.LOCATION_ADDRESS.name: "The physical address of a location",
    SuppField.LOCATION_CITY.name: "City of a location",
    SuppField.LOCATION_ZIP.name: "ZIP code of the business location",
    SuppField.LOCATION_STATE.name: "State of the business location",
    SuppField.GENERAL_CONTRACTOR_NAME.name: "Name of a general contractor",
    SuppField.GENERAL_CONTRACTOR_DESCRIPTION.name: "Description of a general contractor",
    SuppField.GENERAL_CONTRACTOR_ADDRESS.name: "Address of a general contractor",
    SuppField.GENERAL_CONTRACTOR_WEBSITE.name: "Website URL of a general contractor",
    SuppField.DESCRIPTION_OF_OPERATIONS.name: "Description of the applicant's operations",
}

PROJECT_FIELDS = {
    SuppField.PROJECT_NAME.name: "Name of the project",
    SuppField.PROJECT_ADDRESS.name: "The address where the project is located",
    SuppField.PROJECT_DESCRIPTION.name: "Description of the project scope and details",
    SuppField.PROJECT_START_DATE.name: "The date when the project begins",
    SuppField.PROJECT_END_DATE.name: "The date when the project is expected to complete",
}

# Extract these fields out so we ignore them in the parser
IGNORED_FIELDS = {
    "CARRIER_NAME": "Name of the insurance carrier",
    "LICENSE_NAME": "Name on the license",
    "LICENSE_NUMBER": "License number",
}


def _create_parser_chat_completion_prompt(image_data: Image.Image, pdf_page: PdfPage) -> ChatCompletionPrompt:
    emp_format = {
        "key_value_pairs": [
            {
                "field": "APPLICANT_NAME",
                "key": "Name of Applicant",
                "key_bounding_box": [],
                "value": "Company A",
                "value_bounding_box": [],
                "evidence": "Applicant Name: Company A",
                "confidence": "0.85",
            },
            {
                "field": "APPLICANT_BANKRUPTCY",
                "key": "a) Have you considered bankruptcy?",
                "key_bounding_box": [],
                "value": "Yes",
                "value_bounding_box": [],
                "evidence": "a) Have you considered bankruptcy?: [x] Yes [] No",
                "confidence": "0.66",
            },
            {
                "field": "APPLICANT_ADDRESS",
                "key": "Street",
                "key_bounding_box": [],
                "value": "1262 Main St",
                "value_bounding_box": [],
                "evidence": "Street: 1262 Main St",
                "confidence": "0.93",
            },
            {
                "field": "CARRIER_NAME",
                "key": "Carrier Name",
                "key_bounding_box": [],
                "value": "MGXA Insurance Ltd",
                "value_bounding_box": [],
                "evidence": "Carrier Name:\nMGXA Insurance Ltd",
                "confidence": "0.77",
            },
        ]
    }

    field_names = "\n".join([f"- {k}: {v}" for k, v in FIELDS.items()])
    ignore_field_names = "\n".join([f"- {k}: {v}" for k, v in IGNORED_FIELDS.items()])
    project_field_names = "\n".join([f"- {k}: {v}" for k, v in PROJECT_FIELDS.items()])

    prompt_sequence = {
        "messages": [
            {
                "role": "system",
                "content": f"""
Find data about businesses matching the supplied fields in the image and return them as key-value pairs.
If there is only one address with no specific label, it should be the applicant address.
Ignore any fields not listed. Ignore fields with no value in the document.
Assign the same value only to a single, most appropriate, field.
Ignore document headers and footers. Ignore mastheads, logos, and other non-text elements.
An address should consist of street, unit, city, state, and zip code, but some parts could be missing.

<base fields>
{field_names}
{ignore_field_names}
</base fields>

If the document contains information about a specific construction project or job, look for:
<project fields>
{project_field_names}
</project fields>

As evidence attach the full context of the field key and value in the image. Calculate the confidence that your
response is accurate as a decimal between 0.0 and 1.0.

Add precise normalized coordinates of the bounding box for each key and value in {pdf_page.unit} to indicate
the location of the text on the image, assuming the page is
{pdf_page.width} {pdf_page.unit} wide and {pdf_page.height} {pdf_page.unit} high.
Bounding box coordinates should be in the format [xmin, ymin, xmax, ymax].

If you find any key-value pairs, return them in the following JSON format, without comments:
{json.dumps(emp_format, indent=4)}
""".strip(),
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{base64_encode_image(image_data)}"},
                    },
                    {
                        "type": "text",
                        "text": "Look at your response and fix any errors. Ensure JSON values are properly quoted.",
                    },
                ],
            },
        ]
    }
    return ChatCompletionPromptSchema().load(prompt_sequence)  # type: ignore


def parse_name_fields(
    pdf_document: PdfDocument,
    images: list[Image] | None = None,
    classification: ClassificationDocumentType | None = None,
    cross_match_bboxes: bool = False,
    llm_client: LLMClient | None = None,
    logger: BoundLogger | None = None,
) -> dict[SuppField, KeyValuePair]:
    if pdf_document is None:
        return {}

    logger = logger or get_logger()
    llm_client = llm_client or get_llm_client()

    try:
        prompt = _create_parser_chat_completion_prompt(images[0], pdf_document.pages[0])
        llm_params = [
            ClaudeRequestParams(
                model=LLMModel.CLAUDE_3_7_SONNET,
                max_tokens=4096,
                return_json=True,
                raise_exceptions=True,
                use_cache=True,
            ),
            ClaudeRequestParams(
                model=LLMModel.CLAUDE_3_5_SONNET,
                max_tokens=4096,
                return_json=True,
                raise_exceptions=True,
                use_cache=True,
            ),
        ]
        items = llm_client.get_llm_response(llm_params, prompt, call_origin="supplemental_name_parser")
    except Exception:
        logger.warning("GPT extraction failed for supplemental name parsing", exc_info=True)
        return {}

    results = {}
    matched = 0
    if items:
        for item in items.get("key_value_pairs", []):
            try:
                key = item.get("key", "")
                value = item.get("value", "")
                evidence = item.get("evidence", "")
                confidence = item.get("confidence")
                if not clean_text(key) or not clean_text(value):
                    continue

                if key in IGNORED_FIELDS:
                    continue

                key_bounding_box = item.get("key_bounding_box")
                if not key_bounding_box or not isinstance(key_bounding_box, list):
                    continue
                value_bounding_box = item.get("value_bounding_box")
                if not value_bounding_box or not isinstance(value_bounding_box, list):
                    continue

                supp_field = SuppField[item["field"]]

                page = pdf_document.pages[0]
                key_xmin = round(key_bounding_box[0], 3)
                key_ymin = round(key_bounding_box[1], 3)
                key_xmax = round(key_bounding_box[2], 3)
                key_ymax = round(key_bounding_box[3], 3)
                if cross_match_bboxes:
                    key_line = match_to_item(key, page.lines, y_bound=key_ymin * 0.8, relax_y=True)
                    if not key_line:
                        key_line = match_to_item(key, page.lines)
                    if key_line:
                        matched += 1
                        logger.debug(f"Matched name LLM key >{key}< line to layout {key_line}")
                        key_xmin = key_line.xmin
                        key_ymin = key_line.ymin
                        key_xmax = key_line.xmax
                        key_ymax = key_line.ymax
                    else:
                        logger.debug(f"Could not match name LLM key >{key}< line to layout")

                value_xmin = round(value_bounding_box[0], 3)
                value_ymin = round(value_bounding_box[1], 3)
                value_xmax = round(value_bounding_box[2], 3)
                value_ymax = round(value_bounding_box[3], 3)
                if cross_match_bboxes:
                    value_line = match_to_item(value, page.lines, y_bound=value_ymin * 0.8, relax_y=True)
                    if not value_line:
                        value_line = match_to_item(value, page.lines, y_bound=key_ymin, relax_y=True)
                    if value_line:
                        matched += 1
                        logger.debug(f"Matched name LLM value >{value}< line to layout {value_line}")
                        value_xmin = value_line.xmin
                        value_ymin = value_line.ymin
                        value_xmax = value_line.xmax
                        value_ymax = value_line.ymax
                    else:
                        logger.debug(f"Could not match name LLM value >{value}< line to layout")
                if isinstance(confidence, str):
                    confidence = clean_numerical(confidence)
                confidence = confidence if confidence is not None else 0.9
                confidence *= 0.7 if len(value) < 20 else 0.8
                confidence = round(confidence, 3)
                key_ti = TextItem(
                    content=key,
                    page=pdf_document.pages[0].page_number,
                    confidence=confidence,
                    xmin=key_xmin,
                    ymin=key_ymin,
                    xmax=key_xmax,
                    ymax=key_ymax,
                )

                explanation = None
                if clean_text(evidence):
                    explanation = dataclasses.replace(key_ti, content=evidence)
                results[supp_field] = KeyValuePair(
                    key=key_ti,
                    value=dataclasses.replace(
                        key_ti,
                        content=value,
                        xmin=value_xmin,
                        ymin=value_ymin,
                        xmax=value_xmax,
                        ymax=value_ymax,
                    ),
                    page_num=pdf_document.pages[0].page_number,
                    explanation=explanation,
                )
            except Exception as e:
                logger.warning(f"Could not find field mapping for supplemental field {item['key']}: {e!r}")

        if results:
            matched_perc = matched / (len(results) * 2) * 100
            logger.info(f"Matched {matched}/{len(results) * 2} ({matched_perc:.1f}) name fields to layout")

        results = merge_address_fields(results, ADDRESS_FIELDS, SuppField.APPLICANT_ADDRESS)
        results = merge_address_fields(results, _LOCATION_ADDRESS_FIELDS, SuppField.LOCATION_ADDRESS)

    # A common case is that a sole address is recognized as the location address instead of the applicant address
    if results.get(SuppField.APPLICANT_NAME) and not results.get(SuppField.LOCATION_NAME):
        if not results.get(SuppField.APPLICANT_ADDRESS) and results.get(SuppField.LOCATION_ADDRESS):
            results[SuppField.APPLICANT_ADDRESS] = results[SuppField.LOCATION_ADDRESS]
            del results[SuppField.LOCATION_ADDRESS]

    # Or both
    if not results.get(SuppField.APPLICANT_NAME) and results.get(SuppField.LOCATION_NAME):
        if not results.get(SuppField.APPLICANT_ADDRESS) and results.get(SuppField.LOCATION_ADDRESS):
            results[SuppField.APPLICANT_NAME] = results[SuppField.LOCATION_NAME]
            del results[SuppField.LOCATION_NAME]
            results[SuppField.APPLICANT_ADDRESS] = results[SuppField.LOCATION_ADDRESS]
            del results[SuppField.LOCATION_ADDRESS]

    return results
