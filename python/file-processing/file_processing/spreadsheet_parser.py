import csv
import io
import re
from abc import ABC
from copy import deepcopy
from itertools import combinations
from math import ceil
from typing import Dict, List, Optional, Set, Tuple, Union

import numpy as np
import openpyxl
import pandas as pd
import pyxlsb
import xlrd
from infrastructure_common.logging import get_logger
from openpyxl import Workbook as OpenWorkbook
from pyxlsb import Workbook
from static_common.enums.ally_auto import AllyAutoSheetNames
from structlog.stdlib import BoundLogger
from werkzeug.datastructures import FileStorage
from xlrd import XLRDError
from xlrd.book import Book
from xlrd.sheet import Rowinfo, Sheet

from file_processing.ally_auto_questionnaire_parser import (
    AllyAutoQuestionnaireParserOnboardedFile,
)
from file_processing.exceptions import (
    AllSheetsEmptyException,
    FileBrokenException,
    FileEncryptedException,
    ParsingException,
)
from file_processing.file_types import MIMEFileType, detect_file_type
from file_processing.header.header_finder import Header<PERSON>inder, IHeaderFinder
from file_processing.header.header_utils import (
    clean_column_names,
    merge_multicolumn_headers,
)
from file_processing.header.string_utils import is_unnamed
from file_processing.logic.spreadsheet_parsing_result import postprocess_parsing_results
from file_processing.models.requested_properties import AdditionalFieldsOwner
from file_processing.models.workbook_metadata import (
    BookType,
    SpreadsheetParsingMetadata,
    SpreadsheetParsingResult,
    WorkbookMetadata,
)
from file_processing.spreadsheet.sheet_name import order_sheet_names
from file_processing.table_splitter.table_splitter import TableSplitter
from file_processing.table_splitter.table_splitter_interface import Table
from file_processing.utils import (
    limit_df_size_if_too_big,
    return_to_beginning_of_stream,
)

PYXLSB_ENGINE = "pyxlsb"
XLRD_ENGINE = "xlrd"
OPEN_PYXL_ENGINE = "openpyxl"
YES_VALUES = ["Y", "YES", "TRUE", "T"]
NO_VALUES = ["N", "NO", "FALSE", "F"]
UNKNOWN_VALUES = ["UNKNOWN"]
"""
Minimal number of rows to reflect on header row
If header row is above half of all rows it seams suspicious
"""

global_logger = get_logger()


class SpreadsheetParser(ABC):
    MAX_SPREADSHEET_ROWS = 10000  # we won't process more rows than that
    MAX_SPREADSHEET_COLS = 500
    MAX_CONSECUTIVE_EMPTY_CELLS = 100  # if we spot that many cells in a row, we truncate the rest of the df

    def __init__(
        self,
        log_errors: bool = True,
        return_raw_dfs: bool = False,
        header_finder: Optional[IHeaderFinder] = None,
        logger: Optional[BoundLogger] = None,
    ) -> None:
        """
        log_errors: Should the error/exception be logged or just warnings
        return_raw_dfs: If True, do not trim result DataFrames to headers but return exactly as parsed from file
        """
        self.log_errors = log_errors
        self.logger = logger or get_logger()  # logger that is kept for processing keeping its bindings
        self._parsing_metadata = SpreadsheetParsingMetadata()
        self.return_raw_dfs = return_raw_dfs
        if header_finder is None:
            self._header_finder = HeaderFinder(logger=self.logger)
        else:
            self._header_finder = header_finder
        self._table_splitter = TableSplitter(header_finder=self._header_finder, logger=self.logger)

    def _get_decoded_file(self, file: FileStorage) -> io.StringIO:
        for encoding in ("utf-8", "ISO-8859-1", "Windows-1252", "utf-16"):
            try:
                return_to_beginning_of_stream(file=file, logger=self.logger)
                read_file = file.read()
                if isinstance(read_file, str):
                    return io.StringIO(read_file)
                return io.StringIO(read_file.decode(encoding))
            except Exception as e:
                self.logger.warning("Could not decode file", encoding=encoding, error=str(e))
        raise RuntimeError("Cannot decode file")

    def _csv_to_workbook(
        self,
        file: FileStorage,
    ) -> pd.ExcelFile:
        return_to_beginning_of_stream(file=file, logger=self.logger)

        delimiter: str = csv.Sniffer().sniff(self._get_decoded_file(file).read(8192)).delimiter
        biggest_row_size: int = 0
        for idx, row in enumerate(self._get_decoded_file(file)):
            biggest_row_size = max(biggest_row_size, len(row.split(delimiter)))

        df = pd.read_csv(
            self._get_decoded_file(file),
            dtype=str,
            engine="python",
            on_bad_lines="skip",
            delimiter=delimiter,
            header=None,
            names=range(biggest_row_size),
        )
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine="openpyxl") as writer:
            df.to_excel(writer, index=False, sheet_name="Sheet1", header=False)
        excel_buffer.seek(0)
        return pd.ExcelFile(excel_buffer)

    def _get_workbook(self, file: FileStorage, file_type: MIMEFileType) -> BookType | None:
        return_to_beginning_of_stream(file=file, logger=self.logger)
        try:
            if file_type == MIMEFileType.XLSB:
                return pyxlsb.open_workbook(file)
            elif file_type == MIMEFileType.XLS:
                # note: formatting_info=True fails for xlsx
                return xlrd.open_workbook(file_contents=file.read(), formatting_info=True)
            elif file_type == MIMEFileType.XLSX:
                try:
                    # data_only is used to get the values of the cells instead of the formulae
                    return openpyxl.load_workbook(file, data_only=True)
                except Exception as e:
                    self.logger.warning("Could not open workbook, trying again with xlrd", error=str(e))

            elif file_type in (MIMEFileType.CSV, MIMEFileType.TXT):
                return self._csv_to_workbook(file=file)

            # fallback to xlrd
            return_to_beginning_of_stream(file=file, logger=self.logger)
            return xlrd.open_workbook(file_contents=file.read())
        except TypeError as e:
            if "NoneType" in str(e):
                self.logger.warning("Empty file", exc_info=e)
                return None
            else:
                raise e
        except KeyError as e:
            self.logger.warning(
                "Could not open workbook, trying again.",
                error=str(e),
                file_type=file_type,
                filename=file.filename,
            )
            return_to_beginning_of_stream(file=file, logger=self.logger)
            return pd.ExcelFile(file, engine="openpyxl")
        except XLRDError as e:
            error_msg = str(e)
            if "xlsb file" in error_msg:
                return_to_beginning_of_stream(file=file, logger=self.logger)
                return pyxlsb.open_workbook(file)
            else:
                raise e
        except AssertionError as e:
            # Most likely assert _unused_i == nstrings - 1 during unpack_SST_table
            self.logger.warning("Assertion error", exc_info=e)
            raise e
        except NotImplementedError as e:
            if "formatting_info=True not yet implemented" in str(e):
                # Happens when .xlsx file is saved as .xls
                return self._get_workbook(file=file, file_type=MIMEFileType.XLSX)
            raise e

    def _load_metadata(
        self, file: FileStorage | None, sheet_name: str, workbook: BookType | None = None
    ) -> WorkbookMetadata:
        result = WorkbookMetadata([], [], {})
        try:
            workbook = workbook or self._get_workbook(file=file, file_type=detect_file_type(file.stream))
            if isinstance(workbook, OpenWorkbook):
                workbook: OpenWorkbook = workbook
                hidden_rows = []
                for idx in range(1, min(self.MAX_SPREADSHEET_ROWS, workbook[sheet_name].max_row + 1)):
                    if workbook[sheet_name].row_dimensions[idx].hidden:
                        # returns 1-indexed rows like in excel
                        hidden_rows.append(idx - 1)
                hidden_columns_indexes = []
                merged_cells_idxes = self._get_merged_cells_idx(sheet_name, workbook)
                # getting by columns letters (column_letter may not be provided for merged cells)
                for idx, letter in enumerate(
                    [getattr(c[0], "column_letter", None) for c in workbook[sheet_name].columns]
                ):
                    if (
                        letter in workbook[sheet_name].column_dimensions
                        and workbook[sheet_name].column_dimensions[letter].hidden
                    ):
                        hidden_columns_indexes.append(idx)
                return WorkbookMetadata(hidden_rows, hidden_columns_indexes, merged_cells_idxes)
            elif isinstance(workbook, Book):
                workbook: Book = workbook
                sheet: Sheet = next((it for it in workbook.sheets() if it.name == sheet_name), None)
                if sheet is None:
                    return result
                hidden_rows = []
                for idx, info in sheet.rowinfo_map.items():
                    info: Rowinfo = info  # noqa
                    if info and info.hidden:
                        # xlrd uses 0-based indexing
                        hidden_rows.append(idx)
                hidden_columns_indexes = []
                for idx, info in sheet.colinfo_map.items():
                    if info and info.hidden:
                        hidden_columns_indexes.append(idx)
                return WorkbookMetadata(hidden_rows, hidden_columns_indexes, {})
            else:
                return result
        except Exception as e:
            self.logger.warning("Could not load metadata", exc_info=e)
        return result

    def _get_merged_cells_idx(self, sheet_name, workbook):
        merged_cells_idxes = {}
        if workbook[sheet_name].merged_cells:
            merged_cells_idxes = {
                (it.min_row - 1, it.max_row - 1): (it.min_col - 1, it.max_col - 1)
                for it in workbook[sheet_name].merged_cells.ranges
            }
        return merged_cells_idxes

    def _get_engine(self, workbook: BookType) -> str:
        is_xlsb = isinstance(workbook, Workbook)
        is_xlsx = isinstance(workbook, pd.ExcelFile) or isinstance(workbook, OpenWorkbook)
        if is_xlsb:
            return PYXLSB_ENGINE
        if is_xlsx:
            return OPEN_PYXL_ENGINE
        return XLRD_ENGINE

    def _process_hidden_rows(
        self,
        df: pd.DataFrame,
        metadata: WorkbookMetadata,
        header: list[int] | None,
    ) -> Tuple[pd.DataFrame, WorkbookMetadata]:
        try:
            if metadata.hidden_rows:
                if header:
                    shift = header[-1] + 1
                    hidden_rows = [int(it - shift) for it in metadata.hidden_rows if it - shift in df.index]
                else:
                    hidden_rows = metadata.hidden_rows
                self.logger.info(
                    "Dropping hidden rows",
                    hidden_rows=hidden_rows,
                    len_df=len(df),
                    n_dropped_rows=len(hidden_rows),
                )
                df = df.drop(index=hidden_rows, axis=0)
                metadata = WorkbookMetadata(
                    hidden_rows=hidden_rows,
                    hidden_columns=metadata.hidden_columns,
                    extraction_data=metadata.extraction_data,
                )
            if metadata.hidden_columns:
                excluded_columns = [df.columns[it] for it in metadata.hidden_columns if it < len(df.columns)]
                self.logger.info("Dropping hidden columns", excluded_columns=excluded_columns)
                df = df.drop(labels=excluded_columns, axis=1)
            return df, metadata
        except Exception as e:
            self.logger.warning("Could not removed hidden data", exc_info=e)
            return df, metadata

    def _get_clean_df(
        self,
        workbook: OpenWorkbook,
        sheet_name: str,
        metadata: WorkbookMetadata,
    ) -> Tuple[pd.DataFrame, WorkbookMetadata]:
        try:
            return self._get_df_from_workbook(
                workbook=workbook, sheet_name=sheet_name, metadata=metadata, fallback_df=None, header=None
            )
        except Exception as e:
            self.logger.warning("Could not get clean df", exc_info=e)
            return pd.DataFrame(), metadata

    def _get_df_from_workbook(
        self,
        workbook: BookType,
        sheet_name: str,
        metadata: Optional[WorkbookMetadata],
        fallback_df: Optional[pd.DataFrame],
        header: Optional[List[int]],
    ) -> Tuple[pd.DataFrame, WorkbookMetadata]:
        metadata = metadata or self._load_metadata(file=None, sheet_name=sheet_name, workbook=workbook)
        try:
            if header is not None and len(header) >= 2:
                # handle multiheader df, by default pandas fills NaNs with the value from the column on the left
                df = pd.read_excel(
                    workbook,
                    header=None,
                    sheet_name=sheet_name,
                    dtype=str,
                    engine=self._get_engine(workbook),
                    keep_default_na=False,
                    na_values=[""],
                )
                column_names = merge_multicolumn_headers(
                    header=df.iloc[header].T.apply(tuple, axis=1).tolist(), misleading_labels=None
                )
                df.columns = column_names
                df = df.iloc[header[-1] + 1 :]
            else:
                df = pd.read_excel(
                    workbook,
                    header=header,
                    sheet_name=sheet_name,
                    dtype=str,
                    engine=self._get_engine(workbook),
                    keep_default_na=False,
                    na_values=[""],
                )
        except Exception as e:
            if fallback_df is None:
                # if no fallback df, raise the error
                raise e
            else:
                self.logger.warning("Could not read the file with pandas, using fallback df", header=header, exc_info=e)
                df = self._get_pandas_df_from_fallback(fallback_df, header)

        if metadata is not None:
            # hidden columns may mess up with header detection, let's remove them if they are present
            df, metadata = self._process_hidden_rows(df=df, metadata=metadata, header=header)
        df = limit_df_size_if_too_big(
            df=df,
            max_consecutive_empty_cells=self.MAX_CONSECUTIVE_EMPTY_CELLS,
            max_rows=self.MAX_SPREADSHEET_ROWS,
            max_cols=self.MAX_SPREADSHEET_COLS,
        )
        return df, metadata

    def _get_index_group(self, index: str) -> Optional[str]:
        """
        Returns index group for given index.
        If used to recognise conflicting/misleading indexes.
        Index pointing bo be both name and address is misleading.
        By default, returns None (no group).
        """
        return None

    def _get_misleading_indexes(self, header: pd.MultiIndex) -> Set[str]:
        """
        Detects and returns list of indexes that are misleading when pared with other index.
        The first part of index have to have cases where it is conflicting with second part and is duplicating as well.
        Then it is misleading and can be removed.
        Example:
            ("Location Address", "Location Name"), ("Location Address", "Address Street") =>
            lets keep "Location Name", "Address Street" and "Location Address" is returned as misleading

            ("Business Name", "City"), "("Business Name", "Legal Name") => keeps "City", "Legal Name" and
             the "Business Name" is  misleading
        """
        conflicting: Set[str] = set()
        duplicate: Set[str] = set()
        try:
            for multi_index in header:
                if all([type(row) != str for row in multi_index]):
                    continue
                groups = [self._get_index_group(row) for row in multi_index]
                for idx_and_row_1, idx_and_row_2 in combinations(enumerate(multi_index), 2):
                    idx_1, row_1 = idx_and_row_1
                    idx_2, row_2 = idx_and_row_2
                    if idx_1 >= idx_2:
                        continue
                    row_1_group, row_2_group = groups[idx_1], groups[idx_2]
                    if row_1_group and row_2_group and row_1_group != row_2_group:
                        conflicting.add(row_1)
                    if row_1_group and row_2_group and row_1_group == row_2_group:
                        duplicate.add(row_1)
        except Exception:
            self.logger.exception("Could not _get_misleading_labels", headers=list(map(str, header)))
        return conflicting.intersection(duplicate)

    def _parse_one_sheet(
        self,
        workbook: BookType,
        sheet_name: str,
        filename: str,
        file: io.BytesIO,
        sheet_index: int,
        is_hidden: bool,
        file_type: MIMEFileType,
    ) -> List[SpreadsheetParsingResult]:
        result = []
        try:
            header = None
            try:
                header, _ = self.identify_header_rows_and_df(workbook, sheet_name)
            except Exception as e:
                if "already closed" in str(e).lower():
                    workbook = self._get_workbook_and_handle_exceptions(
                        filename=filename, file=file, file_type=file_type
                    )
                    header, _ = self.identify_header_rows_and_df(workbook, sheet_name)
                else:
                    self.logger.exception(
                        "Could not identify header rows", sheet_name=sheet_name, sheet_index=sheet_index
                    )
                    raise ParsingException(f"Could not identify header rows: {e}")
            try:
                df, _ = self._get_df_from_workbook(
                    workbook=workbook, sheet_name=sheet_name, metadata=None, header=header, fallback_df=None
                )
            except Exception as e:
                if "already closed" in str(e).lower():
                    workbook = self._get_workbook_and_handle_exceptions(
                        filename=filename, file=file, file_type=file_type
                    )
                    df, _ = self._get_df_from_workbook(
                        workbook=workbook, sheet_name=sheet_name, metadata=None, header=header, fallback_df=None
                    )
                else:
                    raise e
            raw_df = None
            try:
                if self.return_raw_dfs:
                    raw_df, _ = self._get_df_from_workbook(
                        workbook=workbook, sheet_name=sheet_name, metadata=None, header=None, fallback_df=None
                    )
            except Exception as e:
                if "already closed" in str(e).lower():
                    workbook = self._get_workbook_and_handle_exceptions(
                        filename=filename, file=file, file_type=file_type
                    )
                    raw_df, _ = self._get_df_from_workbook(
                        workbook=workbook, sheet_name=sheet_name, metadata=None, header=None, fallback_df=None
                    )

                else:
                    raise e
            result.append(
                SpreadsheetParsingResult(
                    sheet_idx=sheet_index,
                    sheet_name=sheet_name,
                    column_names=self._clean_column_names(raw_column_names=df.columns.tolist()),
                    df=raw_df if self.return_raw_dfs else df,
                    prediction=None,
                    workbook=workbook,
                    is_hidden=is_hidden,
                )
            )
        except ParsingException:
            pass
        return result

    def _split_tables(self, df: pd.DataFrame, sheet_name: str) -> List[Table]:
        if (
            AllyAutoSheetNames.GENERAL_INFO.is_name_matching(sheet_name)
            or AllyAutoSheetNames.DEALER_UW_QUESTIONNAIRE.is_name_matching(sheet_name)
            or AllyAutoSheetNames.RENEWAL_QUESTIONNAIRE.is_name_matching(sheet_name)
            or (len(df) > 0 and AllyAutoQuestionnaireParserOnboardedFile.YES_COLUMN in df.iloc[0].values)
        ):
            return [Table(header_rows=[0], df=df)]
        else:
            return self._table_splitter.split_tables(df=df)

    def _parse_one_sheet_with_table_splitting(
        self,
        workbook: BookType,
        sheet_name: str,
        filename: str,
        file: io.BytesIO,
        sheet_index: int,
        is_hidden: bool,
        file_type: MIMEFileType,
    ) -> List[SpreadsheetParsingResult]:
        result = []
        try:
            try:
                df, _ = self._get_df_from_workbook(
                    workbook=workbook, sheet_name=sheet_name, metadata=None, fallback_df=None, header=None
                )
            except Exception as e:
                if "already closed" in str(e).lower():
                    workbook = self._get_workbook_and_handle_exceptions(
                        filename=filename, file=file, file_type=file_type
                    )
                    df, _ = self._get_df_from_workbook(
                        workbook=workbook, sheet_name=sheet_name, metadata=None, fallback_df=None, header=None
                    )
                else:
                    self.logger.exception("Could not get df", sheet_name=sheet_name, sheet_index=sheet_index)
                    raise e

            tables = self._split_tables(df=df, sheet_name=sheet_name)
            all_column_names = []
            for table_idx, table in enumerate(tables):
                column_names = merge_multicolumn_headers(
                    header=table.df.iloc[table.header_rows].T.apply(tuple, axis=1).tolist(), misleading_labels=None
                )
                all_column_names.append(column_names)
                result_df = table.df
                if not self.return_raw_dfs:
                    result_df.columns = column_names
                    result_df = result_df.iloc[table.header_rows[-1] + 1 :]

                result.append(
                    SpreadsheetParsingResult(
                        sheet_idx=sheet_index,
                        sheet_name=f"{sheet_name}_table_{table_idx}",
                        column_names=self._clean_column_names(raw_column_names=column_names),
                        df=result_df,
                        prediction=None,
                        workbook=workbook,
                        is_hidden=is_hidden,
                        is_sub_table=True,
                    )
                )

            if len(tables) > 1:
                self.logger.info(
                    "Splitting tables",
                    sheet_name=sheet_name,
                    sheet_index=sheet_index,
                    n_tables=len(tables),
                    file_name=filename,
                    column_names=all_column_names,
                    is_hidden=is_hidden,
                )

        except ParsingException:
            pass
        return result

    def _parse_sheets_of_spreadsheet(
        self,
        filename: str,
        file: io.BytesIO,
        split_tables: bool,
        drop_hidden_rows: bool,
        file_type: MIMEFileType,
    ) -> List[SpreadsheetParsingResult]:
        workbook = self._get_workbook_and_handle_exceptions(filename=filename, file=deepcopy(file), file_type=file_type)
        if workbook is None:
            raise ParsingException("Invalid file.")
        sheet_names_with_visibility: list[tuple[str, bool]] = self.get_sheet_names_with_visibility(workbook)
        if len(sheet_names_with_visibility) == 0:
            raise AllSheetsEmptyException("All sheets in the provided submissions are empty.")
        file.seek(0)

        # TODO: ENG-18851 - clean up all this
        result = []
        for ind, (sheet_name, is_hidden) in enumerate(sheet_names_with_visibility):
            self.logger.info("Parsing sheets", sheet_name=sheet_name, sheet_index=ind)
            if split_tables:
                result += self._parse_one_sheet_with_table_splitting(
                    workbook=workbook,
                    sheet_name=sheet_name,
                    filename=filename,
                    file=file,
                    sheet_index=ind,
                    is_hidden=is_hidden,
                    file_type=file_type,
                )
            else:
                result += self._parse_one_sheet(
                    workbook=workbook,
                    sheet_name=sheet_name,
                    filename=filename,
                    file=file,
                    sheet_index=ind,
                    is_hidden=is_hidden,
                    file_type=file_type,
                )
        if len(result) == 0:
            raise AllSheetsEmptyException("All sheets in the provided submissions are empty.")
        return result

    def _get_workbook_and_handle_exceptions(
        self, filename: str, file: io.BytesIO, file_type: MIMEFileType
    ) -> Optional[BookType]:
        try:
            file.seek(0)
            file_stor = FileStorage(stream=file, filename=filename, content_type="application/octet-stream")
            workbook = self._get_workbook(file=file_stor, file_type=file_type)
        except XLRDError as e:
            if str(e) == "Workbook is encrypted":
                raise FileEncryptedException()
            if str(e) == "Can't find workbook in OLE2 compound document":
                raise FileBrokenException()
            raise ParsingException(f"Could not open the workbook from file {file} due to: {e}")
        except AssertionError:
            raise FileBrokenException()
        except Exception as e:
            raise ParsingException(f"Could not open the workbook from file {file} due to: {e}")

        return workbook

    def _clean_column_names(self, raw_column_names: List[Union[Tuple, str]]) -> List[str]:
        column_names = []
        for col in raw_column_names:
            if isinstance(col, Tuple):
                column_names.append(" ".join([str(c) for c in col if not is_unnamed(c)]))
            elif isinstance(col, str) and not is_unnamed(col) and len(col.strip()) > 0 and col.strip() != "*":
                column_names.append(col)
        return clean_column_names(column_names=column_names)

    def _get_pandas_df_from_fallback(self, fallback_df: pd.DataFrame, header: Optional[List[int]]) -> pd.DataFrame:
        """
        When we can not read the file with pandas, we use the fallback df to get the data
        We need to clean the df, as it might have some extra rows and columns
        """
        # the file was closed, and we have df from header identification
        if header:
            new_df = fallback_df.drop(fallback_df.index[: fallback_df.index.get_loc(header[0])])
            new_header = pd.MultiIndex.from_arrays(new_df.loc[header].fillna("").values.tolist())
            # drop the header, as it is already used as columns
            new_df = new_df.drop(header)
        else:
            new_df = fallback_df
            new_header = new_df.iloc[0].apply(lambda x: str(x).strip("*").strip())
            new_df = new_df.drop(new_df.index[0])

        # get columns from the first row of the df, in clean format
        new_df.columns = new_header

        df = new_df.reset_index(drop=True)
        df = df.astype(object)
        df = df.replace("", np.nan)
        if drop_thresh := ceil(len(df.columns) * 0.05):  # drop rows with more than 95% NaN
            df = df.dropna(thresh=drop_thresh)  # Keeps rows with at least x non-null values
        return df

    def _can_be_identifier_column(self, value: str):
        return not is_unnamed(s=value)

    @staticmethod
    def get_sheet_names_with_visibility(workbook: BookType) -> list[tuple[str, bool]]:
        is_xlsb = isinstance(workbook, Workbook)
        is_xlsm = isinstance(workbook, pd.ExcelFile)
        is_xlsx = isinstance(workbook, OpenWorkbook)
        if is_xlsb:
            # pyxlsb does not have a way for checking if tab is hidden
            return [(x, False) for x in workbook.sheets]
        elif is_xlsm:
            openpyxl_workbook = openpyxl.load_workbook(workbook._io, keep_links=False)
            return [(s.title, s.sheet_state == "hidden") for s in openpyxl_workbook.worksheets]
        elif is_xlsx:
            return [(s.title, s.sheet_state in ("hidden", "veryHidden")) for s in workbook.worksheets]
        else:
            return [(s.name, s.visibility != 0) for s in workbook.sheets() if s.nrows > 0]

    @staticmethod
    def get_sheet_names(workbook: BookType) -> List[str]:
        is_xlsb = isinstance(workbook, Workbook)
        is_xlsm = isinstance(workbook, pd.ExcelFile)
        is_xlsx = isinstance(workbook, OpenWorkbook)
        if is_xlsb:
            return workbook.sheets
        elif is_xlsm:
            return workbook.sheet_names
        elif is_xlsx:
            return workbook.sheetnames
        else:
            sheets = workbook.sheets()
            sheets_name = [s.name for s in sheets if s.nrows > 0]
            return sheets_name

    @property
    def parsing_metadata(self) -> SpreadsheetParsingMetadata:
        return self._parsing_metadata

    def identify_header_rows_and_df(
        self,
        workbook: BookType,
        sheet_name: str,
        metadata: Optional[WorkbookMetadata] = None,
        clean_df: Optional[pd.DataFrame] = None,
    ) -> Tuple[Optional[List[int]], pd.DataFrame]:
        """It assumes that there is single table in sheet and at most 3 rows of headers"""
        if clean_df is not None:
            df = clean_df
        else:
            df, metadata = self._get_clean_df(workbook=workbook, sheet_name=sheet_name, metadata=metadata)
        header = self._header_finder.identify_header_rows_for_data_frame(
            df=df,
            merged_cells=metadata.merged_cells if metadata else None,
            hidden_rows=metadata.hidden_rows if metadata else [],
        )

        if header:
            self._parsing_metadata.header_row_start_idx = header[0] + 1
            self._parsing_metadata.header_row_end_idx = header[-1] + 1

        return header, df

    def parse_sheets_of_excel(
        self, sheet_wise_df: Dict[Union[int, str], pd.DataFrame]
    ) -> List[SpreadsheetParsingResult]:
        sheets = []
        for sheet in sheet_wise_df:
            df = sheet_wise_df[sheet]
            sheets.append(
                SpreadsheetParsingResult(
                    sheet_name=None,
                    sheet_idx=0,
                    df=df,
                    column_names=self._clean_column_names(raw_column_names=df.columns.tolist()),
                    prediction=None,
                )
            )
        return sheets

    def parse_spreadsheet(
        self,
        filename: str,
        file: io.BytesIO,
        split_tables: bool = False,
        apply_postprocessing: bool = True,
        s3_key: Optional[str] = None,
        drop_hidden_rows: bool = False,
    ) -> List[SpreadsheetParsingResult]:
        # TODO refactor for entire parser to use FileStorage
        self.logger = self.logger.bind(s3_key=s3_key, filename=filename)
        file_type = detect_file_type(file)
        try:
            result = self._parse_sheets_of_spreadsheet(
                filename=filename,
                file=file,
                split_tables=split_tables,
                drop_hidden_rows=drop_hidden_rows,
                file_type=file_type,
            )
        except FileEncryptedException:
            self.logger.warning("Cannot open - file encrypted")
            return []
        except FileBrokenException:
            self.logger.warning("Cannot open - file broken")
            return []

        if apply_postprocessing:
            result = postprocess_parsing_results(result)
        return result

    def parse(self, file: FileStorage) -> List[AdditionalFieldsOwner]:
        """The common method for spreadsheet parsing"""
        pass

    def get_all_sheets_tsv_data_with_metadata(self, file: FileStorage) -> dict[str, Tuple[str, WorkbookMetadata]]:
        """
        Returns all sheets tsv data with metadata.
        :param file: The file to be processed.
        :return: A dictionary with sheet names as keys and tuples of tsv data and metadata as values.
        """
        return self._get_tsv_data_with_metadata(file=file, only_first_sheet=False)

    def get_tsv_data_with_metadata(self, file: FileStorage) -> Tuple[str, WorkbookMetadata]:
        """
        Returns the tsv file and metadata.
        :param file: The file to be processed.
        :return: The tsv file and metadata.
        """
        result = self._get_tsv_data_with_metadata(file=file, only_first_sheet=True)
        return next(iter(result.values()))

    def _get_tsv_data_with_metadata(
        self, file: FileStorage, only_first_sheet: bool
    ) -> dict[str, Tuple[str, WorkbookMetadata]]:
        file_type = detect_file_type(file.stream)
        try:
            workbook = self._get_workbook(file=file, file_type=file_type)
        except Exception as e:
            raise ParsingException(f"Could not open the workbook from file {file} due to: {e}")
        if not workbook:
            raise ParsingException("Invalid file.")
        sheet_names = self.get_sheet_names(workbook)

        if len(sheet_names) == 0:
            raise AllSheetsEmptyException("All sheets in the provided submissions are empty.")

        result = dict()
        try:
            sheet_names = order_sheet_names(sheet_names)

            for sheet_name in sheet_names:
                self.logger.info("Parsing sheet", sheet_name=sheet_name)
                metadata = self._load_metadata(file, workbook=workbook, sheet_name=sheet_name)
                cleaned_df, metadata = self._get_clean_df(workbook=workbook, sheet_name=sheet_name, metadata=metadata)
                if cleaned_df.empty:
                    self.logger.warning("Sheet is empty", sheet_name=sheet_name)
                    continue
                # the new lines as cell content mess up csv and mislead the AI
                cleaned_df = cleaned_df.applymap(lambda x: re.sub(r"[\r\n\t]+", " ", str(x)))
                cleaned_df = cleaned_df.replace("nan", np.NaN)  # Cleanup sheet
                tsv_data = cleaned_df.to_csv(sep="\t", header=False, index=True)
                result[sheet_name] = tsv_data, metadata
                if only_first_sheet:
                    return result
            if result:
                return result
        except Exception as e:
            self.logger.warning("Could not get tsv data", exc_info=e)
            raise ParsingException(f"Could not get tsv data due to: {e}")
        raise AllSheetsEmptyException("All sheets in the provided submissions are empty.")
