ACORD_140_2014_12 = {
    "$schema": "https://schema.cognitiveservices.azure.com/formrecognizer/2021-03-01/labels.json",
    "document": "140_2014_1.pdf",
    "labels": [
        {
            "label": "agency_customer_id_label",
            "value": [
                {
                    "page": 1,
                    "text": "AGENCY",
                    "boundingBoxes": [
                        [
                            0.45730588235294123,
                            0.042972727272727274,
                            0.5146117647058823,
                            0.042536363636363636,
                            0.5146117647058823,
                            0.055554545454545454,
                            0.45787058823529414,
                            0.055118181818181816,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "CUSTOMER",
                    "boundingBoxes": [
                        [
                            0.5174117647058823,
                            0.042536363636363636,
                            0.5910117647058823,
                            0.042536363636363636,
                            0.5910117647058823,
                            0.055554545454545454,
                            0.5179764705882353,
                            0.055554545454545454,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "ID:",
                    "boundingBoxes": [
                        [
                            0.5949411764705883,
                            0.042536363636363636,
                            0.615164705882353,
                            0.042536363636363636,
                            0.615164705882353,
                            0.055554545454545454,
                            0.5949411764705883,
                            0.055554545454545454,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "agency_customer_id_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6172061781674685,
                            0.037619764358371444,
                            0.8946224466281874,
                            0.037619764358371444,
                            0.8946224466281874,
                            0.054263018635875127,
                            0.6172061781674685,
                            0.054263018635875127,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "date_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.8336578864937959,
                            0.07669169576444634,
                            0.9667365927404233,
                            0.07669169576444634,
                            0.9667365927404233,
                            0.08964791569524255,
                            0.8336578864937959,
                            0.08964791569524255,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "date_label",
            "value": [
                {
                    "page": 1,
                    "text": "DATE",
                    "boundingBoxes": [
                        [
                            0.8511294117647059,
                            0.06597272727272728,
                            0.876964705882353,
                            0.06597272727272728,
                            0.876964705882353,
                            0.07551818181818182,
                            0.8511294117647059,
                            0.07551818181818182,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "(MM/DD/YYYY)",
                    "boundingBoxes": [
                        [
                            0.8797764705882354,
                            0.06597272727272728,
                            0.9488705882352941,
                            0.06509999999999999,
                            0.949435294117647,
                            0.07639090909090909,
                            0.8797764705882354,
                            0.07551818181818182,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "agency_label",
            "value": [
                {
                    "page": 1,
                    "text": "AGENCY",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.0967909090909091,
                            0.07584705882352942,
                            0.09635454545454546,
                            0.07584705882352942,
                            0.10503636363636364,
                            0.034835294117647055,
                            0.10547272727272726,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "NAME",
                    "boundingBoxes": [
                        [
                            0.07809411764705881,
                            0.09635454545454546,
                            0.10674117647058823,
                            0.09635454545454546,
                            0.10674117647058823,
                            0.10503636363636364,
                            0.07809411764705881,
                            0.10503636363636364,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "carrier_label",
            "value": [
                {
                    "page": 1,
                    "text": "CARRIER",
                    "boundingBoxes": [
                        [
                            0.5056235294117647,
                            0.09505454545454546,
                            0.5634823529411764,
                            0.09548181818181818,
                            0.5634823529411764,
                            0.10633636363636363,
                            0.5056235294117647,
                            0.10633636363636363,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "agency_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.031972301911886436,
                            0.10203184785212283,
                            0.49849040500083014,
                            0.10203184785212283,
                            0.49849040500083014,
                            0.12107370717112309,
                            0.031972301911886436,
                            0.12107370717112309,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "carrier_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.49836601307189543,
                            0.10227272727272729,
                            0.8797293517467111,
                            0.10227272727272729,
                            0.8797293517467111,
                            0.12106745265993268,
                            0.49836601307189543,
                            0.12106745265993268,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "naics_code_label",
            "value": [
                {
                    "page": 1,
                    "text": "NAIC",
                    "boundingBoxes": [
                        [
                            0.8870823529411764,
                            0.09635454545454546,
                            0.9101176470588235,
                            0.09635454545454546,
                            0.9106705882352941,
                            0.10503636363636364,
                            0.8870823529411764,
                            0.10547272727272726,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "CODE",
                    "boundingBoxes": [
                        [
                            0.9140470588235294,
                            0.09635454545454546,
                            0.9410117647058823,
                            0.0967909090909091,
                            0.9410117647058823,
                            0.10547272727272726,
                            0.9140470588235294,
                            0.10503636363636364,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "naics_code_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.8799019607843137,
                            0.10227272727272729,
                            0.969379307674555,
                            0.10227272727272729,
                            0.969379307674555,
                            0.12114563404981205,
                            0.8799019607843137,
                            0.12114563404981205,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "policy_number_label",
            "value": [
                {
                    "page": 1,
                    "text": "POLICY",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.12673636363636362,
                            0.07078823529411765,
                            0.12673636363636362,
                            0.07078823529411765,
                            0.13541818181818183,
                            0.034270588235294115,
                            0.13541818181818183,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "NUMBER",
                    "boundingBoxes": [
                        [
                            0.07303529411764706,
                            0.12673636363636362,
                            0.11404705882352942,
                            0.12673636363636362,
                            0.11404705882352942,
                            0.13585454545454545,
                            0.07303529411764706,
                            0.13541818181818183,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "policy_number_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.031883267105529786,
                            0.1303585290331878,
                            0.40486625908015744,
                            0.1303585290331878,
                            0.40486625908015744,
                            0.15221804564343788,
                            0.031883267105529786,
                            0.15221804564343788,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "effective_date_label",
            "value": [
                {
                    "page": 1,
                    "text": "EFFECTIVE",
                    "boundingBoxes": [
                        [
                            0.41123529411764703,
                            0.1276,
                            0.46348235294117646,
                            0.12717272727272727,
                            0.46348235294117646,
                            0.13541818181818183,
                            0.41123529411764703,
                            0.13498181818181818,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "DATE",
                    "boundingBoxes": [
                        [
                            0.46685882352941177,
                            0.12717272727272727,
                            0.49269411764705884,
                            0.12673636363636362,
                            0.49269411764705884,
                            0.13585454545454545,
                            0.46685882352941177,
                            0.13541818181818183,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "effective_date_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.40522875816993464,
                            0.13005050505050508,
                            0.5012221547413183,
                            0.13005050505050508,
                            0.5012221547413183,
                            0.15303425935377768,
                            0.40522875816993464,
                            0.15303425935377768,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "named_insured_label",
            "value": [
                {
                    "page": 1,
                    "text": "NAMED",
                    "boundingBoxes": [
                        [
                            0.503929411764706,
                            0.12717272727272727,
                            0.5393294117647058,
                            0.12673636363636362,
                            0.5398823529411765,
                            0.13541818181818183,
                            0.503929411764706,
                            0.13541818181818183,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "INSURED(S)",
                    "boundingBoxes": [
                        [
                            0.5415764705882352,
                            0.1263,
                            0.6,
                            0.12717272727272727,
                            0.6,
                            0.13671818181818182,
                            0.542129411764706,
                            0.13541818181818183,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "named_insured_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5008169934640523,
                            0.13005050505050508,
                            0.9692376613917147,
                            0.13005050505050508,
                            0.9692376613917147,
                            0.15129550524286217,
                            0.5008169934640523,
                            0.15129550524286217,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "location_number_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "PREMISES",
                    "boundingBoxes": [
                        [
                            0.23034117647058824,
                            0.21788181818181818,
                            0.2803411764705882,
                            0.21788181818181818,
                            0.2803411764705882,
                            0.22656363636363636,
                            0.23034117647058824,
                            0.2256909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "#:",
                    "boundingBoxes": [
                        [
                            0.28202352941176473,
                            0.21788181818181818,
                            0.2932588235294118,
                            0.21788181818181818,
                            0.2932588235294118,
                            0.22699999999999998,
                            0.28258823529411764,
                            0.22656363636363636,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "location_number_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.29361822130069404,
                            0.21411717299765565,
                            0.33927572845972,
                            0.21411717299765565,
                            0.33927572845972,
                            0.22927369092113525,
                            0.29361822130069404,
                            0.22927369092113525,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_number_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "BUILDING",
                    "boundingBoxes": [
                        [
                            0.22864705882352943,
                            0.2330727272727273,
                            0.2736,
                            0.2330727272727273,
                            0.27415294117647054,
                            0.24175454545454544,
                            0.22921176470588234,
                            0.24131818181818182,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "#:",
                    "boundingBoxes": [
                        [
                            0.27696470588235295,
                            0.2330727272727273,
                            0.2882,
                            0.2330727272727273,
                            0.2882,
                            0.24175454545454544,
                            0.27752941176470586,
                            0.24175454545454544,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_number_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.29330065359477125,
                            0.22916666666666663,
                            0.34119206986014977,
                            0.22916666666666663,
                            0.34119206986014977,
                            0.24390720098149843,
                            0.29330065359477125,
                            0.24390720098149843,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "typedef_table_name_premises_information_table_1_meta",
            "value": [
                {
                    "page": 1,
                    "text": "PREMISES",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.23177272727272727,
                            0.10505882352941176,
                            0.23133636363636365,
                            0.10562352941176471,
                            0.24305454545454544,
                            0.034835294117647055,
                            0.24305454545454544,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "INFORMATION",
                    "boundingBoxes": [
                        [
                            0.10787058823529412,
                            0.23133636363636365,
                            0.2039294117647059,
                            0.23133636363636365,
                            0.2039294117647059,
                            0.24305454545454544,
                            0.1084235294117647,
                            0.24305454545454544,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_1_col_subject_of_insurance_header",
            "value": [
                {
                    "page": 1,
                    "text": "SUBJECT",
                    "boundingBoxes": [
                        [
                            0.06461176470588235,
                            0.2474,
                            0.11067058823529412,
                            0.2474,
                            0.11067058823529412,
                            0.25694545454545453,
                            0.06516470588235293,
                            0.25738181818181816,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "OF",
                    "boundingBoxes": [
                        [
                            0.11291764705882353,
                            0.2474,
                            0.12471764705882353,
                            0.2474,
                            0.12471764705882353,
                            0.25694545454545453,
                            0.11348235294117648,
                            0.25694545454545453,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "INSURANCE",
                    "boundingBoxes": [
                        [
                            0.1275294117647059,
                            0.2474,
                            0.1865176470588235,
                            0.2474,
                            0.1865176470588235,
                            0.25694545454545453,
                            0.1275294117647059,
                            0.25694545454545453,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_1_col_subject_of_insurance_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.02930427694214541,
                            0.25993918603515,
                            0.2245078771640322,
                            0.25993918603515,
                            0.2245078771640322,
                            0.28939015971178494,
                            0.02930427694214541,
                            0.28939015971178494,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_subject_of_insurance_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029411764705882353,
                            0.28914141414141414,
                            0.224350320220014,
                            0.28914141414141414,
                            0.224350320220014,
                            0.32006105383587713,
                            0.029411764705882353,
                            0.32006105383587713,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_subject_of_insurance_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029411764705882353,
                            0.32007575757575757,
                            0.22429044858128713,
                            0.32007575757575757,
                            0.22429044858128713,
                            0.3503155679270049,
                            0.029411764705882353,
                            0.3503155679270049,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_subject_of_insurance_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029411764705882353,
                            0.35037878787878785,
                            0.22438183160881767,
                            0.35037878787878785,
                            0.22438183160881767,
                            0.38037284937093907,
                            0.029411764705882353,
                            0.38037284937093907,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_subject_of_insurance_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029411764705882353,
                            0.38068181818181823,
                            0.2241328916372689,
                            0.38068181818181823,
                            0.2241328916372689,
                            0.40973616409326574,
                            0.029411764705882353,
                            0.40973616409326574,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "typedef_table_name_premises_information_table_2_meta",
            "value": [
                {
                    "page": 2,
                    "text": "ADDITIONAL",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.06597272727272728,
                            0.11910588235294117,
                            0.06597272727272728,
                            0.11965882352941175,
                            0.07812727272727273,
                            0.034835294117647055,
                            0.07769090909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "PREMISES",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.07986363636363636,
                            0.10505882352941176,
                            0.07986363636363636,
                            0.10505882352941176,
                            0.0915818181818182,
                            0.034835294117647055,
                            0.09114545454545454,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "INFORMATION",
                    "boundingBoxes": [
                        [
                            0.10787058823529412,
                            0.07942727272727274,
                            0.2039294117647059,
                            0.07986363636363636,
                            0.2039294117647059,
                            0.09201818181818182,
                            0.10787058823529412,
                            0.0915818181818182,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_2_col_subject_of_insurance_header",
            "value": [
                {
                    "page": 2,
                    "text": "SUBJECT",
                    "boundingBoxes": [
                        [
                            0.06516470588235293,
                            0.0959181818181818,
                            0.11011764705882354,
                            0.0959181818181818,
                            0.11011764705882354,
                            0.10547272727272726,
                            0.06572941176470588,
                            0.10590000000000001,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "OF",
                    "boundingBoxes": [
                        [
                            0.11236470588235294,
                            0.0959181818181818,
                            0.12471764705882353,
                            0.0959181818181818,
                            0.12528235294117646,
                            0.10547272727272726,
                            0.11291764705882353,
                            0.10547272727272726,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "INSURANCE",
                    "boundingBoxes": [
                        [
                            0.12696470588235292,
                            0.0959181818181818,
                            0.1859529411764706,
                            0.0959181818181818,
                            0.1859529411764706,
                            0.10547272727272726,
                            0.1275294117647059,
                            0.10547272727272726,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_2_col_subject_of_insurance_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.03132989549378565,
                            0.10817048856750067,
                            0.22430857596679468,
                            0.10817048856750067,
                            0.22430857596679468,
                            0.13790558235527361,
                            0.03132989549378565,
                            0.13790558235527361,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_subject_of_insurance_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.03104575163398693,
                            0.13762626262626265,
                            0.2249948684516345,
                            0.13762626262626265,
                            0.2249948684516345,
                            0.1674726179641406,
                            0.03104575163398693,
                            0.1674726179641406,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_subject_of_insurance_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.03104575163398693,
                            0.16729797979797978,
                            0.22493203885795193,
                            0.16729797979797978,
                            0.22493203885795193,
                            0.19858578881894284,
                            0.03104575163398693,
                            0.19858578881894284,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_subject_of_insurance_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.03104575163398693,
                            0.19886363636363635,
                            0.22409592195740768,
                            0.19886363636363635,
                            0.22409592195740768,
                            0.22919478513702707,
                            0.03104575163398693,
                            0.22919478513702707,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_subject_of_insurance_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.03104575163398693,
                            0.22916666666666663,
                            0.22500936758863815,
                            0.22916666666666663,
                            0.22500936758863815,
                            0.25842196976188403,
                            0.03104575163398693,
                            0.25842196976188403,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_amount_header",
            "value": [
                {
                    "page": 1,
                    "text": "AMOUNT",
                    "boundingBoxes": [
                        [
                            0.2606705882352941,
                            0.2487,
                            0.3033764705882353,
                            0.2487,
                            0.3039294117647059,
                            0.2565090909090909,
                            0.26123529411764707,
                            0.2565090909090909,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "table_premises_information_table_1_col_amount_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.26010101010101006,
                            0.34207995434370325,
                            0.26010101010101006,
                            0.34207995434370325,
                            0.28887538178846406,
                            0.2246732026143791,
                            0.28887538178846406,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_amount_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.28914141414141414,
                            0.3427189647093418,
                            0.28914141414141414,
                            0.3427189647093418,
                            0.31996582759949377,
                            0.2246732026143791,
                            0.31996582759949377,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_amount_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.32007575757575757,
                            0.34156721548744234,
                            0.32007575757575757,
                            0.34156721548744234,
                            0.34969911557626165,
                            0.2246732026143791,
                            0.34969911557626165,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_amount_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.35037878787878785,
                            0.34213352407495434,
                            0.35037878787878785,
                            0.34213352407495434,
                            0.3799794061659675,
                            0.2246732026143791,
                            0.3799794061659675,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_amount_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.3800505050505051,
                            0.34175853595619643,
                            0.3800505050505051,
                            0.34175853595619643,
                            0.4103336160276918,
                            0.2246732026143791,
                            0.4103336160276918,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_amount_header",
            "value": [
                {
                    "page": 2,
                    "text": "AMOUNT",
                    "boundingBoxes": [
                        [
                            0.2606705882352941,
                            0.09635454545454546,
                            0.3039294117647059,
                            0.09635454545454546,
                            0.3039294117647059,
                            0.10547272727272726,
                            0.26123529411764707,
                            0.10503636363636364,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "table_premises_information_table_2_col_amount_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.10795454545454541,
                            0.34280345005291113,
                            0.10795454545454541,
                            0.34280345005291113,
                            0.1379522759654629,
                            0.2246732026143791,
                            0.1379522759654629,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_amount_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.1382575757575758,
                            0.3420573002247703,
                            0.1382575757575758,
                            0.3420573002247703,
                            0.1683951889536095,
                            0.2246732026143791,
                            0.1683951889536095,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_amount_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.16856060606060608,
                            0.34236723938415187,
                            0.16856060606060608,
                            0.34236723938415187,
                            0.1991692602803995,
                            0.2246732026143791,
                            0.1991692602803995,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_amount_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.19886363636363635,
                            0.3419807720372687,
                            0.19886363636363635,
                            0.3419807720372687,
                            0.22844720554153275,
                            0.2246732026143791,
                            0.22844720554153275,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_amount_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2246732026143791,
                            0.22853535353535348,
                            0.3411274827466256,
                            0.22853535353535348,
                            0.3411274827466256,
                            0.25872453936035766,
                            0.2246732026143791,
                            0.25872453936035766,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_coins_percentage_header",
            "value": [
                {
                    "page": 1,
                    "text": "COINS",
                    "boundingBoxes": [
                        [
                            0.3432588235294118,
                            0.24826363636363638,
                            0.37360000000000004,
                            0.2487,
                            0.37360000000000004,
                            0.25694545454545453,
                            0.3438235294117647,
                            0.2565090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "%",
                    "boundingBoxes": [
                        [
                            0.38034117647058824,
                            0.24913636363636363,
                            0.3865176470588235,
                            0.24913636363636363,
                            0.3865176470588235,
                            0.25694545454545453,
                            0.38034117647058824,
                            0.25694545454545453,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_1_col_coins_percentage_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3423202614379085,
                            0.26010101010101006,
                            0.38954686697890384,
                            0.26010101010101006,
                            0.38954686697890384,
                            0.28848443827505255,
                            0.3423202614379085,
                            0.28848443827505255,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_coins_percentage_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3423202614379085,
                            0.288510101010101,
                            0.38979558358828414,
                            0.288510101010101,
                            0.38979558358828414,
                            0.31965176012898155,
                            0.3423202614379085,
                            0.31965176012898155,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_coins_percentage_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3415032679738562,
                            0.32007575757575757,
                            0.38917187886014587,
                            0.32007575757575757,
                            0.38917187886014587,
                            0.34995866165661393,
                            0.3415032679738562,
                            0.34995866165661393,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_coins_percentage_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3423202614379085,
                            0.35037878787878785,
                            0.38908004503514393,
                            0.35037878787878785,
                            0.38908004503514393,
                            0.3807504736086885,
                            0.3423202614379085,
                            0.3807504736086885,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_coins_percentage_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3423202614379085,
                            0.38068181818181823,
                            0.3889461207070161,
                            0.38068181818181823,
                            0.3889461207070161,
                            0.4104216693969608,
                            0.3423202614379085,
                            0.4104216693969608,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_coins_percentage_header",
            "value": [
                {
                    "page": 2,
                    "text": "COINS",
                    "boundingBoxes": [
                        [
                            0.3426941176470588,
                            0.0967909090909091,
                            0.37360000000000004,
                            0.0967909090909091,
                            0.37360000000000004,
                            0.10503636363636364,
                            0.3432588235294118,
                            0.10503636363636364,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "%",
                    "boundingBoxes": [
                        [
                            0.3808941176470588,
                            0.09721818181818181,
                            0.3870823529411765,
                            0.09765454545454545,
                            0.3870823529411765,
                            0.10547272727272726,
                            0.3808941176470588,
                            0.10547272727272726,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_2_col_coins_percentage_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3431372549019608,
                            0.10795454545454541,
                            0.3895315613414035,
                            0.10795454545454541,
                            0.3895315613414035,
                            0.13775121554557257,
                            0.3431372549019608,
                            0.13775121554557257,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_coins_percentage_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3423202614379085,
                            0.1382575757575758,
                            0.3896922705351569,
                            0.1382575757575758,
                            0.3896922705351569,
                            0.16732483789478036,
                            0.3423202614379085,
                            0.16732483789478036,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_coins_percentage_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3423202614379085,
                            0.16729797979797978,
                            0.3889384678882659,
                            0.16729797979797978,
                            0.3889384678882659,
                            0.19894750246434412,
                            0.3423202614379085,
                            0.19894750246434412,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_coins_percentage_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3423202614379085,
                            0.19886363636363635,
                            0.3885787854070083,
                            0.19886363636363635,
                            0.3885787854070083,
                            0.22859504408557052,
                            0.3423202614379085,
                            0.22859504408557052,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_coins_percentage_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3423202614379085,
                            0.22853535353535348,
                            0.38960808952890513,
                            0.22853535353535348,
                            0.38960808952890513,
                            0.258251456019439,
                            0.3423202614379085,
                            0.258251456019439,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_valuation_header",
            "value": [
                {
                    "page": 1,
                    "text": "VALU-",
                    "boundingBoxes": [
                        [
                            0.3904470588235294,
                            0.2447909090909091,
                            0.4207882352941177,
                            0.2447909090909091,
                            0.4207882352941177,
                            0.25217272727272727,
                            0.39101176470588234,
                            0.2526,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "ATION",
                    "boundingBoxes": [
                        [
                            0.3904470588235294,
                            0.2513,
                            0.4185411764705882,
                            0.25086363636363634,
                            0.4185411764705882,
                            0.2591181818181818,
                            0.39101176470588234,
                            0.2591181818181818,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_1_col_valuation_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.26010101010101006,
                            0.4246579994046493,
                            0.26010101010101006,
                            0.4246579994046493,
                            0.2889131700527606,
                            0.3897058823529412,
                            0.2889131700527606,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_valuation_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.28914141414141414,
                            0.42489141037652933,
                            0.28914141414141414,
                            0.42489141037652933,
                            0.3189687460555296,
                            0.3897058823529412,
                            0.3189687460555296,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_valuation_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.3194444444444444,
                            0.4249449801077805,
                            0.3194444444444444,
                            0.4249449801077805,
                            0.35010354342977057,
                            0.3897058823529412,
                            0.35010354342977057,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_valuation_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.35037878787878785,
                            0.4252319608109115,
                            0.35037878787878785,
                            0.4252319608109115,
                            0.3798220475521348,
                            0.3897058823529412,
                            0.3798220475521348,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_valuation_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.3800505050505051,
                            0.42505211957028277,
                            0.3800505050505051,
                            0.42505211957028277,
                            0.40997224022308765,
                            0.3897058823529412,
                            0.40997224022308765,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_valuation_header",
            "value": [
                {
                    "page": 2,
                    "text": "VALU-",
                    "boundingBoxes": [
                        [
                            0.39101176470588234,
                            0.09375454545454547,
                            0.42022352941176466,
                            0.09331818181818181,
                            0.42022352941176466,
                            0.10112727272727273,
                            0.39101176470588234,
                            0.10112727272727273,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "ATION",
                    "boundingBoxes": [
                        [
                            0.3904470588235294,
                            0.10069090909090908,
                            0.4185411764705882,
                            0.09939090909090909,
                            0.4185411764705882,
                            0.10807272727272728,
                            0.39101176470588234,
                            0.1072090909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_2_col_valuation_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.10795454545454541,
                            0.424960285745281,
                            0.10795454545454541,
                            0.424960285745281,
                            0.13787835669344495,
                            0.3897058823529412,
                            0.13787835669344495,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_valuation_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.13762626262626265,
                            0.4238161893421317,
                            0.13762626262626265,
                            0.4238161893421317,
                            0.16772104519280062,
                            0.3897058823529412,
                            0.16772104519280062,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_valuation_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.16792929292929293,
                            0.4247421804109014,
                            0.16792929292929293,
                            0.4247421804109014,
                            0.1980072493242676,
                            0.3897058823529412,
                            0.1980072493242676,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_valuation_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.1982323232323232,
                            0.42460060326402344,
                            0.1982323232323232,
                            0.42460060326402344,
                            0.2287931477345807,
                            0.3897058823529412,
                            0.2287931477345807,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_valuation_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3897058823529412,
                            0.22853535353535348,
                            0.42463504094839916,
                            0.22853535353535348,
                            0.42463504094839916,
                            0.25823075862327416,
                            0.3897058823529412,
                            0.25823075862327416,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_causes_of_loss_header",
            "value": [
                {
                    "page": 1,
                    "text": "CAUSES",
                    "boundingBoxes": [
                        [
                            0.43370588235294116,
                            0.24782727272727276,
                            0.4730352941176471,
                            0.24782727272727276,
                            0.4730352941176471,
                            0.25694545454545453,
                            0.43370588235294116,
                            0.25694545454545453,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "OF",
                    "boundingBoxes": [
                        [
                            0.47640000000000005,
                            0.2474,
                            0.4893294117647059,
                            0.24782727272727276,
                            0.4893294117647059,
                            0.25694545454545453,
                            0.47640000000000005,
                            0.25694545454545453,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "LOSS",
                    "boundingBoxes": [
                        [
                            0.4921294117647058,
                            0.24782727272727276,
                            0.5185411764705883,
                            0.24782727272727276,
                            0.5185411764705883,
                            0.25694545454545453,
                            0.4921294117647058,
                            0.25694545454545453,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_1_col_causes_of_loss_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.26010101010101006,
                            0.5298804308100097,
                            0.26010101010101006,
                            0.5298804308100097,
                            0.28903439765887107,
                            0.42483660130718953,
                            0.28903439765887107,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_causes_of_loss_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.28914141414141414,
                            0.530745199328778,
                            0.28914141414141414,
                            0.530745199328778,
                            0.3202519846177728,
                            0.42483660130718953,
                            0.3202519846177728,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_causes_of_loss_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.32007575757575757,
                            0.530511788356898,
                            0.32007575757575757,
                            0.530511788356898,
                            0.3501774627017892,
                            0.42483660130718953,
                            0.3501774627017892,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_causes_of_loss_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.35037878787878785,
                            0.5309059085225314,
                            0.35037878787878785,
                            0.5309059085225314,
                            0.3802300819336776,
                            0.42483660130718953,
                            0.3802300819336776,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_causes_of_loss_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.3800505050505051,
                            0.5303472527537696,
                            0.3800505050505051,
                            0.5303472527537696,
                            0.41029748501996965,
                            0.42483660130718953,
                            0.41029748501996965,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_causes_of_loss_header",
            "value": [
                {
                    "page": 2,
                    "text": "CAUSES",
                    "boundingBoxes": [
                        [
                            0.43258823529411766,
                            0.09635454545454546,
                            0.4730352941176471,
                            0.09635454545454546,
                            0.4730352941176471,
                            0.10547272727272726,
                            0.43258823529411766,
                            0.10503636363636364,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "OF",
                    "boundingBoxes": [
                        [
                            0.47640000000000005,
                            0.09635454545454546,
                            0.4893294117647059,
                            0.09635454545454546,
                            0.4893294117647059,
                            0.10547272727272726,
                            0.47640000000000005,
                            0.10547272727272726,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "LOSS",
                    "boundingBoxes": [
                        [
                            0.4921294117647058,
                            0.09635454545454546,
                            0.5185411764705883,
                            0.09635454545454546,
                            0.5185411764705883,
                            0.10547272727272726,
                            0.4921294117647058,
                            0.10547272727272726,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_2_col_causes_of_loss_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.10795454545454541,
                            0.531200542044413,
                            0.10795454545454541,
                            0.531200542044413,
                            0.13790201086049036,
                            0.42483660130718953,
                            0.13790201086049036,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_causes_of_loss_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.13762626262626265,
                            0.530967131072533,
                            0.13762626262626265,
                            0.530967131072533,
                            0.16806698738584702,
                            0.42483660130718953,
                            0.16806698738584702,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_causes_of_loss_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.16792929292929293,
                            0.5309326933881574,
                            0.16792929292929293,
                            0.5309326933881574,
                            0.19750164150365968,
                            0.42483660130718953,
                            0.19750164150365968,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_causes_of_loss_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.19760101010101006,
                            0.5300449664131386,
                            0.19760101010101006,
                            0.5300449664131386,
                            0.22864530919054293,
                            0.42483660130718953,
                            0.22864530919054293,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_causes_of_loss_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.42483660130718953,
                            0.22853535353535348,
                            0.5307451993287784,
                            0.22853535353535348,
                            0.5307451993287784,
                            0.25868905810978915,
                            0.42483660130718953,
                            0.25868905810978915,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_inflation_guard_percentage_header",
            "value": [
                {
                    "page": 1,
                    "text": "INFLATION",
                    "boundingBoxes": [
                        [
                            0.5314588235294118,
                            0.24435454545454546,
                            0.5820235294117646,
                            0.2447909090909091,
                            0.5825882352941176,
                            0.2526,
                            0.5314588235294118,
                            0.25217272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "GUARD",
                    "boundingBoxes": [
                        [
                            0.5348352941176471,
                            0.25173636363636365,
                            0.5691058823529411,
                            0.25173636363636365,
                            0.5691058823529411,
                            0.2591181818181818,
                            0.5353882352941176,
                            0.2591181818181818,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "%",
                    "boundingBoxes": [
                        [
                            0.5769647058823529,
                            0.25173636363636365,
                            0.5814588235294118,
                            0.25173636363636365,
                            0.5814588235294118,
                            0.2595454545454545,
                            0.5769647058823529,
                            0.2595454545454545,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_1_col_inflation_guard_percentage_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5302287581699346,
                            0.26010101010101006,
                            0.5895494586050273,
                            0.26010101010101006,
                            0.5895494586050273,
                            0.2892384148496425,
                            0.5302287581699346,
                            0.2892384148496425,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_inflation_guard_percentage_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5302287581699346,
                            0.28914141414141414,
                            0.5890252405206411,
                            0.28914141414141414,
                            0.5890252405206411,
                            0.3196281059619357,
                            0.5302287581699346,
                            0.3196281059619357,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_inflation_guard_percentage_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5302287581699346,
                            0.3194444444444444,
                            0.5897675639394069,
                            0.3194444444444444,
                            0.5897675639394069,
                            0.3503341715584686,
                            0.5302287581699346,
                            0.3503341715584686,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_inflation_guard_percentage_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5310457516339869,
                            0.35037878787878785,
                            0.5894155342768994,
                            0.35037878787878785,
                            0.5894155342768994,
                            0.3801118110984477,
                            0.5310457516339869,
                            0.3801118110984477,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_inflation_guard_percentage_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5302287581699346,
                            0.3800505050505051,
                            0.5898938354487846,
                            0.3800505050505051,
                            0.5898938354487846,
                            0.4098598829296194,
                            0.5302287581699346,
                            0.4098598829296194,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_inflation_guard_percentage_header",
            "value": [
                {
                    "page": 2,
                    "text": "INFLATION",
                    "boundingBoxes": [
                        [
                            0.5314588235294118,
                            0.09288181818181819,
                            0.5820235294117646,
                            0.09331818181818181,
                            0.5820235294117646,
                            0.10156363636363636,
                            0.5314588235294118,
                            0.10069090909090908,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "GUARD",
                    "boundingBoxes": [
                        [
                            0.5353882352941176,
                            0.10026363636363636,
                            0.5691058823529411,
                            0.10026363636363636,
                            0.5691058823529411,
                            0.10807272727272728,
                            0.5353882352941176,
                            0.10763636363636363,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "%",
                    "boundingBoxes": [
                        [
                            0.5769647058823529,
                            0.10026363636363636,
                            0.5825882352941176,
                            0.10026363636363636,
                            0.5825882352941176,
                            0.10807272727272728,
                            0.5775294117647058,
                            0.10807272727272728,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_2_col_inflation_guard_percentage_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5310457516339869,
                            0.10795454545454541,
                            0.5900086277300367,
                            0.10795454545454541,
                            0.5900086277300367,
                            0.13758267960537018,
                            0.5310457516339869,
                            0.13758267960537018,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_inflation_guard_percentage_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5310457516339869,
                            0.13762626262626265,
                            0.5894882360550256,
                            0.13762626262626265,
                            0.5894882360550256,
                            0.16829465874366467,
                            0.5310457516339869,
                            0.16829465874366467,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_inflation_guard_percentage_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5310457516339869,
                            0.16856060606060608,
                            0.589645118839404,
                            0.16856060606060608,
                            0.589645118839404,
                            0.19844780818549823,
                            0.5310457516339869,
                            0.19844780818549823,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_inflation_guard_percentage_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5310457516339869,
                            0.1982323232323232,
                            0.5907165134644268,
                            0.1982323232323232,
                            0.5907165134644268,
                            0.22826979928868862,
                            0.5310457516339869,
                            0.22826979928868862,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_inflation_guard_percentage_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5310457516339869,
                            0.22853535353535348,
                            0.5895418057862768,
                            0.22853535353535348,
                            0.5895418057862768,
                            0.258227801852393,
                            0.5310457516339869,
                            0.258227801852393,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_header",
            "value": [
                {
                    "page": 1,
                    "text": "DED",
                    "boundingBoxes": [
                        [
                            0.6101176470588235,
                            0.2487,
                            0.6286470588235293,
                            0.2487,
                            0.6286470588235293,
                            0.2560727272727273,
                            0.6101176470588235,
                            0.2560727272727273,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "table_premises_information_table_1_col_deductible_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5898692810457516,
                            0.26010101010101006,
                            0.6537910456032667,
                            0.26010101010101006,
                            0.6537910456032667,
                            0.28955478933388257,
                            0.5898692810457516,
                            0.28955478933388257,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5898692810457516,
                            0.2897727272727273,
                            0.6528956658094976,
                            0.2897727272727273,
                            0.6528956658094976,
                            0.31963106273281683,
                            0.5898692810457516,
                            0.31963106273281683,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5890522875816994,
                            0.3194444444444444,
                            0.6542884788220273,
                            0.3194444444444444,
                            0.6542884788220273,
                            0.3500030132198255,
                            0.5890522875816994,
                            0.3500030132198255,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5890522875816994,
                            0.3497474747474747,
                            0.6542119506345258,
                            0.3497474747474747,
                            0.6542119506345258,
                            0.38053462933439464,
                            0.5890522875816994,
                            0.38053462933439464,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5890522875816994,
                            0.3800505050505051,
                            0.6539785396626457,
                            0.3800505050505051,
                            0.6539785396626457,
                            0.41073804388120094,
                            0.5890522875816994,
                            0.41073804388120094,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_header",
            "value": [
                {
                    "page": 2,
                    "text": "DED",
                    "boundingBoxes": [
                        [
                            0.6095529411764705,
                            0.0967909090909091,
                            0.6292117647058824,
                            0.0967909090909091,
                            0.6292117647058824,
                            0.10460000000000001,
                            0.6095529411764705,
                            0.10460000000000001,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "table_premises_information_table_2_col_deductible_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5898692810457516,
                            0.10795454545454541,
                            0.6536915589595146,
                            0.10795454545454541,
                            0.6536915589595146,
                            0.13797001659074748,
                            0.5898692810457516,
                            0.13797001659074748,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5898692810457516,
                            0.1382575757575758,
                            0.6533165708407567,
                            0.1382575757575758,
                            0.6533165708407567,
                            0.16807290092760852,
                            0.5898692810457516,
                            0.16807290092760852,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5898692810457516,
                            0.16792929292929293,
                            0.6537719135563914,
                            0.16792929292929293,
                            0.6537719135563914,
                            0.19856312224984707,
                            0.5898692810457516,
                            0.19856312224984707,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5906862745098039,
                            0.1982323232323232,
                            0.6538369625157676,
                            0.1982323232323232,
                            0.6538369625157676,
                            0.22896464044566323,
                            0.5906862745098039,
                            0.22896464044566323,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5906862745098039,
                            0.22916666666666663,
                            0.6537719135563914,
                            0.22916666666666663,
                            0.6537719135563914,
                            0.2591030060330931,
                            0.5906862745098039,
                            0.2591030060330931,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_type_header",
            "value": [
                {
                    "page": 1,
                    "text": "DED",
                    "boundingBoxes": [
                        [
                            0.6691058823529412,
                            0.2447909090909091,
                            0.687635294117647,
                            0.2447909090909091,
                            0.687635294117647,
                            0.25173636363636365,
                            0.6691058823529412,
                            0.25173636363636365,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "TYPE",
                    "boundingBoxes": [
                        [
                            0.6674117647058824,
                            0.2513,
                            0.6910117647058823,
                            0.2513,
                            0.6910117647058823,
                            0.2586818181818182,
                            0.6674117647058824,
                            0.2586818181818182,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_1_col_deductible_type_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6535947712418301,
                            0.26010101010101006,
                            0.7071809356137757,
                            0.26010101010101006,
                            0.7071809356137757,
                            0.2897262820449651,
                            0.6535947712418301,
                            0.2897262820449651,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_type_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6535947712418301,
                            0.2897727272727273,
                            0.7065495780668872,
                            0.2897727272727273,
                            0.7065495780668872,
                            0.3195955814822474,
                            0.6535947712418301,
                            0.3195955814822474,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_type_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6544117647058824,
                            0.3194444444444444,
                            0.7071464979294,
                            0.3194444444444444,
                            0.7071464979294,
                            0.3499734455110177,
                            0.6544117647058824,
                            0.3499734455110177,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_type_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6544117647058824,
                            0.3497474747474747,
                            0.7072153732981514,
                            0.3497474747474747,
                            0.7072153732981514,
                            0.379783609530685,
                            0.6544117647058824,
                            0.379783609530685,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_deductible_type_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6544117647058824,
                            0.38068181818181823,
                            0.7069360454137704,
                            0.38068181818181823,
                            0.7069360454137704,
                            0.4106552542965396,
                            0.6544117647058824,
                            0.4106552542965396,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_type_header",
            "value": [
                {
                    "page": 2,
                    "text": "DED",
                    "boundingBoxes": [
                        [
                            0.6685411764705882,
                            0.09288181818181819,
                            0.687635294117647,
                            0.09288181818181819,
                            0.6870823529411765,
                            0.10069090909090908,
                            0.6685411764705882,
                            0.10069090909090908,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "TYPE",
                    "boundingBoxes": [
                        [
                            0.6668588235294118,
                            0.10026363636363636,
                            0.6898823529411765,
                            0.10026363636363636,
                            0.6898823529411765,
                            0.10677272727272728,
                            0.6674117647058824,
                            0.10677272727272728,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_2_col_deductible_type_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6535947712418301,
                            0.10795454545454541,
                            0.7073913881294049,
                            0.10795454545454541,
                            0.7073913881294049,
                            0.13853475982896946,
                            0.6535947712418301,
                            0.13853475982896946,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_type_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6535947712418301,
                            0.1382575757575758,
                            0.707632451920035,
                            0.1382575757575758,
                            0.707632451920035,
                            0.16844249728770144,
                            0.6535947712418301,
                            0.16844249728770144,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_type_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6535947712418301,
                            0.16856060606060608,
                            0.7070737961512733,
                            0.16856060606060608,
                            0.7070737961512733,
                            0.19911308163366537,
                            0.6535947712418301,
                            0.19911308163366537,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_type_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6535947712418301,
                            0.1982323232323232,
                            0.707112060245024,
                            0.1982323232323232,
                            0.707112060245024,
                            0.22896759721654392,
                            0.6535947712418301,
                            0.22896759721654392,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_deductible_type_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6535947712418301,
                            0.22916666666666663,
                            0.7074526106794062,
                            0.22916666666666663,
                            0.7074526106794062,
                            0.2589610810308174,
                            0.6535947712418301,
                            0.2589610810308174,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_blanket_number_header",
            "value": [
                {
                    "page": 1,
                    "text": "BLKT",
                    "boundingBoxes": [
                        [
                            0.7073058823529412,
                            0.24435454545454546,
                            0.7337058823529412,
                            0.24435454545454546,
                            0.7337058823529412,
                            0.25217272727272727,
                            0.7073058823529412,
                            0.25217272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.7162941176470587,
                            0.25086363636363634,
                            0.7230352941176471,
                            0.25086363636363634,
                            0.7230352941176471,
                            0.25824545454545456,
                            0.7162941176470587,
                            0.25824545454545456,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_1_col_blanket_number_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.26010101010101006,
                            0.736716989580028,
                            0.26010101010101006,
                            0.736716989580028,
                            0.28946608620745984,
                            0.7075163398692811,
                            0.28946608620745984,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_blanket_number_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.2897727272727273,
                            0.737099630517536,
                            0.2897727272727273,
                            0.737099630517536,
                            0.31991786950824874,
                            0.7075163398692811,
                            0.31991786950824874,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_blanket_number_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.32007575757575757,
                            0.7365524539768994,
                            0.32007575757575757,
                            0.7365524539768994,
                            0.3499024830098799,
                            0.7075163398692811,
                            0.3499024830098799,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_blanket_number_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.3497474747474747,
                            0.7362539940456432,
                            0.3497474747474747,
                            0.7362539940456432,
                            0.38040157464476076,
                            0.7075163398692811,
                            0.38040157464476076,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_blanket_number_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.38068181818181823,
                            0.7367858649487794,
                            0.38068181818181823,
                            0.7367858649487794,
                            0.410421669396961,
                            0.7075163398692811,
                            0.410421669396961,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_blanket_number_header",
            "value": [
                {
                    "page": 2,
                    "text": "BLKT",
                    "boundingBoxes": [
                        [
                            0.7073058823529412,
                            0.09331818181818181,
                            0.7331411764705882,
                            0.09331818181818181,
                            0.7331411764705882,
                            0.10069090909090908,
                            0.7073058823529412,
                            0.10026363636363636,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.7168588235294118,
                            0.09982727272727274,
                            0.7230352941176471,
                            0.09982727272727274,
                            0.7230352941176471,
                            0.1072090909090909,
                            0.7168588235294118,
                            0.1072090909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_2_col_blanket_number_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.10795454545454541,
                            0.7359899717987626,
                            0.10795454545454541,
                            0.7359899717987626,
                            0.13688783844839547,
                            0.7075163398692811,
                            0.13688783844839547,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_blanket_number_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.1369949494949495,
                            0.7361392017643907,
                            0.1369949494949495,
                            0.7361392017643907,
                            0.16840110249537166,
                            0.7075163398692811,
                            0.16840110249537166,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_blanket_number_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.16856060606060608,
                            0.7361812922675167,
                            0.16856060606060608,
                            0.7361812922675167,
                            0.1982526613073694,
                            0.7075163398692811,
                            0.1982526613073694,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_blanket_number_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.1982323232323232,
                            0.7365103634737737,
                            0.1982323232323232,
                            0.7365103634737737,
                            0.22857730346028615,
                            0.7075163398692811,
                            0.22857730346028615,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_blanket_number_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7075163398692811,
                            0.22853535353535348,
                            0.7362004243143921,
                            0.22853535353535348,
                            0.7362004243143921,
                            0.2587688909235696,
                            0.7075163398692811,
                            0.2587688909235696,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_forms_and_conditions_to_apply_header",
            "value": [
                {
                    "page": 1,
                    "text": "FORMS",
                    "boundingBoxes": [
                        [
                            0.765164705882353,
                            0.24826363636363638,
                            0.800564705882353,
                            0.24782727272727276,
                            0.8011294117647059,
                            0.25694545454545453,
                            0.7657294117647059,
                            0.25694545454545453,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "AND",
                    "boundingBoxes": [
                        [
                            0.8033764705882354,
                            0.24782727272727276,
                            0.8236,
                            0.24782727272727276,
                            0.8241529411764706,
                            0.25738181818181816,
                            0.8033764705882354,
                            0.25694545454545453,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "CONDITIONS",
                    "boundingBoxes": [
                        [
                            0.8269647058823529,
                            0.24782727272727276,
                            0.8882,
                            0.2474,
                            0.8887647058823529,
                            0.25738181818181816,
                            0.8269647058823529,
                            0.25738181818181816,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "TO",
                    "boundingBoxes": [
                        [
                            0.8910117647058824,
                            0.2474,
                            0.9028117647058823,
                            0.24782727272727276,
                            0.9028117647058823,
                            0.25694545454545453,
                            0.8910117647058824,
                            0.25738181818181816,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "APPLY",
                    "boundingBoxes": [
                        [
                            0.9067411764705883,
                            0.24782727272727276,
                            0.938764705882353,
                            0.24782727272727276,
                            0.938764705882353,
                            0.25694545454545453,
                            0.9067411764705883,
                            0.25694545454545453,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_1_col_forms_and_conditions_to_apply_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7369281045751634,
                            0.26010101010101006,
                            0.9703078027006148,
                            0.26010101010101006,
                            0.9703078027006148,
                            0.289566616417405,
                            0.7369281045751634,
                            0.289566616417405,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_forms_and_conditions_to_apply_2_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7369281045751634,
                            0.2897727272727273,
                            0.971164918400633,
                            0.2897727272727273,
                            0.971164918400633,
                            0.31983803669446853,
                            0.7369281045751634,
                            0.31983803669446853,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_forms_and_conditions_to_apply_3_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7369281045751634,
                            0.32007575757575757,
                            0.9703231083381152,
                            0.32007575757575757,
                            0.9703231083381152,
                            0.34988769915547613,
                            0.7369281045751634,
                            0.34988769915547613,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_forms_and_conditions_to_apply_4_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7369281045751634,
                            0.3497474747474747,
                            0.9709085489725026,
                            0.3497474747474747,
                            0.9709085489725026,
                            0.3799491887000067,
                            0.7369281045751634,
                            0.3799491887000067,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_1_col_forms_and_conditions_to_apply_5_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7369281045751634,
                            0.3800505050505051,
                            0.971635566753768,
                            0.3800505050505051,
                            0.971635566753768,
                            0.41019695481002416,
                            0.7369281045751634,
                            0.41019695481002416,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_forms_and_conditions_to_apply_header",
            "value": [
                {
                    "page": 2,
                    "text": "FORMS",
                    "boundingBoxes": [
                        [
                            0.765164705882353,
                            0.09635454545454546,
                            0.800564705882353,
                            0.09635454545454546,
                            0.8011294117647059,
                            0.10547272727272726,
                            0.7657294117647059,
                            0.10547272727272726,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "AND",
                    "boundingBoxes": [
                        [
                            0.8033764705882354,
                            0.09635454545454546,
                            0.8236,
                            0.09635454545454546,
                            0.8241529411764706,
                            0.10547272727272726,
                            0.8033764705882354,
                            0.10547272727272726,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "CONDITIONS",
                    "boundingBoxes": [
                        [
                            0.8269647058823529,
                            0.09635454545454546,
                            0.8876352941176471,
                            0.09635454545454546,
                            0.8876352941176471,
                            0.10547272727272726,
                            0.8269647058823529,
                            0.10547272727272726,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "TO",
                    "boundingBoxes": [
                        [
                            0.8910117647058824,
                            0.09635454545454546,
                            0.9028117647058823,
                            0.09635454545454546,
                            0.9028117647058823,
                            0.10547272727272726,
                            0.8910117647058824,
                            0.10547272727272726,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "APPLY",
                    "boundingBoxes": [
                        [
                            0.9067411764705883,
                            0.09635454545454546,
                            0.938764705882353,
                            0.09635454545454546,
                            0.938764705882353,
                            0.10547272727272726,
                            0.9067411764705883,
                            0.10547272727272726,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "table_premises_information_table_2_col_forms_and_conditions_to_apply_1_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7361111111111112,
                            0.10795454545454541,
                            0.9710960430318819,
                            0.10795454545454541,
                            0.9710960430318819,
                            0.13754719835480156,
                            0.7361111111111112,
                            0.13754719835480156,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_forms_and_conditions_to_apply_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7361111111111112,
                            0.13762626262626265,
                            0.9720373397381518,
                            0.13762626262626265,
                            0.9720373397381518,
                            0.16849276239267486,
                            0.7361111111111112,
                            0.16849276239267486,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_forms_and_conditions_to_apply_3_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7361111111111112,
                            0.16856060606060608,
                            0.9714442462850142,
                            0.16856060606060608,
                            0.9714442462850142,
                            0.19830883995410353,
                            0.7361111111111112,
                            0.19830883995410353,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_forms_and_conditions_to_apply_4_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7361111111111112,
                            0.1982323232323232,
                            0.9708281943756262,
                            0.1982323232323232,
                            0.9708281943756262,
                            0.2291952685743619,
                            0.7361111111111112,
                            0.2291952685743619,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "table_premises_information_table_2_col_forms_and_conditions_to_apply_5_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7361111111111112,
                            0.22853535353535348,
                            0.9705220816256196,
                            0.22853535353535348,
                            0.9705220816256196,
                            0.2593218070782689,
                            0.7361111111111112,
                            0.2593218070782689,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "street_address_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "STREET",
                    "boundingBoxes": [
                        [
                            0.34550588235294116,
                            0.21788181818181818,
                            0.3837058823529412,
                            0.21744545454545455,
                            0.3837058823529412,
                            0.22699999999999998,
                            0.34607058823529413,
                            0.22612727272727273,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "ADDRESS:",
                    "boundingBoxes": [
                        [
                            0.3865176470588235,
                            0.21744545454545455,
                            0.4393294117647059,
                            0.2170181818181818,
                            0.43988235294117645,
                            0.22786363636363635,
                            0.3865176470588235,
                            0.22699999999999998,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "street_address_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.44132630380140164,
                            0.21396765571224796,
                            0.97194653856364,
                            0.21396765571224796,
                            0.97194653856364,
                            0.2277267613954035,
                            0.44132630380140164,
                            0.2277267613954035,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_description_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "BLDG",
                    "boundingBoxes": [
                        [
                            0.34607058823529413,
                            0.23350909090909092,
                            0.3719058823529412,
                            0.23350909090909092,
                            0.3719058823529412,
                            0.24131818181818182,
                            0.34607058823529413,
                            0.24131818181818182,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "DESCRIPTION:",
                    "boundingBoxes": [
                        [
                            0.37639999999999996,
                            0.23350909090909092,
                            0.4455058823529412,
                            0.2330727272727273,
                            0.4455058823529412,
                            0.24131818181818182,
                            0.37639999999999996,
                            0.24131818181818182,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_description_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.44999485870297656,
                            0.22790404040404044,
                            0.9722222222222222,
                            0.22790404040404044,
                            0.9722222222222222,
                            0.24415883426712137,
                            0.44999485870297656,
                            0.24415883426712137,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "business_income_extra_expense_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "BUSINESS",
                    "boundingBoxes": [
                        [
                            0.21685882352941177,
                            0.4145,
                            0.2651647058823529,
                            0.4140636363636364,
                            0.2651647058823529,
                            0.4236090909090909,
                            0.21685882352941177,
                            0.4236090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "INCOME",
                    "boundingBoxes": [
                        [
                            0.26741176470588235,
                            0.4140636363636364,
                            0.30617647058823527,
                            0.4140636363636364,
                            0.30617647058823527,
                            0.4236090909090909,
                            0.26741176470588235,
                            0.4236090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "EXTRA",
                    "boundingBoxes": [
                        [
                            0.31516470588235296,
                            0.4140636363636364,
                            0.3477529411764706,
                            0.4145,
                            0.3477529411764706,
                            0.4236090909090909,
                            0.31516470588235296,
                            0.4236090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "EXPENSE",
                    "boundingBoxes": [
                        [
                            0.35056470588235294,
                            0.4145,
                            0.39494117647058824,
                            0.4145,
                            0.39494117647058824,
                            0.4236090909090909,
                            0.35056470588235294,
                            0.4236090909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "business_income_extra_expense_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.18894117647058825,
                            0.4106090909090909,
                            0.2118,
                            0.4106090909090909,
                            0.2118,
                            0.4262,
                            0.18894117647058825,
                            0.4262,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "value_reporting_information_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "VALUE",
                    "boundingBoxes": [
                        [
                            0.5814588235294118,
                            0.4145,
                            0.6134823529411765,
                            0.4145,
                            0.6134823529411765,
                            0.4236090909090909,
                            0.5814588235294118,
                            0.4236090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "REPORTING",
                    "boundingBoxes": [
                        [
                            0.6162941176470589,
                            0.4145,
                            0.6730352941176471,
                            0.4145,
                            0.6730352941176471,
                            0.42404545454545456,
                            0.6162941176470589,
                            0.4236090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "INFORMATION",
                    "boundingBoxes": [
                        [
                            0.6752823529411764,
                            0.4145,
                            0.7438235294117647,
                            0.4145,
                            0.7438235294117647,
                            0.42404545454545456,
                            0.6758470588235294,
                            0.42404545454545456,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "value_reporting_information_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.5530705882352942,
                            0.4103818181818182,
                            0.5771294117647059,
                            0.4103818181818182,
                            0.5771294117647059,
                            0.4258545454545455,
                            0.5530705882352942,
                            0.4258545454545455,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "spoilage_coverage_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "SPOILAGE",
                    "boundingBoxes": [
                        [
                            0.039329411764705884,
                            0.44357272727272723,
                            0.08932941176470588,
                            0.44314545454545456,
                            0.08932941176470588,
                            0.45269090909090903,
                            0.039329411764705884,
                            0.45182727272727274,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "spoilage_coverage_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.051741176470588235,
                            0.4778545454545455,
                            0.07728235294117647,
                            0.4778545454545455,
                            0.07728235294117647,
                            0.4949454545454545,
                            0.051741176470588235,
                            0.4949454545454545,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "description_of_property_covered_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "DESCRIPTION",
                    "boundingBoxes": [
                        [
                            0.10505882352941176,
                            0.44488181818181816,
                            0.17135294117647057,
                            0.44444545454545453,
                            0.17135294117647057,
                            0.45442727272727274,
                            0.10505882352941176,
                            0.4539909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "OF",
                    "boundingBoxes": [
                        [
                            0.17471764705882353,
                            0.44444545454545453,
                            0.18819999999999998,
                            0.44444545454545453,
                            0.18819999999999998,
                            0.45442727272727274,
                            0.17471764705882353,
                            0.45442727272727274,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "PROPERTY",
                    "boundingBoxes": [
                        [
                            0.19101176470588235,
                            0.44444545454545453,
                            0.2455058823529412,
                            0.4440090909090909,
                            0.2455058823529412,
                            0.45442727272727274,
                            0.19101176470588235,
                            0.45442727272727274,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "COVERED",
                    "boundingBoxes": [
                        [
                            0.2477529411764706,
                            0.4440090909090909,
                            0.2955058823529412,
                            0.4440090909090909,
                            0.2955058823529412,
                            0.45442727272727274,
                            0.2477529411764706,
                            0.45442727272727274,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "description_of_property_covered_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.10033609559023329,
                            0.4419191909673348,
                            0.5301871314383018,
                            0.4419191909673348,
                            0.5301871314383018,
                            0.501366349574091,
                            0.10033609559023329,
                            0.501366349574091,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "limit_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "LIMIT",
                    "boundingBoxes": [
                        [
                            0.534270588235294,
                            0.44488181818181816,
                            0.5601176470588235,
                            0.44488181818181816,
                            0.5601176470588235,
                            0.45355454545454543,
                            0.534270588235294,
                            0.45355454545454543,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "limit_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5302287581699346,
                            0.44191919191919193,
                            0.660553714673976,
                            0.44191919191919193,
                            0.660553714673976,
                            0.4714613119819324,
                            0.5302287581699346,
                            0.4714613119819324,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "deductible_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "DEDUCTIBLE",
                    "boundingBoxes": [
                        [
                            0.534270588235294,
                            0.4752636363636364,
                            0.5977529411764706,
                            0.4748272727272727,
                            0.5977529411764706,
                            0.4843727272727273,
                            0.534270588235294,
                            0.48394545454545457,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "deductible_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5302287581699346,
                            0.47159090909090906,
                            0.660130102705704,
                            0.47159090909090906,
                            0.660130102705704,
                            0.5011066151607385,
                            0.5302287581699346,
                            0.5011066151607385,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "refrig_maint_agreement_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "REFRIG",
                    "boundingBoxes": [
                        [
                            0.6629176470588236,
                            0.4470454545454546,
                            0.699435294117647,
                            0.4470454545454546,
                            0.7000000000000001,
                            0.4561636363636364,
                            0.6634823529411764,
                            0.4557272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "MAINT",
                    "boundingBoxes": [
                        [
                            0.7028117647058824,
                            0.4470454545454546,
                            0.734835294117647,
                            0.4466181818181818,
                            0.7353882352941177,
                            0.4557272727272727,
                            0.7028117647058824,
                            0.4561636363636364,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "refrig_maint_agreement_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.6874470588235294,
                            0.47843636363636366,
                            0.7128470588235294,
                            0.47843636363636366,
                            0.7128470588235294,
                            0.4946,
                            0.6874470588235294,
                            0.4946,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "breakdown_or_contamination_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "BREAKDOWN",
                    "boundingBoxes": [
                        [
                            0.7696588235294117,
                            0.4605,
                            0.8325882352941176,
                            0.4600727272727273,
                            0.8331411764705882,
                            0.4687545454545454,
                            0.7696588235294117,
                            0.4687545454545454,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "OR",
                    "boundingBoxes": [
                        [
                            0.8370823529411764,
                            0.4600727272727273,
                            0.85,
                            0.4600727272727273,
                            0.8505647058823529,
                            0.4687545454545454,
                            0.8370823529411764,
                            0.4687545454545454,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "CONTAMINATION",
                    "boundingBoxes": [
                        [
                            0.8544941176470588,
                            0.4600727272727273,
                            0.9348352941176471,
                            0.4600727272727273,
                            0.9348352941176471,
                            0.4687545454545454,
                            0.8550588235294118,
                            0.4687545454545454,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "breakdown_or_contamination_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.7416235294117647,
                            0.4555090909090909,
                            0.7650823529411764,
                            0.4555090909090909,
                            0.7650823529411764,
                            0.47145454545454546,
                            0.7416235294117647,
                            0.47145454545454546,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "power_outage_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "POWER",
                    "boundingBoxes": [
                        [
                            0.7691058823529412,
                            0.4752636363636364,
                            0.8056235294117647,
                            0.4752636363636364,
                            0.8061764705882353,
                            0.4843727272727273,
                            0.7691058823529412,
                            0.4843727272727273,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "OUTAGE",
                    "boundingBoxes": [
                        [
                            0.8095529411764706,
                            0.4752636363636364,
                            0.85,
                            0.47569090909090905,
                            0.8505647058823529,
                            0.4843727272727273,
                            0.8101176470588235,
                            0.4843727272727273,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "power_outage_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.7408705882352941,
                            0.4713727272727273,
                            0.7655294117647058,
                            0.4713727272727273,
                            0.7655294117647058,
                            0.4865,
                            0.7408705882352941,
                            0.4865,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "selling_price_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "SELLING",
                    "boundingBoxes": [
                        [
                            0.8932588235294118,
                            0.4713545454545454,
                            0.9337058823529412,
                            0.47091818181818185,
                            0.9337058823529412,
                            0.4791636363636364,
                            0.8938235294117647,
                            0.4787363636363636,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "PRICE",
                    "boundingBoxes": [
                        [
                            0.8932588235294118,
                            0.4804727272727272,
                            0.9219058823529411,
                            0.48003636363636365,
                            0.9224705882352942,
                            0.4878454545454545,
                            0.8932588235294118,
                            0.48740909090909096,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "selling_price_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.8633411764705883,
                            0.47056363636363635,
                            0.8891882352941176,
                            0.47056363636363635,
                            0.8891882352941176,
                            0.4868454545454545,
                            0.8633411764705883,
                            0.4868454545454545,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "sinkhole_coverage_accept_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "ACCEPT",
                    "boundingBoxes": [
                        [
                            0.45224705882352945,
                            0.5060727272727272,
                            0.4932588235294118,
                            0.5052090909090908,
                            0.4932588235294118,
                            0.5143272727272727,
                            0.45224705882352945,
                            0.5143272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "COVERAGE",
                    "boundingBoxes": [
                        [
                            0.4955058823529412,
                            0.5052090909090908,
                            0.5511294117647059,
                            0.5052090909090908,
                            0.5511294117647059,
                            0.5147545454545455,
                            0.4955058823529412,
                            0.5143272727272727,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "sinkhole_coverage_accept_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.42361176470588235,
                            0.5013454545454545,
                            0.44736470588235294,
                            0.5013454545454545,
                            0.44736470588235294,
                            0.5165909090909091,
                            0.42361176470588235,
                            0.5165909090909091,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "sinkhole_coverage_limit_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "REJECT",
                    "boundingBoxes": [
                        [
                            0.6044941176470588,
                            0.5060727272727272,
                            0.6443764705882353,
                            0.5056454545454545,
                            0.6449411764705882,
                            0.5143272727272727,
                            0.6050588235294118,
                            0.5138909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "COVERAGE",
                    "boundingBoxes": [
                        [
                            0.6466235294117647,
                            0.5056454545454545,
                            0.7022470588235294,
                            0.5060727272727272,
                            0.7022470588235294,
                            0.5143272727272727,
                            0.6471882352941176,
                            0.5143272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "LIMIT:",
                    "boundingBoxes": [
                        [
                            0.7342705882352941,
                            0.5056454545454545,
                            0.7662941176470588,
                            0.5060727272727272,
                            0.7662941176470588,
                            0.515190909090909,
                            0.7342705882352941,
                            0.5147545454545455,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "sinkhole_coverage_limit_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7676763185777904,
                            0.5012268228528951,
                            0.9712097233023675,
                            0.5012268228528951,
                            0.9712097233023675,
                            0.516308495471568,
                            0.7676763185777904,
                            0.516308495471568,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "mine_subsidence_coverage_accept_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "ACCEPT",
                    "boundingBoxes": [
                        [
                            0.4516823529411765,
                            0.5208363636363637,
                            0.4932588235294118,
                            0.5204,
                            0.49382352941176466,
                            0.5295181818181818,
                            0.45224705882352945,
                            0.5299454545454545,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "COVERAGE",
                    "boundingBoxes": [
                        [
                            0.4955058823529412,
                            0.5204,
                            0.5511294117647059,
                            0.5204,
                            0.5511294117647059,
                            0.5299454545454545,
                            0.4960705882352941,
                            0.5295181818181818,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "mine_subsidence_coverage_accept_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.4243411764705882,
                            0.5168090909090909,
                            0.4466705882352941,
                            0.5168090909090909,
                            0.4466705882352941,
                            0.5314,
                            0.4243411764705882,
                            0.5314,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "mine_subsidence_coverage_limit_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "REJECT",
                    "boundingBoxes": [
                        [
                            0.6044941176470588,
                            0.5204,
                            0.6443764705882353,
                            0.5208363636363637,
                            0.6449411764705882,
                            0.5295181818181818,
                            0.6044941176470588,
                            0.5299454545454545,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "COVERAGE",
                    "boundingBoxes": [
                        [
                            0.6466235294117647,
                            0.5208363636363637,
                            0.7022470588235294,
                            0.5204,
                            0.7022470588235294,
                            0.5299454545454545,
                            0.6471882352941176,
                            0.5295181818181818,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "LIMIT:",
                    "boundingBoxes": [
                        [
                            0.7342705882352941,
                            0.5212636363636364,
                            0.7657294117647059,
                            0.5212636363636364,
                            0.7657294117647059,
                            0.5299454545454545,
                            0.7342705882352941,
                            0.5299454545454545,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "mine_subsidence_coverage_limit_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7679738562091504,
                            0.5164141414141414,
                            0.9718804612732487,
                            0.5164141414141414,
                            0.9718804612732487,
                            0.5328037067892053,
                            0.7679738562091504,
                            0.5328037067892053,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "designates_historical_landmark_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "PROPERTY",
                    "boundingBoxes": [
                        [
                            0.05730588235294117,
                            0.5355909090909091,
                            0.11180000000000001,
                            0.5351545454545454,
                            0.11180000000000001,
                            0.5451363636363636,
                            0.05787058823529412,
                            0.5447090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "HAS",
                    "boundingBoxes": [
                        [
                            0.11404705882352942,
                            0.5351545454545454,
                            0.1342705882352941,
                            0.5351545454545454,
                            0.1342705882352941,
                            0.5451363636363636,
                            0.11404705882352942,
                            0.5451363636363636,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "BEEN",
                    "boundingBoxes": [
                        [
                            0.13651764705882355,
                            0.5351545454545454,
                            0.16236470588235297,
                            0.5351545454545454,
                            0.16291764705882353,
                            0.5451363636363636,
                            0.13708235294117646,
                            0.5451363636363636,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "DESIGNATED",
                    "boundingBoxes": [
                        [
                            0.16629411764705881,
                            0.5351545454545454,
                            0.22921176470588234,
                            0.5351545454545454,
                            0.2297764705882353,
                            0.5451363636363636,
                            0.16685882352941175,
                            0.5451363636363636,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "AN",
                    "boundingBoxes": [
                        [
                            0.23370588235294118,
                            0.5351545454545454,
                            0.2455058823529412,
                            0.5351545454545454,
                            0.24607058823529412,
                            0.5451363636363636,
                            0.23370588235294118,
                            0.5451363636363636,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "designates_historical_landmark_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.02971764705882353,
                            0.5319090909090909,
                            0.053317647058823525,
                            0.5319090909090909,
                            0.053317647058823525,
                            0.5472636363636364,
                            0.02971764705882353,
                            0.5472636363636364,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "number_of_open_sides_on_structure_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.7337058823529412,
                            0.5355909090909091,
                            0.7404470588235295,
                            0.5355909090909091,
                            0.7404470588235295,
                            0.5447090909090909,
                            0.7342705882352941,
                            0.5442727272727272,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "OF",
                    "boundingBoxes": [
                        [
                            0.7426941176470588,
                            0.5355909090909091,
                            0.7556235294117647,
                            0.5355909090909091,
                            0.7561764705882353,
                            0.5447090909090909,
                            0.7426941176470588,
                            0.5447090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "OPEN",
                    "boundingBoxes": [
                        [
                            0.7584235294117647,
                            0.5355909090909091,
                            0.784270588235294,
                            0.5355909090909091,
                            0.784270588235294,
                            0.5447090909090909,
                            0.7584235294117647,
                            0.5447090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "SIDES",
                    "boundingBoxes": [
                        [
                            0.788764705882353,
                            0.5355909090909091,
                            0.8179764705882353,
                            0.5355909090909091,
                            0.8179764705882353,
                            0.5447090909090909,
                            0.7893294117647058,
                            0.5447090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "ON",
                    "boundingBoxes": [
                        [
                            0.8202235294117647,
                            0.5355909090909091,
                            0.8337058823529412,
                            0.5355909090909091,
                            0.8337058823529412,
                            0.5447090909090909,
                            0.8202235294117647,
                            0.5447090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "STRUCTURE:",
                    "boundingBoxes": [
                        [
                            0.8370823529411764,
                            0.5355909090909091,
                            0.901129411764706,
                            0.5351545454545454,
                            0.901129411764706,
                            0.5442727272727272,
                            0.8376352941176471,
                            0.5447090909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "number_of_open_sides_on_structure_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.9017749838033191,
                            0.5329173286889526,
                            0.9500714231891585,
                            0.5329173286889526,
                            0.9500714231891585,
                            0.5444767831833655,
                            0.9017749838033191,
                            0.5444767831833655,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "policy_inclusions_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "HISTORICAL",
                    "boundingBoxes": [
                        [
                            0.24887058823529415,
                            0.5351545454545454,
                            0.3078705882352941,
                            0.5355909090909091,
                            0.3078705882352941,
                            0.5447090909090909,
                            0.24887058823529415,
                            0.5451363636363636,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "LANDMARK",
                    "boundingBoxes": [
                        [
                            0.31011764705882355,
                            0.5355909090909091,
                            0.36516470588235295,
                            0.5355909090909091,
                            0.36516470588235295,
                            0.5447090909090909,
                            0.3106705882352941,
                            0.5447090909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "policy_inclusions_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.030077264272321703,
                            0.5476365471687069,
                            0.9713473836449279,
                            0.5476365471687069,
                            0.9713473836449279,
                            0.5855002232761661,
                            0.030077264272321703,
                            0.5855002232761661,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "construction_type_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "CONSTRUCTION",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.5876727272727273,
                            0.11123529411764706,
                            0.5881090909090909,
                            0.11123529411764706,
                            0.5972181818181818,
                            0.034270588235294115,
                            0.5972181818181818,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "TYPE",
                    "boundingBoxes": [
                        [
                            0.11629411764705883,
                            0.5881090909090909,
                            0.1404470588235294,
                            0.5881090909090909,
                            0.13988235294117649,
                            0.5972181818181818,
                            0.11572941176470589,
                            0.5972181818181818,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "construction_type_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.03022875816993464,
                            0.5852272727272727,
                            0.2347222706287283,
                            0.5852272727272727,
                            0.2347222706287283,
                            0.6143138893734483,
                            0.03022875816993464,
                            0.6143138893734483,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "distance_to_hydrant_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "HYDRANT",
                    "boundingBoxes": [
                        [
                            0.24607058823529412,
                            0.5937545454545454,
                            0.2938235294117647,
                            0.5937545454545454,
                            0.2938235294117647,
                            0.6011272727272727,
                            0.24607058823529412,
                            0.6015636363636364,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "distance_to_hydrant_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.23637238959223283,
                            0.5994779186199044,
                            0.3072935890146328,
                            0.5994779186199044,
                            0.3072935890146328,
                            0.6153977525637044,
                            0.23637238959223283,
                            0.6153977525637044,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "distance_to_fire_station_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.30718954248366015,
                            0.5997474747474747,
                            0.3591475983898539,
                            0.5997474747474747,
                            0.3591475983898539,
                            0.6153563029660136,
                            0.30718954248366015,
                            0.6153563029660136,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "distance_to_fire_station_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "FIRE",
                    "boundingBoxes": [
                        [
                            0.30674117647058824,
                            0.5937545454545454,
                            0.32752941176470585,
                            0.5933181818181819,
                            0.32752941176470585,
                            0.6015636363636364,
                            0.30674117647058824,
                            0.6011272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "STAT",
                    "boundingBoxes": [
                        [
                            0.3320235294117647,
                            0.5933181818181819,
                            0.3567411764705883,
                            0.5933181818181819,
                            0.3567411764705883,
                            0.6015636363636364,
                            0.3320235294117647,
                            0.6015636363636364,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_district_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "FIRE",
                    "boundingBoxes": [
                        [
                            0.40898823529411765,
                            0.5889727272727273,
                            0.42977647058823526,
                            0.5889727272727273,
                            0.42977647058823526,
                            0.5972181818181818,
                            0.40898823529411765,
                            0.5972181818181818,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "DISTRICT",
                    "boundingBoxes": [
                        [
                            0.43370588235294116,
                            0.5889727272727273,
                            0.4786470588235294,
                            0.5885454545454546,
                            0.4786470588235294,
                            0.5972181818181818,
                            0.43370588235294116,
                            0.5972181818181818,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_district_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.36074031324772254,
                            0.5875978262356467,
                            0.5308224542874785,
                            0.5875978262356467,
                            0.5308224542874785,
                            0.6151267359634194,
                            0.36074031324772254,
                            0.6151267359634194,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "fire_district_code_number_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "CODE",
                    "boundingBoxes": [
                        [
                            0.5365176470588234,
                            0.5889727272727273,
                            0.5634823529411764,
                            0.5889727272727273,
                            0.5634823529411764,
                            0.5972181818181818,
                            0.5365176470588234,
                            0.5967909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "NUMBER",
                    "boundingBoxes": [
                        [
                            0.5662941176470588,
                            0.5889727272727273,
                            0.6078705882352942,
                            0.5889727272727273,
                            0.6073058823529411,
                            0.5972181818181818,
                            0.5662941176470588,
                            0.5972181818181818,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_district_code_number_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5310457516339869,
                            0.5877525252525253,
                            0.6195688562642634,
                            0.5877525252525253,
                            0.6195688562642634,
                            0.6142403522589579,
                            0.5310457516339869,
                            0.6142403522589579,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "fire_protection_class_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "PROT",
                    "boundingBoxes": [
                        [
                            0.6247176470588236,
                            0.5885454545454546,
                            0.6522470588235294,
                            0.5885454545454546,
                            0.6522470588235294,
                            0.5967909090909091,
                            0.6247176470588236,
                            0.5967909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "CL",
                    "boundingBoxes": [
                        [
                            0.6550588235294117,
                            0.5885454545454546,
                            0.6674117647058824,
                            0.5885454545454546,
                            0.6668588235294118,
                            0.5967909090909091,
                            0.6550588235294117,
                            0.5967909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_protection_class_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.619281045751634,
                            0.5877525252525253,
                            0.6761349909443705,
                            0.5877525252525253,
                            0.6761349909443705,
                            0.6145400647345671,
                            0.619281045751634,
                            0.6145400647345671,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "number_of_stories_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.6797764705882353,
                            0.5881090909090909,
                            0.6853882352941176,
                            0.5881090909090909,
                            0.6853882352941176,
                            0.5967909090909091,
                            0.6797764705882353,
                            0.5967909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "STORIES",
                    "boundingBoxes": [
                        [
                            0.688764705882353,
                            0.5881090909090909,
                            0.7297764705882352,
                            0.5881090909090909,
                            0.7297764705882352,
                            0.5967909090909091,
                            0.6882,
                            0.5967909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "number_of_stories_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6764705882352942,
                            0.5877525252525253,
                            0.731505002351235,
                            0.5877525252525253,
                            0.731505002351235,
                            0.6150661557821792,
                            0.6764705882352942,
                            0.6150661557821792,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "number_of_basements_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.7376352941176471,
                            0.5885454545454546,
                            0.7438235294117647,
                            0.5885454545454546,
                            0.7438235294117647,
                            0.5967909090909091,
                            0.7376352941176471,
                            0.5967909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "BASM'TS",
                    "boundingBoxes": [
                        [
                            0.746070588235294,
                            0.5885454545454546,
                            0.7898823529411765,
                            0.5881090909090909,
                            0.7893294117647058,
                            0.5963545454545455,
                            0.746070588235294,
                            0.5967909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "number_of_basements_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7361111111111112,
                            0.5877525252525253,
                            0.7943383653831354,
                            0.5877525252525253,
                            0.7943383653831354,
                            0.6147122399865128,
                            0.7361111111111112,
                            0.6147122399865128,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "year_built_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "YR",
                    "boundingBoxes": [
                        [
                            0.8039294117647059,
                            0.5881090909090909,
                            0.8157294117647059,
                            0.5881090909090909,
                            0.8157294117647059,
                            0.5967909090909091,
                            0.8039294117647059,
                            0.5963545454545455,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "BUILT",
                    "boundingBoxes": [
                        [
                            0.8191058823529411,
                            0.5881090909090909,
                            0.8483176470588235,
                            0.5881090909090909,
                            0.8483176470588235,
                            0.5967909090909091,
                            0.8191058823529411,
                            0.5967909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "year_built_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7941176470588235,
                            0.5877525252525253,
                            0.8596933152115852,
                            0.5877525252525253,
                            0.8596933152115852,
                            0.6152734037706326,
                            0.7941176470588235,
                            0.6152734037706326,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "total_area_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "TOTAL",
                    "boundingBoxes": [
                        [
                            0.8646117647058823,
                            0.5881090909090909,
                            0.8955058823529412,
                            0.5881090909090909,
                            0.8960705882352942,
                            0.5967909090909091,
                            0.8646117647058823,
                            0.5967909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "AREA",
                    "boundingBoxes": [
                        [
                            0.8994352941176471,
                            0.5881090909090909,
                            0.9252823529411764,
                            0.5881090909090909,
                            0.9252823529411764,
                            0.5967909090909091,
                            0.8994352941176471,
                            0.5967909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "total_area_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.8594771241830066,
                            0.5877525252525253,
                            0.9716413639391354,
                            0.5877525252525253,
                            0.9716413639391354,
                            0.614619775499357,
                            0.8594771241830066,
                            0.614619775499357,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_improvements_wiring_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "WIRING,",
                    "boundingBoxes": [
                        [
                            0.05730588235294117,
                            0.6367181818181818,
                            0.09887058823529413,
                            0.6371545454545454,
                            0.09943529411764705,
                            0.6454,
                            0.05787058823529412,
                            0.6454,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.10056470588235294,
                            0.6371545454545454,
                            0.11629411764705883,
                            0.6371545454545454,
                            0.11685882352941176,
                            0.6458363636363637,
                            0.10112941176470588,
                            0.6454,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_wiring_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.029564705882352944,
                            0.6300545454545454,
                            0.053317647058823525,
                            0.6300545454545454,
                            0.053317647058823525,
                            0.6454090909090909,
                            0.029564705882352944,
                            0.6454090909090909,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "building_improvements_wiring_year_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "WIRING,",
                    "boundingBoxes": [
                        [
                            0.05730588235294117,
                            0.6367181818181818,
                            0.09887058823529413,
                            0.6371545454545454,
                            0.09943529411764705,
                            0.6454,
                            0.05787058823529412,
                            0.6454,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.10056470588235294,
                            0.6371545454545454,
                            0.11629411764705883,
                            0.6371545454545454,
                            0.11685882352941176,
                            0.6458363636363637,
                            0.10112941176470588,
                            0.6454,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_wiring_year_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.11764917068548042,
                            0.6326874692566314,
                            0.1697008647414976,
                            0.6326874692566314,
                            0.1697008647414976,
                            0.6456698507814775,
                            0.11764917068548042,
                            0.6456698507814775,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_improvements_roofing_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "ROOFING,",
                    "boundingBoxes": [
                        [
                            0.05730588235294117,
                            0.6519090909090909,
                            0.10730588235294118,
                            0.6519090909090909,
                            0.10787058823529412,
                            0.6610272727272727,
                            0.05787058823529412,
                            0.6610272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.10955294117647059,
                            0.6519090909090909,
                            0.12584705882352942,
                            0.6523454545454546,
                            0.1264,
                            0.6610272727272727,
                            0.11011764705882354,
                            0.6610272727272727,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_roofing_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.02911764705882353,
                            0.6455636363636365,
                            0.05347058823529412,
                            0.6455636363636365,
                            0.05347058823529412,
                            0.6602272727272728,
                            0.02911764705882353,
                            0.6602272727272728,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "building_improvements_roofing_year_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "ROOFING,",
                    "boundingBoxes": [
                        [
                            0.05730588235294117,
                            0.6519090909090909,
                            0.10730588235294118,
                            0.6519090909090909,
                            0.10787058823529412,
                            0.6610272727272727,
                            0.05787058823529412,
                            0.6610272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.10955294117647059,
                            0.6519090909090909,
                            0.12584705882352942,
                            0.6523454545454546,
                            0.1264,
                            0.6610272727272727,
                            0.11011764705882354,
                            0.6610272727272727,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_roofing_year_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.12743554264680712,
                            0.6493633953370094,
                            0.1711079521478919,
                            0.6493633953370094,
                            0.1711079521478919,
                            0.6608675445865985,
                            0.12743554264680712,
                            0.6608675445865985,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_improvements_plumbing_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "PLUMBING,",
                    "boundingBoxes": [
                        [
                            0.19607058823529414,
                            0.6367181818181818,
                            0.25168235294117647,
                            0.6371545454545454,
                            0.25168235294117647,
                            0.6458363636363637,
                            0.19607058823529414,
                            0.6454,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.25392941176470585,
                            0.6371545454545454,
                            0.26910588235294114,
                            0.6375909090909091,
                            0.26910588235294114,
                            0.6458363636363637,
                            0.25392941176470585,
                            0.6458363636363637,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_plumbing_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.17078823529411766,
                            0.6302818181818182,
                            0.19394117647058826,
                            0.6302818181818182,
                            0.19394117647058826,
                            0.6458727272727273,
                            0.17078823529411766,
                            0.6458727272727273,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "building_improvements_plumbing_year_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "PLUMBING,",
                    "boundingBoxes": [
                        [
                            0.19607058823529414,
                            0.6367181818181818,
                            0.25168235294117647,
                            0.6371545454545454,
                            0.25168235294117647,
                            0.6458363636363637,
                            0.19607058823529414,
                            0.6454,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.25392941176470585,
                            0.6371545454545454,
                            0.26910588235294114,
                            0.6375909090909091,
                            0.26910588235294114,
                            0.6458363636363637,
                            0.25392941176470585,
                            0.6458363636363637,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_plumbing_year_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2692509812463234,
                            0.6348355891607582,
                            0.3184568805478352,
                            0.6348355891607582,
                            0.3184568805478352,
                            0.6467693623911728,
                            0.2692509812463234,
                            0.6467693623911728,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_improvements_heating_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "HEATING,",
                    "boundingBoxes": [
                        [
                            0.1966235294117647,
                            0.6519090909090909,
                            0.2438235294117647,
                            0.6523454545454546,
                            0.24437647058823528,
                            0.6610272727272727,
                            0.19718823529411764,
                            0.6610272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.24607058823529412,
                            0.6523454545454546,
                            0.2618,
                            0.6523454545454546,
                            0.26236470588235294,
                            0.6610272727272727,
                            0.24662352941176469,
                            0.6610272727272727,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_heating_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.17063529411764705,
                            0.6460272727272728,
                            0.19454117647058822,
                            0.6460272727272728,
                            0.19454117647058822,
                            0.6605727272727273,
                            0.17063529411764705,
                            0.6605727272727273,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "building_improvements_heating_year_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "HEATING,",
                    "boundingBoxes": [
                        [
                            0.1966235294117647,
                            0.6519090909090909,
                            0.2438235294117647,
                            0.6523454545454546,
                            0.24437647058823528,
                            0.6610272727272727,
                            0.19718823529411764,
                            0.6610272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.24607058823529412,
                            0.6523454545454546,
                            0.2618,
                            0.6523454545454546,
                            0.26236470588235294,
                            0.6610272727272727,
                            0.24662352941176469,
                            0.6610272727272727,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_heating_year_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2640995657416401,
                            0.6500129216397736,
                            0.319099819362742,
                            0.6500129216397736,
                            0.319099819362742,
                            0.661368433208793,
                            0.2640995657416401,
                            0.661368433208793,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_improvements_other_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "OTHER:",
                    "boundingBoxes": [
                        [
                            0.05787058823529412,
                            0.6662363636363636,
                            0.0966235294117647,
                            0.6666636363636363,
                            0.0966235294117647,
                            0.6749090909090909,
                            0.05787058823529412,
                            0.6749090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.2297764705882353,
                            0.6666636363636363,
                            0.24607058823529412,
                            0.6666636363636363,
                            0.24607058823529412,
                            0.6744818181818182,
                            0.2297764705882353,
                            0.6744818181818182,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_other_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.028823529411764706,
                            0.6608363636363636,
                            0.0536235294117647,
                            0.6608363636363636,
                            0.0536235294117647,
                            0.6763090909090909,
                            0.028823529411764706,
                            0.6763090909090909,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "building_improvements_other_year_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "OTHER:",
                    "boundingBoxes": [
                        [
                            0.05787058823529412,
                            0.6662363636363636,
                            0.0966235294117647,
                            0.6666636363636363,
                            0.0966235294117647,
                            0.6749090909090909,
                            0.05787058823529412,
                            0.6749090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.2297764705882353,
                            0.6666636363636363,
                            0.24607058823529412,
                            0.6666636363636363,
                            0.24607058823529412,
                            0.6744818181818182,
                            0.2297764705882353,
                            0.6744818181818182,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_other_year_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.24719607189890697,
                            0.6645000051638139,
                            0.31867295014956615,
                            0.6645000051638139,
                            0.31867295014956615,
                            0.6758331192741172,
                            0.24719607189890697,
                            0.6758331192741172,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_code_grade_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "BLDG",
                    "boundingBoxes": [
                        [
                            0.3230352941176471,
                            0.6171909090909091,
                            0.3488705882352941,
                            0.6167545454545454,
                            0.3488705882352941,
                            0.6245636363636363,
                            0.3236,
                            0.6245636363636363,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "CODE",
                    "boundingBoxes": [
                        [
                            0.3539294117647059,
                            0.6167545454545454,
                            0.38034117647058824,
                            0.6167545454545454,
                            0.38034117647058824,
                            0.6245636363636363,
                            0.3539294117647059,
                            0.6245636363636363,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "GRADE",
                    "boundingBoxes": [
                        [
                            0.33538823529411765,
                            0.6241363636363636,
                            0.36854117647058826,
                            0.6241363636363636,
                            0.36854117647058826,
                            0.6315090909090909,
                            0.33538823529411765,
                            0.6310727272727272,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_code_grade_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3186915852459181,
                            0.616917240597701,
                            0.3880247931647385,
                            0.616917240597701,
                            0.3880247931647385,
                            0.6447493545563348,
                            0.3186915852459181,
                            0.6447493545563348,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "tax_code_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "TAX",
                    "boundingBoxes": [
                        [
                            0.3983176470588235,
                            0.6184909090909091,
                            0.41741176470588237,
                            0.6189272727272728,
                            0.41741176470588237,
                            0.6271727272727273,
                            0.3983176470588235,
                            0.6271727272727273,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "CODE",
                    "boundingBoxes": [
                        [
                            0.42022352941176466,
                            0.6189272727272728,
                            0.44718823529411766,
                            0.6193545454545455,
                            0.4477529411764706,
                            0.6271727272727273,
                            0.42022352941176466,
                            0.6271727272727273,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "tax_code_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.3880718954248366,
                            0.6167929292929293,
                            0.4595172834712982,
                            0.6167929292929293,
                            0.4595172834712982,
                            0.6448565348453095,
                            0.3880718954248366,
                            0.6448565348453095,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "roof_type_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "ROOF",
                    "boundingBoxes": [
                        [
                            0.46348235294117646,
                            0.6193545454545455,
                            0.4915764705882353,
                            0.6189272727272728,
                            0.4915764705882353,
                            0.6271727272727273,
                            0.46348235294117646,
                            0.6267363636363636,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "TYPE",
                    "boundingBoxes": [
                        [
                            0.4955058823529412,
                            0.6189272727272728,
                            0.5191058823529412,
                            0.6189272727272728,
                            0.5191058823529412,
                            0.6271727272727273,
                            0.4955058823529412,
                            0.6271727272727273,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "roof_type_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.4591503267973856,
                            0.6167929292929293,
                            0.5830349838074547,
                            0.6167929292929293,
                            0.5830349838074547,
                            0.6452562883555397,
                            0.4591503267973856,
                            0.6452562883555397,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "other_occupancies_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "OTHER",
                    "boundingBoxes": [
                        [
                            0.5876352941176471,
                            0.6184909090909091,
                            0.6202235294117646,
                            0.6189272727272728,
                            0.6202235294117646,
                            0.6271727272727273,
                            0.5876352941176471,
                            0.6271727272727273,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "OCCUPANCIES",
                    "boundingBoxes": [
                        [
                            0.6241529411764706,
                            0.6189272727272728,
                            0.6949411764705883,
                            0.6189272727272728,
                            0.6949411764705883,
                            0.6271727272727273,
                            0.6241529411764706,
                            0.6271727272727273,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "other_occupancies_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5833333333333334,
                            0.6167929292929293,
                            0.9705511973237725,
                            0.6167929292929293,
                            0.9705511973237725,
                            0.6457081836279739,
                            0.5833333333333334,
                            0.6457081836279739,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "wind_class_resistive_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "WIND",
                    "boundingBoxes": [
                        [
                            0.3219058823529412,
                            0.6484363636363636,
                            0.3477529411764706,
                            0.6484363636363636,
                            0.3477529411764706,
                            0.6571181818181818,
                            0.3224705882352941,
                            0.6575545454545455,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "CLASS",
                    "boundingBoxes": [
                        [
                            0.35112941176470586,
                            0.6484363636363636,
                            0.3837058823529412,
                            0.6484363636363636,
                            0.38427058823529414,
                            0.6571181818181818,
                            0.3516823529411765,
                            0.6571181818181818,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "RESISTIVE",
                    "boundingBoxes": [
                        [
                            0.34607058823529413,
                            0.6666636363636363,
                            0.3960705882352941,
                            0.6662363636363636,
                            0.3966235294117647,
                            0.6753454545454546,
                            0.34607058823529413,
                            0.6749090909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "wind_class_resistive_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.31750588235294114,
                            0.6603727272727272,
                            0.3421647058823529,
                            0.6603727272727272,
                            0.3421647058823529,
                            0.6764272727272727,
                            0.31750588235294114,
                            0.6764272727272727,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "wind_class_semi_resistive_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "SEMI-",
                    "boundingBoxes": [
                        [
                            0.4404470588235294,
                            0.6523454545454546,
                            0.46797647058823527,
                            0.6523454545454546,
                            0.46797647058823527,
                            0.6605909090909091,
                            0.4404470588235294,
                            0.6605909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "RESISTIVE",
                    "boundingBoxes": [
                        [
                            0.4702235294117647,
                            0.6523454545454546,
                            0.5213529411764706,
                            0.6519090909090909,
                            0.5213529411764706,
                            0.6605909090909091,
                            0.4702235294117647,
                            0.6605909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "wind_class_semi_resistive_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.4123058823529412,
                            0.6453272727272727,
                            0.4359058823529412,
                            0.6453272727272727,
                            0.4359058823529412,
                            0.6608090909090909,
                            0.4123058823529412,
                            0.6608090909090909,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "heating_source_incl_woodburning_stove_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "HEATING",
                    "boundingBoxes": [
                        [
                            0.6101176470588235,
                            0.6466999999999999,
                            0.6522470588235294,
                            0.6466999999999999,
                            0.6528117647058823,
                            0.6549454545454545,
                            0.6106705882352941,
                            0.6549454545454545,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "SOURCE",
                    "boundingBoxes": [
                        [
                            0.6573058823529412,
                            0.6466999999999999,
                            0.6983176470588235,
                            0.6466999999999999,
                            0.6983176470588235,
                            0.6549454545454545,
                            0.6578705882352941,
                            0.6549454545454545,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "INCL",
                    "boundingBoxes": [
                        [
                            0.7016823529411764,
                            0.6466999999999999,
                            0.7241529411764707,
                            0.6466999999999999,
                            0.7241529411764707,
                            0.6549454545454545,
                            0.7016823529411764,
                            0.6549454545454545,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "WOODBURNING",
                    "boundingBoxes": [
                        [
                            0.726964705882353,
                            0.6466999999999999,
                            0.8028117647058823,
                            0.6466999999999999,
                            0.8028117647058823,
                            0.6549454545454545,
                            0.726964705882353,
                            0.6549454545454545,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "heating_source_incl_woodburning_stove_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.5828352941176471,
                            0.6454454545454545,
                            0.6064470588235293,
                            0.6454454545454545,
                            0.6064470588235293,
                            0.6605727272727273,
                            0.5828352941176471,
                            0.6605727272727273,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "heating_source_incl_woodburning_stove_manufacturer_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "MANUFACTURER:",
                    "boundingBoxes": [
                        [
                            0.5865176470588236,
                            0.6640636363636364,
                            0.6719058823529411,
                            0.6645,
                            0.6724705882352942,
                            0.6731727272727273,
                            0.5870823529411764,
                            0.6736090909090909,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "heating_source_incl_woodburning_stove_manufacturer_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6726189616196229,
                            0.6621965675426847,
                            0.9714434008103732,
                            0.6621965675426847,
                            0.9714434008103732,
                            0.6755535489605927,
                            0.6726189616196229,
                            0.6755535489605927,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "heating_source_incl_woodburning_stove_date_installed_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "INSTALLED:",
                    "boundingBoxes": [
                        [
                            0.8286470588235294,
                            0.6549454545454545,
                            0.8865176470588235,
                            0.6545181818181818,
                            0.8865176470588235,
                            0.6627636363636363,
                            0.8286470588235294,
                            0.6623272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "STOVE",
                    "boundingBoxes": [
                        [
                            0.6112352941176471,
                            0.6540818181818181,
                            0.6438235294117647,
                            0.6536454545454545,
                            0.6438235294117647,
                            0.663190909090909,
                            0.6112352941176471,
                            0.663190909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "OR",
                    "boundingBoxes": [
                        [
                            0.6471882352941176,
                            0.6536454545454545,
                            0.6601176470588235,
                            0.6536454545454545,
                            0.6601176470588235,
                            0.663190909090909,
                            0.6471882352941176,
                            0.663190909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "FIREPLACE",
                    "boundingBoxes": [
                        [
                            0.6640470588235294,
                            0.6536454545454545,
                            0.7185411764705882,
                            0.6536454545454545,
                            0.7185411764705882,
                            0.663190909090909,
                            0.6640470588235294,
                            0.663190909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "INSERT",
                    "boundingBoxes": [
                        [
                            0.7207882352941176,
                            0.6536454545454545,
                            0.7573058823529412,
                            0.6540818181818181,
                            0.7573058823529412,
                            0.6627636363636363,
                            0.7207882352941176,
                            0.6627636363636363,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "heating_source_incl_woodburning_stove_date_installed_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.8872043175335136,
                            0.6453057450039699,
                            0.9705882352941176,
                            0.6458333333333333,
                            0.9702087650886625,
                            0.6619951828588665,
                            0.8864683458616108,
                            0.6619951828588665,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "primary_heat_boiler_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "BOILER",
                    "boundingBoxes": [
                        [
                            0.05787058823529412,
                            0.6957454545454546,
                            0.09269411764705883,
                            0.6953090909090909,
                            0.09212941176470589,
                            0.7039909090909091,
                            0.05842352941176471,
                            0.7035636363636364,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "primary_heat_boiler_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.029564705882352944,
                            0.6907,
                            0.0531764705882353,
                            0.6907,
                            0.0531764705882353,
                            0.7065181818181818,
                            0.029564705882352944,
                            0.7065181818181818,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "primary_heat_solid_fuel_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "SOLID",
                    "boundingBoxes": [
                        [
                            0.1584235294117647,
                            0.6953090909090909,
                            0.1859529411764706,
                            0.6953090909090909,
                            0.1865176470588235,
                            0.7035636363636364,
                            0.1584235294117647,
                            0.7031272727272727,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "FUEL",
                    "boundingBoxes": [
                        [
                            0.18988235294117647,
                            0.6948818181818182,
                            0.21516470588235292,
                            0.6944454545454545,
                            0.2157294117647059,
                            0.7039909090909091,
                            0.18988235294117647,
                            0.7035636363636364,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "primary_heat_solid_fuel_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.1282235294117647,
                            0.6904636363636364,
                            0.15436470588235296,
                            0.6904636363636364,
                            0.15436470588235296,
                            0.7065181818181818,
                            0.1282235294117647,
                            0.7065181818181818,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "secondary_heat_boiler_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "BOILER",
                    "boundingBoxes": [
                        [
                            0.5286470588235295,
                            0.6953090909090909,
                            0.5629176470588235,
                            0.6948818181818182,
                            0.5629176470588235,
                            0.7039909090909091,
                            0.5286470588235295,
                            0.7035636363636364,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "secondary_heat_boiler_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.4996588235294117,
                            0.6905818181818182,
                            0.5241529411764706,
                            0.6905818181818182,
                            0.5241529411764706,
                            0.7068727272727272,
                            0.4996588235294117,
                            0.7068727272727272,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "secondary_heat_solid_fuel_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "SOLID",
                    "boundingBoxes": [
                        [
                            0.6286470588235293,
                            0.6953090909090909,
                            0.6567411764705883,
                            0.6953090909090909,
                            0.6567411764705883,
                            0.7035636363636364,
                            0.6286470588235293,
                            0.7035636363636364,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "FUEL",
                    "boundingBoxes": [
                        [
                            0.6601176470588235,
                            0.6953090909090909,
                            0.6848352941176471,
                            0.6948818181818182,
                            0.6848352941176471,
                            0.7039909090909091,
                            0.6601176470588235,
                            0.7035636363636364,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "secondary_heat_solid_fuel_1_checkbox",
            "value": [
                {
                    "page": 1,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.5990588235294118,
                            0.6905818181818182,
                            0.6241529411764706,
                            0.6905818181818182,
                            0.6241529411764706,
                            0.7066363636363636,
                            0.5990588235294118,
                            0.7066363636363636,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "burglar_alarm_type_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "BURGLAR",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.7556454545454545,
                            0.08145882352941176,
                            0.7556454545454545,
                            0.08145882352941176,
                            0.7638909090909091,
                            0.034270588235294115,
                            0.7638909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "ALARM",
                    "boundingBoxes": [
                        [
                            0.08538823529411765,
                            0.7556454545454545,
                            0.11797647058823528,
                            0.7556454545454545,
                            0.11797647058823528,
                            0.7638909090909091,
                            0.08538823529411765,
                            0.7638909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "TYPE",
                    "boundingBoxes": [
                        [
                            0.12415294117647058,
                            0.7556454545454545,
                            0.14718823529411765,
                            0.7552090909090908,
                            0.14718823529411765,
                            0.7638909090909091,
                            0.12415294117647058,
                            0.7638909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "burglar_alarm_type_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029204339508910566,
                            0.7510115750242026,
                            0.35319635120711085,
                            0.7510115750242026,
                            0.35319635120711085,
                            0.7816160689897773,
                            0.029204339508910566,
                            0.7816160689897773,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_certificate_number_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "CERTIFICATE",
                    "boundingBoxes": [
                        [
                            0.3578705882352941,
                            0.7552090909090908,
                            0.4213529411764706,
                            0.7556454545454545,
                            0.4213529411764706,
                            0.7638909090909091,
                            0.3578705882352941,
                            0.7638909090909091,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.4236,
                            0.7556454545454545,
                            0.4303411764705882,
                            0.7556454545454545,
                            0.4308941176470588,
                            0.7638909090909091,
                            0.4236,
                            0.7638909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "burglar_alarm_certificate_number_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.36069098574312536,
                            0.7561151223781893,
                            0.6996770753196789,
                            0.7561151223781893,
                            0.6996770753196789,
                            0.7781805857711749,
                            0.36069098574312536,
                            0.7781805857711749,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_expiration_date_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "EXPIRATION",
                    "boundingBoxes": [
                        [
                            0.7162941176470587,
                            0.7560727272727273,
                            0.7736,
                            0.7560727272727273,
                            0.7736,
                            0.7638909090909091,
                            0.7168588235294118,
                            0.7634545454545454,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "DATE",
                    "boundingBoxes": [
                        [
                            0.7780941176470588,
                            0.7556454545454545,
                            0.8033764705882354,
                            0.7556454545454545,
                            0.8033764705882354,
                            0.7638909090909091,
                            0.7780941176470588,
                            0.7638909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "burglar_alarm_expiration_date_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7124183006535948,
                            0.7512626262626263,
                            0.8130285747749381,
                            0.7512626262626263,
                            0.8130285747749381,
                            0.7817575093612612,
                            0.7124183006535948,
                            0.7817575093612612,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_serviced_by_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "BURGLAR",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.7855909090909091,
                            0.08145882352941176,
                            0.7855909090909091,
                            0.08202352941176472,
                            0.7947090909090909,
                            0.034835294117647055,
                            0.7947090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "ALARM",
                    "boundingBoxes": [
                        [
                            0.08483529411764705,
                            0.7855909090909091,
                            0.11741176470588235,
                            0.7855909090909091,
                            0.11797647058823528,
                            0.7947090909090909,
                            0.08538823529411765,
                            0.7947090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "INSTALLED",
                    "boundingBoxes": [
                        [
                            0.1213529411764706,
                            0.7855909090909091,
                            0.1758470588235294,
                            0.7855909090909091,
                            0.1758470588235294,
                            0.7947090909090909,
                            0.12190588235294118,
                            0.7947090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "AND",
                    "boundingBoxes": [
                        [
                            0.1797764705882353,
                            0.7855909090909091,
                            0.1988705882352941,
                            0.7855909090909091,
                            0.19943529411764707,
                            0.7947090909090909,
                            0.1797764705882353,
                            0.7947090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "SERVICED",
                    "boundingBoxes": [
                        [
                            0.2039294117647059,
                            0.7855909090909091,
                            0.2511294117647059,
                            0.7855909090909091,
                            0.25168235294117647,
                            0.7947090909090909,
                            0.2039294117647059,
                            0.7947090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "BY",
                    "boundingBoxes": [
                        [
                            0.2550588235294118,
                            0.7855909090909091,
                            0.26910588235294114,
                            0.7851545454545454,
                            0.26910588235294114,
                            0.7947090909090909,
                            0.2556235294117647,
                            0.7947090909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "burglar_alarm_serviced_by_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029411764705882353,
                            0.7815656565656566,
                            0.5012242962190838,
                            0.7815656565656566,
                            0.5012242962190838,
                            0.8116307832931278,
                            0.029411764705882353,
                            0.8116307832931278,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_extent_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "EXTENT",
                    "boundingBoxes": [
                        [
                            0.5044941176470588,
                            0.7864545454545454,
                            0.5432588235294118,
                            0.7864545454545454,
                            0.5438235294117647,
                            0.7938363636363637,
                            0.5050588235294118,
                            0.7938363636363637,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "burglar_alarm_extent_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5008169934640523,
                            0.7815656565656566,
                            0.6365775513380532,
                            0.7815656565656566,
                            0.6365775513380532,
                            0.8119243387811129,
                            0.5008169934640523,
                            0.8119243387811129,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_grade_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "GRADE",
                    "boundingBoxes": [
                        [
                            0.6404470588235295,
                            0.7860272727272727,
                            0.6747176470588235,
                            0.7860272727272727,
                            0.6747176470588235,
                            0.7942727272727272,
                            0.6404470588235295,
                            0.7942727272727272,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "burglar_alarm_grade_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.636437908496732,
                            0.7815656565656566,
                            0.7128674422744186,
                            0.7815656565656566,
                            0.7128674422744186,
                            0.8123459912093096,
                            0.636437908496732,
                            0.8123459912093096,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "number_of_guards_and_watchmen_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.715729411764706,
                            0.7860272727272727,
                            0.7219058823529412,
                            0.7860272727272727,
                            0.7224705882352941,
                            0.7947090909090909,
                            0.7162941176470587,
                            0.7947090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "GUARDS",
                    "boundingBoxes": [
                        [
                            0.7247176470588235,
                            0.7860272727272727,
                            0.7668588235294118,
                            0.7860272727272727,
                            0.7668588235294118,
                            0.7942727272727272,
                            0.7252823529411765,
                            0.7947090909090909,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "/",
                    "boundingBoxes": [
                        [
                            0.7685411764705883,
                            0.7860272727272727,
                            0.7724705882352941,
                            0.7860272727272727,
                            0.773035294117647,
                            0.7942727272727272,
                            0.7691058823529412,
                            0.7942727272727272,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "WATCHMEN",
                    "boundingBoxes": [
                        [
                            0.7747176470588235,
                            0.7860272727272727,
                            0.8303411764705882,
                            0.7855909090909091,
                            0.8308941176470588,
                            0.7947090909090909,
                            0.7752823529411765,
                            0.7942727272727272,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "number_of_guards_and_watchmen_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7124183006535948,
                            0.7815656565656566,
                            0.8417728374396444,
                            0.7815656565656566,
                            0.8417728374396444,
                            0.811846946879735,
                            0.7124183006535948,
                            0.811846946879735,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "premises_fire_protection_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "PREMISES",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.8142363636363636,
                            0.08427058823529412,
                            0.8142363636363636,
                            0.08427058823529412,
                            0.8233545454545456,
                            0.034270588235294115,
                            0.8229181818181818,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "FIRE",
                    "boundingBoxes": [
                        [
                            0.08651764705882353,
                            0.8142363636363636,
                            0.10787058823529412,
                            0.8142363636363636,
                            0.1084235294117647,
                            0.8233545454545456,
                            0.08651764705882353,
                            0.8233545454545456,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "PROTECTION",
                    "boundingBoxes": [
                        [
                            0.11123529411764706,
                            0.8142363636363636,
                            0.1736,
                            0.8142363636363636,
                            0.1741529411764706,
                            0.8237818181818182,
                            0.11123529411764706,
                            0.8233545454545456,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "premises_fire_protection_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029411764705882353,
                            0.8118686868686869,
                            0.46636026502838185,
                            0.8118686868686869,
                            0.46636026502838185,
                            0.8422699701800099,
                            0.029411764705882353,
                            0.8422699701800099,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "sprinkler_percentage_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "%",
                    "boundingBoxes": [
                        [
                            0.47359999999999997,
                            0.8155363636363636,
                            0.47921176470588234,
                            0.8155363636363636,
                            0.47921176470588234,
                            0.8233545454545456,
                            0.47415294117647067,
                            0.8233545454545456,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "SPRNK",
                    "boundingBoxes": [
                        [
                            0.4814588235294117,
                            0.8155363636363636,
                            0.5134823529411765,
                            0.8151,
                            0.5134823529411765,
                            0.8237818181818182,
                            0.4814588235294117,
                            0.8233545454545456,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "sprinkler_percentage_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.4665032679738562,
                            0.8118686868686869,
                            0.5247881679193475,
                            0.8118686868686869,
                            0.5247881679193475,
                            0.842400735806476,
                            0.4665032679738562,
                            0.842400735806476,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "fire_alarm_manufacturer_1_label",
            "value": [
                {
                    "page": 1,
                    "text": "FIRE",
                    "boundingBoxes": [
                        [
                            0.5286470588235295,
                            0.8155363636363636,
                            0.5499999999999999,
                            0.8151,
                            0.550564705882353,
                            0.8242181818181818,
                            0.5286470588235295,
                            0.8242181818181818,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "ALARM",
                    "boundingBoxes": [
                        [
                            0.5533764705882354,
                            0.8151,
                            0.5853882352941175,
                            0.8151,
                            0.5859529411764706,
                            0.8242181818181818,
                            0.5533764705882354,
                            0.8242181818181818,
                        ]
                    ],
                },
                {
                    "page": 1,
                    "text": "MANUFACTURER",
                    "boundingBoxes": [
                        [
                            0.5898823529411765,
                            0.8151,
                            0.6719058823529411,
                            0.8151,
                            0.6724705882352942,
                            0.8242181818181818,
                            0.5904470588235293,
                            0.8242181818181818,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_alarm_manufacturer_1_value",
            "value": [
                {
                    "page": 1,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5245098039215687,
                            0.8118686868686869,
                            0.8420871144914872,
                            0.8118686868686869,
                            0.8420871144914872,
                            0.8421552166710703,
                            0.5245098039215687,
                            0.8421552166710703,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "location_number_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "PREMISES",
                    "boundingBoxes": [
                        [
                            0.23034117647058824,
                            0.06788181818181818,
                            0.2803411764705882,
                            0.06788181818181818,
                            0.2803411764705882,
                            0.07656363636363636,
                            0.23034117647058824,
                            0.07569090909090911,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "#:",
                    "boundingBoxes": [
                        [
                            0.28202352941176473,
                            0.06788181818181818,
                            0.2932588235294118,
                            0.06788181818181818,
                            0.2932588235294118,
                            0.07699999999999999,
                            0.28258823529411764,
                            0.07656363636363636,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "location_number_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.29361822130069404,
                            0.06411717299765565,
                            0.33927572845972,
                            0.06411717299765565,
                            0.33927572845972,
                            0.07927369092113526,
                            0.29361822130069404,
                            0.07927369092113526,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_number_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "BUILDING",
                    "boundingBoxes": [
                        [
                            0.22864705882352943,
                            0.0830727272727273,
                            0.2736,
                            0.0830727272727273,
                            0.27415294117647054,
                            0.09175454545454545,
                            0.22921176470588234,
                            0.09131818181818183,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "#:",
                    "boundingBoxes": [
                        [
                            0.27696470588235295,
                            0.0830727272727273,
                            0.2882,
                            0.0830727272727273,
                            0.2882,
                            0.09175454545454545,
                            0.27752941176470586,
                            0.09175454545454545,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_number_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.29330065359477125,
                            0.07916666666666664,
                            0.34119206986014977,
                            0.07916666666666664,
                            0.34119206986014977,
                            0.09390720098149843,
                            0.29330065359477125,
                            0.09390720098149843,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "street_address_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "STREET",
                    "boundingBoxes": [
                        [
                            0.34550588235294116,
                            0.06788181818181818,
                            0.3837058823529412,
                            0.06744545454545456,
                            0.3837058823529412,
                            0.07699999999999999,
                            0.34607058823529413,
                            0.07612727272727274,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "ADDRESS:",
                    "boundingBoxes": [
                        [
                            0.3865176470588235,
                            0.06744545454545456,
                            0.4393294117647059,
                            0.06701818181818181,
                            0.43988235294117645,
                            0.07786363636363636,
                            0.3865176470588235,
                            0.07699999999999999,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "street_address_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.44132630380140164,
                            0.06396765571224797,
                            0.97194653856364,
                            0.06396765571224797,
                            0.97194653856364,
                            0.07772676139540349,
                            0.44132630380140164,
                            0.07772676139540349,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_description_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "BLDG",
                    "boundingBoxes": [
                        [
                            0.34607058823529413,
                            0.08350909090909092,
                            0.3719058823529412,
                            0.08350909090909092,
                            0.3719058823529412,
                            0.09131818181818183,
                            0.34607058823529413,
                            0.09131818181818183,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "DESCRIPTION:",
                    "boundingBoxes": [
                        [
                            0.37639999999999996,
                            0.08350909090909092,
                            0.4455058823529412,
                            0.0830727272727273,
                            0.4455058823529412,
                            0.09131818181818183,
                            0.37639999999999996,
                            0.09131818181818183,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_description_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.44999485870297656,
                            0.07790404040404045,
                            0.9722222222222222,
                            0.07790404040404045,
                            0.9722222222222222,
                            0.09415883426712138,
                            0.44999485870297656,
                            0.09415883426712138,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "construction_type_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "CONSTRUCTION",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.4376727272727273,
                            0.11123529411764706,
                            0.4381090909090909,
                            0.11123529411764706,
                            0.44721818181818174,
                            0.034270588235294115,
                            0.44721818181818174,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "TYPE",
                    "boundingBoxes": [
                        [
                            0.11629411764705883,
                            0.4381090909090909,
                            0.1404470588235294,
                            0.4381090909090909,
                            0.13988235294117649,
                            0.44721818181818174,
                            0.11572941176470589,
                            0.44721818181818174,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "construction_type_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.03022875816993464,
                            0.4352272727272727,
                            0.2347222706287283,
                            0.4352272727272727,
                            0.2347222706287283,
                            0.4643138893734483,
                            0.03022875816993464,
                            0.4643138893734483,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "distance_to_hydrant_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "HYDRANT",
                    "boundingBoxes": [
                        [
                            0.24607058823529412,
                            0.4437545454545454,
                            0.2938235294117647,
                            0.4437545454545454,
                            0.2938235294117647,
                            0.4511272727272727,
                            0.24607058823529412,
                            0.4515636363636364,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "distance_to_hydrant_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.23637238959223283,
                            0.4494779186199044,
                            0.3072935890146328,
                            0.4494779186199044,
                            0.3072935890146328,
                            0.4653977525637044,
                            0.23637238959223283,
                            0.4653977525637044,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "distance_to_fire_station_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.30718954248366015,
                            0.4497474747474747,
                            0.3591475983898539,
                            0.4497474747474747,
                            0.3591475983898539,
                            0.4653563029660136,
                            0.30718954248366015,
                            0.4653563029660136,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "distance_to_fire_station_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "FIRE",
                    "boundingBoxes": [
                        [
                            0.30674117647058824,
                            0.4437545454545454,
                            0.32752941176470585,
                            0.44331818181818183,
                            0.32752941176470585,
                            0.4515636363636364,
                            0.30674117647058824,
                            0.4511272727272727,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "STAT",
                    "boundingBoxes": [
                        [
                            0.3320235294117647,
                            0.44331818181818183,
                            0.3567411764705883,
                            0.44331818181818183,
                            0.3567411764705883,
                            0.4515636363636364,
                            0.3320235294117647,
                            0.4515636363636364,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_district_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "FIRE",
                    "boundingBoxes": [
                        [
                            0.40898823529411765,
                            0.4389727272727273,
                            0.42977647058823526,
                            0.4389727272727273,
                            0.42977647058823526,
                            0.44721818181818174,
                            0.40898823529411765,
                            0.44721818181818174,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "DISTRICT",
                    "boundingBoxes": [
                        [
                            0.43370588235294116,
                            0.4389727272727273,
                            0.4786470588235294,
                            0.43854545454545457,
                            0.4786470588235294,
                            0.44721818181818174,
                            0.43370588235294116,
                            0.44721818181818174,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_district_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.36074031324772254,
                            0.4375978262356467,
                            0.5308224542874785,
                            0.4375978262356467,
                            0.5308224542874785,
                            0.46512673596341936,
                            0.36074031324772254,
                            0.46512673596341936,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "fire_district_code_number_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "CODE",
                    "boundingBoxes": [
                        [
                            0.5365176470588234,
                            0.4389727272727273,
                            0.5634823529411764,
                            0.4389727272727273,
                            0.5634823529411764,
                            0.44721818181818174,
                            0.5365176470588234,
                            0.4467909090909091,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "NUMBER",
                    "boundingBoxes": [
                        [
                            0.5662941176470588,
                            0.4389727272727273,
                            0.6078705882352942,
                            0.4389727272727273,
                            0.6073058823529411,
                            0.44721818181818174,
                            0.5662941176470588,
                            0.44721818181818174,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_district_code_number_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5310457516339869,
                            0.4377525252525253,
                            0.6195688562642634,
                            0.4377525252525253,
                            0.6195688562642634,
                            0.4642403522589579,
                            0.5310457516339869,
                            0.4642403522589579,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "fire_protection_class_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "PROT",
                    "boundingBoxes": [
                        [
                            0.6247176470588236,
                            0.43854545454545457,
                            0.6522470588235294,
                            0.43854545454545457,
                            0.6522470588235294,
                            0.4467909090909091,
                            0.6247176470588236,
                            0.4467909090909091,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "CL",
                    "boundingBoxes": [
                        [
                            0.6550588235294117,
                            0.43854545454545457,
                            0.6674117647058824,
                            0.43854545454545457,
                            0.6668588235294118,
                            0.4467909090909091,
                            0.6550588235294117,
                            0.4467909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_protection_class_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.619281045751634,
                            0.4377525252525253,
                            0.6761349909443705,
                            0.4377525252525253,
                            0.6761349909443705,
                            0.4645400647345671,
                            0.619281045751634,
                            0.4645400647345671,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "number_of_stories_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.6797764705882353,
                            0.4381090909090909,
                            0.6853882352941176,
                            0.4381090909090909,
                            0.6853882352941176,
                            0.4467909090909091,
                            0.6797764705882353,
                            0.4467909090909091,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "STORIES",
                    "boundingBoxes": [
                        [
                            0.688764705882353,
                            0.4381090909090909,
                            0.7297764705882352,
                            0.4381090909090909,
                            0.7297764705882352,
                            0.4467909090909091,
                            0.6882,
                            0.4467909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "number_of_stories_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.6764705882352942,
                            0.4377525252525253,
                            0.735705002351235,
                            0.4377525252525253,
                            0.735705002351235,
                            0.46506615578217914,
                            0.6764705882352942,
                            0.46506615578217914,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "number_of_basements_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.7376352941176471,
                            0.43854545454545457,
                            0.7438235294117647,
                            0.43854545454545457,
                            0.7438235294117647,
                            0.4467909090909091,
                            0.7376352941176471,
                            0.4467909090909091,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "BASM'TS",
                    "boundingBoxes": [
                        [
                            0.746070588235294,
                            0.43854545454545457,
                            0.7898823529411765,
                            0.4381090909090909,
                            0.7893294117647058,
                            0.44635454545454545,
                            0.746070588235294,
                            0.4467909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "number_of_basements_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7361111111111112,
                            0.4377525252525253,
                            0.7943383653831354,
                            0.4377525252525253,
                            0.7943383653831354,
                            0.4647122399865128,
                            0.7361111111111112,
                            0.4647122399865128,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "year_built_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "YR",
                    "boundingBoxes": [
                        [
                            0.8039294117647059,
                            0.4381090909090909,
                            0.8157294117647059,
                            0.4381090909090909,
                            0.8157294117647059,
                            0.4467909090909091,
                            0.8039294117647059,
                            0.44635454545454545,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "BUILT",
                    "boundingBoxes": [
                        [
                            0.8191058823529411,
                            0.4381090909090909,
                            0.8483176470588235,
                            0.4381090909090909,
                            0.8483176470588235,
                            0.4467909090909091,
                            0.8191058823529411,
                            0.4467909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "year_built_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7941176470588235,
                            0.4377525252525253,
                            0.8596933152115852,
                            0.4377525252525253,
                            0.8596933152115852,
                            0.46527340377063253,
                            0.7941176470588235,
                            0.46527340377063253,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "total_area_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "TOTAL",
                    "boundingBoxes": [
                        [
                            0.8646117647058823,
                            0.4381090909090909,
                            0.8955058823529412,
                            0.4381090909090909,
                            0.8960705882352942,
                            0.4467909090909091,
                            0.8646117647058823,
                            0.4467909090909091,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "AREA",
                    "boundingBoxes": [
                        [
                            0.8994352941176471,
                            0.4381090909090909,
                            0.9252823529411764,
                            0.4381090909090909,
                            0.9252823529411764,
                            0.4467909090909091,
                            0.8994352941176471,
                            0.4467909090909091,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "total_area_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.8594771241830066,
                            0.4377525252525253,
                            0.9716413639391354,
                            0.4377525252525253,
                            0.9716413639391354,
                            0.46461977549935696,
                            0.8594771241830066,
                            0.46461977549935696,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_improvements_wiring_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "WIRING,",
                    "boundingBoxes": [
                        [
                            0.05730588235294117,
                            0.4867181818181818,
                            0.09887058823529413,
                            0.4871545454545454,
                            0.09943529411764705,
                            0.49539999999999995,
                            0.05787058823529412,
                            0.49539999999999995,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.10056470588235294,
                            0.4871545454545454,
                            0.11629411764705883,
                            0.4871545454545454,
                            0.11685882352941176,
                            0.49583636363636363,
                            0.10112941176470588,
                            0.49539999999999995,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_wiring_2_checkbox",
            "value": [
                {
                    "page": 2,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.029564705882352944,
                            0.4800545454545454,
                            0.053317647058823525,
                            0.4800545454545454,
                            0.053317647058823525,
                            0.4954090909090909,
                            0.029564705882352944,
                            0.4954090909090909,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "building_improvements_wiring_year_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "WIRING,",
                    "boundingBoxes": [
                        [
                            0.05730588235294117,
                            0.4867181818181818,
                            0.09887058823529413,
                            0.4871545454545454,
                            0.09943529411764705,
                            0.49539999999999995,
                            0.05787058823529412,
                            0.49539999999999995,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.10056470588235294,
                            0.4871545454545454,
                            0.11629411764705883,
                            0.4871545454545454,
                            0.11685882352941176,
                            0.49583636363636363,
                            0.10112941176470588,
                            0.49539999999999995,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_wiring_year_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.11764917068548042,
                            0.4826874692566314,
                            0.1697008647414976,
                            0.4826874692566314,
                            0.1697008647414976,
                            0.4956698507814775,
                            0.11764917068548042,
                            0.4956698507814775,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_improvements_roofing_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "ROOFING,",
                    "boundingBoxes": [
                        [
                            0.05730588235294117,
                            0.5019090909090909,
                            0.10730588235294118,
                            0.5019090909090909,
                            0.10787058823529412,
                            0.5110272727272727,
                            0.05787058823529412,
                            0.5110272727272727,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.10955294117647059,
                            0.5019090909090909,
                            0.12584705882352942,
                            0.5023454545454545,
                            0.1264,
                            0.5110272727272727,
                            0.11011764705882354,
                            0.5110272727272727,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_roofing_2_checkbox",
            "value": [
                {
                    "page": 2,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.02911764705882353,
                            0.49556363636363643,
                            0.05347058823529412,
                            0.49556363636363643,
                            0.05347058823529412,
                            0.5102272727272728,
                            0.02911764705882353,
                            0.5102272727272728,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "building_improvements_roofing_year_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "ROOFING,",
                    "boundingBoxes": [
                        [
                            0.05730588235294117,
                            0.5019090909090909,
                            0.10730588235294118,
                            0.5019090909090909,
                            0.10787058823529412,
                            0.5110272727272727,
                            0.05787058823529412,
                            0.5110272727272727,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.10955294117647059,
                            0.5019090909090909,
                            0.12584705882352942,
                            0.5023454545454545,
                            0.1264,
                            0.5110272727272727,
                            0.11011764705882354,
                            0.5110272727272727,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_roofing_year_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.12743554264680712,
                            0.49936339533700935,
                            0.1711079521478919,
                            0.49936339533700935,
                            0.1711079521478919,
                            0.5108675445865984,
                            0.12743554264680712,
                            0.5108675445865984,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_improvements_plumbing_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "PLUMBING,",
                    "boundingBoxes": [
                        [
                            0.19607058823529414,
                            0.4867181818181818,
                            0.25168235294117647,
                            0.4871545454545454,
                            0.25168235294117647,
                            0.49583636363636363,
                            0.19607058823529414,
                            0.49539999999999995,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.25392941176470585,
                            0.4871545454545454,
                            0.26910588235294114,
                            0.4875909090909091,
                            0.26910588235294114,
                            0.49583636363636363,
                            0.25392941176470585,
                            0.49583636363636363,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_plumbing_2_checkbox",
            "value": [
                {
                    "page": 2,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.17078823529411766,
                            0.48028181818181814,
                            0.19394117647058826,
                            0.48028181818181814,
                            0.19394117647058826,
                            0.49587272727272724,
                            0.17078823529411766,
                            0.49587272727272724,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "building_improvements_plumbing_year_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "PLUMBING,",
                    "boundingBoxes": [
                        [
                            0.19607058823529414,
                            0.4867181818181818,
                            0.25168235294117647,
                            0.4871545454545454,
                            0.25168235294117647,
                            0.49583636363636363,
                            0.19607058823529414,
                            0.49539999999999995,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.25392941176470585,
                            0.4871545454545454,
                            0.26910588235294114,
                            0.4875909090909091,
                            0.26910588235294114,
                            0.49583636363636363,
                            0.25392941176470585,
                            0.49583636363636363,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_plumbing_year_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2692509812463234,
                            0.4848355891607582,
                            0.3184568805478352,
                            0.4848355891607582,
                            0.3184568805478352,
                            0.49676936239117275,
                            0.2692509812463234,
                            0.49676936239117275,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "building_improvements_heating_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "HEATING,",
                    "boundingBoxes": [
                        [
                            0.1966235294117647,
                            0.5019090909090909,
                            0.2438235294117647,
                            0.5023454545454545,
                            0.24437647058823528,
                            0.5110272727272727,
                            0.19718823529411764,
                            0.5110272727272727,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.24607058823529412,
                            0.5023454545454545,
                            0.2618,
                            0.5023454545454545,
                            0.26236470588235294,
                            0.5110272727272727,
                            0.24662352941176469,
                            0.5110272727272727,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_heating_2_checkbox",
            "value": [
                {
                    "page": 2,
                    "text": "unselected",
                    "boundingBoxes": [
                        [
                            0.17063529411764705,
                            0.49602727272727276,
                            0.19454117647058822,
                            0.49602727272727276,
                            0.19454117647058822,
                            0.5105727272727273,
                            0.17063529411764705,
                            0.5105727272727273,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "building_improvements_heating_year_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "HEATING,",
                    "boundingBoxes": [
                        [
                            0.1966235294117647,
                            0.5019090909090909,
                            0.2438235294117647,
                            0.5023454545454545,
                            0.24437647058823528,
                            0.5110272727272727,
                            0.19718823529411764,
                            0.5110272727272727,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "YR:",
                    "boundingBoxes": [
                        [
                            0.24607058823529412,
                            0.5023454545454545,
                            0.2618,
                            0.5023454545454545,
                            0.26236470588235294,
                            0.5110272727272727,
                            0.24662352941176469,
                            0.5110272727272727,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "building_improvements_heating_year_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.2640995657416401,
                            0.5000129216397736,
                            0.319099819362742,
                            0.5000129216397736,
                            0.319099819362742,
                            0.511368433208793,
                            0.2640995657416401,
                            0.511368433208793,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_type_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "BURGLAR",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.6056454545454545,
                            0.08145882352941176,
                            0.6056454545454545,
                            0.08145882352941176,
                            0.613890909090909,
                            0.034270588235294115,
                            0.613890909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "ALARM",
                    "boundingBoxes": [
                        [
                            0.08538823529411765,
                            0.6056454545454545,
                            0.11797647058823528,
                            0.6056454545454545,
                            0.11797647058823528,
                            0.613890909090909,
                            0.08538823529411765,
                            0.613890909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "TYPE",
                    "boundingBoxes": [
                        [
                            0.12415294117647058,
                            0.6056454545454545,
                            0.14718823529411765,
                            0.6052090909090908,
                            0.14718823529411765,
                            0.613890909090909,
                            0.12415294117647058,
                            0.613890909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "burglar_alarm_type_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029204339508910566,
                            0.6010115750242025,
                            0.35319635120711085,
                            0.6010115750242025,
                            0.35319635120711085,
                            0.6316160689897773,
                            0.029204339508910566,
                            0.6316160689897773,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_certificate_number_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "CERTIFICATE",
                    "boundingBoxes": [
                        [
                            0.3578705882352941,
                            0.6052090909090908,
                            0.4213529411764706,
                            0.6056454545454545,
                            0.4213529411764706,
                            0.613890909090909,
                            0.3578705882352941,
                            0.613890909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.4236,
                            0.6056454545454545,
                            0.4303411764705882,
                            0.6056454545454545,
                            0.4308941176470588,
                            0.613890909090909,
                            0.4236,
                            0.613890909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "burglar_alarm_certificate_number_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.35294117647058826,
                            0.6012626262626263,
                            0.7124322894334056,
                            0.6012626262626263,
                            0.7124322894334056,
                            0.6319843476928859,
                            0.35294117647058826,
                            0.6319843476928859,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_expiration_date_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "EXPIRATION",
                    "boundingBoxes": [
                        [
                            0.7162941176470587,
                            0.6060727272727273,
                            0.7736,
                            0.6060727272727273,
                            0.7736,
                            0.613890909090909,
                            0.7168588235294118,
                            0.6134545454545454,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "DATE",
                    "boundingBoxes": [
                        [
                            0.7780941176470588,
                            0.6056454545454545,
                            0.8033764705882354,
                            0.6056454545454545,
                            0.8033764705882354,
                            0.613890909090909,
                            0.7780941176470588,
                            0.613890909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "burglar_alarm_expiration_date_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7124183006535948,
                            0.6012626262626263,
                            0.8130285747749381,
                            0.6012626262626263,
                            0.8130285747749381,
                            0.6317575093612612,
                            0.7124183006535948,
                            0.6317575093612612,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_serviced_by_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "BURGLAR",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.6355909090909091,
                            0.08145882352941176,
                            0.6355909090909091,
                            0.08202352941176472,
                            0.6447090909090909,
                            0.034835294117647055,
                            0.6447090909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "ALARM",
                    "boundingBoxes": [
                        [
                            0.08483529411764705,
                            0.6355909090909091,
                            0.11741176470588235,
                            0.6355909090909091,
                            0.11797647058823528,
                            0.6447090909090909,
                            0.08538823529411765,
                            0.6447090909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "INSTALLED",
                    "boundingBoxes": [
                        [
                            0.1213529411764706,
                            0.6355909090909091,
                            0.1758470588235294,
                            0.6355909090909091,
                            0.1758470588235294,
                            0.6447090909090909,
                            0.12190588235294118,
                            0.6447090909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "AND",
                    "boundingBoxes": [
                        [
                            0.1797764705882353,
                            0.6355909090909091,
                            0.1988705882352941,
                            0.6355909090909091,
                            0.19943529411764707,
                            0.6447090909090909,
                            0.1797764705882353,
                            0.6447090909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "SERVICED",
                    "boundingBoxes": [
                        [
                            0.2039294117647059,
                            0.6355909090909091,
                            0.2511294117647059,
                            0.6355909090909091,
                            0.25168235294117647,
                            0.6447090909090909,
                            0.2039294117647059,
                            0.6447090909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "BY",
                    "boundingBoxes": [
                        [
                            0.2550588235294118,
                            0.6355909090909091,
                            0.26910588235294114,
                            0.6351545454545454,
                            0.26910588235294114,
                            0.6447090909090909,
                            0.2556235294117647,
                            0.6447090909090909,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "burglar_alarm_serviced_by_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029411764705882353,
                            0.6315656565656566,
                            0.5012242962190838,
                            0.6315656565656566,
                            0.5012242962190838,
                            0.6616307832931277,
                            0.029411764705882353,
                            0.6616307832931277,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_extent_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "EXTENT",
                    "boundingBoxes": [
                        [
                            0.5044941176470588,
                            0.6364545454545454,
                            0.5432588235294118,
                            0.6364545454545454,
                            0.5438235294117647,
                            0.6438363636363637,
                            0.5050588235294118,
                            0.6438363636363637,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "burglar_alarm_extent_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5008169934640523,
                            0.6315656565656566,
                            0.6365775513380532,
                            0.6315656565656566,
                            0.6365775513380532,
                            0.6619243387811129,
                            0.5008169934640523,
                            0.6619243387811129,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "burglar_alarm_grade_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "GRADE",
                    "boundingBoxes": [
                        [
                            0.6404470588235295,
                            0.6360272727272727,
                            0.6747176470588235,
                            0.6360272727272727,
                            0.6747176470588235,
                            0.6442727272727272,
                            0.6404470588235295,
                            0.6442727272727272,
                        ]
                    ],
                }
            ],
        },
        {
            "label": "burglar_alarm_grade_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.636437908496732,
                            0.6315656565656566,
                            0.7128674422744186,
                            0.6315656565656566,
                            0.7128674422744186,
                            0.6623459912093096,
                            0.636437908496732,
                            0.6623459912093096,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "number_of_guards_and_watchmen_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "#",
                    "boundingBoxes": [
                        [
                            0.715729411764706,
                            0.6360272727272727,
                            0.7219058823529412,
                            0.6360272727272727,
                            0.7224705882352941,
                            0.6447090909090909,
                            0.7162941176470587,
                            0.6447090909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "GUARDS",
                    "boundingBoxes": [
                        [
                            0.7247176470588235,
                            0.6360272727272727,
                            0.7668588235294118,
                            0.6360272727272727,
                            0.7668588235294118,
                            0.6442727272727272,
                            0.7252823529411765,
                            0.6447090909090909,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "/",
                    "boundingBoxes": [
                        [
                            0.7685411764705883,
                            0.6360272727272727,
                            0.7724705882352941,
                            0.6360272727272727,
                            0.773035294117647,
                            0.6442727272727272,
                            0.7691058823529412,
                            0.6442727272727272,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "WATCHMEN",
                    "boundingBoxes": [
                        [
                            0.7747176470588235,
                            0.6360272727272727,
                            0.8303411764705882,
                            0.6355909090909091,
                            0.8308941176470588,
                            0.6447090909090909,
                            0.7752823529411765,
                            0.6442727272727272,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "number_of_guards_and_watchmen_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.7124183006535948,
                            0.6315656565656566,
                            0.8417728374396444,
                            0.6315656565656566,
                            0.8417728374396444,
                            0.661846946879735,
                            0.7124183006535948,
                            0.661846946879735,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "premises_fire_protection_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "PREMISES",
                    "boundingBoxes": [
                        [
                            0.034270588235294115,
                            0.6642363636363636,
                            0.08427058823529412,
                            0.6642363636363636,
                            0.08427058823529412,
                            0.6733545454545455,
                            0.034270588235294115,
                            0.6729181818181817,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "FIRE",
                    "boundingBoxes": [
                        [
                            0.08651764705882353,
                            0.6642363636363636,
                            0.10787058823529412,
                            0.6642363636363636,
                            0.1084235294117647,
                            0.6733545454545455,
                            0.08651764705882353,
                            0.6733545454545455,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "PROTECTION",
                    "boundingBoxes": [
                        [
                            0.11123529411764706,
                            0.6642363636363636,
                            0.1736,
                            0.6642363636363636,
                            0.1741529411764706,
                            0.6737818181818181,
                            0.11123529411764706,
                            0.6733545454545455,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "premises_fire_protection_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.029411764705882353,
                            0.6618686868686868,
                            0.46636026502838185,
                            0.6618686868686868,
                            0.46636026502838185,
                            0.6922699701800099,
                            0.029411764705882353,
                            0.6922699701800099,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "sprinkler_percentage_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "%",
                    "boundingBoxes": [
                        [
                            0.47359999999999997,
                            0.6655363636363636,
                            0.47921176470588234,
                            0.6655363636363636,
                            0.47921176470588234,
                            0.6733545454545455,
                            0.47415294117647067,
                            0.6733545454545455,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "SPRNK",
                    "boundingBoxes": [
                        [
                            0.4814588235294117,
                            0.6655363636363636,
                            0.5134823529411765,
                            0.6651,
                            0.5134823529411765,
                            0.6737818181818181,
                            0.4814588235294117,
                            0.6733545454545455,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "sprinkler_percentage_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.4665032679738562,
                            0.6618686868686868,
                            0.5247881679193475,
                            0.6618686868686868,
                            0.5247881679193475,
                            0.692400735806476,
                            0.4665032679738562,
                            0.692400735806476,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
        {
            "label": "fire_alarm_manufacturer_2_label",
            "value": [
                {
                    "page": 2,
                    "text": "FIRE",
                    "boundingBoxes": [
                        [
                            0.5286470588235295,
                            0.6655363636363636,
                            0.5499999999999999,
                            0.6651,
                            0.550564705882353,
                            0.6742181818181818,
                            0.5286470588235295,
                            0.6742181818181818,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "ALARM",
                    "boundingBoxes": [
                        [
                            0.5533764705882354,
                            0.6651,
                            0.5853882352941175,
                            0.6651,
                            0.5859529411764706,
                            0.6742181818181818,
                            0.5533764705882354,
                            0.6742181818181818,
                        ]
                    ],
                },
                {
                    "page": 2,
                    "text": "MANUFACTURER",
                    "boundingBoxes": [
                        [
                            0.5898823529411765,
                            0.6651,
                            0.6719058823529411,
                            0.6651,
                            0.6724705882352942,
                            0.6742181818181818,
                            0.5904470588235293,
                            0.6742181818181818,
                        ]
                    ],
                },
            ],
        },
        {
            "label": "fire_alarm_manufacturer_2_value",
            "value": [
                {
                    "page": 2,
                    "text": "",
                    "boundingBoxes": [
                        [
                            0.5245098039215687,
                            0.6618686868686868,
                            0.8420871144914872,
                            0.6618686868686868,
                            0.8420871144914872,
                            0.6921552166710703,
                            0.5245098039215687,
                            0.6921552166710703,
                        ]
                    ],
                }
            ],
            "labelType": "region",
        },
    ],
}
