import email
import logging  # allow-stdlib-logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from datetime import datetime
from email.message import Message
from io import BytesIO
from typing import List, Optional, Tuple, Union

import chardet
import extract_msg
import outlookmsgfile
from dateutil.parser import parse
from extract_msg import AttachmentBase, MSGFile, SignedAttachment
from extract_msg.enums import AttachmentType, ErrorBehavior
from extract_msg.exceptions import InvalidFileFormatError
from extract_msg.msg_classes import MessageBase
from infrastructure_common.logging import get_logger
from mailparser import MailParser

from file_processing.utils import emails_to_str, extract_email_address, normalize_data

logging.getLogger("extract_msg").setLevel(logging.NOTSET)
logging.getLogger("extract_msg").disabled = True

EXECUTOR_TIMEOUT = 6 * 60  # 6 minutes. Should be plenty. Average lambda execution is less than 30 seconds.

logger = get_logger()


@dataclass
class Attachment:
    name: str
    binary: bytes
    content_id: Optional[str] = None
    content_type: Optional[str] = None


@dataclass
class Email:
    subject: Optional[str]
    body: Optional[str]
    sender: Optional[str]
    to: str
    message_id: str
    date: Optional[datetime]
    cc: Optional[str]
    reply_to: Optional[str]
    attachments: List[Attachment]
    sender_without_email: Optional[str] = None


def get_html_body(msg: MessageBase) -> Optional[bytes]:
    return msg.htmlBody


class EnrichedMailParser(MailParser):
    @property
    def body_html(self):
        html_body = "".join(self.text_html).strip()
        return normalize_data(html_body)


class StandardEmailParser:
    @staticmethod
    def from_bytes(raw_email_binary: bytes) -> Message:
        return email.message_from_bytes(raw_email_binary)  # type: ignore

    @staticmethod
    def attachments(msg: Message) -> List[Attachment]:
        attachments = []
        for part in msg.walk():
            content_disposition = part.get_content_disposition()
            name = part.get_filename()
            binary = part.get_payload(decode=True)
            if name and binary and content_disposition and "attachment" in content_disposition:
                content_id = part.get("content-id")
                attachments.append(
                    Attachment(
                        name=name,
                        binary=binary,
                        content_id=content_id.strip("<>") if content_id else None,
                        content_type=part.get_content_type(),
                    )
                )

        return attachments


class EmailExtractor:
    @staticmethod
    def parse_from_eml_binary(raw_email_binary: bytes) -> Email:
        msg = StandardEmailParser.from_bytes(raw_email_binary)
        attachments = StandardEmailParser.attachments(msg)

        msg = EnrichedMailParser.from_bytes(raw_email_binary)
        email = Email(
            subject=normalize_data(msg.subject),
            body=msg.body_html or msg.body,
            sender=EmailExtractor._convert(msg.from_) or "",
            to=EmailExtractor._convert(msg.to),
            cc=EmailExtractor._convert(msg.cc) or None,
            reply_to=EmailExtractor._convert(msg.reply_to) or None,
            attachments=[
                att for att in attachments if not EmailExtractor._is_inline_image_attachment(msg.body_html, att)
            ],
            message_id=msg.headers.get("Message-ID"),
            date=msg.date,
        )
        if not email.sender:
            email.sender_without_email = msg.from_
        return email

    @staticmethod
    def parse_from_msg_binary(raw_email_binary: bytes) -> Optional[Email]:
        msg_html_body = None
        try:
            try:
                msg = extract_msg.openMsg(raw_email_binary, errorBehavior=ErrorBehavior.RTFDE)
            except InvalidFileFormatError as e:
                logger.warning("Could not extract data from given file", exc_info=e)
                return None

            try:
                # there are instances in which this enters into an infinite loop
                # to prevent that we are going to wrap it with a threadpool executor
                executor = ThreadPoolExecutor(max_workers=1)
                future = executor.submit(get_html_body, msg)
                for future in as_completed([future], timeout=EXECUTOR_TIMEOUT):
                    if result := future.result():
                        msg_html_body = result
            except UnicodeDecodeError as e:
                logger.warning("Failed to read msg body, trying latin-1 encoding", exc_info=e)
                msg = extract_msg.openMsg(
                    raw_email_binary, overrideEncoding="latin-1", errorBehavior=ErrorBehavior.RTFDE
                )
                try:
                    msg_html_body = msg.htmlBody
                except Exception as e:
                    logger.warning("Could not decode html body from MSG file", exc_info=e)
                    msg_html_body = None
            except AttributeError as e:
                logger.warning("Could not decode html body from MSG file", exc_info=e)
                msg_html_body = None
        except Exception:
            eml = outlookmsgfile.load(BytesIO(raw_email_binary))
            return EmailExtractor.parse_from_eml_binary(eml.as_bytes())

        if msg_html_body:
            html_body = EmailExtractor._decode_bytes_with_detected_encoding(msg_html_body)
        else:
            logger.warning("No html body found in MSG file")
            try:
                html_body = msg.body
            except Exception as e:
                logger.warning("Could not fallback to text body from MSG file", exc_info=e)
                html_body = None

        try:
            if isinstance(msg.date, datetime):
                email_date = msg.date
            else:
                email_date = parse(msg.date)
        except Exception as e:
            logger.warning(f"Failed to parse email date. Review {e}")
            email_date = None

        attachments = []
        for att in msg.attachments:
            try:
                if att.hidden or att.isAttachmentContactPhoto:
                    continue
                if att.type == AttachmentType.WEB:
                    logger.warning(
                        "Skipped WEB type attachment for raw email",
                        attachment=att,
                        subject=normalize_data(msg.subject),
                    )
                    continue
                name = normalize_data(att.name)
                binary = EmailExtractor._get_data_from_attachment(att)
                if not name or not binary:
                    logger.warning(
                        "Skipped attachment with missing name or binary data for raw email",
                        attachment=att,
                        subject=normalize_data(msg.subject),
                    )
                    continue
                attachments.append(
                    Attachment(
                        name=name,
                        binary=binary,
                    )
                )
            except Exception:
                logger.exception("Could extract attachment from MSG file", attachment=att)

        email = Email(
            subject=normalize_data(msg.subject),
            body=normalize_data(html_body),
            sender=extract_email_address(msg.sender),
            to=extract_email_address(msg.to),
            cc=extract_email_address(msg.cc) or None,
            message_id=msg.headerDict.get("Message-ID"),
            reply_to=None,
            attachments=attachments,
            date=email_date,
        )
        if not email.sender:
            email.sender_without_email = msg.sender
        return email

    @staticmethod
    def _get_data_from_attachment(msg_attachment: Union[AttachmentBase, SignedAttachment]):
        if issubclass(msg_attachment.dataType, bytes):
            return msg_attachment.data
        elif issubclass(msg_attachment.dataType, MSGFile):
            if hasattr(msg_attachment.dataType, "asBytes"):
                return msg_attachment.asBytes
            else:
                return msg_attachment.data.exportBytes()
        return None

    @staticmethod
    def _convert(emails: List[Tuple[str, str]]) -> str:
        return normalize_data(emails_to_str(emails))

    @staticmethod
    def _decode_bytes_with_detected_encoding(text: bytes) -> str:
        encoding = chardet.detect(text).get("encoding", "utf-8") or "utf-8"
        try:
            return text.decode(encoding)
        except UnicodeDecodeError:
            logger.warning("Failed to decode bytes with detected encoding", encoding=encoding)
            return text.decode("utf-8", errors="ignore")

    @staticmethod
    def _is_inline_image_attachment(html_body: str, attachment: Attachment) -> bool:
        return (
            html_body
            and attachment.content_type
            and attachment.content_id
            and attachment.content_type.lower().startswith("image")
            and attachment.content_id in html_body
        )
