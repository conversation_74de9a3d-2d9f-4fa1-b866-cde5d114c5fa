[project]
name = "file-processing"
version = "25.5.27.49583.dev0"
description = ""
authors = [{ name = "Kalepa Tech" }]
requires-python = ">=3.11,<3.12"
readme = "README.md"
dependencies = [
    "en-core-web-sm",
    "numpy>=1.22.4,<2",
    "pandas>=1.5.3,<2",
    "openpyxl>=3.1.2,<4",
    "pyxlsb>=1.0.10,<2",
    "PyMuPDFb!=1.23.7",
    "pymupdf>=1.22.5,!=1.23.7,<2.0.0",
    "python-dateutil>=2.8.2,<3",
    "werkzeug>=2.0.1",
    "extract-msg>=0.52,<0.53",
    "mail-parser>=3.15.0,<4",
    "marshmallow>=3.12.1,<4",
    "marshmallow-enum>=1.5.1,<2",
    "fuzzywuzzy>=0.18.0,<0.19",
    "wrapt-timeout-decorator>23",
    "lxml>=5.0.0,<6",
    "pillow>=10.0.0,<11",
    "opencv-python-headless>=********,<5",
    "img2table[azure]==1.0.11",
    "tiktoken>=0.7.0,<0.8",
    "vininfo>=1.7.0,<2",
    "polars==0.19.19",
    "chardet>=5.2.0,<6",
    "convert-outlook-msg-file",
    "more-itertools>=10.5.0,<11",
    "python-magic>=0.4.27,<0.5",
    "measurement==4.0a8",
    "boto3>1.20.0,<1.30.0",
    "common",
    "xlrd",
    "datascience_common",
    "facts-client",
    "facts-client-v2",
    "infrastructure-common",
    "llm-common",
    "static-common",
    "fuzzysearch>=0.7.3",
]

[dependency-groups]
kalepa = [
    "common",
    "xlrd",
    "datascience_common",
    "facts-client",
    "facts-client-v2",
    "infrastructure-common",
    "llm-common",
    "static-common",
]
dev = [
    "pytest>=7.1.2,<8",
    "pytest-mock>=3.11.1,<4",
    "psycopg2-binary>=2.9.5,<3",
    "sqlalchemy>=1.3.20,<2",
    "azure-storage-blob>=12.19.0,<13",
    "mypy>=1.8.0,<2",
    "mypy-extensions==1.0.0",
    "pandas-stubs>=2.2.3.240218,<3",
    "data-science-types>=0.2.23,<0.3",
    "types-python-dateutil>=2.9.0.20240316,<3",
    "pytest-xdist[psutil]>=3.6.1,<4",
    "freeze-uuid==0.4.1",
]

[tool.uv.sources]
common = { index = "kalepi" }
datascience_common = { index = "kalepi" }
facts-client = { index = "kalepi" }
facts-client-v2 = { index = "kalepi" }
infrastructure-common = { index = "kalepi" }
llm-common = { index = "kalepi" }
static-common = { index = "kalepi" }
xlrd = { index = "kalepi" }
en-core-web-sm = { index = "kalepi" }
wrapt-timeout-decorator = { index = "kalepi" }
convert-outlook-msg-file = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[tool.uv]
package = true
default-groups = ["kalepa", "dev"]
override-dependencies = ["opencv-python; sys_platform == 'never'"]


[tool.hatch.build.targets.sdist]
include = ["file_processing", "file_processing/libmagic/**/lib/**/*"]

[tool.hatch.build.targets.wheel]
include = ["file_processing", "file_processing/libmagic/**/lib/**/*"]

[tool.hatch.build.targets.wheel.force-include]
"file_processing/libmagic" = "file_processing/libmagic"

[tool.hatch.build.targets.sdist.force-include]
"file_processing/libmagic" = "file_processing/libmagic"

[tool.hatch.metadata]
allow-direct-references = true

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 120
preview = true
target-version = ['py38', 'py311']

[tool.isort]
profile = "black"
skip = ["__init__.py"]

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
line-length = 120
target-version = 'py38'
extend-exclude = [
    "tests/",
    "scripts/",
    "migrations/",
    "features/",
    "test_utils/",
    "bin/",
    "**/__init__.py",
]

[tool.mypy]
python_version = "3.11"
strict = true
show_error_codes = true
disallow_untyped_calls = false
disallow_any_generics = false
warn_return_any = true
ignore_missing_imports = true
disable_error_code = "abstract"
exclude = [
    '^scripts/',
    '^test/',
    '^pandas/',
    '^file_processing/acord.py',
    '^file_processing/acord_config.py',
    '^file_processing/additional_sheet_parser.py',
    '^file_processing/ai',
    '^file_processing/ally_auto_questionnaire_parser.py',
    '^file_processing/benchmark',
    '^file_processing/budget_parser.py',
    '^file_processing/constants.py',
    '^file_processing/drivers_parser.py',
    '^file_processing/email_extractor.py',
    '^file_processing/enums.py',
    '^file_processing/exceptions.py',
    '^file_processing/facts_identifiction.py',
    '^file_processing/fields.py',
    '^file_processing/financial_statement_parser',
    '^file_processing/first_party_field_provider.py',
    '^file_processing/fleet_pdf_parser.py',
    '^file_processing/ifta_parsing.py',
    '^file_processing/key_value_parser.py',
    '^file_processing/loss_run',
    '^file_processing/metrics',
    '^file_processing/models',
    '^file_processing/no_claims_loss_runs_parser.py',
    '^file_processing/ocr_utils.py',
    '^file_processing/onboarded_file_processing.py',
    '^file_processing/onboarded_file_provider.py',
    '^file_processing/paragon_property_sov_spreadsheet_parser.py',
    '^file_processing/pdf_parser.py',
    '^file_processing/pdf_utils',
    '^file_processing/prompts.py',
    '^file_processing/schemas',
    '^file_processing/sov_spreadsheet_parser.py',
    '^file_processing/spreadsheet_parser.py',
    '^file_processing/spreadsheets.py',
    '^file_processing/supplemental_parsing.py',
    '^file_processing/supplementals',
    '^file_processing/table_processing.py',
    '^file_processing/templates',
    '^file_processing/utils.py',
    '^file_processing/validators.py',
    '^file_processing/vehicles_parser.py',
    '^file_processing/xmod_config.py',
    '^file_processing/xmod_parsing.py',
    '^file_processing/wc_spreadsheet_parser.py',
    '^file_processing/sov_spreadhseet_parser_v2.py',
    '^file_processing/tables',
    '^file_processing/acords/sensible_format_converter.py',
    '^file_processing/spreadsheet_labels/spreadsheet_labels.py',
    '^file_processing/spreadsheet_labels/spreadsheet_builder.py',
    '^file_processing/columns_matching/address.py',
    '^file_processing/columns_matching/column.py',
    '^file_processing/spreadsheet/sheeet_sumarization_detector.py',
    '^file_processing/spreadsheet/sheet_row.py',
    '^file_processing/spreadsheet/structures.py',
    '^file_processing/spreadsheet/column_names.py',
    '^file_processing/trash_detector/simple_trash_detector.py',
    '^file_processing/column_data_fill/fill_address.py',
    '^file_processing/column_data_fill/fill_basic_columns.py',
    '^file_processing/columns_deduction/column_deductor.py',
    '^file_processing/columns_deduction/choose_best_column.py',
    '^file_processing/equipment_parser.py',
    '^file_processing/header/',
]
