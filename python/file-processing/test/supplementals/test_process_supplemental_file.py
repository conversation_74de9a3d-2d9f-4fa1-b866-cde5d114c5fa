import json
from pathlib import Path
from unittest import mock

import pytest
from facts_client_v2.model.fact_subtype import FactSubtype
from llm_common.clients import LLMClient
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.models.file_onboarding import OnboardedFile
from static_common.schemas.coverages import CoverageDetailsSchema
from static_common.schemas.file_onboarding import OnboardedFileSchema
from werkzeug.datastructures import FileStorage

from file_processing.models.feature_flags import FeatureFlags
from file_processing.pdf_documents.loader.form_recognizer import (
    FormRecognizerPdfDocumentLoader,
)
from file_processing.supplementals.supplemental_parser import SupplementalParser
from file_processing.tables.handler import <PERSON>Handler
from file_processing.tables.loader import TableWrapper
from file_processing.tables.result_processor import TableResultProcessor
from file_processing.utils import get_layout_result

CURRENT_DIR = Path(__file__).parent


@pytest.fixture
def fact_subtypes() -> list[FactSubtype]:
    return pytest.fact_subtypes


@pytest.fixture
def filename() -> str:
    return "14ae9874-a20b-498a-aa2c-a3a0e3d95c9c.pdf"


@pytest.fixture
def fake_azure_layout_client(filename: str) -> mock.MagicMock:
    fake_azure_layout_client = mock.MagicMock()
    with open(CURRENT_DIR / ".." / "data" / "supplementals" / "tables" / f"{filename}.json", "rb") as fp:
        fake_azure_layout_client.model_id = "prebuilt-document"
        fake_azure_layout_client.get_read_api_result.return_value = {
            "status": "succeeded",
            "createdDateTime": "2021-09-29T20:00:00Z",
            "lastUpdatedDateTime": "2021-09-29T20:00:00Z",
            "analyzeResult": json.load(fp),
        }
    return fake_azure_layout_client


def test_process_supplemental_file_generic_parser_v2(
    fact_subtypes: list[FactSubtype], filename: str, fake_azure_layout_client: mock.MagicMock
) -> None:
    # GIVEN
    fp = open(CURRENT_DIR / ".." / "data" / "supplementals" / "tables" / filename, "rb")
    file = FileStorage(fp)

    fake_ff_client = mock.MagicMock()
    ff = {
        FeatureFlags.ENABLE_GENERIC_NAME_TEMPLATES: False,
        FeatureFlags.ENABLE_GENERIC_PARSER_V3: False,
        FeatureFlags.ENABLE_MAILING_ADDRESS: True,
        FeatureFlags.ENABLE_SUPPLEMENTAL_POSTPROCESSING: False,
    }
    fake_ff_client.is_feature_enabled = lambda x: ff[x]
    supplemental_parser = SupplementalParser(
        copilot_client=mock.MagicMock(),
        fact_subtypes=fact_subtypes,
        ff_client=fake_ff_client,
    )

    read_file_results = get_layout_result(file, azure_read_client=fake_azure_layout_client)
    pdf_document = FormRecognizerPdfDocumentLoader().load_pdf_document(
        None, read_file_results, file_for_loading_images=file
    )
    document_tables: list[TableWrapper] = []
    for page in pdf_document.pages:
        document_tables.extend(page.tables)

    # WHEN
    # Run table extraction
    onboarded_data = OnboardedFile(files=["file_id"])
    process_result_kwargs = {
        "onboarded_data": onboarded_data,
    }

    with mock.patch("file_processing.tables.extractor.employees.get_llm_response", return_value={}), mock.patch.object(
        LLMClient, "get_llm_response", return_value={}
    ):
        result_processors: list[TableResultProcessor | None] = TableHandler(
            table_wrapper_list=document_tables,
            file_id=None,
            file=file,
            submission_id=None,
            get_llm_response_wrapper=None,
            parse_res_kwargs=process_result_kwargs,
            filename=file.filename,
            organization_id=2137,
        ).handle_tables()  # The result of this function is updated process_result_kwargs dict

    # Verify that two fields are not extracted
    field_names_extracted = [x.name for x in onboarded_data.fields]
    assert len(field_names_extracted) == 0  # We mock all GPT calls, so no fields are extracted
    assert "Subsidiary Name" not in field_names_extracted
    assert "Shareholders:" not in field_names_extracted

    hashes_of_tables_to_parse: set[str] = set()
    assert len(document_tables) == len(result_processors)
    for processor, table in zip(result_processors, document_tables):
        if processor is not None and not processor.is_data_extracted():
            hashes_of_tables_to_parse.add(table.unique_hash)

    (
        onboarded_data,
        onboarded_data_list,
        template_parsing_results,
    ) = supplemental_parser.parse_and_get_template_matching_results(
        file=file,
        file_id=None,
        classification=ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
        organization_id=2137,
        pdf_document=pdf_document,
        hashes_of_tables_to_parse=hashes_of_tables_to_parse,
    )

    # THEN
    # Verify that two fields are extracted
    field_names_extracted = [x.name for x in onboarded_data.fields]
    assert "Subsidiary Name" in field_names_extracted
    assert "Shareholders:" in field_names_extracted

    fp.close()


def test_postprocess_fields(fact_subtypes: list[FactSubtype], mocker) -> None:
    fake_ff_client = mock.MagicMock()
    ff = {
        FeatureFlags.ENABLE_GENERIC_NAME_TEMPLATES: False,
        FeatureFlags.ENABLE_GENERIC_PARSER_V3: True,
        FeatureFlags.ENABLE_MAILING_ADDRESS: False,
        FeatureFlags.ENABLE_SUPPLEMENTAL_POSTPROCESSING: True,
        FeatureFlags.CROSS_MATCH_LLM_BBOXES: False,
    }
    fake_ff_client.is_feature_enabled = lambda x: ff[x]
    supplemental_parser = SupplementalParser(
        copilot_client=mock.MagicMock(),
        fact_subtypes=fact_subtypes,
        ff_client=fake_ff_client,
    )

    with open(CURRENT_DIR / ".." / "data" / "supplementals" / "generic" / "generic_parser_result.json") as fp:
        generic_results = json.load(fp)
        onboarded_file = OnboardedFileSchema().load(generic_results)

    desc_of_ops = (
        "Tavern with hands-on owners in process of purchasing the business. "
        "Owners have experience running two other businesses and 5+ years of tavern/restaurant experience. "
        "Establishment serves bar food and provides a venue for watching sports, socializing, "
        "and listening to music. Features include 1 juke box, 1 pool table, and weekly karaoke."
    )

    response_1 = {
        "fields": [
            {
                "keys": [
                    "Business type classification: Description of Operations",
                    "Additional risk information section: Any Other Information that is Pertinent to this Risk?",
                    "Business Entity Selection: Insured Type",
                ],
                "cleaned_key": "DESCRIPTION_OF_OPERATIONS",
                "cleaned_value": desc_of_ops,
            }
        ]
    }

    response_2 = {
        "fields": [
            {
                "aggregate_limit": "450000",
                "coverage_name": "Property Liability",
                "coverage_type": "PRIMARY",
                "period": "01/25/2025 to 01/25/2026",
            },
            {
                "aggregate_limit": "100000",
                "coverage_name": "Liquor Liability",
                "coverage_type": "PRIMARY",
                "each_occurrence_limit": "100000",
                "period": "01/25/2025 to 01/25/2026",
            },
            {
                "aggregate_limit": "100000",
                "coverage_name": "GeneralLiability",
                "coverage_type": "EXCESS",
                "each_occurrence_limit": "100000",
                "period": "01/25/2025 to 01/25/2026",
            },
            {
                "aggregate_limit": "100000",
                "coverage_name": "CyberLiability",
                "coverage_type": "PRIMARY",
                "each_occurrence_limit": "100000",
                "period": "01/25/2025 to 01/25/2026",
            },
            {
                "aggregate_limit": "100000",
                "coverage_name": "CyberPrivacy",
                "coverage_type": "PRIMARY",
                "each_occurrence_limit": "100000",
                "period": "01/25/2025 to 01/25/2026",
            },
        ]
    }

    supplemental_parser._generic_supplemental_parser._llm_client = mock.MagicMock(spec=LLMClient)
    supplemental_parser._generic_supplemental_parser._llm_client.get_llm_response.side_effect = [
        response_1,
        response_1,
        response_2,
        response_2,
    ]

    mocker.patch("file_processing.supplementals.supplemental_parser.MAX_WORKERS", 1)
    res_ob, res_obl = supplemental_parser._extract_postprocess_fields(onboarded_file, [], fact_subtypes=fact_subtypes)

    assert len(res_ob.fields) == 81
    assert res_ob.fields[80].values[0].value == desc_of_ops

    coverage_data = CoverageDetailsSchema().loads(res_ob.entity_information[2].values[0].value, many=True)
    assert len(coverage_data) == 5
    assert coverage_data[0].coverage_name.name == "Property"
    assert coverage_data[0].limit == 450000.0
    assert coverage_data[0].period == "01/25/2025 to 01/25/2026"
    assert coverage_data[0].coverage_type == "primary"
    assert coverage_data[1].coverage_name.name == "LiquorLiability"
    assert coverage_data[1].limit == 100000.0
    assert coverage_data[1].period == "01/25/2025 to 01/25/2026"
    assert coverage_data[1].coverage_type == "primary"
    assert coverage_data[2].coverage_name.name == "Liability"
    assert coverage_data[2].period == "01/25/2025 to 01/25/2026"
    assert coverage_data[2].coverage_type == "excess"
    assert coverage_data[3].coverage_name.name == "CyberPrivacy"
    assert coverage_data[3].period == "01/25/2025 to 01/25/2026"
    assert coverage_data[3].coverage_type == "primary"
    assert coverage_data[4].coverage_name.name == "CyberPrivacy"
    assert coverage_data[4].period == "01/25/2025 to 01/25/2026"
    assert coverage_data[4].coverage_type == "primary"
