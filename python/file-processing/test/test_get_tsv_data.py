from pathlib import Path

import pytest
from werkzeug.datastructures import FileStorage

from file_processing.sov_spreadhseet_parser_v2 import SOVSpreadsheetParserV2

CURRENT_DIR = Path(__file__).parent


@pytest.fixture
def sov_spreadsheet_parser() -> SOVSpreadsheetParserV2:
    return SOVSpreadsheetParserV2()


@pytest.mark.parametrize(
    "file_name, expected_lines, expected_fields, expected_hidden_columns, expected_hidden_rows",
    [
        (CURRENT_DIR / "data/part_of_la_apartments.xlsx", 4, 31, [], []),
        (CURRENT_DIR / "data/standard_spreadsheet.xlsx", 3, 6, [], []),
        (CURRENT_DIR / "data/sov/real_life_examples/sov_three_headers.xlsx", 35, 24, [], []),
        (CURRENT_DIR / "data/hidden_rows_and_columns.xlsx", 30, 26, [8, 12, 13, 14], [6, 20, 21, 22, 23, 24]),
    ],
)
def test_get_tsv_data_with_metadata(
    sov_spreadsheet_parser: SOVSpreadsheetParserV2,
    file_name: str,
    expected_lines: int,
    expected_fields: int,
    expected_hidden_columns: list[int],
    expected_hidden_rows: list[int],
) -> None:
    with open(file_name, "rb") as fp:
        file = FileStorage(fp)
        tsv, metadata = sov_spreadsheet_parser.get_tsv_data_with_metadata(file)
        tsv_lines = tsv.splitlines()
        assert len(tsv_lines) == expected_lines
        assert metadata.hidden_columns == expected_hidden_columns
        assert metadata.hidden_rows == expected_hidden_rows
        for line in tsv_lines:
            assert len(line.split("\t")) == expected_fields


def test_get_all_sheets_tsv_data_with_metadata(
    sov_spreadsheet_parser: SOVSpreadsheetParserV2,
) -> None:
    file_name = CURRENT_DIR / "data/sov/test-gc-with-multiple-sheets.xlsx"
    with open(file_name, "rb") as fp:
        file = FileStorage(fp)
        result = sov_spreadsheet_parser.get_all_sheets_tsv_data_with_metadata(file)
        assert len(result) == 2
        for key, value in result.items():
            tsv, metadata = value
            if key == "Sheet1":
                tsv_lines = tsv.splitlines()
                assert len(tsv_lines) == 5
                for line in tsv_lines:
                    assert len(line.split("\t")) == 17
            elif key == "Sheet2":
                tsv_lines = tsv.splitlines()
                assert len(tsv_lines) == 2
                for line in tsv_lines:
                    assert len(line.split("\t")) == 2
            else:
                assert False, f"Unexpected sheet name: {key}"
