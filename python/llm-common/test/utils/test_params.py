from typing import Type

import pytest
from pydantic.main import BaseModel

from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import (
    ClaudeRequestParams,
    GPTRequestParams,
    LLMRequestParams,
)
from llm_common.utils.fallback_strategy import FallbackStrategy
from llm_common.utils.params import add_fallback_models_if_missing


class OutputModel(BaseModel):
    pass


@pytest.mark.parametrize(
    ["input_params", "fallback_strategy", "output_model", "expected_models"],
    [
        (
            [GPTRequestParams(model=LLMModel.OPENAI_GPT_4O)],
            FallbackStrategy.IDENTICAL,
            None,
            [LLMModel.OPENAI_GPT_4O, LLMModel.AZURE_GPT_4O],
        ),
        (
            [GPTRequestParams(model=LLMModel.AZURE_GPT_4O)],
            FallbackStrategy.IDENTICAL,
            None,
            [LLMModel.AZURE_GPT_4O, LLMModel.OPENAI_GPT_4O],
        ),
        (
            [ClaudeRequestParams(model=LLMModel.CLAUDE_3_5_SONNET)],
            FallbackStrategy.SIMILAR,
            None,
            [LLMModel.CLAUDE_3_5_SONNET, LLMModel.CLAUDE_3_7_SONNET, LLMModel.CLAUDE_4_SONNET],
        ),
        (
            [ClaudeRequestParams(model=LLMModel.CLAUDE_3_SONNET)],
            FallbackStrategy.SIMILAR,
            None,
            [LLMModel.CLAUDE_4_SONNET],
        ),
        (
            [GPTRequestParams(model=LLMModel.OPENAI_GPT_4O)],
            FallbackStrategy.NO_FALLBACK,
            None,
            [LLMModel.OPENAI_GPT_4O],
        ),
        (
            [GPTRequestParams(model=LLMModel.OPENAI_GPT_4O)],
            FallbackStrategy.SIMILAR,
            OutputModel,
            [
                model
                for model in [LLMModel.OPENAI_GPT_4O, LLMModel.OPENAI_GPT_4_1]
                if model.supports_structured_output()
            ],
        ),
    ],
)
def test_add_fallback_models_if_missing_with_strategies(
    input_params: list[LLMRequestParams],
    fallback_strategy: FallbackStrategy,
    output_model: Type[BaseModel] | None,
    expected_models: list[LLMModel],
):
    adjusted_params = add_fallback_models_if_missing(
        params=input_params,
        output_model=output_model,
        fallback_strategy=fallback_strategy,
    )

    assert [p.model for p in adjusted_params] == expected_models
