from collections import defaultdict
from typing import Type

import pytest
from infrastructure_common.logging import get_logger
from static_common.enums.organization import ExistingOrganizations

from llm_common.models.llm_config import LLMConfig
from llm_common.models.llm_model import LLMModel

logger = get_logger()


def test_all_models_have_cost_configured():
    models_without_cost = defaultdict(list)
    for model in LLMModel:
        if model == LLMModel.KALEPA_ENSEMBLE_AGENT:
            continue

        for cost in ["cost_per_1M_input_tokens", "cost_per_1M_output_tokens"]:
            if getattr(model, cost) is None:
                if model.is_deprecated:
                    logger.warning("Missing cost for deprecated model", model=model, cost=cost)
                else:
                    models_without_cost[model].append(cost)

    assert not models_without_cost, f"Following models are missing cost configuration: {models_without_cost}"


def test_all_models_have_context_window():
    models_without_max_tokens = defaultdict(list)
    for model in LLMModel:
        if model == LLMModel.KALEPA_ENSEMBLE_AGENT:
            continue

        for max_tokens in ["context_window"]:
            if getattr(model, max_tokens) is None:
                if model.is_deprecated:
                    logger.warning(
                        "Missing context_window for deprecated model", model=model, context_window=max_tokens
                    )
                else:
                    models_without_max_tokens[model].append(max_tokens)

    assert (
        not models_without_max_tokens
    ), f"Following models are missing context_window configuration: {models_without_max_tokens}"


def test_all_models_have_client_assigned():
    def get_all_subclasses(cls: Type) -> set[Type]:
        return set(cls.__subclasses__()).union([s for c in cls.__subclasses__() for s in get_all_subclasses(c)])

    config_classes = get_all_subclasses(LLMConfig)
    models_without_client = []

    for model in LLMModel:
        has_config = False
        if model == LLMModel.KALEPA_ENSEMBLE_AGENT:
            continue

        if model.is_deprecated:
            continue

        for config_class in config_classes:
            if config_class.can_use_model(model):
                has_config = True
                break

        if not has_config:
            models_without_client.append(model)

    assert (
        not models_without_client
    ), f"Following models do not have any client that can use them: {models_without_client}"


@pytest.mark.parametrize(
    ["llm_model", "organization", "expected_result"],
    [
        (LLMModel.CLAUDE_3_7_SONNET, ExistingOrganizations.Nationwide, True),
        (LLMModel.CLAUDE_3_7_SONNET, None, True),
        (LLMModel.TOGETHER_AI_DEEPSEEK_R1, None, False),
        (LLMModel.TOGETHER_AI_DEEPSEEK_R1, ExistingOrganizations.Nationwide, False),
        (LLMModel.TOGETHER_AI_DEEPSEEK_R1, ExistingOrganizations.ARU, True),
    ],
)
def test_is_organization_allowed(llm_model, organization, expected_result):
    assert llm_model.is_organization_allowed(organization) == expected_result
