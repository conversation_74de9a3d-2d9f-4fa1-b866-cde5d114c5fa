[project]
name = "llm_common"
version = "25.5.27.31231.dev0"
description = "Kalepa LLM Common"
authors = [{ name = "Kalepa Tech" }]
requires-python = ">=3.11,<3.12"
readme = "README.md"
dependencies = [
    "infrastructure-common",
    "measurement==4.0a8",
    "marshmallow>=3.12.1,<4",
    "boto3-type-annotations>=0.3.1,<0.4",
    "tblib>=3.0.0,<4",
    "tiktoken>=0.7.0,<0.8",
    "redis>=5.1.0,<6",
    "pydantic>=1.9.1,<3",
    "dill>=0.3.9,<0.4",
    "openai>=1.61,<1.62",
]

[dependency-groups]
kalepa = ["common", "infrastructure-common", "static-common"]
dev = [
    "pytest>=7.2.0,<8",
    "boto3>=1.26.18,<2",
    "mypy>=1.13.0,<2",
    "mypy-extensions>=1.0.0,<2",
]

[tool.uv.sources]
common = { index = "kalepi" }
infrastructure-common = { index = "kalepi" }
static-common = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[tool.uv]
default-groups = ["kalepa", "dev"]

[tool.hatch.build.targets.sdist]
include = ["llm_common"]

[tool.hatch.build.targets.wheel]
include = ["llm_common"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 120
target-version = ['py311']

[tool.isort]
profile = "black"
skip = ["__init__.py"]

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
line-length = 120
target-version = "py38"
extend-exclude = ["test/", "**/__init__.py", "playground.py"]
