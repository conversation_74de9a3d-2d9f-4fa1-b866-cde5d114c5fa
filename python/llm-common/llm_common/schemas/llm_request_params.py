from types import NoneType
from typing import Any, Iterable, Type, Union

from marshmallow import EXCLUDE, Schema, ValidationError, fields, post_dump, post_load
from pydantic import BaseModel, create_model

from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import (
    ClaudeRequestParams,
    GeminiRequestParams,
    GPTRequestParams,
    LLMRequestParams,
)


class LLMRequestParamsSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    model = fields.Enum(LLMModel)
    return_json = fields.Boolean(required=False)
    raise_exceptions = fields.Boolean(required=False)
    use_cache = fields.Boolean(required=False)
    request_timeout = fields.Integer(required=False)
    output_model = fields.Method(
        required=False, allow_none=True, serialize="pickle_output_model", deserialize="unpickle_output_model"
    )
    output_model_json = fields.Method(
        required=False,
        allow_none=True,
        serialize="serialize_output_model_json",
        deserialize="deserialize_output_model_json",
    )

    def pickle_output_model(self, obj: LLMRequestParams) -> str | None:
        return None

    def unpickle_output_model(self, output_model: str | None) -> type | None:
        return None

    def serialize_output_model_json(self, obj: LLMRequestParams) -> dict | None:
        output_model = obj.output_model or obj.output_model_json

        if not output_model:
            return None

        return output_model.model_json_schema()

    def deserialize_output_model_json(
        self, output_model: dict, definitions: dict | None = None
    ) -> Type[BaseModel] | None:
        if not output_model:
            return None

        model_name = output_model.get("title", "UnnamedModel")
        model_properties = output_model.get("properties", {})
        definitions = output_model.get("$defs", definitions)  # Recursively pass definitions if they exist

        # Recursive field type resolver for nested models
        def resolve_field_type(field_schema: dict):
            if "$ref" in field_schema:
                ref_path = field_schema["$ref"]
                ref_name = ref_path.split("/")[-1]
                if ref_name in definitions:
                    # Resolve the referenced model recursively
                    return self.deserialize_output_model_json(definitions[ref_name], definitions)

            type_mapping = {
                "string": str,
                "integer": int,
                "number": float,
                "boolean": bool,
                "null": NoneType,
            }

            if possible_types := field_schema.get("anyOf"):
                return Union[*[resolve_field_type(t) for t in possible_types]]

            field_type = field_schema.get("type")
            if field_type == "array":
                items = field_schema.get("items", {})
                return list[resolve_field_type(items)]
            elif field_type == "object":
                return dict[str, Any]

            return type_mapping.get(field_type, Any)

        # Create model fields
        model_fields = {}
        for field_name, field_schema in model_properties.items():
            field_type = resolve_field_type(field_schema)
            model_fields[field_name] = (field_type, ...)

        model_class = create_model(model_name, **model_fields)

        return model_class

    @post_load
    def make_object(self, data, *args, **kwargs) -> LLMRequestParams:
        return LLMRequestParams(**data)

    @post_dump
    def add_class_name(self, data: dict, **kwargs) -> dict:
        data["class_"] = self.__class__.__name__
        return data


class GPTRequestParamsSchema(LLMRequestParamsSchema):
    class Meta:
        unknown = EXCLUDE

    temperature = fields.Float(required=False)
    max_tokens = fields.Integer(required=False)
    top_p = fields.Float(required=False)
    n = fields.Integer(required=False)
    frequency_penalty = fields.Float(required=False)
    presence_penalty = fields.Float(required=False)
    stream = fields.Boolean(required=False)
    logit_bias: dict = fields.Dict(required=False)
    stop = fields.Raw(required=False, allow_none=True)

    @post_load
    def make_object(self, data, *args, **kwargs) -> GPTRequestParams:
        return GPTRequestParams(**data)


class GeminiRequestParamsSchema(GPTRequestParamsSchema):
    class Meta:
        unknown = EXCLUDE

    @post_load
    def make_object(self, data, *args, **kwargs) -> GeminiRequestParams:
        return GeminiRequestParams(**data)


class ClaudeRequestParamsSchema(GPTRequestParamsSchema):
    class Meta:
        unknown = EXCLUDE

    @post_load
    def make_object(self, data, *args, **kwargs) -> ClaudeRequestParams:
        return ClaudeRequestParams(**data)


class PolymorphicLLMRequestParamsSchema(Schema):
    CLASS_NAME_TO_SCHEMA = {
        GPTRequestParams.__name__: GPTRequestParamsSchema,
        GPTRequestParamsSchema.__name__: GPTRequestParamsSchema,
        GeminiRequestParams.__name__: GeminiRequestParamsSchema,
        GeminiRequestParamsSchema.__name__: GeminiRequestParamsSchema,
        ClaudeRequestParams.__name__: ClaudeRequestParamsSchema,
        ClaudeRequestParamsSchema.__name__: ClaudeRequestParamsSchema,
        LLMRequestParams.__name__: LLMRequestParamsSchema,
        LLMRequestParamsSchema.__name__: LLMRequestParamsSchema,
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _do_load(self, data, *args, **kwargs):
        class_name = data.get("class_")
        if not (schema := self.CLASS_NAME_TO_SCHEMA.get(class_name)):
            raise ValidationError(f"Unknown class name: {class_name}")

        return schema()._do_load(data, *args, **kwargs)

    def dump(self, obj: LLMRequestParams | Iterable[LLMRequestParams], *, many: bool = None):
        result = []
        errors = {}
        many = self.many if many is None else bool(many)

        if not many:
            return self._dump(obj)

        for idx, value in enumerate(obj):
            try:
                res = self._dump(value)
                result.append(res)

            except ValidationError as error:
                errors[idx] = error.normalized_messages()
                result.append(error.valid_data)

        if errors:
            raise ValidationError(errors, data=obj, valid_data=result)

        return result

    def _dump(self, obj: LLMRequestParams) -> dict:
        class_name = obj.__class__.__name__
        if not (schema := self.CLASS_NAME_TO_SCHEMA.get(class_name)):
            raise ValidationError(f"Unknown class name: {class_name}")

        return schema().dump(obj)
