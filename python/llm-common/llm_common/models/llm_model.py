from typing import Self, Type

import tiktoken
from infrastructure_common.logging import get_logger
from pydantic import BaseModel
from static_common.enums.enum import StrEnum
from static_common.enums.organization import ExistingOrganizations
from structlog.stdlib import BoundLogger
from tiktoken import Encoding

from llm_common.models.llm_tool import LLMTool
from llm_common.utils.fallback_strategy import FallbackStrategy


class LLMModel(StrEnum):
    AZURE_GPT_35 = "AZURE_GPT_35"
    AZURE_GPT_35_16K = "AZURE_GPT_35_16K"
    AZURE_GPT_4 = "AZURE_GPT_4"
    AZURE_GPT_4_32K = "AZURE_GPT_4_32K"
    AZURE_GPT_4O = "AZURE_GPT_4O"
    AZURE_GPT_4O_MINI = "AZURE_GPT_4O_MINI"
    OPENAI_GPT_35 = "OPENAI_GPT_35"
    OPENAI_GPT_35_16K = "OPENAI_GPT_35_16K"
    OPENAI_GPT_4 = "OPENAI_GPT_4"
    OPENAI_GPT_4_TURBO = "OPENAI_GPT_4_TURBO"
    OPENAI_GPT_4O = "OPENAI_GPT_4O"
    OPENAI_GPT_4O_MINI = "OPENAI_GPT_4O_MINI"
    OPENAI_GPT_4_1 = "OPENAI_GPT_4_1"
    OPENAI_GPT_4_1_MINI = "OPENAI_GPT_4_1_MINI"
    OPENAI_GPT_4_1_NANO = "OPENAI_GPT_4_1_NANO"
    OPENAI_GPT_45 = "OPENAI_GPT_45"
    OPENAI_GPT_O1 = "OPENAI_GPT_O1"
    OPENAI_GPT_O1_MINI = "OPENAI_GPT_O1_MINI"
    OPENAI_GPT_O3_MINI = "OPENAI_GPT_O3_MINI"
    OPENAI_GPT_O4_MINI = "OPENAI_GPT_O4_MINI"
    LLAMA_3_1_8B = "LLAMA_3_1_8B"
    LLAMA_3_1_70B = "LLAMA_3_1_70B"
    LLAMA_3_1_405B = "LLAMA_3_1_405B"
    SONAR = "SONAR"
    SONAR_PRO = "SONAR_PRO"
    SONAR_REASONING = "SONAR_REASONING"
    SONAR_REASONING_PRO = "SONAR_REASONING_PRO"
    CLAUDE_3_OPUS = "CLAUDE_3_OPUS"
    CLAUDE_3_5_SONNET = "CLAUDE_3_5_SONNET"
    CLAUDE_3_7_SONNET = "CLAUDE_3_7_SONNET"
    CLAUDE_3_5_HAIKU = "CLAUDE_3_5_HAIKU"
    CLAUDE_4_SONNET = "CLAUDE_4_SONNET"
    CLAUDE_4_OPUS = "CLAUDE_4_OPUS"
    GEMINI_1_5_PRO = "GEMINI_1_5_PRO"
    GEMINI_2_0_FLASH = "GEMINI_2_0_FLASH"
    GEMINI_2_0_FLASH_LITE = "GEMINI_2_0_FLASH_LITE"
    GEMINI_2_5_PRO = "GEMINI_2_5_PRO"
    KALEPA_ENSEMBLE_AGENT = "Kalepa Ensemble Agent"

    TOGETHER_AI_DEEPSEEK_V3 = "TOGETHER_AI_DEEPSEEK_V3"
    TOGETHER_AI_DEEPSEEK_R1 = "TOGETHER_AI_DEEPSEEK_R1"
    TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO = "TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO"
    TOGETHER_AI_MISTRAL_7B_INSTRUCT = "TOGETHER_AI_MISTRAL_7B_INSTRUCT"
    TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT = "TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT_V_0_1"
    TOGETHER_AI_MISTRAL_SMALL_24B_INSTRUCT = "TOGETHER_AI_MISTRAL_SMALL_24B_INSTRUCT"
    TOGETHER_AI_LLAMA_4_MAVERICK = "TOGETHER_AI_LLAMA_4_MAVERICK"
    TOGETHER_AI_LLAMA_4_SCOUT = "TOGETHER_AI_LLAMA_4_SCOUT"

    GROK_2 = "GROK_2"

    # Deprecated models - support was dropped or otherwise unavailable
    LLAMA_3_8B = "LLAMA_3_8B"
    LLAMA_3_70B = "LLAMA_3_70B"
    MIXTRAL_8X7B = "MIXTRAL_8X7B"
    OPENAI_GPT_4_32K = "OPENAI_GPT_4_32K"
    CLAUDE_3_HAIKU = "CLAUDE_3_HAIKU"
    CLAUDE_3_SONNET = "CLAUDE_3_SONNET"
    GEMINI_1_5_FLASH = "GEMINI_1_5_FLASH"
    GEMINI_1_0_PRO = "GEMINI_1_0_PRO"

    def get_api_model_name(self) -> str:
        if self == LLMModel.AZURE_GPT_35:
            return "gpt-35-turbo"
        elif self == LLMModel.OPENAI_GPT_35:
            return "gpt-3.5-turbo"
        elif self == LLMModel.AZURE_GPT_35_16K:
            return "gpt-35-turbo-16k"
        elif self == LLMModel.OPENAI_GPT_35_16K:
            return "gpt-3.5-turbo-16k"
        elif self == LLMModel.OPENAI_GPT_4_TURBO:
            return "gpt-4-turbo"
        elif self in [LLMModel.OPENAI_GPT_4O, LLMModel.AZURE_GPT_4O, LLMModel.KALEPA_ENSEMBLE_AGENT]:
            return "gpt-4o"
        elif self in [LLMModel.OPENAI_GPT_4O_MINI, LLMModel.AZURE_GPT_4O_MINI]:
            return "gpt-4o-mini"
        elif self in [LLMModel.AZURE_GPT_4, LLMModel.OPENAI_GPT_4]:
            return "gpt-4"
        elif self in [LLMModel.AZURE_GPT_4_32K, LLMModel.OPENAI_GPT_4_32K]:
            return "gpt-4-32k"
        elif self == LLMModel.OPENAI_GPT_O1:
            return "o1"
        elif self == LLMModel.OPENAI_GPT_O1_MINI:
            return "o1-mini"
        elif self == LLMModel.OPENAI_GPT_O3_MINI:
            return "o3-mini"
        elif self == LLMModel.OPENAI_GPT_O4_MINI:
            return "o4-mini"
        elif self == LLMModel.OPENAI_GPT_45:
            return "gpt-4.5-preview"
        elif self == LLMModel.OPENAI_GPT_4_1:
            return "gpt-4.1"
        elif self == LLMModel.OPENAI_GPT_4_1_MINI:
            return "gpt-4.1-mini"
        elif self == LLMModel.OPENAI_GPT_4_1_NANO:
            return "gpt-4.1-nano"
        elif self == LLMModel.LLAMA_3_8B:
            return "llama-3-8b-instruct"
        elif self == LLMModel.LLAMA_3_70B:
            return "llama-3-70b-instruct"
        elif self == LLMModel.LLAMA_3_1_8B:
            return "llama-3.1-sonar-small-128k-online"
        elif self == LLMModel.LLAMA_3_1_70B:
            return "llama-3.1-sonar-large-128k-online"
        elif self == LLMModel.LLAMA_3_1_405B:
            return "llama-3.1-sonar-huge-128k-online"
        elif self == LLMModel.SONAR:
            return "sonar"
        elif self == LLMModel.SONAR_PRO:
            return "sonar-pro"
        elif self == LLMModel.SONAR_REASONING:
            return "sonar-reasoning"
        elif self == LLMModel.SONAR_REASONING_PRO:
            return "sonar-reasoning-pro"
        elif self == LLMModel.MIXTRAL_8X7B:
            return "mixtral-8x7b-instruct"
        elif self == LLMModel.CLAUDE_3_OPUS:
            return "claude-3-opus-latest"
        elif self == LLMModel.CLAUDE_3_SONNET:
            return "claude-3-sonnet-20240229"
        elif self == LLMModel.CLAUDE_3_5_SONNET:
            return "claude-3-5-sonnet-latest"
        elif self == LLMModel.CLAUDE_3_7_SONNET:
            return "claude-3-7-sonnet-latest"
        elif self == LLMModel.CLAUDE_3_HAIKU:
            return "claude-3-haiku-20240307"
        elif self == LLMModel.CLAUDE_3_5_HAIKU:
            return "claude-3-5-haiku-latest"
        elif self == LLMModel.CLAUDE_4_OPUS:
            return "claude-opus-4-20250514"
        elif self == LLMModel.CLAUDE_4_SONNET:
            return "claude-sonnet-4-20250514"
        elif self == LLMModel.GEMINI_1_0_PRO:
            return "gemini-pro"
        elif self == LLMModel.GEMINI_1_5_PRO:
            return "gemini-1.5-pro-latest"
        elif self == LLMModel.GEMINI_1_5_FLASH:
            return "gemini-1.5-flash-latest"
        elif self == LLMModel.GEMINI_2_0_FLASH:
            return "gemini-2.0-flash"
        elif self == LLMModel.GEMINI_2_0_FLASH_LITE:
            return "gemini-2.0-flash-lite"
        elif self == LLMModel.GEMINI_2_5_PRO:
            return "gemini-2.5-pro-preview-05-06"
        elif self == LLMModel.TOGETHER_AI_DEEPSEEK_V3:
            return "deepseek-ai/DeepSeek-V3"
        elif self == LLMModel.TOGETHER_AI_DEEPSEEK_R1:
            return "deepseek-ai/DeepSeek-R1"
        elif self == LLMModel.TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO:
            return "meta-llama/Llama-3.3-70B-Instruct-Turbo"
        elif self == LLMModel.TOGETHER_AI_MISTRAL_7B_INSTRUCT:
            return "mistralai/Mistral-7B-Instruct-v0.3"
        elif self == LLMModel.TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT:
            return "mistralai/Mixtral-8x22B-Instruct-v0.1"
        elif self == LLMModel.TOGETHER_AI_MISTRAL_SMALL_24B_INSTRUCT:
            return "mistralai/Mistral-Small-24B-Instruct-2501"
        elif self == LLMModel.TOGETHER_AI_LLAMA_4_MAVERICK:
            return "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8"
        elif self == LLMModel.TOGETHER_AI_LLAMA_4_SCOUT:
            return "meta-llama/Llama-4-Scout-17B-16E-Instruct"
        elif self == LLMModel.GROK_2:
            return "grok-2-1212"

    def get_broader_context_window_model(self) -> str | None:
        if self == LLMModel.AZURE_GPT_35:
            return LLMModel.AZURE_GPT_35_16K
        elif self == LLMModel.OPENAI_GPT_35:
            return LLMModel.OPENAI_GPT_35_16K
        elif self == LLMModel.AZURE_GPT_4:
            return LLMModel.AZURE_GPT_4_32K

    def get_fallback_models(
        self,
        fallback_strategy: FallbackStrategy,
        output_model: Type[BaseModel] | None = None,
        log: BoundLogger | None = None,
    ) -> list[Self]:
        log = log or get_logger()

        identical_model_sets = [
            [LLMModel.OPENAI_GPT_4O, LLMModel.AZURE_GPT_4O],
            [LLMModel.OPENAI_GPT_4O_MINI, LLMModel.AZURE_GPT_4O_MINI],
            [LLMModel.OPENAI_GPT_4, LLMModel.AZURE_GPT_4],
            [LLMModel.OPENAI_GPT_4_32K, LLMModel.AZURE_GPT_4_32K],
            [LLMModel.OPENAI_GPT_35, LLMModel.AZURE_GPT_35],
            [LLMModel.OPENAI_GPT_35_16K, LLMModel.AZURE_GPT_35_16K],
        ]
        similar_model_sets = [
            [LLMModel.CLAUDE_3_5_SONNET, LLMModel.CLAUDE_3_7_SONNET, LLMModel.CLAUDE_4_SONNET],
            [LLMModel.OPENAI_GPT_O3_MINI, LLMModel.OPENAI_GPT_O4_MINI],
            [LLMModel.OPENAI_GPT_4O, LLMModel.OPENAI_GPT_4_1],
        ]

        deprecated_model_mappings = {
            LLMModel.CLAUDE_3_SONNET: LLMModel.CLAUDE_4_SONNET,
            LLMModel.CLAUDE_3_HAIKU: LLMModel.CLAUDE_3_5_HAIKU,
            LLMModel.GEMINI_1_5_FLASH: LLMModel.GEMINI_2_0_FLASH,
            LLMModel.GEMINI_1_0_PRO: LLMModel.GEMINI_1_5_PRO,
            LLMModel.LLAMA_3_8B: LLMModel.LLAMA_3_1_8B,
            LLMModel.LLAMA_3_70B: LLMModel.LLAMA_3_1_70B,
            LLMModel.MIXTRAL_8X7B: LLMModel.TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT,
            LLMModel.TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT: LLMModel.TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO,
        }

        if new_model := deprecated_model_mappings.get(self):
            log.warning("Deprecated model requested, overriding with new version", old_model=self, new_model=new_model)
            return [new_model]

        fallback_models = []
        model_sets_to_search = (
            identical_model_sets if fallback_strategy == FallbackStrategy.IDENTICAL else similar_model_sets
        )

        for model_set in model_sets_to_search:
            if self in model_set:
                if output_model:
                    supported_models = [model for model in model_set if model.supports_structured_output()]
                else:
                    supported_models = model_set

                fallback_models.extend(model for model in supported_models if model != self)

        return [self, *fallback_models]

    @property
    def is_deprecated(self) -> bool:
        return self in [
            LLMModel.MIXTRAL_8X7B,
            LLMModel.LLAMA_3_8B,
            LLMModel.LLAMA_3_70B,
            LLMModel.LLAMA_3_1_405B,
            LLMModel.CLAUDE_3_HAIKU,
            LLMModel.CLAUDE_3_SONNET,
            LLMModel.OPENAI_GPT_4_32K,
            LLMModel.GEMINI_1_0_PRO,
            LLMModel.GEMINI_1_5_FLASH,
            LLMModel.TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT,
        ]

    def get_encoding(self) -> Encoding | None:
        if self in [
            LLMModel.AZURE_GPT_4,
            LLMModel.AZURE_GPT_4_32K,
            LLMModel.AZURE_GPT_35,
            LLMModel.AZURE_GPT_35_16K,
            LLMModel.OPENAI_GPT_4_32K,
            LLMModel.OPENAI_GPT_4,
            LLMModel.OPENAI_GPT_35,
            LLMModel.OPENAI_GPT_35,
            LLMModel.OPENAI_GPT_4_TURBO,
        ]:
            return tiktoken.encoding_for_model(self.get_api_model_name())
        elif self in [LLMModel.OPENAI_GPT_4O, LLMModel.OPENAI_GPT_4O_MINI]:
            return tiktoken.get_encoding("o200k_base")
        else:
            return tiktoken.get_encoding("cl100k_base")

    def supports_structured_output(self) -> bool:
        return self in [
            LLMModel.OPENAI_GPT_4O_MINI,
            LLMModel.OPENAI_GPT_4O,
            LLMModel.OPENAI_GPT_O1,
            LLMModel.OPENAI_GPT_O3_MINI,
            LLMModel.OPENAI_GPT_O4_MINI,
            LLMModel.OPENAI_GPT_4_1,
            LLMModel.OPENAI_GPT_4_1_MINI,
            LLMModel.OPENAI_GPT_4_1_NANO,
            LLMModel.GEMINI_1_5_FLASH,
            LLMModel.GEMINI_1_5_PRO,
            LLMModel.GEMINI_2_0_FLASH_LITE,
            LLMModel.GEMINI_2_0_FLASH,
            LLMModel.GEMINI_2_5_PRO,
            LLMModel.TOGETHER_AI_LLAMA_4_MAVERICK,
            LLMModel.TOGETHER_AI_LLAMA_4_SCOUT,
            LLMModel.TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO,
            LLMModel.TOGETHER_AI_DEEPSEEK_V3,
        ]

    def supports_json_output(self) -> bool:
        return self in [
            LLMModel.OPENAI_GPT_35,
            LLMModel.OPENAI_GPT_35_16K,
            LLMModel.OPENAI_GPT_4,
            LLMModel.OPENAI_GPT_4_TURBO,
            LLMModel.OPENAI_GPT_4O,
            LLMModel.OPENAI_GPT_4O_MINI,
            LLMModel.OPENAI_GPT_45,
            LLMModel.OPENAI_GPT_O1,
            LLMModel.OPENAI_GPT_O3_MINI,
            LLMModel.OPENAI_GPT_O4_MINI,
            LLMModel.OPENAI_GPT_4_1,
            LLMModel.OPENAI_GPT_4_1_MINI,
            LLMModel.OPENAI_GPT_4_1_NANO,
            LLMModel.AZURE_GPT_35_16K,
            LLMModel.AZURE_GPT_4,
            LLMModel.AZURE_GPT_4O_MINI,
            LLMModel.AZURE_GPT_4O,
            LLMModel.CLAUDE_3_OPUS,
            LLMModel.CLAUDE_3_5_SONNET,
            LLMModel.CLAUDE_3_5_HAIKU,
            LLMModel.CLAUDE_3_7_SONNET,
            LLMModel.CLAUDE_4_OPUS,
            LLMModel.CLAUDE_4_SONNET,
            LLMModel.GEMINI_1_5_PRO,
            LLMModel.GEMINI_2_0_FLASH,
            LLMModel.GEMINI_2_0_FLASH_LITE,
            LLMModel.GEMINI_2_5_PRO,
            LLMModel.LLAMA_3_1_8B,
            LLMModel.LLAMA_3_1_70B,
            LLMModel.SONAR,
            LLMModel.SONAR_PRO,
            LLMModel.SONAR_REASONING,
            LLMModel.SONAR_REASONING_PRO,
            LLMModel.TOGETHER_AI_LLAMA_4_MAVERICK,
            LLMModel.TOGETHER_AI_LLAMA_4_SCOUT,
            LLMModel.TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO,
            LLMModel.TOGETHER_AI_DEEPSEEK_V3,
        ]

    def supports_tool(self, tool: LLMTool) -> bool:
        supported_tools = {LLMTool.CODE_EXECUTION: {LLMModel.GEMINI_2_0_FLASH, LLMModel.GEMINI_2_5_PRO}}
        return self in supported_tools.get(tool, set())

    @property
    def supports_file_content(self) -> bool:
        return self in [LLMModel.GEMINI_2_0_FLASH, LLMModel.GEMINI_2_5_PRO]

    @property
    def context_window(self) -> int:
        context_window_mapping = {
            self.AZURE_GPT_35: 4096,
            self.AZURE_GPT_35_16K: 16_384,
            self.AZURE_GPT_4: 8_192,
            self.AZURE_GPT_4_32K: 32_768,
            self.AZURE_GPT_4O: 144_384,
            self.OPENAI_GPT_35: 4096,
            self.OPENAI_GPT_35_16K: 16_384,
            self.AZURE_GPT_4O_MINI: 200_000,
            self.OPENAI_GPT_4: 8_191,
            self.OPENAI_GPT_4_TURBO: 128_000,
            self.OPENAI_GPT_4O: 128_000,
            self.OPENAI_GPT_4O_MINI: 200_000,
            self.OPENAI_GPT_45: 128_000,
            self.OPENAI_GPT_4_1: 1_047_576,
            self.OPENAI_GPT_4_1_MINI: 1_047_576,
            self.OPENAI_GPT_4_1_NANO: 1_047_576,
            self.OPENAI_GPT_O1: 300_000,
            self.OPENAI_GPT_O1_MINI: 160_768,
            self.OPENAI_GPT_O3_MINI: 300_000,
            self.OPENAI_GPT_O4_MINI: 300_000,
            self.LLAMA_3_1_8B: 16_384,
            self.LLAMA_3_1_70B: 131_072,
            self.LLAMA_3_1_405B: 32_768,
            self.SONAR: 127_072,
            self.SONAR_PRO: 200_000,
            self.SONAR_REASONING: 127_072,
            self.SONAR_REASONING_PRO: 128_000,
            self.CLAUDE_3_OPUS: 200_000,
            self.CLAUDE_4_OPUS: 200_000,
            self.CLAUDE_3_5_SONNET: 200_000,
            self.CLAUDE_3_7_SONNET: 200_000,
            self.CLAUDE_4_SONNET: 200_000,
            self.CLAUDE_3_5_HAIKU: 200_000,
            self.GEMINI_1_0_PRO: 40_992,
            self.GEMINI_1_5_PRO: 2_105_344,
            self.GEMINI_1_5_FLASH: 1_056_768,
            self.GEMINI_2_0_FLASH: 1_056_768,
            self.GEMINI_2_0_FLASH_LITE: 1_056_768,
            self.GEMINI_2_5_PRO: 1_114_111,
            self.TOGETHER_AI_DEEPSEEK_R1: 128_000,
            self.TOGETHER_AI_DEEPSEEK_V3: 128_000,
            self.TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO: 8_000,
            self.TOGETHER_AI_MISTRAL_7B_INSTRUCT: 32_768,
            self.TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT: 65_536,
            self.TOGETHER_AI_MISTRAL_SMALL_24B_INSTRUCT: 32_768,
            self.TOGETHER_AI_LLAMA_4_SCOUT: 10_000_000,
            self.TOGETHER_AI_LLAMA_4_MAVERICK: 1_000_000,
            self.GROK_2: 256_000,
        }
        return context_window_mapping.get(self, None)

    @property
    def cost_per_1M_input_tokens(self) -> float:  # in dollars
        cost_mapping = {
            self.AZURE_GPT_35: 1.50,
            self.AZURE_GPT_35_16K: 3.00,
            self.AZURE_GPT_4: 30.00,
            self.AZURE_GPT_4_32K: 60.00,
            self.AZURE_GPT_4O: 2.50,
            self.OPENAI_GPT_35: 1.50,
            self.OPENAI_GPT_35_16K: 3.00,
            self.AZURE_GPT_4O_MINI: 0.15,
            self.OPENAI_GPT_4: 30.00,
            self.OPENAI_GPT_4_TURBO: 10.00,
            self.OPENAI_GPT_4O: 2.50,
            self.OPENAI_GPT_4O_MINI: 0.15,
            self.OPENAI_GPT_45: 75.00,
            self.OPENAI_GPT_4_1: 2.00,
            self.OPENAI_GPT_4_1_MINI: 0.40,
            self.OPENAI_GPT_4_1_NANO: 0.10,
            self.OPENAI_GPT_O1: 15.00,
            self.OPENAI_GPT_O1_MINI: 1.1,
            self.OPENAI_GPT_O3_MINI: 1.1,
            self.OPENAI_GPT_O4_MINI: 1.1,
            self.LLAMA_3_1_8B: 0.2,
            self.LLAMA_3_1_70B: 1.00,
            self.LLAMA_3_1_405B: 5.00,
            self.SONAR: 1.00,
            self.SONAR_PRO: 3.00,
            self.SONAR_REASONING: 1.00,
            self.SONAR_REASONING_PRO: 2.00,
            self.CLAUDE_3_OPUS: 15.00,
            self.CLAUDE_4_OPUS: 15.00,
            self.CLAUDE_3_5_SONNET: 3.00,
            self.CLAUDE_3_7_SONNET: 3.00,
            self.CLAUDE_4_SONNET: 3.00,
            self.CLAUDE_3_5_HAIKU: 0.80,
            self.GEMINI_1_0_PRO: 1.50,
            self.GEMINI_1_5_PRO: 2.50,
            self.GEMINI_1_5_FLASH: 0.15,
            self.GEMINI_2_0_FLASH: 0.1,
            self.GEMINI_2_0_FLASH_LITE: 0.075,
            self.GEMINI_2_5_PRO: 1.25,
            self.TOGETHER_AI_DEEPSEEK_R1: 3.00,
            self.TOGETHER_AI_DEEPSEEK_V3: 1.25,
            self.TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO: 0.88,
            self.TOGETHER_AI_MISTRAL_7B_INSTRUCT: 0.20,
            self.TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT: 1.20,
            self.TOGETHER_AI_MISTRAL_SMALL_24B_INSTRUCT: 0.80,
            self.TOGETHER_AI_LLAMA_4_SCOUT: 0.18,
            self.TOGETHER_AI_LLAMA_4_MAVERICK: 0.27,
            self.GROK_2: 2.00,
        }
        return cost_mapping.get(self, None)

    @property
    def cost_per_1M_output_tokens(self) -> float:  # in dollars
        cost_mapping = {
            self.AZURE_GPT_35: 2.00,
            self.AZURE_GPT_35_16K: 4.00,
            self.AZURE_GPT_4: 60.00,
            self.AZURE_GPT_4_32K: 120.00,
            self.AZURE_GPT_4O: 10.00,
            self.AZURE_GPT_4O_MINI: 0.60,
            self.OPENAI_GPT_35: 2.00,
            self.OPENAI_GPT_35_16K: 4.00,
            self.OPENAI_GPT_4: 60.00,
            self.OPENAI_GPT_4_TURBO: 30.00,
            self.OPENAI_GPT_4O: 10.00,
            self.OPENAI_GPT_4O_MINI: 0.60,
            self.OPENAI_GPT_45: 150.00,
            self.OPENAI_GPT_4_1: 8.00,
            self.OPENAI_GPT_4_1_MINI: 1.60,
            self.OPENAI_GPT_4_1_NANO: 0.40,
            self.OPENAI_GPT_O1: 7.50,
            self.OPENAI_GPT_O1_MINI: 4.4,
            self.OPENAI_GPT_O3_MINI: 4.4,
            self.OPENAI_GPT_O4_MINI: 4.4,
            self.LLAMA_3_1_8B: 0.2,
            self.LLAMA_3_1_70B: 1.00,
            self.LLAMA_3_1_405B: 5.00,
            self.SONAR: 1.00,
            self.SONAR_PRO: 15.00,
            self.SONAR_REASONING: 5.00,
            self.SONAR_REASONING_PRO: 8.00,
            self.CLAUDE_3_OPUS: 75.00,
            self.CLAUDE_4_OPUS: 75.00,
            self.CLAUDE_3_5_SONNET: 15.00,
            self.CLAUDE_3_7_SONNET: 15.00,
            self.CLAUDE_4_SONNET: 15.00,
            self.CLAUDE_3_5_HAIKU: 4.00,
            self.GEMINI_1_0_PRO: 1.50,
            self.GEMINI_1_5_PRO: 10.00,
            self.GEMINI_1_5_FLASH: 0.60,
            self.GEMINI_2_0_FLASH: 0.4,
            self.GEMINI_2_0_FLASH_LITE: 0.3,
            self.GEMINI_2_5_PRO: 10.00,
            self.TOGETHER_AI_DEEPSEEK_R1: 7.00,
            self.TOGETHER_AI_DEEPSEEK_V3: 1.25,
            self.TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO: 0.88,
            self.TOGETHER_AI_MISTRAL_7B_INSTRUCT: 0.20,
            self.TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT: 1.20,
            self.TOGETHER_AI_MISTRAL_SMALL_24B_INSTRUCT: 0.80,
            self.TOGETHER_AI_LLAMA_4_SCOUT: 0.59,
            self.TOGETHER_AI_LLAMA_4_MAVERICK: 0.85,
            self.GROK_2: 10.00,
        }
        return cost_mapping.get(self, None)

    # By default, no organizations are forbidden
    @property
    def forbidden_organizations(self) -> list[ExistingOrganizations]:
        return []

    # If there are no allowed organizations, then all organizations are allowed
    @property
    def allowed_organizations(self) -> list[ExistingOrganizations]:
        if self in [LLMModel.TOGETHER_AI_DEEPSEEK_R1, LLMModel.TOGETHER_AI_DEEPSEEK_V3]:
            return [
                ExistingOrganizations.KalepaDemo,
                ExistingOrganizations.KalepaTest,
                ExistingOrganizations.KalepaNewDemo,
                ExistingOrganizations.NecSpecialty,
                ExistingOrganizations.ARU,
                ExistingOrganizations.Paragon,
                ExistingOrganizations.BowheadSpecialty,
                ExistingOrganizations.ISC,
            ]
        return []

    # If there are no limitations on organizations, then organization can be None
    def is_organization_allowed(self, organization: ExistingOrganizations | None = None) -> bool:
        if organization is None:
            return not self.forbidden_organizations and not self.allowed_organizations
        result = True
        if self.forbidden_organizations:
            result = result and organization not in self.forbidden_organizations
        if self.allowed_organizations:
            result = result and organization in self.allowed_organizations
        return result
