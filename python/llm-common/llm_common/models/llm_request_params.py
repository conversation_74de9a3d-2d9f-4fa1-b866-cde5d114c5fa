from dataclasses import dataclass, field
from typing import Type

from dataclasses_json import dataclass_json
from infrastructure_common.logging import get_logger
from pydantic import BaseModel

from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_tool import LLMTool

logger = get_logger()


@dataclass_json
@dataclass
class LLMRequestParams:
    model: LLMModel
    return_json: bool = False
    raise_exceptions: bool = False
    use_cache: bool = False
    request_timeout: int = 60
    output_model: Type[BaseModel] | None = None
    output_model_json: Type[BaseModel] | None = None
    tools: list[LLMTool] | None = None

    def __post_init__(self):
        if self.output_model:
            logger.error(
                "Output_model is deprecated. Use output_model_json instead. This will be removed in the future."
            )
            self.output_model_json = self.output_model
            self.output_model = None


@dataclass_json
@dataclass
class GPTRequestParams(LLMRequestParams):
    temperature: float = 0.0
    max_tokens: int = 128
    top_p: float = 1.0
    n: int = 1
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stream: bool = False
    logit_bias: dict = field(default_factory=dict)
    stop: str | list[str] | None = None
    """https://platform.openai.com/docs/api-reference/chat/create#chat-create-stop"""


@dataclass_json
@dataclass
class ClaudeRequestParams(GPTRequestParams):
    model: LLMModel = LLMModel.CLAUDE_3_7_SONNET


@dataclass_json
@dataclass
class GeminiRequestParams(GPTRequestParams):
    model: LLMModel = LLMModel.GEMINI_2_0_FLASH
