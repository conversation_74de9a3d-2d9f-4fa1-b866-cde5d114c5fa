import os
from abc import abstractmethod
from dataclasses import dataclass

from dataclasses_json import dataclass_json

from llm_common.models.llm_model import LLMModel


@dataclass_json
@dataclass
class LLMConfig:
    @staticmethod
    @abstractmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        ...


@dataclass_json
@dataclass
class GPTConfig(LLMConfig):
    api_key: str
    api_type: str
    endpoint: str
    api_version: str
    broader_context_window_fallback: bool = True
    deployment_name: str | None = None
    request_timeout: int = 60


@dataclass_json
@dataclass
class AzureGPTConfig(GPTConfig):
    api_type: str = "azure"
    endpoint: str = f"https://kalepa-{os.getenv('KALEPA_ENV', 'dev')}.openai.azure.com"
    api_version: str = "2024-06-01"

    @staticmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        return llm_model in [
            LLMModel.AZURE_GPT_35,
            LLMModel.AZURE_GPT_35_16K,
            LLMModel.AZURE_GPT_4,
            LLMModel.AZURE_GPT_4_32K,
            LLMModel.AZURE_GPT_4O_MINI,
            LLMModel.AZURE_GPT_4O,
        ]


@dataclass_json
@dataclass
class OpenAIGPTConfig(GPTConfig):
    api_type: str = "openai"
    api_version: str | None = None
    endpoint: str = "https://api.openai.com/v1"

    @staticmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        return llm_model in [
            LLMModel.OPENAI_GPT_35,
            LLMModel.OPENAI_GPT_35_16K,
            LLMModel.OPENAI_GPT_4,
            LLMModel.OPENAI_GPT_4_32K,
            LLMModel.OPENAI_GPT_4_TURBO,
            LLMModel.OPENAI_GPT_4O,
            LLMModel.OPENAI_GPT_4O_MINI,
            LLMModel.OPENAI_GPT_45,
            LLMModel.OPENAI_GPT_4_1,
            LLMModel.OPENAI_GPT_4_1_NANO,
            LLMModel.OPENAI_GPT_4_1_MINI,
            LLMModel.OPENAI_GPT_O1,
            LLMModel.OPENAI_GPT_O1_MINI,
            LLMModel.OPENAI_GPT_O3_MINI,
            LLMModel.OPENAI_GPT_O4_MINI,
        ]


@dataclass_json
@dataclass
class PerplexityGPTConfig(OpenAIGPTConfig):
    endpoint: str = "https://api.perplexity.ai"

    @staticmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        return llm_model in [
            LLMModel.LLAMA_3_1_8B,
            LLMModel.LLAMA_3_1_70B,
            LLMModel.LLAMA_3_1_405B,
            LLMModel.SONAR,
            LLMModel.SONAR_PRO,
            LLMModel.SONAR_REASONING,
            LLMModel.SONAR_REASONING_PRO,
        ]


@dataclass_json
@dataclass
class TogetherAIGPTConfig(OpenAIGPTConfig):
    endpoint: str = "https://api.together.xyz/v1"

    @staticmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        return llm_model in [
            LLMModel.TOGETHER_AI_DEEPSEEK_R1,
            LLMModel.TOGETHER_AI_DEEPSEEK_V3,
            LLMModel.TOGETHER_AI_MISTRAL_7B_INSTRUCT,
            LLMModel.TOGETHER_AI_MIXTRAL_8X22B_INSTRUCT,
            LLMModel.TOGETHER_AI_MISTRAL_SMALL_24B_INSTRUCT,
            LLMModel.TOGETHER_AI_LLAMA_3_3_70B_INSTRUCT_TURBO,
            LLMModel.TOGETHER_AI_LLAMA_4_SCOUT,
            LLMModel.TOGETHER_AI_LLAMA_4_MAVERICK,
        ]


@dataclass_json
@dataclass
class XAIGPTConfig(OpenAIGPTConfig):
    endpoint: str = "https://api.x.ai/v1"

    @staticmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        return llm_model in [
            LLMModel.GROK_2,
        ]


@dataclass_json
@dataclass
class ClaudeConfig(LLMConfig):
    api_key: str

    @staticmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        return llm_model in [
            LLMModel.CLAUDE_3_OPUS,
            LLMModel.CLAUDE_3_SONNET,
            LLMModel.CLAUDE_3_HAIKU,
            LLMModel.CLAUDE_3_5_SONNET,
            LLMModel.CLAUDE_3_5_HAIKU,
            LLMModel.CLAUDE_3_7_SONNET,
            LLMModel.CLAUDE_4_SONNET,
            LLMModel.CLAUDE_4_OPUS,
        ]


@dataclass_json
@dataclass
class ClaudeOpenAIConfig(OpenAIGPTConfig):
    api_type: str = "openai"
    api_version: str | None = None
    endpoint: str = "https://api.anthropic.com/v1/"

    @staticmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        return llm_model in [
            LLMModel.CLAUDE_3_OPUS,
            LLMModel.CLAUDE_3_SONNET,
            LLMModel.CLAUDE_3_HAIKU,
            LLMModel.CLAUDE_3_5_SONNET,
            LLMModel.CLAUDE_3_5_HAIKU,
            LLMModel.CLAUDE_3_7_SONNET,
            LLMModel.CLAUDE_4_SONNET,
            LLMModel.CLAUDE_4_OPUS,
        ]


@dataclass_json
@dataclass
class GeminiConfig(LLMConfig):
    api_key: str

    @staticmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        return llm_model in [
            LLMModel.GEMINI_1_5_PRO,
            LLMModel.GEMINI_1_0_PRO,
            LLMModel.GEMINI_1_5_FLASH,
            LLMModel.GEMINI_2_0_FLASH,
            LLMModel.GEMINI_2_0_FLASH_LITE,
            LLMModel.GEMINI_2_5_PRO,
        ]


@dataclass_json
@dataclass
class GeminiOpenAIGPTConfig(GPTConfig):
    api_type: str = "openai"
    api_version: str | None = None
    endpoint: str = "https://generativelanguage.googleapis.com/v1beta/openai/"

    @staticmethod
    def can_use_model(llm_model: LLMModel) -> bool:
        return llm_model in [
            LLMModel.GEMINI_1_5_PRO,
            LLMModel.GEMINI_1_0_PRO,
            LLMModel.GEMINI_1_5_FLASH,
            LLMModel.GEMINI_2_0_FLASH,
            LLMModel.GEMINI_2_0_FLASH_LITE,
            LLMModel.GEMINI_2_5_PRO,
        ]
