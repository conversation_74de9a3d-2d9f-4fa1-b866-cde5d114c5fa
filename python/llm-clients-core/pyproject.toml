[project]
name = "llm-clients-core"
version = "25.5.23.36890.dev0"
description = "Kalepa LLM Clients Core"
authors = [{ name = "Kalepa Tech" }]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "common",
    "static-common",
    "infrastructure-common",
    "llm-common",
    "google-genai~=1.13.0",
    "anthropic~=0.50.0",
    "google-cloud-aiplatform~=1.79.0",
    "autoblocksai~=0.0.105",
    "pydantic~=2.0",
    "openai~=1.61",
    "httpx>=0.28.1, <1",
    "measurement==4.0a8",
]

[dependency-groups]
dev = ["pytest~=7.2.0", "mypy~=1.13.0"]
kalepa = ["common", "static-common", "infrastructure-common", "llm-common"]

[tool.uv.sources]
common = { index = "kalepi" }
infrastructure-common = { index = "kalepi" }
llm-common = { index = "kalepi" }
static-common = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[tool.uv]
package = true
default-groups = ["dev", "kalepa"]

[tool.black]
line-length = 120
target-version = ['py311']

[tool.isort]
profile = "black"
skip = ["__init__.py"]

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
line-length = 120
target-version = "py38"
extend-exclude = ["test/", "**/__init__.py", "playground.py"]
