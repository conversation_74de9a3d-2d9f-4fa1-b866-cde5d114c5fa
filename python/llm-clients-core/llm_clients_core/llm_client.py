import json
import re
from abc import ABC, abstractmethod
from contextlib import contextmanager
from copy import deepcopy
from typing import Generator

from common.clients.cache import CacheClient
from common.clients.feature_flags import FeatureFlagsClient
from datadog_lambda.metric import lambda_metric
from infrastructure_common.logging import get_logger
from llm_common.models.errors import OutputTooLongError
from llm_common.models.llm_config import LLMConfig
from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import LLMRequestParams
from llm_common.models.llm_response import LLMHeaders, LLMResponse
from llm_common.models.retry_strategy import LLMRetryStrategy
from static_common.enums.organization import ExistingOrganizations
from static_common.models.openai import ChatCompletionPrompt, FileContent, Message
from structlog.stdlib import BoundLogger

from llm_clients_core.llm import (
    LLM_RESPONSE,
    CacheClientNotDefinedError,
    LLMForbiddenForOrganization,
    NoCompatibleClientsError,
    NotSupportedFeatureInModelError,
)
from llm_clients_core.models.feature_flags import FeatureType
from llm_clients_core.models.stop_reason import StopReason
from llm_clients_core.tracing.autoblocks_tracer import AutoblocksLLMTracer
from llm_clients_core.tracing.llm_tracer import LLMTracer
from llm_clients_core.utils import (
    clean_control_characters,
    clean_json_value,
    get_cache_timeout,
    retry_openai,
)

logger = get_logger()


class LLMClient(ABC):
    NOT_LOGGED_ATTRS = ["api_key"]

    def __init__(self, config: LLMConfig, cache_client: CacheClient | None = None) -> None:
        self.cache_client = cache_client
        self._config = config

        self._validate_config()
        self._tracers: list[LLMTracer] = []

        if not self.cache_client:
            self._config.use_cache = False

        self.setup_logger()

    @abstractmethod
    def _get_response(self, prompt: ChatCompletionPrompt | Message, params: LLMRequestParams) -> LLM_RESPONSE | None:
        ...

    @abstractmethod
    def _get_response_str(self, response: LLM_RESPONSE | dict) -> str | None:
        ...

    def _get_stop_reason(self, response: LLM_RESPONSE | dict) -> StopReason | None:
        try:
            return self._do_get_stop_reason(response)
        except:
            self._log.exception("Failed to get stop reason")
            return None

    @abstractmethod
    def _do_get_stop_reason(self, response: LLM_RESPONSE) -> StopReason | None:
        ...

    def _record_usage(self, params: LLMRequestParams, response: LLM_RESPONSE, call_origin: str):
        try:
            ONE_MILLION = 1_000_000  # const number of tokens
            input_tokens = self._get_input_tokens(response)
            output_tokens = self._get_output_tokens(response)
            if input_cost_per_1M := params.model.cost_per_1M_input_tokens:
                input_cost = (input_tokens / ONE_MILLION) * input_cost_per_1M
            else:
                self._log.warning("No input cost per 1M tokens defined")
                input_cost = None

            if output_cost_per_1M := params.model.cost_per_1M_output_tokens:
                output_cost = (output_tokens / ONE_MILLION) * output_cost_per_1M
            else:
                self._log.warning("No output cost per 1M tokens defined")
                output_cost = None

            total_cost = input_cost + output_cost if input_cost is not None and output_cost is not None else None
            self._log.info(
                "LLM model usage",
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=output_tokens + input_tokens,
                input_cost=input_cost,
                output_cost=output_cost,
                total_cost=total_cost,
            )

            self._record_usage_metric(
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                input_cost=input_cost,
                output_cost=output_cost,
                model_name=params.model.value,
                call_origin=call_origin,
            )
        except:
            self._log.warning("Failed to record LLM usage", exc_info=True)

    @abstractmethod
    def _get_input_tokens(self, response: LLM_RESPONSE) -> int:
        ...

    @abstractmethod
    def _get_output_tokens(self, response: LLM_RESPONSE) -> int:
        ...

    @abstractmethod
    def _get_token_usage(self, response: LLM_RESPONSE) -> tuple[int, int]:
        ...

    def can_use_model(self, llm_model: LLMModel) -> bool:
        return self._config.can_use_model(llm_model)

    def _validate_config(self) -> None:
        ...

    def setup_logger(
        self,
        params: LLMRequestParams | None = None,
        call_origin: str | None = None,
        additional_params: dict | None = None,
    ) -> None:
        self._log = get_logger()

        to_bind = {"llm_client": self.__class__.__name__}
        to_bind.update(self._config.to_dict())

        if params:
            to_bind.update(params.to_dict())

        if call_origin:
            to_bind["call_origin"] = call_origin

        if additional_params:
            to_bind.update(additional_params)

        to_bind = {k: v for k, v in to_bind.items() if k not in self.NOT_LOGGED_ATTRS}
        self._log = self._log.bind(**to_bind)

    def get_traced_response(
        self,
        prompt: ChatCompletionPrompt | Message,
        params: LLMRequestParams,
        call_origin: str,
        headers: LLMHeaders = None,
        additional_params: dict | None = None,
    ) -> tuple[LLMResponse, LLMHeaders]:
        headers = headers or {}
        with self._get_response_traced(
            headers=headers, prompt=prompt, params=params, call_origin=call_origin, additional_params=additional_params
        ) as result:
            return result, headers

    def get_response(
        self,
        prompt: ChatCompletionPrompt | Message,
        params: LLMRequestParams,
        call_origin: str,
        additional_params: dict | None = None,
    ) -> LLMResponse:
        self.setup_logger(params, call_origin, additional_params)
        retry_count = 0
        MAX_RETRIES = 2

        while retry_count <= MAX_RETRIES:
            cache_used = False
            try:
                if params.use_cache:
                    if not self.cache_client:
                        if params.raise_exceptions:
                            raise CacheClientNotDefinedError()
                        else:
                            self._log.error("Cache client not defined for request using cache")
                            return LLMResponse.create_empty()

                    raw_response, cache_used = self._get_response_cached(prompt=prompt, params=params)
                else:
                    organization_id = None if not additional_params else additional_params.get("organization_id")
                    organization = ExistingOrganizations(organization_id) if organization_id else None
                    if not params.model.is_organization_allowed(organization):
                        self._log.warning(
                            "Requested model not allowed for given organization",
                            model=params.model,
                            organization=organization,
                        )
                        raise LLMForbiddenForOrganization(model=params.model, organization=organization)
                    raw_response = self._get_response(prompt=prompt, params=params)
                    if self._get_prompt_files(prompt=prompt) and not params.model.supports_file_content:
                        self._log.warning("File upload not allowed for given model", model=params.model)
                        raise NotSupportedFeatureInModelError(model=params.model, feature="files input")
                self._trace_raw_response(raw_response, cache_used, call_origin)

            except Exception as e:
                self._additional_exception_handler(e)

                if params.raise_exceptions:
                    raise e
                else:
                    return LLMResponse.create_empty()

            if not raw_response:
                return LLMResponse.create_empty()

            if not cache_used:
                self._record_usage(params, raw_response, call_origin)
                input_tokens, output_tokens = self._get_token_usage(response=raw_response)
            else:
                input_tokens, output_tokens = None, None

            stop_reason = self._get_stop_reason(raw_response)

            if stop_reason == StopReason.OUTPUT_TOO_LONG:
                tokens_used = input_tokens + output_tokens if input_tokens and output_tokens else None
                self._log.warning("LLM response truncated due to hitting max_tokens_limit")

                if retry_count == MAX_RETRIES:
                    if params.raise_exceptions:
                        raise OutputTooLongError(tokens_used, params.max_tokens, params.model)

                    return LLMResponse(response=None, input_tokens=input_tokens, output_tokens=output_tokens)

                if tokens_used is not None:
                    params.max_tokens = tokens_used + 100
                    self._log = self._log.bind(max_tokens=params.max_tokens)
                    self._log.info("retry with adjusted amount of tokens based on used tokens")
                else:
                    params.max_tokens = int(1.5 * params.max_tokens)
                    self._log = self._log.bind(max_tokens=params.max_tokens)
                    self._log.info("retry with multiplied amount of tokens")
                retry_count += 1
            else:
                break

        response_str = self._get_response_str(raw_response)
        response_str = self._maybe_remove_reasoning(response_str)

        if not response_str:
            response = None
        elif params.return_json:
            response = self._decode_json(response_str, params.raise_exceptions)
        else:
            response = response_str

        return LLMResponse(
            response=response,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
        )

    @contextmanager
    def _get_response_traced(
        self,
        headers: LLMHeaders,
        prompt: ChatCompletionPrompt | Message,
        params: LLMRequestParams,
        call_origin: str,
        additional_params: dict | None,
    ) -> Generator[LLMResponse, None, None]:
        self._set_up_tracers(headers, additional_params)
        self._trace_request(prompt, params, call_origin)
        result: LLMResponse = LLMResponse.create_empty()
        try:
            result = self.get_response(prompt, params, call_origin, additional_params)
            yield result
        finally:
            self._trace_response(result, call_origin)
            self._clear_tracers()

    def _trace_request(
        self, prompt: ChatCompletionPrompt | Message, params: LLMRequestParams, call_origin: str
    ) -> None:
        for tracer in self._tracers:
            tracer.trace_request(prompt, params, call_origin)

    def _trace_raw_response(self, response: LLM_RESPONSE, cache_used: bool, call_origin: str) -> None:
        for tracer in self._tracers:
            tracer.trace_raw_response(response, cache_used, call_origin)

    def _trace_response(self, response: LLMResponse, call_origin: str) -> None:
        for tracer in self._tracers:
            tracer.trace_response(response.response, call_origin)

    def _trace_error(self, error: Exception) -> None:
        for tracer in self._tracers:
            tracer.trace_error(error)

    def _set_up_tracers(self, headers: LLMHeaders, additional_params: dict | None = None) -> None:
        self._tracers = [AutoblocksLLMTracer(headers, additional_params)]

    def _clear_tracers(self) -> None:
        for tracer in self._tracers:
            tracer.reset()

        self._tracers = []

    def _get_response_cached(
        self, prompt: ChatCompletionPrompt | Message, params: LLMRequestParams
    ) -> tuple[LLM_RESPONSE | None, bool]:
        cache_key = f"{prompt}-{params}"
        if cached_value := self.cache_client.get_from_cache(
            cache_key, None, getattr(self.cache_client, "configuration", None)
        ):
            self._log.bind(cache_used=True)
            self._log.info("Using cached response")
            return cached_value, True
        else:
            self._log = self._log.bind(cache_used=False)
            response = self._get_response(prompt, params)
            if response:
                self.cache_client.add_to_cache(cache_key, response, get_cache_timeout())
            return response, False

    @classmethod
    def _record_usage_metric(
        cls,
        input_tokens: int,
        output_tokens: int,
        model_name: str,
        call_origin: str,
        is_batch_request: bool = False,
        input_cost: float | None = None,
        output_cost: float | None = None,
    ) -> None:
        lambda_metric(
            "llm_clients.usage",
            input_tokens,
            tags=[
                f"model_name:{model_name}",
                f"call_origin:{call_origin}",
                "token_type:input",
                f"is_batch_request:{is_batch_request}",
            ],
        )
        lambda_metric(
            "llm_clients.usage",
            output_tokens,
            tags=[
                f"model_name:{model_name}",
                f"call_origin:{call_origin}",
                "token_type:output",
                f"is_batch_request:{is_batch_request}",
            ],
        )
        if input_cost is not None:
            lambda_metric(
                "llm_clients.cost",
                input_cost,
                tags=[
                    f"model_name:{model_name}",
                    f"call_origin:{call_origin}",
                    "cost_type:input",
                    f"is_batch_request:{is_batch_request}",
                ],
            )

        if output_cost is not None:
            lambda_metric(
                "llm_clients.cost",
                output_cost,
                tags=[
                    f"model_name:{model_name}",
                    f"call_origin:{call_origin}",
                    "cost_type:output",
                    f"is_batch_request:{is_batch_request}",
                ],
            )

    def _decode_json(self, json_str: str, raise_exceptions: bool) -> dict | None:
        # get the json part of the string that could be between ```json and ```
        json_substr = re.search(r"```json(.*?)```", json_str, re.DOTALL)
        if json_substr:
            json_str = json_substr.group(1).strip()
        try:
            json_str = clean_control_characters(json_str)
            loaded_json = json.loads(json_str)
            return clean_json_value(loaded_json)
        except json.JSONDecodeError as e:
            self._log.warning("GPT JSON response is not valid JSON", response=json_str)
            if raise_exceptions:
                raise e
            self._log.warning("Failed to decode JSON GPT response", exc_info=True)
            return None

    def _additional_exception_handler(self, e: Exception) -> None:
        self._trace_error(e)

    def _process_messages(self, messages: list[Message]) -> list[Message]:
        """Returns a copy of the messages so different clients can modify them if needed"""
        return deepcopy(messages)

    def _get_prompt_messages(self, prompt: ChatCompletionPrompt | Message) -> list[Message]:
        messages = prompt.messages if isinstance(prompt, ChatCompletionPrompt) else [prompt]
        messages = [m for m in messages if not isinstance(m.content, FileContent)]
        return self._process_messages(messages)

    def _get_prompt_files(self, prompt: ChatCompletionPrompt | Message) -> list[FileContent]:
        messages = prompt.messages if isinstance(prompt, ChatCompletionPrompt) else []
        files = [m.content for m in messages if isinstance(m.content, FileContent)]
        return files

    def _maybe_remove_reasoning(self, response_str: str | None) -> str | None:
        return re.sub(r"<think>.*?</think>", "", response_str, flags=re.DOTALL).strip() if response_str else None


def make_llm_request(
    llm_clients: list[LLMClient],
    params: list[LLMRequestParams],
    prompt: ChatCompletionPrompt,
    retry_strategy: LLMRetryStrategy = LLMRetryStrategy.RETRY_ALL,
    retries: int = 2,
    raise_exceptions: bool = True,
    allow_none: bool = False,
    call_origin: str | None = None,
    headers: LLMHeaders = None,
    additional_params: dict | None = None,
) -> tuple[LLMResponse, LLMHeaders]:
    """
    Makes a request to the LLM API using the provided models and prompt

    Args:
        llm_clients: List of clients to use for the request
        params: List of parameters to use for the request
        prompt: Prompt to send to the LLM API
        retry_strategy: Strategy to use for retries
        retries: Number of retries to use
        raise_exceptions: Whether to raise exceptions or return None and log errors
        allow_none: Whether None is a possible response, if not the next model will be used to try and get the response
        call_origin: Origin of the caller to include in logging
        headers: Tracing headers to use, if any
        additional_params: Additional parameters to log and include in traces

    Returns:
        Response from the LLM API in the desired format
    """
    from llm_clients_core.claude_client import ClaudeLLMClient
    from llm_clients_core.gpt_llm_clients import OpenAIGPTLLMClient

    log = get_logger()
    if additional_params:
        log = log.bind(**additional_params)

    try:
        if not call_origin:
            call_origin = "unknown"

        clients_and_params = []
        models = [p.model for p in params]
        organization_id = None if not additional_params else additional_params.get("organization_id")
        organization = ExistingOrganizations(organization_id) if organization_id else None
        for request_params in params:
            if not request_params.model.is_organization_allowed(organization):
                logger.warning(
                    "Requested model not allowed for given organization",
                    model=request_params.model,
                    organization=organization,
                )
                continue
            client_found = False
            for client in llm_clients:
                if client.can_use_model(request_params.model):
                    client_found = True
                    request_params.raise_exceptions = True

                    use_open_ai_for_claude = FeatureFlagsClient.is_feature_enabled_in_user_context(
                        FeatureType.OPEN_API_FOR_CLAUDE
                    )

                    if request_params.model in [
                        LLMModel.CLAUDE_3_OPUS,
                        LLMModel.CLAUDE_3_SONNET,
                        LLMModel.CLAUDE_3_HAIKU,
                        LLMModel.CLAUDE_3_5_SONNET,
                        LLMModel.CLAUDE_3_5_HAIKU,
                        LLMModel.CLAUDE_3_7_SONNET,
                    ]:
                        if isinstance(client, ClaudeLLMClient) and use_open_ai_for_claude:
                            continue
                        if isinstance(client, OpenAIGPTLLMClient) and not use_open_ai_for_claude:
                            continue
                    clients_and_params.append((client, request_params))

            if not client_found:
                log.warning("No client available for the requested model", model=request_params.model)

        if not clients_and_params:
            log.error("No client available for any of the requested models", models=models)

            if raise_exceptions:
                raise NoCompatibleClientsError(models=models)

            return LLMResponse.create_empty(), headers

        exception = None
        if retry_strategy == LLMRetryStrategy.RETRY_ALL:
            try:
                return _run_all_models(
                    clients_and_params, prompt, call_origin, allow_none, headers, additional_params, log
                )
            except Exception as e:
                exception = e

        else:
            for client, params in clients_and_params:
                try:
                    response, headers = retry_openai(
                        client.get_traced_response,
                        prompt,
                        params,
                        call_origin,
                        retries=retries,
                        raise_exceptions=raise_exceptions,
                        headers=headers,
                        additional_params=additional_params,
                    )
                    if response is not None or allow_none:
                        return response, headers
                    else:
                        log.warning("No response from LLM, using next model if possible")
                except Exception as e:
                    exception = e

        if exception:
            if raise_exceptions:
                raise exception
            else:
                log.warning("Failed to get response from LLM API due to error", exc_info=exception)
                return LLMResponse.create_empty(), headers

        log.warning("Failed to get response from LLM API")
        return LLMResponse.create_empty(), headers
    except Exception as e:
        log.warning("Failed to make request to LLM API", exc_info=e)
        if raise_exceptions:
            raise e
        else:
            return LLMResponse.create_empty(), headers


@retry_openai
def _run_all_models(
    clients_and_params: list[tuple[LLMClient, LLMRequestParams]],
    prompt: ChatCompletionPrompt,
    call_origin: str,
    allow_none: bool = False,
    headers: LLMHeaders = None,
    additional_params: dict | None = None,
    log: BoundLogger = get_logger(),
) -> tuple[LLMResponse, LLMHeaders]:
    exception = None
    for client, params in clients_and_params:
        try:
            response, headers = client.get_traced_response(prompt, params, call_origin, headers, additional_params)
            if response is not None or allow_none:
                return response, headers
            else:
                log.warning("No response from LLM, using next model if possible")
        except Exception as e:
            exception = e

    if exception:
        raise exception

    return LLMResponse.create_empty(), None
