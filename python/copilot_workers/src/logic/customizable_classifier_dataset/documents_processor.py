import random
from collections import defaultdict
from typing import TYPE_CHECKING

from common.clients.facts import <PERSON>acts<PERSON>lient
from datascience_common.models.customizable_classifiers.dataset import (
    CustomizableClassifierDataset,
)
from datascience_common.models.dataset import EntityDataset
from infrastructure_common.logging import get_logger
from static_common.enums.classification import InputProcessingType
from static_common.enums.parent import ParentType
from structlog.stdlib import BoundLogger

from src.clients.ers_v3 import ERSV3<PERSON>lient
from src.logic.customizable_classifier_dataset.input_type_processor import (
    DatasetMetadata,
    InputTypeProcessor,
    InputTypeProcessorResult,
)
from src.logic.customizable_classifier_dataset.task_dataset_creator import InputType

if TYPE_CHECKING:
    from copilot_client_v3 import Submission, SubmissionBusiness
    from facts_client.model.document import Document


class DocumentsProcessor(InputTypeProcessor):
    def __init__(
        self, logger: BoundLogger, input_type: InputType, facts_client: FactsClient, ers_client: ERSV3Client
    ) -> None:
        self._logger = logger or get_logger()
        self._input_type = input_type
        self._facts_client = facts_client
        self._ers_client = ers_client

    def process(self, submissions: list["Submission"], max_dataset_size: int) -> list[InputTypeProcessorResult]:
        entity_id_to_documents: dict[str, list["Document"]] = defaultdict(list)
        entity_id_to_submission_id: dict[str, str] = {}
        for submission in submissions:
            submission_businesses: list[SubmissionBusiness] = submission.businesses
            if submission_businesses is not None:
                documents: list["Document"] = self._facts_client.get_all_documents(
                    parent_ids=[
                        submission_business.business_id
                        for submission_business in submission_businesses
                        if submission_business.business_id is not None
                    ],
                    document_type_id=self._input_type,
                    parent_type=ParentType.BUSINESS.value,
                    expand=["body", "parent_id"],
                )
                for document in documents:
                    entity_id_to_documents[document.parent_id].append(document)
                    entity_id_to_submission_id[document.parent_id] = str(submission.id)

        self._logger.info("Choosing random entities for dataset creation", entity_count=len(entity_id_to_documents))
        entity_ids_in_random_order = random.sample(
            list(entity_id_to_documents.keys()), min(max_dataset_size, len(entity_id_to_documents))
        )

        entity_datasets: list[EntityDataset] = []
        datasets_metadata: list[DatasetMetadata] = []
        for entity_id in entity_ids_in_random_order:
            documents = entity_id_to_documents[entity_id]

            entity = self._ers_client.get_entity(entity_id)
            if entity is None:
                continue

            entity_dataset = EntityDataset(
                entity=entity,
                documents=documents,
            )
            entity_dataset.make_serializable()  # type: ignore[no-untyped-call]

            entity_datasets.append(entity_dataset)
            datasets_metadata.append(
                DatasetMetadata(
                    submission_id=entity_id_to_submission_id.get(entity_id, None),
                    entity_id=entity_id,
                )
            )
            if len(entity_datasets) >= max_dataset_size:
                break

        return [
            InputTypeProcessorResult(
                customizable_classifier_datasets=[
                    CustomizableClassifierDataset(
                        input_processing_type=InputProcessingType.OCR_TEXT,
                        dataset=entity_dataset,
                    )
                ],
                dataset_metadata=dataset_metadata,
            )
            for entity_dataset, dataset_metadata in zip(entity_datasets, datasets_metadata)
        ]
