import random
from typing import TYPE_CHECKING, Optional

from common.clients.copilot_v3_client import CopilotV3Client
from datascience_common.logic.customizable_classifiers_v2.dataset_providers.factory import (
    get_dataset_creator,
)
from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
    InputSource,
)
from datascience_common.models.customizable_classifiers.dataset import (
    CustomizableClassifierDataset,
)
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)
from infrastructure_common.logging import get_logger
from static_common.enums.classification import InputProcessingType
from static_common.models.file_onboarding import OnboardedFile
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
from structlog.stdlib import BoundLogger

from src.logic.customizable_classifier_dataset.input_type_processor import (
    DatasetMetadata,
    InputTypeProcessor,
    InputTypeProcessorResult,
)
from src.logic.customizable_classifier_dataset.task_dataset_creator import InputType

if TYPE_CHECKING:
    from copilot_client_v3 import File, ProcessedFile, Submission


lean_onboarded_file_schema = LeanOnboardedFileSchema()


class FilesProcessor(InputTypeProcessor):
    def __init__(self, logger: BoundLogger, input_type: InputType, copilot_v3_client: CopilotV3Client) -> None:
        self._logger = logger or get_logger()
        self._input_type = input_type
        self._copilot_v3_client = copilot_v3_client

    def _get_chosen_files(
        self, submissions: list["Submission"], max_dataset_size: int
    ) -> list[CopilotFileProcessingContext]:
        chosen_files: list[CopilotFileProcessingContext] = []

        for submission in submissions:
            files: list["File"] = submission.files

            for file in files:
                if file.file_type != self._input_type:
                    continue

                processed_file: Optional["ProcessedFile"] = self._copilot_v3_client.get_processed_file_by_file_id(
                    file_id=file.id
                )

                if processed_file is None:
                    continue

                onboarded_file: OnboardedFile = lean_onboarded_file_schema.load(processed_file.processed_data)
                copilot_file_processing_context = CopilotFileProcessingContext(
                    input_source=InputSource.COPILOT_FILE,
                    submission_id=submission.id,
                    file=file,
                    file_classification=file.classification,
                    processed_data=onboarded_file,
                    input_id=file.id,
                    organization_id=file.organization_id,
                )
                chosen_files.append(copilot_file_processing_context)

        self._logger.info(
            "Choosing files for dataset creation", input_type=self._input_type, total_files=len(chosen_files)
        )
        chosen_files = random.sample(chosen_files, min(max_dataset_size, len(chosen_files)))

        return chosen_files

    def process(self, submissions: list["Submission"], max_dataset_size: int) -> list[InputTypeProcessorResult]:
        chosen_files: list[CopilotFileProcessingContext] = self._get_chosen_files(
            submissions=submissions, max_dataset_size=max_dataset_size
        )

        resources = CustomizableClassifierPipelineResources()
        input_type_processor_results: list[InputTypeProcessorResult] = []
        for context in chosen_files:
            datasets: list[CustomizableClassifierDataset] = []
            for input_processing_type in (InputProcessingType.OCR_TEXT, InputProcessingType.KV_PAIRS):
                dataset_creator = get_dataset_creator(
                    input_processing_type=input_processing_type,
                    context=context,
                    log=self._logger,
                )

                if dataset_creator is None:
                    continue

                try:
                    dataset = dataset_creator.create_dataset(
                        context=context,
                        input_processing_type=input_processing_type,
                        resources=resources,
                    )
                    if dataset is not None:
                        dataset.dataset.make_serializable()
                        datasets.append(dataset)
                    else:
                        self._logger.warning(
                            "Dataset creator returned None",
                            input_processing_type=input_processing_type,
                            context=context,
                        )
                except Exception as e:
                    self._logger.error("Error creating dataset", error=str(e), context=context)

            input_type_processor_results.append(
                InputTypeProcessorResult(
                    customizable_classifier_datasets=datasets,
                    dataset_metadata=DatasetMetadata(
                        submission_id=str(context.submission_id),
                        file_id=str(context.file.id),
                    ),
                )
            )

        return input_type_processor_results
