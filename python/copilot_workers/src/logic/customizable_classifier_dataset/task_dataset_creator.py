import uuid
from datetime import datetime
from typing import TYPE_CHECKING
from uuid import UUID

from common.clients.cache import S3<PERSON>ache<PERSON>lient
from common.clients.copilot_v3_client import CopilotV3<PERSON>lient
from common.clients.facts import <PERSON><PERSON><PERSON>lient
from copilot_client_v3 import (
    CreateTaskDatasetRequest,
    File,
    TaskDatasetInputWithGroundTruth,
)
from facts_client.model.document import Document
from infrastructure_common.logging import get_logger
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.file_type import FileType
from structlog.stdlib import BoundLogger

from src.logic.customizable_classifier_dataset.input_type_processor import (
    InputTypeProcessor,
    InputTypeProcessorResult,
)

if TYPE_CHECKING:
    from copilot_client_v3 import FilterRule, Submission


InputType = FileType | DocumentTypeID

SubmissionDatasetInput = list["File"] | list["Document"]
S3_FOLDER_PATH = "customizable_classifier_datasets"


class TaskDatasetCreator:
    def __init__(
        self,
        copilot_v3_client: Copi<PERSON>V3<PERSON>lient,
        facts_client: FactsClient,
        s3_client: S3CacheClient,
        max_dataset_size: int,
        input_type_processor: InputTypeProcessor,
        input_type: InputType,
        logger: BoundLogger | None = None,
    ) -> None:
        self._copilot_v3_client = copilot_v3_client
        self._facts_client = facts_client
        self._s3_client = s3_client
        self._max_dataset_size = max_dataset_size
        self._input_type_processor = input_type_processor
        self._input_type = input_type
        self._logger = logger or get_logger()

    def _upload_to_s3(self, input_type_processor_result: InputTypeProcessorResult) -> str | None:
        dataset_id = f"{datetime.now().strftime('%Y-%m-%d-%H-%M')}-{uuid.uuid4()}"
        s3_key = f"{S3_FOLDER_PATH}/{dataset_id}.json"

        try:
            self._s3_client.add_to_cache(key=s3_key, value=input_type_processor_result, expiration_seconds=0)
            self._logger.info("Successfully uploaded dataset to S3", s3_key=s3_key)
            return s3_key
        except Exception as e:
            self._logger.exception(
                "Failed to upload dataset to S3",
                s3_key=s3_key,
                error=str(e),
            )
            return None

    def _upload_to_s3_and_prepare_request_body(
        self,
        classifier_id: str,
        input_type_processor_results: list[InputTypeProcessorResult],
    ) -> "CreateTaskDatasetRequest":
        inputs: list[TaskDatasetInputWithGroundTruth] = []

        for input_type_processor_result in input_type_processor_results:
            s3_key = self._upload_to_s3(input_type_processor_result=input_type_processor_result)
            if s3_key is None:
                continue
            inputs.append(
                TaskDatasetInputWithGroundTruth(
                    input={
                        "datasets_s3_key": s3_key,
                        "file_id": input_type_processor_result.dataset_metadata.file_id,
                        "entity_id": input_type_processor_result.dataset_metadata.entity_id,
                        "submission_id": input_type_processor_result.dataset_metadata.submission_id,
                    },
                    ground_truth=None,
                )
            )

        return CreateTaskDatasetRequest(
            description=f"Task dataset for CC {classifier_id}",
            processing_type=str(self._input_type),
            output_type="customizable_classifier",
            inputs=inputs,
        )

    def create(
        self,
        organization_ids: list[int],
        classifier_id: str,
        filter_rules: list["FilterRule"],
        created_at_from: str | None = None,
        created_at_to: str | None = None,
        submissions_limit: int = 10,
    ) -> UUID | None:
        try:
            submissions: list[Submission] = []
            for organization_id in organization_ids:
                submissions.extend(
                    self._copilot_v3_client.copilot_api.get_submissions_for_dataset(
                        {
                            "organization_id": organization_id,
                            "filter_rules": filter_rules,
                            "required_file_types": [self._input_type] if isinstance(self._input_type, FileType) else [],
                            "created_at_to": created_at_to,
                            "created_at_from": created_at_from,
                            "limit": submissions_limit,
                        }
                    ).submissions
                )

            self._logger.info("Downloaded submissions for dataset creation", n_submissions=len(submissions))

            input_type_processor_results: list[InputTypeProcessorResult] = self._input_type_processor.process(
                submissions=submissions,
                max_dataset_size=self._max_dataset_size,
            )

            create_dataset_request = self._upload_to_s3_and_prepare_request_body(
                input_type_processor_results=input_type_processor_results, classifier_id=classifier_id
            )

            if not create_dataset_request.inputs:
                self._logger.error(
                    "Dataset is empty",
                    organization_ids=organization_ids,
                    classifier_id=classifier_id,
                    input_type=self._input_type,
                    filter_rules=filter_rules,
                    created_at_from=created_at_from,
                    created_at_to=created_at_to,
                )
                return None

            result = self._copilot_v3_client.copilot_api.create_dataset_with_inputs_and_ground_truths(
                create_dataset_request
            )
            return UUID(result.id)
        except Exception as e:
            self._logger.exception(
                "Failed to create dataset",
                classifier_id=classifier_id,
                error=str(e),
            )
            return None
