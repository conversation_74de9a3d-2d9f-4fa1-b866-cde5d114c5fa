from datetime import datetime, timedelta
from uuid import UUID

from pydantic import BaseModel


class CreateCustomizableClassifierDatasetInput(BaseModel):
    classifier_id: str
    classifier_config_id: str
    submissions_limit: int = 100
    created_at_from: str = (datetime.now() - timedelta(days=30 * 6)).isoformat()
    created_at_to: str = datetime.now().isoformat()


class CreateCustomizableClassifierDatasetOutput(BaseModel):
    task_dataset_id: UUID | None
