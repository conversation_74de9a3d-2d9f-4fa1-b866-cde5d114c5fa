from dataclasses import dataclass
from typing import TYPE_CHECKING, Protocol

from datascience_common.models.customizable_classifiers.dataset import (
    CustomizableClassifierDataset,
)

if TYPE_CHECKING:
    from copilot_client_v3 import Submission


@dataclass(frozen=True)
class DatasetMetadata:
    submission_id: str | None = None
    entity_id: str | None = None
    file_id: str | None = None


@dataclass(frozen=True)
class InputTypeProcessorResult:
    customizable_classifier_datasets: list[CustomizableClassifierDataset]
    dataset_metadata: DatasetMetadata


class InputTypeProcessor(Protocol):
    def process(self, submissions: list["Submission"], max_dataset_size: int) -> list[InputTypeProcessorResult]:
        ...
