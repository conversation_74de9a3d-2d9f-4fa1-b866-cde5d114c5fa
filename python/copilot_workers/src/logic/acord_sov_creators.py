from collections import OrderedDict, defaultdict
from dataclasses import dataclass
from typing import Any, Callable, List, Optional

from datascience_common.fact_subtypes.field_subtype_selection.single_field_subtype_selection import (
    SingleFieldSubtypeSelection,
)
from infrastructure_common.logging import get_logger
from ordered_set import OrderedSet
from static_common.constants import (
    PREMISES_INFO_FILE_SUFFIX,
    SCHEDULE_OF_HAZARDS_FILE_SUFFIX,
)
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.enum import StrEnum
from static_common.enums.fields import FieldType
from static_common.enums.file_type import FileType
from static_common.enums.parent import ParentType
from static_common.enums.valuation_method import ValuationMethod
from static_common.models.file_onboarding import (
    Acord126TransientData,
    Acord140TransientData,
    PremisesInformationTableRow,
    PremisesInformationTableWithEntity,
    ResolvedDataField,
    ResolvedDataValue,
)

from src.logic.facts_matching_and_normalization.utils import get_fact_subtypes
from src.logic.schedule_of_hazards.premium_basis import (
    normalize_premium_basis,
    update_hazard_dict,
)

logger = get_logger()


@dataclass
class AcordSovProperties:
    file_suffix: str
    csv_rows_creator: Callable[[Any], List[List[str | None]]]


class AcordSovType(StrEnum):
    PREMISES_INFO = "PREMISS_INFO"
    SCHEDULE_OF_HAZARDS = "SCHEDULE_OF_HAZARDS"


def _maybe_get_fact_subtype_for_subject_of_insurance(
    table_row: PremisesInformationTableRow, single_field_subtype_selection: SingleFieldSubtypeSelection
) -> str:
    """Get fact subtype for subject of insurance"""
    if not table_row.subject_of_insurance or not table_row.amount:
        return ""
    field = ResolvedDataField(
        name=table_row.subject_of_insurance,
        value_type=FieldType.NUMBER,
        values=[ResolvedDataValue(value=table_row.amount)],
    )

    single_field_subtype_selection.assign_fact_subtypes(
        mutable_fields=[field],
        parent_type=ParentType.BUSINESS,
        file_type=FileType.SOV,
        reassign_name=True,
    )
    return field.name


def _calculate_amount(rows: list[PremisesInformationTableRow]) -> float:
    rows_per_risk = defaultdict(list)
    for row in rows:
        rows_per_risk[row.causes_of_loss].append(row)
    amounts = []
    for risk, rows in rows_per_risk.items():
        valuation_method = ValuationMethod.try_parse_str(rows[0].valuation)  # type: ignore[arg-type]
        amount = sum(row.amount for row in rows)  # type: ignore[misc]
        amounts.append((amount, valuation_method))
    primary_amounts = [a for a, vm in amounts if vm in {ValuationMethod.ACV, ValuationMethod.RC}]
    if primary_amounts:
        return max(primary_amounts)
    return max(a for a, _ in amounts)


def get_premises_info_csv_rows_with_header(
    acords_transient_data: list[Acord140TransientData],
) -> List[List[Optional[str]]]:
    """Takes a list of PremisesInformationTableWithEntity and converts it to csv rows so that the header is build
    using the property subject_of_insurance as header name and amount as value"""
    fact_subtypes = get_fact_subtypes()
    single_field_subtype_selection = SingleFieldSubtypeSelection.create(
        fact_subtypes=fact_subtypes,
        logger=logger,
    )
    premises_info_tables: list[PremisesInformationTableWithEntity] = []
    for acord_transient_data in acords_transient_data:
        premises_info_tables.extend(acord_transient_data.premises_info or [])
    result = []
    data_as_dict: dict[str, list[Any]] = OrderedDict()
    entity_info_dict: dict[str, list[Any]] = OrderedDict()
    entity_info_dict.update(
        {
            "Name": [],
            "Address": [],
            "Location Number": [],
            "Building Number": [],
        }
    )
    number_rows = 0
    for pi in premises_info_tables:
        pi_dict: dict[str, list[PremisesInformationTableRow]] = OrderedDict()
        valid_rows = (tr for tr in pi.premises_information_table if tr.subject_of_insurance and tr.amount is not None)
        for table_row in valid_rows:
            key = _maybe_get_fact_subtype_for_subject_of_insurance(
                table_row=table_row, single_field_subtype_selection=single_field_subtype_selection
            )
            if key in pi_dict:
                pi_dict[key].append(table_row)
            else:
                pi_dict[key] = [table_row]
        if not pi_dict:
            continue
        pi_dict_values: dict[str, float] = OrderedDict()
        for key, rows in pi_dict.items():
            pi_dict_values[key] = _calculate_amount(rows)

        for key, val in pi_dict_values.items():
            values = data_as_dict.get(key, [None] * number_rows)
            values.append(val)
            data_as_dict[key] = values

        for key, v in data_as_dict.items():
            if key in pi_dict_values:
                continue
            v.append(None)

        entity_info_dict["Name"].append(pi.requested_name)
        entity_info_dict["Address"].append(pi.requested_address)
        entity_info_dict["Location Number"].append(pi.location_number)
        entity_info_dict["Building Number"].append(pi.building_number)
        number_rows += 1

    if number_rows > 0:
        entity_info_dict.update(data_as_dict)
        result.append(list(entity_info_dict.keys()))
        result.extend(list(x) for x in list(zip(*entity_info_dict.values())))

    return result  # type: ignore[return-value]


def get_hazards_csv_rows_with_header(acords_transient_data: list[Acord126TransientData]) -> List[List[Optional[str]]]:
    schedule_of_hazards = []
    for acord_transient_data in acords_transient_data:
        schedule_of_hazards.extend(acord_transient_data.schedule_of_hazards or [])
    result = []
    # first group rows by name, address, location and create dicts from them
    csv_header: OrderedSet[str] = OrderedSet()
    csv_header.update(["Name", "Address", "Location Number", "Building Number"])
    id_to_hazard_dict: dict[str, dict[str, Any]] = OrderedDict()
    for hazard in schedule_of_hazards:
        if not hazard.requested_address or not hazard.exposure:
            # address is required and exposure
            continue
        if not hazard.classification and not hazard.premium_basis:
            # we need one of this to compute a header
            continue
        normalize_premium_basis(mutable_hazard=hazard)
        generated_id = (
            f"{hazard.requested_name}-{hazard.requested_address}-{hazard.location_number}-{hazard.building_number}"
        )
        hazard_dict = id_to_hazard_dict.get(generated_id, OrderedDict())
        hazard_dict["Name"] = hazard.requested_name
        hazard_dict["Address"] = hazard.requested_address
        hazard_dict["Location Number"] = hazard.location_number
        hazard_dict["Building Number"] = hazard.building_number

        classification_key = update_hazard_dict(hazard=hazard, hazard_dict=hazard_dict)
        csv_header.add(classification_key)
        id_to_hazard_dict[generated_id] = hazard_dict

    result.append(list(csv_header))
    for hazard_dict in id_to_hazard_dict.values():
        row = []
        for column_name in csv_header:
            row.append(hazard_dict.get(column_name))
        result.append(row)  # type: ignore

    if not id_to_hazard_dict.values():
        return []

    return result  # type: ignore[return-value]


SOV_CREATORS: dict[AcordSovType, AcordSovProperties] = {
    AcordSovType.PREMISES_INFO: AcordSovProperties(
        PREMISES_INFO_FILE_SUFFIX,
        get_premises_info_csv_rows_with_header,
    ),
    AcordSovType.SCHEDULE_OF_HAZARDS: AcordSovProperties(
        SCHEDULE_OF_HAZARDS_FILE_SUFFIX,
        get_hazards_csv_rows_with_header,
    ),
}

CLASSIFICATION_TO_SOV_TYPE: dict[str, AcordSovType] = {
    ClassificationDocumentType.ACORD_139.value: AcordSovType.PREMISES_INFO,
    ClassificationDocumentType.ACORD_140.value: AcordSovType.PREMISES_INFO,
    ClassificationDocumentType.ACORD_126.value: AcordSovType.SCHEDULE_OF_HAZARDS,
    ClassificationDocumentType.ACORD_211.value: AcordSovType.SCHEDULE_OF_HAZARDS,
    ClassificationDocumentType.APPLIED_126.value: AcordSovType.SCHEDULE_OF_HAZARDS,
    ClassificationDocumentType.OFSCHHAZ.value: AcordSovType.SCHEDULE_OF_HAZARDS,
}
