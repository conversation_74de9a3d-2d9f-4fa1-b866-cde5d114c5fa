import hashlib
from collections import defaultdict
from dataclasses import dataclass
from functools import cached_property
from typing import Any
from uuid import UUID

import markdown
from common.clients.copilot_v3_client import CopilotV3Client
from common.utils.submission import extract_fni
from copilot_client_v3 import RequestedCoverage, Submission, SubmissionBusiness
from dataclasses_json import dataclass_json
from facts_client.model.inference_source import InferenceSource
from facts_client.model.summary import Summary
from infrastructure_common.logging import get_logger
from llm_common.clients.llm_client import LLMClient
from llm_common.models.llm_model import LLMModel
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.group import GroupID
from static_common.enums.parent import ParentType
from static_common.enums.source_types import SourceTypeID
from static_common.enums.summary_type import SummaryType
from static_common.models.openai import ChatCompletionPrompt
from static_common.models.summaries import OverviewSummaryMetadata
from static_common.schemas.openai import ChatCompletionPromptSchema
from static_common.schemas.summaries import OverviewSummaryMetadataSchema
from structlog.stdlib import BoundLogger

from src.clients.ers_v3 import ERSV3Client
from src.clients.facts import FactsClient
from src.clients.utils import get_call_origin
from src.logic.document_export.utils import (
    VALUE_EXTRACTOR_MAPPINGS,
    get_all_structure_facts,
)
from src.logic.llm import get_llm_response

SKIP_SUBTYPES = [
    FactSubtypeID.ADJUSTED_TIV.value,
]

PROMPT_TEMPLATE = """
You are tasked with creating a concise summary of an insurance quote request submission from one or more businesses and their associated property coverage details. This summary will help underwriters quickly evaluate key risk factors, with a specific focus on COPE (Construction, Occupancy, Protection, Exposure) information and Total Insured Value (TIV).

Analyze the provided information, paying special attention to COPE details:
1. Construction: Type of building materials, year built, number of stories, total square footage
2. Occupancy: How the building is used, type of business operations
3. Protection: Fire protection systems, security measures, proximity to fire station
4. Exposure: Surrounding risks, natural hazards, neighboring properties

Display the total TIV data if it is present, flag it if it is not. Highlight the number of Premises/Structures and put the total TIV and premises data in bold font. Only use "TIV" and other specific value data to calculate it. Include premises and structures in the count even if they don't have any value listed, but there is no need to mention the number of businesses.

Create a text summary of the property coverage request, highlighting the COPE information and TIV. Highlight 2-3 significant risk factors or concerns for underwriters to consider. Do not add any formatting or headers. Limit to 2 small paragraphs. ‘Key risk factors:’ (should be in bold) should be the second paragraph and should begin in a separate paragraph after a line break, that would improve readability.

Remember to focus on the most relevant information that will help underwriters quickly assess the risk. Be concise yet comprehensive in your summary.
""".strip()  # noqa


@dataclass
class OverviewSummarizationResult:
    submission_id: UUID
    summary: str
    summary_metadata: OverviewSummaryMetadata

    @cached_property
    def _summary_metadata_schema(self) -> OverviewSummaryMetadataSchema:
        return OverviewSummaryMetadataSchema()

    @cached_property
    def source(self) -> InferenceSource:
        return InferenceSource(
            source_type_id=SourceTypeID.INFERENCE,
            model_version=f"1.{self.prompt_uuid}",
            model_name=self.__class__.__name__,
        )

    @cached_property
    def prompt_uuid(self) -> UUID:
        # to be able to differentiate when small prompt changes:
        return UUID(hashlib.md5(PROMPT_TEMPLATE.encode("utf8", "surrogatepass")).hexdigest())

    @cached_property
    def default_id(self) -> UUID:
        return UUID(hashlib.md5(f"{self.submission_id}-overview_summary".encode("utf8", "surrogatepass")).hexdigest())

    def to_facts_document(self, existing_summary: Summary | None) -> Summary:
        document_id = existing_summary.id if existing_summary else str(self.default_id)
        return Summary(
            id=document_id,
            document_type_id=DocumentTypeID.SUMMARY,
            parent_id=str(self.submission_id),
            parent_type=ParentType.SUBMISSION,
            body=self.summary,
            summary_type=SummaryType.OVERVIEW_SUMMARY,
            summary_metadata=self._summary_metadata_schema.dump(self.summary_metadata),
            source=self.source,
        )


@dataclass_json
@dataclass
class OverviewSummaryUpdateResult:
    document_id: str | None = None
    issue: str | None = None


class OverviewSummaryProvider:
    def __init__(
        self,
        submission_id: UUID,
        organization_id: int,
        copilot_client: CopilotV3Client,
        facts_client: FactsClient,
        ers_client: ERSV3Client,
        llm_client: LLMClient,
        logger: BoundLogger | None = None,
    ):
        self.submission_id = submission_id
        self.organization_id = organization_id
        self.copilot_client = copilot_client
        self.facts_client = facts_client
        self.ers_client = ers_client
        self.llm_client = llm_client

        logger = logger or get_logger()
        self.logger = logger.bind(
            submission_id=submission_id,
            organization_id=organization_id,
        )

    def _get_prompt(
        self,
        coverage: RequestedCoverage,
        businesses: list[SubmissionBusiness],
        totals: dict[str, int],
        observations: dict[str, dict],
    ) -> ChatCompletionPrompt:
        coverage_dict = coverage.to_dict()
        del coverage_dict["id"]
        del coverage_dict["submission_id"]
        del coverage_dict["coverage"]
        coverage_str = "\n".join(f"{k}: {v}" for k, v in coverage_dict.items() if v).strip()

        totals_str = "\n".join(f"{k}: {v}" for k, v in totals.items()).strip()

        information_lines = []
        bdx = 1
        for bid in observations:
            business = next((b for b in businesses if b.business_id == bid), None)
            if not business:
                continue

            information_lines.append(f"Business {bdx}:")
            for pid in observations[bid]:
                pdx = 1
                information_lines.append(f"  - Premises {pdx}:")
                struct_info = self._get_structure_info(observations[bid][pid])
                sid = 1
                for struct in struct_info.values():
                    information_lines.append(f"    + Structure {sid}:")
                    for key, value in struct.items():
                        if key and value:
                            information_lines.append(f"      > {key}: {value}")
                    sid += 1
                pdx += 1
            information_lines.append("")
            bdx += 1
        information_str = "\n".join(information_lines).strip()

        prompt_sequence = {
            "messages": [
                {
                    "role": "system",
                    "content": PROMPT_TEMPLATE,
                },
                {
                    "role": "user",
                    "content": f"""
<coverage_request>
{coverage_str}
</coverage_request>

<totals>
{totals_str}
</totals>

<property_information>
{information_str}
</property_information>
""".strip(),
                },
            ]
        }
        return ChatCompletionPromptSchema().load(prompt_sequence)  # type: ignore

    def _get_structure_info(self, structure_facts: list) -> dict[str, dict[str, Any]]:
        if not structure_facts:
            return {}
        fact_sort_order: dict = {}
        structure_id_to_facts: dict[str, dict[str, Any]] = defaultdict(dict)
        for fact in sorted(structure_facts or [], key=lambda x: fact_sort_order.get(x.fact_subtype.id, 999)):
            try:
                value = VALUE_EXTRACTOR_MAPPINGS.get(fact.fact_subtype.fact_type_id, lambda x: x.observation.value)(
                    fact
                )
                structure_id_to_facts[fact.parent_id][fact.fact_subtype.display_name] = value
            except Exception:
                self.logger.info("Failed to extract value from fact", fact=fact.to_dict())

        return structure_id_to_facts

    def _get_structure_data(self, submission: Submission) -> dict:
        business_ids = [b.business_id for b in submission.businesses or [] if not b.hide_property_facts]
        fni = extract_fni(submission)
        entity_ids = [*business_ids, fni.business_id] if fni else business_ids
        entity_ids = list(set(entity_ids))
        all_entities = self.ers_client.get_entities_by_ids(entity_ids)
        entities = [all_entities[UUID(bid)] for bid in business_ids]

        premises_ids: list[str] = []
        for entity in entities:
            premises_ids.extend([p.premises_id for p in entity.premises or []])

        premises_facts = self.facts_client.get_facts_in_batches(
            parent_ids=premises_ids,
            parent_type=ParentType.PREMISES,
            organization_id=self.organization_id,
            submission_id=self.submission_id,
            group_ids=[
                GroupID.BUILDING_EXPOSURE,
                GroupID.BUILDING_INTEGRITY_RISK,
                GroupID.PROPERTY_CARD,
                GroupID.UNCLASSIFIED_FIRST_PARTY,
                GroupID.ESTIMATED_SALE_VALUE,
                GroupID.FIRE_RISK,
                GroupID.OPERATIONS_CARD,
                GroupID.PROPERTY_INSURANCE_VALUATION,
                GroupID.ROOF,
            ],
        )

        facts_construction_type = self.facts_client.get_facts_in_batches(
            parent_ids=premises_ids,
            parent_type=ParentType.PREMISES,
            organization_id=self.organization_id,
            submission_id=self.submission_id,
            fact_subtype_ids=[FactSubtypeID.CONSTRUCTION_CLASS],
        )
        premises_facts.extend(facts_construction_type)

        structure_facts = get_all_structure_facts(
            self.facts_client, entities, self.organization_id, str(self.submission_id)
        )
        for bid in structure_facts:
            for pid in structure_facts[bid]:
                # no structure data usually means the observations are on the premises level
                if not structure_facts[bid][pid]:
                    structure_facts[bid][pid] = []
                    for fact in premises_facts:
                        if fact.parent_id == pid:
                            structure_facts[bid][pid].append(fact)

        # filter out skip subtypes
        for bid in structure_facts:
            for pid in structure_facts[bid]:
                structure_facts[bid][pid] = [
                    fact for fact in structure_facts[bid][pid] if fact.fact_subtype.id not in SKIP_SUBTYPES
                ]

        return structure_facts

    def _get_existing_summary(self, submission_id: UUID) -> Summary | None:
        existing_summaries = self.facts_client.get_documents(
            parent_ids=[submission_id],
            parent_type=ParentType.SUBMISSION,
            document_type_id=DocumentTypeID.SUMMARY,
        )

        for summary in existing_summaries:
            if summary.summary_type == SummaryType.OVERVIEW_SUMMARY:
                return summary

        return None

    def update_overview_summary(self) -> OverviewSummaryUpdateResult:
        submission = self.copilot_client.get_submission(
            submission_id=self.submission_id, expand=["businesses", "coverages"], enrich_with_ers_data=True
        )
        if not submission:
            self.logger.warning("No submission found, deleting existing summary")
            existing_summary = self._get_existing_summary(self.submission_id)
            if existing_summary:
                self.facts_client.delete_document(existing_summary.id)
            return OverviewSummaryUpdateResult(issue="No submission found")

        property_coverage: RequestedCoverage | None = None
        for coverage in submission.coverages:
            if coverage.coverage.name == "property":
                property_coverage = coverage
                break
        if not property_coverage:
            self.logger.warning("No property coverage found in submission, deleting existing summary")
            existing_summary = self._get_existing_summary(self.submission_id)
            if existing_summary:
                self.facts_client.delete_document(existing_summary.id)
            return OverviewSummaryUpdateResult(issue="No property coverage found")

        observations = self._get_structure_data(submission)

        totals = {
            "Premises": 0,
            "Structures": 0,
            "TIV": 0,
        }
        p_visited = set()
        s_visited = set()
        for bid in observations:
            for pid in observations[bid]:
                premises = observations[bid][pid]
                if not premises:
                    continue

                if pid in p_visited:
                    continue
                p_visited.add(pid)

                totals["Premises"] += 1
                struct_info = self._get_structure_info(observations[bid][pid])
                for sid, struct in struct_info.items():
                    if not struct:
                        continue

                    if sid in s_visited:
                        continue
                    s_visited.add(sid)

                    totals["Structures"] += 1
                    if struct.get("TIV"):
                        try:
                            tiv = int(float(struct["TIV"].replace(",", "").replace("$", "")))
                            totals["TIV"] += tiv
                        except ValueError:
                            self.logger.warning("Failed to parse TIV value", struct=struct)

        response = None
        try:
            prompt = self._get_prompt(property_coverage, submission.businesses, totals, observations)
            response = get_llm_response(
                models=[LLMModel.CLAUDE_3_7_SONNET],
                chat_prompt=prompt,
                return_json=False,
                raise_exceptions=True,
                call_origin=get_call_origin(),
            )
        except Exception:
            self.logger.warning("Failed to get overview summary", exc_info=True)

        document_id = None
        existing_summary = self._get_existing_summary(self.submission_id)
        if response:
            res_md = markdown.markdown(str(response), extensions=["nl2br"]).replace("</p>", "</p><br>")
            summarization_result = OverviewSummarizationResult(
                submission_id=self.submission_id,
                summary=res_md,
                summary_metadata=OverviewSummaryMetadata(number_of_sources=len(observations)),
            )
            new_summary = summarization_result.to_facts_document(existing_summary)

            document = self.facts_client.create_or_replace_document_lite(new_summary)
            if document:
                document_id = document.id
        elif existing_summary:
            self.facts_client.delete_document(existing_summary.id)

        return OverviewSummaryUpdateResult(
            issue="Failed to store document in facts API" if not document_id else None,
            document_id=document_id,
        )
