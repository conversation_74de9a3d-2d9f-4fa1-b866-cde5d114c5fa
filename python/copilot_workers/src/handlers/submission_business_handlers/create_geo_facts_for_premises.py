from datetime import UTC, datetime
from typing import Any, NotRequired, TypedDict
from uuid import UUID

from common.datascience.enums import FreeTextExplanationType
from common.factories.observations.letter_grade import LetterGradeObservationFactory
from common.utils.documents import identify_document
from common.utils.logging import log_function_inputs
from entity_resolution_service_client_v3 import Entity, Premises
from facts_client.model.document import Document
from facts_client.model.document_id import DocumentId
from facts_client.model.flood_zone_observation import FloodZoneObservation
from facts_client.model.free_text import FreeText
from facts_client.model.free_text_explanation import FreeTextExplanation
from facts_client.model.letter_grade_observation import LetterGradeObservation
from facts_client.model.observation import Observation
from facts_client.model.source import Source
from facts_client.model.web_ingested_source import WebIngestedSource
from file_processing.utils import ParentType
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from sentry_sdk.integrations.serverless import serverless_function
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.entity import EntityPremisesType
from static_common.enums.explanation import ExplanationTypeID
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID
from static_common.enums.geo_event_type import GeoZoneType
from static_common.enums.grade import Grade
from static_common.enums.source_types import SourceTypeID, WebIngestedSourceTypeID

from src.clients.ers_v3 import ERSV3Client
from src.clients.facts import FactsClient
from src.handlers.handler_utils import (
    get_copilot_v3_client,
    get_ers_v3_client,
    get_facts_client,
)
from src.models.ers_geo import FloodZoneType, ZonesFeatureCollection

LOGGER = get_logger()

ZONE_TYPE_TO_GRADE = {
    FloodZoneType.A: Grade.F,
    FloodZoneType.AE: Grade.F,
    FloodZoneType.AH: Grade.F,
    FloodZoneType.AO: Grade.F,
    FloodZoneType.V: Grade.F,
    FloodZoneType.VE: Grade.F,
    FloodZoneType.X: Grade.B,
    FloodZoneType.AREA_NOT_INCLUDED: None,
}


NOW = datetime.now(UTC)


class Event(TypedDict):
    """Expected event structure, either of the fields submission_id or business_ids must be present."""

    submission_id: NotRequired[str | None]
    business_ids: NotRequired[list[str] | None]


def _create_letter_grade_observation(
    premises_id: str,
    fact_subtype_id: FactSubtypeID,
    grade: Grade,
    source: Source,
    explanation: FreeTextExplanation | None = None,
) -> LetterGradeObservation:
    observation = LetterGradeObservationFactory.create(
        fact_subtype_id=fact_subtype_id,
        parent_id=UUID(premises_id),
        parent_type=ParentType.PREMISES,
        published_at=datetime.now(UTC),
        source=source,
        grade=grade,
        score=None,
        text=None,
    )

    if explanation is not None:
        observation.free_text_explanations = [explanation]

    return observation


def create_floodzone_observation(
    lat_long: tuple[float, float], premises_id: str, ers_client: ERSV3Client
) -> tuple[list[Observation], list[Document]]:
    # fetch flood zones that contain the premises
    try:
        zones: ZonesFeatureCollection = ers_client.get_zones_near_points(
            centers=[lat_long],
            zone_types=[GeoZoneType.FLOODZONE],
            included_fields=["source"],
        )
    except Exception as e:
        LOGGER.warning("Error fetching flood zones", premises_id=premises_id, exc_info=e)
        return [], []

    matching_zone = next(
        (zone for zone in zones.features),
        None,
    )

    if not matching_zone:
        LOGGER.info("No flood zone found", premises_id=premises_id)
        return [], []

    zone_type = matching_zone.properties.data.zone_type if matching_zone else None

    if zone_type is None or matching_zone.properties.source is None:
        return [], []

    source = WebIngestedSource(
        source_type_id=SourceTypeID.WEB_INGESTED,
        web_ingestion_type=WebIngestedSourceTypeID.FEMA_NFHL,
        url=matching_zone.properties.source.url,
    )

    flood_zone_observation = FloodZoneObservation(
        parent_id=str(premises_id),
        parent_type=ParentType.PREMISES.value,
        fact_type_id=FactTypeID.FLOOD_ZONE.value,
        fact_subtype_id=FactSubtypeID.FLOOD_ZONE.value,
        flood_zone=zone_type.value,
        source=source,
    )

    grade = ZONE_TYPE_TO_GRADE.get(zone_type)
    if grade is None:
        return [flood_zone_observation], []

    document_body = (
        f"Premises is located in a flood zone of type {zone_type.value},"
        + f" which is classified as Grade {grade.value}."
    )

    free_text = FreeText(
        id=str(
            identify_document(
                DocumentTypeID.FREE_TEXT,
                ParentType.PREMISES,
                UUID(premises_id),
                document_body,
            )
        ),
        parent_id=premises_id,
        parent_type=ParentType.PREMISES.value,
        document_type_id=DocumentTypeID.FREE_TEXT.value,
        body=document_body,
        published_at=datetime.now(UTC).isoformat(),
        source=source,
    )

    explanation = FreeTextExplanation(
        explanation_type_id=ExplanationTypeID.FREE_TEXT,
        free_text_explanation_type=FreeTextExplanationType.CUSTOM,
        parent_id=premises_id,
        parent_type=ParentType.PREMISES,
        observation_id=None,
        document_id=str(free_text.id),
    )

    flood_risk_observation = _create_letter_grade_observation(
        premises_id=premises_id,
        fact_subtype_id=FactSubtypeID.FLOOD_RISK,
        grade=grade,
        explanation=explanation,
        source=source,
    )

    return [
        flood_zone_observation,
        flood_risk_observation,
    ], [free_text]


def create_geo_facts_for_a_single_premises(
    premises_entity: Premises,
    ers_client: ERSV3Client,
    facts_client: FactsClient,
) -> None:
    floodzone_observations, documents = create_floodzone_observation(
        lat_long=(premises_entity.latitude, premises_entity.longitude),
        premises_id=premises_entity.id,
        ers_client=ers_client,
    )

    created_documents: list[str] = []
    if documents:
        for document in documents:
            try:
                submitted_document: DocumentId = facts_client.create_or_replace_document_lite(document=document)
            except Exception as e:
                LOGGER.warning(
                    "Error submitting document",
                    premises_id=premises_entity.id,
                    document_id=document.id,
                    exc_info=e,
                )
                continue

            created_documents.append(submitted_document.id)

        LOGGER.info(
            "Explanation created",
            premises_id=premises_entity.id,
            document_ids=created_documents,
        )

    if not floodzone_observations:
        LOGGER.info("No flood zone observation created", premises_id=premises_entity.id)
        return

    appendded_submission_ids: list[str] = []
    for observation in floodzone_observations:
        # the explanation was not created skip the observation
        if (free_text_explanations := getattr(observation, "free_text_explanations", None)) and any(
            fe.document_id not in created_documents for fe in free_text_explanations
        ):
            continue

        appended_submition = facts_client.try_append_observation(observation=observation)

        if appended_submition is not None:
            appendded_submission_ids.append(appended_submition.id)

    LOGGER.info(
        "Floodzone observation created", premises_id=premises_entity.id, observation_ids=appendded_submission_ids
    )


@serverless_function
@log_function_inputs
@bind_lambda_logging_context
def create_geo_facts_for_premises(event: Event, _context: Any | None = None) -> dict[str, int] | None:
    ers_client: ERSV3Client = get_ers_v3_client()
    facts_client = get_facts_client()
    copilot_client = get_copilot_v3_client()

    submission_id = event.get("submission_id")
    received_business_ids = event.get("business_ids")

    business_ids: list[str] = received_business_ids or []

    if submission_id:
        submission = copilot_client.get_submission(submission_id, expand=["businesses"])

        submission_business_ids: set[str] = (
            {sb.business_id for sb in submission.businesses if sb.business_id and not sb.hide_property_facts}
            if submission
            else set()
        )

        business_ids.extend(submission_business_ids)

    if not business_ids:
        LOGGER.info(
            "No business ids provided or found in submission",
            submission_id=submission_id,
            received_business_ids=received_business_ids,
        )
        return {"status": 200}

    businesses: dict[UUID, Entity] = ers_client.get_entities_by_ids(
        entity_ids=list(business_ids),
    )

    premises: list[Premises] = [
        business_premises.premises
        for business in businesses.values()
        for business_premises in business.premises
        if business_premises.type == EntityPremisesType.PHYSICAL_ADDRESS
    ]

    if not premises:
        LOGGER.info(
            "No premises found for business ids",
            submission_id=submission_id,
            received_business_ids=received_business_ids,
        )
        return {"status": 200}

    for premises_entity in premises:
        create_geo_facts_for_a_single_premises(
            premises_entity=premises_entity,
            ers_client=ers_client,
            facts_client=facts_client,
        )

    return {"status": 200}


if __name__ == "__main__":
    ers_v3_client = get_ers_v3_client()
    facts_client = get_facts_client()

    # print(create_floodzone_observation((38.19319, -122.0571), "89ae4211-ef1d-4426-a46a-96977aea5ff6", ers_v3_client))
    create_geo_facts_for_premises(
        {
            "business_ids": [
                "0fc8c0db-1a1d-4b55-aa09-86fcbc4b7216",
            ]
        }
    )
