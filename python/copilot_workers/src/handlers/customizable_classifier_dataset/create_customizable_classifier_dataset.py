import os
from typing import TYPE_CHECKING

from common.clients.cache import S3Cache<PERSON>lient
from common.clients.client_factory import (
    get_copilot_v3_client,
    get_facts_client,
    get_s3_client,
)
from common.clients.copilot_v3_client import CopilotV3Client
from common.clients.facts import Facts<PERSON>lient
from common.utils.logging import log_function_result
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from sentry_sdk.integrations.serverless import serverless_function
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.file_type import FileType

from src.clients.ers_v3 import ERSV3Client
from src.handlers.handler_utils import get_ers_client
from src.logic.customizable_classifier_dataset.documents_processor import (
    DocumentsProcessor,
)
from src.logic.customizable_classifier_dataset.files_processor import FilesProcessor
from src.logic.customizable_classifier_dataset.input_type_processor import (
    InputTypeProcessor,
)
from src.logic.customizable_classifier_dataset.models import (
    CreateCustomizableClassifierDatasetInput,
    CreateCustomizableClassifierDatasetOutput,
)
from src.logic.customizable_classifier_dataset.task_dataset_creator import (
    InputType,
    TaskDatasetCreator,
)

if TYPE_CHECKING:
    from copilot_client_v3 import ClassifierConfig, CustomizableClassifierV2, FilterRule

MAX_DATASET_SIZE: int = 50
S3_BUCKET_NAME = f"kalepa-{os.environ['KALEPA_ENV']}-copilot-uploads-us-east-1"


@log_function_result
@serverless_function
@bind_lambda_logging_context
def handle(event: dict, context: dict) -> dict:
    logger = get_logger()
    request = CreateCustomizableClassifierDatasetInput.model_validate(event)
    logger = logger.bind(
        classifier_id=request.classifier_id,
    )

    copilot_v3_client: CopilotV3Client = get_copilot_v3_client()
    facts_client: FactsClient = get_facts_client()
    ers_client: ERSV3Client = get_ers_client()
    s3_client = S3CacheClient(
        prefix="customizable_classifier_datasets", bucket_name=S3_BUCKET_NAME, s3_client=get_s3_client()
    )
    customizable_classifier: CustomizableClassifierV2 = (
        copilot_v3_client.copilot_api.get_customizable_classifier_v2_by_id(id=request.classifier_id)
    )
    classifier_config: "ClassifierConfig" = copilot_v3_client.copilot_api.get_classifier_config(
        id=request.classifier_config_id
    )

    filter_rules: list[FilterRule] = customizable_classifier.filter_rules
    for rule in filter_rules:
        rule.values = [v.split(":")[1] if ":" in v else v for v in rule.values]

    organization_ids: list[int] = customizable_classifier.organization_ids

    if classifier_config.input_type in list(FileType):
        input_type: InputType = FileType(classifier_config.input_type)
        input_type_processor: InputTypeProcessor = FilesProcessor(
            logger=logger, input_type=input_type, copilot_v3_client=copilot_v3_client
        )
    elif classifier_config.input_type in list(DocumentTypeID):
        input_type = DocumentTypeID(classifier_config.input_type)
        input_type_processor = DocumentsProcessor(
            logger=logger, input_type=input_type, facts_client=facts_client, ers_client=ers_client
        )
    else:
        raise ValueError(f"Unsupported input type: {classifier_config.input_type}")

    task_dataset_creator = TaskDatasetCreator(
        copilot_v3_client=copilot_v3_client,
        facts_client=facts_client,
        s3_client=s3_client,
        max_dataset_size=MAX_DATASET_SIZE,
        input_type=input_type,
        input_type_processor=input_type_processor,
        logger=logger,
    )

    task_dataset_id = task_dataset_creator.create(
        classifier_id=customizable_classifier.id,
        organization_ids=organization_ids,
        filter_rules=filter_rules,
        created_at_from=request.created_at_from,
        created_at_to=request.created_at_to,
        submissions_limit=request.submissions_limit,
    )

    return CreateCustomizableClassifierDatasetOutput(
        task_dataset_id=task_dataset_id,
    ).model_dump(mode="json")
