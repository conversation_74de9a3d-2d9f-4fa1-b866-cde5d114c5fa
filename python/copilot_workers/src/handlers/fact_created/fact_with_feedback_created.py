from common.utils.logging import log_function_inputs
from events_common.kalepa_events import <PERSON><PERSON>paEvents
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from sentry_sdk.integrations.serverless import serverless_function
from static_common.enums.parent import ParentType
from static_common.models.event_bridge import EventBridgeF<PERSON>Created
from static_common.schemas.event_bridge import EventBridgeFactCreatedSchema

from src.handlers.handler_utils import (
    get_copilot_v3_client,
    get_ers_v3_client,
    get_facts_client,
)
from src.logic.event_sender import EventSender

logger = get_logger()


@serverless_function
@log_function_inputs
@bind_lambda_logging_context
def fact_with_feedback_created(event, context=None):
    # Triggered by event bus when a fact is created using feedback. We get affected submissions and send
    # USER_FEEDBACK_CREATED for them.
    fact_created_event: EventBridgeFactCreated = EventBridgeFactCreatedSchema().load(event)
    parent_type = fact_created_event.detail.fact_created.parent_type
    parent_id = fact_created_event.detail.fact_created.parent_id
    organization_id = fact_created_event.detail.fact_created.organization_id

    entity_ids = set()
    submission_ids = set()
    if parent_type == ParentType.BUSINESS:
        entity_ids = {parent_id}
    if parent_type in {ParentType.PREMISES, ParentType.STRUCTURE}:
        premises_ids = set()
        if parent_type == ParentType.PREMISES:
            premises_ids = {parent_id}
        elif parent_type == ParentType.STRUCTURE:
            relationship_facts = get_facts_client().get_relationship_facts_by_remote_id(parent_id).facts
            for f in relationship_facts:
                if f.parent_type not in {ParentType.PREMISES, ParentType.BUSINESS}:
                    logger.error("Structure tied to parent type other than premises/business!", structure_id=parent_id)
                if f.parent_type == ParentType.PREMISES:
                    premises_ids.add(f.parent_id)
                if f.parent_type == ParentType.BUSINESS:
                    entity_ids.add(f.parent_id)
        for premises_id in premises_ids:
            entities = get_ers_v3_client().get_entities_by_premises_id(premises_id)
            entity_ids.update([e.id for e in entities])
    if parent_type == ParentType.SUBMISSION:
        submission = get_copilot_v3_client().get_submission(parent_id, expand=["businesses"])
        if submission and submission.businesses:
            submission_ids.add(parent_id)

    for entity_id in entity_ids:
        reports = get_copilot_v3_client().get_reports_including_business(
            entity_id, include_frozen=False, lightweight=True, organization_id=organization_id
        )
        for report in reports:
            submission = report.submissions[0]
            if submission.businesses:
                submission_ids.add(submission.id)

    logger.info("Publishing events for submission ids", submission_ids=submission_ids)
    for submission_id in submission_ids:
        EventSender().send(event_type=KalepaEvents.USER_FEEDBACK_CREATED, payload={"submission_id": submission_id})
    return {"submission_ids": list(submission_ids)}
