from datetime import datetime

from common.submissions.feins import normalize_fein
from common.utils.logging import log_function_inputs
from copilot_client_v3 import Submission, WorkersCompExperience
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from sentry_sdk.integrations.serverless import serverless_function
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.organization import ExistingOrganizations, OrganizationGroups
from static_common.enums.parent import ParentType
from static_common.enums.states import USStatesEnum
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.workers_compensation import WorkersCompExperienceSource
from web_service_clients.models.wcirb import RatingTypeCodeType
from web_service_clients.models.wcirb.enums import WCIRBResponseCode
from web_service_clients.models.wcirb.workers_comp_underwriting import (
    Submission as WCIRBSubmission,
)

from src.handlers.handler_utils import (
    get_copilot_v3_client,
    get_facts_client,
    get_wcirb_client,
)

logger = get_logger()


def _transform_uw_information_to_wc_experience(
    uw_info: WCIRBSubmission.Wcunderwriting.UnderwritingsInformation.UnderwritingInformation,
    risk_name: str,
    submission_id: str,
) -> WorkersCompExperience:
    rating_effective_date = (
        datetime(
            uw_info.rating_effective_date.year,
            uw_info.rating_effective_date.month,
            uw_info.rating_effective_date.day,
        )
        if uw_info.rating_effective_date
        else None
    )
    if not rating_effective_date:
        # use year as a fallback
        rating_effective_date = datetime(uw_info.rating_effective_year.year, 1, 1)
    return WorkersCompExperience(
        risk_name=risk_name,
        state=USStatesEnum.CA.name,
        experience_modification=float(uw_info.experience_modification_factor),
        rating_effective_date=rating_effective_date,
        source=WorkersCompExperienceSource.API,
        submission_id=submission_id,
        type="WCIRB",
    )


def _get_fein_values(submission: Submission) -> list[str]:
    if submission.fni_fein:
        return [submission.fni_fein.replace("-", "")]
    feins = get_facts_client().get_facts(
        [submission.id],
        ParentType.SUBMISSION,
        fact_subtype_ids=[FactSubtypeID.FEIN],
        organization_id=ExistingOrganizations.Paragon.value,
    )
    if not feins.facts:
        # try and get the feins from the fni
        fni = next(
            (
                b
                for b in submission.businesses
                if b.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
            ),
            None,
        )
        if not fni:
            logger.warning("Cannot find FEIN for submission and fni is not present", submission_id=submission.id)
            return []
        feins = get_facts_client().get_facts(
            [fni.business_id],
            ParentType.BUSINESS,
            fact_subtype_ids=[FactSubtypeID.FEIN],
            organization_id=ExistingOrganizations.Paragon.value,
        )
    if not feins.facts:
        logger.warning("Cannot find FEIN for submission", submission_id=submission.id)
        return []
    return list({val.display_name.replace("-", "") for val in feins.facts[0].observation.tags})


def _get_wc_comp_exp_for_fein(fein: str, submission_id: str) -> list[WorkersCompExperience]:
    try:
        response = get_wcirb_client().get_by_fein(fein)
        response_code = response.wcunderwriting.response_code
        if response_code not in WCIRBResponseCode.__dict__.values():
            logger.error(
                "Received unknown response code", response_code=response_code, fein=fein, submission_id=submission_id
            )
            return []
        if response.wcunderwriting.response_code != WCIRBResponseCode.SUCCESS:
            logger.warning(
                "No data for FEIN",
                fein=fein,
                submission_id=submission_id,
                response_message=response.wcunderwriting.response_message,
            )
            return []

        risk_name = ";".join(
            [
                n.name_of_insured
                for n in response.wcunderwriting.names.name
                if n.federal_employer_identification_number == fein
            ]
        )
        # D = DNQ
        # E = Experience Rating
        # C = Cannot Issue (Pending)
        # W = Withdrawn
        uw_information = [
            info
            for info in response.wcunderwriting.underwritings_information.underwriting_information
            if info.rating_type_code == RatingTypeCodeType.E
        ]
        return [
            _transform_uw_information_to_wc_experience(uw_info, risk_name, submission_id) for uw_info in uw_information
        ]
    except:
        logger.exception("Error getting response from WC Bureau CA", fein=fein)
        raise


def _filter_out_invalid_fein_numbers(fein_numbers: list[str]) -> list[str]:
    validated_fein = []
    for fein_number in fein_numbers:
        normalized_fein = normalize_fein(fein_str=fein_number, with_hyphen_after_second_digit=False)

        if normalized_fein:
            if normalized_fein != fein_number:
                logger.info("Successfully normalized FEIN number", fein_before=fein_number, fein_after=normalized_fein)

            validated_fein.append(normalized_fein)
        else:
            logger.warning("Invalid FEIN number", fein_number=fein_number)

    return validated_fein


@serverless_function
@log_function_inputs
@bind_lambda_logging_context
def get_wc_experience_from_api(event, context=None):  # type: ignore
    submission_id = event["submission_id"]

    # check if this is paragon_wc_submission
    submission = get_copilot_v3_client().get_submission(
        submission_id, expand=["organization_group", "fni_fein", "businesses"]
    )
    if not submission or submission.organization_group != OrganizationGroups.PARAGON_WC.value:
        return {"status": 200}

    not_validated_fein_values = _get_fein_values(submission)
    fein_values = _filter_out_invalid_fein_numbers(not_validated_fein_values)

    if not fein_values:
        logger.warning("No FEIN values found for submission", submission_id=submission_id)
        return {"status": 200}

    wc_experience_ratings = []

    for fein in fein_values:
        wc_experience_ratings.extend(_get_wc_comp_exp_for_fein(fein, submission_id))

    for wc_exp in wc_experience_ratings:
        get_copilot_v3_client().create_workers_comp_experience(wc_exp)
    return {"status": 200}
