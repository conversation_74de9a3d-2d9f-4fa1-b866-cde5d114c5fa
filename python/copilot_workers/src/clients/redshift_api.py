from collections import defaultdict

from common.clients.cache import RedisCacheClient, with_cache
from common.clients.utils import retry_with_backoff
from infrastructure_common.logging import get_logger
from redshift_api_client.api.default_api import Default<PERSON><PERSON>
from redshift_api_client.api_client import <PERSON><PERSON><PERSON>lient
from redshift_api_client.configuration import Configuration
from redshift_api_client.models.percentiles_request import PercentilesRequest
from redshift_api_client.models.percentiles_response import PercentilesResponse
from static_common.enums.fact_subtype import FactSubtypeID

from src.constants import WEEK_IN_SECONDS

logger = get_logger()


class RedshiftApiClient:
    def __init__(self, base_url: str, cache_client: RedisCacheClient | None = None):
        self.base_url = base_url
        self.api = DefaultApi(ApiClient(configuration=Configuration(self.base_url)))
        self.cache_client = cache_client

    @retry_with_backoff  # type: ignore
    def _get_percentiles_data_internal(self, organization_id: int, request: PercentilesRequest) -> PercentilesResponse:
        return self.api.get_percentiles_data(organization_id=organization_id, percentiles_request=request)

    @with_cache(cls=dict, expiration_seconds=WEEK_IN_SECONDS)  # type: ignore
    def get_percentiles_data(
        self, organization_id: int, percentiles: list[int], fact_subtype_ids: list[str]
    ) -> dict | None:
        try:
            request = PercentilesRequest(
                percentiles=percentiles,
                fact_subtype_ids=[FactSubtypeID(v) for v in fact_subtype_ids],  # type: ignore
            )
            response_data = self._get_percentiles_data_internal(organization_id, request)
        except Exception as e:
            logger.error(
                "Failed to get percentiles data",
                exc_info=e,
                error=str(e),
                organization_id=organization_id,
                percentiles=percentiles,
            )
            return None
        data: dict[str, dict] = defaultdict(dict)
        result: dict[str, dict] = defaultdict(dict)
        for item in response_data.columns:
            data[item.fact_subtype_id.value][item.percentile] = item.value
        for fact_subtype_id in fact_subtype_ids:
            for percentile in percentiles:
                value = data[fact_subtype_id].get(percentile)
                result[fact_subtype_id][percentile] = value
        return result
