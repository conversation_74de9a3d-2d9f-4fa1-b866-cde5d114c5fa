from src.handlers.paragon_handlers import _filter_out_invalid_fein_numbers


def test_filter_out_invalid_fein_numbers():
    raw_fein_numbers = [
        "12-3456789",
        "*********",
        "12-34-56789",
        "1234-56789",
        "12-34567890",
        "*********0",
        "AGENCY NAME AND ADDRESS COMPANY: Acrisure Southwest Partners Insurance Services, LLC \x04\x02\x06\x15\x07\x0f\x11 \x11\x12\x01\x15\x03\x0f\x0e\x10\x15\x05\x10 \x08 \x13\x14 UNDERWRITER: 4000 Westerly Place Bruce's Gourmet Catering Inc. Suite 110 APPLICANT NAME: \x06\x1e\x08 \x1c \x18 \x13\x10\x1e\x13 \x1e\x04\x10\x19 \x17\x06\x19 \x14\x10\x06\x0e\x1e\x03\x06\x07 \x0f \x1a \x18\x1e\x04\x11\x18\x1b\x15\x06\x10\x07 \x1e\x05 \x16\x1d \x07 \x18\x01",
    ]

    expected_validated_fein = [
        "*********",
        "*********",
        "*********",
        "*********",
    ]

    assert _filter_out_invalid_fein_numbers(raw_fein_numbers) == expected_validated_fein
