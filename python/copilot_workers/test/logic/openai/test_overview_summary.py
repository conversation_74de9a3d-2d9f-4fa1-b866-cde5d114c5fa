import uuid
from types import SimpleNamespace
from unittest.mock import <PERSON><PERSON>ock, patch
from uuid import UUI<PERSON>

import pytest
from common.clients.copilot_v3_client import CopilotV3Client
from facts_client.model.inference_source import InferenceSource
from facts_client.model.summary import Summary
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.parent import ParentType
from static_common.enums.source_types import SourceTypeID
from static_common.enums.summary_type import SummaryType
from static_common.models.summaries import OverviewSummaryMetadata
from structlog.stdlib import BoundLogger

from src.clients.ers_v3 import ERSV3Client
from src.clients.facts import FactsClient
from src.logic.openai.overview_summary import (
    OverviewSummarizationResult,
    OverviewSummaryProvider,
)


@pytest.fixture
def mock_clients():
    return {
        "copilot_client": MagicMock(spec=CopilotV3Client),
        "facts_client": MagicMock(spec=FactsClient),
        "ers_client": <PERSON><PERSON>ock(spec=ERSV3Client),
        "llm_client": <PERSON>Mock(),
        "logger": <PERSON><PERSON>ock(spec=BoundLogger),
    }


@pytest.fixture
def provider(mock_clients):
    return OverviewSummaryProvider(
        submission_id=UUID("12345678-1234-5678-1234-************"), organization_id=123, **mock_clients
    )


@pytest.fixture
def mock_summary_result():
    return OverviewSummarizationResult(
        submission_id=UUID("12345678-1234-5678-1234-************"),
        summary="Test summary content",
        summary_metadata=OverviewSummaryMetadata(number_of_sources=5),
    )


def test_to_facts_document_new(mock_summary_result):
    result = mock_summary_result.to_facts_document(None)

    assert result.id == str(mock_summary_result.default_id)
    assert result.parent_id == str(mock_summary_result.submission_id)
    assert result.parent_type == ParentType.SUBMISSION
    assert result.body == "Test summary content"
    assert result.document_type_id == DocumentTypeID.SUMMARY
    assert result.summary_type == SummaryType.OVERVIEW_SUMMARY

    # Verify UUID is valid
    UUID(result.id)


def test_to_facts_document_existing(mock_summary_result):
    new_id = str(uuid.uuid4())
    existing_summary = Summary(
        id=new_id,
        document_type_id=DocumentTypeID.SUMMARY,
        parent_id=str(mock_summary_result.submission_id),
        parent_type=ParentType.SUBMISSION,
        body="Old summary content",
        summary_type=SummaryType.OVERVIEW_SUMMARY,
        summary_metadata={},
        source=InferenceSource(
            source_type_id=SourceTypeID.INFERENCE,
            model_version="old",
            model_name="old",
        ),
    )

    result = mock_summary_result.to_facts_document(existing_summary)
    assert result.id == new_id
    assert result.body == "Test summary content"


def test_get_existing_summary_found(provider):
    mock_summary = MagicMock(summary_type=SummaryType.OVERVIEW_SUMMARY)
    provider.facts_client.get_documents.return_value = [mock_summary]

    result = provider._get_existing_summary(provider.submission_id)

    provider.facts_client.get_documents.assert_called_once_with(
        parent_ids=[provider.submission_id],
        parent_type=ParentType.SUBMISSION,
        document_type_id=DocumentTypeID.SUMMARY,
    )
    assert result == mock_summary


def test_get_existing_summary_not_found(provider):
    # Other summary types, but not overview
    mock_summary = MagicMock(summary_type=SummaryType.NEWS_ARTICLES_SUMMARY)
    provider.facts_client.get_documents.return_value = [mock_summary]

    result = provider._get_existing_summary(provider.submission_id)

    assert result is None


@patch("src.logic.openai.overview_summary.get_llm_response")
def test_update_overview_summary_success(mock_get_llm, provider):
    # Mock submission and coverage
    mock_property_coverage = MagicMock()
    mock_property_coverage.coverage.name = "property"
    mock_property_coverage.to_dict.return_value = {
        "id": "coverage-id",
        "submission_id": str(provider.submission_id),
        "coverage": "coverage-obj",
        "tiv": "$1,000,000",
    }

    mock_submission = MagicMock()
    mock_submission.coverages = [mock_property_coverage]
    mock_submission.businesses = [MagicMock(requested_name="Test Business")]
    provider.copilot_client.get_submission.return_value = mock_submission

    with patch.object(provider, "_get_structure_data", return_value={}):
        mock_get_llm.return_value = {"summary": "Generated summary text"}

        provider.facts_client.get_documents.return_value = []
        doc_id = SimpleNamespace()
        doc_id.id = "new-doc-id"
        provider.facts_client.create_or_replace_document_lite.return_value = doc_id

        result = provider.update_overview_summary()

        assert result.document_id == "new-doc-id"
        assert result.issue is None
        provider.facts_client.create_or_replace_document_lite.assert_called_once()


def test_update_overview_summary_no_property_coverage(provider):
    mock_submission = MagicMock()
    mock_submission.coverages = [MagicMock(coverage=MagicMock(name="liability"))]
    provider.copilot_client.get_submission.return_value = mock_submission

    mock_summary = MagicMock(summary_type=SummaryType.OVERVIEW_SUMMARY)
    provider.facts_client.get_documents.return_value = [mock_summary]

    result = provider.update_overview_summary()

    assert result.document_id is None
    assert result.issue == "No property coverage found"
    provider.facts_client.delete_document.assert_called_once_with(mock_summary.id)


@patch("src.logic.openai.overview_summary.get_llm_response")
def test_update_overview_summary_llm_failure(mock_get_llm, provider):
    mock_property_coverage = MagicMock()
    mock_property_coverage.coverage.name = "property"
    mock_property_coverage.to_dict.return_value = {
        "id": "coverage-id",
        "submission_id": str(provider.submission_id),
        "coverage": "coverage-obj",
        "tiv": "$1,000,000",
    }

    mock_submission = MagicMock()
    mock_submission.coverages = [mock_property_coverage]
    mock_submission.businesses = []
    provider.copilot_client.get_submission.return_value = mock_submission

    with patch.object(provider, "_get_structure_data", return_value={}):
        mock_get_llm.side_effect = Exception("LLM error")

        mock_existing_summary = MagicMock(summary_type=SummaryType.OVERVIEW_SUMMARY)
        provider.facts_client.get_documents.return_value = [mock_existing_summary]

        result = provider.update_overview_summary()

        assert result.document_id is None
        assert result.issue == "Failed to store document in facts API"
        provider.facts_client.delete_document.assert_called_once_with(mock_existing_summary.id)


@patch("src.logic.openai.overview_summary.get_llm_response")
def test_update_overview_summary_totals_calculation(mock_get_llm, provider):
    mock_property_coverage = MagicMock()
    mock_property_coverage.coverage.name = "property"
    mock_property_coverage.to_dict.return_value = {
        "id": "coverage-id",
        "submission_id": str(provider.submission_id),
        "coverage": "coverage-obj",
        "tiv": "$1,000,000",
    }

    mock_submission = MagicMock()
    mock_submission.coverages = [mock_property_coverage]
    mock_submission.businesses = [MagicMock(requested_name="Test Business")]
    provider.copilot_client.get_submission.return_value = mock_submission

    mock_fact1 = MagicMock()
    mock_fact1.fact_subtype.display_name = "TIV"
    mock_fact1.fact_subtype.fact_type_id = "MEASUREMENT"
    mock_fact1.observation.number = 500000
    mock_fact1.parent_id = "structure-1"

    mock_fact2 = MagicMock()
    mock_fact2.fact_subtype.display_name = "TIV"
    mock_fact2.fact_subtype.fact_type_id = "MEASUREMENT"
    mock_fact2.observation.number = 300000
    mock_fact2.parent_id = "structure-2"

    mock_fact3 = MagicMock()
    mock_fact3.fact_subtype.display_name = "TIV"
    mock_fact3.fact_subtype.fact_type_id = "MEASUREMENT"
    mock_fact3.observation.number = 200000
    mock_fact3.parent_id = "structure-3"

    # Ignore duplicates
    mock_structure_data = {
        "business-1": {
            "premises-1": [mock_fact1],
            "premises-2": [mock_fact2],
        },
        "business-2": {
            "premises-1": [mock_fact1],
            "premises-2": [mock_fact2],
        },
        "business-3": {
            "premises-4": [mock_fact1],
            "premises-5": [mock_fact2, mock_fact3],
        },
    }

    with patch.object(provider, "_get_structure_data", return_value=mock_structure_data):
        mock_get_llm.return_value = "Generated summary text"

        provider.facts_client.get_documents.return_value = []
        provider.facts_client.create_or_replace_document_lite.return_value = MagicMock(id="new-doc-id")

        result = provider.update_overview_summary()

        assert result.document_id == "new-doc-id"
        assert result.issue is None

        # Verify totals calculation in LLM prompt
        prompt_content = mock_get_llm.call_args[1]["chat_prompt"].messages[1].content

        assert "<totals>" in prompt_content
        assert "Premises: 4" in prompt_content
        assert "Structures: 3" in prompt_content
        assert "TIV: 1000000" in prompt_content
