{"StartAt": "ProcessFromCache", "States": {"ProcessFromCache": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processFileFromCache_lambda_arn}", "Next": "ChoiceFileProcessedFromCache", "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.error_from_cache", "Next": "EnrichRequestWithOrganizationData"}]}, "ChoiceFileProcessedFromCache": {"Type": "Choice", "Default": "ChoiceSendEvent", "Choices": [{"Variable": "$.file_processed_from_cache", "BooleanEquals": false, "Next": "EnrichRequestWithOrganizationData"}]}, "EnrichRequestWithOrganizationData": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${enrichRequestWithOrganizationData_lambda_arn}", "Next": "ProcessFileParallel"}, "ProcessFileParallel": {"Type": "<PERSON><PERSON><PERSON>", "Next": "MergeProcessingResults", "ResultSelector": {"status.$": "$[0].processing_results.status", "error.$": "$[0].processing_results.error", "file_id.$": "$[0].processing_results.file_id", "submission_id.$": "$[0].processing_results.submission_id", "processing_finished.$": "$[0].processing_results.processing_finished", "event_type.$": "$[0].processing_results.event_type", "file_insights.$": "$[1].classifiers_result"}, "Catch": [{"ErrorEquals": ["States.ALL"], "ResultPath": "$.error", "Next": "PrepareFailedEvent"}], "Branches": [{"StartAt": "GetFileInfo", "States": {"GetFileInfo": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${getFileInfo_lambda_arn}", "ResultPath": "$.file_info", "Next": "ChoiceFileType"}, "ChoiceFileType": {"Type": "Choice", "Default": "UnsupportedFileType", "Choices": [{"Variable": "$.file_info.classification", "StringEquals": "HTML_DOCUMENT", "Next": "ProcessHtmlDocument"}, {"Variable": "$.file_info.classification", "StringEquals": "SOV_SPREADSHEET", "Next": "ProcessSOVFile"}, {"Variable": "$.file_info.classification", "StringEquals": "NAMED_INSURED_SCHEDULE_SPREADSHEET", "Next": "ProcessNISFile"}, {"Or": [{"Variable": "$.file_info.classification", "StringEquals": "VEHICLES_SPREADSHEET"}, {"Variable": "$.file_info.classification", "StringEquals": "VEHICLES_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "ACORD_129"}, {"Variable": "$.file_info.classification", "StringEquals": "ACORD_127"}, {"Variable": "$.file_info.classification", "StringEquals": "ACORD_128"}], "Next": "ProcessVehiclesFile"}, {"Or": [{"Variable": "$.file_info.classification", "StringEquals": "DRIVERS_SPREADSHEET"}, {"Variable": "$.file_info.classification", "StringEquals": "DRIVERS_PDF"}], "Next": "ProcessDriversFile"}, {"Variable": "$.file_info.classification", "StringEquals": "EQUIPMENT_SPREADSHEET", "Next": "ProcessEquipmentFile"}, {"And": [{"Variable": "$.file_info.file_type", "StringEquals": "Supplemental Form"}, {"Variable": "$.file_info.classification", "StringMatches": "*_PDF"}], "Next": "ProcessSupplemental"}, {"Variable": "$.file_info.classification", "StringEquals": "EMAIL", "Next": "ProcessEmail"}, {"Variable": "$.file_info.classification", "StringEquals": "RAW_EMAIL", "Next": "ProcessRawEmail"}, {"Variable": "$.file_info.classification", "StringEquals": "COVER_SHEET_PDF", "Next": "ProcessCoverSheet"}, {"Variable": "$.file_info.classification", "StringEquals": "DIRECTORS_AND_OFFICERS_PDF", "Next": "ProcessDirectorsAndOfficers"}, {"Variable": "$.file_info.classification", "StringEquals": "EMPLOYEE_HANDBOOK_PDF", "Next": "ProcessEmployeeHandbook"}, {"Variable": "$.file_info.classification", "StringEquals": "ORG_CHART_PDF", "Next": "ProcessCorporateStructure"}, {"Variable": "$.file_info.classification", "StringEquals": "ERISA_FORM_5500_PDF", "Next": "ProcessErisaForm5500"}, {"Variable": "$.file_info.classification", "StringEquals": "ERISA_FORM_5500_SF_PDF", "Next": "ProcessErisaForm5500"}, {"Variable": "$.file_info.classification", "StringEquals": "BUDGET_PDF", "Next": "ProcessBudgetPdfFile"}, {"Variable": "$.file_info.classification", "StringEquals": "BUDGET_SPREADSHEET", "Next": "ProcessBudgetSpreadsheetFile"}, {"Variable": "$.file_info.classification", "StringEquals": "LOSS_RUN_NO_CLAIM_PDF", "Next": "ProcessNoClaimsLRPdfWithNonAzureTables"}, {"Variable": "$.file_info.classification", "StringEquals": "LOSS_RUN_NO_CLAIM_PDF_DUMMY", "Next": "ProcessNoClaimsLossRunPdfFile"}, {"Variable": "$.file_info.classification", "StringEquals": "CONSOLIDATED_FINANCIAL_STATEMENT_PDF", "Next": "ProcessFinancialStatementPdfFile"}, {"Variable": "$.file_info.classification", "StringEquals": "CONSOLIDATED_FINANCIAL_STATEMENT_SPREADSHEET", "Next": "ProcessFinancialStatementSpreadsheetFile"}, {"Variable": "$.file_info.classification", "StringEquals": "ARCHIVE", "Next": "ProcessArchive"}, {"Variable": "$.file_info.classification", "StringEquals": "WORK_COMP_PAYROLL_SPREADSHEET", "Next": "ProcessWorkersCompPayrollSpreadsheetFile"}, {"Variable": "$.file_info.classification", "StringEquals": "SUPPLEMENTAL_APPLICATION_SPREADSHEET", "Next": "ProcessSpreadsheetSupplemental"}, {"Variable": "$.file_info.classification", "StringEquals": "SOV_PDF", "Next": "ProcessSOVPdf"}, {"Or": [{"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2004_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2005_06"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2006_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2007_10"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2009_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_CA_2023_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2024_11"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2016_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2013_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2014_12"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_FL_2016_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2013_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2011_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2007_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_3_93"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_FL_2015_02"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2007_07"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2001_04"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2009_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_7_98"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2_02"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2003_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_7_96"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2000_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_125_2025_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_823_2011_10"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_823_2015_12"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2007_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2016_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2016_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2014_04"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2011_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2004_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2009_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2005_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2010_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_S_3_93"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2025_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_S_1_97"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2007_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2003_07"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_126_2000_04"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_211_2016_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2014_12"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2016_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2011_10"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2010_12"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2007_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2007_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_1_98"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2002_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2005_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2006_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_S_7_88"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_3_95"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_5_94"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_140_2001_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_131_2009_10"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_131_2017_11"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_131_2013_12"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_131_2011_11"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_131_2016_04"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_131_2007_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_131_2003_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2013_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2004_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2017_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_FL_2019_07"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_CA_2019_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2013_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2000_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_CA_2023_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2005_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2007_11"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2010_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2009_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2002_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_7_98"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_2017_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_130_FL_2002_07"}, {"Variable": "$.file_info.file_version", "StringEquals": "APPLIED_130_API_2005_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "APPLIED_130_API_2013_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_139_2004_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_139_2014_09"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_139_2015_12"}, {"Variable": "$.file_info.file_version", "StringEquals": "APPLIED_126_HAZ_2007_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "APPLIED_126_HS_2005_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "APPLIED_125_ONI_2009_08"}, {"Variable": "$.file_info.file_version", "StringEquals": "OFAPPINFCNI"}, {"Variable": "$.file_info.file_version", "StringEquals": "OFSCHHAZ"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_160_2011_10"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_160_2004_03"}, {"And": [{"Or": [{"Variable": "$.file_info.organization_id", "NumericEquals": 3}, {"Variable": "$.file_info.organization_id", "NumericEquals": 62}]}, {"Or": [{"Variable": "$.file_info.file_version", "StringEquals": "ACORD_101_2008_01"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_101_2016_03"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_829_2009_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_829_LOB_2009_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "ACORD_829_CLAP_2009_05"}, {"Variable": "$.file_info.file_version", "StringEquals": "APPLIED_98_2001_01"}]}]}], "Next": "ProcessAcordFormFile"}, {"Or": [{"Variable": "$.file_info.classification", "StringEquals": "LOSS_RUN_NO_CLAIM"}, {"Variable": "$.file_info.classification", "StringEquals": "LOSS_RUN_NO_CLAIM_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "LOSS_RUN_NO_CLAIM_SPREADSHEET"}, {"Variable": "$.file_info.classification", "StringEquals": "LOSS_RUN_NO_CLAIM_EDITABLE_DOC"}], "Next": "UnprocessableFileType"}, {"Or": [{"Variable": "$.file_info.classification", "StringEquals": "PURCHASE_AGREEMENT_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "FINANCIAL_STATEMENT_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "INVESTMENT_MEMORANDUM_PDF"}], "Next": "ProcessTransactionFile"}, {"Variable": "$.file_info.file_type", "StringEquals": "Loss Run", "Next": "ProcessLossRunFile"}, {"Variable": "$.file_info.cancelled_file", "BooleanEquals": true, "Next": "processCancelledFile"}, {"Or": [{"Variable": "$.file_info.classification", "StringEquals": "WORK_COMP_EXPERIENCE_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "WORK_COMP_EXPERIENCE_CA_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "WORK_COMP_EXPERIENCE_NY_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "WORK_COMP_EXPERIENCE_NJ_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "WORK_COMP_EXPERIENCE_PA_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "WORK_COMP_EXPERIENCE_MN_PDF"}, {"Variable": "$.file_info.classification", "StringEquals": "WORK_COMP_EXPERIENCE_WI_PDF"}], "Next": "ProcessXmodFile"}, {"Variable": "$.file_info.classification", "StringEquals": "IFTA_PDF", "Next": "ProcessIFTAFile"}, {"Variable": "$.file_info.classification", "StringEquals": "IFTA_SPREADSHEET", "Next": "ProcessIFTASpreadsheetFile"}, {"Variable": "$.file_info.classification", "StringEquals": "ALLY_AUTO_SUPPLEMENTAL", "Next": "ProcessAllyAutoSupplementalFile"}, {"Variable": "$.file_info.classification", "StringEquals": "ALLY_AUTO_PROPERTY_SOV", "Next": "ProcessAllyAutoPropertySOV"}, {"Variable": "$.file_info.classification", "StringEquals": "LETTER_OF_INTENT_PDF", "Next": "ProcessLetterOfIntent"}, {"Variable": "$.file_info.classification", "StringEquals": "POLICY_PDF", "Next": "ProcessPolicy"}]}, "UnsupportedFileType": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${unsupportedFileType_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "UnprocessableFileType": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${unprocessableFileType_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessHtmlDocument": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processHtmlDocument_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessSOVFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processSOVFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "ChoiceResolveBusinesses"}, "ProcessNISFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processNISFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "ChoiceResolveBusinesses"}, "ProcessAllyAutoSupplementalFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processAllyAutoSupplementalFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "ChoiceResolveBusinesses"}, "ProcessAllyAutoPropertySOV": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processAllyAutoPropertySOV_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "ChoiceResolveBusinesses"}, "ProcessVehiclesFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processVehiclesFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "ChoiceResolveBusinesses"}, "ProcessDriversFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processDriversFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessEquipmentFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processEquipmentFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessNoClaimsLRPdfWithNonAzureTables": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processNoClaimsLRPdfWithNonAzureTables_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessBudgetSpreadsheetFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processBudgetSpreadsheetFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessFinancialStatementSpreadsheetFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processFinancialStatementSpreadsheetFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessEmail": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processEmail_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessRawEmail": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processRawEmail_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessCoverSheet": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processCoverSheet_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessDirectorsAndOfficers": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processDirectorsAndOfficers_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessEmployeeHandbook": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processEmployeeHandbook_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessTransactionFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processTransactionFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessCorporateStructure": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processCorporateStructure_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessErisaForm5500": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processErisaForm5500_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessArchive": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processArchive_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessLossRunFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${sf_config.processLossRunFileLambdaARN}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "End": true}, "ProcessAcordFormFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${sf_config.processAcordFileLambdaARN}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "End": true}, "ProcessXmodFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processXmodFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessIFTAFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processIFTAFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessIFTASpreadsheetFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processIFTASpreadsheetFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessWorkersCompPayrollSpreadsheetFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processWorkersCompPayrollSpreadsheetFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessSpreadsheetSupplemental": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processSpreadsheetSupplemental_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessSOVPdf": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processSOVPdf_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "processCancelledFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processCancelledFile_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessNoClaimsLossRunPdfFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "arn:aws:states:::states:startExecution.sync:2", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState", "Parameters": {"Input": {"file_info.$": "$.file_info", "organization_id.$": "$.organization_id"}, "StateMachineArn": "${ProcessNoClaimsLossRunPdfDoc_sf_arn}"}, "ResultSelector": {"file_id.$": "$$.Execution.Input.file_id", "submission_id.$": "$$.Execution.Input.submission_id", "status.$": "$.Output.status", "error.$": "$.Output.error", "processing_finished": true}}, "ProcessBudgetPdfFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "arn:aws:states:::states:startExecution.sync:2", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState", "Parameters": {"Input": {"file_info.$": "$.file_info"}, "StateMachineArn": "${ProcessBudgetPdfDoc_sf_arn}"}, "ResultSelector": {"file_id.$": "$$.Execution.Input.file_id", "submission_id.$": "$$.Execution.Input.submission_id", "status.$": "$.Output.status", "error.$": "$.Output.error", "processing_finished": true, "event_type.$": "$.Output.event_type"}}, "ProcessFinancialStatementPdfFile": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processFinancialStatementPdfFrOutput_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ProcessLetterOfIntent": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processLetterOfIntent_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "ChoiceResolveBusinesses"}, "ProcessPolicy": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Resource": "${processPolicy_lambda_arn}", "InputPath": "$.file_info", "ResultPath": "$.processing_results", "Next": "UpdateFileProcessingState"}, "ChoiceResolveBusinesses": {"Type": "Choice", "Default": "ResolveBusinesses", "Choices": [{"Variable": "$.processing_results.organization_id", "NumericEquals": 62, "Next": "UpdateFileProcessingState"}, {"Variable": "$.processing_results.target_file_processing_state", "StringEquals": "PROCESSING_FAILED", "Next": "UpdateFileProcessingState"}]}, "ProcessSupplemental": {"Type": "Task", "Resource": "arn:aws:states:::states:startExecution.sync:2", "Parameters": {"Input.$": "$", "StateMachineArn": "${ProcessSupplemental_sf_arn}"}, "OutputPath": "$.Output", "Next": "ChoiceInferKVInsights"}, "ChoiceInferKVInsights": {"Type": "Choice", "Default": "UpdateFileProcessingState", "Choices": [{"Variable": "$.pds_file_insights_enabled", "BooleanEquals": false, "Next": "InferFileKeyValueInsights"}]}, "InferFileKeyValueInsights": {"Type": "Task", "Resource": "${sf_config.insightsInferKeyValueInsightsLambdaARN}", "Parameters": {"file_id.$": "$.file_id", "submission_id.$": "$.submission_id", "organization_id.$": "$.organization_id"}, "ResultPath": null, "Next": "UpdateFileProcessingState"}, "UpdateFileProcessingState": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "ResultPath": null, "End": true, "Resource": "${updateFileProcessingState_lambda_arn}"}, "ResolveBusinesses": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Parameters": {"Input": {"file_id.$": "$$.Execution.Input.file_id", "submission_id.$": "$$.Execution.Input.submission_id", "set_to_processing": false, "target_file_processing_state.$": "$.processing_results.target_file_processing_state"}, "StateMachineArn": "${ResolveFileEntities_sf_arn}"}, "Resource": "arn:aws:states:::states:startExecution.sync:2", "ResultSelector": {"file_id.$": "$$.Execution.Input.file_id", "submission_id.$": "$$.Execution.Input.submission_id", "status.$": "$.Output.processing_results.status", "error.$": "$.Output.processing_results.error", "processing_finished.$": "$.Output.processing_results.processing_finished", "event_type": "SUBMISSION_FILE_DATA_EXTRACTED", "target_file_processing_state.$": "$.Output.processing_results.target_file_processing_state", "classification.$": "$.Output.processing_results.classification", "organization_id.$": "$.Output.processing_results.organization_id"}, "ResultPath": "$.processing_results", "End": true}}}, {"StartAt": "ChoiceInferFileInsights", "States": {"ChoiceInferFileInsights": {"Type": "Choice", "Default": "SkipInferFileInsights", "Choices": [{"And": [{"Variable": "$.organization_id", "NumericEquals": 62}, {"Variable": "$.pds_file_insights_enabled", "BooleanEquals": false}], "Next": "InferFileInsights"}]}, "SkipInferFileInsights": {"Type": "Pass", "Result": {"classifiers_result": null}, "End": true}, "InferFileInsights": {"Type": "Task", "Resource": "${sf_config.insightsInferFileInsightsLambdaARN}", "Parameters": {"file_id.$": "$.file_id", "submission_id.$": "$.submission_id", "organization_id.$": "$.organization_id"}, "End": true}}}]}, "MergeProcessingResults": {"Type": "Task", "Next": "ChoiceSendEvent", "ResultPath": null, "Resource": "${mergeFileProcessingResults_lambda_arn}"}, "PrepareFailedEvent": {"Type": "Task", "Next": "SendEvent", "Parameters": {"error.$": "$.error", "status": "FAILURE", "file_id.$": "$.file_id", "submission_id.$": "$.submission_id", "event_type": "SUBMISSION_FILE_PROCESSING_FINISHED"}, "Resource": "${handleFileProcessingFailure_lambda_arn}"}, "ChoiceSendEvent": {"Type": "Choice", "Default": "SendEvent", "Choices": [{"Variable": "$.processing_finished", "BooleanEquals": false, "Next": "SkipSendEvent"}]}, "SendEvent": {"Type": "Task", "Retry": [{"ErrorEquals": ["Lambda.ClientExecutionTimeoutException", "Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 5, "MaxAttempts": 3}], "Next": "ChoiceSuccess", "Parameters": {"event_type.$": "$.event_type", "file_id.$": "$$.Execution.Input.file_id", "submission_id.$": "$$.Execution.Input.submission_id", "step_function_status.$": "$.status", "step_function_execution_id.$": "$$.Execution.Id", "error.$": "$.error.Error", "cause.$": "$.error.Cause"}, "ResultPath": null, "Resource": "${sf_config.sendKalepaEventsLambdaARN}"}, "SkipSendEvent": {"Type": "Pass", "Next": "ChoiceSuccess"}, "ChoiceSuccess": {"Type": "Choice", "Default": "ExecutionSuccessful", "Choices": [{"Variable": "$.status", "StringEquals": "FAILURE", "Next": "ExecutionFailed"}]}, "ExecutionFailed": {"Type": "Fail"}, "ExecutionSuccessful": {"Type": "Succeed"}}}