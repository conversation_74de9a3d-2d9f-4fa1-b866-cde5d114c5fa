locals {
  common_lambdas_env = {
    "ENTITY_RESOLUTION_SERVICE_V3_URL"        = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
    "FACTS_API_URL"                           = "http://facts-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
    "FACTS_API_V2_URL"                        = "http://facts-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v2.0"
    "COPILOT_API_V3_URL"                      = "http://copilot-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
    "RECOMMENDATIONS_API_URL"                 = "http://recommendations-v2.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
    "REDSHIFT_API_URL"                        = "http://redshift-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}"
    "COPILOT_DATA_BUCKET"                     = local.copilot_bucket
    "TEXTRACT_BUCKET"                         = local.textract_bucket
    "NATIONWIDE_ORGANIZATION_ID"              = 6
    "SEARCH_AND_IDENTIFY_LITE_LAMBDA_NAME"    = "ingestion-search-and-identify-lite-lambda"
    "MERGE_BUSINESS_FACTS_STATE_MACHINE_ARN"  = "arn:aws:states:us-east-1:${local.workflow_parameters.account_id}:stateMachine:${local.sf_name_prefix}merge-business-facts"
    "RUNTIME_SSM_PARAMETER"                   = local.region_specific_parameters.lambda.runtime_ssm_parameter_name
    "DD_TRACE_URLLIB3_ENABLED"                = "False"
    "DD_TRACE_API_VERSION"                    = "v0.5"
    "EMAIL_FR_MODEL_ARN"                      = "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:form-data-extraction-run-email-form-recognizer-model"
    "SEND_KALEPA_EVENTS_LAMBDA_ARN"           = "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:infrastructure-events-event-sender"
    "PROCESS_LABEL_STUDIO_WEBHOOK_ACTION_ARN" = "arn:aws:states:us-east-1:${local.workflow_parameters.account_id}:stateMachine:copilot-workers-process-label-studio-webhook-action"
    "GET_ERISA_5500_DATA_LAMBDA_ARN"          = "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:ingestion-get-erisa5500-for-plan-lambda"
    "EXECUTE_TASK_ARN"                        = "arn:aws:states:us-east-1:${local.workflow_parameters.account_id}:stateMachine:copilot-workers-execute-task"
    "MDA_ENGINE_EXECUTE_TASK_ARN"             = "arn:aws:states:us-east-1:${local.workflow_parameters.account_id}:stateMachine:mda-engine-execute-task"
    "LABEL_STUDIO_BASE_URL"                   = local.env_config.label_studio_base_url
    "LABEL_STUDIO_WEBHOOK_URL"                = local.env_config.label_studio_webhook_url
    "DD_PATCH_MODULES"                        = "flask:false"
    "DD_TRACE_FLASK_ENABLED"                  = "false"
    "service"                                 = local.workflow_parameters.workload_name
    "env"                                     = local.env
    "SAFE_INIT_RESOLVE_SECRETS"               = "True"
    "SAFE_INIT_CACHE_SECRETS"                 = "True"
    "SAFE_INIT_SECRET_CACHE_REDIS_HOST"       = local.redis_cache_host
    "SAFE_INIT_SECRET_CACHE_REDIS_PORT"       = 6739
    "LAUNCH_DARKLY_API_KEY_SECRET_ARN"        = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
  }

  lambdas = {
    regexFactsMatchingStep = {
      handler     = "src.handlers.facts_matching_and_normalization.regex_facts_matching_step.handle"
      memory_size = 2048
      timeout     = 300
    }
    llmFactsMatchingStep = {
      handler     = "src.handlers.facts_matching_and_normalization.llm_facts_matching_step.handle"
      memory_size = 2048
      timeout     = 450
      environment = {
        PINECONE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-key
        PINECONE_API_ENV_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-env
        OPENAI_API_KEY_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.openai-api-key
      }
    }
    fieldsDerivationStep = {
      handler     = "src.handlers.facts_matching_and_normalization.fields_derivation_step.handle"
      memory_size = 2048
      timeout     = 300
      environment = {
        PINECONE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-key
        PINECONE_API_ENV_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-env
        OPENAI_API_KEY_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.openai-api-key
      }
    }
    valuesNormalizationStep = {
      handler     = "src.handlers.facts_matching_and_normalization.values_normalization_step.handle"
      memory_size = 2048
      timeout     = 300
      environment = {
        PINECONE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-key
        PINECONE_API_ENV_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-env
        OPENAI_API_KEY_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.openai-api-key
      }
    }
    dataValidationStep = {
      handler     = "src.handlers.facts_matching_and_normalization.data_validation_step.handle"
      memory_size = 2048
      timeout     = 300
      environment = {
        PINECONE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-key
        PINECONE_API_ENV_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-env
        OPENAI_API_KEY_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.openai-api-key
      }
    }
    llmDataValidationStep = {
      handler     = "src.handlers.facts_matching_and_normalization.llm_data_validation_step.handle"
      memory_size = 2048
      timeout     = 300
      environment = {
        PINECONE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-key
        PINECONE_API_ENV_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-env
        OPENAI_API_KEY_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.openai-api-key
      }
    }
    updateProcessingStateAndSendEventStep = {
      handler     = "src.handlers.facts_matching_and_normalization.update_processing_state_and_send_event_step.handle"
      memory_size = 2048
      timeout     = 300
    }
    handleFailStep = {
      handler     = "src.handlers.facts_matching_and_normalization.handle_fail_step.handle"
      memory_size = 2048
      timeout     = 300
    }
    factSubtypeSelectionBenchmark = {
      handler     = "src.handlers.fact_subtype_selection_benchmark.handle"
      memory_size = 2048
      timeout     = 420
      environment = {
        PINECONE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-key
        PINECONE_API_ENV_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.pinecone-api-env
        OPENAI_API_KEY_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.openai-api-key
      }
    }
    mergeBusinessesFacts = {
      handler     = "src.handlers.fact_created.merge_businesses_facts.merge_businesses_facts"
      memory_size = 1024
      timeout     = 900
    }
    onFactRequiringMergingCreated = {
      handler     = "src.handlers.fact_created.fact_requiring_merging_created.fact_requiring_merging_created"
      memory_size = 600
      timeout     = 900
      triggers = [
        {
          eventBridge = {
            event_bus = local.facts_event_bus_arn
            event_pattern = {
              source = ["facts_api"]
              detail = {
                fact_created = {
                  parent_type = ["BUSINESS"]
                  fact_subtype = {
                    id = ["FEIN"]
                  }
                  original_observation_source = ["FIRST_PARTY", "USER_FEEDBACK"]
                }
              }
            }
          }
        }
      ]
    }
    onFactWithFeedbackCreated = {
      handler     = "src.handlers.fact_created.fact_with_feedback_created.fact_with_feedback_created"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          eventBridge = {
            event_bus = local.facts_event_bus_arn
            event_pattern = {
              source = ["facts_api"]
              detail = {
                fact_created = {
                  parent_type                 = ["BUSINESS", "PREMISES", "STRUCTURE", "SUBMISSION"]
                  original_observation_source = ["USER_FEEDBACK"]
                }
              }
            }
          }
        }
      ]
    }
    emitException = {
      handler     = "src.handlers.emit_exception.emit_exception"
      memory_size = 512
      timeout     = 300
    }
    resolveSubmissionEntities = {
      handler     = "src.handlers.submission_handlers.resolve_entities.resolve_submission_entities"
      memory_size = 512
      timeout     = 300
    }
    expireSubmissions = {
      handler     = "src.handlers.expiration.expire_submissions"
      memory_size = 376
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0 * * * ? *)"
        }
      ]
    }
    sendScheduledEmails = {
      handler     = "src.handlers.scheduled_emails.send_scheduled_emails"
      memory_size = 376
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0 * ? * * *)"
        }
      ]
    }
    refreshSensibleCache = {
      handler     = "src.handlers.cache_handlers.refresh_sensible_cache"
      memory_size = 512
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0 9 * * ? *)"
        }
      ]
    }
    onFileUpload = {
      handler     = "src.handlers.files.uploaded_file"
      memory_size = 420
      timeout     = 376
      triggers = [
        {
          s3 = {
            bucket               = local.copilot_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "post/uploads/files"
          }
        }
      ]
    }
    redistributeTasksFromInactiveProjects = {
      environment = {
        LABEL_STUDIO_WEBHOOK_TOKEN_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.label-studio-webhook-token
        LABEL_STUDIO_API_TOKEN_SECRET_ARN     = local.region_specific_parameters.runtime_secret_arns.label-studio-token
      }
      handler     = "src.handlers.label_studio.redistribute_tasks"
      memory_size = 300
      timeout     = 300
    }
    cleanupOldLSTasks = {
      environment = {
        LABEL_STUDIO_WEBHOOK_TOKEN_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.label-studio-webhook-token
        LABEL_STUDIO_API_TOKEN_SECRET_ARN     = local.region_specific_parameters.runtime_secret_arns.label-studio-token
      }
      handler     = "src.handlers.label_studio.cleanup_old_tasks"
      memory_size = 300
      timeout     = 300
    }
    autoVerifySubmission = {
      handler     = "src.handlers.submission_handlers.small_functions.auto_verify_submission"
      memory_size = 512
      timeout     = 900
    }
    consolidateAcordsData = {
      handler     = "src.handlers.submission_handlers.small_functions.consolidate_acord_data"
      memory_size = 330
      timeout     = 900
    }
    deduplicateBusinesses = {
      handler     = "src.handlers.submission_handlers.deduplicate.deduplicate_businesses"
      memory_size = 512
      timeout     = 900
    }
    calculatePremisesDistances = {
      handler     = "src.handlers.submission_business_handlers.calculate_premises_distances.calculate_premises_distances"
      memory_size = 1024
      timeout     = 900
    }
    createGeoFactsForPremises = {
      handler     = "src.handlers.submission_business_handlers.create_geo_facts_for_premises.create_geo_facts_for_premises"
      memory_size = 1024
      timeout     = 900
    }
    excludeFacts = {
      handler     = "src.handlers.submission_business_handlers.exclude_facts.exclude_facts"
      memory_size = 768
      timeout     = 900
    }
    handleStuckProcessingFiles = {
      handler     = "src.handlers.reports_handlers.handle_stuck_processing_files"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0/2 * * * ? *)"
        }
      ]
    }
    handleStuckProcessingSubmissions = {
      handler     = "src.handlers.reports_handlers.handle_submissions_stuck_in_processing_state"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0/10 * * * ? *)"
        }
      ]
    }
    handleNotStartedAPISubmissions = {
      handler     = "src.handlers.reports_handlers.handle_not_started_api_submissions"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0/10 * * * ? *)"
        }
      ]
    }
    cleanupReportDuplicates = {
      handler     = "src.handlers.reports_handlers.cleanup_report_duplicates"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0/15 * * * ? *)"
        }
      ]
    }
    verifySubmissions = {
      handler     = "src.handlers.reports_handlers.verify_submissions"
      memory_size = 390
      timeout     = 240
      triggers = [
        {
          schedule = "cron(0/1 * * * ? *)"
        }
      ]
    }
    cancelProcessingForClosedSubmissions = {
      handler     = "src.handlers.reports_handlers.cancel_processing_for_closed_submissions"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0/5 * * * ? *)"
        }
      ]
    }
    missingNaicsAlerts = {
      handler     = "src.handlers.reports_handlers.create_missing_naics_alerts"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0/10 * * * ? *)"
        }
      ]
    }
    missingProcessingAlerts = {
      handler     = "src.handlers.reports_handlers.create_missing_processing_alerts"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0/30 * * * ? *)"
        }
      ]
    }
    notProcessedSubmissionsAlerts = {
      handler     = "src.handlers.reports_handlers.create_not_processed_submissions_alerts"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0/5 * * * ? *)"
        }
      ]
    }
    createNotVerifiedAlerts = {
      handler     = "src.handlers.reports_handlers.create_not_verified_alerts"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = "cron(0 */1 * * ? *)"
        }
      ]
    }
    sensibleMonitoringChecks = {
      handler = "src.handlers.sensible_monitoring.check_communication_errors"
      environment = {
        SLACK_TOKEN_SECRET_ARN = "${local.region_specific_parameters.runtime_secret_arns.slack-token}~token"
      }
      memory_size = 360
      timeout     = 300
      triggers = [
        {
          schedule = "cron(10/15 * * * ? *)"
        }
      ]
    }
    checkSupportUserReportAssigment = {
      handler     = "src.handlers.reports_handlers.check_support_user_report_assigment"
      memory_size = 420
      timeout     = 300
      triggers = [
        {
          schedule = local.schedules.only_prod_every_minute
        }
      ]
    }
    updateArchSubmissionStatus = {
      environment = {
        ARCH_API_ENABLED_SECRET_ARN       = "${local.region_specific_parameters.runtime_secret_arns.arch-api-config}~enabled"
        ARCH_API_BASE_URL_SECRET_ARN      = "${local.region_specific_parameters.runtime_secret_arns.arch-api-config}~base_url"
        ARCH_API_TOKEN_URL_SECRET_ARN     = "${local.region_specific_parameters.runtime_secret_arns.arch-api-config}~token_url"
        ARCH_API_CLIENT_ID_SECRET_ARN     = "${local.region_specific_parameters.runtime_secret_arns.arch-api-config}~client_id"
        ARCH_API_CLIENT_SECRET_SECRET_ARN = "${local.region_specific_parameters.runtime_secret_arns.arch-api-config}~client_secret"
        ARCH_API_AUDIENCE_SECRET_ARN      = "${local.region_specific_parameters.runtime_secret_arns.arch-api-config}~audience"
      }
      handler     = "src.handlers.arch_handlers.update_arch_submission_status"
      memory_size = 325
      timeout     = 300
    }
    generateSubmissionHighlight = {
      handler     = "src.handlers.submission_handlers.generate_highlight.generate_submission_highlight"
      memory_size = 512
      timeout     = 300
    }
    createAutomaticSubmissionRelations = {
      handler     = "src.handlers.submission_handlers.small_functions.create_automatic_submission_relations"
      memory_size = 512
      timeout     = 900
    }
    createAcordSOVFiles = {
      handler     = "src.handlers.submission_handlers.create_acord_sov.create_acord_sov_files"
      memory_size = 512
      timeout     = 900
    }
    getGroupedFirstPartyData = {
      handler     = "src.handlers.first_party.get_grouped_first_party_data"
      memory_size = 1280
      timeout     = 900
    }
    cacheFirstPartyDocuments = {
      handler     = "src.handlers.first_party.cache_first_party_documents"
      memory_size = 2048 # Supplemental processing uses 4M, but there are additional stuff there, so hope 2M is enough
      timeout     = 900
    }
    createRelations = {
      handler     = "src.handlers.first_party.create_relations"
      memory_size = 1536 # Required for subs with ~10k vehicles
      timeout     = 900
    }
    processFirstPartyDataBatch = {
      handler     = "src.handlers.first_party.process_first_party_data_batch"
      memory_size = 768
      timeout     = 900
      environment = {
        DD_TRACE_SAMPLE_RATE = "0.2"
      }
    }
    logFirstPartyBatchFailed = {
      handler     = "src.handlers.first_party.log_first_party_batch_failed"
      memory_size = 512
      timeout     = 300
    }
    aggregateResult = {
      handler     = "src.handlers.first_party.aggregate_results"
      memory_size = 512
      timeout     = 300
    }
    aggregateRegisteredFacts = {
      handler     = "src.handlers.first_party.aggregate_registered_facts"
      memory_size = 512
      timeout     = 300
    }
    triggerDeriveFactsForParent = {
      handler     = "src.handlers.first_party.trigger_derive_facts_for_parent"
      memory_size = 512
      timeout     = 300
    }
    resolveBusinesses = {
      handler     = "src.handlers.resolution.resolve_businesses"
      memory_size = 420
      timeout     = 900
    }
    resolveBusiness = {
      handler     = "src.handlers.resolution.resolve_business"
      memory_size = 420
      timeout     = 900
    }
    getLock = {
      handler     = "src.handlers.locks.get_lock"
      memory_size = 300
      timeout     = 320
    }
    releaseLock = {
      handler     = "src.handlers.locks.release_lock"
      memory_size = 300
      timeout     = 300
    }
    processFileFromCache = {
      handler     = "src.handlers.file_processors.files.process_file_from_cache"
      memory_size = 455
      timeout     = 300
    }
    enrichRequestWithOrganizationData = {
      handler     = "src.handlers.submission_handlers.enrich_request.enrich_request_with_organization_data"
      memory_size = 512
      timeout     = 300
    }
    sendExecutionEvent = {
      handler     = "src.handlers.resolution.send_finished_execution_event"
      memory_size = 512
      timeout     = 300
    }
    getSubmissionFiles = {
      handler     = "src.handlers.files.get_submission_files"
      memory_size = 260
      timeout     = 300
    }
    processFile = {
      handler     = "src.handlers.files.process_file"
      memory_size = 512
      timeout     = 600
    }
    processFileFailed = {
      handler     = "src.handlers.files.process_file_failed"
      memory_size = 512
      timeout     = 300
    }
    aggregateResults = {
      handler     = "src.handlers.files.aggregate_results"
      memory_size = 512
      timeout     = 300
    }
    getFileInfo = {
      handler     = "src.handlers.file_processors.get_file_info.get_file_info"
      memory_size = 512
      timeout     = 300
    }
    inferLossLobs = {
      handler                = "src.handlers.loss_run_handlers.infer_loss_lobs"
      memory_size            = 4096
      timeout                = 900
      ephemeral_storage_size = 1024
    }
    unsupportedFileType = {
      handler     = "src.handlers.file_processors.files.unsupported_file_type"
      memory_size = 450
      timeout     = 300
    }
    unprocessableFileType = {
      handler     = "src.handlers.file_processors.files.unprocessable_file_type"
      memory_size = 450
      timeout     = 300
    }
    processSOVFile = {
      handler = "src.handlers.process_sov.process_SOV_file"
      environment = {
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
      memory_size = 4096
      timeout     = 600
    }
    processNISFile = {
      handler     = "src.handlers.process_nis.process_NIS_file"
      memory_size = 768
      timeout     = 300
    }
    processCancelledFile = {
      handler     = "src.handlers.file_processors.files.process_cancelled_file"
      memory_size = 512
      timeout     = 300
    }
    processDriversFile = {
      handler = "src.handlers.file_processors.fleet.process_drivers_file"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
      memory_size = 1280
      timeout     = 600
    }
    processBudgetSpreadsheetFile = {
      handler     = "src.handlers.file_processors.budget.process_budget_spreadsheet"
      memory_size = 608
      timeout     = 300
    }
    processNoClaimsLRPdfWithNonAzureTables = {
      handler = "src.handlers.file_processors.pdf.process_no_claims_lr_pdf_with_non_azure_tables"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
      memory_size = 1524
      timeout     = 600
    }
    processBudgetPdfFrOutput = {
      handler     = "src.handlers.file_processors.budget.process_budget_pdf_fr_output"
      memory_size = 537
      timeout     = 300
    }
    processNoClaimsLossRunPdfFrOutput = {
      handler     = "src.handlers.file_processors.pdf.process_no_claims_loss_run_pdf_fr_output"
      memory_size = 512
      timeout     = 300
    }
    processFinancialStatementSpreadsheetFile = {
      handler     = "src.handlers.file_processors.financial_statements.process_financial_statement_spreadsheet"
      memory_size = 2048
      timeout     = 600
    }
    processFinancialStatementPdfFrOutput = {
      handler     = "src.handlers.file_processors.financial_statements.process_financial_statement_pdf_fr_output"
      memory_size = 1024
      timeout     = 600
    }
    processVehiclesFile = {
      handler = "src.handlers.file_processors.fleet.process_vehicles_file"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
      memory_size = 1600
      timeout     = 600
    }
    processEquipmentFile = {
      handler = "src.handlers.file_processors.fleet.process_equipment_file"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
      memory_size = 1600
      timeout     = 600
    }
    processEmail = {
      handler = "src.handlers.file_processors.email.process_email"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN    = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
      memory_size = 1512
      timeout     = 600
    }
    processLetterOfIntent = {
      handler = "src.handlers.file_processors.letter_of_intent.process_letter_of_intent"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN    = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
      memory_size = 1512
      timeout     = 600
    }
    processRawEmail = {
      handler              = "src.handlers.file_processors.raw_email.process_raw_email"
      memory_size          = 1769 # Needed for better CPU for processing bigger emails
      ephemeralStorageSize = 1024
      timeout              = 600
      environment = {
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
    }
    processCoverSheet = {
      handler = "src.handlers.file_processors.cover_sheet.process_cover_sheet"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN    = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
      memory_size = 1024
      timeout     = 600
    }
    processDirectorsAndOfficers = {
      handler = "src.handlers.file_processors.directors_and_officers.process_directors_and_officers"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
      memory_size = 465
      timeout     = 600
    }
    processPolicy = {
      handler = "src.handlers.file_processors.policy.process_policy_file"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
      memory_size = 465
      timeout     = 600
    }
    processEmployeeHandbook = {
      handler     = "src.handlers.file_processors.employee_handbook.process_employee_handbook"
      memory_size = 1024
      timeout     = 600
    }
    processTransactionFile = {
      handler     = "src.handlers.file_processors.transaction_generic.process_transaction_file"
      memory_size = 2048
      timeout     = 600
    }
    processCorporateStructure = {
      handler     = "src.handlers.file_processors.corporate_structure.process_corporate_structure"
      memory_size = 1536
      timeout     = 300
    }
    processErisaForm5500 = {
      handler     = "src.handlers.file_processors.erisa_form_5500.process_erisa_form_5500"
      memory_size = 465
      timeout     = 600
    }
    startErisaPlanIngestion = {
      handler     = "src.handlers.file_processors.erisa_form_5500.start_erisa_plan_ingestion"
      memory_size = 465
      timeout     = 600
    }
    processXmodFile = {
      handler = "src.handlers.file_processors.xmod.process_xmod_file"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
      memory_size          = 3072
      ephemeralStorageSize = 2048
      timeout              = 600
    }
    processWorkersCompPayrollSpreadsheetFile = {
      handler              = "src.handlers.file_processors.workers_comp_payroll.parse_workers_comp_payroll_spreadsheet"
      memory_size          = 1024
      ephemeralStorageSize = 1024
      timeout              = 600
    }
    processIFTAFile = {
      handler = "src.handlers.file_processors.ifta.process_ifta_file"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
      memory_size          = 1024
      ephemeralStorageSize = 1024
      timeout              = 600
    }
    processIFTASpreadsheetFile = {
      handler              = "src.handlers.file_processors.ifta.process_ifta_spreadsheet_file"
      memory_size          = 512
      ephemeralStorageSize = 512
      timeout              = 300
    }
    processAllyAutoSupplementalFile = {
      handler              = "src.handlers.file_processors.ally_auto.process_ally_auto_supplemental_file"
      memory_size          = 1024
      ephemeralStorageSize = 1024
      timeout              = 600
    }
    processAllyAutoPropertySOV = {
      handler              = "src.handlers.file_processors.ally_auto.process_ally_auto_property_sov"
      memory_size          = 1024
      ephemeralStorageSize = 1024
      timeout              = 600
    }
    processArchive = {
      handler     = "src.handlers.file_processors.archive.process_archive"
      memory_size = 1024
      timeout     = 600
    }
    processHtmlDocument = {
      handler              = "src.handlers.file_processors.html_doc.process_html_document"
      memory_size          = 1024
      ephemeralStorageSize = 1024
      timeout              = 600
    }
    processSpreadsheetSupplemental = {
      handler              = "src.handlers.file_processors.supplemental_spreadsheet.process_spreadsheet_supplemental_file"
      memory_size          = 1024
      ephemeralStorageSize = 1024
      timeout              = 600
    }
    processSOVPdf = {
      handler              = "src.handlers.file_processors.sov_pdf.process_sov_pdf"
      memory_size          = 1024
      ephemeralStorageSize = 1024
      timeout              = 600
    }
    storeProcessingResult = {
      handler     = "src.handlers.file_processors.supplementals.store_supplemental_processing_result"
      memory_size = 512
      timeout     = 300
      environment = {
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
    }
    handleFileProcessingFailure = {
      handler     = "src.handlers.file_processors.files.handle_file_processing_failure"
      memory_size = 1024
      timeout     = 300
    }
    handleFileClassificationEventSending = {
      handler     = "src.handlers.classification.handle_file_classification_event_sending"
      memory_size = 1024
      timeout     = 180
    }
    runERSSearch = {
      handler     = "src.handlers.file_processors.run_ers_search.run_ers_search"
      memory_size = 512
      timeout     = 300
    }
    getEntityFromRedis = {
      handler     = "src.handlers.get_entity_from_redis.get_entity_from_redis"
      memory_size = 2048 # (pczubak): do not lower; fast cold starts needed
      timeout     = 300
    }
    validateBusinessResolution = {
      handler     = "src.handlers.file_processors.validate_business_resolution.validate"
      memory_size = 1024
      timeout     = 900
    }
    saveBusinessResolutionData = {
      handler     = "src.handlers.file_processors.save_business_resolution_data.save_business_resolution_data"
      memory_size = 1024
      timeout     = 300
    }
    updateFileProcessingState = {
      handler     = "src.handlers.file_processors.update_processing_state.update_file_processing_state"
      memory_size = 512
      timeout     = 120
    }
    convertPDFToImages = {
      handler              = "src.handlers.pdf_to_images.pdf_to_images"
      memory_size          = 768
      ephemeralStorageSize = 768
      timeout              = 300
    }
    sendFormRecognizerOutputToLabelStudio = {
      handler = "src.handlers.label_studio.send_form_recognizer_output_to_label_studio"
      environment = {
        LABEL_STUDIO_WEBHOOK_TOKEN_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.label-studio-webhook-token
        LABEL_STUDIO_API_TOKEN_SECRET_ARN     = local.region_specific_parameters.runtime_secret_arns.label-studio-token
      }
      memory_size = 512
      timeout     = 300
    }
    triggerLabelStudioAnnotationsVerified = {
      handler = "src.handlers.file_processors.label_studio.on_label_studio_annotations_verified"
      environment = {
        LABEL_STUDIO_WEBHOOK_TOKEN_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.label-studio-webhook-token
        LABEL_STUDIO_API_TOKEN_SECRET_ARN     = local.region_specific_parameters.runtime_secret_arns.label-studio-token
      }
      memory_size = 512
      timeout     = 300
    }
    processLabelStudioAnnotation = {
      handler = "src.handlers.label_studio.process_label_studio_annotation"
      environment = {
        LABEL_STUDIO_WEBHOOK_TOKEN_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.label-studio-webhook-token
        LABEL_STUDIO_API_TOKEN_SECRET_ARN     = local.region_specific_parameters.runtime_secret_arns.label-studio-token
      }
      memory_size = 512
      timeout     = 300
    }
    getRedirectedEntities = {
      handler     = "src.handlers.submission_handlers.small_functions.get_redirected_entities"
      memory_size = 512
      timeout     = 900
    }
    mergeFactsAndDocuments = {
      handler     = "src.handlers.submission_handlers.small_functions.merge_facts_and_documents"
      memory_size = 512
      timeout     = 900
    }
    patchSubmissions = {
      handler     = "src.handlers.submission_handlers.small_functions.patch_submissions"
      memory_size = 512
      timeout     = 900
    }
    deleteMergedEntities = {
      handler     = "src.handlers.submission_handlers.small_functions.delete_merged_entities"
      memory_size = 512
      timeout     = 900
    }
    keyValuePairFactResolution = {
      handler     = "src.handlers.file_processors.fact_resolution.key_value_pair_fact_resolution"
      memory_size = 512
      timeout     = 900
    }
    fetchAffectedSubmissionBusinesses = {
      handler     = "src.handlers.submission_business_handlers.fetch_affected_submission_businesses.fetch_affected_submission_businesses"
      memory_size = 420
      timeout     = 300
    }
    patchSubmissionBusiness = {
      handler     = "src.handlers.submission_business_handlers.patch_submission_business.patch_submission_business"
      memory_size = 384
      timeout     = 300
    }
    fetchSubmissionBusinessesToPatch = {
      handler     = "src.handlers.submission_business_handlers.fetch_submission_businesses_to_be_patched.fetch_submission_businesses_to_be_patched"
      memory_size = 384
      timeout     = 300
    }
    patchSubmissionBusinessEntity = {
      handler     = "src.handlers.submission_business_handlers.associate_submission_business_with_compose_node.associate_submission_business_with_compose_node"
      memory_size = 384
      timeout     = 300
    }
    createRelationshipsForSubmission = {
      handler     = "src.handlers.submission_handlers.relationships.create_relationships_for_submission"
      memory_size = 512
      timeout     = 900
    }
    addRelationshipsForConfirmedBusiness = {
      handler     = "src.handlers.submission_handlers.relationships.add_relationships_for_confirmed_business"
      memory_size = 512
      timeout     = 900
    }
    removeRelationshipsForDisconfirmedBusiness = {
      handler     = "src.handlers.submission_handlers.relationships.remove_relationships_for_disconfirmed_business"
      memory_size = 512
      timeout     = 900
    }
    createLocationForFniEntity = {
      handler     = "src.handlers.submission_handlers.create_location.create_location_for_fni_entity"
      memory_size = 512
      timeout     = 900
    }
    createShellsForConfirmedSubmissionBusiness = {
      handler     = "src.handlers.submission_handlers.create_shells.create_shells_for_confirmed_submission_business"
      memory_size = 768
      timeout     = 900
    }
    getResolutionData = {
      handler     = "src.handlers.file_business_resolution.get_resolution_data"
      memory_size = 2048 # (pczubak): do not lower; fast cold starts needed
      timeout     = 300
    }
    aggregateSubmissionResolutionResults = {
      handler     = "src.handlers.submission_handlers.aggregate_results.aggregate_submission_resolution_results"
      memory_size = 512
      timeout     = 300
    }
    getSubmissionFilesForConfirmation = {
      handler     = "src.handlers.submission_handlers.get_files.get_submission_files_for_confirmation"
      memory_size = 512
      timeout     = 720
    }
    getValidationFiles = {
      handler = "src.handlers.sensible_templates.get_validation_files"
      environment = {
        SENSIBLE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.sensible-api-key
      }
      memory_size = 512
      timeout     = 300
    }
    validateTemplate = {
      handler = "src.handlers.sensible_templates.validate_template"
      environment = {
        SENSIBLE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.sensible-api-key
      }
      memory_size = 512
      timeout     = 900
    }
    checkValidationStatus = {
      handler = "src.handlers.sensible_templates.check_validation_status"
      environment = {
        SLACK_TOKEN_SECRET_ARN      = "${local.region_specific_parameters.runtime_secret_arns.slack-token}~token"
        SENSIBLE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.sensible-api-key
      }
      memory_size = 512
      timeout     = 300
    }
    getFilesForReprocessing = {
      handler = "src.handlers.sensible_templates.get_files_for_reprocessing"
      environment = {
        SLACK_TOKEN_SECRET_ARN      = "${local.region_specific_parameters.runtime_secret_arns.slack-token}~token"
        SENSIBLE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.sensible-api-key
      }
      memory_size = 512
      timeout     = 300
    }
    reprocessFile = {
      handler = "src.handlers.file_processors.files.reprocess_file"
      environment = {
        SLACK_TOKEN_SECRET_ARN      = "${local.region_specific_parameters.runtime_secret_arns.slack-token}~token"
        SENSIBLE_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.sensible-api-key
      }
      memory_size = 512
      timeout     = 300
    }
    processSupplemental = {
      handler = "src.handlers.file_processors.supplementals.process_supplemental_file"
      environment = {
        AZURE_READ_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.azure-read-api-key
        AZURE_READ_ENDPOINT_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.azure-read-endpoint
      }
      memory_size          = 4096
      ephemeralStorageSize = 2048
      timeout              = 720
    }
    syncParagonImsToCopilot = {
      handler     = "src.handlers.copilot_adhoc_tasks.start_capi_adhoc_task"
      memory_size = 512
      timeout     = 120
      triggers = [
        {
          schedule = {
            expression = local.schedules.sync_paragon_ims_to_copilot
            input = {
              task_name               = "paragon_ims_to_copilot_sync"
              max_allowed_concurrency = 5
              executing_user_email    = "<EMAIL>"
              task_input = {
                age_in_months                        = 3
                safety_breakpoint_on_processed_count = 10000
                full_sync                            = "no"
              }
            }
          }
        }
      ]
    }
    fullSyncParagonImsToCopilot = {
      handler     = "src.handlers.paragon.full_sync_paragon_ims_to_copilot"
      memory_size = 512
      timeout     = 120
      triggers = [
        {
          schedule = local.schedules.weekly_full_sync_paragon_ims_to_copilot
        }
      ]
    }
    dailyParagonImsReportWithMissingLinks = {
      handler     = "src.handlers.copilot_adhoc_tasks.start_capi_adhoc_task"
      memory_size = 512
      timeout     = 120
      triggers = [
        {
          schedule = {
            expression = local.schedules.daily_paragon_ims_report_with_missing_links
            input = {
              task_name               = "paragon_ims_daily_missing_report"
              max_allowed_concurrency = 1
              executing_user_email    = "<EMAIL>"
              task_input = {
                from_last_hours = -1
              }
            }
          }
        }
      ]
    }
    paragonBackfillMissingImsLinks = {
      handler     = "src.handlers.paragon.backfill_missing_ims_links"
      memory_size = 512
      timeout     = 120
      triggers = [
        {
          schedule = local.schedules.paragon_backfill_missing_ims_links
        }
      ]
    }
    getWorkersCompExpFromApi = {
      handler = "src.handlers.paragon_handlers.get_wc_experience_from_api"
      environment = {
        SAFE_INIT_DLQ                         = local.region_specific_parameters.events.safe_init_dlq_sqs.url
        WC_BUREAU_CA_PRIMARY_KEY_SECRET_ARN   = local.region_specific_parameters.runtime_secret_arns.wc-bureau-ca-primary
        WC_BUREAU_CA_SECONDARY_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.wc-bureau-ca-secondary
      }
      memory_size = 512
      timeout     = 300
    }
    generateReviewDocument = {
      handler = "src.handlers.nationwide_ml_handlers.generate_review_document"
      environment = {
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
      memory_size = 512
      timeout     = 300
    }
    prepareMergeBusinessFactsRequests = {
      handler     = "src.handlers.fact_merger.prepare_merge_business_facts_requests.prepare_merge_business_facts_requests"
      memory_size = 512
      timeout     = 900
    }
    mergeFactsPerFactSubtypeBatch = {
      handler     = "src.handlers.fact_merger.merge_facts_per_fact_subtype_batch.merge_facts_per_fact_subtype_batch"
      memory_size = 1400 # Don't lower - needed for bigger submissions
      timeout     = 900
    }
    logMergeFactsPerFactSubtypeBatchFailed = {
      handler     = "src.handlers.fact_merger.log_merge_facts_per_fact_subtype_batch_failed.log_merge_facts_per_fact_subtype_batch_failed"
      memory_size = 512
      timeout     = 300
    }
    finalizeMergeBusinessFactsRequests = {
      handler     = "src.handlers.fact_merger.finalize_merge_business_facts_requests.finalize_merge_business_facts_requests"
      memory_size = 512
      timeout     = 300
    }
    createEntityRelationsForSubmission = {
      handler     = "src.handlers.create_relations_from_onboarded_data.create_entity_relations"
      memory_size = 512
      timeout     = 300
    }
    initTaskExecution = {
      handler     = "src.handlers.task_execution.init_task_execution.init_task_execution"
      memory_size = 512
      timeout     = 300
    }
    processTaskExecutionOutput = {
      handler     = "src.handlers.task_execution.process_task_execution_output.process_task_execution_output"
      memory_size = 512
      timeout     = 300
    }
    storeTaskOutput = {
      handler     = "src.handlers.task_execution.store_task_output.store_task_output"
      memory_size = 512
      timeout     = 300
    }
    testModel = {
      handler     = "src.handlers.task_execution.test_model.test_model"
      memory_size = 512
      timeout     = 300
    }
    executeTaskModel = {
      handler     = "src.handlers.tasks.model_task_executor.execute_task_model"
      memory_size = 640
      timeout     = 300
    }
    executeTaskModelXl = {
      handler     = "src.handlers.tasks.model_task_executor.execute_task_model"
      memory_size = 1512
      timeout     = 300
    }
    consolidateEmailSubmodels = {
      handler     = "src.handlers.tasks.email.consolidate_email_submodels"
      memory_size = 640
      timeout     = 300
    }
    storeEmailClassifications = {
      handler     = "src.handlers.email_classification.store_email_classifications"
      memory_size = 640
      timeout     = 300
    }
    mergeFileProcessingResults = {
      handler     = "src.handlers.file_processors.merge_processing_results.merge_file_processing_results"
      memory_size = 700
      timeout     = 300
    }
    checkDescriptionOfOperationsConsistency = {
      handler     = "src.handlers.submission_handlers.consistency_check.check_consistency_of_description"
      memory_size = 512
      timeout     = 900
    }
    generateOverviewSummary = {
      handler     = "src.handlers.submission_handlers.overview_summarization.generate_overview_summary"
      memory_size = 512
      timeout     = 600
    }
    createCustomizableClassifierDataset = {
      handler     = "src.handlers.customizable_classifier_dataset.create_customizable_classifier_dataset.handle"
      memory_size = 2048
      timeout     = 420
    }
    alignClientStages = {
      handler     = "src.handlers.align_client_stages_handler.align_client_stages"
      memory_size = 512
      timeout     = 120
      triggers = [
        {
          schedule = local.schedules.align_client_stages
        }
      ]
    }
  }
}
