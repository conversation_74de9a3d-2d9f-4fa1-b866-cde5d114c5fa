from static_common.enums.enum import StrEnum


class FeatureFlags(StrEnum):
    ENABLE_COPILOT_TO_PARAGON_IMS_STATUS_SYNC = "enable-copilot-to-paragon-ims-status-sync"
    ENABLE_PARAGON_SUBMISSION_BY_COVERAGE_SPLIT = "enable-paragon-submission-by-coverage-split"
    PARAGON_IMS_CIRCUIT_BREAKER_MANUAL_INTERNAL = "paragon-ims-circuit-breaker-manual-internal"
    PARAGON_IMS_CIRCUIT_BREAKER_MANUAL_PUBLIC = "paragon-ims-circuit-breaker-manual-public"
    PARAGON_IMS_CLEARING_ENABLED = "paragon-ims-clearing-enabled"
    PARAGON_IMS_FALLBACK_INSURED_SEARCH_BY_NAME_ONLY = "paragon-ims-fallback-insured-search-by-name-only"
    PARAGON_IMS_PSP_E3_INTEGRATION_ENABLED = "paragon-ims-psp-e3-be-integration-enabled"
    PARAGON_IMS_TRIDENT_INTEGRATION_ENABLED = "paragon-ims-trident-integration-enabled"
    PARAGON_IMS_ES_FORWARD_INTEGRATION_KILL_SWITCH = "paragon-ims-es-forward-integration-kill-switch"
    FILE_PROCESSING_QUEUE_ENABLED = "file-processing-queue-enabled"
    ARCH_UW_SYNC_ENABLED = "arch-uw-sync-enabled"
    SUBMISSION_NEWS_ENABLED = "submission-news-enabled"
    ENABLE_NEWS_SUMMARY_GENERATION = "enable-news-summary-generation"
    SKIP_NATIONWIDE_LITE_REQUESTS = "skip-nationwide-lite-requests"
    ARU_SUREFYRE_INTEGRATION_ENABLED = "aru-surefyre-integration-enabled"
    CONIFER_FINYS_INTEGRATION_ENABLED = "conifer-finys-integration-enabled"
    CONIFER_FINYS_INTEGRATION_TARGET_UAT = "conifer-finys-integration-target-uat"
    CONSISTENCY_CHECK_REQUESTED = "consistency-check-requested"
    """https://app.launchdarkly.com/projects/default/flags/consistency-check-requested/targeting?env=production&env=stage&env=test&selected-env=production"""
    PDS_CUSTOMIZABLE_CLASSIFIERS = "pds-customizable-classifiers"
    """https://app.launchdarkly.com/projects/default/flags/pds-customizable-classifiers/"""
    ENABLE_SUBMISSION_BACKFILL = "enable-submission-backfill"
