import json
import os
import re
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import date, datetime, timedelta
from pathlib import Path
from typing import Callable, Dict, List, Optional, Set, Tuple

from common.clients.feature_flags import FeatureFlagsClient
from common.clients.slack import SlackClient
from common.logic.addresses import try_parse_address_string
from common.logic.identification.legal_entity import (
    get_business_type_from_legal_entity_name,
)
from common.logic.paragon import IMSSearchInput, ParagonEmails
from copilot_client_v3 import (
    AssignedUnderwriter,
    BrokerageEmployee,
    File,
    ParagonCompanyLine,
    ProcessedFile,
    RequestedCoverage,
    Submission,
    SubmissionClientId,
)
from dateutil.relativedelta import relativedelta
from infrastructure_common.logging import get_logger
from paragon_ims_api_client.models.clearance_info import ClearanceInfo
from paragon_ims_api_client.models.insured import Insured
from paragon_ims_api_client.models.paragon_enums import BusinessTypes
from paragon_ims_api_client.models.quote_status import (
    QuoteD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    QuoteStatus,
)
from paragon_ims_api_client.models.supplementary_data import IMSSupplementaryData
from paragon_ims_api_client.paragon_ims_client import ParagonIMSAPIClient
from sqlalchemy.orm import Session
from static_common.enums.business import BusinessType, ParagonBusinessTypeMapping
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.coverage_names import CoverageName
from static_common.enums.entity import EntityFieldID, EntityInformation
from static_common.enums.enum import StrEnum
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.origin import Origin
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.models.address import Address
from static_common.models.file_onboarding import OnboardedFile
from static_common.models.paragon import ImsCoverage, ParagonClientSubmissionId
from static_common.models.submission_action.paragon import ParagonSubmissionInfo
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
from static_common.schemas.paragon import ParagonClientSubmissionIdSchema
from structlog.stdlib import BoundLogger

from infrastructure_events.clients.copilot import CopilotV3Client
from infrastructure_events.config.feature_flags import FeatureFlags
from infrastructure_events.model.paragon_ims_circuit_breakers import (
    CircuitBreakerStatus,
    ImsCircuitBreakerEvent,
    ParagonImsCircuitBreakers,
)

logger = get_logger()
sub_client_id_schema = ParagonClientSubmissionIdSchema()

onboarded_file_schema = LeanOnboardedFileSchema()

AUTO_CIRCUIT_BREAKER_ERRORS_CHECK_MINUTES = 60
AUTO_CIRCUIT_BREAKER_ERRORS_THRESHOLD = 25

EXTRA_CONTEXT_UA_GUID = "paragon_underwriter_assistant_guid"
ES_FORWARD_TEMPORARY_SKIP_MESSAGE = (
    "Paragon E&S Forward submissions were not supported at the time this request came in"
)


class ParagonLineOfBusiness(StrEnum):
    EXCESS_AND_SURPLUS = "Excess and Surplus"
    WORKERS_COMPENSATION = "Workers Compensation"
    ALLY_AUTO = "Ally Auto"
    PSP_E3_PROPERTY = "PSP E3 Property"
    TRIDENT_PUBLIC_RISK = "Trident Public Risk"

    def is_wc(self):
        return self == ParagonLineOfBusiness.WORKERS_COMPENSATION

    def is_es(self):
        return self == ParagonLineOfBusiness.EXCESS_AND_SURPLUS

    def is_ally_auto(self):
        return self == ParagonLineOfBusiness.ALLY_AUTO

    def is_trident_public_risk(self):
        return self == ParagonLineOfBusiness.TRIDENT_PUBLIC_RISK

    def is_psp_e3_property(self):
        return self == ParagonLineOfBusiness.PSP_E3_PROPERTY

    @staticmethod
    def paragon_es_gl_primary_name():
        return "Casualty E&S  General Liability Primary"

    @staticmethod
    def paragon_es_gl_excess_supported_name():
        return "Casualty E&S Excess (Supported)"

    @staticmethod
    def paragon_es_gl_excess_unsupported_name():
        return "Casualty E&S Excess (Unsupported)"

    @staticmethod
    def paragon_workers_comp_name():
        return "Workers Compensation"

    @staticmethod
    def ally_auto_falls_lake_dealers_package_name():
        return "Falls Lake Auto Dealers Package"

    @staticmethod
    def ally_auto_falls_lake_dealers_umbrella_name():
        return "Falls Lake Auto Dealers Umbrella"

    @staticmethod
    def ally_auto_auto_dealers_equipment_breakdown_name():
        return "Auto Dealers Equipment Breakdown"

    @staticmethod
    def paragon_psp_e3_property_name():
        return "E&S Excess Package"


class ParagonImsCreationIssues(StrEnum):
    MISSING_REQUESTED_BUSINESS_NAME = "Missing *requested business name* on First Named Insured"
    MISSING_FNI = "Missing *First Named Insured*"
    MISSING_COUNTRY = "Missing *country* on First Named Insured"
    MISSING_STATE = "Missing *state* on First Named Insured"
    MISSING_CITY = "Missing *city* on First Named Insured"
    MISSING_ZIP_CODE = "Missing *zip code* on First Named Insured"
    COULD_NOT_PARSE_FNI_ADDRESS = "Could not parse First Named Insured address"
    MISSING_COUNTY = "Missing *county* on First Named Insured and for Ally Auto it is required"
    MISSING_FEIN = "Missing *FEIN* on First Named Insured (WC check only)"
    MISSING_COPILOT_BROKER_EMAIL = "Missing Copilot *broker email*"
    MISSING_IMS_BROKER = "Missing IMS *broker*"
    MISSING_IMS_PRODUCER_GUID = "Missing *Paragon Producer GUID* for broker email"
    MISSING_IMS_PRODUCER_CONTACT_GUID = "Missing *Paragon Producer Contact GUID* for broker email"
    MISSING_IMS_PRODUCER_LOCATION_GUID = "Missing *Paragon Producer Location GUID* for broker email"
    MISSING_COPILOT_UNDERWRITER = "Missing Copilot *underwriter*"
    MISSING_IMS_UNDERWRITER = "Missing *Paragon Underwriter*"
    MISSING_WC_UW_MAPPING = "Missing *mapping for WC Underwriter*"
    MISSING_PSP_UW_MAPPING = "Missing *mapping for PSP/E3 Underwriter*"
    AMBIGUOUS_COPILOT_UNDERWRITER = "Ambiguous *Copilot Underwriter*, can't pick the right one"
    MISSING_COPILOT_COVERAGES = "Missing Copilot *coverages*"

    # Temporary for the E&S forward flow
    RECOVERABLE_MISSING_COPILOT_COVERAGES = "Missing Copilot's *coverages* (to be manually assigned)"

    MISSING_IMS_LOBS = "Missing Paragon *Company Lines* for Copilot Coverages"
    MULTIPLE_INSUREDS_FOR_SINGLE_FEIN = "There are multiple InsuredGUIDs associated with this FEIN"
    GENERIC_FAILURE = "Issue with Copilot creating submission in IMS"

    @classmethod
    def is_related_to_paragon_broker(cls, issue: str) -> bool:
        """Check if the issue is related to Paragon Broker/Agency based on its prefix."""
        relevant_prefixes = {
            cls.MISSING_IMS_BROKER,
            cls.MISSING_IMS_PRODUCER_GUID,
            cls.MISSING_IMS_PRODUCER_CONTACT_GUID,
            cls.MISSING_IMS_PRODUCER_LOCATION_GUID,
            cls.MISSING_WC_UW_MAPPING,
            cls.MISSING_PSP_UW_MAPPING,
        }
        return any(prefix in issue for prefix in relevant_prefixes)

    @classmethod
    def is_related_to_missing_copilot_data(cls, issue: str) -> bool:
        """Check if the issue is related to missing data in Copilot based on its prefix."""
        relevant_prefixes = {
            cls.MISSING_REQUESTED_BUSINESS_NAME,
            cls.MISSING_FNI,
            cls.MISSING_COUNTRY,
            cls.MISSING_STATE,
            cls.MISSING_CITY,
            cls.MISSING_ZIP_CODE,
            cls.MISSING_COUNTY,
            cls.COULD_NOT_PARSE_FNI_ADDRESS,
            cls.MISSING_FEIN,
            cls.MISSING_COPILOT_BROKER_EMAIL,
            cls.MISSING_COPILOT_UNDERWRITER,
            cls.MISSING_IMS_UNDERWRITER,
            cls.AMBIGUOUS_COPILOT_UNDERWRITER,
            cls.MISSING_COPILOT_COVERAGES,
            cls.RECOVERABLE_MISSING_COPILOT_COVERAGES,
            cls.MISSING_IMS_LOBS,
            cls.GENERIC_FAILURE,
        }
        return any(prefix in issue for prefix in relevant_prefixes)

    @classmethod
    def get_action_description(cls, issue: str, report_url: str) -> str | None:
        """Returns steps/actions required to fix the issue."""
        # Detect the enum member
        matching_issue = None
        for issue_member in cls:
            if issue_member.value in issue:
                matching_issue = issue_member
                break

        if not matching_issue:
            logger.warning(f"Unknown issue '{issue}' in ParagonImsCreationIssues", issue_ping="plenza")
            return None

        tab = "&emsp;"
        mtab = f"{tab}{tab}{tab}"
        broker_with_same_name_but_diff_email_issue_content = "However there is IMS broker for person"
        manual_creation_needed_action = f"{tab}1. Create this submission manually in IMS<br>"

        match matching_issue:
            case cls.MISSING_WC_UW_MAPPING | cls.MISSING_PSP_UW_MAPPING:
                return (
                    f"{tab}1. Go to the submission in Copilot and assign the proper underwriter, then retry"
                    f" IMS:<br>{mtab}a. {report_url}<br>{mtab}b. In top right, click on Action -> Assign<br>{mtab}c."
                    " Enter in the correct underwriter to be assigned. Make sure 1 and only 1 underwriter is"
                    f" assigned<br>{mtab}d. Click `Retry IMS Creation` button<br>{tab}2. Send updated broker <>"
                    " underwriter mapping in the normal email flow<br>"
                )
            case cls.MISSING_IMS_BROKER:
                if broker_with_same_name_but_diff_email_issue_content in issue:
                    return (
                        f"{tab}1. Determine if the broker email address from Copilot represents the similar broker in"
                        f" IMS<br>{mtab}a. If so, please update the broker`s email in IMS to reflect"
                        f" Copilot`s<br>{mtab}b. If not, please add the Copilot broker as a new broker in"
                        f" IMS<br>{tab}2. Go to the submission in Copilot and retry IMS creation:<br>{mtab}a."
                        f" {report_url}<br>{mtab}b. Click `Retry IMS Creation` button<br>"
                    )
                return (
                    f"{tab}1. Add the broker in IMS<br>{tab}2. Go to the submission in Copilot and retry IMS"
                    f" creation:<br>{mtab}a. {report_url}<br>{mtab}b. Click `Retry IMS Creation` button<br>"
                )
            case cls.AMBIGUOUS_COPILOT_UNDERWRITER | cls.MISSING_IMS_UNDERWRITER:
                return (
                    f"{tab}1. Go to the submission in Copilot and assign the proper underwriter, then retry"
                    f" IMS:<br>{mtab}a. {report_url}<br>{mtab}b. In top right, click on Action -> Assign<br>{mtab}c."
                    " Enter in the correct underwriter to be assigned. Make sure 1 and only 1 underwriter is"
                    f" assigned<br>{mtab}d. Click `Retry IMS Creation` button<br>"
                )
            case cls.MULTIPLE_INSUREDS_FOR_SINGLE_FEIN:
                return (
                    f"{tab}1. Consolidate the Insured records in IMS<br>{tab}2. Go to the submission in Copilot and"
                    f" retry IMS creation:<br>{mtab}a. {report_url}<br>{mtab}b. Click `Retry IMS Creation`"
                    " button<br>"
                )
            case cls.RECOVERABLE_MISSING_COPILOT_COVERAGES:
                return (
                    f"{tab}1. Go to the submission in Copilot and assign the proper coverages, then retry"
                    f" IMS:<br>{mtab}a. {report_url}<br>{mtab}b. On the left, click on Submission"
                    f" Information<br>{mtab}c. Enter in the correct coverages to be assigned.<br>{mtab}d. Click `Retry"
                    " IMS Creation` button<br>"
                )
            case cls.GENERIC_FAILURE:
                return manual_creation_needed_action
            case _:
                return manual_creation_needed_action


class ParagonFolders(StrEnum):
    # Typos in folder names are OK == this is how they are named in Paragon IMS system
    ACORD = "Documentation \\Applications\\Accord"
    COMPANY_LOSS_RUN = "Documentation \\Loss  History\\Company Loss Runs"
    MISC = "Documentation \\Applications\\Misc"
    SOV = "Documentation \\Applications\\SOV"
    SUPPLEMENTAL_APPLICATIONS = "Documentation \\Applications\\Supplemental Applicatons"
    AGENCY_CORRESPONDENCE = "Documentation \\Correspondence\\Agency Correspondence"
    DMV_SCHEDULE = "Documentation \\Automobile\\DMV Schedule"
    AUDITED_FINANCIAL_REPORTS = "Documentation \\Financials\\Audited  Financial Reports"
    UNKNOWN = "Documentation \\Applications"


@dataclass
class InsuredSearchResult:
    got_copilot_business: bool = False
    found_insureds: Optional[List[Insured]] = None

    # Data to be used to create a new Insured in IMS
    insured_requested_name: Optional[str] = None
    state: Optional[str] = None
    city: Optional[str] = None
    country: Optional[str] = None
    county: Optional[str] = None
    address1: Optional[str] = None
    address2: Optional[str] = None
    zip_code: Optional[str] = None
    business_type: Optional[BusinessTypes] = None
    fein: Optional[str] = None
    raw_address_string: Optional[str] = None

    # Data to be used to search for existing Insured in IMS (it's more extensive)
    ims_search_input: IMSSearchInput = field(default_factory=IMSSearchInput)


@dataclass
class BrokerSearchResults:
    broker_email: Optional[str]
    broker_other_email_in_ims: Optional[str]
    broker_name: Optional[str]
    got_producer_info: Optional[bool]
    missing_paragon_producer_info: Optional[bool]
    producer_contact_guid: Optional[str]
    producer_guid: Optional[str]
    producer_name: Optional[str]
    producer_location_guid: Optional[str]


class ParagonNotificationType(StrEnum):
    SUCCESSFULLY_CREATED_IN_IMS = "SUCCESSFULLY_CREATED_IN_IMS"
    FAILED_TO_CREATE_IN_IMS = "FAILED_TO_CREATE_IN_IMS"
    SKIPPED_IMS_CREATION = "SKIPPED_IMS_CREATION"


class ParagonBusinessRules:
    ALLY_FINANCIAL_AGENCY_NAME = "Ally Financial"

    # This UW is assigned to Paragon submissions for Workers Comp coverage == IMS does not support this yet.
    COPILOT_UW_FOR_PARAGON_WORKERS_COMP = "<EMAIL>"
    # And this is the name of coverage that we use for Workers Comp
    WORKERS_COMP_COVERAGE_NAME = "workersComp"
    # This UW is assigned to Paragon submissions for PSP coverage when we need them to do manual intervention
    COPILOT_UW_FOR_PARAGON_PSP = "<EMAIL>"
    # This UW is assigned to Paragon submissions for Trident Public Risk when we need them to do manual intervention
    COPILOT_UW_FOR_PARAGON_TRIDENT = "<EMAIL>"

    @staticmethod
    def pick_underwriter(
        log, copilot_underwriters: List[AssignedUnderwriter], slack_msg: str
    ) -> Tuple[str, Optional[AssignedUnderwriter]]:
        # Priorities in Copilot assigned UWs defined in descending order
        priorities = ["MANUAL", "EMAIL", "RECOMMENDATIONS", "AUTO", "NONE"]

        # Filter out Paragon Ally-Auto underwriter; It is sometimes present on Ally-Auto subs but it does not
        # exist in IMS and has no GUID.
        filtered_underwriters = [uw for uw in copilot_underwriters if uw.email != "<EMAIL>"]

        if not filtered_underwriters:
            log.info("[PARAGON_IMS_QUEUE_HANDLER] No underwriters assigned in Copilot")
            return slack_msg, None

        # Sort underwriters by priority
        def get_priority_index(uw: AssignedUnderwriter) -> int:
            if uw.source is None or uw.source.upper() not in priorities:
                return priorities.index("NONE")
            else:
                return priorities.index(uw.source.upper())

        sorted_underwriters = sorted(filtered_underwriters, key=get_priority_index)

        # If there is more than 1 assigned UW in Copilot and for the same highest priority there is again more than 1
        # then we cannot select the right one (required for IMS) and we need to raise an error. Such submission
        # needs to be handled manually.
        if len(sorted_underwriters) > 1:
            if sorted_underwriters[0].source == sorted_underwriters[1].source:
                log.info(
                    "[PARAGON_IMS_QUEUE_HANDLER] More than 1 underwriter"
                    " assigned in Copilot with the same (highest) priority"
                )
                slack_msg += (
                    "*[######## ERROR ########]* More than 1 underwriter"
                    f" assigned in Copilot with the same ({sorted_underwriters[0].source}) priority\n"
                )
                return slack_msg, None

        return slack_msg, sorted_underwriters[0]

    @staticmethod
    def should_run_ims_search_if_fein_search_was_a_miss(lob: ParagonLineOfBusiness) -> bool:
        return lob == ParagonLineOfBusiness.EXCESS_AND_SURPLUS

    @staticmethod
    def pick_ims_insured(insureds: list[Insured]) -> Insured | None:
        # Pick first found Insured for now
        if not insureds:
            return None

        return insureds[0]

    @staticmethod
    def pick_copilot_renewal_submission(
        renewal_submissions: list[Submission], control_number: str, broker: BrokerageEmployee
    ) -> Submission | None:
        if not renewal_submissions:
            return None

        filtered_renewal_submissions = []
        control_numbers = {control_number}

        for renewal_submission in renewal_submissions:
            client_ids: list[SubmissionClientId] = renewal_submission.client_submission_ids
            client_ids_raw = {client_id.client_submission_id for client_id in client_ids}

            if not renewal_submission.is_renewal:
                continue

            if not renewal_submission.broker:
                continue

            if renewal_submission.broker.id != broker.id:
                continue

            if client_ids_raw == control_numbers:
                filtered_renewal_submissions.append(renewal_submission)

        if len(filtered_renewal_submissions) > 1:
            return None

        if not filtered_renewal_submissions:
            return None
        else:
            return max(filtered_renewal_submissions, key=lambda x: x.created_at)

    @staticmethod
    def file_supported_for_ims_upload(file: File, all_submission_files: List[File]) -> bool:
        # 1. IMS won't allow uploading files without extension and they are likely invalid anyway
        if not ParagonBusinessRules._has_extension(file.name):
            return False

        # 2. If file is ARCHIVE then it is not supported but its unpacked contents are and will be sent to IMS
        if file.file_type == FileType.ARCHIVE.value:
            return False

        # 3. If parent file is present and there is a parent file with "id" matching
        # this parent_file_id that is ARCHIVE, the file is supported.
        if file.parent_file_id:
            parent_file = next((f for f in all_submission_files if f.id == file.parent_file_id), None)
            if parent_file and parent_file.file_type == FileType.ARCHIVE.value:
                return True
            else:
                # 4. If file.parent_file_id is present and parent is not ARCHIVE, file is not supported.
                #    It is a split that Kalepa processing created.
                return False

        # 5. if file.is_internal is True, file is not supported unless file_type is EMAIL or RAW_EMAIL
        #    or CORRESPONDENCE_EMAIL
        if file.is_internal and file.file_type not in [
            FileType.EMAIL.value,
            FileType.RAW_EMAIL.value,
            FileType.CORRESPONDENCE_EMAIL.value,
        ]:
            return False

        # 6. Otherwise, file is supported.
        return True

    @staticmethod
    def _has_extension(file_name: str) -> bool:
        _, ext = os.path.splitext(file_name)
        return bool(ext)

    @staticmethod
    def create_map_of_paragon_folders_to_ids(ims_supplementary_data: IMSSupplementaryData) -> Dict[ParagonFolders, int]:
        """
        This creates a map of Paragon folders that we need and use (in ParagonFolders enum) to their IDs because
        IDs are required when uploadaing files to IMS.
        """
        paragon_folders_by_name = {
            ims_folder.folder_name: ims_folder for ims_folder in ims_supplementary_data.ims_folders
        }
        return {
            paragon_folder: paragon_folders_by_name[paragon_folder.value].folder_id for paragon_folder in ParagonFolders
        }

    @staticmethod
    def calculate_policy_dates(effective_date: datetime) -> (date, date):
        """
        Calculate the expiration date based on the effective date.
        If the effective date is in the past, the expiration date should be 1 year from
        the 1st of the following-effective-date month.
        If the effective date is today or in the future, the expiration date should be
        1 year from the effective date.
        """
        current_date = datetime.today().date()
        if not effective_date:
            effective_date = current_date
        else:
            effective_date = effective_date.date()

        # If effective_date is in the past, set it to the 1st of the next month
        if effective_date < current_date:
            future_month = current_date.month + 1 if current_date.month < 12 else 1
            future_year = current_date.year if current_date.month < 12 else current_date.year + 1
            effective_date = datetime(future_year, future_month, 1).date()

        # Set expiration_date to one year from the effective_date
        try:
            expiration_date = datetime(effective_date.year + 1, effective_date.month, effective_date.day)
        except ValueError:
            # Handle leap year
            if effective_date.month == 2 and effective_date.day == 29:
                expiration_date = datetime(effective_date.year + 1, 2, 28)
            else:
                raise

        return effective_date, expiration_date.date()

    @staticmethod
    def pick_line_of_business(
        lines: List[ParagonCompanyLine],
        paragon_lob: ParagonLineOfBusiness,
        fni_state: str,
        wc_class_codes: Set[str] | None,
        policy_effective_date: datetime,
    ) -> ParagonCompanyLine:
        """
        Paragon has multiple lines of business but for different companies.
        Ally auto is a special case where we need to hand pick the one starting with "Falls Lake" and also WC
        is a special case where we need to hand pick the one starting with "Clear Spring Property and Casualty Company".
        Temp: E&S is also a special case because subs with eff date >= 2024-08-01 should be assigned to carrier
              Canopius and other ones to previously used Falls Lake E&S.
        """
        normalized_policy_effective_date = policy_effective_date.date() if policy_effective_date else None
        match paragon_lob:
            case ParagonLineOfBusiness.ALLY_AUTO:
                for line in lines:
                    if line.location_name.startswith("Falls Lake"):
                        return line
            case ParagonLineOfBusiness.EXCESS_AND_SURPLUS:
                for line in lines:
                    if normalized_policy_effective_date and normalized_policy_effective_date >= date(2024, 8, 1):
                        if line.location_name.startswith("Canopius"):
                            return line
                    elif line.location_name.startswith("Falls Lake"):
                        return line
                # If no specific line is found, return the first one by default (fallback)
                return lines[0]
            case ParagonLineOfBusiness.WORKERS_COMPENSATION:
                wc_required_location_name = ParagonBusinessRules._pick_wc_location_name(fni_state, wc_class_codes)
                for line in lines:
                    if line.location_name.startswith(wc_required_location_name):
                        return line
            case ParagonLineOfBusiness.PSP_E3_PROPERTY:
                for line in lines:
                    if line.location_name.startswith("PSP E3 Excess Package"):
                        return line
            case _:
                raise NotImplementedError(
                    f"'pick_line_of_business' op is not implemented for Paragon LOB {paragon_lob}"
                )

    @staticmethod
    def get_file_description(file: File) -> str:
        """
        They need to have uploaded documents with descriptions matching specific mapping.
        Should be blank "" for some.
        """

        # Detected MERGED files should be described by file name, not "Multiple Files" (as for MERGED enum).
        if file.file_type == FileType.MERGED:
            return file.name
        description = COPILOT_FILE_TYPE_TO_PARAGON_DESCRIPTION.get(file.file_type, file.name)
        if "unknown" == description.lower():
            return file.name
        return description

    @staticmethod
    def normalize_insured_name(insured_name: str):
        """
        Tries to strip some common prefixes from insured name, like remove leading 'owner'
        or trailing 'project:' and 'dba'/'dba:' and everything after them, and strip leading/trailing
        spaces and specific punctuation.
        """
        if not insured_name:
            return insured_name

        # Remove leading "owner" or "owner:"
        owner_pattern = r"^(OWNER\s*:?\s*)"
        modified_insured_name = re.sub(owner_pattern, "", insured_name, flags=re.IGNORECASE)

        # Remove "project:" and everything after it (colon must be present)
        project_pattern = r"\bproject:.*$"
        modified_insured_name = re.sub(project_pattern, "", modified_insured_name, flags=re.IGNORECASE)

        # Remove "dba" or "dba:" and everything after it
        dba_pattern = r"\bdba:?\b.*$"
        modified_insured_name = re.sub(dba_pattern, "", modified_insured_name, flags=re.IGNORECASE)

        # Remove extra spaces (happens sometimes)
        consecutive_spaces_pattern = r"\s{2,}"
        modified_insured_name = re.sub(consecutive_spaces_pattern, " ", modified_insured_name)

        # Strip trailing whitespace and selected punctuation chars (,;:)
        return modified_insured_name.rstrip().rstrip(",;:")

    @staticmethod
    def is_workers_comp_coverage(submission: Submission, submission_email_account: Optional[str]) -> bool:
        # If sub came from WC email account return True quickly
        if (
            submission_email_account
            and submission_email_account.lower() == ParagonEmails.WORKERS_COMPENSATION.kalepa_forwarding_email()
        ):
            return True

        # Fallback - if special Copilot User-UW for workers comp is assigned to submission, return True
        if submission.assigned_underwriters:
            uw_emails = [uw.email for uw in submission.assigned_underwriters]
            if ParagonBusinessRules.COPILOT_UW_FOR_PARAGON_WORKERS_COMP in uw_emails:
                return True

        return False

    @staticmethod
    def is_psp_e3_property_coverage(submission_email_account: Optional[str]) -> bool:
        if (
            submission_email_account
            and submission_email_account.lower() == ParagonEmails.PSP_E3_PROPERTY.kalepa_forwarding_email()
        ):
            return True

        return False

    @staticmethod
    def is_trident_public_risk_coverage(submission_email_account: Optional[str]) -> bool:
        if (
            submission_email_account
            and submission_email_account.lower() == ParagonEmails.TRIDENT_PUBLIC_RISK.kalepa_forwarding_email()
        ):
            return True

        return False

    @staticmethod
    def is_es_auto_forward(submission_email_account: Optional[str]) -> bool:
        return submission_email_account and submission_email_account == "<EMAIL>"

    @staticmethod
    def is_renewal(submission: Submission) -> bool:
        return submission.is_renewal

    @staticmethod
    def is_ally_auto(submission_email_account: Optional[str]) -> bool:
        return submission_email_account and submission_email_account == "<EMAIL>"

    @staticmethod
    def get_paragon_folder(log, file: File, submission_files: List[File]) -> ParagonFolders:
        """
        This method tries to determine the Paragon folder for the given Copilot submission file.
        For any file type that is not MERGED, it is easy - we just use the mapping from
        COPILOT_FILE_TYPE_TO_PARAGON_FOLDER. However, because we do not upload splits into IMS, only MERGED,
        we need to determine the folder based on the most frequent file type of the children files.
        """
        if file.file_type == FileType.MERGED.value:
            children_split_files = [
                split_file for split_file in submission_files if split_file.parent_file_id == file.id
            ]
            # Count occurrences of each file type
            file_type_histogram = defaultdict(int)
            for split_file in children_split_files:
                file_type_histogram[split_file.file_type] += 1

            # Find the most frequent file type
            if not file_type_histogram:
                log.info("[PARAGON_IMS_QUEUE_HANDLER] No children files for MERGED file")
                return ParagonFolders.UNKNOWN
            most_frequent_file_type = max(file_type_histogram, key=file_type_histogram.get)
            log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Most frequent file type in MERGED file: {most_frequent_file_type}")
            paragon_folder = COPILOT_FILE_TYPE_TO_PARAGON_FOLDER.get(most_frequent_file_type, ParagonFolders.UNKNOWN)
        else:
            paragon_folder = COPILOT_FILE_TYPE_TO_PARAGON_FOLDER.get(file.file_type, ParagonFolders.UNKNOWN)

        return paragon_folder

    @staticmethod
    def leave_only_issues_related_to_paragon_broker(issue_list: List[str]) -> List[str]:
        if not issue_list:
            return issue_list

        return [issue for issue in issue_list if ParagonImsCreationIssues.is_related_to_paragon_broker(issue)]

    @staticmethod
    def leave_only_issues_related_to_missing_copilot_data(issue_list: List[str]) -> List[str]:
        if not issue_list:
            return issue_list

        return [issue for issue in issue_list if ParagonImsCreationIssues.is_related_to_missing_copilot_data(issue)]

    @staticmethod
    def get_action_list_for_issues(issue_list: List[str], report_url: str) -> List[str]:
        if not issue_list:
            return issue_list

        return [
            action
            for issue in issue_list
            if (action := ParagonImsCreationIssues.get_action_description(issue, report_url)) is not None
        ]

    @staticmethod
    def strip_trailing_county_word(county_name: str) -> str:
        # This regular expression looks for the last occurrence of 'county'
        # (in any case) at the end of the string and replaces it with an empty string.
        # It removes only SINGLE word 'county' at the end of the string.
        return re.sub(r"(?i)\bcounty\b\s*$", "", county_name).rstrip()

    @staticmethod
    def try_to_determine_business_type(
        log, business_type: Optional[str], insured_requested_name: Optional[str]
    ) -> BusinessTypes:
        def convert_to_paragon_business_type(bt: str):
            try:
                return BusinessTypes[ParagonBusinessTypeMapping[bt]]
            except KeyError:
                log.error(f"Unable to convert business type {bt} to Paragon Business Type")
                return BusinessTypes.OTHER

        # First, try to get business type from insured_requested_name.
        if insured_requested_name:
            possible_business_type = get_business_type_from_legal_entity_name(insured_requested_name)
            log.info(f"Possible business type from insured_requested_name: {possible_business_type}")
            if possible_business_type and possible_business_type != BusinessType.OTHER:
                return convert_to_paragon_business_type(possible_business_type)

        # If the above fails or returns OTHER, fallback to the original business_type.
        log.info("Converting Business type from ERS", business_type=business_type)
        paragon_business_type = (
            convert_to_paragon_business_type(business_type) if business_type else BusinessTypes.OTHER
        )
        return paragon_business_type

    @staticmethod
    def is_copy(submission: Submission) -> bool:
        if not submission.origin:
            return False
        return submission.origin.lower() == Origin.COPY.value.lower()

    @staticmethod
    def is_sync(submission: Submission) -> bool:
        if not submission.origin:
            return False
        return submission.origin.lower() == Origin.SYNC.value.lower()

    @staticmethod
    def submission_has_no_required_files(submission: Submission) -> bool:
        if not submission.files:
            return True

        got_only_email_related_files = all(
            file_lite.file_type in [FileType.EMAIL, FileType.RAW_EMAIL, FileType.CORRESPONDENCE_EMAIL]
            for file_lite in submission.files
        )
        return got_only_email_related_files

    @staticmethod
    def _load_processed_data(processed_data: dict) -> OnboardedFile:
        return onboarded_file_schema.load(processed_data)

    @staticmethod
    def extract_business_data_from_attachments(
        submission: Submission, slack_msg: str, copilot_client: CopilotV3Client, bound_log: BoundLogger
    ) -> tuple[str, InsuredSearchResult]:
        result = InsuredSearchResult(country="USA")

        address_string = None
        fni_requested_name = None
        used_file_id = None

        # Try to find shell supported file and then get its processed data with entity information and
        # then pull Address string and requested business name.
        for file_lite in submission.files:
            if file_lite.file_type not in [FileType.EMAIL, FileType.ACORD_FORM]:
                continue
            used_file_id = file_lite.id

            processed_file: ProcessedFile = copilot_client.get_processed_file_by_file_id(file_id=file_lite.id)
            if processed_file and processed_file.processed_data:
                processed_data = ParagonBusinessRules._load_processed_data(processed_file.processed_data)
                relevant_idxs = [
                    idx for idx, entity in enumerate(processed_data.entities) if entity.is_resolvable_entity
                ]
                # Both formats have the method implemented
                if not fni_requested_name:
                    fni_requested_name = processed_data.get_entity_info_value_for_entity(
                        [EntityInformation.BUSINESS_NAME.value, EntityFieldID.NAME.value], relevant_idxs
                    )
                if not address_string:
                    address_string = processed_data.get_entity_info_value_for_entity(
                        [EntityInformation.BUSINESS_MAILING_ADDRESS.value, EntityFieldID.ADDRESS.value],
                        relevant_idxs,
                    )

        bound_log.info(
            "Extracted business data from attached file",
            address_string=address_string,
            fni_requested_name=fni_requested_name,
            used_file_id=used_file_id,
        )

        # If something is present in email data, try to parse it
        parsed_address: Address = None
        if address_string and address_string.lower().strip() != "unknown":
            result.raw_address_string = address_string
            parsed_address = try_parse_address_string(address_string)
            if not parsed_address:
                bound_log.info(
                    "Failed to parse address from file data, will fallback to dummy", address_string=address_string
                )
                slack_msg += "Failed to parse address from file data, will fallback to dummy\n"

        # If no address in email data or parsing it failed, fallback to use default address
        if not parsed_address:
            address_string = "45 Nod Road, Avon, CT 06001"  # Provided by Paragon as a dummy-default address for shells
            bound_log.info("Using Paragon default address for shells", address_string=address_string)
            slack_msg += "Using Paragon default address for shells\n"
            result.raw_address_string = address_string
            parsed_address = try_parse_address_string(address_string)

        bound_log.info(
            "Parsed address from processed file from Copilot",
            address_string=address_string,
            parsed_address=parsed_address,
        )

        result.ims_search_input.requested_address = parsed_address
        result.ims_search_input.requested_name = fni_requested_name

        result.city = parsed_address.city
        result.country = "US"
        result.address1 = parsed_address.address_line_1
        result.address2 = parsed_address.address_line_2
        result.state = parsed_address.state
        result.zip_code = parsed_address.zip_code
        result.got_copilot_business = True

        if fni_requested_name:
            result.insured_requested_name = ParagonBusinessRules.normalize_insured_name(fni_requested_name)

        result.business_type = ParagonBusinessRules.try_to_determine_business_type(
            bound_log, None, result.insured_requested_name
        )

        return slack_msg, result

    @staticmethod
    def is_missing_fni_address(result: InsuredSearchResult) -> bool:
        return not result.city or not result.state or not result.zip_code or not result.address1

    @staticmethod
    def set_fni_address_from_requested_address(
        result: InsuredSearchResult, requested_address: Optional[str], bound_log: BoundLogger
    ) -> None:
        address_log = bound_log.bind(requested_address=requested_address)
        if not requested_address or requested_address.lower().strip() == "unknown":
            address_log.info("Requested address is unknown or missing")
            return

        result.raw_address_string = requested_address
        parsed_address: Address = try_parse_address_string(requested_address)
        if not parsed_address:
            address_log.info("Failed to parse requested address")
            return

        address_log.info("Parsed requested address", parsed_address=parsed_address)
        result.city = parsed_address.city
        result.zip_code = parsed_address.zip_code
        result.state = parsed_address.state
        result.address1 = parsed_address.address_line_1
        result.address2 = parsed_address.address_line_2

    @staticmethod
    def get_guid_of_underwriter_assistant(ims_underwriter_guid: str, request_additional_context: dict) -> Optional[str]:
        if request_additional_context and EXTRA_CONTEXT_UA_GUID in request_additional_context:
            return request_additional_context[EXTRA_CONTEXT_UA_GUID]
        # Fallback to our hardcoded mapping (still required for E&S for which we don't have Paragon Rest API
        # to get agency info with assistant)
        return PARAGON_UW_TO_UW_ASSISTANT.get(ims_underwriter_guid)

    @staticmethod
    def is_quote_auditable(ims_coverage: str, primary_naics_code: Optional[str]) -> bool:
        # All WC quotes are auditable
        if ims_coverage == ImsCoverage.WORKERS_COMPENSATION:
            return True

        # All ES GL Primary accounts are auditable with the exception of Real Estate and Habitational risks,
        # which are not auditable. Use NAICS = `53*`
        if ims_coverage == ImsCoverage.CASUALTY_PRIMARY and primary_naics_code:
            return not primary_naics_code.upper().startswith("NAICS_53")

        # Everything else is not auditable (for example E&S Excess or PSP)
        return False

    @staticmethod
    def normalize_fein(fein_str: str) -> str | None:
        if not fein_str:
            return None
        fein_str = fein_str.strip()

        # Check if the string contains only digits, hyphens, and spaces
        if not re.match(r"^[\d\s-]+$", fein_str):
            return None

        # Remove hyphens and spaces
        cleaned_fein = fein_str.replace("-", "").replace(" ", "")

        # Check if the cleaned string is exactly 9 digits long
        if len(cleaned_fein) == 9 and cleaned_fein.isdigit():
            # Format to Paragon required FEIN format with single hyphen
            return f"{cleaned_fein[:2]}-{cleaned_fein[2:]}"
        else:
            return None

    @staticmethod
    def get_triage_result(request_additional_context: dict) -> str | None:
        if request_additional_context and "triage_result" in request_additional_context:
            return request_additional_context["triage_result"]
        return None

    @staticmethod
    def any_file_failed_processing(submission: Submission, classification: ClassificationDocumentType) -> bool:
        return any(
            file_lite.classification == classification and file_lite.processing_state != FileProcessingState.COMPLETED
            for file_lite in submission.files
        )

    @staticmethod
    def determine_quote_initial_status(
        submission: Submission,
        paragon_lob: ParagonLineOfBusiness,
        insured_search_result: InsuredSearchResult,
        policy_effective_date: datetime,
        broker_email: str,
        request_additional_context: dict,
        paragon_ims_client: ParagonIMSAPIClient,
        ims_clearing_enabled: bool,
        bound_log: BoundLogger,
    ) -> tuple[QuoteStatus, QuoteDeclinedStatusReason | None]:
        triage_result = ParagonBusinessRules.get_triage_result(request_additional_context)

        if triage_result == "DECLINE_NOT_ELIGIBLE_PRODUCER":
            bound_log.info(
                "Triage result is DECLINE_NOT_ELIGIBLE_PRODUCER, setting initial status to DECLINED",
                lob=paragon_lob,
                triage_result=triage_result,
            )
            return QuoteStatus.DECLINED, QuoteDeclinedStatusReason.PRODUCER_NOT_ELIGIBLE_FOR_CLASS_OF_BUSINESS

        if triage_result and triage_result.startswith("DECLINE"):
            bound_log.info(
                "Triage result is DECLINE, setting initial status to DECLINED",
                lob=paragon_lob,
                triage_result=triage_result,
            )
            return QuoteStatus.DECLINED, None

        if not paragon_lob.is_wc():
            bound_log.info("Quote is not WC, setting initial status to SUBMITTED")
            return QuoteStatus.SUBMITTED, None

        # WC only
        # No FEIN == Declined but only if all ACORD_130 files were processed successfully.
        # Otherwise we don't know for sure and should assume that FEIN is present.
        if not insured_search_result.fein and not ParagonBusinessRules.any_file_failed_processing(
            submission, ClassificationDocumentType.ACORD_130
        ):
            bound_log.info("Quote is WC and no FEIN found, setting initial status to DECLINED")
            return QuoteStatus.DECLINED, None

        # Triage - declined by FEIN
        if triage_result == "BLOCKED_FEIN":
            bound_log.info("Quote is WC and triage result is BLOCKED_FEIN, setting initial status to DECLINED - OTHER")
            return QuoteStatus.DECLINED, QuoteDeclinedStatusReason.FEIN_BLOCKED

        # WC only
        # Check if the submission has at least one of each required file type or valid substitutes;
        # If not, it should be Declined - Incomplete
        has_acord_form = any(file_lite.file_type == FileType.ACORD_FORM for file_lite in submission.files)

        wc_sub_is_complete = has_acord_form
        if not wc_sub_is_complete:
            bound_log.info(
                "Quote is WC and one or more required documents are missing, setting initial status to DECLINED -"
                " INCOMPLETE"
            )
            return QuoteStatus.DECLINED, QuoteDeclinedStatusReason.INCOMPLETE

        if ims_clearing_enabled:
            if triage_result == "BLOCKED_AT_MARKET":
                bound_log.info(
                    "Quote is WC but IMS already has cleared submission, setting initial status to"
                    " Declined-Blocked-at-Market"
                )
                return QuoteStatus.DECLINED, QuoteDeclinedStatusReason.BLOCKED_AT_MARKET
            else:
                bound_log.info("Quote is WC and IMS does not have cleared submission")

        bound_log.info("Quote is WC and all conditions met, setting initial status to SUBMITTED")
        return QuoteStatus.SUBMITTED, None

    @staticmethod
    def set_address_parts_from_openai_response(
        openai_response: str, insured_search_result: InsuredSearchResult, bound_log: BoundLogger
    ) -> None:
        # Example response string. It should always be 5 parts divided by newlines. If ChatGPT could not find
        # any part it should return 'Not present' for that part.
        # response_string = """'1. Zip code: 92201
        #                       2. City: Indio
        #                       3. State: CA
        #                       4. Address line 1: PO Box 4100 Jefferson St.
        #                       5. Address line 2: Ste 403-311'"""

        # Split the string into lines
        lines = openai_response.split("\n")

        # Extract the value after "N. " in each line, considering the first occurrence of ": " only
        extracted_values = tuple(line.split(": ", 1)[1] for line in lines if ": " in line)
        bound_log.info("Extracted address parts from OpenAI response", extracted_values=extracted_values)

        if len(extracted_values) != 5:
            bound_log.info("OpenAI response does not contain 5 address parts, skipping")
            return

        def _assign_value_if_present(value: str) -> str | None:
            if not value or "not present" in value.lower():
                return None
            return value

        insured_search_result.zip_code = _assign_value_if_present(extracted_values[0])
        insured_search_result.city = _assign_value_if_present(extracted_values[1])
        insured_search_result.state = _assign_value_if_present(extracted_values[2])
        insured_search_result.address1 = _assign_value_if_present(extracted_values[3])
        insured_search_result.address2 = _assign_value_if_present(extracted_values[4])

        insured_search_result.got_copilot_business = True
        bound_log.info(
            "Updated InsuredSearchResult with address parts from OpenAI response",
            insured_search_result=insured_search_result,
        )

    @staticmethod
    def normalize_country_iso_code(country: str) -> str:
        if not country:
            return country
        return country.strip().upper().replace("US", "USA")

    @staticmethod
    def _pick_wc_location_name(fni_state: str, wc_class_codes: Set[str]) -> str:
        # A default WC carrier
        wc_location_name = "Clear Spring Property and Casualty Company"

        if not fni_state or not wc_class_codes:
            return wc_location_name

        # These are requirements to pick "Service American Indemnity Company" as WC carrier
        if fni_state.upper() not in ["NY", "WI", "MN"] and wc_class_codes.intersection(
            {"8107", "8116", "8267", "8028", "8264", "8265", "8500", "8847", "9403", "9401-1", "9401-2", "9401-3"}
        ):
            wc_location_name = "Service American Indemnity Company"

        return wc_location_name

    @staticmethod
    def fetch_all_submission_files_and_their_data(
        bound_log: BoundLogger, submission: Submission
    ) -> dict[str, OnboardedFile]:
        # Sort submission files by priority for FEIN extraction - first FNI FEIN will have top priority but
        # if it is not found, we will try to extract FEIN from other files and not from FNI.
        def _file_priority_for_fein_extraction(file_type):
            priority_order = {FileType.ACORD_FORM: 1, FileType.EMAIL: 2, FileType.SUPPLEMENTAL_FORM: 3}
            # Return a high number for other types to sort them at the end
            return priority_order.get(file_type, 1000)

        # Fetch all submission files and their processed_data for types we support
        sorted_files = sorted(submission.files, key=lambda file: _file_priority_for_fein_extraction(file.file_type))
        submission_processed_data = {}
        for file in sorted_files:
            if file.file_type == FileType.COVER_SHEET:
                # Cover sheets are not yet migrated to new OnboardedFile format
                # TODO(plenza): remove this when https://kalepa.atlassian.net/browse/ENG-20818 is released
                continue

            processed_file: ProcessedFile = file.processed_file
            if processed_file and processed_file.processed_data:
                processed_data: OnboardedFile = ParagonBusinessRules._load_processed_data(processed_file.processed_data)
                if not isinstance(processed_data, OnboardedFile):
                    actual_type = type(processed_data).__name__
                    bound_log.error(
                        "Processed data is not of type OnboardedFile", file_id=file.id, actual_type=actual_type
                    )
                    continue
                submission_processed_data[file.id] = processed_data
        return submission_processed_data

    @staticmethod
    def _find_fni_entities_indices(
        bound_log: BoundLogger, submission_processed_data: dict[str, OnboardedFile]
    ) -> dict[str, int]:
        # Go over all entities and find indices of FNIs for each file. They will be prioritised for FEIN extraction.
        fni_entity_idx = {}
        for file_id, processed_data in submission_processed_data.items():
            for idx, entity in enumerate(processed_data.entities):
                if (
                    entity.entity_named_insured
                    and entity.entity_named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED.value
                ):
                    fni_entity_idx[file_id] = idx
                    break
        bound_log.info("Found FNI entity indices", fni_entity_idx=fni_entity_idx)
        return fni_entity_idx

    @staticmethod
    def _normalize_date(date_value: str | datetime) -> datetime.date:
        if isinstance(date_value, datetime):
            return date_value.date()
        else:
            return datetime.strptime(date_value, "%Y-%m-%dT%H:%M:%S").date()

    @staticmethod
    def check_if_broker_has_bound_sub_for_previous_year(
        broker_name: str,
        lob: ParagonLineOfBusiness,
        copilot_coverages: list[RequestedCoverage],
        policy_effective_date: datetime,
        clearance_info_list: List[ClearanceInfo],
    ) -> bool:
        valid_line_names = ParagonBusinessRules.get_paragon_line_names(lob, copilot_coverages)
        normalized_policy_effective_date = policy_effective_date.date() if policy_effective_date else None

        for clearance in clearance_info_list:
            if clearance.effective_date:
                normalized_clearance_date = ParagonBusinessRules._normalize_date(clearance.effective_date)
            else:
                normalized_clearance_date = None

            if (
                clearance.line_name in valid_line_names
                and clearance.producer_name.strip() == broker_name.strip()
                and clearance.quote_status_id == QuoteStatus.BOUND.value
                and normalized_clearance_date
                and normalized_policy_effective_date
            ):
                # If IMS has BOUND policy for the same brokerage and coverage but with effective date exactly 1 year in
                # the past, we know we received a renewal
                one_year_prior = normalized_policy_effective_date - relativedelta(years=1)
                if normalized_clearance_date == one_year_prior:
                    return True
        return False

    @staticmethod
    def detect_ims_control_numbers_for_renewal(
        broker_name: str,
        lob: ParagonLineOfBusiness,
        copilot_coverages: list[RequestedCoverage],
        policy_effective_date: datetime,
        clearance_info_list: List[ClearanceInfo],
    ) -> dict[str, int]:
        detected_control_numbers = {}
        normalized_policy_effective_date = policy_effective_date.date() if policy_effective_date else None
        valid_line_names = ParagonBusinessRules.get_paragon_line_names(lob, copilot_coverages)

        for clearance in clearance_info_list:
            if clearance.effective_date:
                normalized_clearance_date = ParagonBusinessRules._normalize_date(clearance.effective_date)
            else:
                normalized_clearance_date = None

            if (
                clearance.line_name in valid_line_names
                and clearance.producer_name.strip() == broker_name.strip()
                and normalized_clearance_date
                and normalized_policy_effective_date
                and normalized_policy_effective_date == normalized_clearance_date
            ):
                detected_control_numbers[clearance.line_name] = clearance.control_no

        return detected_control_numbers

    @staticmethod
    def get_paragon_line_names(
        paragon_lob: ParagonLineOfBusiness, copilot_coverages: list[RequestedCoverage]
    ) -> list[str]:
        """
        Given Paragon program (LOB) and current Copilot coverages, returns list of valid Paragon company lines that
        can be used for this submission. This is useful when creating quote in IMS where we need to know exactly
        what lines we need to create for Copilot sub and its coverages but also when detecting if submission has
        already cleared or renewed submission.
        """
        match paragon_lob:
            case ParagonLineOfBusiness.ALLY_AUTO:
                return [
                    ParagonLineOfBusiness.ally_auto_falls_lake_dealers_package_name(),
                    ParagonLineOfBusiness.ally_auto_falls_lake_dealers_umbrella_name(),
                    ParagonLineOfBusiness.ally_auto_auto_dealers_equipment_breakdown_name(),
                ]
            case ParagonLineOfBusiness.WORKERS_COMPENSATION:
                return [ParagonLineOfBusiness.paragon_workers_comp_name()]
            case ParagonLineOfBusiness.EXCESS_AND_SURPLUS:
                liability_primaries = 0
                liability_excesses = 0
                for coverage in copilot_coverages:
                    if coverage.coverage.name.lower() == CoverageName.Liability.value:
                        if coverage.coverage_type.lower() == "primary":
                            liability_primaries += 1
                        elif coverage.coverage_type.lower() == "excess":
                            liability_excesses += 1

                # Only EXCESS coverage found in submission == Unsupported in Paragon
                if liability_primaries == 0 and liability_excesses == 1:
                    return [ParagonLineOfBusiness.paragon_es_gl_excess_unsupported_name()]
                else:
                    lines = []
                    if liability_primaries == 1:
                        lines.append(ParagonLineOfBusiness.paragon_es_gl_primary_name())
                    if liability_excesses == 1:
                        lines.append(ParagonLineOfBusiness.paragon_es_gl_excess_supported_name())
                    return lines
            case ParagonLineOfBusiness.PSP_E3_PROPERTY:
                return [ParagonLineOfBusiness.paragon_psp_e3_property_name()]
            case _:
                raise NotImplementedError(
                    f"'get_paragon_line_names' op is not implemented for Paragon LOB: {paragon_lob}"
                )

    @staticmethod
    def get_prioritized_submission_for_blocked_at_market_check(
        clearance_info_list: list[ParagonSubmissionInfo],
    ) -> ParagonSubmissionInfo:
        if len(clearance_info_list) == 0:
            raise ValueError("No clearance info list provided")

        declined_but_not_blocked_at_market = None
        for clearance in clearance_info_list:
            # Prioritize submission that was declined but not because being blocked at market
            # This means that we want to find the original submission considered by Paragon
            if (
                clearance.quote_status_id == QuoteStatus.DECLINED.value
                and clearance.quote_status_reason_id != QuoteDeclinedStatusReason.BLOCKED_AT_MARKET.value
            ):
                declined_but_not_blocked_at_market = clearance
                break

        if declined_but_not_blocked_at_market:
            return declined_but_not_blocked_at_market

        not_blocked_at_market = None
        for clearance in clearance_info_list:
            if clearance.quote_status_reason_id != QuoteDeclinedStatusReason.BLOCKED_AT_MARKET.value:
                not_blocked_at_market = clearance
                break

        if not_blocked_at_market:
            return not_blocked_at_market

        return clearance_info_list[0]

    def get_lowercased_retrying_user_email(request_additional_context: dict) -> str | None:
        if request_additional_context and "retrying_user_email" in request_additional_context:
            return request_additional_context["retrying_user_email"].strip().lower()
        return None

    @staticmethod
    def is_retrying_user_email_whitelisted(retrying_user_email: str) -> bool:
        whitelisted_domains = {
            "kalepa.com",
            "kalepa.co",
            "paragoninsgroup.com",
            "tridentpublicrisk.com",  # Paragon outsourcing partner
        }
        return any(retrying_user_email.endswith(domain) for domain in whitelisted_domains)

    @staticmethod
    def clean_filename(filename: str | None) -> str | None:
        if not filename:
            return filename

        # Get file extension (if any)
        path_obj = Path(filename)
        extension = path_obj.suffix

        # Remove non-ASCII characters
        cleaned = re.sub(r"[^\x00-\x7F]+", "", filename)

        # Check if the result would be empty or just the extension
        if not cleaned or cleaned == extension:
            # Return a default name with original extension or a generic name (IMS won't accept empty names)
            return f"file{extension}" if extension else "unnamed_file"

        if cleaned.strip() == ".":
            return "unnamed_file"

        return cleaned


COPILOT_FILE_TYPE_TO_PARAGON_FOLDER = {
    FileType.ACORD_FORM: ParagonFolders.ACORD,
    FileType.ALLY_AUTO_SOV: ParagonFolders.SOV,
    FileType.ARCHIVE: ParagonFolders.UNKNOWN,
    FileType.BUDGET: ParagonFolders.UNKNOWN,
    FileType.CAT_REQUEST_FORM: ParagonFolders.UNKNOWN,
    FileType.CAT_RESULT: ParagonFolders.UNKNOWN,
    FileType.COMPANY_BYLAWS: ParagonFolders.UNKNOWN,
    FileType.COMPANY_STRUCTURE: ParagonFolders.UNKNOWN,
    FileType.CONSOLIDATED_FINANCIAL_STATEMENT: ParagonFolders.AUDITED_FINANCIAL_REPORTS,
    FileType.COPILOT_TERMS_AND_CONDITIONS: ParagonFolders.UNKNOWN,
    FileType.CORRESPONDENCE_EMAIL: ParagonFolders.AGENCY_CORRESPONDENCE,
    FileType.DRIVERS: ParagonFolders.DMV_SCHEDULE,
    FileType.EMAIL: ParagonFolders.AGENCY_CORRESPONDENCE,
    FileType.EMPLOYEE_HANDBOOK: ParagonFolders.UNKNOWN,
    FileType.FINANCIAL_STATEMENT: ParagonFolders.AUDITED_FINANCIAL_REPORTS,
    FileType.GEOTECH_REPORT: ParagonFolders.UNKNOWN,
    FileType.HIRING_GUIDELINES: ParagonFolders.UNKNOWN,
    FileType.IFTA: ParagonFolders.UNKNOWN,
    FileType.LOSS_RUN: ParagonFolders.COMPANY_LOSS_RUN,
    FileType.LOSS_RUN_SUMMARY: ParagonFolders.COMPANY_LOSS_RUN,
    FileType.MERGED: ParagonFolders.UNKNOWN,
    FileType.NAMED_INSURED_SCHEDULE: ParagonFolders.UNKNOWN,
    FileType.OTHER: ParagonFolders.UNKNOWN,
    FileType.RAW_EMAIL: ParagonFolders.AGENCY_CORRESPONDENCE,
    FileType.RESUME: ParagonFolders.UNKNOWN,
    FileType.SAFETY_MANUAL: ParagonFolders.UNKNOWN,
    FileType.SITE_REPORT: ParagonFolders.UNKNOWN,
    FileType.SOV: ParagonFolders.SOV,
    FileType.SUPPLEMENTAL_FORM: ParagonFolders.SUPPLEMENTAL_APPLICATIONS,
    FileType.UNKNOWN: ParagonFolders.UNKNOWN,
    FileType.VEHICLES: ParagonFolders.UNKNOWN,
    FileType.VEHICLES_UNDERLYING_POLICY: ParagonFolders.UNKNOWN,
    FileType.WELL_SCHEDULE: ParagonFolders.UNKNOWN,
    FileType.WORK_COMP_EXPERIENCE: ParagonFolders.UNKNOWN,
    FileType.WORK_COMP_PAYROLL: ParagonFolders.UNKNOWN,
}

COPILOT_FILE_TYPE_TO_PARAGON_DESCRIPTION = {
    FileType.ACORD_FORM: "ACORD",
    FileType.ALLY_AUTO_SOV: "SOV",
    FileType.ARCHIVE: FileType.ARCHIVE.value,
    FileType.BUDGET: FileType.BUDGET.value,
    FileType.CAT_REQUEST_FORM: FileType.CAT_REQUEST_FORM.value,
    FileType.CAT_RESULT: FileType.CAT_RESULT.value,
    FileType.COMPANY_BYLAWS: FileType.COMPANY_BYLAWS.value,
    FileType.COMPANY_STRUCTURE: FileType.COMPANY_STRUCTURE.value,
    FileType.CONSOLIDATED_FINANCIAL_STATEMENT: "Financials",
    FileType.COPILOT_TERMS_AND_CONDITIONS: FileType.COPILOT_TERMS_AND_CONDITIONS.value,
    FileType.DRIVERS: "Driver List",
    FileType.EMAIL: "Sub from Agt",
    FileType.EMPLOYEE_HANDBOOK: FileType.EMPLOYEE_HANDBOOK.value,
    FileType.FINANCIAL_STATEMENT: "Financials",
    FileType.GEOTECH_REPORT: FileType.GEOTECH_REPORT.value,
    FileType.HIRING_GUIDELINES: FileType.HIRING_GUIDELINES.value,
    FileType.IFTA: FileType.IFTA.value,
    FileType.LOSS_RUN: "Loss Run",
    FileType.LOSS_RUN_SUMMARY: "Loss Run Summary",
    FileType.MERGED: FileType.MERGED.value,
    FileType.NAMED_INSURED_SCHEDULE: "SOV",
    FileType.OTHER: FileType.OTHER.value,
    FileType.RAW_EMAIL: "Sub from Agt",
    FileType.RESUME: FileType.RESUME.value,
    FileType.SAFETY_MANUAL: FileType.SAFETY_MANUAL.value,
    FileType.SITE_REPORT: FileType.SITE_REPORT.value,
    FileType.SOV: "SOV",
    FileType.SUPPLEMENTAL_FORM: "Supp",
    FileType.UNKNOWN: FileType.UNKNOWN.value,
    FileType.VEHICLES: "Vehicles",
    FileType.VEHICLES_UNDERLYING_POLICY: FileType.VEHICLES_UNDERLYING_POLICY.value,
    FileType.WELL_SCHEDULE: FileType.WELL_SCHEDULE.value,
    FileType.WORK_COMP_EXPERIENCE: FileType.WORK_COMP_EXPERIENCE.value,
    FileType.WORK_COMP_PAYROLL: FileType.WORK_COMP_PAYROLL.value,
}

PARAGON_UW_TO_UW_ASSISTANT = {
    # -- Main Underwriters
    #   -- E&S
    # <EMAIL>       b19fe654-9cea-4678-bec2-c994a0354235
    # <EMAIL>     f0e704e8-6ebc-4480-90e4-5a371e29d152
    # <EMAIL>       35202dac-5abf-457f-b1b7-2337f7ab84b9
    # <EMAIL>     bfe24cf4-dcfe-4572-8145-976a7958697c
    # <EMAIL>  cac3030d-b868-49c1-bad8-2c899f7d8e98
    # <EMAIL>      2bd335ee-5096-43e9-b1d1-50a5343ff786
    # <EMAIL>      60252514-7ae4-4ce2-8b4f-64e2cafbeb89
    #   -- WC
    # <EMAIL>      2f3df043-2b47-4017-b117-a00f79bb0bda
    # <EMAIL>      74b47853-79f3-4fea-ae90-6821c9687b58
    # bpizzurro                       b8aadd79-c67e-41a0-b751-11e9b2d23010
    # <EMAIL>      d9189d41-b847-4965-89fa-206faa994b5f
    # <EMAIL>   0deab81b-d6ab-47d4-92d6-5a30769e165d
    # <EMAIL>  40196577-d3d8-4b98-b29f-a864bc049edf
    # <EMAIL>      5b6fadf1-0546-4de1-b9a0-fe080fb17831
    # <EMAIL>    480919c9-8603-4621-a1bd-b465ae016ad5
    # <EMAIL>     9605fed8-9c02-4aac-b9e0-805086300a9c
    # caugon                          ae2a2755-bec6-4cad-a4bc-6f8da2b54604
    # rzimmerman                      ec42ead2-840d-42ad-b0ee-4e81abfb8473
    # ssullivan                       6b2c6a40-907c-4d22-b74f-bb66e12f9016
    # lsmith                          67e21eea-d28c-4b89-9808-c6f044216a21
    # -- Assistants
    # <EMAIL>     5a06366c-5812-4e66-9e3d-da6677c58d02
    # <EMAIL>       7f2c899e-879c-437b-b9e6-f58563315405
    # <EMAIL>       14fab712-731e-4985-a780-ec4907a12bac
    # <EMAIL>      54d0cfac-3e81-46f6-96e1-dfed810565c8
    # <EMAIL>      fdd93e3a-6838-4ead-91c7-a90e914190c0
    # <EMAIL>      808fe259-e848-4242-8934-b2bca3cfc704
    # <EMAIL>     e2a9f30e-447f-44e0-8651-8b6411820ac3
    # <EMAIL>    d5dab331-7c87-4d5f-b1e0-216cf04983d0
    # <EMAIL> 3ede3026-09df-4960-af60-2c40fac48e62
    # <EMAIL>     83e8c9bb-577a-47e1-9451-137b156e366a
    # btackett                        ab22162b-a1e1-432d-826b-89dbab5c94a5
    # ltulimero                       28376b5f-aae6-48a7-951e-9029045d06fe
    # kostrander                      aa734355-c95a-462f-98fa-f951bef97ffe
    # bsmith                          312766b1-b79d-4d9f-96fb-3e3af8607796
    #
    # Lynn -> Tulimero
    "b19fe654-9cea-4678-bec2-c994a0354235": "28376b5f-aae6-48a7-951e-9029045d06fe",
    # Koller -> Dulude
    "f0e704e8-6ebc-4480-90e4-5a371e29d152": "5a06366c-5812-4e66-9e3d-da6677c58d02",
    # Paul -> Nieves
    "35202dac-5abf-457f-b1b7-2337f7ab84b9": "83e8c9bb-577a-47e1-9451-137b156e366a",
    # Hunter -> Bach
    "bfe24cf4-dcfe-4572-8145-976a7958697c": "7f2c899e-879c-437b-b9e6-f58563315405",
    # Leonhardt -> Bach
    "cac3030d-b868-49c1-bad8-2c899f7d8e98": "7f2c899e-879c-437b-b9e6-f58563315405",
    # Gallo -> Tulimero
    "2bd335ee-5096-43e9-b1d1-50a5343ff786": "28376b5f-aae6-48a7-951e-9029045d06fe",
    # Bartz -> Nieves
    "60252514-7ae4-4ce2-8b4f-64e2cafbeb89": "83e8c9bb-577a-47e1-9451-137b156e366a",
    # Allen -> Leal
    "2f3df043-2b47-4017-b117-a00f79bb0bda": "14fab712-731e-4985-a780-ec4907a12bac",
    # Abueg -> Tubis
    "74b47853-79f3-4fea-ae90-6821c9687b58": "54d0cfac-3e81-46f6-96e1-dfed810565c8",
    # Pizzurro -> Tubis
    "b8aadd79-c67e-41a0-b751-11e9b2d23010": "54d0cfac-3e81-46f6-96e1-dfed810565c8",
    # Davis -> Price
    "d9189d41-b847-4965-89fa-206faa994b5f": "fdd93e3a-6838-4ead-91c7-a90e914190c0",
    # Helitzer -> Alers
    "0deab81b-d6ab-47d4-92d6-5a30769e165d": "808fe259-e848-4242-8934-b2bca3cfc704",
    # Scarberry -> Valenzuela
    "40196577-d3d8-4b98-b29f-a864bc049edf": "3ede3026-09df-4960-af60-2c40fac48e62",
    # Ortiz -> Acosta
    "5b6fadf1-0546-4de1-b9a0-fe080fb17831": "e2a9f30e-447f-44e0-8651-8b6411820ac3",
    # Rodgers -> Leal
    "480919c9-8603-4621-a1bd-b465ae016ad5": "14fab712-731e-4985-a780-ec4907a12bac",
    # Etzler -> Valenzuela
    "9605fed8-9c02-4aac-b9e0-805086300a9c": "3ede3026-09df-4960-af60-2c40fac48e62",
    # Aguon -> Tackett
    "ae2a2755-bec6-4cad-a4bc-6f8da2b54604": "ab22162b-a1e1-432d-826b-89dbab5c94a5",
    # Zimmerman -> Tulimero
    "ec42ead2-840d-42ad-b0ee-4e81abfb8473": "28376b5f-aae6-48a7-951e-9029045d06fe",
    # Sullivan -> Ostrander
    "6b2c6a40-907c-4d22-b74f-bb66e12f9016": "aa734355-c95a-462f-98fa-f951bef97ffe",
    # Lon Smith -> Beverly Smith
    "67e21eea-d28c-4b89-9808-c6f044216a21": "312766b1-b79d-4d9f-96fb-3e3af8607796",
}


def extract_ims_quote_guids_from_copilot_submission(submission_client_ids: List[SubmissionClientId]) -> Dict[str, str]:
    # Just for the reference, "client_submission_id" for Paragon IMS is JSON string. Real example:
    # {
    #   "ims_insured_guid": "f2b9dbc6-6846-4665-b82e-aee0584a2899",
    #   "ims_submission_id": "b7382657-**************-f9436e22d0ee",
    #   "ims_quote_guids": {
    #     "Casualty E&S  General Liability Primary": "cb2bc6ac-91f3-43a8-a3ca-671c52bde83b",
    #     "Casualty E&S Excess (Supported)": "e548aca5-**************-3b153951f850"}
    #    },
    #   "ims_control_numbers": {
    #     "Casualty E&S  General Liability Primary": 123,
    #     "Casualty E&S Excess (Supported)": 124}
    #    }
    #    It's that complicated because in IMS submission is just a shell for many Quotes.
    #    Each Quote is for separated LOB/Coverage and we can update their status separately.
    #    This works differently in Copilot where usually single Submission hosts multiple LOBs/Coverages and
    #    has 1 shared status.
    # Alternatively it can be just a single integer with IMS control number. In this case this should return empty dict.
    ims_quote_guids_map = {}

    for submission_client_id_wrapper in submission_client_ids:
        client_submission_id_json = submission_client_id_wrapper.client_submission_id

        if client_submission_id_json:
            try:
                data = json.loads(client_submission_id_json)
                if isinstance(data, int):
                    logger.info("Submission has raw IMS control number in client id, skipping quote guid extraction")
                    continue

                # Extract ims_quote_guids if it exists and is a dictionary (old obsolete format used list of GUIDS
                # but new format uses Dict of Coverage Type/LOB to IMS quote GUID). We can skip old submissions.
                ims_quote_guids = data.get("ims_quote_guids", {})
                if isinstance(ims_quote_guids, dict):
                    ims_quote_guids_map.update(ims_quote_guids)

            except json.JSONDecodeError:
                # Handle JSON decode error by skipping to the next item, this should not happen but if it does we can
                # see the logs and fix the issue.
                logger.warn(
                    f"Failed to decode client_submission_id_json for Paragon submission: {client_submission_id_json}",
                    exc_info=True,
                )
                continue

    return ims_quote_guids_map


def _is_integer_string(s: str) -> bool:
    try:
        int(s)
        return True
    except ValueError:
        return False


def extract_ims_control_numbers_from_copilot_submission(
    submission_client_ids: List[SubmissionClientId],
) -> Dict[str, str]:
    # Just for the reference, "client_submission_id" for Paragon IMS is JSON string. Real example:
    # {
    #   "ims_insured_guid": "f2b9dbc6-6846-4665-b82e-aee0584a2899",
    #   "ims_submission_id": "b7382657-**************-f9436e22d0ee",
    #   "ims_quote_guids": {
    #     "Casualty E&S  General Liability Primary": "cb2bc6ac-91f3-43a8-a3ca-671c52bde83b",
    #     "Casualty E&S Excess (Supported)": "e548aca5-**************-3b153951f850"}
    #    },
    #   "ims_control_numbers": {
    #     "Casualty E&S  General Liability Primary": 123,
    #     "Casualty E&S Excess (Supported)": 124}
    #    }
    #    It's that complicated because in IMS submission is just a shell for many Quotes.
    #    Each Quote is for separated LOB/Coverage and we can update their status separately.
    #    This works differently in Copilot where usually single Submission hosts multiple LOBs/Coverages and
    #    has 1 shared status.
    # Alternatively it can be just a single integer with IMS control number. In this case this should return dict
    # with some dummy coverage.
    ims_control_numbers_map = {}

    for submission_client_id_wrapper in submission_client_ids:
        try:
            sub_id = submission_client_id_wrapper.client_submission_id
            if _is_integer_string(sub_id):
                # This is just a single integer with IMS control number, we will return it with some dummy coverage.
                logger.info("Submission has raw IMS control number in client id, adding it to the map")
                ims_control_numbers_map[f"RAW_CONTROL_NUMBER_{sub_id}"] = int(sub_id)
                continue

            client_sub_id: ParagonClientSubmissionId = sub_client_id_schema.loads(sub_id)

            if client_sub_id:
                for ims_control_number in client_sub_id.ims_control_numbers:
                    ims_control_numbers_map[ims_control_number.ims_coverage] = ims_control_number.control_number
        except json.JSONDecodeError:
            # This should not happen but if it does we can see the logs and fix the issue.
            logger.warn(
                f"Failed to decode client_submission_id for Paragon submission: {submission_client_id_wrapper}",
                exc_info=True,
            )
            return {}

    return ims_control_numbers_map


def create_ims_quote_status_from_id(status_id: int) -> Optional[QuoteStatus]:
    try:
        return QuoteStatus(status_id)
    except ValueError:
        logger.warn("Unsupported IMS quote status ID", status_id=status_id)
        return None


def send_slack_message_to_paragon_debug_channel(slack_client: SlackClient, debug_message: str) -> None:
    paragon_slack_debug_channel = "paragon-debug"
    separated_debug_message = 40 * "=" + "\n" + debug_message
    slack_client.send_slack_message(paragon_slack_debug_channel, separated_debug_message)


class ParagonCircuitBreaker:
    def __init__(
        self,
        log: BoundLogger,
        db_session: Session,
        feature_flags_client: FeatureFlagsClient,
        callback_on_cb_opened: Callable[[ImsCircuitBreakerEvent], None] = None,
        callback_on_cb_closed: Callable[[ImsCircuitBreakerEvent], None] = None,
    ):
        self._log = log
        self._db_session = db_session
        self._ff_client = feature_flags_client
        self._manual_internal_cb_status = CircuitBreakerStatus.CLOSED
        self._manual_public_cb_status = CircuitBreakerStatus.CLOSED
        self._auto_public_cb_status = CircuitBreakerStatus.CLOSED
        self._callback_on_cb_opened = callback_on_cb_opened
        self._callback_on_cb_closed = callback_on_cb_closed

    def refresh(self) -> None:
        # Get current Feature Flags state for manually enabled Circuit Breakers
        ff_cb_manual_internal_state = self._ff_enabled(FeatureFlags.PARAGON_IMS_CIRCUIT_BREAKER_MANUAL_INTERNAL)
        ff_cb_manual_public_state = self._ff_enabled(FeatureFlags.PARAGON_IMS_CIRCUIT_BREAKER_MANUAL_PUBLIC)

        # Get our current Circuit Breakers state from the database
        newest_db_manual_internal_breaker = self._newest_db_cb(ImsCircuitBreakerEvent.CIRCUIT_BREAKER_MANUAL_INTERNAL)
        newest_db_manual_public_breaker = self._newest_db_cb(ImsCircuitBreakerEvent.CIRCUIT_BREAKER_MANUAL_PUBLIC)
        newest_db_auto_public_breaker = self._newest_db_cb(ImsCircuitBreakerEvent.CIRCUIT_BREAKER_AUTO_PUBLIC)

        # If auto became opened it must stay open no matter if following errors count is below threshold
        # We need to manually close this breaker either by adding DB row manually or executing SPT lambda for this.
        has_been_closed_manually_in_db = False
        if newest_db_auto_public_breaker.status == CircuitBreakerStatus.CLOSED:
            # Check number of recent failures
            failed_ims_creations_count = self._count_failed_ims_creations_in_the_last_minutes(
                AUTO_CIRCUIT_BREAKER_ERRORS_CHECK_MINUTES
            )
            # False = Closed, True = Open
            auto_cb_desired_state = failed_ims_creations_count >= AUTO_CIRCUIT_BREAKER_ERRORS_THRESHOLD
            # Auto CB has been closed manually (it has no reason present)
            if not auto_cb_desired_state and not newest_db_auto_public_breaker.reason:
                has_been_closed_manually_in_db = True
        else:
            auto_cb_desired_state = True

        self._log.info(
            "Circuit Breaker statuses",
            ff_cb_manual_internal_state=ff_cb_manual_internal_state,
            ff_cb_manual_public_state=ff_cb_manual_public_state,
            auto_cb_desired_state=auto_cb_desired_state,
            has_been_closed_manually_in_db=has_been_closed_manually_in_db,
            newest_db_manual_internal_breaker=newest_db_manual_internal_breaker.status,
            newest_db_manual_public_breaker=newest_db_manual_public_breaker.status,
            newest_db_auto_public_breaker=newest_db_auto_public_breaker.status,
        )

        # Update the statuses where necessary, insert new DB rows only if there is diff with FF
        self._manual_internal_cb_status = self._update_circuit_breaker_status(
            newest_db_manual_internal_breaker, ff_cb_manual_internal_state
        )
        self._manual_public_cb_status = self._update_circuit_breaker_status(
            newest_db_manual_public_breaker, ff_cb_manual_public_state
        )
        self._auto_public_cb_status = self._update_circuit_breaker_status(
            newest_db_auto_public_breaker,
            auto_cb_desired_state,
            has_been_closed_manually_in_db=has_been_closed_manually_in_db,
        )

    def is_open(self) -> bool:
        self._log.info(
            "Evaluating DB Circuit Breaker statuses",
            newest_db_manual_internal_breaker=self._manual_internal_cb_status,
            newest_db_manual_public_breaker=self._manual_public_cb_status,
            newest_db_auto_public_breaker=self._auto_public_cb_status,
        )

        return (
            self._manual_internal_cb_status == CircuitBreakerStatus.OPEN
            or self._manual_public_cb_status == CircuitBreakerStatus.OPEN
            or self._auto_public_cb_status == CircuitBreakerStatus.OPEN
        )

    def _ff_enabled(self, feature_name: str) -> bool:
        return self._ff_client.is_feature_enabled(feature_name)

    def _newest_db_cb(self, circuit_breaker_name: str) -> ParagonImsCircuitBreakers:
        newest_db_cb = (
            self._db_session.query(ParagonImsCircuitBreakers)
            .filter(ParagonImsCircuitBreakers.event == circuit_breaker_name)
            .order_by(ParagonImsCircuitBreakers.occurred_at.desc())
            .first()
        )

        # If DB has no record for this Circuit Breaker yet, we need to seed it with a new one as CLOSED
        if not newest_db_cb:
            newest_db_cb = ParagonImsCircuitBreakers(event=circuit_breaker_name, status=CircuitBreakerStatus.CLOSED)
            self._db_session.add(newest_db_cb)
            self._db_session.commit()

        return newest_db_cb

    def _update_circuit_breaker_status(
        self, cb_record: ParagonImsCircuitBreakers, cb_desired_state: bool, has_been_closed_manually_in_db: bool = False
    ) -> None:
        new_cb_record = None
        if cb_desired_state and cb_record.status == CircuitBreakerStatus.CLOSED:
            new_cb_record = ParagonImsCircuitBreakers(event=cb_record.event, status=CircuitBreakerStatus.OPEN)
            self._log.info(f"{cb_record.event.value} circuit breaker set to OPEN.")
            if self._callback_on_cb_opened:
                self._callback_on_cb_opened(cb_record.event)
        elif has_been_closed_manually_in_db or (not cb_desired_state and cb_record.status == CircuitBreakerStatus.OPEN):
            new_cb_record = ParagonImsCircuitBreakers(event=cb_record.event, status=CircuitBreakerStatus.CLOSED)
            if has_been_closed_manually_in_db:
                new_cb_record.reason = "Circuit breaker has been closed manually in db."
            self._log.info(f"{cb_record.event.value} circuit breaker set to CLOSED.")
            if self._callback_on_cb_closed:
                self._callback_on_cb_closed(cb_record.event)
        if new_cb_record:
            self._db_session.add(new_cb_record)
            self._db_session.commit()
            return new_cb_record.status
        return cb_record.status

    @staticmethod
    def create_slack_message_for_opening_and_closing_events(event: ImsCircuitBreakerEvent, is_opened: bool) -> None:
        base_msg = (
            "<!here> `{type}` IMS Circuit Breaker has been *{status}*{details} :point_left: IMS cards *{will_status}*"
            " be created"
        )
        details = ""
        if is_opened:
            if event == ImsCircuitBreakerEvent.CIRCUIT_BREAKER_AUTO_PUBLIC:
                details = " - too many errors and emails sent to Paragon in the last hour"
            will_status = "WILL NOT"
            additional_info = " and will be queued. " + (
                "Contact engineering to resume processing!\n"
                if event == ImsCircuitBreakerEvent.CIRCUIT_BREAKER_AUTO_PUBLIC
                else (
                    "Feature flag named 'Paragon IMS Circuit Breaker {type}' must be turned off to resume processing!\n"
                )
            )
        else:
            will_status = "WILL"
            additional_info = "\n"

        type_str = {
            ImsCircuitBreakerEvent.CIRCUIT_BREAKER_MANUAL_INTERNAL: "MANUAL INTERNAL",
            ImsCircuitBreakerEvent.CIRCUIT_BREAKER_MANUAL_PUBLIC: "MANUAL PUBLIC",
            ImsCircuitBreakerEvent.CIRCUIT_BREAKER_AUTO_PUBLIC: "AUTOMATIC",
        }.get(event, "UNKNOWN")

        status = "ACTIVATED" if is_opened else "DE-ACTIVATED"
        return f"{base_msg}{additional_info}".format(
            type=type_str, status=status, details=details, will_status=will_status
        )

    def _count_failed_ims_creations_in_the_last_minutes(self, minutes: int) -> int:
        timerange = datetime.utcnow() - timedelta(minutes=minutes)

        count = (
            self._db_session.query(ParagonImsCircuitBreakers)
            .filter(ParagonImsCircuitBreakers.event == ImsCircuitBreakerEvent.FAILED_IMS_CREATION)
            .filter(ParagonImsCircuitBreakers.occurred_at >= timerange)
            .count()
        )

        return count
