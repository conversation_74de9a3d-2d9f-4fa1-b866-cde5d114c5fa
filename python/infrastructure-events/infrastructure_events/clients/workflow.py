import json
from datetime import datetime
from functools import partial
from typing import Callable, Dict, List, Optional, Sequence
from uuid import UUID, uuid4

import boto3
from events_common.kalepa_events import KalepaEvents
from infrastructure_common.logging import get_logger
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.premises import PremisesIngestionType
from static_common.models.business_resolution_request import BusinessResolutionRequest
from static_common.models.insights import FirstPartyInsightsBulkRequest
from static_common.models.on_demand import OnDemandRequest
from static_common.models.types import StrUUID
from static_common.schemas.business_resolution_request import (
    BusinessResolutionRequestSchema,
)
from static_common.schemas.on_demand import OnDemandIngestionRequestSchema

from infrastructure_events.sf_constants import SUBMISSION_TAXONOMY_SYNC_SF_NAME
from infrastructure_events.utils import (
    build_step_function_arn,
    detect_current_aws_account_id,
    detect_current_aws_region,
)

_step_functions_client = boto3.client("stepfunctions", region_name=detect_current_aws_region())
MAX_EXECUTIONS = 10**9

logger = get_logger()


class WorkflowClient:
    def __init__(self):
        step_function_arn = partial(
            build_step_function_arn, detect_current_aws_region(), detect_current_aws_account_id()
        )
        # NOTE: These should be ARNs to bulk versions of step functions that limit the concurrency.
        self.resolve_businesses_arn = step_function_arn("copilot-workers-resolve-businesses")
        self.facts_merge_for_submission_arn = step_function_arn("copilot-workers-merge-business-facts-v2")
        self.relationships_creation_for_submission_arn = step_function_arn(
            "copilot-workers-create-relationships-for-submission"
        )
        self.create_location_for_fni_entity_arn = step_function_arn("copilot-workers-create-location-for-fni-entity")
        self.create_shell_entities_for_businesses_resolved_to_real_estates_arn = step_function_arn(
            "copilot-workers-create-shell-entities-for-businesses-resolved-to-real-estates"
        )
        self.register_first_party_data_arn = step_function_arn("copilot-workers-register-first-party-data")
        self.recommendations_v2_state_machine_arn = step_function_arn("recommendations-v2-run-engine-v2")
        self.summarization_state_machine_arn = step_function_arn("summarization-summarize-report-blue")
        self.ingestion_state_machine_arn = step_function_arn("ingestion-businesses-ingestion-v2")
        self.related_ingestion_state_machine_arn = step_function_arn("ingestion-related-businesses-ingestion-v2")
        self.insights_state_machine_arn = step_function_arn("insights-bulk-infer-insights")
        self.businesses_merge_state_machine_arn = step_function_arn("copilot-workers-merge-businesses")
        self.premises_ingestion_state_machine_arn = step_function_arn("locations-workers-ingest-premises-v3")
        self.patch_submission_businesses = step_function_arn("copilot-workers-patch-composed-nodes-submissions")
        self.resolve_submission_files_entities_arn = step_function_arn(
            "copilot-workers-resolve-submission-files-entities"
        )
        self.infer_first_party_insights = step_function_arn("insights-infer-first-party-submission-insights")
        self.validate_sensible_template_arn = step_function_arn("copilot-workers-validate-sensible-template")
        self.step_functions_client = _step_functions_client
        self.classify_file_arn = step_function_arn("copilot-workers-classify-file")
        self.process_file_arn = step_function_arn("copilot-workers-process-file")
        self.submission_news_ingestion_v2_state_machine_arn = step_function_arn("news-worker-ingest-submission-news-v2")
        self.classify_email_state_machine_arn = step_function_arn("copilot-workers-classify-email")
        self.synchronize_submission_taxonomy_state_machine_arn = step_function_arn(SUBMISSION_TAXONOMY_SYNC_SF_NAME)
        self.infer_first_party_insights_state_machine_arn = step_function_arn(
            "insights-infer-first-party-submission-insights-v2"
        )

        self._invocation_function_by_arn = {
            self.resolve_businesses_arn: self.start_resolve_businesses,
            self.facts_merge_for_submission_arn: self.invoke_facts_merge_for_submission,
            self.relationships_creation_for_submission_arn: self.invoke_relationships_creation_for_submission,
            self.create_location_for_fni_entity_arn: self.invoke_create_location_for_fni_entity,
            self.register_first_party_data_arn: self.invoke_first_party_registration,
            self.summarization_state_machine_arn: self.invoke_summarization,
            self.ingestion_state_machine_arn: self.invoke_bulk_ingestion,
            self.related_ingestion_state_machine_arn: self.invoke_related_bulk_ingestion,
            self.insights_state_machine_arn: self.invoke_insights_for_business_ids,
            self.businesses_merge_state_machine_arn: self.invoke_businesses_merge,
            self.premises_ingestion_state_machine_arn: self.invoke_premises_ingestion,
            self.recommendations_v2_state_machine_arn: self.invoke_recommendation,
            self.submission_news_ingestion_v2_state_machine_arn: self.invoke_submission_news_ingestion_v2,
            self.synchronize_submission_taxonomy_state_machine_arn: self.invoke_synchronize_submission_taxonomy,
            self.infer_first_party_insights_state_machine_arn: self.invoke_first_party_insights_v2,
        }

    def _start_execution(self, state_machine_arn: str, name: str, input: str):
        return self.step_functions_client.start_execution(stateMachineArn=state_machine_arn, name=name, input=input)

    @staticmethod
    def _get_now_str():
        return str(datetime.utcnow()).replace("-", "").replace(":", "").replace(".", "").replace(" ", "")

    def get_invocation_method(self, arn: str) -> Optional[Callable]:
        return self._invocation_function_by_arn.get(arn)

    def invoke_bulk_ingestion(self, requests: Sequence[OnDemandRequest]) -> str:
        requests_json = [OnDemandIngestionRequestSchema().dump(r) for r in requests]
        for r in requests_json:
            r["business_id"] = r.pop("kalepa_id")

        request = {"businesses": requests_json}

        result = self._start_execution(
            self.ingestion_state_machine_arn, f"{uuid4()}-{self._get_now_str()}", json.dumps(request)
        )

        return result.get("executionArn") if result else None

    def invoke_related_bulk_ingestion(self, requests: Sequence[OnDemandRequest]) -> str:
        requests_json = [OnDemandIngestionRequestSchema().dump(r) for r in requests]
        for r in requests_json:
            r["business_id"] = r.pop("kalepa_id")

        request = {"businesses": requests_json}

        result = self._start_execution(
            self.related_ingestion_state_machine_arn, f"{uuid4()}-{self._get_now_str()}", json.dumps(request)
        )

        return result.get("executionArn") if result else None

    def invoke_insights_for_business_ids(self, business_ids: Sequence[UUID]) -> str:
        request = {"jobs": [{"business_id": str(b_id)} for b_id in business_ids]}
        result = self._start_execution(
            self.insights_state_machine_arn, f"{uuid4()}-{self._get_now_str()}", json.dumps(request)
        )
        return result.get("executionArn") if result else None

    def start_resolve_businesses(self, submission_id: UUID):
        request = BusinessResolutionRequest(submission_id=submission_id)
        self._start_execution(
            self.resolve_businesses_arn,
            f"{submission_id}-{self._get_now_str()}",
            BusinessResolutionRequestSchema().dumps(request),
        )

    def invoke_facts_merge_for_submission(self, submission_id: UUID, force: bool = False) -> str:
        request = {"submission_id": str(submission_id), "force": force}
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.facts_merge_for_submission_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_relationships_creation_for_submission(self, submission_id: UUID) -> str:
        request = {"submission_id": str(submission_id)}
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.relationships_creation_for_submission_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_create_shell_entities_for_businesses_resolved_to_real_estates(
        self, submission_id: UUID, additional_data: Dict
    ) -> str:
        request = {"submission_id": str(submission_id), "additional_data": additional_data}
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(
            self.create_shell_entities_for_businesses_resolved_to_real_estates_arn, name, json.dumps(request)
        )
        return result.get("executionArn") if result else None

    def invoke_create_location_for_fni_entity(self, submission_id: UUID, additional_data: Dict) -> str:
        request = {"submission_id": str(submission_id), "additional_data": additional_data}
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.create_location_for_fni_entity_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_first_party_registration(
        self, submission_id: UUID, s3_key: Optional[str] = None, file_id: Optional[UUID] = None
    ) -> str:
        request = {
            "submission_id": str(submission_id),
        }

        if s3_key:
            request["s3_key"] = s3_key
        if file_id:
            request["file_id"] = str(file_id)

        result = self._start_execution(
            self.register_first_party_data_arn,
            f"{submission_id}-{self._get_now_str()}",
            json.dumps(request),
        )
        return result.get("executionArn") if result else None

    def invoke_recommendation(self, submission_id: UUID, rule_id: Optional[UUID] = None) -> str:
        request = {"submission_id": str(submission_id)}
        name = f"{submission_id!s}-{self._get_now_str()}"
        input = json.dumps({**request, "rule_id": str(rule_id) if rule_id else None})
        result = self._start_execution(self.recommendations_v2_state_machine_arn, name, input)
        return result.get("executionArn") if result else None

    def invoke_patch_submission_businesses(self, business_id: UUID) -> str:
        request = {"business_id": str(business_id)}
        name = f"{business_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.patch_submission_businesses, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_summarization(self, submission_id: UUID, force: bool = False) -> str:
        summarization_request = {"submission_ids": [str(submission_id)], "force": force}
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.summarization_state_machine_arn, name, json.dumps(summarization_request))
        return result.get("executionArn") if result else None

    def invoke_first_party_insights(self, submission_id: UUID, organization_id: int) -> Optional[str]:
        request = {"submission_id": str(submission_id), "organization_id": organization_id}
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.infer_first_party_insights, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_businesses_merge(self, business_id: UUID, old_ids: List[UUID], merge_facts: bool) -> str:
        request = {
            "business_id": str(business_id),
            "old_ids": [str(old_id) for old_id in old_ids],
            "merge_facts": merge_facts,
        }
        name = f"{business_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.businesses_merge_state_machine_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_premises_ingestion(
        self,
        submission_id: UUID,
        composite_id: UUID,
        ingestion_mode: Optional[str] = None,
        premises_id: Optional[str] = None,
        prometrix_risk_id: Optional[str] = None,
        premises_ingestion_type: str = PremisesIngestionType.HAZARD_HUB.value,
        riskmeter_category: Optional[str] = None,
    ) -> str:
        request = {
            "submission_id": str(submission_id),
            "composite_id": str(composite_id) if composite_id else None,
            "premises_ingestion_type": premises_ingestion_type,
            "premises_id": str(premises_id) if premises_id else None,
            "prometrix_risk_id": prometrix_risk_id,
            "riskmeter_category": riskmeter_category,
        }
        if ingestion_mode:
            request["ingestion_mode"] = ingestion_mode
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.premises_ingestion_state_machine_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_resolve_submission_files_entities(
        self,
        submission_id: UUID,
        file_processing_state: FileProcessingState,
        finished_event_type: KalepaEvents | None,
        use_entity_mapped_data: bool,
        target_file_processing_state: FileProcessingState | None,
        file_finished_event_type: KalepaEvents | None,
    ) -> Optional[str]:
        request = {
            "submission_id": str(submission_id),
            "file_processing_state": str(file_processing_state),
            "finished_event_type": str(finished_event_type) if finished_event_type else None,
            "use_entity_mapped_data": use_entity_mapped_data,
            "target_file_processing_state": str(target_file_processing_state) if target_file_processing_state else None,
            "file_finished_event_type": str(file_finished_event_type) if file_finished_event_type else None,
        }
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.resolve_submission_files_entities_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_classify_email(self, email_id: str, submission_id: UUID, organization_id: int) -> Optional[str]:
        request = {"email_id": str(email_id), "submission_id": str(submission_id), "organization_id": organization_id}
        name = f"{request['email_id']}-{self._get_now_str()}"
        result = self._start_execution(self.classify_email_state_machine_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_validate_sensible_template(
        self, document_type: str, configuration: str, promote_to_prod: bool
    ) -> Optional[str]:
        request = {"document_type": document_type, "configuration": configuration, "promote_to_prod": promote_to_prod}
        name = f"{document_type}-{configuration}-{self._get_now_str()}"
        result = self._start_execution(self.validate_sensible_template_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_submission_news_ingestion_v2(self, submission_id: StrUUID, use_file_businesses: bool) -> Optional[str]:
        request = {
            "submission_id": str(submission_id),
            "use_file_businesses": use_file_businesses,
        }
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(self.submission_news_ingestion_v2_state_machine_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def get_number_of_running_executions(self, arn: str) -> int:
        logger.info("Getting number of running executions", arn=arn)
        result = 0
        next_token = None

        try:
            while True:
                # for whatever reason passing None to nextToken ends up with a validation error,
                # so it needs to be this way
                if not next_token:
                    response = self.step_functions_client.list_executions(
                        stateMachineArn=arn,
                        statusFilter="RUNNING",
                    )
                else:
                    response = self.step_functions_client.list_executions(
                        stateMachineArn=arn, statusFilter="RUNNING", nextToken=next_token
                    )

                result += len(response["executions"])
                next_token = response.get("nextToken")

                if not next_token:
                    return result
        except Exception as e:
            if "ThrottlingException" in str(e):
                logger.warning("ThrottlingException while getting running executions", arn=arn)
                # If AWS is throttling us, it means that the system is under quite heavy load. We can return
                # a big number to avoid starting new executions for a while.
                return MAX_EXECUTIONS
            logger.exception("Failed to get running executions", arn=arn)
            return result

    def invoke_classify_file(self, request: Dict) -> Optional[str]:
        name = f"{request['file_id']}-{self._get_now_str()}"
        result = self._start_execution(self.classify_file_arn, name, json.dumps(request))
        return result.get("executionArn") if result else None

    def invoke_synchronize_submission_taxonomy(
        self,
        submission_id: str | UUID,
        taxonomy_change_type: str,
        taxonomy_change_value: str,
        organization_id: int,
    ) -> Optional[str]:
        request = {
            "submission_id": str(submission_id),
            "taxonomy_change_type": taxonomy_change_type,
            "taxonomy_change_value": taxonomy_change_value,
            "organization_id": organization_id,
        }
        name = f"{submission_id!s}-{taxonomy_change_type}-{self._get_now_str()}"
        result = self._start_execution(
            self.synchronize_submission_taxonomy_state_machine_arn, name, json.dumps(request)
        )
        return result.get("executionArn") if result else None

    def invoke_first_party_insights_v2(self, submission_id: UUID, organization_id: int) -> Optional[str]:
        request = FirstPartyInsightsBulkRequest(submission_id=submission_id, organization_id=organization_id)
        name = f"{submission_id!s}-{self._get_now_str()}"
        result = self._start_execution(
            self.infer_first_party_insights_state_machine_arn,
            name,
            FirstPartyInsightsBulkRequest.model_dump(request, mode="json"),
        )
        return result.get("executionArn") if result else None
