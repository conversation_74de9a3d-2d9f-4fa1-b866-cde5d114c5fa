import base64
import json
import os
import traceback
from dataclasses import dataclass, field
from datetime import date, datetime, timedelta, timezone
from functools import cache
from typing import Dict, List, Optional, Set, Tuple, Union
from uuid import UUID

import requests
from common.clients.facts import FactsClient
from common.clients.feature_flags import Feature<PERSON>lagsClient
from common.clients.paragon_ims import IMSSearchClient
from common.clients.slack import <PERSON>lack<PERSON>lient
from common.clients.zippopotam_client import ZippopotamClient
from common.logic.addresses import try_parse_address_string
from common.logic.entity_resolution.entity import (
    get_physical_premises,
    get_primary_dba_name,
    get_primary_legal_name,
    get_primary_premises,
)
from common.logic.paragon import IMSSearchInput, ParagonEmails
from common.submissions.feins import get_fein
from copilot_client_v3 import (
    ApiException,
    AssignedUnderwriter,
    ParagonCompanyLine,
    ParagonNotification,
    ParagonNotificationResponse,
    ParagonNotificationTemplateData,
    ParagonUnderwriter,
    PartiallyBlockedOnClearingEsData,
    Report,
    Submission,
    SubmissionBusiness,
)
from entity_resolution_service_client_v3 import Entity
from events_common.model.types import RequestStatus
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from llm_common.clients import get_llm_client
from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import GPTRequestParams
from paragon_ims_api_client.models.insured import Insured
from paragon_ims_api_client.models.location import Location
from paragon_ims_api_client.models.paragon_enums import (
    BillingRecipient,
    BusinessTypes,
    DeliveryMethods,
    PolicyTypes,
)
from paragon_ims_api_client.models.producer import Producer
from paragon_ims_api_client.models.producer_contact import ProducerContact
from paragon_ims_api_client.models.quote_status import (
    QuoteDeclinedStatusReason,
    QuoteStatus,
)
from paragon_ims_api_client.paragon_ims_client import ParagonIMSAPIClient
from paragon_ims_api_client.paragon_ims_client_exceptions import (
    InconsistentIMSInsuredsForFeinException,
    NoSuchParagonProducerContactException,
)
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.coverage_names import CoverageName
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.submission import SubmissionStage
from static_common.enums.submission_client_id import SubmissionClientIdSource
from static_common.models.address import Address
from static_common.models.openai import ChatCompletionPrompt
from static_common.models.paragon import ImsCoverage
from static_common.models.submission_action.paragon import (
    ParagonClearanceCheckStatus,
    ParagonClearanceCheckStatusInput,
    ParagonCoverageClearanceStatus,
    ParagonSubmissionInfo,
)
from static_common.schemas.openai import ChatCompletionPromptSchema
from static_common.schemas.submission_action.paragon import (
    ParagonClearanceCheckStatusSchema,
)
from structlog.stdlib import BoundLogger

from infrastructure_events.clients.copilot import CopilotV3Client
from infrastructure_events.clients.ers_v3 import ERSClientV3
from infrastructure_events.clients.paragon_ims import (
    dummy_call_to_check_if_ims_api_is_up,
    get_paragon_ims_client,
)
from infrastructure_events.clients.sendgrid import SendGridClient
from infrastructure_events.config.feature_flags import FeatureFlags
from infrastructure_events.config.queue_handler_config import QueueHandlerConfig
from infrastructure_events.handlers.queue_handlers.get_first_execution_mixin import (
    GetFirstExecutionMixin,
)
from infrastructure_events.handlers.queue_handlers.single_id_api_queue_handler import (
    ActionResult,
    ActionStatus,
    SingleIdApiBasedQueueHandler,
)
from infrastructure_events.logic.common import (
    DeduplicatedRequestsQueueResult,
    get_deduplicated_requests,
)
from infrastructure_events.logic.paragon import (
    ES_FORWARD_TEMPORARY_SKIP_MESSAGE,
    BrokerSearchResults,
    InsuredSearchResult,
    ParagonBusinessRules,
    ParagonCircuitBreaker,
    ParagonImsCreationIssues,
    ParagonLineOfBusiness,
    ParagonNotificationType,
)
from infrastructure_events.model.paragon_ims_circuit_breakers import (
    ImsCircuitBreakerEvent,
    ParagonImsCircuitBreakers,
)
from infrastructure_events.model.paragon_ims_submission_creation_request_queue import (
    ParagonImsSubmissionCreationRequestQueue,
    ParagonRenewalDetectorResult,
)
from infrastructure_events.utils import get_report_url

HOURS_TO_FAIL_THRESHOLD = int(os.environ.get("HOURS_TO_FAIL_THRESHOLD", "0"))
MAX_ATTEMPTS = int(os.environ.get("MAX_ATTEMPTS", "1"))
SLACK_DEBUG_CHANNEL = "paragon-debug"
SLACK_COMPLETENESS_CHANNEL = "paragon-completeness"
PATRYK_LENZA_SLACK_ID = "@U02RDFPJW5D"
PFUCHS_SLACK_ID = "@U063N4JD2RF"

SHORT_FIRST_RETRY_CUT_OFF_IN_MINUTES = 60
SHORT_SECOND_RETRY_CUT_OFF_MINUTES = 24 * 60
LONG_FIRST_RETRY_CUT_OFF_IN_MINUTES = 24 * 60
LONG_SECOND_RETRY_CUT_OFF_MINUTES = 3 * 24 * 60

feature_flags_client = FeatureFlagsClient(api_key=os.getenv("LD_API_KEY"), default_context="KALEPA_EVENTS")

queue_config = QueueHandlerConfig(
    max_requests=5,
    max_concurrency=None,
    hours_to_fail=HOURS_TO_FAIL_THRESHOLD,
    max_attempts=MAX_ATTEMPTS,
    autocomplete_request=False,
    check_concurrency=False,
    spread_requests=False,
)

env = os.environ.get("KALEPA_ENV", "dev")


@dataclass
class NewSubmissionClearanceInfo:
    initial_quote_status: QuoteStatus
    initial_declined_reason: QuoteDeclinedStatusReason

    # Was the submission blocked by a declined policy that was not declined because of being blocked at market
    was_blocked_by_declined_policy: bool


@dataclass
class ParagonClearanceInfo:
    existing_unsupported_to_notify_about_primary_available: ParagonSubmissionInfo | None = None
    company_line_to_initial_status_and_reason: dict[str, NewSubmissionClearanceInfo] = field(default_factory=dict)

    def initial_quote_status(self, line_name: str, fallback: QuoteStatus) -> QuoteStatus:
        if line_name in self.company_line_to_initial_status_and_reason:
            return self.company_line_to_initial_status_and_reason[line_name].initial_quote_status

        return fallback

    def initial_quote_declined_reason(self, line_name: str) -> QuoteDeclinedStatusReason | None:
        if line_name in self.company_line_to_initial_status_and_reason:
            sub_clearance_info = self.company_line_to_initial_status_and_reason[line_name]
            if sub_clearance_info.initial_quote_status == QuoteStatus.DECLINED:
                return sub_clearance_info.initial_declined_reason

        return None

    def was_blocked_by_declined_policy(self, line_name: str) -> bool:
        if line_name in self.company_line_to_initial_status_and_reason:
            sub_clearance_info = self.company_line_to_initial_status_and_reason[line_name]
            if sub_clearance_info.initial_quote_status == QuoteStatus.DECLINED:
                return sub_clearance_info.was_blocked_by_declined_policy

        return False


class FailedToCreateInImsException(Exception):
    def __init__(self, reason: str, paragon_lob: ParagonLineOfBusiness):
        self.reason = reason
        self.paragon_lob = paragon_lob

    def __str__(self):
        return f"Issue with Copilot creating ({self.paragon_lob}) submission in IMS: {self.reason}"

    def __repr__(self):
        return f"{self.__class__.__name__}({self.paragon_lob!r})({self.reason!r})"


class ParagonImsSubmissionCreationRequestQueueHandler(GetFirstExecutionMixin, SingleIdApiBasedQueueHandler):
    def __init__(self, paragon_ims_client: ParagonIMSAPIClient):
        super().__init__(
            ParagonImsSubmissionCreationRequestQueue,
            lambda request: self.process_ims_submission_creation(request),
            queue_config,
        )
        self.log = get_logger()
        self._slack_client = SlackClient(os.getenv("SLACK_TOKEN"))
        self._paragon_ims_client: ParagonIMSAPIClient = paragon_ims_client
        self._copilot_client = CopilotV3Client(os.environ.get("COPILOT_API_V3_URL"))
        self._facts_api_client = FactsClient(os.environ.get("FACTS_API_URL"))
        self._ers_client = ERSClientV3(os.environ.get("ERS_V3_URL"))
        self._zippopotam_client = ZippopotamClient()
        self._sendgrid_client = SendGridClient(os.getenv("SENDGRID_API_KEY"))
        self.submissions_to_split = {}
        self.ims_sub_created = False

    def process_ims_submission_creation(self, request: ParagonImsSubmissionCreationRequestQueue) -> ActionResult:
        submission = None
        self.ims_sub_created = False
        try:
            self._setup_logger(request)

            try:
                cb: ParagonCircuitBreaker = ParagonCircuitBreaker(
                    self.log,
                    self.session,
                    feature_flags_client,
                    callback_on_cb_opened=self._handle_circuit_breaker_opened,
                    callback_on_cb_closed=self._handle_circuit_breaker_closed,
                )
                cb.refresh()
                if cb.is_open():
                    self.log.info(
                        "Circuit breaker is open, skipping processing for this request, but it will be queued"
                    )
                    return ActionResult(ActionStatus.SKIPPED, inc_attempt_count=False)
            except Exception:
                # TODO(plenza): keep for testing, we don't want to break IMS integration and send emails to Paragon
                # if this is broken.
                self.log.warning(
                    "Failed to use circuit breaker, proceeding with processing", issue_ping="plenza", exc_info=True
                )

            self._get_and_update_actual_submission_report_is_linked_to(request)

            if not (submission := self._try_fetch_submission(request.submission_id)):
                self._handle_no_submission_error(request)
                return ActionResult(ActionStatus.CANCELLED, inc_attempt_count=True)

            self._log_submission_found(submission)

            return self._process_paragon_ims_submission_creation(request, submission)
        except FailedToCreateInImsException as failed_to_create_in_ims_exception:
            return self._handle_failed_to_create_in_ims_exception(
                failed_to_create_in_ims_exception, submission, request
            )
        except Exception as error:
            # This except block will be entered on some serious IMS issue. In this case
            # we can retry manually (there is events lambda for that) when IMS is OK.
            # But it also means that we should not split submission in Copilot because it won't work
            # correctly with IMS on retry. Split will happen automatically on rerun.
            try:
                if submission:
                    return self._handle_processing_error(error, request, submission)
                else:
                    self.log.error("IMS processing handler - should not be here, investigate ASAP")
            except ValueError:
                self.log.warning("Submission was not in the list of submissions to split")

            self._send_notification_to_copilot_api(ParagonNotificationType.FAILED_TO_CREATE_IN_IMS, request)
            return ActionResult(ActionStatus.FAILED, inc_attempt_count=True)

    def _get_and_update_actual_submission_report_is_linked_to(
        self, request: ParagonImsSubmissionCreationRequestQueue
    ) -> Optional[str]:
        # This method takes Report ID which was triaged in Copilot and fetches actual Submission linked to it.
        # We need to do this here before starting processing because linked Submission could change
        # due to shadow report feature. The change could happen between domain event sending from Copilot API to
        # this queue handler being executed.
        if not request.report_id:
            self.log.info("Request has no report_id, cannot fetch actual submission")
            return None

        report: Report = self._copilot_client.get_report_by_id(request.report_id, expand="submissions")
        if not report:
            self.log.info("Report was deleted")
            return None

        submission_id_str = report.submissions[0].id

        if str(request.submission_id) == submission_id_str:
            self.log.info("Report is linked to the same actual submission as in the request")
            return None

        self.log.info(
            "Report is linked to different actual submission than in the request == updating",
            new_submission_id=submission_id_str,
        )

        # We need to update our database row to reflect submission we really worked on with IMS
        # And update our Python object so all further operations are on the right submission
        current_updated_at = request.updated_at
        request.submission_id = UUID(submission_id_str)
        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.submission_id: UUID(submission_id_str),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        request.updated_at = current_updated_at
        self.session.commit()

        # Rebind logger with new submission_id
        self.log = self.log.try_unbind("submission_id")
        self.log = self.log.bind(submission_id=submission_id_str)

        return submission_id_str

    def _process_paragon_ims_submission_creation(
        self, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> ActionResult:
        self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Processing Paragon IMS submission creation request")

        paragon_lob: ParagonLineOfBusiness
        paragon_lob = ParagonLineOfBusiness.EXCESS_AND_SURPLUS
        issues_list = []  # This is list of issues (for slack) that prevent us from creating IMS submission and quote
        dbg_slack_msg = ""  # This is much more detailed debug slack message for IMS creation process (or failure)

        report_email_account = self._get_report_source_email_account(submission)

        is_enhanced_shell = False
        if submission.is_enhanced_shell:
            self.log.info("Received a declined enhanced shell submission")
            is_enhanced_shell = True
            dbg_slack_msg += "*[COPILOT] Submission is a declined enhanced shell*\n"

        is_declined_shell = False
        if submission.is_verified_shell:
            is_declined_shell = True
            if not is_enhanced_shell:
                dbg_slack_msg += "*[COPILOT] Submission is a declined verified shell*\n"
                self.log.info("Received a declined verified shell submission")

        if self._should_skip_processing(dbg_slack_msg, request, submission):
            self._send_notification_to_copilot_api(ParagonNotificationType.SKIPPED_IMS_CREATION, request)
            return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True)

        if ParagonBusinessRules.is_workers_comp_coverage(submission, report_email_account):
            paragon_lob = ParagonLineOfBusiness.WORKERS_COMPENSATION
            self.log.info("Received a Workers Comp submission")
            dbg_slack_msg += "*[COPILOT] Submission is Workers Comp*\n"
        elif ParagonBusinessRules.is_psp_e3_property_coverage(report_email_account):
            paragon_lob = ParagonLineOfBusiness.PSP_E3_PROPERTY
            self.log.info("Received a PSP E3 Property submission")
            dbg_slack_msg += "*[COPILOT] Submission is PSP E3*\n"
            if not feature_flags_client.is_feature_enabled(FeatureFlags.PARAGON_IMS_PSP_E3_INTEGRATION_ENABLED):
                self.log.info("PSP E3 Property integration is disabled by feature flag, skipping processing")
                self._handle_psp_e3_property_skip(dbg_slack_msg, request, submission)
                self._send_notification_to_copilot_api(ParagonNotificationType.SKIPPED_IMS_CREATION, request)
                return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True)
        elif ParagonBusinessRules.is_trident_public_risk_coverage(report_email_account):
            paragon_lob = ParagonLineOfBusiness.TRIDENT_PUBLIC_RISK
            self.log.info("Received a Trident Public Risk submission")
            dbg_slack_msg += "*[COPILOT] Submission is Trident Public Risk*\n"
            if not feature_flags_client.is_feature_enabled(FeatureFlags.PARAGON_IMS_TRIDENT_INTEGRATION_ENABLED):
                self.log.info("Trident Public Risk integration is disabled by feature flag, skipping processing")
                self._handle_trident_public_risk_skip(dbg_slack_msg, request, submission)
                self._send_notification_to_copilot_api(ParagonNotificationType.SKIPPED_IMS_CREATION, request)
                return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True)

        if (
            report_email_account
            and report_email_account.lower() == ParagonEmails.paragon_excess_and_surplus_autoforward_email().lower()
            and feature_flags_client.is_feature_enabled(FeatureFlags.PARAGON_IMS_ES_FORWARD_INTEGRATION_KILL_SWITCH)
        ):
            self.log.info("Received E&S submission from autoforward email, skipping processing by FF (kill switch)")
            self._handle_paragon_es_forward_temporary_skip(dbg_slack_msg, request, submission)
            return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True)

        if paragon_lob.is_psp_e3_property() and (
            action_result := self._detect_and_handle_psp_e3_property_skip(submission, request, dbg_slack_msg)
        ):
            self.log.info("Skipped IMS creation of PSP E3 submission")
            return action_result

        if action_result := self._detect_and_handle_submission_to_be_deleted(
            submission, paragon_lob, request, dbg_slack_msg
        ):
            self.log.info("Skipped IMS creation of submission to be deleted")
            return action_result

        # Try to get Paragon Insured (aka business) details
        dbg_slack_msg, insured_search_result = self._try_get_insureds(
            dbg_slack_msg, submission, is_declined_shell, paragon_lob
        )
        self._update_possible_insured_issues(issues_list, insured_search_result)

        # Try to get Paragon Producer (aka broker) details
        dbg_slack_msg, broker_search_result = self._try_get_broker_details(dbg_slack_msg, submission)
        if ParagonBusinessRules.is_ally_auto(report_email_account):
            paragon_lob = ParagonLineOfBusiness.ALLY_AUTO
            self._handle_ally_auto_skip(dbg_slack_msg, request, submission)
            self._send_notification_to_copilot_api(ParagonNotificationType.SKIPPED_IMS_CREATION, request)
            return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True)
        self._update_possible_broker_issues(issues_list, broker_search_result, paragon_lob)

        dbg_slack_msg, effective_date_str, expiration_date_str = self._calculate_policy_dates(dbg_slack_msg, submission)
        effective_date = datetime.strptime(effective_date_str, "%Y-%m-%d")

        if not issues_list:
            # Detect and handle renewals. If renewal is detected (is_renewal_with_action_result has value) skip processing.
            # Do the detection and Copilot submission update only if we don't have any issues so far.
            is_renewal_with_action_result, detector_result = self._detect_and_handle_renewals(
                submission,
                paragon_lob,
                insured_search_result,
                broker_search_result,
                effective_date,
                request,
                dbg_slack_msg,
                is_declined_shell,
            )

            if is_renewal_with_action_result and detector_result.was_merged_with_existing_renewal:
                self.log.info("Detected renewal and merged with existing submission in the Copilot")
                return is_renewal_with_action_result
            elif is_renewal_with_action_result:
                self.log.info("Detected renewal, skipping processing")
                self._send_notification_to_copilot_api(
                    ParagonNotificationType.SKIPPED_IMS_CREATION,
                    request,
                    ims_control_numbers=detector_result.detected_ims_renewal_control_numbers,
                    is_renewal=True,
                )
                return is_renewal_with_action_result

        # Try to get Paragon Underwriter details
        dbg_slack_msg, paragon_underwriter = self._try_get_paragon_underwriter(dbg_slack_msg, submission)
        self._update_possible_underwriter_issues(
            issues_list,
            submission.assigned_underwriters,
            paragon_underwriter,
            paragon_lob,
            broker_search_result,
            request,
        )

        # Try to get Paragon Company Line details (Line of Business) for Copilot coverages
        dbg_slack_msg, paragon_company_lines = self._try_get_company_lines(
            dbg_slack_msg, insured_search_result.state, submission, paragon_lob, effective_date
        )
        if insured_search_result.state:
            self._update_possible_lob_issues(
                issues_list, submission.coverages, paragon_company_lines, report_email_account
            )

        dbg_slack_msg = self._update_debug_msg_with_policy_dates(dbg_slack_msg, submission)
        clearance_outcome = self._handle_clearance(
            company_lines=paragon_company_lines,
            submission=submission,
            report_email_account=report_email_account,
            found_insureds=insured_search_result.found_insureds,
        )

        if not issues_list:
            self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Got all the info needed to create IMS submission and quote")

            ims_created_quotes_guids = {}
            ims_insured_guid = None
            ims_insured_location_guid = None
            ims_submission_guid = None
            # IMS API requires to create all entities (Insured, Submission, Quote) in one call for the first LOB.
            # Then all additional LOBs are added by separate API call.
            first_paragon_lob = paragon_company_lines[0]
            if not insured_search_result.found_insureds:
                self.log.info("[PARAGON_IMS_QUEUE_HANDLER] - No IMS Insured(s) found - creating all entities")

                locations = [
                    Location(
                        location_name=insured_search_result.insured_requested_name,
                        address1=insured_search_result.address1,
                        address2=insured_search_result.address2,
                        country=ParagonBusinessRules.normalize_country_iso_code(insured_search_result.country),
                        city=insured_search_result.city,
                        county=insured_search_result.county,
                        state=insured_search_result.state,
                        zip_code=insured_search_result.zip_code,
                    )
                ]
                try:
                    new_insured_guid, new_locations_guids = self._paragon_ims_client.create_insured_with_locations(
                        insured_name=insured_search_result.insured_requested_name,
                        locations=locations,
                        business_type=insured_search_result.business_type,
                        delivery_method=DeliveryMethods.MAIL,
                        fein=insured_search_result.fein,
                    )
                    ims_insured_guid = new_insured_guid
                    ims_insured_location_guid = new_locations_guids[0]
                    self.log.info(
                        f"[PARAGON_IMS_QUEUE_HANDLER] Created new IMS Insured({ims_insured_guid}) "
                        f"with Location({ims_insured_location_guid})"
                    )
                    dbg_slack_msg += (
                        f"*[IMS+] Created Insured&Location, yeah!:* New insured `{ims_insured_guid}`; "
                        f"new location `{ims_insured_location_guid}`\n"
                    )
                except Exception as ims_error:
                    ims_error_str = str(ims_error)
                    self.log.warning("Failed to create IMS Insured", ims_error_str=ims_error_str)
                    raise FailedToCreateInImsException(reason=ims_error_str, paragon_lob=paragon_lob)
            else:
                self.log.info(
                    f"[PARAGON_IMS_QUEUE_HANDLER] - Found ({len(insured_search_result.found_insureds)}) IMS Insured(s)"
                )
                # Pick first found Insured for now
                first_insured = ParagonBusinessRules.pick_ims_insured(insured_search_result.found_insureds)
                ims_insured_guid = first_insured.insured_guid

            quote_initial_status, quote_initial_decline_reason = ParagonBusinessRules.determine_quote_initial_status(
                submission,
                paragon_lob,
                insured_search_result,
                effective_date,
                broker_search_result.broker_email,
                request.additional_context,
                self._paragon_ims_client,
                feature_flags_client.is_feature_enabled(FeatureFlags.PARAGON_IMS_CLEARING_ENABLED),
                self.log,
            )

            quote_initial_status_after_clearance = clearance_outcome.initial_quote_status(
                first_paragon_lob.line_name, quote_initial_status
            )
            quote_initial_decline_reason_after_clearance = clearance_outcome.initial_quote_declined_reason(
                first_paragon_lob.line_name
            )

            self.log.info(
                "Determined quote initial status and decline reason",
                quote_initial_status=quote_initial_status.name,
                quote_initial_decline_reason=(
                    quote_initial_decline_reason.name if quote_initial_decline_reason else "None"
                ),
                quote_initial_status_after_clearance=quote_initial_status_after_clearance.name,
                quote_initial_decline_reason_after_clearance=(
                    quote_initial_decline_reason_after_clearance.name
                    if quote_initial_decline_reason_after_clearance
                    else "None"
                ),
            )

            dbg_slack_msg += (
                f"[IMS+] Quote initial status `{quote_initial_status_after_clearance.name}` and decline reason after"
                " clearance"
                f" `{quote_initial_decline_reason_after_clearance.name if quote_initial_decline_reason_after_clearance else 'None'}` with"  # noqa
                f" initial status `{quote_initial_status.name}` and decline reason"
                f" `{quote_initial_decline_reason.name if quote_initial_decline_reason else 'None'}`\n"
            )
            new_submission_guid = self._paragon_ims_client.add_submission(
                insured_guid=ims_insured_guid,
                producer_contact_guid=broker_search_result.producer_contact_guid,
                producer_location_guid=broker_search_result.producer_location_guid,
                underwriter_guid=paragon_underwriter.ims_underwriter_guid,
                submission_date=date.today().strftime("%Y-%m-%d"),
            )
            new_quote_id = self._paragon_ims_client.add_quote_with_existing_submission(
                submission_guid=new_submission_guid,
                producer_contact_guid=broker_search_result.producer_contact_guid,
                underwriter_guid=paragon_underwriter.ims_underwriter_guid,
                business_type=insured_search_result.business_type,
                state_id=insured_search_result.state,
                quoting_location_guid=first_paragon_lob.office_guid,
                issuing_location_guid=first_paragon_lob.office_guid,
                company_location_guid=first_paragon_lob.company_location_guid,
                line_guid=first_paragon_lob.line_guid,
                billing_type_id=first_paragon_lob.billing_type_id,
                effective_date=effective_date_str,
                expiration_date=expiration_date_str,
                policy_type=PolicyTypes.NEW,  # TODO(plenza): Renewals?
                cost_center_id=-1,  # -1 will make Vertafore calculate this shit
                initial_status=quote_initial_status_after_clearance,
            )
            self.ims_sub_created = True
            self.log.info(
                f"[PARAGON_IMS_QUEUE_HANDLER] IMS Quote Result with sub {new_submission_guid=} {new_quote_id=}"
            )
            dbg_slack_msg += f"*[IMS+] Created Sub&Quote, yeah!:* New quote `{new_quote_id}`\n"
            ims_created_quotes_guids[first_paragon_lob.line_name] = new_quote_id
            ims_submission_guid = new_submission_guid

            if len(paragon_company_lines) > 1:
                dbg_slack_msg = self._create_additional_quotes(
                    effective_date_str,
                    expiration_date_str,
                    ims_created_quotes_guids,
                    ims_submission_guid,
                    paragon_underwriter,
                    broker_search_result.producer_contact_guid,
                    dbg_slack_msg,
                    insured_search_result.state,
                    paragon_company_lines,
                    insured_search_result.business_type,
                    quote_initial_status,
                    submission.id,
                    clearance_outcome,
                )

            ims_control_numbers = self._get_control_numbers(ims_created_quotes_guids)
            self._prepare_and_add_submission_client_ids(
                ims_created_quotes_guids,
                ims_control_numbers,
                ims_submission_guid,
                ims_insured_guid,
                submission.id,
                request,
            )
            self._possibly_add_submission_to_split(submission)

            blocked_on_clearing = False
            email_template_override = None
            email_template_data = None
            if paragon_lob.is_wc():
                blocked_on_clearing = self._workers_comp_logic_after_creation(
                    insured_search_result, new_quote_id, quote_initial_decline_reason, quote_initial_status, submission
                )

            if paragon_lob.is_es():
                blocked_on_clearing, email_template_override, email_template_data = self._es_logic_after_creation(
                    submission,
                    ims_created_quotes_guids,
                    clearance_outcome,
                    quote_initial_status,
                )

            notification_response = self._send_notification_to_copilot_api(
                ParagonNotificationType.SUCCESSFULLY_CREATED_IN_IMS,
                request,
                ims_control_numbers,
                quote_initial_decline_reason,
                quote_initial_status,
                blocked_on_clearing,
                email_template_override=email_template_override,
                email_template_data=email_template_data,
            )

            dbg_slack_msg = self._update_billing(ims_created_quotes_guids, paragon_lob, dbg_slack_msg)
            dbg_slack_msg, added_ims_documents_guids, files_failed_to_upload = self._upload_files_to_ims(
                ims_created_quotes_guids, paragon_underwriter, dbg_slack_msg, submission, notification_response
            )
            if files_failed_to_upload:
                self._send_slack_msg_for_failed_files(files_failed_to_upload, submission)

            dbg_slack_msg = self._update_ims_extras(
                ims_control_numbers, paragon_underwriter, submission, dbg_slack_msg, request
            )

            self._update_queue_request(
                ims_created_quotes_guids,
                ims_control_numbers,
                ims_insured_guid,
                ims_insured_location_guid,
                ims_submission_guid,
                added_ims_documents_guids,
                request,
            )

            self.session.commit()
            processing_result = ActionStatus.COMPLETED
            if request.is_manual_retry():
                self._send_slack_msg_for_successful_retry(submission)
            if (
                is_declined_shell
                and insured_search_result.city.lower() == "avon"
                and insured_search_result.address1 == "45 NOD RD"
            ):
                self.log.info("Created a triaged-out shell with Paragon dummy address")
        else:
            self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Errors prohibit to create IMS submission and quote")
            dbg_slack_msg += "*[######## ERROR ########]* Errors prohibit to create IMS submission and quote\n"

            if broker_search_result.missing_paragon_producer_info:
                self._set_broker_missing_error_on_request(request)

            self._send_notification_to_copilot_api(ParagonNotificationType.FAILED_TO_CREATE_IN_IMS, request)
            self._send_slack_completeness_message(
                submission,
                issues_list,
                msg_prefix=self._get_slack_msg_prefix(request, is_declined_shell, is_enhanced_shell, paragon_lob),
            )
            try:
                self._maybe_send_email_to_paragon(submission, paragon_lob, issues_list, request)
            except Exception:
                self.log.exception("[PARAGON_IMS_QUEUE_HANDLER] Failed to send email to Paragon")
            processing_result = ActionStatus.FAILED

        self._send_slack_debug_message(request, submission, dbg_slack_msg)
        return ActionResult(processing_result, inc_attempt_count=True)

    def _send_slack_msg_for_successful_retry(self, submission: Submission) -> None:
        report_url = "Unknown"
        submission_name = "Unknown"
        submission_id = "Unknown"
        if submission:
            report_url = get_report_url(submission.report_ids[0])
            submission_name = submission.name
            submission_id = str(submission.id)
        retry_succ_msg = (
            f"[Successful IMS creation retry!] *'{submission_name}'* :point_right: {report_url} with sub ID"
            f" `'{submission_id}'`\n"
        )
        self._slack_client.send_slack_message(SLACK_COMPLETENESS_CHANNEL, retry_succ_msg)

    def _handle_psp_broker_decline(self, submission: Submission, dbg_slack_msg: str) -> str:
        try:
            if submission.stage != SubmissionStage.DECLINED:
                self._copilot_client.decline_submission(
                    submission.id, send_decline_email=False, decline_reason="Paragon PSP submission's broker is blocked"
                )
                dbg_slack_msg += "*[COPILOT] Declined PSP submission due to broker being blocked*\n"
        except Exception:
            self.log.exception("Failed to decline PSP submission due to broker decline")

        return dbg_slack_msg

    def _es_logic_after_creation(
        self,
        submission: Submission,
        ims_created_quotes_guids: dict[str, str],
        clearance_outcome: ParagonClearanceInfo,
        quote_initial_status: QuoteStatus,
    ) -> tuple[bool, str | None, ParagonNotificationTemplateData]:
        blocked_on_clearing = False

        for line_name, quote_id in ims_created_quotes_guids.items():
            if reason := clearance_outcome.initial_quote_declined_reason(line_name):
                status = clearance_outcome.initial_quote_status(line_name, quote_initial_status)
                self.log.info(
                    "Updating quote status",
                    quote_guid=quote_id,
                    quote_status=status,
                    quote_status_reason_id=reason.value,
                )
                self._paragon_ims_client.update_quote_status(
                    quote_guid=quote_id,
                    quote_status=status,
                    quote_status_reason_id=reason.value,
                )

        if clearance_outcome.existing_unsupported_to_notify_about_primary_available:
            incoming_gl_control_number = ims_created_quotes_guids[ImsCoverage.CASUALTY_PRIMARY]
            existing_unsupported_xs_control_number = (
                clearance_outcome.existing_unsupported_to_notify_about_primary_available.control_number
            )
            self._sendgrid_client.send_ims_es_gl_submission_incoming_with_unsupported_xs_existing_notification(
                submission_name=submission.name,
                incoming_gl_control_number=incoming_gl_control_number,
                existing_unsupported_xs_control_number=str(existing_unsupported_xs_control_number),
                to_emails=[ParagonEmails.EXCESS_AND_SURPLUS.paragon_program_email()],
                bcc_email=ParagonEmails.kalepa_comms_out_group_email(),
            )

        email_template_override = None
        email_template_data = None
        if ImsCoverage.CASUALTY_EXCESS_UNSUPPORTED in ims_created_quotes_guids:
            # We're handling clearance for unsupported excess submission
            is_blocked_at_market = (
                clearance_outcome.initial_quote_declined_reason(ImsCoverage.CASUALTY_EXCESS_UNSUPPORTED)
                == QuoteDeclinedStatusReason.BLOCKED_AT_MARKET
            )
            is_blocked_by_declined_policy = clearance_outcome.was_blocked_by_declined_policy(
                ImsCoverage.CASUALTY_EXCESS_UNSUPPORTED
            )

            if is_blocked_at_market:
                email_template_override = (
                    "BLOCKED_ON_CLEARING_BY_DECLINED_SUBMISSION"
                    if is_blocked_by_declined_policy
                    else "BLOCKED_ON_CLEARING"
                )
                blocked_on_clearing = True
        else:
            has_xs = ImsCoverage.CASUALTY_EXCESS_SUPPORTED in ims_created_quotes_guids

            # We're handling clearance for GL + (possibly) XS submission
            is_gl_blocked_at_market = (
                clearance_outcome.initial_quote_declined_reason(ImsCoverage.CASUALTY_PRIMARY)
                == QuoteDeclinedStatusReason.BLOCKED_AT_MARKET
            )
            is_gl_blocked_by_declined_policy = clearance_outcome.was_blocked_by_declined_policy(
                ImsCoverage.CASUALTY_PRIMARY
            )

            is_xs_blocked_at_market = (
                clearance_outcome.initial_quote_declined_reason(ImsCoverage.CASUALTY_EXCESS_SUPPORTED)
                == QuoteDeclinedStatusReason.BLOCKED_AT_MARKET
            )
            is_xs_blocked_by_declined_policy = clearance_outcome.was_blocked_by_declined_policy(
                ImsCoverage.CASUALTY_EXCESS_SUPPORTED
            )

            if not has_xs:
                if is_gl_blocked_at_market:
                    email_template_override = (
                        "BLOCKED_ON_CLEARING_BY_DECLINED_SUBMISSION"
                        if is_gl_blocked_by_declined_policy
                        else "BLOCKED_ON_CLEARING"
                    )
                    blocked_on_clearing = True
            # We handle cases where we have both GL and XS
            elif is_gl_blocked_at_market and is_xs_blocked_at_market:
                email_template_override = (
                    "BLOCKED_ON_CLEARING_BY_DECLINED_SUBMISSION"
                    if is_gl_blocked_by_declined_policy and is_xs_blocked_by_declined_policy
                    else "BLOCKED_ON_CLEARING"
                )
                blocked_on_clearing = True
            elif not is_gl_blocked_at_market and is_xs_blocked_at_market:
                email_template_override = "BLOCKED_ON_CLEARING_PARTIALLY"
                email_template_data = ParagonNotificationTemplateData(
                    partially_blocked_on_clearing_es=PartiallyBlockedOnClearingEsData(
                        blocked_coverage_name="Excess", allowed_coverage_name="General Liability"
                    )
                )
                blocked_on_clearing = True
            elif is_gl_blocked_at_market and not is_xs_blocked_at_market:
                email_template_override = "BLOCKED_ON_CLEARING_PARTIALLY"
                email_template_data = ParagonNotificationTemplateData(
                    partially_blocked_on_clearing_es=PartiallyBlockedOnClearingEsData(
                        blocked_coverage_name="General Liability", allowed_coverage_name="Excess"
                    )
                )
                blocked_on_clearing = True

        return blocked_on_clearing, email_template_override, email_template_data

    def _workers_comp_logic_after_creation(
        self,
        insured_search_result: InsuredSearchResult,
        new_quote_id: str,
        quote_initial_decline_reason: QuoteDeclinedStatusReason | None,
        quote_initial_status: QuoteStatus,
        submission: Submission,
    ) -> bool:
        blocked_on_clearing = False
        if not insured_search_result.fein:
            # We don't have FEIN and ACORD_130 processing not completed/failed = decline+different email to Paragon Ops
            if ParagonBusinessRules.any_file_failed_processing(submission, ClassificationDocumentType.ACORD_130):
                self._handle_wc_undetermined_missing_or_existing_fein(insured_search_result, submission)
            # We don't have FEIN and ACORD_130 completed processing successfully = decline+email to Paragon Ops
            else:
                self._handle_wc_missing_fein(insured_search_result, submission)
            return blocked_on_clearing

        if not quote_initial_decline_reason:
            # Everything went well, main status was set during creation and there is no decline reason
            return blocked_on_clearing

        # We have decline reason which unfortunately means we need to do separate API call-can't be set during creation
        match quote_initial_decline_reason:
            case QuoteDeclinedStatusReason.BLOCKED_AT_MARKET:
                self._handle_wc_declined_blocked_at_market(
                    new_quote_id, quote_initial_decline_reason, quote_initial_status, submission
                )
                blocked_on_clearing = True
            case QuoteDeclinedStatusReason.INCOMPLETE:
                self._handle_wc_incomplete(new_quote_id, quote_initial_decline_reason, quote_initial_status, submission)
            case QuoteDeclinedStatusReason.OTHER:
                self._handle_wc_sub_general_decline(
                    new_quote_id, quote_initial_decline_reason, quote_initial_status, submission
                )
            case QuoteDeclinedStatusReason.FEIN_BLOCKED:
                self._handle_wc_sub_general_decline(
                    new_quote_id, quote_initial_decline_reason, quote_initial_status, submission, "FEIN blocked"
                )
            case _:
                self.log.error(
                    "Unexpected decline reason for Paragon WC submission",
                    reason=quote_initial_decline_reason,
                )
        return blocked_on_clearing

    def _handle_wc_incomplete(
        self,
        new_quote_id: str,
        quote_initial_decline_reason: QuoteDeclinedStatusReason,
        quote_initial_status: QuoteStatus,
        submission: Submission,
    ) -> None:
        self.log.info("Paragon WC submission is incomplete, setting as Declined - Incomplete")
        try:
            self._paragon_ims_client.update_quote_status(
                quote_guid=new_quote_id,
                quote_status=quote_initial_status,
                quote_status_reason_id=quote_initial_decline_reason.value,
            )
            if submission.stage != SubmissionStage.DECLINED:
                self._copilot_client.decline_submission(
                    submission.id,
                    send_decline_email=False,
                    decline_reason="Paragon WC submission is incomplete",
                )
        except Exception:
            self.log.exception("Failed to set Paragon and Copilot submission as Declined - Incomplete")

    def _handle_wc_missing_fein(self, insured_search_result: InsuredSearchResult, submission: Submission) -> None:
        try:
            self._send_email_to_paragon_with_missing_fein_notification(submission, insured_search_result)
            if submission.stage != SubmissionStage.DECLINED:
                self._copilot_client.decline_submission(
                    submission.id,
                    send_decline_email=False,
                    decline_reason="Paragon WC submission missing FEIN",
                )
        except Exception:
            self.log.exception("Failed to process Paragon WC submission missing FEIN")

    def _handle_wc_undetermined_missing_or_existing_fein(
        self, insured_search_result: InsuredSearchResult, submission: Submission
    ) -> None:
        try:
            self._send_email_to_paragon_with_undetermined_fein_notification(submission, insured_search_result)
        except Exception:
            self.log.exception("Failed to process Paragon WC submission with undetermined FEIN")

    def _handle_wc_sub_general_decline(
        self,
        new_quote_id: str,
        quote_initial_decline_reason: QuoteDeclinedStatusReason,
        quote_initial_status: QuoteStatus,
        submission: Submission,
        visible_explanation: str = "other reasons",
    ) -> None:
        self.log.info(f"Paragon WC submission has been declined for {visible_explanation}")
        try:
            self._paragon_ims_client.update_quote_status(
                quote_guid=new_quote_id,
                quote_status=quote_initial_status,
                quote_status_reason_id=quote_initial_decline_reason.value,
            )
            if submission.stage != SubmissionStage.DECLINED:
                self._copilot_client.decline_submission(
                    submission.id,
                    send_decline_email=False,
                    decline_reason=f"Paragon WC submission is declined for {visible_explanation}",
                )
        except Exception:
            self.log.exception(f"Failed to set Paragon and Copilot submission as Declined - {visible_explanation}")

    def _handle_wc_declined_blocked_at_market(
        self,
        new_quote_id: str,
        quote_initial_decline_reason: QuoteDeclinedStatusReason,
        quote_initial_status: QuoteStatus,
        submission: Submission,
    ) -> None:
        if feature_flags_client.is_feature_enabled(FeatureFlags.PARAGON_IMS_CLEARING_ENABLED):
            self.log.info("Clearing feature is enabled, setting Copilot and IMS submission as blocked")
            try:
                self._paragon_ims_client.update_quote_status(
                    quote_guid=new_quote_id,
                    quote_status=quote_initial_status,
                    quote_status_reason_id=quote_initial_decline_reason.value,
                )
                if submission.stage != SubmissionStage.BLOCKED:
                    self.log.info("Setting Copilot submission as blocked", old_copilot_sub_stage=submission.stage)
                    self._copilot_client.block_submission(submission.id)
            except Exception:
                self.log.exception("Failed to set Paragon and Copilot submission as blocked")
        else:
            self.log.info("Clearing feature is disabled, skipping setting Copilot and IMS submission as blocked")

    def _send_notification_to_copilot_api(
        self,
        notification: str,
        request: ParagonImsSubmissionCreationRequestQueue,
        ims_control_numbers: dict[str, int] = None,
        quote_initial_decline_reason: QuoteDeclinedStatusReason | None = None,
        quote_initial_status: QuoteStatus = None,
        blocked_on_clearing: bool = None,
        is_renewal: bool = None,
        email_template_override: str | None = None,
        email_template_data: ParagonNotificationTemplateData | None = None,
    ) -> ParagonNotificationResponse | None:
        self.log.info("Sending notification to Copilot API - successfully created IMS quote(s)")
        try:
            return self._copilot_client.send_paragon_notification(
                report_id=str(request.report_id),
                paragon_notification=ParagonNotification(
                    notification=notification,
                    ims_initial_status=quote_initial_status.value if quote_initial_status else None,
                    ims_initial_reason=quote_initial_decline_reason.value if quote_initial_decline_reason else None,
                    ims_control_numbers=list(ims_control_numbers.values()) if ims_control_numbers else None,
                    ims_lobs=list(ims_control_numbers.keys()) if ims_control_numbers else None,
                    blocked_on_clearing=blocked_on_clearing,
                    is_renewal=is_renewal,
                    email_template_override=email_template_override,
                    email_template_data=email_template_data,
                ),
            )
        except Exception:
            self.log.exception("Failed to send Paragon notification to Copilot API")
            return None

    def _calculate_policy_dates(self, dbg_slack_msg: str, submission: Submission) -> Tuple[str, str]:
        effective_date, expiration_date = ParagonBusinessRules.calculate_policy_dates(
            submission.proposed_effective_date
        )
        effective_date_str = effective_date.strftime("%Y-%m-%d")
        expiration_date_str = expiration_date.strftime("%Y-%m-%d")
        self.log.info(
            f"[PARAGON_IMS_QUEUE_HANDLER] Calculated policy dates {effective_date_str=} {expiration_date_str=}"
        )
        dbg_slack_msg += f"*[IMS+] Calculated policy dates:* {effective_date_str=} {expiration_date_str=}\n"
        return dbg_slack_msg, effective_date_str, expiration_date_str

    def _handle_clearance(
        self,
        company_lines: List[ParagonCompanyLine],
        submission: Submission,
        report_email_account: str,
        found_insureds: list[Insured] | None,
    ) -> ParagonClearanceInfo:
        clearance_result = ParagonClearanceInfo()
        if report_email_account.lower() != ParagonEmails.paragon_excess_and_surplus_autoforward_email().lower():
            self.log.info(
                "Skipping clearance for submission",
                submission_id=submission.id,
                report_email_account=report_email_account,
            )
            return clearance_result

        picked_ims_insured = ParagonBusinessRules.pick_ims_insured(found_insureds)
        if not picked_ims_insured:
            self.log.info(
                "Skipping clearance for submission with no Insured",
                submission_id=submission.id,
                report_email_account=report_email_account,
            )
            return clearance_result

        if any(file.file_type == FileType.BROKER_OF_RECORD_LETTER for file in submission.files):
            self.log.info(
                "Skipping clearance for submission with Broker of Record letter",
                submission_id=submission.id,
            )
            return clearance_result

        self.log.info(
            "Handling clearance for submission", submission_id=submission.id, report_email_account=report_email_account
        )

        submission_lines = {line.line_name for line in company_lines}
        clearance_action = ParagonClearanceCheckStatusInput(
            coverages=[
                ImsCoverage.CASUALTY_EXCESS_SUPPORTED,
                ImsCoverage.CASUALTY_EXCESS_UNSUPPORTED,
                ImsCoverage.CASUALTY_PRIMARY,
            ],
            insured_guid=picked_ims_insured.insured_guid,
        )

        clearance_check_result_raw = self._copilot_client.execute_submission_action(
            submission_id=submission.id, action=clearance_action
        )
        clearance_check_result: ParagonClearanceCheckStatus = ParagonClearanceCheckStatusSchema().load(
            clearance_check_result_raw
        )
        coverage_to_status: dict[ImsCoverage, ParagonCoverageClearanceStatus] = {
            status.coverage_name: status for status in clearance_check_result.coverage_statuses
        }

        # Incoming submission has GL coverage included, but there exists an unsupported XS submission.
        # Paragon's ops team needs to change the coverage of the unsupported XS submission to supported.
        if (
            ImsCoverage.CASUALTY_PRIMARY in submission_lines  # Incoming submission has GL coverage included
            and coverage_to_status[
                ImsCoverage.CASUALTY_EXCESS_UNSUPPORTED
            ].is_blocked  # There exists an unsupported XS submission
            and not coverage_to_status[ImsCoverage.CASUALTY_PRIMARY].is_blocked  # GL submission is not blocked
        ):
            blocker = ParagonBusinessRules.get_prioritized_submission_for_blocked_at_market_check(
                coverage_to_status[ImsCoverage.CASUALTY_EXCESS_UNSUPPORTED].blocked_by_submissions
            )
            self.log.info(
                "There exists an unsupported XS submission and GL submission is not blocked, notifying Paragon",
                blocker=blocker,
            )
            clearance_result.existing_unsupported_to_notify_about_primary_available = blocker

        coverage_to_initial_status_and_reason = {}

        # If GL is blocked, then all GL submissions must be blocked
        coverage_clearance_status = coverage_to_status[ImsCoverage.CASUALTY_PRIMARY]
        if coverage_clearance_status.is_blocked:
            blocker = ParagonBusinessRules.get_prioritized_submission_for_blocked_at_market_check(
                coverage_clearance_status.blocked_by_submissions
            )

            coverage_to_initial_status_and_reason[ImsCoverage.CASUALTY_PRIMARY] = NewSubmissionClearanceInfo(
                initial_quote_status=QuoteStatus.DECLINED,
                initial_declined_reason=QuoteDeclinedStatusReason.BLOCKED_AT_MARKET,
                was_blocked_by_declined_policy=blocker.quote_status_id == QuoteStatus.DECLINED.value
                and blocker.quote_status_reason_id != QuoteDeclinedStatusReason.BLOCKED_AT_MARKET.value,
            )

        # Handle XS - both supported and unsupported
        # If there exists a XS submission (either supported or unsupported), then all XS submissions must be blocked
        supported_xs_coverage_status = coverage_to_status[ImsCoverage.CASUALTY_EXCESS_SUPPORTED]
        unsupported_xs_coverage_status = coverage_to_status[ImsCoverage.CASUALTY_EXCESS_UNSUPPORTED]

        if supported_xs_coverage_status.is_blocked and unsupported_xs_coverage_status.is_blocked:
            self.log.error(
                "Both supported and unsupported XS submissions are blocked, this should never happen",
                coverage_to_status=coverage_to_status,
            )

        existing_xs_policies = (supported_xs_coverage_status.blocked_by_submissions or []) + (
            unsupported_xs_coverage_status.blocked_by_submissions or []
        )

        if existing_xs_policies:
            xs_blocker = ParagonBusinessRules.get_prioritized_submission_for_blocked_at_market_check(
                existing_xs_policies
            )

            new_submission_clearance_info = NewSubmissionClearanceInfo(
                initial_quote_status=QuoteStatus.DECLINED,
                initial_declined_reason=QuoteDeclinedStatusReason.BLOCKED_AT_MARKET,
                was_blocked_by_declined_policy=xs_blocker.quote_status_id == QuoteStatus.DECLINED.value
                and xs_blocker.quote_status_reason_id != QuoteDeclinedStatusReason.BLOCKED_AT_MARKET.value,
            )

            coverage_to_initial_status_and_reason[ImsCoverage.CASUALTY_EXCESS_SUPPORTED] = new_submission_clearance_info
            coverage_to_initial_status_and_reason[ImsCoverage.CASUALTY_EXCESS_UNSUPPORTED] = (
                new_submission_clearance_info
            )

        clearance_result.company_line_to_initial_status_and_reason = coverage_to_initial_status_and_reason

        self.log.info("Clearance result", clearance_result=clearance_result)

        return clearance_result

    def _update_debug_msg_with_policy_dates(self, dbg_slack_msg: str, submission: Submission) -> str:
        if not submission.policy_expiration_date:
            dbg_slack_msg += "*[######## ERROR ########]* Policy expiration date is missing\n"
        else:
            dbg_slack_msg += f"*[COPILOT+] Policy expiration date:* {submission.policy_expiration_date}\n"
        if not submission.proposed_effective_date:
            dbg_slack_msg += "*[######## ERROR ########]* Policy effective date is missing\n"
        else:
            dbg_slack_msg += f"*[COPILOT+] Policy effective date:* {submission.proposed_effective_date}\n"
        return dbg_slack_msg

    def _send_slack_msg_for_failed_files(self, files_failed_to_upload: List[str], submission: Submission) -> None:
        prefixed_failed_files = [
            f"Submission created OK but this file failed to upload to IMS: `{file_name}`"
            for file_name in files_failed_to_upload
        ]
        self._send_slack_completeness_message(submission, prefixed_failed_files)

    def get_requests(self) -> List[DeduplicatedRequestsQueueResult]:
        # Custom implementation that takes into account the number of attempts and the age of the request and also
        # if the request is missing Paragon Producer info (in this case we want to retry with much longer delay).
        deduplicated_requests: List[DeduplicatedRequestsQueueResult] = get_deduplicated_requests(
            self.session,
            self.queue_cls,
            self.config.max_attempts,
            self.config.hours_to_fail,
            self.config.delay_requests,
        )

        # Create a mapping of event ID to deduplicated request, take the newest events deduplicated by submission id
        event_id_to_request = {request.event_ids[-1]: request for request in deduplicated_requests}
        event_ids = list(event_id_to_request.keys())

        # And fetch all possible events to process by using 'event_ids'
        possible_events_to_process: List[ParagonImsSubmissionCreationRequestQueue] = (
            self.session.query(ParagonImsSubmissionCreationRequestQueue)
            .filter(ParagonImsSubmissionCreationRequestQueue.id.in_(event_ids))
            .all()
        )

        # Logic here:
        # 1. If event is seen for the first time, include it
        # 2. If event is seen for the second time, so there was an issue, include it if it is older than X minutes
        #   if it 'missing IMS broker' and we need to wait for Paragon or older than Y minutes if it is any other issue.
        #   Y is much shorther than X - if broker is missing we want to initially wait at least 1 day after retrying.
        # 3. If event is seen for the third and final time, include it if it is older than Z minutes or W minutes
        #   with the same logic for missing broker.
        now = datetime.now(timezone.utc)
        short_first_retry_cut_off = now - timedelta(minutes=SHORT_FIRST_RETRY_CUT_OFF_IN_MINUTES)
        short_second_retry_cut_off = now - timedelta(minutes=SHORT_SECOND_RETRY_CUT_OFF_MINUTES)
        long_first_retry_cut_off = now - timedelta(minutes=LONG_FIRST_RETRY_CUT_OFF_IN_MINUTES)
        long_second_retry_cut_off = now - timedelta(minutes=LONG_SECOND_RETRY_CUT_OFF_MINUTES)
        events_to_process = []
        for event in possible_events_to_process:
            deduplicated_request = event_id_to_request.get(event.id)
            if deduplicated_request:
                if event.attempt_count == 0:
                    # Include all submissions that we see for the first time
                    events_to_process.append(deduplicated_request)
                elif event.attempt_count == 1:
                    self._add_request_if_cut_off_time_passed(
                        deduplicated_request,
                        event,
                        events_to_process,
                        long_first_retry_cut_off,
                        short_first_retry_cut_off,
                    )
                elif event.attempt_count == 2:
                    self._add_request_if_cut_off_time_passed(
                        deduplicated_request,
                        event,
                        events_to_process,
                        long_second_retry_cut_off,
                        short_second_retry_cut_off,
                    )
                else:
                    self.log.warning("Event has more than 2 attempts which is not supported, skipping")

        return events_to_process

    def _add_request_if_cut_off_time_passed(
        self,
        deduplicated_request: DeduplicatedRequestsQueueResult,
        event: ParagonImsSubmissionCreationRequestQueue,
        events_to_process: List[DeduplicatedRequestsQueueResult],
        long_retry_cut_off: datetime,
        short_retry_cut_off,
    ) -> None:
        if event.error_details == ParagonImsCreationIssues.MISSING_IMS_BROKER:
            if event.updated_at < long_retry_cut_off:
                events_to_process.append(deduplicated_request)
        elif event.updated_at < short_retry_cut_off:
            events_to_process.append(deduplicated_request)

    def _get_control_numbers(self, ims_created_quotes_guids: Dict[str, str]) -> Dict[str, int]:
        control_numbers = {}
        for coverage_name, quote_guid in ims_created_quotes_guids.items():
            control_number = self._paragon_ims_client.get_control_number(quote_guid=quote_guid)
            if control_number:
                control_numbers[coverage_name] = control_number
                self.log.info("Got control number", quote_guid=quote_guid, control_number=control_number)
            else:
                self.log.info("Failed to get control number", quote_guid=quote_guid)
        return control_numbers

    def _prepare_and_add_submission_client_ids(
        self,
        ims_created_quotes_guids: Dict[str, str],
        control_numbers: Dict[str, int],
        ims_submission_guid: str,
        ims_insured_guid: str,
        submission_id: UUID,
        request: ParagonImsSubmissionCreationRequestQueue,
    ) -> None:
        submission_client_id_data = {
            "ims_insured_guid": ims_insured_guid,
            "ims_submission_id": ims_submission_guid,
            "ims_quote_guids": ims_created_quotes_guids,
            "ims_control_numbers": control_numbers,
        }
        try:
            self._copilot_client.add_submission_client_id(
                submission_id,
                json.dumps(submission_client_id_data),
                raise_exc=True,
                source=SubmissionClientIdSource.API,
            )
        except ApiException as e:
            # 404 means that shadow report was created and we need to use actual submission id
            if e.status == 404:
                self.log.info("404 when adding submission client id, trying to use actual submission id")
                actual_submission_id_str = self._get_and_update_actual_submission_report_is_linked_to(request)
                if actual_submission_id_str:
                    added_client_id = self._copilot_client.add_submission_client_id(
                        UUID(actual_submission_id_str),
                        json.dumps(submission_client_id_data),
                        source=SubmissionClientIdSource.API,
                    )
                    if not added_client_id:
                        self.log.info("Failed to add submission client id and could not add using actual submission id")
            elif e.status == 409:
                self.log.warning("Submission client id already exists")
            else:
                raise e

    def _update_queue_request(
        self,
        ims_created_quotes_guids: Dict[str, str],
        ims_control_numbers: Dict[str, int],
        ims_insured_guid: str,
        ims_insured_location_guid: str,
        ims_submission_guid: str,
        added_ims_documents_guids: List[str],
        request: ParagonImsSubmissionCreationRequestQueue,
    ) -> None:
        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.paragon_ims_created_quotes_guids: (
                    ims_created_quotes_guids.values()
                ),
                ParagonImsSubmissionCreationRequestQueue.paragon_ims_created_control_numbers: (
                    ims_control_numbers.values()
                ),
                ParagonImsSubmissionCreationRequestQueue.paragon_ims_insured_guid: ims_insured_guid,
                ParagonImsSubmissionCreationRequestQueue.paragon_ims_insured_location_guid: ims_insured_location_guid,
                ParagonImsSubmissionCreationRequestQueue.paragon_ims_submission_group_guid: ims_submission_guid,
                ParagonImsSubmissionCreationRequestQueue.paragon_ims_created_quote_at: datetime.utcnow(),
                ParagonImsSubmissionCreationRequestQueue.paragon_ims_created_documents_guids: added_ims_documents_guids,
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )

    def _create_additional_quotes(
        self,
        effective_date: str,
        expiration_date: str,
        ims_created_quotes_guids: Dict[str, str],
        ims_submission_guid: str,
        paragon_underwriter: ParagonUnderwriter,
        producer_contact_guid: str,
        slack_msg: str,
        state: str,
        state_paragon_lines: List[ParagonCompanyLine],
        business_type: BusinessTypes,
        quote_initial_status: QuoteStatus,
        copilot_submission_id: Union[UUID, str],
        clearance_outcome: ParagonClearanceInfo,
    ) -> str:
        for line in state_paragon_lines[1:]:
            new_quote_id = self._paragon_ims_client.add_quote_with_existing_submission(
                submission_guid=ims_submission_guid,
                producer_contact_guid=producer_contact_guid,
                underwriter_guid=paragon_underwriter.ims_underwriter_guid,
                business_type=business_type,
                state_id=state,
                quoting_location_guid=line.office_guid,
                issuing_location_guid=line.office_guid,
                company_location_guid=line.company_location_guid,
                line_guid=line.line_guid,
                billing_type_id=line.billing_type_id,
                effective_date=effective_date,
                expiration_date=expiration_date,
                policy_type=PolicyTypes.NEW,  # TODO(plenza): Renewals?
                cost_center_id=-1,  # -1 will make Vertafore calculate this shit
                initial_status=clearance_outcome.initial_quote_status(line.line_name, quote_initial_status),
            )
            ims_created_quotes_guids[line.line_name] = new_quote_id
            self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Additional quote created {new_quote_id=}")
            slack_msg += f"*[IMS+] Created additional quote `{new_quote_id}`\n"
        return slack_msg

    def _update_billing(
        self, ims_created_quotes_guids: dict, paragon_lob: ParagonLineOfBusiness, slack_msg: str
    ) -> str:
        match paragon_lob:
            case ParagonLineOfBusiness.EXCESS_AND_SURPLUS:
                billing_recipient = BillingRecipient.STATEMENT_BILLING
            case ParagonLineOfBusiness.WORKERS_COMPENSATION:
                billing_recipient = BillingRecipient.MONTHLY_REPORTING
            case ParagonLineOfBusiness.ALLY_AUTO:
                billing_recipient = BillingRecipient.INSURED
            case ParagonLineOfBusiness.PSP_E3_PROPERTY:
                billing_recipient = BillingRecipient.STATEMENT_BILLING
            case _:
                raise NotImplementedError(f"'update_billing' op is not implemented for Paragon LOB {paragon_lob}")

        for created_quote_guid in ims_created_quotes_guids.values():
            self._paragon_ims_client.set_billing_recipient(
                quote_guid=created_quote_guid, billing_recipient=billing_recipient
            )
            self.log.info(
                f"[PARAGON_IMS_QUEUE_HANDLER] Set billing recipient to {billing_recipient=} on {created_quote_guid=}"
            )
            slack_msg += f"*[IMS+] Set billing recipient to {billing_recipient=} on {created_quote_guid=}\n"
        return slack_msg

    def _upload_files_to_ims(
        self,
        ims_created_quotes_guids: Dict[str, str],
        paragon_underwriter: ParagonUnderwriter,
        slack_msg: str,
        submission: Submission,
        notification_response: ParagonNotificationResponse | None,
    ) -> Tuple[str, List[str], List[str]]:
        added_ims_documents = []
        files_failed_to_upload = []
        paragon_folders_to_ids = self._cached_create_map_of_paragon_folders_to_ids()
        # As per Paragon requirements we should add files into the first quote that was created (LOB)
        first_quote_guid = next(iter(ims_created_quotes_guids.values()))

        files_to_upload = list(submission.files)
        if notification_response and notification_response.file_id_of_email_sent_to_broker:
            files_to_upload.append(
                self._copilot_client.get_file(submission.id, notification_response.file_id_of_email_sent_to_broker)
            )

        for file_lite in files_to_upload:
            if not ParagonBusinessRules.file_supported_for_ims_upload(file_lite, files_to_upload):
                self.log.info(
                    "[PARAGON_IMS_QUEUE_HANDLER] - Skipping file "
                    f"{file_lite.name} with id {file_lite.id} and type "
                    f"{file_lite.file_type} as it is not supported for IMS sync"
                )
                continue

            try:
                if file := self._copilot_client.get_file(submission.id, file_lite.id):
                    paragon_folder = ParagonBusinessRules.get_paragon_folder(self.log, file, files_to_upload)
                    paragon_folder_id = paragon_folders_to_ids.get(paragon_folder)
                    submission_file = requests.get(file.presigned_url)
                    self.log.info(
                        "[PARAGON_IMS_QUEUE_HANDLER] - Uploading file "
                        f"{file.name} with id {file_lite.id} to Paragon IMS folder "
                        f"'{paragon_folder}' with id {paragon_folder_id}"
                    )
                    if submission_file.status_code == 200:
                        submission_file_contents = submission_file.content
                        submission_file_contents_as_base64_binary = base64.b64encode(submission_file_contents)
                        submission_file_contents_as_base64_string = submission_file_contents_as_base64_binary.decode(
                            "ascii"
                        )
                        added_doc_guid = self._paragon_ims_client.add_document_to_quote(
                            underwriter_guid=paragon_underwriter.ims_underwriter_guid,
                            quote_guid=first_quote_guid,
                            folder_id=paragon_folder_id,
                            file_name=ParagonBusinessRules.clean_filename(file.name),
                            base64_file_contents=submission_file_contents_as_base64_string,
                            file_description=ParagonBusinessRules.get_file_description(file),
                        )
                        added_ims_documents.append(added_doc_guid)
                    else:
                        self.log.info(
                            "[PARAGON_IMS_QUEUE_HANDLER] - Failed to download file "
                            f"{file.name} with id {file_lite.id} from Copilot"
                        )
                        files_failed_to_upload.append(file.name)
                else:
                    self.log.info(
                        f"[PARAGON_IMS_QUEUE_HANDLER] - File {file_lite.name} "
                        f"with id {file_lite.id} was not found in Copilot DB"
                    )
                    slack_msg += (
                        f"*[######## ERROR ########]* File {file_lite.name} "
                        f"with id {file_lite.id} was not found in Copilot DB\n"
                    )
                    files_failed_to_upload.append(file_lite.name)
            except Exception:
                self.log.exception(f"[PARAGON_IMS_QUEUE_HANDLER] - Failed to upload file {file_lite.name} to IMS")
                files_failed_to_upload.append(file_lite.name)
        slack_msg += (
            f"*[IMS+] Added {len(added_ims_documents)} document(s) "
            f"to quote {first_quote_guid}:* {added_ims_documents}\n"
        )
        return slack_msg, added_ims_documents, files_failed_to_upload

    @cache
    def _cached_create_map_of_paragon_folders_to_ids(self):
        paragon_folders_to_ids = ParagonBusinessRules.create_map_of_paragon_folders_to_ids(
            self._paragon_ims_client.get_supplementary_data()
        )
        return paragon_folders_to_ids

    def _try_get_company_lines(
        self,
        slack_msg: str,
        state: Optional[str],
        submission: Submission,
        paragon_lob: ParagonLineOfBusiness,
        policy_effective_date: datetime,
    ) -> Tuple[str, List[ParagonCompanyLine]]:
        if not submission.coverages:
            self.log.info("[PARAGON_IMS_QUEUE_HANDLER] No coverages found in submission")
            slack_msg += "*[######## ERROR ########]* No coverages found in submission\n"
            return slack_msg, None

        if state is None:
            self.log.info("[PARAGON_IMS_QUEUE_HANDLER] state is 'None' - unable to find Company line")
            slack_msg += "*[######## ERROR ########]* state is 'None' - unable to find Company line\n"
            return slack_msg, None

        company_lines_to_fetch = ParagonBusinessRules.get_paragon_line_names(paragon_lob, submission.coverages)
        self.log.info("Selected Paragon company lines to fetch", company_lines=company_lines_to_fetch)
        slack_msg += f"*[IMS+] Selected Paragon company lines to fetch:* {company_lines_to_fetch}\n"

        state_paragon_lines = []
        wc_class_codes = self._fetch_wc_class_codes_if_possible(paragon_lob, submission)
        for company_line_to_fetch in company_lines_to_fetch:
            paragon_company_lines = self._copilot_client.get_paragon_company_lines(company_line_to_fetch)

            lines = [pp for pp in paragon_company_lines if pp.state_id.lower() == state.lower()]
            if len(lines) == 0:
                slack_msg += (
                    "*[######## ERROR ########]* Failed to find Paragon Company Line "
                    f"for {company_line_to_fetch=} {state=}\n"
                )
            else:
                picked_line = ParagonBusinessRules.pick_line_of_business(
                    lines, paragon_lob, state, wc_class_codes, policy_effective_date
                )
                state_paragon_lines.append(picked_line)
                slack_msg += (
                    f"[IMS+] Found ({len(lines)}) Paragon Company Line(s) for "
                    f"line_name=`{company_line_to_fetch}` state=`{state}` carrier=`{picked_line.location_name}`\n"
                )
                self.log.info(
                    "Found Paragon Company Line", company_line_to_fetch=company_line_to_fetch, picked_line=picked_line
                )

        return slack_msg, state_paragon_lines

    def _fetch_wc_class_codes_if_possible(
        self, paragon_lob: ParagonLineOfBusiness, submission: Submission
    ) -> Optional[Set[str]]:
        wc_class_codes = None
        if paragon_lob.is_wc():
            wc_class_codes_full = self._copilot_client.get_workers_comp_state_rating_info_for_submission(
                submission_id=submission.id
            )
            wc_class_codes = {wc_class_code.class_code for wc_class_code in wc_class_codes_full}
        return wc_class_codes

    def _try_get_broker_details(self, slack_msg: str, submission: Submission) -> Tuple[str, BrokerSearchResults]:
        try:
            got_producer_info = False
            broker_email = "Unknown"
            broker_other_email_in_ims = None
            broker_name = "Unknown"
            producer_contact_guid = None
            producer_info = "Unknown"
            producer_guid = "Unknown"
            producer_name = "Unknown"
            producer_location_guid = "Unknown"

            broker_email = submission.broker.email if submission.broker else None
            broker_name = submission.broker.name if submission.broker else None
            if not broker_email:
                self.log.info("Broker email is missing - trying to get it from brokerage contact")
                broker_email = submission.brokerage_contact.email if submission.brokerage_contact else None
                broker_name = submission.brokerage_contact.name if submission.brokerage_contact else None

            brokerage_name = submission.brokerage.name if submission.brokerage else "Unknown"

            if not broker_email:
                slack_msg += (
                    "*[######## ERROR ########]* Broker email is missing - unable to find any IMS Producer info\n"
                )
            else:
                producer_contact_guid = self._paragon_ims_client.get_producer_contact_guid(
                    producer_contact_email=broker_email
                )
                self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Producer Contact GUID by email {producer_contact_guid}")

                producer_info: Producer = self._paragon_ims_client.get_producer_info_by_contact_guid(
                    producer_contact_guid=producer_contact_guid
                )
                self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Producer Info by contact GUID {producer_info}")
                producer_guid = self._paragon_ims_client.get_producer_guid_by_code(
                    producer_code=producer_info.producer_code
                )
                self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Producer GUID {producer_guid}")
                producer_contact_info = self._paragon_ims_client.get_producer_contact_info(
                    producer_contact_guid=producer_contact_guid
                )
                self.log.info(
                    f"[PARAGON_IMS_QUEUE_HANDLER] Producer Contact Info by contact GUID {producer_contact_info}"
                )
                producer_location_guid = producer_info.producer_location_guid
                producer_name = producer_info.producer_name
                got_producer_info = True
        except Exception as error:
            self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Failed to get broker details {error}")
            if broker_name:
                try:
                    possible_ims_broker_guid = self._paragon_ims_client.get_producer_contact_guid(
                        contact_name=broker_name
                    )
                    if possible_ims_broker_guid:
                        possible_ims_broker: ProducerContact = self._paragon_ims_client.get_producer_contact_info(
                            producer_contact_guid=possible_ims_broker_guid
                        )
                        self.log.info(
                            f"Because IMS broker for email {broker_email} is missing, tried by name {broker_name} and"
                            f" found {possible_ims_broker.email} in IMS"
                        )
                        broker_other_email_in_ims = possible_ims_broker.email
                except NoSuchParagonProducerContactException:
                    self.log.info(
                        f"Because IMS broker for email {broker_email} is missing, tried by name {broker_name} "
                        "and there is no such broker at all in IMS"
                    )

            slack_msg += f"*[######## ERROR ########]* Failed to get broker details {error}\n"
        finally:
            slack_msg += (
                f"\n\n*[COPILOT+] Broker(name='{broker_name}') Email:* `{broker_email}` "
                f"Other Email: `{broker_other_email_in_ims}`\n"
                f"*[COPILOT+] Agency Name:* `{brokerage_name}`\n"
                f"*[IMS+] Producer Contact GUID:* `{producer_contact_guid}`\n"
                f"*[IMS+] Producer GUID:* `{producer_guid}`\n"
                f"*[IMS+] Producer Name:* `{producer_name}`\n"
                f"*[IMS+] Producer Location GUID:* `{producer_location_guid}`\n"
                f"*[IMS+] Producer Info:* `{producer_info}`\n\n"
            )

        return slack_msg, BrokerSearchResults(
            broker_email=broker_email,
            broker_other_email_in_ims=broker_other_email_in_ims,
            broker_name=broker_name,
            got_producer_info=got_producer_info,
            missing_paragon_producer_info=(broker_email and not got_producer_info),
            producer_contact_guid=producer_contact_guid,
            producer_guid=producer_guid,
            producer_name=producer_name,
            producer_location_guid=producer_location_guid,
        )

    def _try_get_paragon_underwriter(self, slack_msg: str, submission: Submission) -> Tuple[str, ParagonUnderwriter]:
        paragon_underwriter = None
        try:
            if not submission.assigned_underwriters:
                self.log.info("[PARAGON_IMS_QUEUE_HANDLER] No underwriters assigned in Copilot")
                slack_msg += "*[######## ERROR ########]* No underwriters assigned in Copilot\n"
            else:
                self.log.info(
                    f"[PARAGON_IMS_QUEUE_HANDLER] Underwriters assigned in Copilot {submission.assigned_underwriters}"
                )

                slack_msg, picked_uw = ParagonBusinessRules.pick_underwriter(
                    self.log, submission.assigned_underwriters, slack_msg
                )

                if picked_uw:
                    uw_email = picked_uw.email
                    paragon_underwriter = self._copilot_client.get_paragon_underwriter_by_email(uw_email)
                    self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Paragon Underwriter {paragon_underwriter}")
                    uw_emails = [uw.email for uw in submission.assigned_underwriters]
                    slack_msg += f"*[COPILOT+] Underwriters assigned in Copilot:* {uw_emails}\n"
                    if not paragon_underwriter:
                        slack_msg += "*[######## ERROR ########]* Failed to get paragon underwriter\n"
                    else:
                        slack_msg += (
                            "*[IMS+] Paragon Underwriter:* "
                            f"`{paragon_underwriter.email} {paragon_underwriter.ims_underwriter_guid}`\n"
                        )
        except Exception as error:
            self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Failed to get paragon underwriter {error}")
            slack_msg += f"*[######## ERROR ########]* Failed to get paragon underwriter {error}\n"
        return slack_msg, paragon_underwriter

    def _try_get_insureds(
        self, slack_msg: str, submission: Submission, is_declined_shell: bool, paragon_lob: ParagonLineOfBusiness
    ) -> Tuple[str, InsuredSearchResult]:
        bus_data: InsuredSearchResult = InsuredSearchResult()
        found_insureds = []

        try:
            if is_declined_shell:
                slack_msg, bus_data = ParagonBusinessRules.extract_business_data_from_attachments(
                    submission, slack_msg, self._copilot_client, self.log
                )
            else:
                slack_msg, bus_data = self._extract_business_data_from_entity_resolution(submission, slack_msg)

            self._extract_and_set_fein(bus_data, submission)

            # If at this point we are still missing address parts required for IMS (state or zip or city or address1)
            # we should try final fallback to ChatGPT 4 assuming we have raw_address_string (either from
            # raw email or files processing or from requested address in ERS).
            if bus_data.raw_address_string and ParagonBusinessRules.is_missing_fni_address(bus_data):
                self.log.info(
                    "All standard ways to extract FNI address parts failed, trying to get them using ChatGPT",
                    raw_address_string=bus_data.raw_address_string,
                    extracted_business_data=bus_data,
                )
                self._try_to_get_fni_address_from_chatgpt(bus_data)

            if not bus_data.zip_code:
                self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Zip code is missing")

            if bus_data.zip_code and (not bus_data.city or not bus_data.state):
                (
                    possible_city,
                    possible_state,
                ) = self._zippopotam_client.try_get_city_and_state_abbreviation_from_zipcode(bus_data.zip_code.strip())
                self.log.info(
                    "[PARAGON_IMS_QUEUE_HANDLER] Fallback city+state search: "
                    f"got city and state from zip code {possible_city=} {possible_state=}"
                )
                bus_data.city = bus_data.city or possible_city
                bus_data.state = bus_data.state or possible_state

            if not bus_data.city:
                self.log.info("[PARAGON_IMS_QUEUE_HANDLER] City is missing")
                slack_msg += "*[######## ERROR ########]* City is missing in Copilot\n"

            if bus_data.city and len(bus_data.city) > 49:
                self.log.info("[PARAGON_IMS_QUEUE_HANDLER] City is too long, truncating to 49 chars")
                bus_data.city = bus_data.city[0:49]

            if bus_data.address1 and len(bus_data.address1) > 49:
                self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Address1 is too long, truncating to 49 chars")
                bus_data.address1 = bus_data.address1[0:49]

            if bus_data.address2 and len(bus_data.address2) > 49:
                self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Address2 is too long, truncating to 49 chars")
                bus_data.address2 = bus_data.address2[0:49]

            if bus_data.insured_requested_name:
                found_insureds = ParagonImsSubmissionCreationRequestQueueHandler._find_insureds_in_ims(
                    self.log, self._paragon_ims_client, bus_data, paragon_lob, is_declined_shell
                )
                bus_data.found_insureds = found_insureds
                slack_msg += (
                    f"*[COPILOT+] Business(FNI):* business_name=`{bus_data.insured_requested_name}`; "
                    f"business_type=`{bus_data.business_type}`; fein=`{bus_data.fein}`;"
                    f"country=`{bus_data.country}`; county=`{bus_data.county}`; city=`{bus_data.city}`; "
                    f"state=`{bus_data.state}`; zip_code=`{bus_data.zip_code}`; address1=`{bus_data.address1}`; "
                    f"address2=`{bus_data.address2}; search_input=`{bus_data.ims_search_input}``\n"
                )
                self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Found IMS Insureds for {bus_data}; {found_insureds}")

        finally:
            if not found_insureds:
                slack_msg += "*[## WARN ##]* No IMS Insureds found\n"
            else:
                found_insureds_str = "\n".join([str(found_insured) for found_insured in found_insureds])
                slack_msg += f"*[IMS+] IMS Insureds:*\n{found_insureds_str}\n"
            if not bus_data.insured_requested_name or bus_data.insured_requested_name == "NOT_PRESENT":
                slack_msg += "*[######## ERROR ########]* Insured requested name is missing in Copilot!\n"

        return slack_msg, bus_data

    def _extract_and_set_fein(self, bus_data: InsuredSearchResult, submission: Submission) -> None:
        fni_business_id = None
        for business in submission.businesses:
            if business.named_insured == "FIRST_NAMED_INSURED":
                fni_business_id = business.business_id
        onboarded_data = ParagonBusinessRules.fetch_all_submission_files_and_their_data(self.log, submission)
        bus_data.fein = get_fein(
            business_id=fni_business_id,
            organization_id=ExistingOrganizations.Paragon.value,
            submission_id=submission.id,
            onboarded_files=onboarded_data,
            facts_api_client=self._facts_api_client,
            normalize_with_hyphen_after_second_digit=True,
            bound_log=self.log,
        )

    @staticmethod
    def _find_insureds_in_ims(
        bound_log: BoundLogger,
        ims_client: ParagonIMSAPIClient,
        copilot_business_data: InsuredSearchResult,
        paragon_lob: ParagonLineOfBusiness,
        is_shell_submission: bool,
    ) -> list[Insured]:
        found_insureds = []
        # If Copilot sub has FEIN the logic of IMS Insured interaction should be:
        # 1. Try to find IMS Insured by FEIN
        # 2. If found, use it directly
        # 3. If not found, always create new IMS Insured with Copilot FEIN
        # 4. If multiple IMS Insureds found by FEIN, raise an error, send email and DO NOT create in IMS
        if copilot_business_data.fein:
            bound_log.info("FEIN is present in Copilot submission - searching IMS Insured by FEIN")
            try:
                ims_insured_guid_with_fein = ims_client.get_insured_guid_from_fein(fein=copilot_business_data.fein)
                if ims_insured_guid_with_fein:
                    bound_log.info("Found IMS Insured by FEIN", ims_insured_guid_with_fein=ims_insured_guid_with_fein)
                    found_insureds = [
                        Insured(
                            insured_guid=ims_insured_guid_with_fein,
                            policy_name=copilot_business_data.insured_requested_name,
                            city=copilot_business_data.city,
                            state=copilot_business_data.state,
                            zip_code=copilot_business_data.zip_code,
                            tax_number_match=False,
                            status_id=1,
                        )
                    ]
                    return found_insureds
                else:
                    bound_log.info("No IMS Insured found by FEIN")
            except InconsistentIMSInsuredsForFeinException:
                issue = f"{ParagonImsCreationIssues.MULTIPLE_INSUREDS_FOR_SINGLE_FEIN}({copilot_business_data.fein})."
                raise FailedToCreateInImsException(reason=issue, paragon_lob=paragon_lob)

        if copilot_business_data.fein and not ParagonBusinessRules.should_run_ims_search_if_fein_search_was_a_miss(
            paragon_lob
        ):
            bound_log.info(
                (
                    "FEIN is present in Copilot submission, but search by FEIN was a miss - skipping IMS Insured search"
                    " for this LOB"
                ),
                paragon_lob=paragon_lob,
            )
        else:
            bound_log.info(
                "FEIN is missing in Copilot submission (or FEIN search missed) - searching IMS Insured by name, city,"
                " state"
            )
            # When Copilot sub does not have FEIN (or we should perform search) the logic of IMS Insured interaction is:
            # 1. Try to use IMS Search Client (it has multiple steps of searching).
            # 2. If found, use the first result (they're sorted by matching score).
            # 3. If not found, try to search only by Insured name, use the first result.
            # 4. If not found create new IMS Insured and send email that FEIN is missing (WC only)

            bound_log.info(
                "Searching IMS Insureds with IMS search client", ims_search_input=copilot_business_data.ims_search_input
            )

            search_client = IMSSearchClient(ims_client, bound_log)
            search_result = search_client.search_for_entity(copilot_business_data.ims_search_input)
            if conclusive_step := search_result.conclusive_step:
                found_insureds = [candidate.insured for candidate in conclusive_step.candidates]

            bound_log.info(
                "Finished searching IMS Insureds with IMS search client",
                found_insureds=found_insureds,
                search_result=search_result,
                is_conclusive=search_result.is_conclusive,
                picked_ims_guid=search_result.picked_ims_guid,
            )

            if found_insureds:
                bound_log.info("Found IMS Insureds by using main search logic")

            if (
                not found_insureds
                and feature_flags_client.is_feature_enabled(
                    FeatureFlags.PARAGON_IMS_FALLBACK_INSURED_SEARCH_BY_NAME_ONLY
                )
                and is_shell_submission
            ):
                bound_log.info(
                    (
                        "No IMS Insureds found by main search logic; Fallback to search by name only - for shell"
                        " submissions only"
                    ),
                    requested_name=copilot_business_data.insured_requested_name,
                )
                found_insureds = ims_client.find_insureds(
                    policy_name_contains=copilot_business_data.insured_requested_name,
                    city=None,
                    state=None,
                )
                if found_insureds:
                    bound_log.info("Found IMS Insureds by name only", found_insureds=found_insureds)
                else:
                    bound_log.info("No IMS Insureds found by name only", found_insureds=found_insureds)

        return found_insureds

    def _set_ims_search_input_from_entity_resolution(
        self, ims_search_input: IMSSearchInput, entity: Entity, submission_business: SubmissionBusiness
    ):
        primary_premises = get_primary_premises(entity)
        premises = primary_premises.premises
        if premises:
            primary_address = Address(
                city=premises.city,
                zip_code=premises.postal_code,
                state=premises.state,
                address_line_1=premises.address_line_1,
                address_line_2=premises.address_line_2,
                is_po_box=False,
            )

            ims_search_input.entity_addresses = [primary_address]

        ims_search_input.dba_name = get_primary_dba_name(entity)
        ims_search_input.legal_name = get_primary_legal_name(entity)
        ims_search_input.all_names = [en.value for en in entity.names]

        ims_search_input.requested_name = submission_business.requested_name
        ims_search_input.requested_address = (
            try_parse_address_string(submission_business.requested_address)
            if submission_business.requested_address
            else None
        )

    def _extract_business_data_from_entity_resolution(
        self, submission: Submission, slack_msg: str
    ) -> tuple[str, InsuredSearchResult]:
        result = InsuredSearchResult(country="USA")
        result.insured_requested_name = ParagonBusinessRules.normalize_insured_name(submission.name)
        for business in submission.businesses:
            if business.named_insured == "FIRST_NAMED_INSURED":
                self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Found FIRST_NAMED_INSURED")
                ers_entity = self._ers_client.get_entity(entity_id=business.business_id)
                business_type = ParagonBusinessRules.try_to_determine_business_type(
                    self.log, ers_entity.business_type, result.insured_requested_name
                )

                self._set_ims_search_input_from_entity_resolution(result.ims_search_input, ers_entity, business)

                premises = get_physical_premises(ers_entity)
                # if premises.premises and premises.is_physical_address:
                # Lifting is_physical_address check for now, because it is a lot not true for Paragon (?)
                if premises.premises:
                    result.city = premises.premises.city
                    result.country = premises.premises.country

                    county = premises.premises.county
                    if county:
                        county = ParagonBusinessRules.strip_trailing_county_word(county)
                    result.county = county

                    result.address1 = (
                        premises.premises.address_line_1 if premises.premises.address_line_1 else "Unknown"
                    )
                    result.address2 = premises.premises.address_line_2  # address2 can be empty in IMS
                    result.state = premises.premises.state
                    result.zip_code = premises.premises.postal_code
                    result.business_type = business_type
                    if ParagonBusinessRules.is_missing_fni_address(result):
                        self.log.info(
                            "FNI address is missing in ERS, trying to set it from requested address",
                            premises=premises.premises,
                        )
                        ParagonBusinessRules.set_fni_address_from_requested_address(
                            result, business.requested_address, self.log
                        )
                        slack_msg += "FNI address is missing in ERS, tried from requested address\n"
                    result.got_copilot_business = True
                    break
                else:
                    slack_msg += (
                        "*[######## ERROR ########]* Failed to get premises for "
                        f"{result.insured_requested_name=}; {premises=}\n"
                    )

        return slack_msg, result

    def _send_slack_debug_message(
        self, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission, slack_msg: str
    ) -> None:
        self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Sending slack message {slack_msg}")

        if env != "prod":
            return

        organization_name = "Paragon"
        report_url = "Unknown"
        submission_name = "Unknown"
        submission_id = "Unknown"
        if submission:
            report_url = get_report_url(submission.report_ids[0])
            submission_name = submission.name
            submission_id = str(submission.id)

        message = (
            40 * "="
            + "\n"
            + f"Submission named *'{submission_name}'* with ID `'{submission_id}'` for organization "
            f"'{organization_name}' ({report_url}) has been verified."
            f"Here comes some details about IMS interactions: {slack_msg}"
        )
        self._slack_client.send_slack_message(SLACK_DEBUG_CHANNEL, message)

    def _send_slack_completeness_message(
        self,
        submission: Submission,
        issues_list: List[str],
        msg_prefix: Optional[str] = None,
        slack_channel: str = SLACK_COMPLETENESS_CHANNEL,
    ) -> None:
        self.log.info(f"[PARAGON_IMS_QUEUE_HANDLER] Sending slack completeness message {issues_list}")

        if env != "prod":
            return

        report_url = "Unknown"
        submission_name = "Unknown"
        submission_id = "Unknown"
        if submission:
            report_url = get_report_url(submission.report_ids[0])
            submission_name = submission.name
            submission_id = str(submission.id)

        issue_bullet_points = "\n".join([f"• {issue}" for issue in issues_list])
        prefix = f"{msg_prefix} " if msg_prefix else ""
        message = (
            f"{prefix}Submission named *'{submission_name}'* :point_right: {report_url} with sub ID "
            f"`'{submission_id}'` could not be created in IMS:\n{issue_bullet_points}\n"
            "=========================================================================\n"
        )
        self._slack_client.send_slack_message(slack_channel, message)

    def _setup_logger(self, request: ParagonImsSubmissionCreationRequestQueue) -> None:
        self.log = self.log.try_unbind(
            "submission_id",
            "request",
            "status",
            "reason_id",
            "reason_description",
            "current_status",
        )
        self.log = self.log.bind(submission_id=request.submission_id, request=request)

    def _try_fetch_submission(self, submission_id: Union[str, UUID]) -> Submission:
        return self._copilot_client.get_submission(
            str(submission_id),
            expand=[
                "broker",
                "brokerage",
                "assigned_underwriters",
                "businesses",
                "brokerage_contact",
                "coverages",
                "client_submission_ids",
                "is_renewal",
                "report_ids",
                "files",
                "files.id",
                "files.name",
                "files.classification",
                "files.file_type",
                "files.is_internal",
                "files.parent_file_id",
                "files.processing_state",
                "files.processed_file.processed_data",
                "policy_expiration_date",
                "proposed_effective_date",
                "origin",
                "is_verified",
                "is_verified_shell",
                "primary_naics_code",
                "stage",
                "is_enhanced_shell",
            ],
            enrich_with_ers_data=False,
        )

    def _handle_processing_error(
        self, error: Exception, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> ActionResult:
        if "Execution Timeout Expired".lower() in str(error).lower() and not self.ims_sub_created:
            self.log.warning(
                (
                    "Failed to create Paragon IMS submission due to timeout but IMS root entities not created yet so"
                    " will retry"
                ),
                error=error,
            )
            self._send_slack_debug_message(
                request,
                submission,
                (
                    f"\nCC: <{PATRYK_LENZA_SLACK_ID}> - Submission with id `{request.submission_id}` "
                    "timed out on creating Paragon IMS entities but root entities not created yet so will retry; "
                    f"Error details: {error}\n"
                ),
            )
            return ActionResult(ActionStatus.FAILED, inc_attempt_count=False)

        if request.attempt_count == self.config.max_attempts - 1:
            self.log.warning(
                "Failed to create Paragon IMS submission - final attempt reached",
                error_str=str(error),
                error_type=f"{type(error).__name__}",
                error_repr=repr(error),
                error_traceback=traceback.format_exc(),
            )

            lob = ParagonLineOfBusiness.EXCESS_AND_SURPLUS
            report_email_account = self._get_report_source_email_account(submission)
            if ParagonBusinessRules.is_workers_comp_coverage(submission, report_email_account):
                lob = ParagonLineOfBusiness.WORKERS_COMPENSATION
            elif ParagonBusinessRules.is_psp_e3_property_coverage(report_email_account):
                lob = ParagonLineOfBusiness.PSP_E3_PROPERTY

            base_issue = f"{ParagonImsCreationIssues.GENERIC_FAILURE}. ({lob})"
            email_to_paragon_issue = f"{base_issue} - Internal Copilot error - Kalepa team has been notified"
            slack_detailed_issue = f"{base_issue} - {error}"

            self._maybe_send_email_to_paragon(submission, lob, [email_to_paragon_issue], request)
            self._send_slack_completeness_message(
                submission, [f"{slack_detailed_issue}\nCC: <{PATRYK_LENZA_SLACK_ID}>"], msg_prefix="[FINAL ATTEMPT]"
            )
            self._send_notification_to_copilot_api(ParagonNotificationType.FAILED_TO_CREATE_IN_IMS, request)
        else:
            self.log.warning(
                "Failed to create Paragon IMS submission, retrying in next iteration",
                error=error,
            )
            self._send_slack_debug_message(
                request,
                submission,
                (
                    f"\nCC: <{PATRYK_LENZA_SLACK_ID}> - Submission with id `{request.submission_id}` "
                    f"failed to create Paragon IMS entities - retrying in next iteration; Error details: {error}\n"
                ),
            )
            self.submissions_to_split.pop(str(submission.id), None)

        return ActionResult(ActionStatus.FAILED, inc_attempt_count=True)

    def _handle_failed_to_create_in_ims_exception(
        self,
        error: FailedToCreateInImsException,
        submission: Submission,
        request: ParagonImsSubmissionCreationRequestQueue,
    ) -> ActionResult:
        self.log.warning("Failed to create entity in Paragon IMS", error_details=str(error))
        ping_plenza = True
        if error.reason.startswith(ParagonImsCreationIssues.MULTIPLE_INSUREDS_FOR_SINGLE_FEIN):
            self._set_multiple_insureds_for_fein_error_on_request(request)
            ping_plenza = False
        issue = f"{ParagonImsCreationIssues.GENERIC_FAILURE}. ({error.paragon_lob}) - {error.reason}"
        self._maybe_send_email_to_paragon(submission, error.paragon_lob, [issue], request)
        ping_plenza_msg = f"\nCC: <{PATRYK_LENZA_SLACK_ID}>" if ping_plenza else ""
        self._send_slack_completeness_message(submission, [f"{issue}{ping_plenza_msg}"])
        self._send_notification_to_copilot_api(ParagonNotificationType.FAILED_TO_CREATE_IN_IMS, request)
        return ActionResult(ActionStatus.FAILED, inc_attempt_count=True)

    def _update_possible_insured_issues(
        self, issues_list: List[str], insured_search_result: InsuredSearchResult
    ) -> None:
        if (
            not insured_search_result.insured_requested_name
            or insured_search_result.insured_requested_name == "NOT_PRESENT"
        ):
            issues_list.append(ParagonImsCreationIssues.MISSING_REQUESTED_BUSINESS_NAME)
            return

        if not insured_search_result.got_copilot_business:
            issues_list.append(ParagonImsCreationIssues.MISSING_FNI)
            return

        if (
            not insured_search_result.country
            or not insured_search_result.state
            or not insured_search_result.city
            or not insured_search_result.zip_code
        ):
            issues_list.append(ParagonImsCreationIssues.COULD_NOT_PARSE_FNI_ADDRESS)

    def _update_possible_broker_issues(
        self, issues_list: List[str], broker_search_results: BrokerSearchResults, paragon_lob: ParagonLineOfBusiness
    ) -> None:
        if not broker_search_results.broker_email:
            issues_list.append(ParagonImsCreationIssues.MISSING_COPILOT_BROKER_EMAIL)
        elif not broker_search_results.got_producer_info:
            missing_broker_msg = (
                f'"{paragon_lob.value}" - {ParagonImsCreationIssues.MISSING_IMS_BROKER} for '
                f"'{broker_search_results.broker_email}' ({broker_search_results.broker_name})"
            )
            if broker_search_results.broker_other_email_in_ims:
                missing_broker_msg += (
                    "; However there is IMS broker for person with this name but with email "
                    f"'{broker_search_results.broker_other_email_in_ims}'"
                )
            issues_list.append(missing_broker_msg)
        else:
            if not broker_search_results.producer_guid:
                issues_list.append(
                    f"{ParagonImsCreationIssues.MISSING_IMS_PRODUCER_GUID} for '{broker_search_results.broker_email}'"
                )
            if not broker_search_results.producer_contact_guid:
                issues_list.append(
                    f"{ParagonImsCreationIssues.MISSING_IMS_PRODUCER_CONTACT_GUID} for"
                    f" '{broker_search_results.broker_email}'"
                )
            if not broker_search_results.producer_location_guid:
                issues_list.append(
                    f"{ParagonImsCreationIssues.MISSING_IMS_PRODUCER_LOCATION_GUID} for"
                    f" '{broker_search_results.broker_email}'"
                )

    def _update_possible_underwriter_issues(
        self,
        issues_list: List[str],
        assigned_underwriters: List[AssignedUnderwriter],
        paragon_underwriter: ParagonUnderwriter,
        paragon_lob: ParagonLineOfBusiness,
        broker_search_result: BrokerSearchResults,
        request: ParagonImsSubmissionCreationRequestQueue,
    ) -> None:
        if not assigned_underwriters:
            issues_list.append(ParagonImsCreationIssues.MISSING_COPILOT_UNDERWRITER)
            self._set_uw_missing_error_on_request(request)
            return
        if paragon_underwriter:
            return

        uw_emails = [uw.email for uw in assigned_underwriters]

        if (
            paragon_lob.is_wc()
            and ParagonBusinessRules.COPILOT_UW_FOR_PARAGON_WORKERS_COMP in uw_emails
            and broker_search_result.broker_email
        ):
            broker_email = broker_search_result.broker_email
            broker_name = broker_search_result.broker_name or "Unknown Name"
            issues_list.append(
                f"{ParagonImsCreationIssues.MISSING_WC_UW_MAPPING} for Broker '{broker_email}' ({broker_name})"
            )
            self._set_missing_wc_uw_mapping_error_on_request(request)
            return

        if (
            paragon_lob.is_psp_e3_property()
            and ParagonBusinessRules.COPILOT_UW_FOR_PARAGON_PSP in uw_emails
            and broker_search_result.broker_email
        ):
            broker_email = broker_search_result.broker_email
            broker_name = broker_search_result.broker_name or "Unknown Name"
            issues_list.append(
                f"{ParagonImsCreationIssues.MISSING_PSP_UW_MAPPING} for Broker '{broker_email}' ({broker_name})"
            )
            self._set_missing_psp_uw_mapping_error_on_request(request)
            return

        if len(assigned_underwriters) == 1:
            issues_list.append(f"{ParagonImsCreationIssues.MISSING_IMS_UNDERWRITER} for {uw_emails}")
        else:
            issues_list.append(f"{ParagonImsCreationIssues.AMBIGUOUS_COPILOT_UNDERWRITER} for {uw_emails}")
            self._set_ambiguous_uw_error_on_request(request)

    def _update_possible_lob_issues(
        self, issues_list: List[str], coverages, state_paragon_lines, email_account: str
    ) -> None:
        if (
            email_account.lower() == ParagonEmails.paragon_excess_and_surplus_autoforward_email().lower()
            and not coverages
        ):
            issues_list.append(ParagonImsCreationIssues.RECOVERABLE_MISSING_COPILOT_COVERAGES)
        elif not coverages:
            issues_list.append(ParagonImsCreationIssues.MISSING_COPILOT_COVERAGES)
        elif not state_paragon_lines:
            coverages = {coverage.coverage_type.upper() for coverage in coverages}
            issues_list.append(f"{ParagonImsCreationIssues.MISSING_IMS_LOBS} {coverages}")

    def _handle_no_submission_error(self, request: ParagonImsSubmissionCreationRequestQueue) -> None:
        self.log.warning(
            "[PARAGON_IMS_QUEUE_HANDLER] Submission was not found, cancelling Paragon IMS submission creation request"
        )
        error_msg = f"Submission with id '{request.submission_id}' was not found in Copilot Database"
        self._send_slack_debug_message(request, None, f"\nCC: <{PATRYK_LENZA_SLACK_ID}> - {error_msg}\n")

    def _log_submission_found(self, submission: Submission) -> None:
        self.log.info(
            "[PARAGON_IMS_QUEUE_HANDLER] Paragon Submission found",
            submission_id=str(submission.id),
            is_verified=submission.is_verified,
            is_verified_shell=submission.is_verified_shell,
        )

    def _handle_submission_being_a_copy(self, request: ParagonImsSubmissionCreationRequestQueue) -> None:
        self.log.info("Submission is a copy, we already created IMS entities for original one - Skipping")

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: "Submission was a copy",
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()

    def _handle_submission_being_a_sync(self, request: ParagonImsSubmissionCreationRequestQueue) -> None:
        self.log.info("Submission origin is a SYNC - Skipping")

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: "Submission origin was a SYNC",
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()

    def _handle_renewals_not_supported(
        self, dbg_slack_msg: str, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> None:
        self.log.info("Paragon Submission is a renewal - Skipping IMS creation")

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: (
                    "Renewals were not supported at the time this request came in"
                ),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += "*[COPILOT+] Submission is a renewal - Skipping\n"
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _handle_ally_auto_skip(
        self, dbg_slack_msg: str, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> None:
        self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Submission is for Ally Auto - Skipping entirely")

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: (
                    "Ally Auto submissions were not supported at the time this request came in"
                ),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += "*[COPILOT+] Submission is for Ally Auto - Skipping entirely\n"
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _handle_paragon_es_forward_temporary_skip(
        self, dbg_slack_msg: str, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> None:
        self.log.info(
            "[PARAGON_IMS_QUEUE_HANDLER] Submission is for Paragon E&S Forward and integration is disabled on FF -"
            " Skipping"
        )

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: ES_FORWARD_TEMPORARY_SKIP_MESSAGE,
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += (
            f"*[COPILOT+] Submission is for Paragon E&S Forward - Skipping on FF (CC: <{PFUCHS_SLACK_ID}>) \n"
        )
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _handle_psp_e3_property_skip(
        self, dbg_slack_msg: str, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> None:
        self.log.info(
            "[PARAGON_IMS_QUEUE_HANDLER] Submission is for PSP E3 Property and IMS integration is disabled on feature"
            " flag - skipping"
        )

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: (
                    "PSP E3 Property submissions were not enabled on feature flag at the time this request came in"
                ),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += "*[COPILOT+] Submission is for PSP E3 Property - Skipped by feature flag\n"
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _handle_trident_public_risk_skip(
        self, dbg_slack_msg: str, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> None:
        self.log.info(
            "[PARAGON_IMS_QUEUE_HANDLER] Submission is for Paragon Trident and IMS integration is disabled on feature"
            " flag - skipping"
        )

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: (
                    "Trident submissions were not enabled on feature flag at the time this request came in"
                ),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += "*[COPILOT+] Submission is for Paragon Trident - Skipped by feature flag\n"
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _handle_ally_auto_renewal_skip(
        self, dbg_slack_msg: str, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> None:
        self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Submission is for Ally Auto and is renewal - Skipping")

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: (
                    "Ally Auto renewals were not supported at the time this request came in"
                ),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += "*[COPILOT+] Submission is for Ally Auto and is renewal - Skipping\n"
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _set_error_on_request(self, request: ParagonImsSubmissionCreationRequestQueue, error_message: str) -> None:
        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: error_message,
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()

    def _set_broker_missing_error_on_request(self, request: ParagonImsSubmissionCreationRequestQueue) -> None:
        self._set_error_on_request(request, ParagonImsCreationIssues.MISSING_IMS_BROKER)

    def _set_multiple_insureds_for_fein_error_on_request(
        self, request: ParagonImsSubmissionCreationRequestQueue
    ) -> None:
        self._set_error_on_request(request, ParagonImsCreationIssues.MULTIPLE_INSUREDS_FOR_SINGLE_FEIN)

    def _set_uw_missing_error_on_request(self, request: ParagonImsSubmissionCreationRequestQueue) -> None:
        self._set_error_on_request(request, ParagonImsCreationIssues.MISSING_COPILOT_UNDERWRITER)

    def _set_missing_wc_uw_mapping_error_on_request(self, request: ParagonImsSubmissionCreationRequestQueue) -> None:
        self._set_error_on_request(request, ParagonImsCreationIssues.MISSING_WC_UW_MAPPING)

    def _set_missing_psp_uw_mapping_error_on_request(self, request: ParagonImsSubmissionCreationRequestQueue) -> None:
        self._set_error_on_request(request, ParagonImsCreationIssues.MISSING_PSP_UW_MAPPING)

    def _set_ambiguous_uw_error_on_request(self, request: ParagonImsSubmissionCreationRequestQueue) -> None:
        self._set_error_on_request(request, ParagonImsCreationIssues.AMBIGUOUS_COPILOT_UNDERWRITER)

    def _get_slack_msg_prefix(
        self,
        request: ParagonImsSubmissionCreationRequestQueue,
        is_declined_shell: bool,
        is_enhanced_shell: bool,
        paragon_lob: ParagonLineOfBusiness,
    ) -> str:
        attempts_to_prefix = {
            0: None,
            1: "[2nd Attempt]",
            2: "[3rd and Final Attempt]",
        }
        prefix = attempts_to_prefix.get(request.attempt_count)
        if is_enhanced_shell:
            prefix = "[Enhanced Shell]" + (prefix or "")
        elif is_declined_shell:
            prefix = "[Declined Shell]" + (prefix or "")
        else:
            prefix = "[Fully Verified]" + (prefix or "")

        match paragon_lob:
            case ParagonLineOfBusiness.EXCESS_AND_SURPLUS:
                prefix = "[E&S]" + (prefix or "")
            case ParagonLineOfBusiness.WORKERS_COMPENSATION:
                prefix = "[Workers Comp]" + (prefix or "")
            case ParagonLineOfBusiness.ALLY_AUTO:
                prefix = "[Ally Auto]" + (prefix or "")
            case ParagonLineOfBusiness.PSP_E3_PROPERTY:
                prefix = "[PSP E3]" + (prefix or "")
            case ParagonLineOfBusiness.TRIDENT_PUBLIC_RISK:
                prefix = "[Trident Public Risk]" + (prefix or "")
            case _:
                prefix = "[Unknown LOB]" + (prefix or "")

        return prefix

    def _maybe_send_email_to_paragon(
        self,
        submission: Submission,
        paragon_lob: ParagonLineOfBusiness,
        issue_list: List[str],
        request: ParagonImsSubmissionCreationRequestQueue,
    ) -> None:
        if (
            not paragon_lob
            or paragon_lob == ParagonLineOfBusiness.ALLY_AUTO
            or (
                paragon_lob == ParagonLineOfBusiness.PSP_E3_PROPERTY
                and not feature_flags_client.is_feature_enabled(FeatureFlags.PARAGON_IMS_PSP_E3_INTEGRATION_ENABLED)
            )
        ):
            self.log.info("Skipping email to Paragon", lob=paragon_lob)
            return

        # Separate the issues into two lists
        paragon_broker_issue_list = ParagonBusinessRules.leave_only_issues_related_to_paragon_broker(issue_list)
        missing_copilot_data_issue_list = ParagonBusinessRules.leave_only_issues_related_to_missing_copilot_data(
            issue_list
        )

        # Check if there are any relevant issues before making API calls
        if not paragon_broker_issue_list and not missing_copilot_data_issue_list:
            self.log.info("[PARAGON_IMS_QUEUE_HANDLER] No relevant issues found, not sending email to Paragon")
            return

        report = self._copilot_client.get_report_by_id(
            submission.report_ids[0], expand="email_subject,correspondence_id"
        )
        source_email_subject = report.email_subject or submission.name
        report_url = f"https://copilot.kalepa.com/report/{submission.report_ids[0]}"

        new_ims_creation_issue = ParagonImsCircuitBreakers(
            event=ImsCircuitBreakerEvent.FAILED_IMS_CREATION,
            reason="; ".join(issue_list),
            submission_id=submission.id,
            report_id=report.id,
        )
        self.session.add(new_ims_creation_issue)

        cc_emails = self._determine_cc_emails(request)

        # Send email for Paragon Broker issues
        if paragon_broker_issue_list:
            action_list = ParagonBusinessRules.get_action_list_for_issues(paragon_broker_issue_list, report_url)
            cleaned_issue_list = [issue.replace("*", "") for issue in paragon_broker_issue_list]
            self.log.info(
                f"[PARAGON_IMS_QUEUE_HANDLER] Sending email to Paragon Agency Apps for issues: {cleaned_issue_list}",
                to_email="<EMAIL>",
                cc_emails=cc_emails if cc_emails else "None",
            )
            self._sendgrid_client.send_ims_submission_not_created_notification(
                report_url=report_url,
                submission_name=submission.name,
                source_email_subject=source_email_subject,
                line_of_business=paragon_lob.value,
                is_ally_auto=paragon_lob.is_ally_auto(),
                issue_list=cleaned_issue_list,
                action_list=action_list,
                to_emails=["<EMAIL>"],
                cc_emails=cc_emails,
                bcc_email=ParagonEmails.kalepa_comms_out_group_email(),
            )

        if not missing_copilot_data_issue_list:
            return

        # Send email for missing Copilot data issues
        match paragon_lob:
            case ParagonLineOfBusiness.EXCESS_AND_SURPLUS:
                email = self._copilot_client.get_email(
                    correspondence_id=report.correspondence_id, prefer_root_email=True
                )
                email_from_lower = email.email_from.lower()
                email_account_lower = email.email_account.lower()
                recipient_email = ParagonEmails.EXCESS_AND_SURPLUS.paragon_program_email()
                if (
                    ParagonEmails.EXCESS_AND_SURPLUS.paragon_program_email() in email_from_lower
                    or ParagonEmails.paragon_excess_and_surplus_autoforward_email() in email_account_lower
                ):
                    recipient_email = ParagonEmails.EXCESS_AND_SURPLUS.paragon_program_email()
                elif ParagonEmails.EXCESS_AND_SURPLUS_UNSUPPORTED.paragon_program_email() in email_from_lower:
                    recipient_email = ParagonEmails.EXCESS_AND_SURPLUS_UNSUPPORTED.paragon_program_email()
            case ParagonLineOfBusiness.WORKERS_COMPENSATION:
                recipient_email = ParagonEmails.WORKERS_COMPENSATION.paragon_program_email()
            case ParagonLineOfBusiness.ALLY_AUTO:
                return
            case ParagonLineOfBusiness.PSP_E3_PROPERTY:
                recipient_email = ParagonEmails.PSP_E3_PROPERTY_QUOTE.paragon_program_email()
            case _:
                raise NotImplementedError(f"'sending_email' op is not implemented for Paragon LOB: {paragon_lob}")

        self.log.info(
            (
                f"[PARAGON_IMS_QUEUE_HANDLER] Sending email to Paragon Source {recipient_email} "
                f"for issues: {missing_copilot_data_issue_list}"
            ),
            to_email=recipient_email,
            cc_emails=cc_emails if cc_emails else "None",
        )

        action_list = ParagonBusinessRules.get_action_list_for_issues(missing_copilot_data_issue_list, report_url)
        cleaned_issue_list = [issue.replace("*", "") for issue in missing_copilot_data_issue_list]
        self._sendgrid_client.send_ims_submission_not_created_notification(
            report_url=report_url,
            submission_name=submission.name,
            source_email_subject=source_email_subject,
            line_of_business=paragon_lob.value,
            is_ally_auto=paragon_lob.is_ally_auto(),
            issue_list=cleaned_issue_list,
            action_list=action_list,
            to_emails=[recipient_email],
            cc_emails=cc_emails,
            bcc_email=ParagonEmails.kalepa_comms_out_group_email(),
        )

    def _determine_cc_emails(self, request: ParagonImsSubmissionCreationRequestQueue) -> Optional[List[str]]:
        cc_emails = None
        if request.is_manual_retry():
            retrying_user_email = ParagonBusinessRules.get_lowercased_retrying_user_email(request.additional_context)
            if retrying_user_email and ParagonBusinessRules.is_retrying_user_email_whitelisted(retrying_user_email):
                cc_emails = [retrying_user_email]
        return cc_emails

    def _send_email_to_paragon_with_missing_fein_notification(
        self, submission: Submission, insured_search_result: InsuredSearchResult
    ) -> None:
        self.log.info(f"Sending email to Paragon WC team about missing FEIN for submission {submission.name}")
        sub_name = insured_search_result.insured_requested_name or submission.name
        self._sendgrid_client.send_ims_wc_submission_missing_fein_notification(
            submission_name=sub_name,
            source_email_subject=sub_name,
            to_emails=[ParagonEmails.WORKERS_COMPENSATION.paragon_program_email()],
            bcc_email=ParagonEmails.kalepa_comms_out_group_email(),
        )

    def _send_email_to_paragon_with_undetermined_fein_notification(
        self, submission: Submission, insured_search_result: InsuredSearchResult
    ) -> None:
        self.log.info(f"Sending email to Paragon WC team about undetermined FEIN for submission {submission.name}")
        sub_name = insured_search_result.insured_requested_name or submission.name
        self._sendgrid_client.send_ims_wc_submission_undetermined_fein_notification(
            submission_name=sub_name,
            source_email_subject=sub_name,
            to_emails=[ParagonEmails.WORKERS_COMPENSATION.paragon_program_email()],
            bcc_email=ParagonEmails.kalepa_comms_out_group_email(),
        )

    def _is_submission_already_processed(
        self, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> bool:
        return (
            self.session.query(ParagonImsSubmissionCreationRequestQueue)
            .filter(
                ParagonImsSubmissionCreationRequestQueue.id != request.id,
                ParagonImsSubmissionCreationRequestQueue.submission_id == submission.id,
                ParagonImsSubmissionCreationRequestQueue.status != RequestStatus.PENDING,
            )
            .first()
            is not None
        )

    def _handle_submission_already_processed(
        self, dbg_slack_msg: str, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> None:
        self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Submission already processed - Skipping")

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: "This submission has already been processed",
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += "[COPILOT+] Submission already processed - Skipping\n"
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _handle_submission_already_having_ims_control_number(
        self, dbg_slack_msg: str, submission: Submission, request: ParagonImsSubmissionCreationRequestQueue
    ) -> None:
        self.log.info(
            "Submission already has IMS control number - Skipping",
            submission_client_ids=submission.client_submission_ids,
        )

        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: (
                    "This submission already had IMS control number"
                ),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += "[COPILOT+] Submission already has IMS control number - Skipping\n"
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _handle_submission_having_no_required_files(
        self, dbg_slack_msg: str, submission: Submission, request: ParagonImsSubmissionCreationRequestQueue
    ) -> None:
        self.log.info("Submission has no required files - Skipping")
        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: "Submission had no required files",
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += "[COPILOT+] Submission has no required files - Skipping\n"
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _should_skip_processing(
        self, dbg_slack_msg: str, request: ParagonImsSubmissionCreationRequestQueue, submission: Submission
    ) -> bool:
        # If Copilot submission already has IMS control number it means that we are tracking correlated IMS
        # quote. It can happen due to missing links daily sync (Paragon provided us their control number of quote
        # created manually). Copilot submission could also be force "reprocessed" which results in new
        # triaging and this handler being executed. Because IMS API does not allow any update operations we can
        # only skip.
        if submission.client_submission_ids:
            self._handle_submission_already_having_ims_control_number(dbg_slack_msg, submission, request)
            return True

        # Submission has no files apart from the ones created from email body == for Paragon this is a no-go
        if ParagonBusinessRules.submission_has_no_required_files(submission):
            self._handle_submission_having_no_required_files(dbg_slack_msg, submission, request)
            return True

        # Submissions that are copies of other submissions should be skipped - we don't want to create IMS duplicates
        # This is a precaution in case we have some bug with split and we verify newly split submission.
        if ParagonBusinessRules.is_copy(submission):
            self._handle_submission_being_a_copy(request)
            return True

        # Submissions that are sync in Paragon mean auto-declines aka dummy-sync-shell submissions.
        # They should never ever enter this handler because they are not triaged but better be safe than sorry.
        if ParagonBusinessRules.is_sync(submission):
            self._handle_submission_being_a_sync(request)
            return True

        if self._is_submission_already_processed(request, submission):
            self._handle_submission_already_processed(dbg_slack_msg, request, submission)
            return True

        return False

    def _get_report_source_email_account(self, submission: Submission) -> Optional[str]:
        report = self._copilot_client.get_report_by_id(submission.report_ids[0], expand="correspondence_id")
        email = self._copilot_client.get_email(correspondence_id=report.correspondence_id, prefer_root_email=True)
        if email and email.email_account:
            return email.email_account.lower()
        return None

    def _update_ims_extras(
        self,
        ims_control_numbers: dict,
        paragon_underwriter: ParagonUnderwriter,
        submission: Submission,
        slack_msg: str,
        request: ParagonImsSubmissionCreationRequestQueue,
    ) -> str:
        try:
            ua_guid = ParagonBusinessRules.get_guid_of_underwriter_assistant(
                paragon_underwriter.ims_underwriter_guid, request.additional_context
            )

            for ims_coverage, control_number in ims_control_numbers.items():
                self.log.info(
                    "Updating IMS extras",
                    ims_coverage=ims_coverage,
                    control_number=control_number,
                    ua_guid=ua_guid,
                )
                # Set Underwriter Assistant if there is a mapping for main Paragon Underwriter
                if ua_guid:
                    self._paragon_ims_client.update_underwriter_or_assistant(
                        control_number=control_number, underwriter_assistant_guid=ua_guid
                    )

                # Set Auditable
                is_auditable = ParagonBusinessRules.is_quote_auditable(ims_coverage, submission.primary_naics_code)
                self.log.info(
                    "Setting auditable", is_auditable=is_auditable, submission_naics=submission.primary_naics_code
                )
                self._paragon_ims_client.update_auditable(control_number=control_number, auditable=is_auditable)

                # Set Fixed Effective Date
                if ims_coverage == ImsCoverage.WORKERS_COMPENSATION:
                    # Set this to True at this point. However there is another process that executes
                    # after First Party Insights are finished. See paragon_ims_insights_update.py module in this
                    # project and GeneralLite queue handler.
                    self._paragon_ims_client.update_fixed_effective_date(
                        control_number=control_number, fixed_effective_date=True
                    )

                slack_msg += (
                    f"*Updated IMS extras for `{control_number}`; "
                    f"Underwriter Assistant: `{ua_guid}`; Auditable: `{is_auditable}`; "
                    "Fixed Effective Date (WC only): `True`\n"
                )
        except Exception as error:
            # This should not stop request processing because it's not critical.
            self.log.exception("Failed to update IMS extras")
            self._slack_client.send_slack_message(
                SLACK_DEBUG_CHANNEL,
                (
                    f"\nCC: <{PATRYK_LENZA_SLACK_ID}> - Failed to update IMS extras for submission"
                    f" `{submission.id}`; Error {error}.\n"
                ),
            )
        return slack_msg

    def _try_to_get_fni_address_from_chatgpt(self, insured_search_result: InsuredSearchResult) -> None:
        try:
            chat_prompt = create_chat_completion_prompt(insured_search_result.raw_address_string)
            response = get_llm_client().get_llm_response(
                params=[GPTRequestParams(model=LLMModel.OPENAI_GPT_4, max_tokens=256)],
                prompt=chat_prompt,
            )
            if response:
                self.log.info("Got FNI address from ChatGPT", response=response)
                ParagonBusinessRules.set_address_parts_from_openai_response(response, insured_search_result, self.log)
            else:
                self.log.info("Failed to get FNI address from ChatGPT", response=response)
        except Exception:
            self.log.exception("Exception when getting FNI address from ChatGPT in Paragon flow")

    def _handle_circuit_breaker_event(self, cb_event: ImsCircuitBreakerEvent, is_opened: bool) -> None:
        slack_msg = ParagonCircuitBreaker.create_slack_message_for_opening_and_closing_events(cb_event, is_opened)
        self._slack_client.send_slack_message(SLACK_COMPLETENESS_CHANNEL, slack_msg)

    def _handle_circuit_breaker_opened(self, cb_event: ImsCircuitBreakerEvent) -> None:
        self._handle_circuit_breaker_event(cb_event, is_opened=True)

    def _handle_circuit_breaker_closed(self, cb_event: ImsCircuitBreakerEvent) -> None:
        self._handle_circuit_breaker_event(cb_event, is_opened=False)

    def _possibly_add_submission_to_split(self, submission: Submission) -> None:
        liability_coverages_count = sum(
            1 for coverage in submission.coverages if coverage.coverage.name.lower() == CoverageName.Liability
        )

        # Only add to split if there are more than 1 liability coverages. Currently only Paragon E&S has this feat.
        if liability_coverages_count > 1:
            self.submissions_to_split[str(submission.id)] = liability_coverages_count

    def _handle_psp_e3_submission_blocked_broker_decline(
        self, submission: Submission, request: ParagonImsSubmissionCreationRequestQueue, dbg_slack_msg: str
    ):
        self.log.info("[PARAGON_IMS_QUEUE_HANDLER] Submission is for PSP E3 Property and broker is blocked - Skipping")
        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: (
                    "PSP E3 Property submissions with BLOCKED_BROKER triage status weren't created in the IMS at the"
                    " time this request came in"
                ),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += (
            "*[COPILOT+] Submission is for PSP E3 Property and has BLOCKED_BROKER triage result, skipping IMS"
            " creation*\n"
        )

        if submission.stage != SubmissionStage.DECLINED:
            try:
                self.log.info("Declining submission due to BLOCKED_BROKER triage status")
                self._copilot_client.decline_submission(submission_id=submission.id, decline_reason="Blocked Broker")
                dbg_slack_msg += "[COPILOT] Declined submission due to BLOCKED_BROKER triage status\n"
            except Exception:
                self.log.exception("Failed to decline submission due to BLOCKED_BROKER triage status")

        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _handle_psp_e3_unappointed_broker_decline(
        self, submission: Submission, request: ParagonImsSubmissionCreationRequestQueue, dbg_slack_msg: str
    ):
        self.log.info(
            "[PARAGON_IMS_QUEUE_HANDLER] Submission is for PSP E3 Property and it's missing broker UW data - skipping"
        )
        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: (
                    "PSP E3 Property submissions with UNAPPOINTED_BROKER triage status weren't created in the"
                    " IMS at the time this request came in"
                ),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += (
            "*[COPILOT+] Submission is for PSP E3 Property and has UNAPPOINTED_BROKER triage result, skipping"
            " IMS creation*\n"
        )

        if submission.stage != SubmissionStage.DECLINED:
            try:
                self.log.info("Declining submission due to UNAPPOINTED_BROKER triage status")
                self._copilot_client.decline_submission(
                    submission_id=submission.id, decline_reason="Not Appointed Broker"
                )
                dbg_slack_msg += "[COPILOT] Declined submission due to UNAPPOINTED_BROKER triage status\n"
            except Exception:
                self.log.exception("Failed to decline submission due to UNAPPOINTED_BROKER triage status")

        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _handle_psp_e3_submission_old_skip(
        self, submission: Submission, request: ParagonImsSubmissionCreationRequestQueue, dbg_slack_msg: str
    ):
        self.log.info(
            "[PARAGON_IMS_QUEUE_HANDLER] Submission is for PSP E3 Property and it's created before cutoff date -"
            " skipping"
        )
        self.session.query(ParagonImsSubmissionCreationRequestQueue).filter_by(id=request.id).update(
            {
                ParagonImsSubmissionCreationRequestQueue.error_details: (
                    "PSP E3 Property submission older than 2024-09-01 wasn't created in the"
                    " IMS at the time this request came in"
                ),
                ParagonImsSubmissionCreationRequestQueue.updated_at: request.updated_at,
            },
            synchronize_session=False,
        )
        self.session.commit()
        dbg_slack_msg += (
            f"*[COPILOT+] Submission is for PSP E3 Property and it was created ({submission.created_at}) before cutoff"
            " date - skipping IMS creation*\n"
        )
        self._send_slack_debug_message(request, submission, dbg_slack_msg)

    def _detect_and_handle_psp_e3_property_skip(
        self, submission: Submission, request: ParagonImsSubmissionCreationRequestQueue, dbg_slack_msg: str
    ) -> ActionResult | None:
        if submission.created_at < datetime(2024, 9, 1, tzinfo=timezone.utc):
            self._handle_psp_e3_submission_old_skip(submission, request, dbg_slack_msg)
            self._send_notification_to_copilot_api(ParagonNotificationType.SKIPPED_IMS_CREATION, request)
            return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True)

        triage_result = ParagonBusinessRules.get_triage_result(request.additional_context)
        match triage_result:
            case "BLOCKED_BROKER":
                self._handle_psp_e3_submission_blocked_broker_decline(submission, request, dbg_slack_msg)
                self._send_notification_to_copilot_api(ParagonNotificationType.SKIPPED_IMS_CREATION, request)
                return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True)
            case "UNAPPOINTED_BROKER":
                self._handle_psp_e3_unappointed_broker_decline(submission, request, dbg_slack_msg)
                self._send_notification_to_copilot_api(ParagonNotificationType.SKIPPED_IMS_CREATION, request)
                return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True)

        return None

    def _try_merge_with_existing_renewal(
        self, submission: Submission, control_numbers: list[int], dbg_slack_msg: str, is_declined_shell: bool
    ) -> tuple[bool, str]:
        if len(control_numbers) != 1:
            dbg_slack_msg += "[COPILOT+] Skipping merge with existing renewal - multiple control numbers found\n"
            self.log.warning("Skipping merge with existing renewal - multiple control numbers found")
            return False, dbg_slack_msg

        if is_declined_shell:
            dbg_slack_msg += (
                "[COPILOT+] Skipping merge with existing renewal - submission is declined shell and currently we don't"
                " support merging such submissions\n"
            )
            self.log.info("Skipping merge with existing renewal - submission is declined shell")
            return False, dbg_slack_msg

        control_number = str(control_numbers[0])
        existing_renewal_submissions: list[Submission] = self._copilot_client.get_submissions_by_client_ids(
            [control_number], ExistingOrganizations.Paragon.value
        )

        picked_renewal = ParagonBusinessRules.pick_copilot_renewal_submission(
            existing_renewal_submissions, control_number, submission.broker
        )
        if not picked_renewal:
            self.log.info("Skipping merge with existing renewal - none was found")
            dbg_slack_msg += "[COPILOT+] Skipping merge with existing renewal - no renewal found\n"
            return False, dbg_slack_msg

        try:
            self.log.info(
                "Merging with existing renewal",
                control_number=control_number,
                submission=submission,
                picked_renewal=picked_renewal,
            )
            self._copilot_client.merge_correspondence(
                from_report_id=submission.report_ids[0],
                to_report_id=picked_renewal.report_ids[0],
            )
            dbg_slack_msg += (
                "*[COPILOT+] Merged with existing renewal, existing renewal report_id is"
                f" `{picked_renewal.report_ids[0]}`*\n"
            )
            return True, dbg_slack_msg
        except Exception:
            self.log.exception("Merging correspondence failed")
            dbg_slack_msg += (
                "*[COPILOT+] Merging with existing renewal failed, existing renewal report_id is"
                f" `{picked_renewal.report_ids[0]}`*\n"
            )
            return False, dbg_slack_msg

    def _detect_and_handle_submission_to_be_deleted(
        self,
        submission: Submission,
        paragon_lob: ParagonLineOfBusiness,
        request: ParagonImsSubmissionCreationRequestQueue,
        slack_msg: str,
    ) -> ActionResult | None:
        if not paragon_lob.is_es():
            return None

        triage_result = ParagonBusinessRules.get_triage_result(request.additional_context)
        if triage_result == "EFFECTIVE_DATE_TOO_DISTANT":
            self.log.info("Submission is E&S and has EFFECTIVE_DATE_TOO_DISTANT triage result - deleting")

            # We need to first send the notification in order to trigger email sending and then
            # delete the report to get rid of all the data.
            self._send_notification_to_copilot_api(
                notification=ParagonNotificationType.SKIPPED_IMS_CREATION,
                request=request,
            )
            self._copilot_client.delete_report(report_id=request.report_id)

            self.log.info("Deleted report")

            slack_msg += (
                "*[COPILOT+] Submission is E&S and has EFFECTIVE_DATE_TOO_DISTANT triage result - deleting*\n"
                f"*[COPILOT+] Deleted report with ID `{request.report_id}`*\n"
            )

            self._send_slack_debug_message(request, submission, slack_msg)
            return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True)

        return None

    def _detect_and_handle_renewals(
        self,
        submission: Submission,
        paragon_lob: ParagonLineOfBusiness,
        insured_search_result: InsuredSearchResult,
        broker_search_result: BrokerSearchResults,
        policy_effective_date: datetime,
        request: ParagonImsSubmissionCreationRequestQueue,
        dbg_slack_msg: str,
        is_declined_shell: bool,
    ) -> tuple[ActionResult | None, ParagonRenewalDetectorResult]:
        # Logic here:
        # If we have found Insureds in IMS, either by FEIN or by name+address search, we will check if Paragon
        # already has submissions for this Insured. If they have no submissions we are sure this is NOT renewal.
        # If they have submissions we will check if same broker already has BOUND submission for the same LOB
        # for the previous year. If yes, this is renewal. If not it's NOT renewal.
        # If we have not found Insureds in IMS we will check if submission is renewal by Copilot's rules (simple
        # email parsing detector).
        # Additionally we will check if there already is IMS quote for the same coverage+broker+eff_date. If yes
        # it means it has been created in IMS and we can use its control number to automatically set in Copilot.
        # Finally we will update Copilot submission renewal status to match detected one if needed.
        # If we detect renewal we will skip IMS creation and send Slack message about it.
        # If we detect that user executed manual retry and no renewal should be detected we will skip
        # all renewals checks and proceed with IMS creation.

        detected_ims_renewal_control_numbers = {}
        skip_renewal_check = request.is_manual_retry_with_skip_renewal_detection()
        if skip_renewal_check:
            self.log.info("Manual retry for renewal, skipping renewal detector")
            return None, ParagonRenewalDetectorResult.create_empty_result()

        is_renewal = False
        lpr = "Renewal Detector -"
        if insured_search_result.found_insureds:
            self.log.info(f"{lpr} Found Insureds in IMS, will run clearance info check")
            first_insured = insured_search_result.found_insureds[0]
            clearance_info_list = self._paragon_ims_client.get_clearance_info_from_insured_guid(
                first_insured.insured_guid
            )
            self.log.info(
                f"{lpr} Got clearance info from IMS",
                insured_guid=first_insured.insured_guid,
                clearance_info_list=clearance_info_list if clearance_info_list else "No IMS clearance info",
            )

            if clearance_info_list:
                is_renewal = ParagonBusinessRules.check_if_broker_has_bound_sub_for_previous_year(
                    broker_search_result.producer_name,
                    paragon_lob,
                    submission.coverages,
                    policy_effective_date,
                    clearance_info_list,
                )
                self.log.info(f"{lpr} Checked in IMS if submission is a renewal", is_renewal=is_renewal)
                if is_renewal:
                    detected_ims_renewal_control_numbers = ParagonBusinessRules.detect_ims_control_numbers_for_renewal(
                        broker_search_result.producer_name,
                        paragon_lob,
                        submission.coverages,
                        policy_effective_date,
                        clearance_info_list,
                    )
            else:
                self.log.info(f"{lpr} No IMS clearance info means this is not a renewal")
        else:
            is_renewal = ParagonBusinessRules.is_renewal(submission)
            self.log.info(f"{lpr} No Insureds found in IMS, will use Copilot's renewal detector", is_renewal=is_renewal)

        self._update_copilot_sub_renewal_status_if_needed(submission, is_renewal)
        if is_renewal:
            was_merged, dbg_slack_msg = self._try_merge_with_existing_renewal(
                submission, list(detected_ims_renewal_control_numbers.values()), dbg_slack_msg, is_declined_shell
            )
            self._handle_renewals_not_supported(dbg_slack_msg, request, submission)
            detector_result = ParagonRenewalDetectorResult(
                detected_ims_renewal_control_numbers=detected_ims_renewal_control_numbers,
                was_merged_with_existing_renewal=was_merged,
            )

            return ActionResult(ActionStatus.COMPLETED, inc_attempt_count=True), detector_result

        return None, ParagonRenewalDetectorResult(
            detected_ims_renewal_control_numbers=detected_ims_renewal_control_numbers,
            was_merged_with_existing_renewal=False,
        )

    def _update_copilot_sub_renewal_status_if_needed(self, submission: Submission, is_renewal: bool) -> None:
        if ParagonBusinessRules.is_renewal(submission) != is_renewal:
            self.log.info("Updating Copilot submission renewal status", is_renewal=is_renewal)
            self._copilot_client.update_submission_is_renewal(submission.id, is_renewal)


def _try_to_send_unrecoverable_error_on_slack(error: Exception) -> None:
    slack_client = SlackClient(os.getenv("SLACK_TOKEN"))

    if env != "prod":
        return

    slack_client.send_slack_message(
        SLACK_COMPLETENESS_CHANNEL, f"Unrecoverable error while processing Paragon IMS creation request: {error}"
    )


def _try_to_send_ims_is_down_error_on_slack() -> None:
    slack_client = SlackClient(os.getenv("SLACK_TOKEN"))

    if env != "prod":
        return

    slack_client.send_slack_message(
        SLACK_DEBUG_CHANNEL,
        (
            f"\nCC: <{PATRYK_LENZA_SLACK_ID}> - IMS seems to be down, will retry in 5 minutes. See engineering logs for"
            " more details"
        ),
    )


def _send_sub_split_error_on_debug_slack(unprocessed_submission_ids: List[str]) -> None:
    if env != "prod":
        return

    slack_client = SlackClient(os.getenv("SLACK_TOKEN"))

    slack_client.send_slack_message(
        SLACK_DEBUG_CHANNEL,
        (
            f"\nCC: <{PATRYK_LENZA_SLACK_ID}> - Submissions with id `{unprocessed_submission_ids}` "
            "failed to split in Copilot API. Rerun split historical subs jobs to reprocess them.\n"
        ),
    )


def _send_sub_split_result_on_debug_slack(submission_id: str, result: Optional[Dict]) -> None:
    if env != "prod":
        return

    slack_client = SlackClient(os.getenv("SLACK_TOKEN"))

    slack_client.send_slack_message(
        SLACK_DEBUG_CHANNEL,
        40 * "=" + "\n" + f"Submission with id `{submission_id}` was split into {result}\n",
    )


def _split_submissions(log: BoundLogger, submissions_to_split: Dict[str, int]) -> None:
    # I want this function to be self-contained and deal with exceptions. It should not break the main
    # IMS creation flow. If we don't split submission immediately it won't be a big problem.
    if not submissions_to_split:
        log.info("No submissions to split")
        return

    log.info("Splitting submissions", submissions_to_split=submissions_to_split)
    failed_splits = []

    if not feature_flags_client.is_feature_enabled(FeatureFlags.ENABLE_PARAGON_SUBMISSION_BY_COVERAGE_SPLIT):
        log.info(
            "Feature flag for Paragon submission split is not enabled, skipping submission split",
            submissions_to_split=submissions_to_split,
        )
        return

    copilot_client = CopilotV3Client(os.environ.get("COPILOT_API_V3_URL"))

    for sub_id, expected_final_split_count in submissions_to_split.items():
        if expected_final_split_count <= 1:
            continue
        try:
            result = copilot_client.initiate_submission_split(submission_id=sub_id, async_exec=True)
            if not result.successfully_started:
                failed_splits.append(sub_id)
        except Exception:
            # I want warn here and error below when there is a list of all failed subs. They will need to be rerun.
            log.warn("Failed to split submission", submission_id=sub_id, exc_info=True)
            failed_splits.append(sub_id)

    if failed_splits:
        _send_sub_split_error_on_debug_slack(failed_splits)
        log.error(f"Failed Paragon submission splits: {failed_splits}")


def create_chat_completion_prompt(raw_address_str: str) -> ChatCompletionPrompt:
    instruction = (
        "You will be given a short text with US address. It can be PO box. "
        "I need you to extract following information: 1. zip code 2. city 3. state 4. address line 1 "
        "5. address line 2.  State needs to be 2 uppercase characters. If any of these are not present, "
        "please mark them as 'Not present'."
    )
    prompt_sequence = {
        "messages": [{"role": "system", "content": instruction}, {"role": "user", "content": raw_address_str}]
    }
    return ChatCompletionPromptSchema().load(prompt_sequence)


@bind_lambda_logging_context
def process_requests(event, context=None):
    log = get_logger()
    paragon_ims_client = None
    try:
        # Create IMS client and log in
        paragon_ims_client: ParagonIMSAPIClient = get_paragon_ims_client(log, login=True)
        if not paragon_ims_client:
            log.info("Paragon IMS API client is not enabled = skipping processing requests")
            return
        dummy_call_to_check_if_ims_api_is_up(paragon_ims_client)
    except Exception:
        log.info("IMS seems to be down, unable to create, login and verify Paragon IMS API client", exc_info=True)
        _try_to_send_ims_is_down_error_on_slack()
        return

    try:
        handler = ParagonImsSubmissionCreationRequestQueueHandler(paragon_ims_client)
        handler.handle()
        _split_submissions(log, handler.submissions_to_split)
    except Exception as error:
        log.exception("Failed to process Paragon IMS Submission Creation Request")
        _try_to_send_unrecoverable_error_on_slack(error)


# if __name__ == "__main__":
#     process_requests(None)
