from events_common.kalepa_event import <PERSON><PERSON>pa<PERSON><PERSON>
from events_common.kalepa_events import Ka<PERSON>paEvents
from events_common.lambdas.constants import IS_AUTO_PROCESSED_KEY
from infrastructure_common.logging import get_logger
from static_common.enums.file_processing_state import FileProcessingState

from infrastructure_events.handlers.base_handler import <PERSON><PERSON>and<PERSON>
from infrastructure_events.handlers.event_handlers.handler_factory import HandlerFactory

logger = get_logger()


@HandlerFactory.register(KalepaEvents.SUBMISSION_DATA_CONSOLIDATED)
class SubmissionDataConsolidatedHandler(BaseHandler):
    # Triggered once data is consolidated and ready for insights.

    def handle(self, event: KalepaEvent, **kwargs):
        additional_data = event.additional_data or {}
        if not additional_data.get(IS_AUTO_PROCESSED_KEY, True):
            return

        ff_enabled = additional_data.get("pds_customizable_classifiers_enabled", False)

        if ff_enabled:
            logger.info(
                "PDS file insights are enabled for submission, invoking insights", submission_id=event.submission_id
            )
            self.invoke_submission_insights(event=event)
        else:
            self.invoke_resolve_submission_file_entities(
                event=event,
                file_processing_state=FileProcessingState.DATA_CONSOLIDATED,
                finished_event_type=None,
                use_entity_mapped_data=False,
                target_file_processing_state=FileProcessingState.PROCESSED,
                file_finished_event_type=KalepaEvents.SUBMISSION_FILE_PROCESSING_FINISHED,
            )
            self.invoke_create_acord_sov_files(event)
