from dataclasses import dataclass, field
from datetime import datetime
from functools import cached_property
from typing import List, Optional
from uuid import UUID

import pytz
from common.utils.collections import filter_none
from copilot_client_v3 import SubmissionBusiness
from entity_resolution_service_client_v3 import Entity
from facts_client.model.classification_task import ClassificationTask
from facts_client.model.fact_subtype import FactSubtype
from facts_client.model.summary_config import SummaryConfig
from static_common.enums.execution import ExecutionEventType
from static_common.enums.fact_type import FactTypeID
from static_common.enums.metric import MetricType
from static_common.enums.parent import ParentType
from static_common.enums.units import Units

from src.models.constants import ALWAYS_MEAN_STRUCTURES_SUMMARY_FACT_SUBTPYE_IDS
from src.models.events import Error
from src.models.fact_summarization_wrapper import FactSummarizationWrapper
from src.utils.collections import (
    get_matching_fact_wrappers,
    get_structure_ids_from_fact_wrappers,
)

from .summaries.fact_summarization_result import FactSummarizationResult


@dataclass
class SummarizeOneFactResult:
    fact_subtype_id: str
    error: Optional[str] = None
    cause: Optional[str] = None


@dataclass
class SummarizeCustomMetricResult:
    custom_summary_config_id: str
    metric_created: bool = False
    error: Optional[str] = None
    cause: Optional[str] = None


@dataclass
class SummarizeAddressComponentsResult:
    county: bool = False
    state: bool = False
    zip_code: bool = False
    error: Optional[str] = None
    cause: Optional[str] = None


@dataclass
class SummarizeHUDLowIncomeUnitsFractionResult:
    error: Optional[str] = None
    cause: Optional[str] = None


@dataclass
class AggregateBatchResultsRequest:
    report_id: UUID
    organization_id: int
    submission_ids: List[UUID]
    coverages: List[str]
    business_ids: List[UUID]

    summarize_facts_results: Optional[List[FactSummarizationResult]] = None
    summarize_custom_metric_results: Optional[List[SummarizeCustomMetricResult]] = None

    execution_id: Optional[UUID] = None
    execution_started_at: datetime = field(default_factory=datetime.utcnow)

    lock_id: Optional[UUID] = field(compare=False, default=None)
    lock_acquired: Optional[bool] = field(compare=False, default=None)

    dossiers_key: Optional[str] = None
    event_type: Optional[ExecutionEventType] = None

    step_function_execution_id: Optional[str] = None
    error: Optional[Error] = None

    @property
    def lock_name(self) -> str:
        return f"{self.submission_ids[0]}-summarization"


@dataclass
class AggregateResultsRequest:
    report_id: UUID
    organization_id: int
    submission_ids: List[UUID]
    coverages: List[str]
    business_ids: List[UUID]

    summarize_facts_results: List[SummarizeOneFactResult]
    summarize_custom_metric_results: List[SummarizeCustomMetricResult]

    execution_id: Optional[UUID] = None
    execution_started_at: datetime = field(default_factory=datetime.utcnow)

    lock_id: Optional[UUID] = field(compare=False, default=None)
    lock_acquired: Optional[bool] = field(compare=False, default=None)

    dossiers_key: Optional[str] = None
    event_type: Optional[ExecutionEventType] = None

    @property
    def lock_name(self) -> str:
        return f"{self.report_id}-summarization"


@dataclass
class ExecutionInfo:
    execution_id: str
    execution_start_datetime: datetime
    execution_finish_datetime: datetime


@dataclass
class SummarizeAllFactsRequest:
    submission_ids: List[UUID]
    coverages: Optional[List[str]] = None
    report_id: Optional[UUID] = None
    organization_id: Optional[int] = None
    business_ids: Optional[List[UUID]] = None
    business_ids_excluded_for_property: Optional[List[UUID]] = None
    submission_businesses: Optional[List[SubmissionBusiness]] = None

    execution_id: Optional[UUID] = None
    execution_started_at: datetime = field(default_factory=datetime.utcnow)
    force: bool = False

    lock_id: Optional[UUID] = field(compare=False, default=None)
    lock_acquired: Optional[bool] = field(compare=False, default=None)

    dossiers_key: Optional[str] = None
    event_type: Optional[ExecutionEventType] = None
    naics_codes: Optional[List[str]] = None
    is_metrics_set_manually: Optional[bool] = False
    last_execution_info: Optional[ExecutionInfo] = None
    submission_frozen_as_of: Optional[datetime] = None
    entities: Optional[List[Entity]] = None

    # AWS stuff
    step_function_execution_id: Optional[str] = None

    @property
    def lock_name(self) -> str:
        return f"{self.submission_ids[0]}-summarization"

    @cached_property
    def submission_business_has_changed(self) -> bool:
        if not self.submission_businesses:
            return True

        last_updated_submission_business = max([sb.updated_at or sb.created_at for sb in self.submission_businesses])
        business_has_been_updated_since_last_execution = (
            not self.last_execution_info
        ) or self.last_execution_info.execution_start_datetime.replace(
            tzinfo=pytz.UTC
        ) <= last_updated_submission_business.replace(
            tzinfo=pytz.UTC
        )

        return business_has_been_updated_since_last_execution


@dataclass
class SummarizeOneFactRequest:
    report_id: UUID
    submission_ids: List[UUID]
    coverages: List[str]
    organization_id: int

    execution_id: UUID
    execution_started_at: datetime
    parent_type: ParentType

    fact_type_id: FactTypeID
    fact_subtype_id: str

    fact_summarization_wrappers: Optional[List[FactSummarizationWrapper]] = None

    summary_parent_id: Optional[UUID] = None
    summary_parent_type: Optional[ParentType] = None
    classification_task: Optional[ClassificationTask] = None
    fact_subtype: Optional[FactSubtype] = None
    summary_config: Optional[SummaryConfig] = None
    naics_codes: Optional[List[str]] = None
    is_metrics_set_manually: Optional[bool] = False
    structures_have_at_least_one_fact: Optional[bool] = False
    submission_frozen_as_of: Optional[datetime] = None

    @property
    def parent_ids(self) -> List[UUID]:
        if not self.fact_subtype:
            raise Exception("Trying to get all parent_ids without fact_subtype")
        all_discovered_in_set = set()
        all_relationships_in_set = set()
        for summary_config in self.fact_subtype.summary_configs:
            for discovered_in in summary_config.only_discovered_in or []:
                all_discovered_in_set.add(discovered_in)
            for relationship in summary_config.only_from_relationships or []:
                all_relationships_in_set.add(relationship)

        return filter_none(
            [
                fsw.parent_id
                for fsw in get_matching_fact_wrappers(
                    self.fact_summarization_wrappers, self.parent_type, all_discovered_in_set, all_relationships_in_set
                )
            ]
        )

    @property
    def structure_ids(self) -> List[UUID]:
        return get_structure_ids_from_fact_wrappers(self.fact_summarization_wrappers)

    @property
    def premises_ids(self) -> List[UUID]:
        return filter_none(
            list(
                {
                    wrapper.parent_id
                    for wrapper in self.fact_summarization_wrappers
                    if wrapper.parent_type == ParentType.PREMISES
                }
            )
        )

    @property
    def business_ids(self) -> List[UUID]:
        return filter_none(
            list(
                {
                    wrapper.business_id
                    for wrapper in self.fact_summarization_wrappers
                    if wrapper.parent_type == ParentType.BUSINESS
                }
            )
        )


@dataclass
class SummarizeOneFactRequestBatch:
    fact_summarization_batches: List[List[SummarizeOneFactRequest]]


class SummarizeStructuresRequest(SummarizeOneFactRequest):
    pass


class SummarizationRequest(SummarizeOneFactRequest):
    pass


class SummarizeOneFactRequestFactory:
    @classmethod
    def create_summarization_request(
        cls, request: SummarizeOneFactRequest, summary_config: SummaryConfig
    ) -> SummarizationRequest:
        fact_summarization_wrappers = get_matching_fact_wrappers(
            request.fact_summarization_wrappers,
            summary_config.parent_type,
            set(summary_config.only_discovered_in or []),
            set(summary_config.only_from_relationships or []),
        )
        return SummarizationRequest(
            summary_parent_type=ParentType.REPORT,
            summary_parent_id=request.report_id,
            report_id=request.report_id,
            submission_ids=request.submission_ids,
            organization_id=request.organization_id,
            execution_id=request.execution_id,
            execution_started_at=request.execution_started_at,
            parent_type=summary_config.parent_type,
            fact_summarization_wrappers=fact_summarization_wrappers,
            fact_type_id=request.fact_type_id,
            fact_subtype_id=request.fact_subtype_id,
            fact_subtype=request.fact_subtype,
            summary_config=summary_config,
            classification_task=request.classification_task,
            naics_codes=request.naics_codes,
            is_metrics_set_manually=request.is_metrics_set_manually,
            coverages=request.coverages,
            submission_frozen_as_of=request.submission_frozen_as_of,
        )

    @classmethod
    def create_summarize_structures_request(
        cls,
        request: SummarizeOneFactRequest,
        summary_config: SummaryConfig,
        business_id: Optional[UUID] = None,
        premises_id: Optional[UUID] = None,
    ) -> SummarizeStructuresRequest:
        fact_summarization_wrappers = [
            wrapper
            for wrapper in request.fact_summarization_wrappers
            if (
                (business_id and wrapper.business_id == business_id)
                or (premises_id and wrapper.premises_id == premises_id)
            )
            and wrapper.parent_type == ParentType.STRUCTURE
        ]
        matching_fact_summarization_wrappers = get_matching_fact_wrappers(
            fact_summarization_wrappers, None, set(summary_config.only_discovered_in or []), None
        )
        if request.fact_subtype_id in ALWAYS_MEAN_STRUCTURES_SUMMARY_FACT_SUBTPYE_IDS or (
            hasattr(summary_config, "units_label") and summary_config.units_label in {Units.PERCENTAGE, Units.YEARS}
        ):
            summary_config.summary_type_id = MetricType.MEAN.name
        elif summary_config.summary_type_id == MetricType.MEAN.name:
            summary_config.summary_type_id = MetricType.SUM.name  # For structures we always want to do SUM
        return SummarizeStructuresRequest(
            summary_parent_type=ParentType.BUSINESS if business_id else ParentType.PREMISES,
            summary_parent_id=business_id or premises_id,
            report_id=request.report_id,
            submission_ids=request.submission_ids,
            coverages=request.coverages,
            organization_id=request.organization_id,
            execution_id=request.execution_id,
            execution_started_at=request.execution_started_at,
            parent_type=ParentType.STRUCTURE,
            fact_summarization_wrappers=matching_fact_summarization_wrappers,
            fact_type_id=request.fact_type_id,
            fact_subtype_id=request.fact_subtype_id,
            fact_subtype=request.fact_subtype,
            summary_config=summary_config,
            classification_task=request.classification_task,
            naics_codes=request.naics_codes,
            submission_frozen_as_of=request.submission_frozen_as_of,
        )

    @classmethod
    def create(
        cls,
        summarization_invocation: SummarizeAllFactsRequest,
        fact_subtype: FactSubtype,
        naics_codes: List[str],
        is_metrics_set_manually: bool,
        structures_have_at_least_one_fact: bool,
        parent_type: ParentType,
    ) -> SummarizeOneFactRequest:
        return SummarizeOneFactRequest(
            report_id=summarization_invocation.report_id,
            execution_id=summarization_invocation.execution_id,
            execution_started_at=summarization_invocation.execution_started_at,
            parent_type=parent_type,
            fact_type_id=fact_subtype.fact_type_id,
            fact_subtype_id=fact_subtype.id,
            organization_id=summarization_invocation.organization_id,
            submission_ids=summarization_invocation.submission_ids,
            coverages=summarization_invocation.coverages,
            naics_codes=naics_codes,
            is_metrics_set_manually=is_metrics_set_manually,
            structures_have_at_least_one_fact=structures_have_at_least_one_fact,
            submission_frozen_as_of=summarization_invocation.submission_frozen_as_of,
        )
