from collections import defaultdict
from uuid import UUID

from copilot_client_v3.models import MetricPreference, MetricV2Request
from facts_client.model.relationships_observation import RelationshipsObservation
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.metric import MetricType
from static_common.enums.parent import ParentType
from structlog.stdlib import BoundLogger

from src.models.metrics import MetricContext
from src.models.requests import SummarizeAllFactsRequest
from src.models.summaries.custom import (
    CustomMetricSummarizationResult,
    CustomMetricSummarizationUnit,
    MetricPreferenceHandler,
)
from src.models.summaries.metrics import MetricV2RequestFactory
from src.models.types import MetricGroupName
from src.utils.ers import (
    get_business_id_to_premises_id_from_entities,
    get_most_suitable_premises,
    get_premises_id_to_entity_id_from_entities,
)
from src.utils.lazy_clients import get_copilot_v3_client, get_facts_client

ALLEGED_STRUCTURE_ID: str = "00000000-0000-0000-0000-000000000000"
METRIC_DISPLAY_NAME: str = "Structure Count"


def __get_structures_by_parent_id(
    relationship_observations: list[RelationshipsObservation],
) -> dict[str, list[str]]:
    parent_id_to_structure_ids: dict[str, list[str]] = defaultdict(list)
    for relationships in relationship_observations:
        for structure in relationships.children:
            parent_id_to_structure_ids[str(relationships.parent_id)].append(str(structure.remote_id))

    return parent_id_to_structure_ids


def __create_report_level_metric_context(
    request: SummarizeAllFactsRequest, summary_config_id: str, metric_display_name: str
) -> MetricContext:
    return MetricContext(
        report_id=request.report_id,  # type: ignore
        children_type=ParentType.PREMISES.value,
        summary_parent_id=request.report_id,  # type: ignore
        summary_parent_type=ParentType.REPORT,
        summary_config_id=summary_config_id,
        display_name=metric_display_name,
        only_discovered_in=None,
        metric_group_name=MetricGroupName.PREMISES.value,  # type: ignore
        only_from_relationships=None,
    )


def __create_report_level_metric(
    request: SummarizeAllFactsRequest,
    metric_context: MetricContext,
    premises_id_to_business_id: dict[UUID, UUID],
    parent_ids_to_structure_ids: dict[str, list[str]],
    all_premises_ids: list[UUID],
) -> MetricV2Request:
    parent_ids: list[str] = []
    parent_types: list[ParentType] = []
    business_ids: list[str | None] = []
    values: list[float] = []

    for premises_id in all_premises_ids:
        structure_ids: list[str] | None = parent_ids_to_structure_ids.get(str(premises_id), None)
        # TODO: this code never does checks for vacant land - to be fixed
        value_to_append = len(structure_ids) if structure_ids else 1  # We assume premise is structure
        parent_ids.append(str(premises_id))
        parent_types.append(ParentType.PREMISES.value)
        business_ids.append(str(premises_id_to_business_id.get(premises_id, None)))
        values.append(value_to_append)

    return MetricV2RequestFactory.create_custom(
        str(request.execution_id),
        MetricType.SUM.value,
        metric_context,
        value_parent_ids=parent_ids,
        value_parent_types=parent_types,
        value_business_ids=business_ids,
        float_values=values,
        units="structures",
    )


def __create_parent_level_metric_context(
    request: SummarizeAllFactsRequest,
    premises_id: str | None,
    business_id: str | None,
    submission_business_id: UUID | None,
    summary_config_id: str,
    metric_display_name: str,
) -> MetricContext:
    return MetricContext(
        report_id=request.report_id,  # type: ignore
        children_type=ParentType.STRUCTURE.value,
        summary_parent_id=UUID(premises_id or business_id),
        summary_parent_type=ParentType.PREMISES if premises_id else ParentType.BUSINESS,
        summary_config_id=summary_config_id,
        display_name=metric_display_name,
        only_discovered_in=None,
        metric_group_name=MetricGroupName.PREMISES.value,  # type: ignore
        only_from_relationships=None,
        submission_business_id=submission_business_id,
    )


def __create_parent_level_metric(
    request: SummarizeAllFactsRequest,
    premises_id: str | None,
    business_id: str | None,
    submission_business_id: UUID | None,
    summary_config_id: str,
    metric_display_name: str,
    structure_ids: list[str],
) -> MetricV2Request:
    metric_context: MetricContext = __create_parent_level_metric_context(
        request, premises_id, business_id, submission_business_id, summary_config_id, metric_display_name
    )

    parent_ids: list[str] = []
    parent_types: list[ParentType] = []
    business_ids: list[str | None] = []
    values: list[float] = []

    for structure_id in structure_ids:
        parent_ids.append(structure_id)
        parent_types.append(ParentType.STRUCTURE.value)
        business_ids.append(None)
        values.append(1.0)

    return MetricV2RequestFactory.create_custom(
        str(request.execution_id),
        MetricType.SUM.value,
        metric_context,
        value_parent_ids=parent_ids,
        value_parent_types=parent_types,
        value_business_ids=business_ids,
        float_values=values,
        units="structures",
    )


def __try_create_metric_requests_individually(
    request: SummarizeAllFactsRequest, metric_requests: list[MetricV2Request], log: BoundLogger
) -> bool:
    copilot_client = get_copilot_v3_client()
    successfully_created_one_metric: bool = False
    for metric_request in metric_requests:
        try:
            copilot_client.create_metric_v2(request.report_id, metric_request)  # type: ignore
            successfully_created_one_metric = True
        except Exception:
            log.exception("Failed to create metric in one by one mode", metric_request=metric_request)

    return successfully_created_one_metric


def __try_create_metric_requests_bulk(
    request: SummarizeAllFactsRequest, metric_requests: list[MetricV2Request], log: BoundLogger
) -> bool:
    copilot_client = get_copilot_v3_client()
    try:
        copilot_client.bulk_create_metric_v2(str(request.report_id), metric_requests)
        return True
    except Exception:
        log.exception("Failed to bulk create metric, falling back to one-by-one processing")
        return __try_create_metric_requests_individually(request, metric_requests, log)


def __get_parent_id_to_submission_business_id(
    request: SummarizeAllFactsRequest,
) -> dict[str, str]:
    parent_id_to_submission_business_id: dict[str, str] = {
        sb.business_id: sb.id for sb in request.submission_businesses  # type: ignore
    }

    for submission_business in request.submission_businesses:
        entity = next((e for e in (request.entities or []) if e.id == submission_business.business_id), None)
        if not entity:
            continue
        premise_id = get_most_suitable_premises(entity.premises)
        if premise_id:
            parent_id_to_submission_business_id[str(premise_id.premises_id)] = str(submission_business.id)

    return parent_id_to_submission_business_id


def summarize_structure_counts(
    request: SummarizeAllFactsRequest, summary_config_id: str, _: BoundLogger
) -> CustomMetricSummarizationResult | None:
    excluded_ids_for_property = [str(id) for id in request.business_ids_excluded_for_property]  # type: ignore
    parent_id_to_business_id: dict[UUID, UUID] = get_premises_id_to_entity_id_from_entities(
        request.entities, excluded_ids_for_property
    )
    business_id_to_premises_id: dict[str, str] = get_business_id_to_premises_id_from_entities(
        request.entities, excluded_ids_for_property
    )
    premises_ids: list[UUID] = list(parent_id_to_business_id.keys())
    business_ids: list[UUID] = list([UUID(id) for id in business_id_to_premises_id.keys()])
    premises_id_to_submission_business_id: dict[str, str] = __get_parent_id_to_submission_business_id(request)

    business_relationship_facts = get_facts_client().get_facts(
        parent_ids=business_ids,
        parent_type=ParentType.BUSINESS,
        fact_subtype_id=FactSubtypeID.STRUCTURES.value,
        organization_id=request.organization_id,
        submission_id=request.submission_ids[0],
        as_of=request.submission_frozen_as_of,
    )

    premises_relationship_facts = get_facts_client().get_facts(
        parent_ids=premises_ids,
        parent_type=ParentType.PREMISES,
        fact_subtype_id=FactSubtypeID.STRUCTURES.value,
        organization_id=request.organization_id,
        submission_id=request.submission_ids[0],
        as_of=request.submission_frozen_as_of,
    )

    relationship_observations: list[RelationshipsObservation] = [
        fact.observation for fact in business_relationship_facts + premises_relationship_facts
    ]

    parent_ids_to_structure_ids = __get_structures_by_parent_id(relationship_observations)

    report_level_metric_context: MetricContext = __create_report_level_metric_context(
        request, summary_config_id, METRIC_DISPLAY_NAME
    )

    report_level_metric: MetricV2Request = __create_report_level_metric(
        request,
        report_level_metric_context,
        parent_id_to_business_id,
        parent_ids_to_structure_ids,
        premises_ids,
    )
    parent_level_metrics: list[MetricV2Request] = []
    for parent_id in business_ids + premises_ids:
        is_premise = parent_id in premises_ids
        structure_ids: list[str] | None = parent_ids_to_structure_ids.get(str(parent_id), None)
        submission_business_id: str | None = premises_id_to_submission_business_id.get(str(parent_id), None)

        if not structure_ids:
            continue

        parent_level_metric: MetricV2Request = __create_parent_level_metric(
            request=request,
            premises_id=str(parent_id) if is_premise else None,
            business_id=str(parent_id) if not is_premise else None,
            submission_business_id=submission_business_id,
            summary_config_id=summary_config_id,
            metric_display_name=METRIC_DISPLAY_NAME,
            structure_ids=structure_ids,
        )
        parent_level_metrics.append(parent_level_metric)

    existing_metric_preference: MetricPreference | None = get_copilot_v3_client().get_metric_preference(
        request.report_id,  # type: ignore
        request.report_id,  # type: ignore
        ParentType.REPORT,
        summary_config_id,
    )

    metric_preference_handler = MetricPreferenceHandler(
        metric_context=report_level_metric_context,
        metric_name=METRIC_DISPLAY_NAME,
        metric_preference=existing_metric_preference,
        is_submitted_data_preference=False,
        fact_subtype_id=FactSubtypeID.STRUCTURES_COUNT.value,
        require_metric_created=True,
    )

    summarization_unit = CustomMetricSummarizationUnit(
        metric_requests=[report_level_metric, *parent_level_metrics],
        metric_preference_handler=metric_preference_handler,
    )

    return CustomMetricSummarizationResult(summarization_units=[summarization_unit])
