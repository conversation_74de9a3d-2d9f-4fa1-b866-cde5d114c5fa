import os
import uuid
from collections import defaultdict
from datetime import datetime
from typing import Any, Dict, Iterable, List, Optional
from uuid import UUID

import pytz
import redis
import sentry_sdk
from common.clients.cache import RedisCacheClient
from common.clients.copilot_v1 import CopilotV1Client
from common.clients.utils import chunks
from copilot_client_v3 import (
    MetricPreference,
    MetricV2Request,
    Submission,
    SubmissionBusiness,
)
from entity_resolution_service_client_v3 import Entity
from facts_client.model.fact import Fact
from facts_client.model.fact_subtype import FactSubtype
from facts_client.model.relationship import Relationship
from facts_client.model.relationships_observation import RelationshipsObservation
from facts_client.model.summary_config import SummaryConfig
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from sentry_sdk.integrations.serverless import serverless_function
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID
from static_common.enums.parent import ParentType
from static_common.enums.source_types import SourceTypeID

from src.clients.copilot_v3 import <PERSON><PERSON>lot<PERSON>3<PERSON>lient
from src.clients.ers_v3 import ERSV3Client
from src.clients.facts import FactsClient
from src.clients.facts_v2 import FactsClientV2
from src.clients.redis import (
    CopilotSubmissionRedisClient,
    FactSummarizationMetadataRedisClient,
)
from src.init.params import Params, load_and_cache_params
from src.logic.enable_rules import is_default_metric_preference
from src.logic.sources import summarize_fact_sources
from src.logic.summarizers import SUMMARY_TYPE_ID_SUMMARIZER
from src.models.fact_summarization_wrapper import (
    FactSummarizationMetadata,
    FactSummarizationWrapper,
    FactSummarizationWrapperFactory,
)
from src.models.metrics import MetricContext, MetricPreferenceKey
from src.models.requests import (
    SummarizationRequest,
    SummarizeAllFactsRequest,
    SummarizeOneFactRequest,
    SummarizeOneFactRequestBatch,
    SummarizeOneFactRequestFactory,
)
from src.models.summaries.errors import NoValuesException
from src.models.summaries.fact_summarization_result import (
    FactSummarizationResult,
    OneFactSummarizationResult,
)
from src.schemas.requests import (
    SummarizeAllFactsRequestSchema,
    SummarizeOneFactRequestBatchSchema,
    SummarizeOneFactRequestSchema,
)
from src.schemas.summaries.fact_summarization_result import (
    FactSummarizationResultSchema,
)
from src.utils.collections import (
    deduplicate_fact_wrappers,
    get_structure_ids_from_fact_wrappers,
)
from src.utils.ers import (
    get_most_suitable_premises,
    get_premises_id_to_entity_id_from_entities,
    get_premises_ids_from_entities,
)
from src.utils.lazy_clients import get_fact_summarization_storage_object_redis_client
from src.utils.logging import log_function_inputs
from src.utils.mapping import get_metric_group_name
from src.utils.metric_request_storage import (
    FactSummarizationStorage,
    FactSummarizationStorageObject,
    StructureSummaryStorageObject,
)
from src.utils.relationship_children_priority import get_first_party_source_priority

utc = pytz.UTC
logger = get_logger()

load_and_cache_params()

sentry_sdk.init(
    os.environ.get("SUMMARIZATION_SENTRY_DSN"),
    environment=os.environ.get("KALEPA_ENV", "dev"),
    integrations=[AwsLambdaIntegration(timeout_warning=True)],
)

summarize_all_facts_request_schema = SummarizeAllFactsRequestSchema()
summarize_one_fact_request_schema = SummarizeOneFactRequestSchema()
summarize_one_fact_request_batch_schema = SummarizeOneFactRequestBatchSchema()
fact_summarization_result_schema = FactSummarizationResultSchema()

redis_cache = redis.Redis(
    host=Params.REDIS_CACHE_HOSTNAME.get(),
    port=Params.REDIS_CACHE_PORT.get(),
    health_check_interval=30,  # seconds
)

cache_client = RedisCacheClient(redis_cache)

fact_summarization_metadata_redis_client = FactSummarizationMetadataRedisClient(cache_client)
submission_redis_client = CopilotSubmissionRedisClient(cache_client)


facts_client = FactsClient(
    os.environ["FACTS_API_URL"], proxy_url=os.environ.get("PROXY_URL"), cache_client=cache_client
)
facts_v2_client = FactsClientV2(os.environ["FACTS_API_V2_URL"], proxy_url=os.environ.get("PROXY_URL"))

copilot_client = CopilotV3Client(os.environ["COPILOT_API_V3_URL"], proxy_url=os.environ.get("PROXY_URL"))
copilot_v1_client = CopilotV1Client(
    base_url=os.environ["COPILOT_API_V3_URL"].replace("v3.0", "v1.0"), cache_client=cache_client
)

ers_client = ERSV3Client(os.environ["ENTITY_RESOLUTION_SERVICE_V3_URL"], proxy_url=os.environ.get("PROXY_URL"))

fact_wrapper_factory = FactSummarizationWrapperFactory()
logged_first_info_log = False

METRICS_V2_EPOCH = datetime(year=2023, month=11, day=15)

# Some RELATIONSHIPS should not be taken directly from BUSINESS because we keep track of more accurate information
# on submission level
BANNED_PER_BUSINESS_RELATIONSHIPS: List[FactSubtypeID] = [
    FactSubtypeID.VEHICLES,
    FactSubtypeID.TRAILERS,
    FactSubtypeID.DRIVERS,
]


def __get_fact_wrappers_from_relationship_observation(
    parent_id: UUID,
    parent_type: ParentType,
    relationships: Dict[str, List[RelationshipsObservation]],
    submission: Submission,
    submission_business_id: Optional[UUID] = None,
) -> List[FactSummarizationWrapper]:
    fact_wrappers = []
    relationship_observations = relationships.get(str(parent_id))
    if relationship_observations:
        valid_relationships = []
        for relationship_observation in relationship_observations:
            if (
                relationship_observation.parent_type != ParentType.BUSINESS
                or relationship_observation.fact_subtype_id not in BANNED_PER_BUSINESS_RELATIONSHIPS
            ):
                valid_relationships.append(relationship_observation)

        children_to_check_sources: List[Relationship] = []
        for relationship_observation in relationship_observations:
            if relationship_observation.children_type == "VEHICLE":
                children_to_check_sources.extend(relationship_observation.children)

        child_priorities = get_first_party_source_priority(
            copilot_client, facts_client, children_to_check_sources, submission
        )
        min_priority = min([value[0] for value in child_priorities.values()], default=None)

        for relationship_observation in valid_relationships:
            for child in relationship_observation.children:
                if child.discovered_in and "PROMETRIX" in child.discovered_in:
                    continue
                is_priority_first_party_data_in_relationship = (
                    relationship_observation.children_type != "VEHICLE"
                    or min_priority is None
                    or min_priority == child_priorities.get(child.remote_id, (100, []))[0]
                )

                wrapper = fact_wrapper_factory.create(
                    parent_id=child.remote_id,
                    parent_type=relationship_observation.children_type,
                    discovered_in=child.discovered_in,
                    relationships=[relationship_observation.fact_subtype_id],
                    is_priority_first_party_data_in_relationship=is_priority_first_party_data_in_relationship,
                )
                wrapper.submission_business_id = submission_business_id
                wrapper.business_id = parent_id if parent_type == ParentType.BUSINESS else None
                wrapper.premises_id = parent_id if parent_type == ParentType.PREMISES else None
                fact_wrappers.append(wrapper)
    return fact_wrappers


def __get_fact_wrappers_from_entities(
    entities: List[Entity], submission: Submission, summarize_all_facts_request: SummarizeAllFactsRequest
) -> List[FactSummarizationWrapper]:
    submission_id: UUID = UUID(submission.id)
    entity_id_to_submission_business_id: Dict[str, SubmissionBusiness] = {
        sb.business_id: sb.id for sb in submission.businesses
    }
    fact_wrappers: List[FactSummarizationWrapper] = []

    organization_id = summarize_all_facts_request.organization_id
    excluded_ids_for_property = [str(id) for id in summarize_all_facts_request.business_ids_excluded_for_property]
    submission_ids = [str(submission_id)]
    premises_ids = get_premises_ids_from_entities(entities, excluded_ids_for_property)
    premise_id_to_business_id = get_premises_id_to_entity_id_from_entities(entities, excluded_ids_for_property)
    business_id_to_premise_id = {
        str(business_id): str(premise_id) for premise_id, business_id in premise_id_to_business_id.items()
    }

    entity_ids = list({str(e.id) for e in entities})
    relationship_per_business = facts_client.get_relationship_observations(
        entity_ids,
        ParentType.BUSINESS,
        organization_id,
        submission_id,
        as_of=summarize_all_facts_request.submission_frozen_as_of,
    )
    relationship_per_premises = (
        facts_client.get_relationship_observations(
            premises_ids,
            ParentType.PREMISES,
            organization_id,
            submission_id,
            as_of=summarize_all_facts_request.submission_frozen_as_of,
        )
        if premises_ids
        else {}
    )
    relationship_per_submission = facts_client.get_relationship_observations(
        submission_ids,
        ParentType.SUBMISSION,
        organization_id,
        submission_id,
        as_of=summarize_all_facts_request.submission_frozen_as_of,
    )

    __filter_out_premise_structures(relationship_per_business, relationship_per_premises, business_id_to_premise_id)

    for entity in entities:
        if entity is None:
            continue
        submission_business_id = entity_id_to_submission_business_id.get(str(entity.id))
        premise = None if entity.id in excluded_ids_for_property else get_most_suitable_premises(entity.premises)
        fact_wrappers.append(
            fact_wrapper_factory.create(
                business_id=UUID(entity.id),
                parent_id=UUID(entity.id),
                parent_type=ParentType.BUSINESS.value,
                premises_id=UUID(premise.premises_id) if premise else None,
            )
        )
        if premise:
            fact_wrappers.append(
                fact_wrapper_factory.create(
                    business_id=UUID(entity.id),
                    parent_id=UUID(premise.premises_id),
                    parent_type=ParentType.PREMISES.value,
                    premises_id=UUID(premise.premises_id),
                )
            )
        fact_wrappers.extend(
            __get_fact_wrappers_from_relationship_observation(
                UUID(entity.id),
                ParentType.BUSINESS,
                relationship_per_business,
                submission,
                submission_business_id=submission_business_id,
            )
        )
        if premise:
            fact_wrappers.extend(
                __get_fact_wrappers_from_relationship_observation(
                    UUID(premise.premises_id),
                    ParentType.PREMISES,
                    relationship_per_premises,
                    submission,
                    submission_business_id=submission_business_id,
                )
            )

    fact_wrappers.extend(
        __get_fact_wrappers_from_relationship_observation(
            submission_id, ParentType.SUBMISSION, relationship_per_submission, submission
        )
    )

    return deduplicate_fact_wrappers(fact_wrappers)


def __filter_out_premise_structures(
    business_relationships: Dict[str, List[RelationshipsObservation]],
    premise_relationships: Dict[str, List[RelationshipsObservation]],
    business_id_to_premise_id: Dict[str, str],
) -> None:
    # Structures relationship can be saved both, against business and premise
    # we assume that business is more accurate, so we remove the one against premise
    # We are levareging the fact that lists inside the dict are mutable
    for business_id, relationships in business_relationships.items():
        for relationship_observation in relationships:
            if relationship_observation.fact_subtype_id == FactSubtypeID.STRUCTURES:
                premise_id = business_id_to_premise_id.get(business_id)
                if not premise_id:
                    continue
                premise_rels = premise_relationships.get(premise_id, None)
                if premise_rels:
                    for premise_relationship_observation in premise_rels:
                        if premise_relationship_observation.fact_subtype_id == FactSubtypeID.STRUCTURES:
                            premise_rels.remove(premise_relationship_observation)
                            break


def __get_fact_subtypes_by_id(
    summarize_all_facts_request: SummarizeAllFactsRequest,
    submission: Submission,
    entities: List[Entity],
    organization_id: int,
) -> Dict[str, FactSubtype]:
    execution_start_datetime: Optional[datetime] = (
        summarize_all_facts_request.last_execution_info.execution_start_datetime
        if not summarize_all_facts_request.submission_business_has_changed
        else None
    )
    premises_ids: List[UUID] = []
    for entity in entities:
        premise = get_most_suitable_premises(entity.premises)
        if premise:
            premises_ids.append(UUID(premise.premises_id))
    fact_subtypes: List[FactSubtype] = facts_v2_client.get_latest_fact_subtypes(
        business_ids=[UUID(entity.id) for entity in entities],
        premises_ids=premises_ids,
        last_summarization_datetime=execution_start_datetime,
        submission_frozen_as_of=summarize_all_facts_request.submission_frozen_as_of,
        submission_id=UUID(submission.id),
        organization_id=organization_id,
    )

    return {subtype.id: subtype for subtype in fact_subtypes}


def __get_fact_counts_by_id_for_structures(
    structure_ids: List[UUID],
    fact_subtype_ids: List[str],
    organization_id: int,
    submission_id: str,
    as_of: Optional[datetime],
) -> Dict[str, int]:
    if len(structure_ids) == 0 or len(fact_subtype_ids) == 0:
        return {}

    fact_count_by_fact_subtype_id = facts_client.get_fact_count_by_fact_subtype_id(
        parent_ids=structure_ids,
        fact_subtype_ids=fact_subtype_ids,
        parent_types=[ParentType.STRUCTURE],
        organization_id=organization_id,
        submission_id=str(submission_id),
        as_of=as_of,
    )

    return {str(key[0]): count for key, count in fact_count_by_fact_subtype_id.items()}


def __create_batch(
    requests: List[SummarizeOneFactRequest], parent_id_count: int
) -> List[List[SummarizeOneFactRequest]]:
    if parent_id_count <= 10:
        batch_size = 20
    elif parent_id_count <= 20:
        batch_size = 12
    elif parent_id_count <= 30:
        batch_size = 8
    elif parent_id_count <= 50:
        batch_size = 5
    elif parent_id_count <= 100:
        batch_size = 3
    elif parent_id_count <= 200:
        batch_size = 2
    else:
        batch_size = 1

    return [list(x) for x in chunks(requests, batch_size)]


def __batch_summarize_one_fact_requests(
    requests: Iterable[SummarizeOneFactRequest], fact_wrappers: List[FactSummarizationWrapper]
) -> SummarizeOneFactRequestBatch:
    parent_type_to_parent_id_count: Dict[str, int] = defaultdict(int)
    for fact_wrapper in fact_wrappers:
        parent_type_to_parent_id_count[fact_wrapper.parent_type] += 1

    parent_type_to_summarize_requests: Dict[str, List[SummarizeOneFactRequest]] = defaultdict(list)
    for summarize_request in requests:
        parent_type_to_summarize_requests[summarize_request.parent_type].append(summarize_request)

    batches: List[List[SummarizeOneFactRequest]] = []
    for parent_type, requests in parent_type_to_summarize_requests.items():
        parent_id_count = parent_type_to_parent_id_count[parent_type]
        batches.extend(__create_batch(requests, parent_id_count))

    return SummarizeOneFactRequestBatch(fact_summarization_batches=batches)


# noinspection PyUnusedLocal
@bind_lambda_logging_context
@log_function_inputs
@serverless_function
def create_summarize_one_fact_request_batches(event, context=None):
    summarize_all_facts_request: SummarizeAllFactsRequest = summarize_all_facts_request_schema.load(event)
    if len(summarize_all_facts_request.submission_ids) != 1:
        raise Exception(f"Expected exactly one submission_id for report {summarize_all_facts_request.report_id}")

    submission_id = summarize_all_facts_request.submission_ids[0]
    submission = submission_redis_client.get_submission(submission_id, summarize_all_facts_request.execution_id)
    if not submission:
        logger.warning(
            "Submission cache hit was a miss",
            submission_id=submission_id,
            execution_id=summarize_all_facts_request.execution_id,
        )
        submission = copilot_client.get_submission(
            submission_id, expand=["businesses", "is_metrics_set_manually", "coverages", "report_is_deleted", "files"]
        )

    if submission.report_is_deleted:
        logger.info(
            "No summaries are required because report_is_deleted",
            report_id=summarize_all_facts_request.report_id,
            execution_id=summarize_all_facts_request.execution_id,
        )
        return summarize_one_fact_request_batch_schema.dump(SummarizeOneFactRequestBatch(fact_summarization_batches=[]))

    summarize_all_facts_request.submission_businesses = submission.businesses

    organization_id: int = copilot_v1_client.get_user_org_id(id=submission.owner_id)
    entities: List[Entity] = ers_client.get_entities(summarize_all_facts_request.business_ids)
    fact_wrappers: List[FactSummarizationWrapper] = __get_fact_wrappers_from_entities(
        entities, submission, summarize_all_facts_request
    )
    fact_summarization_metadata_redis_client.put_fact_summarization_metadata(
        summarize_all_facts_request.execution_id, FactSummarizationMetadata(fact_wrappers)
    )

    fact_subtypes_by_id: Dict[str, FactSubtype] = __get_fact_subtypes_by_id(
        summarize_all_facts_request, submission, entities, organization_id
    )
    structure_ids: List[UUID] = get_structure_ids_from_fact_wrappers(fact_wrappers)
    fact_counts_for_structures: Dict[str, int] = __get_fact_counts_by_id_for_structures(
        structure_ids=structure_ids,
        fact_subtype_ids=list(fact_subtypes_by_id),
        organization_id=organization_id,
        submission_id=submission_id,
        as_of=summarize_all_facts_request.submission_frozen_as_of,
    )

    existing_metric_preferences: List[MetricPreference] = copilot_client.get_all_metric_preferences_for_report(
        report_id=summarize_all_facts_request.report_id
    )
    existing_metric_preferences: Dict[MetricPreferenceKey, MetricPreference] = {
        MetricPreferenceKey(m.parent_id, m.parent_type, m.summary_config_id): m for m in existing_metric_preferences
    }

    naics_codes: List[str] = (
        []
        if submission.is_metrics_set_manually
        else facts_client.get_naics_codes(
            submission_id, organization_id, as_of=summarize_all_facts_request.submission_frozen_as_of
        )
    )

    cache_key_values = []
    summarize_one_fact_requests: List[SummarizeOneFactRequest] = []
    for fact_subtype in fact_subtypes_by_id.values():
        structures_have_at_least_one_fact: bool = fact_counts_for_structures.get(fact_subtype.id, 0) > 0
        parent_types = {ParentType(x.parent_type) for x in fact_subtype.summary_configs}
        for parent_type in parent_types:
            request = SummarizeOneFactRequestFactory.create(
                summarization_invocation=summarize_all_facts_request,
                fact_subtype=fact_subtype,
                naics_codes=naics_codes,
                is_metrics_set_manually=(
                    submission.is_metrics_set_manually if submission.is_metrics_set_manually is not None else False
                ),
                structures_have_at_least_one_fact=structures_have_at_least_one_fact,
                parent_type=parent_type,
            )

            metric_preferences_for_request: List[MetricPreference] = __get_or_create_metric_preferences_for_request(
                fact_subtype,
                request,
                existing_metric_preferences,
            )

            if any(metric_preferences_for_request):
                summarize_one_fact_requests.append(request)
                for metric_preference in metric_preferences_for_request:
                    cache_key: str = __get_metric_preference_cache_key(
                        request.execution_id, metric_preference.summary_config_id, metric_preference.parent_id
                    )
                    cache_key_values.append((cache_key, metric_preference))

    summarize_one_fact_request_batch = __batch_summarize_one_fact_requests(summarize_one_fact_requests, fact_wrappers)
    cache_client.add_to_cache_bulk(cache_key_values, expiration_seconds=3600)

    return summarize_one_fact_request_batch_schema.dump(summarize_one_fact_request_batch)


def __requires_structure_metric_summarization(
    request_parent_type: ParentType, structures_have_at_least_one_fact: bool
) -> bool:
    return request_parent_type in {ParentType.PREMISES, ParentType.BUSINESS} and structures_have_at_least_one_fact


def __get_or_create_metric_preference_with_metric_context(
    metric_context: MetricContext,
    request: SummarizeOneFactRequest,
    existing_metric_preferences: Dict[MetricPreferenceKey, MetricPreference],
) -> Optional[MetricPreference]:
    key = MetricPreferenceKey(
        str(metric_context.summary_parent_id), metric_context.summary_parent_type, metric_context.summary_config_id
    )
    existing_metric_preference: Optional[MetricPreference] = existing_metric_preferences.get(key)
    if existing_metric_preference:
        return existing_metric_preference

    # We initialize this as False to prevent the need to retrieve facts outside SummarizeOneFact
    is_submitted_data_preference: bool = False
    # This is always set to False because applicability is handled in CAPI
    is_applicable: bool = False
    is_enabled_by_default: bool = is_default_metric_preference(
        metric_context.display_name,
        request.coverages,
        request.naics_codes,
        request.is_metrics_set_manually,
        organization_id=None,
        fact_subtype=request.fact_subtype,
        summary_config_id=metric_context.summary_config_id,
    )
    metric_preference: Optional[MetricPreference] = copilot_client.create_metric_preference(
        metric_context, is_enabled_by_default, is_submitted_data_preference, is_applicable
    )
    if not metric_preference:
        logger.warning(
            "Did not get or create metric preference",
            metric_context=metric_context.display_name,
            report_id=request.report_id,
        )
    return metric_preference


def __get_or_create_metric_preferences_for_request(
    fact_subtype: FactSubtype,
    request: SummarizeOneFactRequest,
    existing_metric_preferences: Dict[MetricPreferenceKey, MetricPreference],
) -> List[MetricPreference]:
    metric_preferences_for_request: List[MetricPreference] = []
    for summary_config in [x for x in fact_subtype.summary_configs if x.parent_type == request.parent_type]:
        metric_context: MetricContext = MetricContext(
            report_id=request.report_id,
            children_type=summary_config.parent_type,
            summary_parent_id=request.report_id,
            summary_parent_type=ParentType.REPORT,
            summary_config_id=summary_config.id,
            display_name=summary_config.display_name,
            metric_group_name=get_metric_group_name(summary_config, fact_subtype, request.coverages),
            is_structure_summary_config=False,
            only_discovered_in=summary_config.only_discovered_in,
            only_from_relationships=summary_config.only_from_relationships,
        )
        metric_preference: Optional[MetricPreference] = __get_or_create_metric_preference_with_metric_context(
            metric_context, request, existing_metric_preferences
        )
        if metric_preference:
            metric_preferences_for_request.append(metric_preference)

    return metric_preferences_for_request


def has_at_least_one_first_party_observation(request: SummarizeOneFactRequest) -> bool:
    for fact_wrapper in request.fact_summarization_wrappers:
        fact: Optional[Fact] = fact_wrapper.fact
        if fact and fact.observation.source.source_type_id == SourceTypeID.FIRST_PARTY:
            return True
    return False


def summarize_one_fact_with_summary_config(
    request: SummarizeOneFactRequest,
    submission_business_id: Optional[UUID] = None,
    metric_storage_object: Optional[FactSummarizationStorageObject | StructureSummaryStorageObject] = None,
) -> Optional[MetricV2Request]:
    is_structure_summary = True if submission_business_id else False
    is_single_structure_summary = is_structure_summary and len(request.fact_summarization_wrappers) <= 1

    metric_context = MetricContext(
        report_id=request.report_id,
        children_type=request.summary_config.parent_type,
        summary_parent_id=request.summary_parent_id,
        summary_parent_type=request.summary_parent_type,
        summary_config_id=request.summary_config.id,
        display_name=request.summary_config.display_name,
        metric_group_name=get_metric_group_name(request.summary_config, request.fact_subtype, request.coverages),
        is_structure_summary_config=is_structure_summary,
        only_discovered_in=request.summary_config.only_discovered_in,
        only_from_relationships=request.summary_config.only_from_relationships,
        submission_business_id=submission_business_id,
    )
    try:
        logger.debug("Summarizing request", request=request)
        summarizer = SUMMARY_TYPE_ID_SUMMARIZER[request.summary_config.summary_type_id]
        original_summary_config = request.summary_config
        request.summary_config = original_summary_config

        metric_v2_request: MetricV2Request = summarizer.summarize_v2(request, metric_context)
        metric_v2_request.sources = summarize_fact_sources(request)
        if not is_single_structure_summary:
            if metric_storage_object is not None:
                metric_storage_object.register_request(metric_v2_request)
            else:
                copilot_client.create_metric_v2(report_id=request.report_id, metric_v2_request=metric_v2_request)

        return metric_v2_request
    except NoValuesException:
        return None


def __has_no_structure_data(structure_request) -> bool:
    if not structure_request.fact_summarization_wrappers:
        return True

    num_structures_with_facts = [w for w in structure_request.fact_summarization_wrappers if w.fact]
    return len(num_structures_with_facts) == 0


def summarize_structures(
    request: SummarizeOneFactRequest,
    business_level_request: SummarizeOneFactRequest,
    summary_config: SummaryConfig,
    metric_storage_object: Optional[StructureSummaryStorageObject] = None,
) -> List[FactSummarizationWrapper]:
    # Copilot API now handles premise vs structure logic and expects metrics to be in the follow format:
    # [parent_1, structure_1_for_parent_1, structure_2_for_parent_1, parent_2, parent_3, structure_1_for_parent_3]
    wrappers_for_report_metric: List[FactSummarizationWrapper] = []
    wrapper_by_parent_id = {
        wrapper.premises_id: wrapper
        for wrapper in business_level_request.fact_summarization_wrappers
        if wrapper.parent_type == request.parent_type
    }
    for premises_id in request.premises_ids:
        if parent_wrapper := wrapper_by_parent_id.get(premises_id):
            wrappers_for_report_metric.append(parent_wrapper)

    at_least_one_structure_comes_from_business = any(
        wrapper.parent_type == ParentType.STRUCTURE and wrapper.business_id
        for wrapper in request.fact_summarization_wrappers
    )

    parent_type = ParentType.BUSINESS if at_least_one_structure_comes_from_business else ParentType.PREMISES
    parent_ids = request.business_ids if at_least_one_structure_comes_from_business else request.premises_ids

    for parent_id in parent_ids:
        structure_request = SummarizeOneFactRequestFactory.create_summarize_structures_request(
            request,
            summary_config,
            business_id=parent_id if parent_type == ParentType.BUSINESS else None,
            premises_id=parent_id if parent_type == ParentType.PREMISES else None,
        )

        if __has_no_structure_data(structure_request):
            continue

        submission_business_id = structure_request.fact_summarization_wrappers[0].submission_business_id
        structure_metric_request = summarize_one_fact_with_summary_config(
            structure_request, submission_business_id, metric_storage_object=metric_storage_object
        )
        if structure_metric_request:
            for structure_wrapper in structure_request.fact_summarization_wrappers:
                # It is possible that a structure does not have a data point (fact) to summarize
                # in that case we should not try to create a summarization entry for a given structure
                if not structure_wrapper.fact:
                    continue

                wrappers_for_report_metric.append(structure_wrapper)

    return wrappers_for_report_metric


@bind_lambda_logging_context
@log_function_inputs
@serverless_function
def summarize_one_fact_batch(event, context=None):
    requests: List[SummarizeOneFactRequest] = summarize_one_fact_request_schema.load(event, many=True)

    request_results = []
    fact_summarization_storage = FactSummarizationStorage()
    for request in requests:
        try:
            summarization_result = __summarize_one_fact(request, fact_summarization_storage)
            if "error" in summarization_result:
                request_results.append(summarization_result)
        except Exception as e:
            logger.exception(
                "Summarizing one fact failed",
                request=request,
                report_id=request.report_id,
                execution_id=request.execution_id,
                submission_ids=request.submission_ids,
            )
            request_results.append({"fact_subtype_id": request.fact_subtype_id, "error": str(e)})

    summarization_results = []
    for request_result in request_results:
        if (fact_subtype_id := request_result.get("fact_subtype_id")) and (error := request_result.get("error")):
            summarization_results.append(OneFactSummarizationResult(fact_subtype_id=fact_subtype_id, error=error))

    fact_summarization_result = FactSummarizationResult(summarization_results=summarization_results)
    captured_requests = fact_summarization_storage.get_storage_objects()

    if captured_requests:
        object_cache_key = str(uuid.uuid4())
        get_fact_summarization_storage_object_redis_client().put_storage_objects(captured_requests, object_cache_key)
        fact_summarization_result.metrics_creation_cache_key = object_cache_key

    return fact_summarization_result_schema.dump(fact_summarization_result)


def __summarize_one_fact(
    request: SummarizeOneFactRequest,
    fact_summarization_storage: Optional[FactSummarizationStorage] = None,
):
    __enrich_summarize_one_fact_request(request)

    for summary_config in request.fact_subtype.summary_configs:
        if summary_config.parent_type != request.parent_type:
            continue
        summarization_request = SummarizeOneFactRequestFactory.create_summarization_request(request, summary_config)
        fact_summarization_storage_object = FactSummarizationStorageObject() if fact_summarization_storage else None
        structure_summary_storage_object = StructureSummaryStorageObject() if fact_summarization_storage else None
        try:
            if __requires_structure_metric_summarization(
                summary_config.parent_type, request.structures_have_at_least_one_fact
            ):
                updated_fact_summarization_wrappers = summarize_structures(
                    request, summarization_request, summary_config, structure_summary_storage_object
                )
                summarization_request.fact_summarization_wrappers = updated_fact_summarization_wrappers
            cache_key: str = __get_metric_preference_cache_key(
                request.execution_id, summary_config.id, summarization_request.summary_parent_id
            )
            metric_preference_dictionary: Dict[Any, Any] = cache_client.get_from_cache(cache_key, None, None)
            metric_preference: MetricPreference = MetricPreference(**metric_preference_dictionary)
            metric_request: Optional[MetricV2Request] = summarize_one_fact_with_summary_config(
                summarization_request, metric_storage_object=fact_summarization_storage_object
            )
            __update_metric_preference_if_needed(
                request,
                summarization_request,
                metric_preference,
                metric_request is not None,
                __is_at_most_1_value_specified(metric_request),
            )
        except Exception as e:
            logger.exception(
                "Metric creation failed",
                request=request,
                report_id=request.report_id,
                execution_id=request.execution_id,
                submission_ids=request.submission_ids,
            )
            return {"fact_subtype_id": request.fact_subtype_id, "error": str(e)}
        finally:
            if fact_summarization_storage_object and fact_summarization_storage:
                fact_summarization_storage_object.register_structure_storage(structure_summary_storage_object)
                fact_summarization_storage.maybe_append_object(fact_summarization_storage_object)

    return {"fact_subtype_id": request.fact_subtype_id}


def __enrich_summarize_one_fact_request(request) -> None:
    submission_id = request.submission_ids[0]
    # Custom subtypes can change fact types and summary configs, so we should skip cache for those.
    skip_cache = str(submission_id) in request.fact_subtype_id
    request.fact_subtype = facts_client.get_fact_subtype(fact_subtype_id=request.fact_subtype_id, skip_cache=skip_cache)

    summarization_metadata = fact_summarization_metadata_redis_client.get_fact_summarization_metadata(
        request.execution_id
    )
    request.fact_summarization_wrappers = (
        summarization_metadata.fact_summarization_wrappers if summarization_metadata else []
    )

    __enrich_request_with_facts(request)
    __optionally_enrich_request_with_classification_task(request)


def __enrich_request_with_facts(request: SummarizeOneFactRequest):
    facts = facts_client.get_facts(
        request.parent_ids,
        request.parent_type,
        fact_subtype_id=request.fact_subtype_id,
        submission_id=request.submission_ids[0],
        organization_id=request.organization_id,
        as_of=request.submission_frozen_as_of,
    )

    logger.debug(
        "Fetched facts",
        fact_count=len(facts),
        fact_subtype_id=request.fact_subtype_id,
        report_id=request.report_id,
        as_of=request.submission_frozen_as_of,
    )
    structure_facts = []
    if request.structures_have_at_least_one_fact:
        structure_facts = facts_client.get_facts(
            request.structure_ids,
            ParentType.STRUCTURE,
            fact_subtype_id=request.fact_subtype_id,
            submission_id=request.submission_ids[0],
            organization_id=request.organization_id,
            as_of=request.submission_frozen_as_of,
        )
        logger.debug(
            "Fetched structure facts",
            fact_count=len(structure_facts),
            fact_subtype_id=request.fact_subtype_id,
            report_id=request.report_id,
            as_of=request.submission_frozen_as_of,
        )

    # Group all fact wrappers by their parent id (stringified) for fast lookup
    fact_wrappers_by_parent_id = defaultdict(list)
    for fact_wrapper in request.fact_summarization_wrappers:
        fact_wrappers_by_parent_id[str(fact_wrapper.parent_id)].append(fact_wrapper)

    for fact in facts:
        matched_fact_wrappers = fact_wrappers_by_parent_id.get(fact.parent_id, [])
        for matched_fact_wrapper in matched_fact_wrappers:
            if matched_fact_wrapper.parent_type == fact.parent_type:
                matched_fact_wrapper.fact = fact

    for structure_fact in structure_facts:
        matched_fact_wrappers = fact_wrappers_by_parent_id.get(structure_fact.parent_id, [])
        for matched_fact_wrapper in matched_fact_wrappers:
            if matched_fact_wrapper.parent_type == ParentType.STRUCTURE:
                matched_fact_wrapper.fact = structure_fact


def __optionally_enrich_request_with_classification_task(request: SummarizeOneFactRequest):
    if request.fact_type_id in [
        FactTypeID.MULTI_LABEL_CLASSIFICATION,
        FactTypeID.MULTICLASS_CLASSIFICATION,
        FactTypeID.ALCOHOL_SERVED,
    ]:
        request.classification_task = facts_client.get_classification_task(
            classification_task_id=request.fact_subtype_id
        )
    elif request.fact_type_id == FactTypeID.BINARY_CLASSIFICATION:
        request.classification_task = facts_client.get_classification_task(
            classification_task_id=request.fact_type_id.value
        )


def __is_at_most_1_value_specified(metric_request: Optional[MetricV2Request]) -> bool:
    if not metric_request:
        return False
    try:
        if metric_request.metric_type == "CATEGORICAL_DATA_SUMMARY":
            return len([v for v in metric_request.string_values if v and v != "Unknown"]) < 2
        if metric_request.metric_type in {"MEAN", "SUM"}:
            return len([v for v in metric_request.float_values if v is not None]) < 2
    except Exception:
        logger.exception("Failed to check if at most 1 value specified")

    return False


def __get_metric_preference_cache_key(execution_id: UUID, summary_config_id: str, summary_parent_id: UUID) -> str:
    return f"{execution_id!s}#{summary_config_id}#{summary_parent_id!s}"


def __update_metric_preference_if_needed(
    request: SummarizeOneFactRequest,
    summarization_request: SummarizationRequest,
    metric_preference: MetricPreference,
    metric_was_created: bool,
    is_at_most_one_value: bool,
    fact_summarization_storage_object: Optional[FactSummarizationStorageObject] = None,
) -> None:
    # We need to exclude CLOSEST_PREMISE_IN_SUBMISSION_METRIC_NAME because it will always have FirstPartySource until
    # a more appropriate source type is created for observations calculated within copilot.
    is_submitted_data_preference: bool = (
        has_at_least_one_first_party_observation(request)
        and request.fact_subtype_id != FactSubtypeID.DISTANCE_TO_CLOSEST_PREMISES_IN_SUBMISSION.value
    )

    metric_group_name: str = get_metric_group_name(
        summarization_request.summary_config, request.fact_subtype, request.coverages
    )
    is_enabled_by_default: bool = is_default_metric_preference(
        summarization_request.summary_config.display_name,
        request.coverages,
        request.naics_codes,
        request.is_metrics_set_manually,
        organization_id=None,
        fact_subtype=request.fact_subtype,
        is_at_most_one_value=is_at_most_one_value,
        metric_group_name=metric_group_name,
        summary_config_id=summarization_request.summary_config.id if summarization_request.summary_config else None,
    )

    requires_update: bool = False
    if not request.is_metrics_set_manually and (is_enabled_by_default != metric_preference.is_enabled):
        requires_update = True
        metric_preference.is_enabled = is_enabled_by_default

    # Metric was applicable before, but now it is not. We should hide it.
    if metric_preference.is_applicable and not metric_was_created:
        requires_update = True

    if is_submitted_data_preference != metric_preference.is_submitted_data:
        requires_update = True
        metric_preference.is_submitted_data = is_submitted_data_preference

    if metric_group_name != metric_preference.metric_group_name:
        requires_update = True
        metric_preference.metric_group_name = metric_group_name

    if requires_update:
        metric_preference.is_applicable = metric_was_created
        if fact_summarization_storage_object:
            fact_summarization_storage_object.related_metric_preference = metric_preference
        else:
            copilot_client.update_metric_preference(metric_preference)
