from collections.abc import Iterable
from typing import List, Optional
from uuid import UUID

from entity_resolution_service_client_v3 import Entity, EntityPremises


def get_most_suitable_premises(premises: Optional[List[EntityPremises]]) -> Optional[EntityPremises]:
    if not premises:
        return None
    types_in_order = ["PHYSICAL_ADDRESS", "UNDEFINED", "MAILING_ADDRESS"]
    premises_copy = premises.copy()
    premises_copy.sort(
        key=lambda item: types_in_order.index(item.type) if item.type in types_in_order else len(types_in_order)
    )
    return premises_copy[0]


def get_premises_id_to_entity_id_from_entities(
    entities: Iterable[Entity], business_ids_excluded_for_property: list[str]
) -> dict[UUID, UUID]:
    premises_ids_to_entity_id: dict[UUID, UUID] = dict()
    for e in entities:
        premise = None if e.id in business_ids_excluded_for_property else get_most_suitable_premises(e.premises)
        if premise:
            premises_ids_to_entity_id[premise.premises_id] = e.id  # type: ignore
    return premises_ids_to_entity_id


def get_business_id_to_premises_id_from_entities(
    entities: Iterable[Entity], business_ids_excluded_for_property: list[str]
) -> dict[str, str]:
    business_id_to_premises_id: dict[str, str] = dict()
    for e in entities:
        if e.id not in business_ids_excluded_for_property:
            premise = get_most_suitable_premises(e.premises)
            if premise:
                business_id_to_premises_id[str(e.id)] = str(premise.premises_id)  # type: ignore
    return business_id_to_premises_id


def get_premises_ids_from_entities(
    entities: Iterable[Entity], business_ids_excluded_for_property: list[str]
) -> List[UUID]:
    return list(get_premises_id_to_entity_id_from_entities(entities, business_ids_excluded_for_property).keys())
