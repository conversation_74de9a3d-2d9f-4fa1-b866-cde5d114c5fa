from datetime import datetime
from typing import List
from uuid import UUID

import pytest
from facts_client.model.fact import Fact
from facts_client.model.importance import Importance
from facts_client.model.mean_summary_config import MeanSummaryConfig
from facts_client.model.observation import Observation
from facts_client.model.source import Source
from facts_client.model.web_ingested_source import WebIngestedSource
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID
from static_common.enums.parent import ParentType
from static_common.enums.source_types import SourceTypeID

from src.handlers.facts import (
    has_at_least_one_first_party_observation,
    summarize_structures,
)
from src.models.fact_summarization_wrapper import FactSummarizationWrapper
from src.models.requests import SummarizeOneFactRequest, SummarizeOneFactRequestFactory


def create_summarization_request(
    fact_summarization_wrappers: List[FactSummarizationWrapper],
) -> SummarizeOneFactRequest:
    return SummarizeOneFactRequest(
        report_id=UUID("95a1189f-4472-442f-bd9c-c7e0416b31cc"),
        execution_id=UUID("95a1189f-4472-442f-bd9c-c7e0416b31cc"),
        execution_started_at=datetime.now(),
        parent_type=ParentType.PREMISES,
        fact_summarization_wrappers=fact_summarization_wrappers,
        fact_type_id=FactTypeID.INTEGER,
        fact_subtype_id=FactSubtypeID.BUILDING_AGE,
        organization_id=1,
        submission_ids=[],
        coverages=[],
    )


def get_mean_summary_config():
    return MeanSummaryConfig(
        id="TEST_SUMMARY_CONFIG_ID",
        display_name="test display name",
        summary_type_id="MEAN",
        parent_type=ParentType.PREMISES,
        value_attr="at_most_miles",
        units_label="miles",
        only_discovered_in=[],
        only_from_relationships=[],
    )


FIRST_PARTY_SOURCE = Source(source_type_id=SourceTypeID.FIRST_PARTY)
NON_FIRST_PARTY_SOURCE = WebIngestedSource(source_type_id=SourceTypeID.WEB_INGESTED)
FIRST_PARTY_OBSERVATION = Observation._from_openapi_data(
    source=FIRST_PARTY_SOURCE,
    fact_type_id=FactTypeID.INTEGER,
    parent_id="95a1189f-4472-442f-bd9c-c7e0416b31cc",
    parent_type=ParentType.PREMISES,
    published_at=None,
)
NON_FIRST_PARTY_OBSERVATION = Observation._from_openapi_data(
    source=NON_FIRST_PARTY_SOURCE,
    fact_type_id=FactTypeID.INTEGER,
    parent_id="95a1189f-4472-442f-bd9c-c7e0416b31cc",
    parent_type=ParentType.PREMISES,
    published_at=None,
)
FIRST_PARTY_FACT = Fact(
    _check_type=False,
    observation=FIRST_PARTY_OBSERVATION,
    parent_id="95a1189f-4472-442f-bd9c-c7e0416b31cc",
    parent_type=ParentType.PREMISES,
    importance=Importance(grade="LOW"),
)
NON_FIRST_PARTY_FACT = Fact(
    _check_type=False,
    observation=NON_FIRST_PARTY_OBSERVATION,
    parent_id="95a1189f-4472-442f-bd9c-c7e0416b31cc",
    parent_type=ParentType.PREMISES,
    importance=Importance(grade="LOW"),
)


def test_has_at_least_one_first_party_observation_when_no_facts_expect_false():
    request_no_facts = create_summarization_request([])
    assert not has_at_least_one_first_party_observation(request_no_facts)


def test_has_at_least_one_first_party_observation_when_no_first_party_facts_expect_false():
    request_no_first_party_facts = create_summarization_request(
        [
            FactSummarizationWrapper(
                premises_id=UUID("475b18ab-67ed-4577-a077-129e8de7990d"),
                parent_id=UUID("475b18ab-67ed-4577-a077-129e8de7990d"),
                parent_type=ParentType.PREMISES,
            ),
            FactSummarizationWrapper(
                business_id=UUID("95a1189f-4472-442f-bd9c-c7e0416b31cc"),
                parent_id=UUID("e98ca17d-8a93-428b-a649-a134098749a9"),
                parent_type=ParentType.PREMISES,
                fact=NON_FIRST_PARTY_FACT,
            ),
        ]
    )
    assert not has_at_least_one_first_party_observation(request_no_first_party_facts)


def test_has_at_least_one_first_party_observation_when_first_party_fact_expect_true():
    request_first_party_facts = create_summarization_request(
        [
            FactSummarizationWrapper(
                business_id=UUID("95a1189f-4472-442f-bd9c-c7e0416b31cc"),
                parent_id=UUID("95a1189f-4472-442f-bd9c-c7e0416b31cc"),
                parent_type=ParentType.PREMISES,
            ),
            FactSummarizationWrapper(
                business_id=UUID("95a1189f-4472-442f-bd9c-c7e0416b31cc"),
                parent_id=UUID("95a1189f-4472-442f-bd9c-c7e0416b31cc"),
                parent_type=ParentType.PREMISES,
                fact=FIRST_PARTY_FACT,
            ),
        ]
    )
    assert has_at_least_one_first_party_observation(request_first_party_facts)


def test_summarize_structures(mocker):
    summarize_one_fact_with_summary_config_mock = mocker.patch(
        "src.handlers.facts.summarize_one_fact_with_summary_config",
        return_value=True,
    )
    mean_summary_config = get_mean_summary_config()

    premises_id_without_structures: UUID = UUID("18fbfd38-b1fc-44ed-81d9-0568025636da")
    premises_id_without_fact: UUID = UUID("0c1fc619-8c23-44cf-b931-0b49697bc997")
    premises_id_with_fact: UUID = UUID("faaafa55-f3ef-4d94-8585-0181f5ebaa3e")

    structure_id_without_fact: UUID = UUID("a447e426-2973-4a9e-a1a6-94eb27fa5b1c")
    structure_id_1_with_fact: UUID = UUID("599447ed-5f11-428e-8834-4b47ce1cdf78")
    structure_id_2_with_fact: UUID = UUID("30c7879c-b231-48c6-80b7-9bfebadf793c")

    summarization_request = create_summarization_request(
        [
            FactSummarizationWrapper(
                premises_id=premises_id_without_structures,
                parent_id=premises_id_without_structures,
                parent_type=ParentType.PREMISES,
            ),
            FactSummarizationWrapper(
                premises_id=premises_id_without_fact,
                parent_id=premises_id_without_fact,
                parent_type=ParentType.PREMISES,
            ),
            FactSummarizationWrapper(
                premises_id=premises_id_with_fact,
                parent_id=premises_id_with_fact,
                parent_type=ParentType.PREMISES,
                fact=FIRST_PARTY_FACT,
            ),
            FactSummarizationWrapper(
                premises_id=premises_id_without_fact,
                parent_id=structure_id_1_with_fact,
                parent_type=ParentType.STRUCTURE,
                fact=FIRST_PARTY_FACT,
            ),
            FactSummarizationWrapper(
                premises_id=premises_id_without_fact,
                parent_id=structure_id_without_fact,
                parent_type=ParentType.STRUCTURE,
            ),
            FactSummarizationWrapper(
                premises_id=premises_id_with_fact,
                parent_id=structure_id_2_with_fact,
                parent_type=ParentType.STRUCTURE,
                fact=NON_FIRST_PARTY_FACT,
            ),
        ]
    )
    business_level_request = SummarizeOneFactRequestFactory.create_summarization_request(
        summarization_request, mean_summary_config
    )
    updated_fact_wrappers = summarize_structures(summarization_request, business_level_request, mean_summary_config)

    assert 2 == summarize_one_fact_with_summary_config_mock.call_count
    assert 5 == len(updated_fact_wrappers)
    assert [
        premises_id_with_fact,
        premises_id_without_structures,
        premises_id_without_fact,
        structure_id_2_with_fact,
        structure_id_1_with_fact,
    ] == [wrapper.parent_id for wrapper in updated_fact_wrappers]
