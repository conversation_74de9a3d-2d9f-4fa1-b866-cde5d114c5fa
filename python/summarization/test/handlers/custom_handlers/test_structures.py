from datetime import datetime
from unittest.mock import MagicMock
from uuid import UUID, uuid4

import pytest
from copilot_client_v3 import SubmissionBusiness
from entity_resolution_service_client_v3 import Entity, EntityPremises
from facts_client.model.fact import Fact
from facts_client.model.first_party_source import FirstPartySource
from facts_client.model.integer_observation import IntegerObservation
from facts_client.model.relationship import Relationship
from facts_client.model.relationships_observation import RelationshipsObservation
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID
from static_common.enums.parent import ParentType
from static_common.enums.source_types import SourceTypeID

from src.handlers.custom_handlers.structures import (
    ALLEGED_STRUCTURE_ID,
    summarize_structure_counts,
)
from src.models.requests import SummarizeAllFactsRequest

SUMMARY_CONFIG_ID = "STRUCTURE_COUNTS_CUSTOM_CONFIG"
ORGANIZATION_ID = 1
FIRST_PARTY_SOURCE = FirstPartySource(source_type_id=SourceTypeID.FIRST_PARTY.value, submission_id=str(uuid4()))

SUBMISSION_BUSINESS_ID_1 = uuid4()
SUBMISSION_BUSINESS_ID_2 = uuid4()
SUBMISSION_BUSINESS_ID_3 = uuid4()

BUSINESS_ID_1 = uuid4()
BUSINESS_ID_2 = uuid4()
BUSINESS_ID_3 = uuid4()
PREMISES_ID_1 = uuid4()
PREMISES_ID_2 = uuid4()
STRUCTURES_ID_1 = uuid4()
STRUCTURES_ID_2 = uuid4()
STRUCTURES_ID_3 = uuid4()
STRUCTURES_ID_4 = uuid4()


def __create_structure_count_observation(parent_id: UUID, value: int) -> IntegerObservation:
    return IntegerObservation(
        _check_type=False,
        parent_id=str(parent_id),
        parent_type=ParentType.PREMISES.value,
        fact_type_id=FactTypeID.INTEGER.value,
        fact_subtype_id=FactSubtypeID.STRUCTURES_COUNT.value,
        value=value,
        source=FIRST_PARTY_SOURCE,
    )


def __create_structure_count_fact(parent_id: UUID, value: int) -> Fact:
    return Fact(
        _check_type=False,
        parent_id=str(parent_id),
        parent_type=ParentType.PREMISES.value,
        fact_type_id=FactTypeID.INTEGER.value,
        fact_subtype_id=FactSubtypeID.STRUCTURES_COUNT.value,
        observation=__create_structure_count_observation(parent_id, value),
    )


def __create_relationships_observation(parent_id, parent_type, children) -> RelationshipsObservation:
    return RelationshipsObservation(
        fact_type_id=FactTypeID.RELATIONSHIPS,
        fact_subtype_id=FactSubtypeID.STRUCTURES.value,
        parent_id=str(parent_id),
        children_type=ParentType.STRUCTURE.value,
        parent_type=parent_type.value,
        relationship_type="STRUCTURE",
        children=children,
    )


def __create_structures_fact(parent_id: UUID, parent_type: ParentType, children: list[Relationship]) -> Fact:
    return Fact(
        _check_type=False,
        parent_id=str(parent_id),
        parent_type=parent_type.value,
        fact_type_id=FactTypeID.RELATIONSHIPS.value,
        fact_subtype_id=FactSubtypeID.STRUCTURES.value,
        observation=__create_relationships_observation(parent_id, parent_type, children),
    )


def __create_relationship(remote_id: UUID) -> Relationship:
    return Relationship(remote_id=str(remote_id), display_name="who cares")


@pytest.fixture
def entities() -> list[Entity]:
    return [
        Entity(id=str(BUSINESS_ID_1), premises=[EntityPremises(premises_id=str(PREMISES_ID_1))]),
        Entity(id=str(BUSINESS_ID_2), premises=[EntityPremises(premises_id=str(PREMISES_ID_1))]),
        Entity(id=str(BUSINESS_ID_3), premises=[EntityPremises(premises_id=str(PREMISES_ID_2))]),
    ]


@pytest.fixture
def submission_businesses() -> list[SubmissionBusiness]:
    return [
        SubmissionBusiness(id=str(SUBMISSION_BUSINESS_ID_1), business_id=str(BUSINESS_ID_1)),
        SubmissionBusiness(id=str(SUBMISSION_BUSINESS_ID_2), business_id=str(BUSINESS_ID_2)),
        SubmissionBusiness(id=str(SUBMISSION_BUSINESS_ID_3), business_id=str(BUSINESS_ID_3)),
    ]


@pytest.fixture
def summarize_all_facts_request(entities, submission_businesses) -> SummarizeAllFactsRequest:
    return SummarizeAllFactsRequest(
        organization_id=1,
        submission_ids=[uuid4()],
        submission_frozen_as_of=datetime.now(),
        business_ids_excluded_for_property=[uuid4()],
        entities=entities,
        report_id=uuid4(),
        execution_id=uuid4(),
        submission_businesses=submission_businesses,
    )


@pytest.fixture
def log_mock():
    return MagicMock()


@pytest.fixture
def facts_client_mock(mocker):
    mock = MagicMock()
    mock.get_facts.side_effect = [
        [
            __create_structures_fact(
                BUSINESS_ID_1,
                ParentType.BUSINESS,
                [__create_relationship(STRUCTURES_ID_1), __create_relationship(STRUCTURES_ID_2)],
            ),
            __create_structures_fact(
                BUSINESS_ID_2,
                ParentType.BUSINESS,
                [__create_relationship(STRUCTURES_ID_3), __create_relationship(STRUCTURES_ID_4)],
            ),
        ],
        [
            __create_structures_fact(
                PREMISES_ID_1,
                ParentType.PREMISES,
                [
                    __create_relationship(STRUCTURES_ID_1),
                    __create_relationship(STRUCTURES_ID_2),
                    __create_relationship(STRUCTURES_ID_3),
                    __create_relationship(STRUCTURES_ID_4),
                ],
            ),
        ],
    ]
    mocker.patch(
        "src.handlers.custom_handlers.structures.get_facts_client",
        return_value=mock,
    )

    return mock


@pytest.fixture
def copilot_client_mock(mocker):
    mock = MagicMock()
    mock.get_metric_preference.return_value = None
    mocker.patch(
        "src.handlers.custom_handlers.structures.get_copilot_v3_client",
        return_value=mock,
    )
    return mock


def test_summarize_structure_counts(summarize_all_facts_request, log_mock, facts_client_mock, copilot_client_mock):
    summarization_result = summarize_structure_counts(summarize_all_facts_request, SUMMARY_CONFIG_ID, log_mock)
    assert summarization_result is not None

    actual_metrics = [
        metric_request for unit in summarization_result.summarization_units for metric_request in unit.metric_requests
    ]

    report_level_metric = next((metric for metric in actual_metrics if metric.parent_type == "REPORT"), None)
    assert report_level_metric is not None

    assert ParentType.PREMISES.value == report_level_metric.children_type
    assert [
        str(PREMISES_ID_1),
        str(PREMISES_ID_2),
    ] == report_level_metric.value_parent_ids
    assert [
        str(BUSINESS_ID_2),
        str(BUSINESS_ID_3),
    ] == report_level_metric.value_business_ids
    assert [
        ParentType.PREMISES.value,
        ParentType.PREMISES.value,
    ] == report_level_metric.value_parent_types
    assert [4.0, 1] == report_level_metric.float_values

    business_1_metric = next(
        (
            metric
            for metric in actual_metrics
            if metric.parent_type == "BUSINESS" and metric.parent_id == str(BUSINESS_ID_1)
        ),
        None,
    )
    assert business_1_metric is not None
    assert str(SUBMISSION_BUSINESS_ID_1) == business_1_metric.submission_business_id
    assert ParentType.STRUCTURE.value == business_1_metric.children_type
    assert [1.0, 1.0] == business_1_metric.float_values
    assert [
        ParentType.STRUCTURE.value,
        ParentType.STRUCTURE.value,
    ] == business_1_metric.value_parent_types
    assert {str(STRUCTURES_ID_1), str(STRUCTURES_ID_2)} == set(business_1_metric.value_parent_ids)

    business_2_metric = next(
        (
            metric
            for metric in actual_metrics
            if metric.parent_type == "BUSINESS" and metric.parent_id == str(BUSINESS_ID_2)
        ),
        None,
    )
    assert business_2_metric is not None
    assert str(SUBMISSION_BUSINESS_ID_2) == business_2_metric.submission_business_id
    assert ParentType.STRUCTURE.value == business_2_metric.children_type
    assert [1.0, 1.0] == business_2_metric.float_values
    assert [ParentType.STRUCTURE.value, ParentType.STRUCTURE.value] == business_2_metric.value_parent_types
    assert {str(STRUCTURES_ID_3), str(STRUCTURES_ID_4)} == set(business_2_metric.value_parent_ids)

    assert facts_client_mock.get_facts.call_count == 2
    assert facts_client_mock.get_structures_counts.call_count == 0
    assert copilot_client_mock.get_metric_preference.call_count == 1
    assert copilot_client_mock.create_or_update_metric_preference.call_count == 0
    assert copilot_client_mock.create_metric_v2.call_count == 0
    assert copilot_client_mock.bulk_create_metric_v2.call_count == 0
