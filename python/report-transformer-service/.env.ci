IS_TEST_ENV="true"
KALEPA_ENV="dev"
AWS_DEFAULT_REGION="us-east-1"
AWS_ACCESS_KEY_ID="testing"
AWS_SECRET_ACCESS_KEY="testing"
AWS_SECURITY_TOKEN="testing"
AWS_SESSION_TOKEN="testing"
REDIS_CACHE_HOST=FAKE_HOST
REDIS_CACHE_PORT=6379
LOGGING_USE_CONSOLE_RENDERER=True
PYTEST_DISABLE_PLUGIN_AUTOLOAD=1
PYTEST_PLUGINS=pytest_mock,requests_mock,xdist,xdist.plugin,xdist.looponfail
DB_CONFIG_HOST=localhost
DB_CONFIG_PORT=5436
DB_CONFIG_USER=report-transformer-service
DB_CONFIG_PASSWORD=report-transformer-service
DB_CONFIG_DBNAME=report-transformer-service
FACTS_API_V1_URL="facts"
FACTS_API_V2_URL="facts"
COPILOT_API_V3_URL="copilot"
RTS_API_V1_URL="report-transformer-service"
ERS_API_V3_URL="ers"
REDIS_HOST="redis"
REDIS_PORT=6379
REPORT_TRANSFORMER_SERVICE_BUCKET_NAME="report-transformer-service-bucket"
