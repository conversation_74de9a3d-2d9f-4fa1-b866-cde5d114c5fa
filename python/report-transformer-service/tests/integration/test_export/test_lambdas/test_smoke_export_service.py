from unittest.mock import MagicMock, patch
import uuid

from report_transformer_service_client.models.report_export_generation_request import (
    ReportExportGenerationRequest,
)
from sqlalchemy.orm.session import Session

from src.export.lambdas.smoke_export_service import (
    perform_smoke_tests_for_report_exports,
)
from src.export.models.report_generation import ReportExportGenerationDbModel
from src.export.models.report_generation_status import ReportGenerationStatus
from src.views.models.api.report_export import ReportExportFormat
from tests.integration.factories import export_fixture, view_fixture


def generation_status(generation_id: str):
    return MagicMock(status=ReportGenerationStatus.ERROR, generation_id=generation_id)


def test_smoke_export_service(db_session: Session):
    view = view_fixture(db_session, organization_ids=[8])
    export_fixture(db_session, name="Smoke Test Export", organization_id=8, view=view)
    db_session.commit()
    with (
        patch("src.export.logic.generation_scheduling.schedule_export_generation_with_rts_client") as schedule_mock,
        patch("src.export.logic.generation_scheduling.get_export_generation_status_from_rts_client") as status_mock,
        patch("src.export.lambdas.smoke_export_service._get_slack_messages_client") as mock_slack_client,
        patch("src.export.lambdas.smoke_export_service._get_capi_client") as mock_capi_client,
    ):
        schedule_mock.return_value = ReportExportGenerationRequest(
            export_format=ReportExportFormat.PDF,
            generation_id=str(uuid.uuid4()),
            report_id="2ca89f96-70f1-48be-9966-b5e2e61c4534",
            organization_id=8,
        )
        status_mock.return_value = MagicMock(side_effect=generation_status)

        mock_slack = MagicMock()
        mock_slack_client.return_value = mock_slack

        mock_capi = MagicMock()
        mock_capi.get_reports = MagicMock(reports=[MagicMock(id="2ca89f96-70f1-48be-9966-b5e2e61c4534")])
        mock_capi_client.return_value = mock_capi

        generation_requests = perform_smoke_tests_for_report_exports({})
        for generation_id, report_id, org_id in generation_requests:
            assert (
                db_session.query(ReportExportGenerationDbModel)
                .filter(
                    ReportExportGenerationDbModel.id == generation_id,
                    ReportExportGenerationDbModel.export_generation_request_params["report_id"] == report_id,
                    ReportExportGenerationDbModel.export_generation_request_params["organization_id"] == org_id,
                )
                .one_or_none()
            )
