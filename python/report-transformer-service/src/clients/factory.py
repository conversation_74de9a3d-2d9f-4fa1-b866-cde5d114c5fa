from typing import TYPE_CHECKING
import os

if TYPE_CHECKING:
    from common.clients.cache import RedisCache<PERSON>lient
    from common.clients.copilot import CopilotV3Client
    from common.clients.copilot_v3_client import CopilotV3<PERSON>lient as CopilotV3ClientV2
    from common.clients.facts import FactsClient
    from common.clients.facts_v2 import FactsClientV2
    from common.clients.rts_v1_client import RtsV1Client
    from entity_resolution_service_client_v3 import <PERSON><PERSON><PERSON><PERSON><PERSON> as ErsV3Client
    from redis import Redis

    from src.clients.aws_lambda import LambdaClient
    from src.clients.aws_sqs import SQSClient
    from src.clients.s3 import S3Client


def create_raw_redis_client() -> "Redis":
    from redis import Redis

    return Redis(host=os.environ["REDIS_HOST"], port=int(os.environ["REDIS_PORT"]))


def create_facts_client() -> "FactsClient":
    from common.clients.facts import FactsClient

    return FactsClient(os.environ["FACTS_API_V1_URL"])


def create_copilot_client() -> "CopilotV3Client":
    from common.clients.copilot import CopilotV3<PERSON>lient

    return CopilotV3Client(os.environ["COPILOT_API_V3_URL"])


def create_new_copilot_client() -> "CopilotV3ClientV2":
    from common.clients.copilot_v3_client import CopilotV3Client

    return CopilotV3Client(os.environ["COPILOT_API_V3_URL"])


def create_ers_client() -> "ErsV3Client":
    from entity_resolution_service_client_v3 import ApiClient as ErsApiClient
    from entity_resolution_service_client_v3 import Configuration as ErsConfig
    from entity_resolution_service_client_v3 import DefaultApi as ErsV3Client

    return ErsV3Client(ErsApiClient(ErsConfig(os.environ["ERS_API_V3_URL"])))


def create_lambda_client() -> "LambdaClient":
    from src.clients.aws_lambda import LambdaClient

    return LambdaClient()


def create_sqs_client() -> "SQSClient":
    from src.clients.aws_sqs import SQSClient

    return SQSClient()


def create_s3_client() -> "S3Client":
    from src.clients.s3 import S3Client

    return S3Client(os.environ["REPORT_TRANSFORMER_SERVICE_BUCKET_NAME"])


def create_redis_cache_client() -> "RedisCacheClient":
    from common.clients.cache import RedisCacheClient

    return RedisCacheClient(create_raw_redis_client())


def create_facts_client_v2() -> "FactsClientV2":
    from common.clients.facts_v2 import FactsClientV2

    return FactsClientV2(os.environ["FACTS_API_V2_URL"], cache_client=create_redis_cache_client())


def create_rts_v1_client() -> "RtsV1Client":
    from common.clients.rts_v1_client import RtsV1Client

    return RtsV1Client(os.environ["RTS_API_V1_URL"], cache_client=create_redis_cache_client())
