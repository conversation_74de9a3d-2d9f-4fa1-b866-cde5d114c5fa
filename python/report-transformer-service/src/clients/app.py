from collections.abc import Callable
from typing import TYPE_CHECKING, Protocol, TypeAlias, TypeVar

from src.clients.factory import (
    create_copilot_client,
    create_ers_client,
    create_facts_client,
    create_facts_client_v2,
    create_lambda_client,
    create_new_copilot_client,
    create_redis_cache_client,
    create_rts_v1_client,
    create_s3_client,
    create_sqs_client,
)
from src.utils import is_running_in_lambda_environment

if TYPE_CHECKING:
    from common.clients.copilot import CopilotV3Client as CopilotV3ClientTypeRaw
    from common.clients.copilot_v3_client import CopilotV3Client as NewCopilotV3ClientTypeRaw
    from common.clients.facts import FactsClient as FactsClientTypeRaw
    from entity_resolution_service_client_v3 import Default<PERSON><PERSON> as ErsV3ClientTypeRaw
    from src.clients.aws_lambda import LambdaClient as LambdaClientTypeRaw
    from src.clients.s3 import S3Client
    from src.clients.aws_sqs import SQSClient
    from common.clients.facts_v2 import FactsClientV2 as FactsV2ClientTypeRaw
    from common.clients.cache import RedisCacheClient as RedisCacheClientTypeRaw
    from common.clients.rts_v1_client import RtsV1Client as RTSV1ClientTypeRaw

from infrastructure_common.logging import get_logger

logger = get_logger()

T = TypeVar("T", covariant=True)


FactsV1ClientType: TypeAlias = "FactsClientTypeRaw"
FactsV2ClientType: TypeAlias = "FactsV2ClientTypeRaw"
CopilotV3ClientType: TypeAlias = "CopilotV3ClientTypeRaw"
NewCopilotV3ClientType: TypeAlias = "NewCopilotV3ClientTypeRaw"
ErsV3ClientType: TypeAlias = "ErsV3ClientTypeRaw"
LambdaClientType: TypeAlias = "LambdaClientTypeRaw"
S3ClientType: TypeAlias = "S3Client"
RedisCacheClientType: TypeAlias = "RedisCacheClientTypeRaw"
SQSClientType: TypeAlias = "SQSClient"
RTSV1ClientType: TypeAlias = "RTSV1ClientTypeRaw"


class IClientProxy(Protocol[T]):
    def __init__(self, client_factory: Callable[[], T]) -> None:
        ...

    def get_client(self) -> T:
        ...


class LazyClientProxy(IClientProxy[T]):
    """
    Generates client at first access.
    """

    _client: T | None

    def __init__(self, client_factory: Callable[[], T]) -> None:
        self._client_factory = client_factory
        self._client = None

    def get_client(self) -> T:
        if self._client is None:
            self._client = self._client_factory()

        return self._client


class EagerClientProxy(IClientProxy[T]):
    """
    Generates client immediately.
    """

    _client: T

    def __init__(self, client_factory: Callable[[], T]) -> None:
        self._client = client_factory()

    def get_client(self) -> T:
        return self._client


proxy_implementation: type[IClientProxy]
if is_running_in_lambda_environment():
    logger.info("Using lazy client proxy implementation")
    proxy_implementation = LazyClientProxy
else:
    proxy_implementation = EagerClientProxy


facts_v1_client: IClientProxy[FactsV1ClientType] = proxy_implementation(create_facts_client)
facts_v2_client: IClientProxy[FactsV2ClientType] = proxy_implementation(create_facts_client_v2)
copilot_v3_client: IClientProxy[CopilotV3ClientType] = proxy_implementation(create_copilot_client)
new_copilot_v3_client: IClientProxy[NewCopilotV3ClientType] = proxy_implementation(create_new_copilot_client)
ers_v3_client: IClientProxy[ErsV3ClientType] = proxy_implementation(create_ers_client)
lambda_client: IClientProxy[LambdaClientType] = proxy_implementation(create_lambda_client)
sqs_client: IClientProxy[SQSClientType] = proxy_implementation(create_sqs_client)
s3_client: IClientProxy[S3ClientType] = proxy_implementation(create_s3_client)
redis_cache_client: IClientProxy[RedisCacheClientType] = proxy_implementation(create_redis_cache_client)
rts_v1_client: IClientProxy[RTSV1ClientType] = LazyClientProxy(create_rts_v1_client)  # it is only used in rts lambdas
