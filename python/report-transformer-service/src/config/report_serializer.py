from uuid import UUID

from report_serializer.config.config import SerializerConfig
from report_serializer.config.insureds_config import InsuredNameType
from report_serializer.config.kalepa_api_clients import KalepaApiClients
from report_serializer.config.serialization_main_config import EntitySource


def get_report_serializer_config(
    submission_id: UUID | str, organization_id: int, api_clients: KalepaApiClients
) -> SerializerConfig:
    serializer_config = get_report_serializer_config_lite(submission_id, organization_id, api_clients)

    # TODO(ENG-27117): Set this dynamically based on the export requirements
    serializer_config.facts_config.set_premise_facts = True
    serializer_config.facts_config.create_virtual_structure_from_premises_if_structure_facts_missing = True
    serializer_config.facts_config.set_structure_facts = True
    serializer_config.license_config.set_license_details = True
    serializer_config.facts_config.populate_missing_facts_from_premises_to_structures = True
    serializer_config.facts_config.set_report_facts = True
    serializer_config.facts_config.set_business_facts = True
    serializer_config.facts_config.set_vehicle_facts = True
    serializer_config.facts_config.set_driver_facts = True
    serializer_config.submission_general_config.set_file_details = True
    serializer_config.submission_general_config.populate_received_date_from_created_date_if_missing = True
    serializer_config.insureds_config.name_type_priority = [
        InsuredNameType.DBA,
        InsuredNameType.LEGAL,
        InsuredNameType.REQUESTED,
        InsuredNameType.PUBLIC_VERIFIED,
    ]
    serializer_config.osha_config.set_inspection_details = True
    serializer_config.product_recalls_config.set_product_recalls_details = True
    serializer_config.submission_general_config.fetch_email_body = True
    serializer_config.submission_general_config.set_wc_experience = True
    serializer_config.submission_general_config.set_wc_state_rating = True

    return serializer_config


def get_conifer_export_serializer_config(
    submission_id: UUID | str, organization_id: int, api_clients: KalepaApiClients
) -> SerializerConfig:
    serializer_config = get_report_serializer_config_lite(submission_id, organization_id, api_clients)
    serializer_config.facts_config.set_business_facts = False
    serializer_config.facts_config.set_structure_facts = False
    serializer_config.facts_config.set_equipment_facts = False
    serializer_config.submission_general_config.populate_received_date_from_created_date_if_missing = True
    serializer_config.insureds_config.name_type_priority = [
        InsuredNameType.DBA,
        InsuredNameType.LEGAL,
        InsuredNameType.REQUESTED,
        InsuredNameType.PUBLIC_VERIFIED,
    ]
    serializer_config.news_config.set_news_details = False
    serializer_config.news_config.set_summary = False
    serializer_config.losses_config.set_losses_details = False
    serializer_config.submission_general_config.set_naics_description = False
    serializer_config.submission_general_config.set_naics_display_name = False
    return serializer_config


def get_report_serializer_config_lite(
    submission_id: UUID | str, organization_id: int, api_clients: KalepaApiClients
) -> SerializerConfig:
    serializer_config = SerializerConfig()
    serializer_config.kalepa_api_clients = api_clients

    serializer_config.serialization_main_config.submission_id = submission_id
    serializer_config.serialization_main_config.organization_id = organization_id
    serializer_config.serialization_main_config.report_source = EntitySource.KALEPA_API_CALL
    serializer_config.serialization_main_config.submission_source = EntitySource.KALEPA_API_CALL

    return serializer_config
