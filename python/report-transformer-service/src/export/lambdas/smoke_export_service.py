import os

from common.clients.copilot_v3_client import CopilotV3Client
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from report_transformer_service_client.models.report_export_generation_request import (
    ReportExportGenerationRequest,
)
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from slack_sdk import WebClient
from sqlalchemy.orm.session import Session
from static_common.enums.organization import ExistingOrganizations
import sentry_sdk

from src.clients.app import new_copilot_v3_client
from src.dependencies.db import LambdaDbSessionProvider
from src.export.logic.export_schedule_generation import (
    schedule_export_generation_using_rts_api,
)
from src.export.logic.export_smoke_tests import process_export_generations
from src.export.logic.slack_messages import SlackMessages
from src.export.models.export_schedule_generation import ExportScheduleGeneration
from src.export.models.predefined_export import PredefinedExport  # noqa
from src.views.models.view import ViewDbModel  # noqa

LIMIT = 5

DEFAULT_ORG_IDS = [ExistingOrganizations.AdmiralInsuranceGroup.value, ExistingOrganizations.BishopConifer.value]


logger = get_logger()

sentry_sdk.init(
    os.environ.get("SENTRY_DSN"),
    integrations=[AwsLambdaIntegration(timeout_warning=True)],
    environment=os.environ.get("KALEPA_ENV", "dev"),
)


def _create_slack_client() -> WebClient:
    return WebClient(os.getenv("SLACK_TOKEN", ""))


def _get_slack_messages_client(session: Session) -> SlackMessages:
    return SlackMessages(_create_slack_client(), session)


def _get_capi_client() -> CopilotV3Client:
    return new_copilot_v3_client.get_client()


@bind_lambda_logging_context
def perform_smoke_tests_for_report_exports(
    event: dict, context: dict | None = None
) -> list[tuple[str, str | None, int]]:
    with LambdaDbSessionProvider() as db_session:
        slack_client = _get_slack_messages_client(db_session)
        capi_client = _get_capi_client()

        exports_schedule_generation: ExportScheduleGeneration = schedule_export_generation_using_rts_api(
            db_session, LIMIT, capi_client, slack_client, DEFAULT_ORG_IDS
        )
        tries = exports_schedule_generation.total
        errors_schedules = exports_schedule_generation.failed_schedules
        generation_requests: list[ReportExportGenerationRequest] = exports_schedule_generation.requests

        successes, fails, errors_process = process_export_generations(db_session, generation_requests, slack_client)

    slack_client.send()
    logger.info(
        "Smoke tests finished", tries=tries, successes=successes, fails=fails, errors=errors_schedules + errors_process
    )

    return [(request.generation_id, request.report_id, request.organization_id) for request in generation_requests]
