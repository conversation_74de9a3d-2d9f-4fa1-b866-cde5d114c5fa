from uuid import UUID

from sqlalchemy import case, func
from sqlalchemy.orm import Session

from src.export.models.export_generation_batch import ExportBatchGenerationStatusDTO
from src.export.models.report_generation import ReportExportGenerationDbModel
from src.export.models.report_generation_status import ReportGenerationStatus


def _maybe_enrich_with_page_info(
    export_batch_generation_status_dto: ExportBatchGenerationStatusDTO,
    batch_id: UUID,
    db_session: Session,
) -> ExportBatchGenerationStatusDTO:
    """
    Enrich the batch status DTO with pagination information.
    """
    if not export_batch_generation_status_dto.is_finished:
        return export_batch_generation_status_dto

    generation_db_models: list[ReportExportGenerationDbModel] = (
        db_session.query(ReportExportGenerationDbModel)
        .filter(ReportExportGenerationDbModel.batch_id == batch_id)
        .order_by(ReportExportGenerationDbModel.created_at.desc(), ReportExportGenerationDbModel.id.desc())
        .limit(export_batch_generation_status_dto.page_size)
        .offset((export_batch_generation_status_dto.page_number - 1) * export_batch_generation_status_dto.page_size)
        .all()
    )

    export_batch_generation_status_dto.reports = [
        generation_db.get_export_dto() for generation_db in generation_db_models
    ]

    return export_batch_generation_status_dto


def get_batch_status(
    db_session: Session,
    batch_id: UUID,
    page_number: int = 1,
    page_size: int = 20,
) -> ExportBatchGenerationStatusDTO:
    """
    Get the status of a batch export generation request.
    """
    status_query = (
        db_session.query(
            func.sum(
                case(
                    (ReportExportGenerationDbModel.status.in_(ReportGenerationStatus.pending_statuses()), 1),
                    else_=0,
                ).label("pending_count"),
            ),
            func.sum(
                case(
                    (ReportExportGenerationDbModel.status.in_(ReportGenerationStatus.finished_statuses()), 1),
                    else_=0,
                ).label("finished_count"),
            ),
            func.count(ReportExportGenerationDbModel.id).label("total_count"),
        )
        .filter(ReportExportGenerationDbModel.batch_id == batch_id)
        .one()
    )

    pending_count, finished_count, total_count = status_query

    batch_status_dto = ExportBatchGenerationStatusDTO(
        total_exports=total_count or 0,
        finished_exports=finished_count or 0,
        page_number=page_number,
        page_size=page_size,
    )

    return _maybe_enrich_with_page_info(batch_status_dto, batch_id, db_session)
