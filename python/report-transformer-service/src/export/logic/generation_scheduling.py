from uuid import UUID

from fastapi import HTTPException, status
from infrastructure_common.logging import get_logger
from report_transformer_service_client.models.export_generation_status_dto import (
    ExportGenerationStatusDTO,
)
from report_transformer_service_client.models.report_export_generation_request import (
    ReportExportGenerationRequest as RTSReportExportGenerationRequest,
)
from sqlalchemy import nullslast, or_
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger

from src.clients.app import copilot_v3_client, rts_v1_client, sqs_client
from src.clients.aws_sqs import SQSQueue
from src.dependencies.db import DBSession
from src.export.models.api.report_export import EnqueueExportGenerationRequest
from src.export.models.error import PredefinedExportNotFoundError
from src.export.models.export import ExportDbModel, ExportDefinitionStatus
from src.export.models.export_generation_batch import (
    EnqueueExportGenerationBatchRequest,
)
from src.export.models.predefined_export import PredefinedExport, PredefinedExportType
from src.export.models.report_generation import (
    ReportExportGenerationDbModel,
    ReportExportGenerationRequest,
    ReportExportGenerationRequestParams,
)
from src.export.models.report_generation_status import ReportGenerationStatus
from src.views.models.api.report_export import ReportExportFormat
from src.views.models.view import ViewDbModel

logger = get_logger()


def get_export_generation_params(
    request: EnqueueExportGenerationRequest, db_session: Session
) -> ReportExportGenerationRequestParams:
    log = logger.bind(request=request)

    target_view_id = (
        _find_target_view_id(db_session, log, requested_view_id=request.view_id, requested_view_name=request.view_name)
        if request.targets_view()
        else None
    )
    target_export_id = (
        _find_target_export_id(
            db_session,
            log,
            requested_export_id=request.export_id,
            requested_export_name=request.export_name,
            organization_id=request.organization_id,
        )
        if request.targets_export()
        else None
    )

    return ReportExportGenerationRequestParams(
        submission_id=request.submission_id,
        report_id=request.report_id,
        organization_id=request.organization_id,
        view_id=target_view_id,
        export_id=target_export_id,
        export_format=request.export_format,
    )


def get_export_generation_params_for_batch(
    request: EnqueueExportGenerationBatchRequest,
    db_session: DBSession,
) -> list[ReportExportGenerationRequestParams]:
    assert request.organization_id is not None

    target_export_id = _find_target_export_id(
        db_session,
        logger,
        requested_export_id=request.export_id,
        requested_export_name=request.export_name,
        organization_id=request.organization_id,
    )

    request_params = []
    for report_id in request.report_ids or []:
        request_params.append(
            ReportExportGenerationRequestParams(
                report_id=report_id,
                organization_id=request.organization_id,
                export_format=request.export_format,
                export_id=target_export_id,
            )
        )

    for submission_id in request.submission_ids or []:
        request_params.append(
            ReportExportGenerationRequestParams(
                submission_id=submission_id,
                organization_id=request.organization_id,
                export_format=request.export_format,
                export_id=target_export_id,
            )
        )

    return request_params


def _schedule_export_generation_using_params_sqs_based(
    requests: list[ReportExportGenerationRequest],
    is_batch: bool = False,
) -> None:
    serialized_requests = [request.model_dump(mode="json") for request in requests]

    sqs_client.get_client().send_messages(
        queue=SQSQueue.GENERATE_EXPORT_BATCH_SQS if is_batch else SQSQueue.GENERATE_EXPORT_SQS,
        messages=serialized_requests,
    )


def schedule_export_generation_using_params(
    requests_params: list[ReportExportGenerationRequestParams], db_session: DBSession, batch_id: UUID | None = None
) -> list[ReportExportGenerationRequest]:
    generation_db_models = [
        ReportExportGenerationDbModel(
            export_generation_request_params=params, status=ReportGenerationStatus.PENDING, batch_id=batch_id
        )
        for params in requests_params
    ]

    db_session.add_all(generation_db_models)
    db_session.commit()

    requests = [
        ReportExportGenerationRequest(
            generation_id=generation_db.id,
            **generation_db.export_generation_request_params.model_dump(),
        )
        for generation_db in generation_db_models
    ]

    try:
        _schedule_export_generation_using_params_sqs_based(requests, is_batch=batch_id is not None)
    except Exception as e:
        logger.exception("Failed to invoke report generation lambda")

        for db_model in generation_db_models:
            db_session.delete(db_model)
        db_session.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to invoke report generation lambda"
        ) from e

    return requests


def _find_views(
    view_id: UUID | None,
    view_name: str | None,
    db_session: DBSession,
) -> list[ViewDbModel]:
    filters = []
    if view_id:
        filters.append(ViewDbModel.id == view_id)
    if view_name:
        filters.append(ViewDbModel.name == view_name)

    if not filters:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No view filters provided")

    return db_session.query(ViewDbModel).filter(*filters).all()


def _find_target_view_id(
    db_session: DBSession,
    log: BoundLogger,
    requested_view_id: UUID | None = None,
    requested_view_name: str | None = None,
) -> UUID:
    views = _find_views(requested_view_id, requested_view_name, db_session)
    log = log.bind(requested_view_id=requested_view_id, requested_view_name=requested_view_name)

    if not views:
        log.info("No views found for request")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No views found")
    if len(views) > 1:
        log.warning("Multiple views found for request")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Multiple views found")

    view_id = views[0].id
    log.info("Scheduling export generation using view", view_id=view_id)
    return view_id


def _find_exports(
    export_id: UUID | None,
    export_name: str | None,
    organization_id: int | None,
    db_session: DBSession,
    find_only_published: bool,
) -> list[ExportDbModel]:
    filters = []
    if export_id:
        filters.append(ExportDbModel.id == export_id)
    if export_name:
        filters.append(ExportDbModel.name == export_name)

    if not filters:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No export filters provided")

    filters.append(
        or_(
            ExportDbModel.organization_id == organization_id,
            ExportDbModel.organization_id.is_(None),
        )
    )

    exports_query = db_session.query(ExportDbModel).filter(*filters)
    exports_query = exports_query.order_by(ExportDbModel.organization_id.desc().nullslast())

    found_exports = exports_query.all()
    if find_only_published:
        found_exports = [exp for exp in found_exports if exp.status == ExportDefinitionStatus.PUBLISHED]
    return found_exports


def _find_target_export_id(
    db_session: DBSession,
    log: BoundLogger,
    requested_export_id: UUID | None,
    requested_export_name: str | None,
    organization_id: int | None,
) -> UUID:
    exports = _find_exports(
        export_id=requested_export_id,
        export_name=requested_export_name,
        organization_id=organization_id,
        db_session=db_session,
        find_only_published=True,
    )
    log = log.bind(
        requested_export_id=requested_export_id,
        requested_export_name=requested_export_name,
        organization_id=organization_id,
    )

    if not exports:
        log.info("No exports found for request")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No published exports found")
    export_id = exports[0].id
    log.info("Scheduling export generation using export", export_id=export_id)
    return export_id


def get_request_for_scheduling_predefined_export(
    report_id: UUID,
    organization_id: int | None,
    export_type: PredefinedExportType,
    db_session: DBSession,
) -> EnqueueExportGenerationRequest:
    predefined_export: PredefinedExport | None = (
        db_session.query(PredefinedExport)
        .filter(PredefinedExport.predefined_export_type == export_type)
        .filter(or_(PredefinedExport.organization_id == organization_id, PredefinedExport.organization_id.is_(None)))
        .order_by(nullslast(PredefinedExport.organization_id.desc()))
        .first()
    )

    if not predefined_export:
        raise PredefinedExportNotFoundError(export_type, organization_id)

    copilot_client = copilot_v3_client.get_client()
    report = copilot_client.get_report(report_id=str(report_id), expand="submissions,organization_id")
    if not report:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Report(id={report_id}) not found")

    if report.organization_id != organization_id:
        logger.error(
            "Organization ID mismatch for GET report - RTS, not urgent but good to check",
            report_id=report_id,
            organization_id=organization_id,
            export_type=export_type,
        )
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You don't have access to this report")

    return EnqueueExportGenerationRequest(
        submission_id=UUID(report.submissions[0].id),
        organization_id=organization_id,
        export_format=ReportExportFormat.JSON,
        export_id=predefined_export.export_id,
    )


def schedule_export_generation_with_rts_client(
    organization_id: int, export_format: ReportExportFormat, report_id: str, export_id: str
) -> RTSReportExportGenerationRequest:
    rts_client = rts_v1_client.get_client()
    return rts_client.schedule_async_export_generation(
        organization_id=organization_id,
        export_format=export_format,
        report_id=report_id,
        export_id=export_id,
    )


def get_export_generation_status_from_rts_client(generation_id: str) -> ExportGenerationStatusDTO:
    rts_client = rts_v1_client.get_client()
    return rts_client.wait_until_export_generation_is_finished(generation_id)
