import random

from common.clients.copilot_v3_client import CopilotV3Client
from infrastructure_common.logging import get_logger
from report_transformer_service_client.models.report_export_generation_request import (
    ReportExportGenerationRequest,
)
from sqlalchemy.orm import Session

from src.export.logic.export_smoke_tests import (
    get_exports,
    get_report_id_for_organization,
)
from src.export.logic.generation_scheduling import (
    schedule_export_generation_with_rts_client,
)
from src.export.logic.slack_messages import SlackMessages
from src.export.models.export_schedule_generation import ExportScheduleGeneration
from src.views.models.api.report_export import ReportExportFormat

logger = get_logger()


FORMAT_MAP = {
    "Conifer - JSON export": ReportExportFormat.JSON,
    "Conifer - PDF export": ReportExportFormat.PDF,
    "Admiral - PDF export": ReportExportFormat.PDF,
    "Report Detail Endpoint": ReportExportFormat.JSON,
    "Admiral Batch Export": ReportExportFormat.JSON,
    "Conifer Batch Export": ReportExportFormat.JSON,
    "Secura PDF Export": ReportExportFormat.PDF,
}


def _get_export_format(export_name: str) -> ReportExportFormat:
    if export_name in FORMAT_MAP:
        return FORMAT_MAP[export_name]

    if "pdf" in export_name.lower():
        return ReportExportFormat.PDF

    return ReportExportFormat.JSON


def schedule_export_generation_using_rts_api(
    db_session: Session,
    limit: int,
    capi_client: CopilotV3Client,
    slack_client: SlackMessages,
    default_org_ids: list[int],
) -> ExportScheduleGeneration:
    exports = get_exports(db_session, limit)
    total = len(exports)
    errors = 0

    generation_requests: list[ReportExportGenerationRequest] = []

    for export in exports:
        org_id: int = int(export.organization_id) if export.organization_id else random.choice(default_org_ids)
        query_result = get_report_id_for_organization(org_id, capi_client)
        if not query_result:
            logger.error("No report found for the given organization ID", org_id=org_id)
            slack_client.add_error_message(organization_id=org_id, error="No report found")
            errors += 1
            continue
        rep_id: str = query_result

        try:
            generation_requests.append(
                schedule_export_generation_with_rts_client(
                    organization_id=org_id,
                    export_format=_get_export_format(export.name),
                    report_id=rep_id,
                    export_id=str(export.id),
                )
            )
        except Exception as e:
            logger.error("Failed to schedule export generation", error=str(e))
            slack_client.add_error_message(organization_id=org_id, report_id=rep_id, error=str(e))
            errors += 1

    return ExportScheduleGeneration(
        total=total,
        failed_schedules=errors,
        requests=generation_requests,
    )
