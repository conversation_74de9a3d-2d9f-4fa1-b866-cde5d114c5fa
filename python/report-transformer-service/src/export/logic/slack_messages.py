import os

from infrastructure_common.logging import get_logger
from slack_sdk import WebClient
from sqlalchemy.orm.session import Session

from src.utils import get_organization_pretty_name

logger = get_logger()


class SlackMessages:
    _fails: list[str] = []
    _errors: list[str] = []
    CHANNEL_ID = "C0542ET9ATE"
    FUCHS_ID = "U063N4JD2RF"

    def __init__(self, client: WebClient, session: Session):
        self._client = client
        self._fails = []
        self._errors = []
        self._session = session

    def add_fail_message(self, generation_id: str, report_id: str, organization_id: int, error_message: str) -> None:
        self._fails.append(
            f"Export generation {generation_id} failed for report {report_id} and organization"
            f" {get_organization_pretty_name(organization_id)} with error: {error_message}"
        )

    def add_error_message(self, organization_id: int, report_id: str | None = None, error: str | None = None) -> None:
        self._errors.append(
            f"Error occurred for organization {get_organization_pretty_name(organization_id)}"
            f" with report {report_id} and error: {error if error else 'Unknown error'}"
        )

    def send(self) -> None:
        env = os.environ.get("KALEPA_ENV", "dev")

        if not self._fails and not self._errors:
            logger.info("Smoke export service test passed, no errors or fails to report.")
            return
        if env == "dev":
            return
        mention = f"(<@{self.FUCHS_ID}>)" if env == "prod" else ""
        self._client.chat_postMessage(
            channel=self.CHANNEL_ID,
            attachments=[
                {
                    "mrkdwn_in": ["text"],
                    "color": "warning",
                    "pretext": f"[{env}] report-transformer-service - Smoke export service test failed {mention}",
                    "text": f"*[{env}] Smoke export service test failed*",
                    "fields": [
                        {
                            "title": "Fails",
                            "value": "\n".join(self._fails) if self._fails else "No fails",
                            "short": False,
                        },
                        {
                            "title": "Errors",
                            "value": "\n".join(self._errors) if self._errors else "No errors",
                            "short": False,
                        },
                    ],
                }
            ],
        )
        self._fails = []
        self._errors = []
