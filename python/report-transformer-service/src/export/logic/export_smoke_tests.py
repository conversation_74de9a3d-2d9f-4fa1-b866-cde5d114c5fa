from common.clients.copilot_v3_client import CopilotV3Client
from infrastructure_common.logging import get_logger
from report_transformer_service_client.models.report_export_generation_request import (
    ReportExportGenerationRequest,
)
from sqlalchemy import func
from sqlalchemy.orm import Session

from src.export.logic.generation_scheduling import (
    get_export_generation_status_from_rts_client,
)
from src.export.logic.slack_messages import SlackMessages
from src.export.models.export import ExportDbModel, ExportDefinitionStatus
from src.export.models.report_generation import ReportExportGenerationDbModel
from src.export.models.report_generation_status import ReportGenerationStatus

logger = get_logger()


def process_export_generations(
    db_session: Session,
    generation_requests: list[ReportExportGenerationRequest],
    slack_client: SlackMessages,
) -> tuple[int, int, int]:
    fails = 0
    successes = 0
    errors = 0

    for request in generation_requests:
        if not request.report_id:
            logger.error("Report ID is missing in the generation request", request=request)
            slack_client.add_error_message(
                organization_id=request.organization_id,
                error=f"Report ID is missing for generation {request.generation_id}",
            )
            errors += 1
            continue
        else:
            try:
                generation_status = get_export_generation_status_from_rts_client(
                    str(request.generation_id),
                )
                logger.info("Fetched export generation status", generation_status=generation_status)
                if generation_status.status.value == ReportGenerationStatus.ERROR:
                    error_message = get_error_message_for_export_generation_by_report_id(
                        session=db_session,
                        report_id=str(request.report_id),
                    )

                    slack_client.add_fail_message(
                        generation_id=str(generation_status.generation_id),
                        report_id=str(request.report_id),
                        organization_id=request.organization_id,
                        error_message=error_message,
                    )
                    fails += 1
                    logger.warn("Export generation failed", generation_id=request.generation_id, error=error_message)
                else:
                    successes += 1
            except Exception as e:
                logger.error("Failed to schedule generation or to fetch export generation status", error=str(e))
                slack_client.add_error_message(
                    organization_id=request.organization_id, report_id=str(request.report_id), error=str(e)
                )
                errors += 1

    return successes, fails, errors


def get_report_id_for_organization(org_id: int, capi_client: CopilotV3Client) -> str | None:
    try:
        response = capi_client.get_reports(
            page=1, per_page=1, organization_id=org_id, order_by="created_at", descending=True
        )
        return response.reports[0].id
    except Exception as e:
        logger.warning("Failed to fetch report ID for organization with REST api", org_id=org_id, exc_info=e)
        return None


def get_error_message_for_export_generation_by_report_id(session: Session, report_id: str) -> str:
    try:
        error = (
            session.query(ReportExportGenerationDbModel)
            .filter(
                ReportExportGenerationDbModel.status == "ERROR",
                ReportExportGenerationDbModel.export_generation_request_params["report_id"] == report_id,
            )
            .order_by(ReportExportGenerationDbModel.updated_at.desc())
            .all()
        )
        error_info = error[0].error_info
        if error_info:
            return error_info.error_details.message
        else:
            return "Unknown error"
    except Exception:
        return "Unknown error"


def get_exports(db_session: Session, limit: int = 5) -> list[ExportDbModel]:
    try:
        return (
            db_session.query(ExportDbModel)
            .filter(ExportDbModel.status == ExportDefinitionStatus.PUBLISHED.value)
            .order_by(func.random())
            .limit(limit)
            .all()
        )
    except Exception as e:
        logger.error("Failed to fetch exports", error=str(e))
        return []
