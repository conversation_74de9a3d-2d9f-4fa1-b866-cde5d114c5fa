from collections import Counter
from datetime import datetime
from uuid import UUID
import json

from infrastructure_common.logging import get_logger
from report_serializer.config.kalepa_api_clients import KalepaApiClients
from report_serializer.serializer import get_report_representation
from report_serializer.units_provider import UnitsProvider
from sqlalchemy.orm import Session
from static_common.enums.fact_subtype import FactSubtypeID

from src.clients.app import (
    copilot_v3_client,
    ers_v3_client,
    facts_v1_client,
    facts_v2_client,
    new_copilot_v3_client,
    s3_client,
)
from src.config.report_serializer import (
    get_conifer_export_serializer_config,
    get_report_serializer_config,
)
from src.export.models.export import ExportDbModel
from src.metrics import log_metrics
from src.ontology.models.client_mapping import (
    ClientOntologyMapping,
    ClientOntologyMappingDBModel,
)
from src.ontology.models.entity_type import EntityType
from src.ontology.ontology_provider import NaiveOntologyProvider
from src.utils import get_organization_pretty_snake_case_name
from src.views import OntologyViewDefinition, ViewDefinition
from src.views.models.api.report_export import (
    ExportType,
    JsonExport,
    PDFExport,
    RenderParams,
    ReportExportFormat,
)
from src.views.models.view import ViewDbModel
from src.views.rendering.config import RenderingConfigType
from src.views.rendering.core import IOntologyProvider, Renderer, RenderingContext
from src.views.rendering.json_renderer import JSONRenderer
from src.views.rendering.pdf_renderer.renderer import PDFRenderer

logger = get_logger()


class ViewOrExportNotFound(Exception):
    def __init__(self, view_id: UUID | None = None, export_id: UUID | None = None) -> None:
        if view_id:
            super().__init__(f"View with id {view_id} not found")
        elif export_id:
            super().__init__(f"Export with id {export_id} not found")
        self.view_id = view_id
        self.export_id = export_id


class ViewWithMultipleConfigTypesError(Exception):
    def __init__(self, config_counts: Counter[RenderingConfigType]) -> None:
        super().__init__("View has multiple rendering configs")
        self.config_counts = config_counts


class InvalidExportFormatRequestedError(Exception):
    def __init__(self, config_counts: Counter[RenderingConfigType], requested_format: ReportExportFormat) -> None:
        super().__init__("Invalid export format requested")
        self.config_counts = config_counts
        self.requested_format = requested_format


def _get_all_configs(view: ViewDefinition | OntologyViewDefinition) -> list[RenderingConfigType]:
    if isinstance(view, OntologyViewDefinition):
        return [view.rendering_config.config_type] if view.rendering_config else []

    configs = []
    for child in view.viewable_children:
        configs.extend(_get_all_configs(child))

    return configs


def _get_ontology_provider(view: ViewDefinition, session: Session) -> IOntologyProvider:
    provider = NaiveOntologyProvider(session)
    provider.initialize([view])
    return provider


def _get_units_provider() -> UnitsProvider:
    facts_v2_client_impl = facts_v2_client.get_client()
    fact_subtypes = facts_v2_client_impl.get_fact_subtypes(expand=["summary_configs"])
    fact_subtypes_mapping = {}

    for fact_subtype in fact_subtypes:
        parsed_fact_subtype_id = FactSubtypeID.try_parse_str(fact_subtype.id)
        if parsed_fact_subtype_id:
            fact_subtypes_mapping[parsed_fact_subtype_id] = fact_subtype

    return UnitsProvider(fact_subtypes_mapping)


def _get_sanitized_renderer(
    view: ViewDefinition, requested_export_format: ReportExportFormat, session: Session
) -> Renderer:
    configs = _get_all_configs(view)
    configs_with_count = Counter(configs)

    config_types = set(configs_with_count.keys())

    if len(config_types) > 1:
        raise ViewWithMultipleConfigTypesError(configs_with_count)

    config_type = config_types.pop() if config_types else None
    if requested_export_format == ReportExportFormat.JSON:
        if config_type and config_type != RenderingConfigType.JSON:
            raise InvalidExportFormatRequestedError(configs_with_count, requested_export_format)

        return JSONRenderer(_get_ontology_provider(view, session))
    elif requested_export_format == ReportExportFormat.PDF:
        return PDFRenderer(_get_ontology_provider(view, session), _get_units_provider())


def _get_ontology_values_mapping(session: Session, organization_id: int) -> dict[UUID, ClientOntologyMapping]:
    ontology_mappings = (
        session.query(ClientOntologyMappingDBModel)
        .filter(ClientOntologyMappingDBModel.organization_ids.contains([organization_id]))
        .all()
    )

    return {mapping.ontology_id: mapping.mappings for mapping in ontology_mappings}


def _store_pdf_export(render_request: RenderParams, pdf_export: bytes) -> PDFExport:
    s3_bucket_client = s3_client.get_client()

    pretty_org_name = get_organization_pretty_snake_case_name(render_request.organization_id)
    pretty_date = datetime.now().isoformat()
    file_name = f"{render_request.report_id or render_request.submission_id}_CopilotExport_{pretty_date}.pdf"

    pdf_export_key = f"pdf_export/{pretty_org_name}/{file_name}"

    s3_bucket_client.upload_file(
        key=pdf_export_key,
        file_content=pdf_export,
    )
    presigned_url = s3_bucket_client.generate_presigned_url(pdf_export_key)

    return PDFExport(
        s3_key=pdf_export_key,
        presigned_url=presigned_url,
    )


def _get_submission_id_from_params(
    params: RenderParams,
) -> UUID:
    if params.report_id:
        report = copilot_v3_client.get_client().get_report(str(params.report_id), expand="submissions")
        return UUID(report.submissions[0].id)

    if params.submission_id:
        return params.submission_id

    raise RuntimeError("No submission_id or report_id provided in RenderParams")


def render_view(
    render_request: RenderParams,
    session: Session,
) -> ExportType:
    view = None
    export_name = ""

    export_db_model: ExportDbModel | None = None
    if render_request.targets_view():
        logger.info("Rendering view with id %s", render_request.view_id)
        view_db_model = session.query(ViewDbModel).filter(ViewDbModel.id == render_request.view_id).one_or_none()
        export_name = view_db_model.name if view_db_model else ""
        logger.info("Rendering view", view_id=view_db_model.id if view_db_model else "None")
        if not view_db_model:
            raise ViewOrExportNotFound(view_id=render_request.view_id)
        view = view_db_model.definition
    elif render_request.targets_export():
        export_db_model = (
            session.query(ExportDbModel).filter(ExportDbModel.id == render_request.export_id).one_or_none()
        )
        export_name = export_db_model.name if export_db_model else ""
        logger.info(
            "Rendering export",
            export_id=export_db_model.id if export_db_model else "None",
            export_version=export_db_model.current_version if export_db_model else "None",
        )
        if not export_db_model:
            raise ViewOrExportNotFound(export_id=render_request.export_id)
        view = export_db_model.get_current_version(session).view.definition

    if not view:
        # Should not happen, but just in case
        raise RuntimeError(f"No view(id={render_request.view_id}) or export(id={render_request.export_id}) found")

    clients = KalepaApiClients(
        facts_v1=facts_v1_client.get_client(),
        copilot_v3=copilot_v3_client.get_client(),
        ers_v3=ers_v3_client.get_client(),
        facts_v2=facts_v2_client.get_client(),
        new_copilot_v3=new_copilot_v3_client.get_client(),
    )

    if export_db_model and export_db_model.name == "Conifer Batch Export":
        serializer_config = get_conifer_export_serializer_config(
            submission_id=_get_submission_id_from_params(render_request),
            organization_id=render_request.organization_id,
            api_clients=clients,
        )
    else:
        serializer_config = get_report_serializer_config(
            submission_id=_get_submission_id_from_params(render_request),
            organization_id=render_request.organization_id,
            api_clients=clients,
        )

    report_representation_start_time = datetime.now()
    report_repr = get_report_representation(
        serializer_config=serializer_config, call_origin="report-transformer-service"
    )
    report_representation_end_time = datetime.now()

    if not report_repr:
        raise RuntimeError("Failed to get report representation")

    renderer = _get_sanitized_renderer(view, render_request.export_format, session)
    rendering_context = RenderingContext(
        current_entity_type=EntityType.SUBMISSION,
        rendering_object=report_repr,
        ontology_values_mapping=_get_ontology_values_mapping(session, render_request.organization_id),
    )

    report_export_start_time = datetime.now()
    result_store = renderer.render_view_definition(view_definition=view, context=rendering_context)
    report_export_end_time = datetime.now()

    raw_export, export_format = result_store.serialize()

    log_metrics(
        "report representation time",
        (report_representation_end_time - report_representation_start_time).total_seconds(),
        render_request.submission_id,
        report_repr.report_id,
        export_name,
        metric_tags=[f"export_type:{export_format}", f"organization_id:{render_request.organization_id}"],
    )
    log_metrics(
        "report export time",
        (report_export_end_time - report_export_start_time).total_seconds(),
        render_request.submission_id,
        report_repr.report_id,
        export_name,
        metric_tags=[f"export_type:{export_format}", f"organization_id:{render_request.organization_id}"],
    )

    if export_format == ReportExportFormat.JSON:
        return JsonExport(json_export=json.loads(raw_export))
    elif export_format == ReportExportFormat.PDF:
        return _store_pdf_export(render_request, raw_export)

    raise NotImplementedError(f"Export format {export_format} not implemented")
