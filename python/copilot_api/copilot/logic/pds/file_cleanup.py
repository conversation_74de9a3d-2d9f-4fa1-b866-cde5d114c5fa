from dataclasses import replace

from file_processing.financial_statement_parser.phrases import (
    REGULAR_EXPRESSIONS_FINANCIAL,
)
from infrastructure_common.logging import get_logger
from static_common.constants import ORGS_TO_PROCESS_FINANCIAL_STATEMENTS
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityFieldID
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.file_type import FileType
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.business_resolution_data import BusinessResolutionData
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    SubmissionEntity,
)
from static_common.schemas.business_resolution_data import BusinessResolutionDataSchema
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.logic.onboarded_files_transformation import load_processed_data
from copilot.models import File, User, db
from copilot.models.files import LoadedProcessedFile, ProcessedFile

logger = get_logger()

NON_FLEET_FIELDS = [
    "loc #",
    "loc. #",
    "bldg. #",
    "store #",
    "bldg no.",
    "bldg#",
    "location #",
    "loc#",
    "loc",
    "loc no.",
    "#",
    "prem #",
    "loc.#",
    "loc. no.",
]
FIELDS_TO_REMOVE_BY_ENTITY_TYPE = {
    SubmissionEntityType.BUSINESS: NON_FLEET_FIELDS,
    SubmissionEntityType.STRUCTURE: NON_FLEET_FIELDS,
    SubmissionEntityType.VEHICLE: [
        "unit number",
        "unit #",
        "item #",
        "veh #",
        "equip #",
        "asset #",
        "truck_no",
        "vehicle #",
        "tag#",
        "insured id",
        "no.",
        "vehicle",
        "unit",
        "unit id#",
        "policy number",
        "vehicle number",
        "veh#",
        "veh no.",
        "trailer #",
        "veh. #",
        "insrd id no.",
        "item no.",
        "client veh#",
        "truck #",
        "covered auto no.",
        "insured veh#",
        "customer vehicle #",
        "customer #",
        "company vehicle #",
        "agy veh#",
        "unit no.",
        "vehicle info veh #",
        "no",
        "policy #",
        "equip. #",
        "item   #",
        "insured vehicle #",
    ],
    SubmissionEntityType.DRIVER: ["driver #", "#", "no.", "ssn", "last 4 of ss#", "driver no.", "driver#", "use veh #"],
}

FINANCIAL_FACTS_TO_KEEP = {FactSubtypeID.TOTAL_SALES, FactSubtypeID.PAYROLL}
FINANCIAL_FACTS = set(REGULAR_EXPRESSIONS_FINANCIAL.keys())
FINANCIAL_FACTS_TO_REMOVE = FINANCIAL_FACTS - FINANCIAL_FACTS_TO_KEEP


def _is_entity_eligible_for_removal(entity: BusinessResolutionData, file: File) -> bool:
    if file.is_document_ingestion:
        return False
    # We do not want to drop FNIs from ACORD 125s
    if (
        entity.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
        and file.classification == ClassificationDocumentType.ACORD_125
    ):
        return False
    # Remove not resolved named insured entities without an address
    if entity.named_insured and not entity.requested_address and not entity.entity_id:
        return True
    # Remove GARAGES that are not confirmed (as we don't want to show them in the UI)
    if entity.submission_entity_type == SubmissionEntityType.GARAGE and not entity.entity_id:
        return True
    return False


def _get_entity_idxs_to_remove(pf: LoadedProcessedFile, file: File) -> list[int]:
    brd: list[BusinessResolutionData] = pf.business_resolution_data or []
    entity_idxs_to_remove = []
    for item in brd:
        if _is_entity_eligible_for_removal(item, file):
            logger.info(
                "Entity was not resolved, so it will be removed.",
                requested_name=item.requested_name,
                requested_address=item.requested_address,
                named_insured=item.named_insured,
                file_id=str(file.id),
                classification=file.classification,
                is_autoconfirmed=item.is_autoconfirmed,
                submission_entity_type=item.submission_entity_type,
            )
            entity_idxs_to_remove.append(item.entity_idx)
    rejected_entities_idxs = [
        idx for idx, e in enumerate(pf.processed_data.entities) if e.type == SubmissionEntityType.REJECTED
    ]
    entity_idxs_to_remove.extend(rejected_entities_idxs)
    return entity_idxs_to_remove


def get_filtered_entities(
    entities: list[SubmissionEntity], entity_idxs_to_remove: list[int]
) -> tuple[list[SubmissionEntity], dict[int, int]]:
    entity_idx_map = {}
    cleaned_entities = []
    for idx, entity in enumerate(entities):
        if idx not in entity_idxs_to_remove:
            entity_idx_map[idx] = len(cleaned_entities)
            cleaned_entities.append(entity)
    return cleaned_entities, entity_idx_map


def _get_filtered_brd(
    brd: list[BusinessResolutionData], entity_idx_map: dict[int, int]
) -> list[BusinessResolutionData] | None:
    if not brd:
        return None
    cleaned_brd = []
    for item in brd:
        new_idx = entity_idx_map.get(item.entity_idx)
        if new_idx is not None:
            item.entity_idx = new_idx
            cleaned_brd.append(item)
    return cleaned_brd


def filter_out_values_for_removed_entities(fields: list[ResolvedDataField], entity_idx_map: dict[int, int]) -> None:
    for field in fields:
        cleaned_values = []
        for value in field.values:
            new_idx = entity_idx_map.get(value.entity_idx)
            if new_idx is not None:
                cleaned_values.append(replace(value, entity_idx=new_idx))
        field.values = cleaned_values


def update_entity_parent_idx(entities: list[SubmissionEntity], entity_idx_map: dict[int, int]) -> None:
    for entity in entities:
        # In theory, we shouldn't have structure to ONI, so I'm unsure what should happen with the children of removed
        # entity. That is why for the time being we set the parent to None and keep the entity.
        entity.parent_idx = entity_idx_map.get(entity.parent_idx)


def _drop_sov_labels(fields: list[ResolvedDataField]) -> list[ResolvedDataField]:
    fields_to_drop = [
        EntityFieldID.SPREADSHEET_LABEL,
        EntityFieldID.SPREADSHEET_SECONDARY_LABELS,
        EntityFieldID.SPREADSHEET_COMMENT,
        EntityFieldID.SPREADSHEET_SECONDARY_COMMENTS,
    ]
    return [f for f in fields if f.name not in fields_to_drop]


def _drop_entities_if_needed(pf: LoadedProcessedFile, file: File) -> None:
    entity_idxs_to_remove = _get_entity_idxs_to_remove(pf, file)
    cleaned_entities, entity_idx_map = get_filtered_entities(pf.processed_data.entities, entity_idxs_to_remove)
    update_entity_parent_idx(cleaned_entities, entity_idx_map)
    filter_out_values_for_removed_entities(
        pf.processed_data.fields + pf.processed_data.entity_information, entity_idx_map
    )
    pf.processed_data.entity_information = _drop_sov_labels(pf.processed_data.entity_information)
    pf.processed_data.entities = cleaned_entities
    pf.business_resolution_data = _get_filtered_brd(pf.business_resolution_data, entity_idx_map)


def _load_processed_file(file: File) -> LoadedProcessedFile | None:
    pf = ProcessedFile.query.filter(ProcessedFile.file_id == file.id).first()
    if not pf:
        logger.warning("ProcessedFile not found for file", file_id=str(file.id))
        return None
    processed_data = load_processed_data(pf)
    brd = (
        BusinessResolutionDataSchema().load(pf.business_resolution_data, many=True)
        if pf.business_resolution_data
        else None
    )

    return LoadedProcessedFile(processed_data=processed_data, business_resolution_data=brd)


def _persist_processed_file(loaded_pf: LoadedProcessedFile, file: File) -> None:
    pf = ProcessedFile.query.filter(ProcessedFile.file_id == file.id).first()
    pf.processed_data = LeanOnboardedFileSchema().dump(loaded_pf.processed_data)
    if loaded_pf.business_resolution_data:
        pf.business_resolution_data = {
            "resolution_data": BusinessResolutionDataSchema().dump(loaded_pf.business_resolution_data, many=True)
        }
    else:
        pf.business_resolution_data = None
    db.session.commit()


def clean_up(file: File) -> LoadedProcessedFile | None:
    pf = _load_processed_file(file)
    user_email = None
    if user := User.query.filter(User.id == file.user_id).first():
        user_email = user.email
    if not pf:
        return None
    if (
        file.classification in ClassificationDocumentType.drop_not_resolved_named_insureds_classifications()
        or file.classification == ClassificationDocumentType.VEHICLES_SPREADSHEET
        or file.classification == ClassificationDocumentType.SOV_SPREADSHEET
    ):
        _drop_entities_if_needed(pf, file)
    if file.is_document_ingestion:
        _persist_processed_file(pf, file)
        return pf
    remover = UnnecessaryFieldsRemover(pf, file)
    if FeatureFlagsClient.is_feature_enabled(FeatureType.DROP_UNNECESSARY_COLUMNS, user_email):
        remover.clean()
    else:
        remover.dry_clean()
    if FeatureFlagsClient.is_feature_enabled(FeatureType.DROP_FINANCIAL_FACTS, user_email):
        _drop_facts_from_non_nw_ml_subs(pf, file)
    _persist_processed_file(pf, file)
    return pf


class UnnecessaryFieldsRemover:
    def __init__(self, pf: LoadedProcessedFile, file: File):
        self.pf = pf
        self.file = file
        self.log = logger.bind(file_id=str(file.id), submission_id=str(file.submission_id))
        self.entity_parent_type_map = self._build_entity_parent_type_map(pf.processed_data)

    @staticmethod
    def _build_entity_parent_type_map(data: OnboardedFile) -> dict[int, SubmissionEntityType]:
        entity_parent_type_map = {}
        for idx, entity in enumerate(data.entities):
            entity_parent_type_map[idx] = entity.type
        return entity_parent_type_map

    def _should_keep_value(self, field_name: str, entity_idx: int | None) -> bool:
        if entity_idx is None:
            return True
        if not (entity_type := self.entity_parent_type_map.get(entity_idx)):
            self.log.warning("Entity type not found for entity", entity_idx=entity_idx)
            return True
        names_to_remove = FIELDS_TO_REMOVE_BY_ENTITY_TYPE.get(entity_type, [])
        return field_name.lower() not in names_to_remove

    def clean(self) -> None:
        for field_ in self.pf.processed_data.fields:
            field_.values = [value for value in field_.values if self._should_keep_value(field_.name, value.entity_idx)]
        dropped_fields = [f.name for f in self.pf.processed_data.fields if not f.values]
        if dropped_fields:
            self.log.info("Dropping unnecessary fields", fields=dropped_fields)
        self.pf.processed_data.fields = [f for f in self.pf.processed_data.fields if f.values]

    def dry_clean(self) -> None:
        new_fields = []
        for field_ in self.pf.processed_data.fields:
            new_field = replace(field_)
            new_field.values = [
                value for value in field_.values if self._should_keep_value(field_.name, value.entity_idx)
            ]
            new_fields.append(new_field)
        dropped_fields = [f.name for f in new_fields if not f.values]
        if dropped_fields:
            self.log.info("Dropping unnecessary fields", fields=dropped_fields)


def _drop_facts_from_non_nw_ml_subs(pf: LoadedProcessedFile, file: File):
    file_types_to_clean = {
        FileType.FINANCIAL_STATEMENT,
        FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
        FileType.SUPPLEMENTAL_FORM,
    }
    if file.organization_id in ORGS_TO_PROCESS_FINANCIAL_STATEMENTS or file.file_type not in file_types_to_clean:
        return
    for field_ in pf.processed_data.fields:
        field_.values = [value for value in field_.values if field_.fact_subtype_id not in FINANCIAL_FACTS_TO_REMOVE]
    dropped_fields = [f.name for f in pf.processed_data.fields if not f.values]
    if dropped_fields:
        logger.info(
            "Dropping financial fields for non ML sub",
            fields=dropped_fields,
            submission_id=file.submission_id,
            organization_id=file.organization_id,
            file_id=file.id,
        )
    pf.processed_data.fields = [f for f in pf.processed_data.fields if f.values]
