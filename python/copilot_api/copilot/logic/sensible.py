from collections import Counter
from dataclasses import asdict
from datetime import date, datetime
from io import Bytes<PERSON>
from typing import Any
from uuid import UUID, uuid4
import json
import os
import re

from file_processing.metrics.values_validation_metrics import (
    ValuesValidationMetricsProvider,
)
from fitz import Document
from flask import current_app
from infrastructure_common.logging import get_logger
from sqlalchemy import and_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Query, exc
from sqlalchemy.orm.exc import ObjectDeletedError, StaleDataError
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityFieldID, EntityInformation
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.file_metric import FileMetricName
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.iso_gl_code import ISOGLCode
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.sensible import SensibleStatus, SensibleUploadStatus
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.file_additional_info import (
    AcordClassificationInfo,
    FileAdditionalInfo,
)
from static_common.models.file_onboarding import BoundingBox, OnboardedFile
from static_common.schemas.file_additional_info import FileAdditionalInfoSchema
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
from static_common.taxonomies.industry_classification import SICCode
from werkzeug.datastructures import FileStorage
import fitz
import flask
import psycopg2
import requests

from copilot.acord_metrics import (
    ACORD_101_SCORING_CONFIG,
    ACORD_125_SCORING_CONFIG,
    ACORD_126_SCORING_CONFIG,
    ACORD_130_SCORING_CONFIG,
    ACORD_131_SCORING_CONFIG,
    ACORD_140_SCORING_CONFIG,
    ACORD_211_SCORING_CONFIG,
    ACORD_823_SCORING_CONFIG,
    ACORD_829_SCORING_CONFIG,
    APPLIED_98_SCORING_CONFIG,
    APPLIED_125_SCORING_CONFIG,
    APPLIED_126_SCORING_CONFIG,
    APPLIED_130_SCORING_CONFIG,
    OFAPPINFCNI_SCORING_CONFIG,
    OFSCHHAZ_SCORING_CONFIG,
)
from copilot.acord_metrics.acord_139_scorer_config import ACORD_139_SCORING_CONFIG
from copilot.acord_metrics.acord_160_scorer_config import ACORD_160_SCORING_CONFIG
from copilot.clients.response_configuration.sensible import (
    ACORD_101_CONFIG,
    ACORD_125_CONFIG,
    ACORD_126_CONFIG,
    ACORD_130_CONFIG,
    ACORD_131_CONFIG,
    ACORD_139_CONFIG,
    ACORD_140_CONFIG,
    ACORD_160_CONFIG,
    ACORD_211_CONFIG,
    ACORD_823_CONFIG,
    ACORD_829_CONFIG,
    APPLIED_98_CONFIG,
    APPLIED_125_CONFIG,
    APPLIED_126_CONFIG,
    APPLIED_130_CONFIG,
    OFSCHHAZ_CONFIG,
)
from copilot.clients.response_mappers import AcordMapper
from copilot.logic.acords import get_extraction_errors
from copilot.logic.files import put_file_metrics_internal
from copilot.logic.loss_runs.data_mapping import map_sensible_data_to_loss
from copilot.logic.loss_runs.deduplication import (
    count_duplicates,
    mark_duplicates_from_other_files,
    mark_duplicates_with_db_data,
    mark_duplicates_within_list,
)
from copilot.logic.loss_runs.policy_matching import extract_loss_run_policies
from copilot.logic.loss_runs.validity import is_valid_claim
from copilot.logic.submissions import extract_submission_level_data
from copilot.logic.workers_comp import save_premium_information, save_rating_information
from copilot.models import File, Loss, Submission, db
from copilot.models.acord_metrics.calculations import AcordCalculation
from copilot.models.acord_metrics.metrics import AcordScorer
from copilot.models.files import FileMetric, ProcessedFile
from copilot.models.losses import LossRunDocumentExtractionResult
from copilot.models.mappers import MappingResponse
from copilot.models.policy import LossPolicy
from copilot.models.sensible_cache import SensibleDocumentResponseCache
from copilot.models.sensible_calls import DAYS_OF_MONTH, SensibleCalls, SensibleQuota
from copilot.models.sensible_claim import (
    Acord130Form,
    AcordConfiguration,
    ExtractionErrorType,
    PageMetadata,
    SensibleClaim,
    SensibleConfiguration,
    SensibleDateValue,
    SensibleDocumentType,
)
from copilot.models.sensible_extraction import (
    LossRunSensibleExtractionDocument,
    SensibleExtraction,
    SensibleExtractionDocument,
    SensibleResponseExtraction,
)
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.schemas.sensible_claim import (
    Acord101Schema,
    Acord125Schema,
    Acord126Schema,
    Acord130Schema,
    Acord131Schema,
    Acord139FormSchema,
    Acord140Schema,
    Acord160FormSchema,
    Acord211Schema,
    Acord823Schema,
    Acord829Schema,
    Applied98Schema,
    Applied125Schema,
    Applied126Schema,
    Applied130Schema,
    OFSCHHAZSchema,
    SensibleClaimSchema,
    SensibleConfigurationSchema,
    SensibleDateValueSchema,
    SensibleDocumentTypeSchema,
    SensiblePolicySchema,
)
from copilot.schemas.sensible_extraction import SensibleResponseExtractionSchema
from copilot.utils import get_file_checksum_from_file_object

logger = get_logger()

sensible_response_schema = SensibleResponseExtractionSchema()

SENSIBLE_TIMEOUT_IN_SECONDS = 50
ONE_SECOND_IN_MS = 1000
ONE_MB = 1024 * 1024
ONE_HOUR_IN_SECONDS = 3600

policy_schema = SensiblePolicySchema()
date_schema = SensibleDateValueSchema()
document_type_schema = SensibleDocumentTypeSchema(many=True)
configuration_schema = SensibleConfigurationSchema(many=True)
file_additional_info_schema = FileAdditionalInfoSchema()

FACT_SUBTYPE_ID_TO_SUBMISSION_PROPERTY = {
    FactSubtypeID.SIC: (EntityInformation.SIC_CODE, SICCode),
    FactSubtypeID.GL_CODE: (EntityInformation.GL_CODE, ISOGLCode),
}


def save_sensible_extraction(body: dict, cache_hit: bool) -> SensibleExtraction | None:
    try:
        extraction: SensibleResponseExtraction = sensible_response_schema.load(body)
    except Exception:
        return None
    try:
        now = datetime.utcnow()
        to_save = (
            SensibleExtraction.query.filter(
                SensibleExtraction.extraction_id == extraction.id,
                SensibleExtraction.status != SensibleUploadStatus.CACHE_HIT,
            ).one_or_none()
            if not cache_hit
            else None
        )
        to_save = to_save or SensibleExtraction()
        to_save.created_at = now if cache_hit else extraction.created
        to_save.updated_at = now if cache_hit else extraction.completed
        to_save.submission_id = extraction.submission_id
        to_save.file_id = extraction.file_id
        to_save.status = SensibleUploadStatus.CACHE_HIT if cache_hit else SensibleUploadStatus.RESPONSE_RECEIVED
        to_save.extraction_id = extraction.id
        to_save.extraction_status = extraction.status
        to_save.number_of_pages = extraction.page_count
        to_save.extraction_documents = []

        for doc in extraction.documents or []:
            extraction_document = SensibleExtractionDocument()
            extraction_document.created_at = now if cache_hit else extraction.completed
            extraction_document.document_type = doc.document_type
            extraction_document.configuration = doc.configuration
            extraction_document.start_page = doc.start_page
            extraction_document.end_page = doc.end_page
            extraction_document.validations = doc.output.validations
            extraction_document.errors = doc.output.errors
            extraction_document.classification_summary = doc.output.classification_summary
            extraction_document.validation_summary = doc.output.validation_summary
            to_save.extraction_documents.append(extraction_document)

        db.session.add(to_save)
        db.session.commit()
        return to_save
    except (ObjectDeletedError, IntegrityError) as e:
        db.session.rollback()
        logger.warning(
            "Cannot save sensible extraction response because one of file/submission has ben deleted",
            file_id=extraction.file_id,
            submission_id=extraction.submission_id,
            exc_info=e,
        )
    except Exception:
        db.session.rollback()
        logger.exception("Cannot save sensible extraction response", submission_id=extraction.submission_id)
    return None


def save_into_sensible_extraction(
    submission_id: UUID,
    file_id: UUID,
    status: SensibleUploadStatus,
    extraction_id: UUID | None = None,
    upload_error: str | None = None,
) -> None:
    try:
        sensible_extraction = SensibleExtraction()
        sensible_extraction.submission_id = submission_id
        sensible_extraction.file_id = file_id
        sensible_extraction.status = status
        sensible_extraction.extraction_id = extraction_id
        sensible_extraction.upload_error = upload_error
        db.session.add(sensible_extraction)
        db.session.commit()
    except (exc.ObjectDeletedError, psycopg2.errors.ForeignKeyViolation, StaleDataError) as e:
        db.session.rollback()
        logger.warning("Cannot save Sensible Extraction", e=e, submission_id=submission_id, file_id=file_id)
    except:
        logger.exception("Cannot save Sensible Extraction")
        db.session.rollback()
    return None


def get_default_sensible_calls(organization_id: int, date: date) -> SensibleCalls:
    return SensibleCalls(
        id=uuid4(), year=date.year, month=date.month, day=date.day, organization_id=organization_id, calls_made=0
    )


def get_sensible_calls_query(organization_id: int, date: date) -> Query:
    return SensibleCalls.query.order_by(SensibleCalls.day.desc()).filter(
        and_(
            SensibleCalls.year == date.year,
            SensibleCalls.month == date.month,
            SensibleCalls.day <= date.day,
            SensibleCalls.organization_id == organization_id,
        )
    )


def update_sensible_calls(organization_id: int) -> None:
    query = get_sensible_calls_query(organization_id, date.today())
    if not (last_call := query.first()) or last_call.day != date.today().day:
        last_call = get_default_sensible_calls(organization_id, date.today())
    last_call.calls_made += 1
    db.session.add(last_call)
    db.session.commit()


def check_sensible_calls(organization_id: int) -> bool:
    query = get_sensible_calls_query(organization_id, date.today())
    call_history = query.all()

    calls = 0
    for sensible_call in call_history:
        calls += sensible_call.calls_made

    days_in_month = DAYS_OF_MONTH[date.today().month]
    # Calls should be spread out over the month, so quota applies proportionally. However, we allow the proportions
    # to be exceeded at the beginning of the month (if the first day of the month is Monday, there's a high
    # likelihood that more than 1/30 of quota would be used).
    monthly_quota = (
        db.session.query(SensibleQuota.quota).filter(SensibleQuota.organization_id == organization_id).scalar()
    )
    quota = monthly_quota * max(date.today().day, 5) / days_in_month
    return calls < quota


def cache_sensible_response(body: dict, file: File) -> None:
    # disable the cache on stage
    if os.environ.get("KALEPA_ENV", None) == "stage":
        return None
    try:
        _cache_sensible_response(body, file)
    except IntegrityError as e:
        db.session.rollback()
        if isinstance(e.orig, psycopg2.errors.UniqueViolation):
            logger.warning("Cached response already exists", file_id=file.id)
        else:
            logger.exception("Exception occurred while saving sensible response to the cache", file_id=file.id)
    except Exception:
        db.session.rollback()
        logger.exception("Exception occurred while saving sensible response to the cache", file_id=file.id)


def _remove_text_data(body: dict) -> dict:
    try:
        for doc in body["documents"]:
            output = doc["output"]
            if "text" in output:
                del output["text"]
    except KeyError:
        return body
    return body


def remove_bbox_data(body: dict) -> dict:
    def remove_key(elem: Any) -> dict:
        if isinstance(elem, dict):
            modified_dict = {}
            for key, item in elem.items():
                if key in ["lines", "valueConfidence", "anchorConfidence"]:
                    continue
                if isinstance(item, dict):
                    modified_dict[key] = remove_key(item)
                elif isinstance(item, list):
                    modified_dict[key] = [remove_key(x) for x in item]
                else:
                    modified_dict[key] = item
            return modified_dict
        return elem

    no_lines = remove_key(body)
    return _remove_text_data(no_lines)


def _cache_sensible_response(body: dict, file: File) -> None:
    # first delete the existing payload, because we do not want to update other submissions and files by mistake
    body["payload"] = None
    body["webhook"]["payload"] = None
    documents: list[dict] = body.get("documents", [])
    configurations = set()

    for document in documents:
        document_type = document.get("documentType", None)
        configuration = document.get("configuration", None)
        configurations.add(f"{document_type}#{configuration}")

    # check if we already have a response and log an error. This should reveal if we have any problems
    s3_file_checksum = current_app.submission_s3_client.get_file_checksum(file.s3_key)
    if not s3_file_checksum:
        logger.warning(
            "Cannot get file checksum from S3 while trying to update Sensible Cache, file might have been deleted",
            file_id=file.id,
            s3_key=file.s3_key,
        )
        return None

    file_hash = UUID(s3_file_checksum)

    existing: SensibleDocumentResponseCache = SensibleDocumentResponseCache.query.filter(
        SensibleDocumentResponseCache.id == file_hash
    ).one_or_none()

    if existing:
        logger.warning(
            "SensibleDocumentResponseCache already exists. This should not happen. Updating the existing one",
            existing_item_id=file_hash,
        )
        existing.response = body
        existing.configurations = list(configurations)
        if file.id not in existing.file_ids:
            existing.file_ids.append(file.id)
        db.session.add(existing)
        db.session.commit()
        return None

    if file.organization_id not in [
        ExistingOrganizations.KalepaTest.value,
        ExistingOrganizations.KalepaDemo.value,
        ExistingOrganizations.KalepaNewDemo.value,
        ExistingOrganizations.MarkelDemo.value,
    ]:
        try:
            body = remove_bbox_data(body)
        except Exception:
            logger.error("Failed to remove text data from Sensible response", file_id=str(file.id))

    to_insert = SensibleDocumentResponseCache()
    to_insert.id = file_hash
    to_insert.file_ids = [file.id]
    to_insert.response = body
    to_insert.configurations = list(configurations)
    db.session.add(to_insert)
    db.session.commit()


def check_duplicate_file(file: File) -> bool:
    if file.checksum:
        existing_file = File.query.filter(
            File.checksum == file.checksum, File.submission_id == file.submission_id
        ).first()
    else:
        if not file.size:
            return False
        existing_file = File.query.filter(
            File.size == file.size, File.name == file.name, File.submission_id == file.submission_id
        ).first()
    return existing_file is not None


def _upload_file(file_object: BytesIO, key: str) -> None:
    flask.current_app.submission_s3_client.upload_file(
        file_object, key, extra_args={"CacheControl": f"maxage={ONE_HOUR_IN_SECONDS}"}
    )


def _download_file(key: str | None) -> FileStorage:
    return flask.current_app.submission_s3_client.get_file_as_file_storage(key)


def get_page_ranges(documents: dict, file_id: UUID, page_length: int) -> dict[int, int]:
    page_ranges = dict()  # keys = start, values = end
    known_page_ranges = dict()
    for document in documents:
        start_page = document.get("startPage")
        end_page = document.get("endPage")
        if start_page is None or end_page is None:
            raise ValueError(f"Issue parsing start and end pages for file {file_id}")
        known_page_ranges[start_page] = end_page
    # Split the unknown pages
    last_end = max(known_page_ranges.values())
    first_start = min(known_page_ranges.keys())

    if first_start != 0:
        known_page_ranges[0] = first_start - 1  # First page to X needs to be added as a failure
    for start, end in known_page_ranges.items():
        new_start = end + 1
        if new_start in known_page_ranges or new_start == page_length:  # Next page is part of a document, or the end
            continue
        new_end = new_start
        while new_end + 1 not in known_page_ranges and new_end + 1 < page_length:
            new_end += 1
        if new_end > last_end:
            last_end = new_end
        page_ranges[new_start] = new_end
    if last_end < (page_length - 1):
        page_ranges[last_end] = page_length - 1  # To match 0 indexing like in Sensible response
    return {**known_page_ranges, **page_ranges}


def upload_failed_loss_run(file_metadata: File | None) -> None:
    try:
        now = file_metadata.created_at if file_metadata.created_at else datetime.utcnow()
        upload_path: str = (
            f'{now.strftime("%Y-%m-%d")}/{file_metadata.id}-{file_metadata.sensible_status}-{file_metadata.name}'
        )
        file_object: FileStorage = _download_file(file_metadata.s3_key)
        flask.current_app.failed_loss_run_s3_client.upload_file(
            file_object, upload_path, extra_args={"CacheControl": f"maxage={ONE_HOUR_IN_SECONDS}"}
        )
    except Exception:
        logger.exception("Failed to upload failed loss run", file_id=file_metadata.id)


def get_page_bbox_data(azure_res: dict, page_num: int) -> list[tuple[str, BoundingBox, float]]:
    if not azure_res.get("analyzeResult"):
        return []

    page_res = azure_res["analyzeResult"]["pages"][page_num]
    width = page_res["width"]
    height = page_res["height"]
    result = []
    for item in page_res["words"]:
        poly = item["polygon"]
        box = BoundingBox(
            xmin=min(poly[0::2]) / width,
            ymin=min(poly[1::2]) / height,
            xmax=max(poly[0::2]) / width,
            ymax=max(poly[1::2]) / height,
        )
        result.append((item["content"], box, item["confidence"]))
    return result


def get_page_metadata(text_data: dict, presigned_url: str | None = None) -> dict[int, PageMetadata]:
    azure_res = None
    if presigned_url:
        try:
            azure_res = flask.current_app.azure_fr_prebuilt_read_client.get_read_api_result(
                uploaded_file=presigned_url, file_type="url"
            )
        except Exception:
            logger.error("Failed to get OCR result")
    return {
        idx: PageMetadata(
            width=page_data["width"],
            height=page_data["height"],
            bbox_page_data=get_page_bbox_data(azure_res, idx) if azure_res else [],
        )
        for idx, page_data in enumerate(text_data["pages"])
    }


class SensibleUtils:
    def __init__(self, kalepa_key: str):
        self.kalepa_key = kalepa_key

    def authorize_response(self, payload: str) -> bool:
        return self.kalepa_key == payload.split("#")[-1]

    def _validate_document(self, document: dict) -> bool:
        if "validation_summary" not in document or document["validation_summary"]["errors"] > 0:
            logger.warning("Validations failed for Sensible document", document=document)
            return False
        return True

    def _get_org_from_key(self, key: str) -> str:
        return key.split("#")[0]

    def get_submission_from_key(self, key: str) -> UUID:
        return UUID(key.split("#")[1])

    def _get_file_id_from_key(self, key: str) -> UUID:
        return UUID(key.split("#")[2])

    def get_file_id(self, key: str) -> UUID:
        return self._get_file_id_from_key(key)

    @staticmethod
    def _get_file_sensible_status(led: LossRunSensibleExtractionDocument, is_parent: bool) -> SensibleStatus:
        has_not_loaded_claims = (led.invalid_claims and led.invalid_claims > 0) or (
            led.not_loadable_claims and led.not_loadable_claims > 0
        )
        has_duplicates = led.duplicated_claims and led.duplicated_claims > 0
        if has_not_loaded_claims and (led.extracted_claims or has_duplicates):
            sensible_status = SensibleStatus.PARTIALLY_COMPLETE
            log_msg = "Loss Run is Partially Complete"
        elif led.extracted_claims:
            sensible_status = SensibleStatus.COMPLETE
            log_msg = "Loss Run is Complete"
        elif has_duplicates:
            sensible_status = SensibleStatus.COMPLETE
            log_msg = "Loss Run is Complete with only duplicated claims"
        elif has_not_loaded_claims:
            sensible_status = SensibleStatus.INVALID
            log_msg = "Loss Run has only invalid claims."
        elif is_parent:
            # no losses, but it's a root file, which means it has already been in the classifier that said LR
            sensible_status = SensibleStatus.UNCERTAIN
            log_msg = "Loss Run is Uncertain"
        else:
            # no losses and it's a file split from root file -> NO_LOSSES, should be sent to classifier
            log_msg = "Loss Run has no losses and will be sent to classifier"
            sensible_status = SensibleStatus.NO_LOSSES
        logger.info(log_msg, file_id=str(led.file_id))
        return sensible_status

    def map_loss_run_data(
        self, loss_run_response: dict, file_id: UUID, extraction: SensibleExtraction
    ) -> tuple[list[Loss], list[LossPolicy], list[UUID]]:
        processed_losses = []
        processed_loss_policies = []
        webhook = loss_run_response["webhook"]
        organization_id = self._get_org_from_key(webhook["payload"])
        submission_id = self.get_submission_from_key(webhook["payload"])
        file = File.query.get_or_404(file_id, description="The file with specified ID wasn't found")
        split_files = self._split_into_documents(loss_run_response["documents"], file, FileType.LOSS_RUN)
        file.sensible_status = SensibleStatus.COMPLETE.value
        losses_in_whole_file: list[Loss] = []
        loss_policies_in_whole_file: list[LossPolicy] = []
        led_by_file_id: dict[file_id, LossRunSensibleExtractionDocument] = {}
        for document in loss_run_response["documents"]:
            if not (output := document.get("output")):
                continue
            start_page = document.get("startPage")
            if start_page not in split_files:
                continue
            new_file = split_files.get(start_page)
            extraction_doc = extraction.get_document_for_start_page(start_page)
            if extraction_doc:
                loss_run_extraction_doc = LossRunSensibleExtractionDocument(
                    id=extraction_doc.id,
                    file_id=new_file.id,
                    extracted_claims=0,
                    invalid_claims=0,
                    not_loadable_claims=0,
                    same_file_duplicated_claims=0,
                    duplicated_claims=0,
                )
                led_by_file_id[new_file.id] = loss_run_extraction_doc
                db.session.add(loss_run_extraction_doc)
            try:
                if not self._validate_document(output):
                    db.session.add(new_file)  # Add the misses to the table but no data to process
                    new_file.sensible_status = SensibleStatus.INVALID.value
                    db.session.commit()
                    upload_failed_loss_run(new_file)
                    continue

                file_losses, loss_policies, led = self._map_loss_run_data(
                    output,
                    new_file.id,
                    organization_id,
                    submission_id,
                    loss_policies_in_whole_file,
                )
                losses_in_whole_file += file_losses
                sensible_status = self._get_file_sensible_status(led, new_file.id == file.id)
                if doc := led_by_file_id.get(new_file.id):
                    doc.extracted_claims = led.extracted_claims
                    doc.not_loadable_claims = led.not_loadable_claims
                    doc.invalid_claims = led.invalid_claims
                    doc.same_file_duplicated_claims = led.same_file_duplicated_claims

                processed_losses.extend(file_losses)
                processed_loss_policies.extend(loss_policies)
                new_file.sensible_status = sensible_status.value

            except ValueError:
                logger.exception("Failed to process loss run", file_id=str(file_id))
                # In this case, one or more documents has data errors,
                # we treat it like a single file with a bug, keep the rest
                new_file.sensible_status = SensibleStatus.CANNOT_BE_PROCESSED.value
                file.sensible_status = SensibleStatus.CANNOT_BE_PROCESSED.value
                upload_failed_loss_run(new_file)
            except Exception as e:
                # Unexpected error, rollback the info and raise an error
                db.session.rollback()
                logger.warning("Caught exception mapping loss run data", exc=e)
                raise Exception
        for sub_file in split_files.values():
            if sub_file.sensible_status is None:  # Set the misses as failures
                sub_file.sensible_status = SensibleStatus.NO_DOCUMENTS_FOUND.value
                file.sensible_status = SensibleStatus.INVALID.value
            db.session.add(sub_file)
            db.session.commit()  # Add the sub_file to the files table
        file.processing_state = FileProcessingState.PROCESSED
        db.session.add(file)
        db.session.commit()

        if file.sensible_status == SensibleStatus.UNCERTAIN.value:
            upload_failed_loss_run(file)

        if processed_losses:
            mark_duplicates_from_other_files(processed_losses, full_duplicates_only=False)
            mark_duplicates_with_db_data(submission_id, processed_losses, full_duplicates_only=False)
            count_duplicates(processed_losses, led_by_file_id)

        return processed_losses, processed_loss_policies, [split_file.id for split_file in split_files.values()]

    def _map_loss_run_data(
        self,
        loss_runs: dict,
        file_id: UUID | None,
        organization_id: int,
        submission_id: UUID,
        loss_policies_in_whole_file: list[LossPolicy],
    ) -> tuple[list[Loss], list[LossPolicy], LossRunSensibleExtractionDocument]:
        result = loss_runs["parsedDocument"]
        configuration = loss_runs.get("configuration")
        report_generated_date = None
        try:
            if generated_date := result.get("report_generated_date"):
                report_generated_date = date_schema.load(generated_date)
        except Exception as e:
            logger.warning("Error processing report generated/valuation date", exc=e)

        page_text_data = loss_runs.get("text")
        page_metadata = get_page_metadata(page_text_data) if page_text_data else None

        extraction_result: LossRunDocumentExtractionResult = self._extract_loss_run_claims(
            file_id,
            organization_id,
            report_generated_date,
            result,
            submission_id,
            configuration,
            page_metadata=page_metadata,
        )
        loss_policies_in_sub_document = extract_loss_run_policies(
            file_id, loss_policies_in_whole_file, result, submission_id, organization_id, page_metadata=page_metadata
        )
        loss_run_extraction_doc = LossRunSensibleExtractionDocument(
            file_id=file_id,
            not_loadable_claims=extraction_result.not_loadable_claim_count,
            extracted_claims=len(extraction_result.losses),
            invalid_claims=extraction_result.invalid_claim_count,
            same_file_duplicated_claims=extraction_result.same_file_duplicate_count,
        )

        if not loss_policies_in_sub_document:
            logger.info("Sensible found no loss run policies", file_id=str(file_id))

        if not extraction_result.losses:
            logger.info(
                "Sensible found no new claims",
                file_id=str(file_id),
                invalid=loss_run_extraction_doc.invalid_claims,
                duplicated=loss_run_extraction_doc.duplicated_claims,
            )

        return extraction_result.losses, loss_policies_in_sub_document, loss_run_extraction_doc

    def _extract_loss_run_claims(
        self,
        file_id: UUID | None,
        organization_id: int,
        report_generated_date: SensibleDateValue | None,
        result: dict,
        submission_id: UUID,
        configuration: str | None,
        page_metadata: dict[int, PageMetadata] | None = None,
    ) -> LossRunDocumentExtractionResult:
        losses_in_sub_document: list[Loss] = []
        not_loadable_claim_count: int = 0
        invalid_claim_count: int = 0
        log = logger.bind(
            configuration=configuration,
            file_id=str(file_id),
            submission_id=str(submission_id),
            organization_id=organization_id,
        )
        for index, claim in enumerate(result.get("claims", [])):
            try:
                claim_data: SensibleClaim = SensibleClaimSchema(context={"logger": log}).load(claim)
            except Exception as e:
                not_loadable_claim_count += 1
                log.warning("Data quality check failed: Encountered unloadable claim", exc=e)
                continue
            if claim_data.claim_number is None:
                log.warning("Data quality check failed: Missing claim_number")

            loss, computed_total_amount_incurred, extracted_total_net_incurred = map_sensible_data_to_loss(
                data=claim_data,
                index=index,
                organization_id=organization_id,
                submission_id=submission_id,
                file_id=file_id,
                report_generated_date=report_generated_date,
                log=log,
                page_metadata=page_metadata,
            )

            if not is_valid_claim(loss, computed_total_amount_incurred, extracted_total_net_incurred, log):
                invalid_claim_count += 1
            else:
                losses_in_sub_document.append(loss)

        num_duplicates: int = mark_duplicates_within_list(losses_in_sub_document, full_duplicates_only=True)
        log.info(
            "Successfully extracted claims from configuration",
            count=len(losses_in_sub_document),
            num_duplicates=num_duplicates,
        )
        return LossRunDocumentExtractionResult(
            losses=losses_in_sub_document,
            not_loadable_claim_count=not_loadable_claim_count,
            invalid_claim_count=invalid_claim_count,
            same_file_duplicate_count=num_duplicates,
        )

    def _get_document_filename(self, filename: str, start_page: int, end_page: int) -> str:
        return filename[:-4] + f"_Pages_{start_page + 1}_{end_page + 1}.pdf"

    def _split_into_documents(
        self,
        documents: dict,
        file: File,
        file_type: FileType,
        processing_state: FileProcessingState = FileProcessingState.PROCESSED,
    ) -> dict[int, File]:
        # Documents is a dictionary created from Sensible response, has mixed types
        file_content = _download_file(file.s3_key)
        doc = fitz.open(stream=file_content.read(), filetype="pdf")
        page_ranges = get_page_ranges(documents, file.id, len(doc))
        if len(page_ranges.keys()) == 1:
            if page_ranges.get(0, -1) == (len(doc) - 1):  # Whole document is 1 file, just return the original
                return {0: file}
        return self._create_documents(page_ranges, file, doc, file_type, processing_state)

    def _create_documents(
        self,
        page_ranges: dict[int, int],
        file: File,
        doc: Document,
        file_type: FileType,
        processing_state: FileProcessingState,
    ) -> dict[int, File]:
        new_documents = dict()
        checksums = set()
        for start_page, end_page in page_ranges.items():
            new_document = fitz.open()
            try:
                new_document.insert_pdf(doc, from_page=start_page, to_page=end_page)
            except Exception as e:
                logger.warning("Cannot properly split file", from_page=start_page, to_page=end_page, exc_info=e)
                # Default to original file
                new_documents[start_page] = file
                continue

            name = self._get_document_filename(file.name, start_page, end_page)
            key = file.s3_key.split(file.name)[0] + name  # Use the same bucket
            bytes_ = BytesIO(new_document.write(no_new_id=True, garbage=2))
            checksum = get_file_checksum_from_file_object(FileStorage(bytes_))
            new_file = File(
                id=uuid4(),
                name=name,
                s3_key=key,
                file_type=file_type,
                classification=file.classification,
                user_id=file.user_id,
                processing_state=processing_state,
                organization_id=file.organization_id,
                submission_id=file.submission_id,
                parent_file_id=file.id,
                origin=file.origin,
                checksum=checksum,
            )
            if checksum in checksums or check_duplicate_file(new_file):
                logger.warning(
                    "Creating duplicate file when splitting parent", file_id=file.id, new_file_id=new_file.id
                )
                continue
            if new_file.checksum:
                checksums.add(new_file.checksum)
            _upload_file(bytes_, key)
            new_documents[start_page] = new_file
        return new_documents


class AcordUtils(SensibleUtils):
    def __init__(self, kalepa_key: str) -> None:
        super().__init__(kalepa_key)
        self.acord_mapper = AcordMapper()
        self.acord_configs: dict[str, AcordConfiguration] = {
            ClassificationDocumentType.ACORD_101.value: AcordConfiguration(Acord101Schema(), ACORD_101_CONFIG, False),
            ClassificationDocumentType.ACORD_125.value: AcordConfiguration(Acord125Schema(), ACORD_125_CONFIG, True),
            ClassificationDocumentType.ACORD_126.value: AcordConfiguration(Acord126Schema(), ACORD_126_CONFIG, False),
            ClassificationDocumentType.ACORD_130.value: AcordConfiguration(Acord130Schema(), ACORD_130_CONFIG, True),
            ClassificationDocumentType.ACORD_131.value: AcordConfiguration(Acord131Schema(), ACORD_131_CONFIG, True),
            ClassificationDocumentType.ACORD_140.value: AcordConfiguration(Acord140Schema(), ACORD_140_CONFIG, True),
            ClassificationDocumentType.ACORD_823.value: AcordConfiguration(Acord823Schema(), ACORD_823_CONFIG, True),
            ClassificationDocumentType.ACORD_829.value: AcordConfiguration(Acord829Schema(), ACORD_829_CONFIG, False),
            ClassificationDocumentType.APPLIED_98.value: AcordConfiguration(
                Applied98Schema(), APPLIED_98_CONFIG, False
            ),
            ClassificationDocumentType.APPLIED_130.value: AcordConfiguration(
                Applied130Schema(), APPLIED_130_CONFIG, True
            ),
            ClassificationDocumentType.ACORD_211.value: AcordConfiguration(Acord211Schema(), ACORD_211_CONFIG, False),
            ClassificationDocumentType.APPLIED_126.value: AcordConfiguration(
                Applied126Schema(), APPLIED_126_CONFIG, False
            ),
            ClassificationDocumentType.APPLIED_125.value: AcordConfiguration(
                Applied125Schema(), APPLIED_125_CONFIG, True
            ),
            ClassificationDocumentType.OFAPPINFCNI.value: AcordConfiguration(
                Applied125Schema(), APPLIED_125_CONFIG, True
            ),
            ClassificationDocumentType.OFSCHHAZ.value: AcordConfiguration(OFSCHHAZSchema(), OFSCHHAZ_CONFIG, False),
            ClassificationDocumentType.ACORD_160.value: AcordConfiguration(
                Acord160FormSchema(), ACORD_160_CONFIG, True
            ),
            ClassificationDocumentType.ACORD_139.value: AcordConfiguration(
                Acord139FormSchema(), ACORD_139_CONFIG, True
            ),
        }
        self.acord_scoring_configs: dict[str, AcordScorer] = {
            ClassificationDocumentType.ACORD_101.value: ACORD_101_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_125.value: ACORD_125_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_126.value: ACORD_126_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_130.value: ACORD_130_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_211.value: ACORD_211_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_823.value: ACORD_823_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_829.value: ACORD_829_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_131.value: ACORD_131_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_139.value: ACORD_139_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_140.value: ACORD_140_SCORING_CONFIG,
            ClassificationDocumentType.APPLIED_130.value: APPLIED_130_SCORING_CONFIG,
            ClassificationDocumentType.APPLIED_98.value: APPLIED_98_SCORING_CONFIG,
            ClassificationDocumentType.APPLIED_126.value: APPLIED_126_SCORING_CONFIG,
            ClassificationDocumentType.APPLIED_125.value: APPLIED_125_SCORING_CONFIG,
            ClassificationDocumentType.OFAPPINFCNI.value: OFAPPINFCNI_SCORING_CONFIG,
            ClassificationDocumentType.OFSCHHAZ.value: OFSCHHAZ_SCORING_CONFIG,
            ClassificationDocumentType.ACORD_FORM.value: ACORD_160_SCORING_CONFIG,
        }
        self.supported_acord_versions: dict[str, str] = {}
        for sa in ClassificationDocumentType.supported_acords():
            self.supported_acord_versions.update(
                {v.lower(): sa for v in ClassificationDocumentType[sa].supported_acord_versions}
            )

    def get_supported_versions(self) -> list[str]:
        return list(self.supported_acord_versions.keys())

    def get_acord_config_for_acord_version(self, acord_version: str) -> AcordConfiguration:
        config = self.acord_configs[self.supported_acord_versions[acord_version]]
        return config

    def get_acord_scorer_config_for_acord_version(self, acord_version: str) -> AcordScorer | None:
        config = self.acord_scoring_configs.get(self.supported_acord_versions[acord_version])
        return config

    def build_additional_data(self, configuration: str) -> dict:
        form_name = self.supported_acord_versions.get(configuration, "")
        return file_additional_info_schema.dump(
            FileAdditionalInfo(acord=AcordClassificationInfo(form_name=form_name, version_id=configuration.upper()))
        )

    @staticmethod
    def perform_acord_130_additional_processing(
        acord_data: Acord130Form, file: File, submission_id: UUID, mapped_response: MappingResponse
    ) -> None:
        if acord_data.rating_information:
            save_rating_information(acord_data.rating_information, file, submission_id)
        if acord_data.premium_information:
            fni_idx = mapped_response.raw_processed_data.fni_idx
            fni_name = None
            name_field = next(
                (
                    field
                    for field in mapped_response.raw_processed_data.entity_information
                    if field.name == EntityFieldID.NAME
                ),
                None,
            )
            if fni_idx is not None and name_field:
                fni_name = next((v.value for v in name_field.values if v.entity_idx == fni_idx), None)
            save_premium_information(acord_data.premium_information, file.id, submission_id, fni_name)

    def score_extraction(self, log, configuration: str, document_response: dict, file_id: UUID) -> FileMetric | None:
        scorer_config = self.get_acord_scorer_config_for_acord_version(configuration)
        if not scorer_config:
            return
        try:
            calculation: AcordCalculation = scorer_config.calculate(document_response)
        except:
            log.exception("Cannot calculate score")
            return None
        if not (
            metric := FileMetric.query.filter(
                FileMetric.file_id == file_id,
                FileMetric.metric_name == FileMetricName.ACORD_EXTRACTION_SCORE.value,
            ).one_or_none()
        ):
            metric = FileMetric(
                file_id=file_id,
            )
        metric.metric_name = FileMetricName.ACORD_EXTRACTION_SCORE
        metric.metric_value = calculation.score
        metric.score_calculation = asdict(calculation)
        db.session.add(metric)

        return metric

    @staticmethod
    def create_extraction_errors_metrics(body: dict, file_id: UUID) -> None:
        errors = get_extraction_errors(body, [], "")
        counter = Counter([error.error_type.value for error in errors])
        metrics: list[FileMetric] = FileMetric.query.filter(FileMetric.file_id == file_id).all()
        metric_type_to_metric = {metric.metric_name: metric for metric in metrics}
        for error_type, count in counter.items():
            metric = metric_type_to_metric.get(
                error_type,
                FileMetric(
                    file_id=file_id,
                    metric_name=error_type,
                ),
            )
            metric.metric_value = count
            db.session.add(metric)

    def map_acord_forms(self, body: dict, file: File, is_sensible: bool = True) -> list[File]:
        if is_sensible:
            split_files = self._split_into_documents(
                body["documents"],
                file,
                FileType.ACORD_FORM,
                FileProcessingState.PROCESSING,
            )
            submission_id = self.get_submission_from_key(body["webhook"]["payload"])
            organization_id = self._get_org_from_key(body["webhook"]["payload"])
        else:
            # in-house processed acord will always be a single document
            split_files = {0: file}
            submission_id = file.submission_id
            organization_id = file.submission.organization_id
        log = logger.bind(file_id=str(file.id), submission_id=str(submission_id), organization_id=str(organization_id))
        files = []
        for document in body["documents"]:
            document_type = document.get("documentType", None)
            configuration = document.get("configuration", None)

            if not document_type or not configuration or document_type != "acord_forms":
                log.error(
                    "Received acord response without document_type or configuration",
                    document_type=document_type,
                    configuration=configuration,
                )
                continue

            if configuration not in self.supported_acord_versions:
                log.error("Received invalid acord configuration. Check file classifier", configuration=configuration)
                continue

            if not (output := document.get("output")):
                continue

            start_page = document.get("startPage") if is_sensible else 0

            if start_page not in split_files:
                # this means that the newly created file was already created within this submission,
                # and it was most likely processed, or it is already processing
                continue

            new_file = split_files[start_page]
            new_file.additional_info = new_file.additional_info or self.build_additional_data(configuration)
            try:
                db.session.add(new_file)
                files.append(new_file)
                if is_sensible and not self._validate_document(output):
                    continue
                data = output.get("parsedDocument")
                page_text_data = output.get("text")
                file_url = file.presigned_url
                if file.organization_id not in [
                    ExistingOrganizations.KalepaTest.value,
                    ExistingOrganizations.KalepaDemo.value,
                    ExistingOrganizations.KalepaNewDemo.value,
                    ExistingOrganizations.MarkelDemo.value,
                ]:
                    file_url = None
                page_metadata = get_page_metadata(page_text_data, file_url) if page_text_data else None

                # Remove \u0000 as it's not supported by postgres since 9.4.1. It's important to do it before scoring,
                # so that we fail the fail if all values are rubbish.
                data = json.loads(re.sub(r"\\u0000.", "", json.dumps(data)))
                acord_configuration = self.get_acord_config_for_acord_version(configuration)

                metric = self.score_extraction(log, configuration, data, new_file.id)
                log.info(
                    "ACORD form document scored",
                    file_id=new_file.id,
                    configuration=configuration,
                    score=metric.metric_value if metric else None,
                    is_sensible=is_sensible,
                )

                status = SensibleStatus.COMPLETE.value
                schema = acord_configuration.schema
                mapping_config = acord_configuration.mapping_config
                acord_data = schema.load(data)

                mapping_result = self.acord_mapper.process_response(
                    mapping_configuration=mapping_config,
                    response_data=acord_data,
                    organization_id=int(organization_id),
                    submission_id=file.submission_id,
                    page_metadata=page_metadata,
                )
                mapping_result.raw_processed_data.files.append(file.id)

                if mapping_result.raw_processed_data.submission_entity_idx is None:
                    log.error("No submission entity in processed data and there should be at this point")

                if (
                    new_file.classification == ClassificationDocumentType.ACORD_130.value
                    and configuration.upper() in ClassificationDocumentType.ACORD_130.supported_acord_versions
                ):
                    self.perform_acord_130_additional_processing(acord_data, new_file, submission_id, mapping_result)

                if acord_configuration.should_contain_businesses and not any(
                    e for e in mapping_result.raw_processed_data.entities if e.type == SubmissionEntityType.BUSINESS
                ):
                    # we did not manage to extract any businesses, so we should flag this.
                    # this does not mean the extraction was wrong. Instead, we could have received a file
                    # that has the following value: "See attached SOVs" in the address field
                    data["extraction_error"] = ExtractionErrorType.NO_BUSINESSES_EXTRACTED

                if "extraction_error" in str(data):
                    status = SensibleStatus.PARTIALLY_COMPLETE.value
                    body["status"] = status

                new_file.sensible_status = status
                if not (processed_file := ProcessedFile.query.filter(ProcessedFile.file_id == new_file.id).first()):
                    processed_file = ProcessedFile(
                        file_id=new_file.id,
                    )
                onboarded_file_dict = LeanOnboardedFileSchema().dump(mapping_result.raw_processed_data)
                processed_file.raw_processed_data = onboarded_file_dict
                processed_file.processed_data = onboarded_file_dict
                self.create_extraction_errors_metrics(body, new_file.id)
                try:
                    metric_provider = ValuesValidationMetricsProvider()
                    metrics = metric_provider.get_metrics(
                        onb_file=mapping_result.raw_processed_data,
                        facts=current_app.facts_client_v2.get_fact_subtypes_as_dict(),
                    )
                    for m in metrics:
                        m.file_id = str(new_file.id)
                    # the file-processing metric calculator returns OpenAPI FileMetric, which we need to map
                    # to the CAPI model
                    metrics = [FileMetric(**m.to_dict()) for m in metrics]
                    put_file_metrics_internal(new_file.id, metrics)
                except Exception as e:
                    log.error("Failed to put file-metrics", exc=e, error=str(e))

                db.session.add(processed_file)
                db.session.commit()

                self._store_submission_level_data(new_file, mapping_result.raw_processed_data, file.submission)
            except Exception as e:
                # Unexpected error, rollback the info and raise an error
                db.session.rollback()
                log.exception("Caught exception mapping acord data")
                raise e
        return files

    def _store_submission_level_data(self, file: File, raw_process_data: OnboardedFile, submission: Submission):
        log = logger.bind(file_id=str(file.id), submission_id=str(submission.id))
        try:
            db.session.refresh(file)
            log.info("Storing submission level data for ACORD file")
            existing_data = SubmissionLevelExtractedData.query.filter(
                SubmissionLevelExtractedData.file_id == file.id,
                SubmissionLevelExtractedData.submission_id == submission.id,
            ).all()

            data = {dp.field: json.loads(dp.value) for dp in existing_data}
            existing_data = {dp.field: dp for dp in existing_data}

            sub_entity_idx = raw_process_data.submission_entity_idx
            for field in raw_process_data.entity_information:
                for value in field.values:
                    if value.entity_idx == sub_entity_idx:
                        data[field.name] = value.value

            fni_idx = raw_process_data.fni_idx
            for field in raw_process_data.fields:
                if field.fact_subtype_id in FACT_SUBTYPE_ID_TO_SUBMISSION_PROPERTY:
                    if fni_value := next((v.value for v in field.values if v.entity_idx == fni_idx), None):
                        field_name, enum = FACT_SUBTYPE_ID_TO_SUBMISSION_PROPERTY[field.fact_subtype_id]
                        data[field_name] = enum.try_parse_str(fni_value)

            if file.processed_file:
                processed_file_extracted_data = extract_submission_level_data(
                    file.processed_file, submission, allow_no_submission_entity=True
                )
                data.update(processed_file_extracted_data)

            new_submission_level_extracted_data = []
            for field, value in data.items():
                if field in existing_data.keys():
                    logger.info("Submission level field exists for ACORD file, updating value", field=field)
                    existing_data[field].value = json.dumps(value, default=str)
                else:
                    logger.info("New submission level field exists for ACORD file", field=field, value=value)
                    new_submission_level_extracted_data.append(
                        SubmissionLevelExtractedData(
                            submission_id=submission.id,
                            file_id=file.id,
                            field=field,
                            value=json.dumps(value, default=str),
                        )
                    )

            if new_submission_level_extracted_data:
                db.session.add_all(new_submission_level_extracted_data)

            db.session.commit()
        except Exception as e:
            log.exception("Failed to store submission level data for ACORD file", exc=e)
            db.session.rollback()


class SensibleConfigurationClient:
    def __init__(self, base_url: str, api_key: str, env: str):
        self._base_url = base_url
        self._env = "production" if env == "prod" else "development"
        self._headers = {"accept": "application/json", "authorization": f"Bearer {api_key}"}

    def get_extraction(self, extraction_id: str) -> dict | None:
        response = requests.get(f"https://api.sensible.so/v0/documents/{extraction_id}", headers=self._headers)
        response.raise_for_status()
        return response.json()

    def invalidate_cache(self) -> None:
        # gets configuration metadata from sensible and
        # deletes cache entries that have updated configuration
        document_types = self._get_document_types()

        for dt in document_types:
            configs = self._get_configurations(dt)
            for c in configs:
                self._process_configuration(dt, c)

    def get_configuration(self, document_type_name: str, configuration_name: str) -> SensibleConfiguration | None:
        document_types = self._get_document_types()

        if not (document_type := [dt for dt in document_types if dt.name == document_type_name]):
            logger.error("Document type not found", document_type=document_type_name)
            return None

        if not (configuration := self._get_configuration(document_type[0], configuration_name)):
            logger.error("Configuration not found", document_type=document_type_name, configuration=configuration_name)
            return None

        return configuration

    def _get_document_types(self) -> list[SensibleDocumentType]:
        url = f"{self._base_url}/document_types"

        try:
            response = requests.get(url, headers=self._headers)
            response.raise_for_status()
        except:
            logger.exception("Cannot get document types from Sensible")
            return []

        return document_type_schema.loads(response.text)

    def _get_configurations(self, document_type: SensibleDocumentType) -> list[SensibleConfiguration]:
        url = f"{self._base_url}/document_types/{document_type.id}/configurations"

        try:
            response = requests.get(url, headers=self._headers)
            response.raise_for_status()
        except Exception:
            logger.exception("Cannot get document configuration from Sensible", document_type=document_type.name)
            return []

        return configuration_schema.loads(response.text)

    def _get_configuration(
        self, document_type: SensibleDocumentType, configuration_name: str
    ) -> SensibleConfiguration | None:
        url = f"{self._base_url}/document_types/{document_type.id}/configurations/{configuration_name}"

        try:
            response = requests.get(url, headers=self._headers)
            response.raise_for_status()
        except Exception:
            logger.exception(
                "Cannot get document configuration from Sensible",
                document_type=document_type.name,
                configuration_name=configuration_name,
            )
            return None

        return SensibleConfigurationSchema().loads(response.text)

    def _process_configuration(self, document_type: SensibleDocumentType, config: SensibleConfiguration) -> None:
        last_updated = next((v.last_updated for v in config.versions or [] if self._env in v.environments), None)
        doc_type_config = f"{document_type.name}#{config.name}"
        if not last_updated:
            # configuration not deployed on this environment
            return None

        deleted_rows = SensibleDocumentResponseCache.query.filter(
            SensibleDocumentResponseCache.created_at < last_updated,
            SensibleDocumentResponseCache.configurations.contains([doc_type_config]),
        ).delete(synchronize_session="fetch")

        if deleted_rows:
            logger.info(
                "Deleted cached responses",
                deleted_rows=deleted_rows,
                document_type=document_type.name,
                config_name=config.name,
            )
            db.session.commit()

        return None
