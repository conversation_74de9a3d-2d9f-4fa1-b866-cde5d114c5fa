from copy import copy
from dataclasses import asdict, dataclass
from typing import Optional

from dataclasses_json import dataclass_json
from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy import func
from sqlalchemy.exc import IntegrityError
from static_common.enums.underwriters import SubmissionUserSource
from static_common.models.submission_events import (
    SubmissionUnderwritersChangedPayload,
    UnderwriterChange,
    UnderwriterChangeType,
)
import psycopg2

from copilot.logic.submissions import add_underwriter_to_submission
from copilot.logic.users.errors import (
    CrossOrgUserAssignmentError,
    MultipleUsersAssignmentError,
    UserAlreadyAssignedError,
    UserNotFoundError,
)
from copilot.models import Organization, ReportPermission, Submission, User, db
from copilot.models.reports import SubmissionUser
from copilot.models.types import PermissionType, SubmissionEvent

logger = get_logger()


@dataclass_json
@dataclass
class UnderwriterAssignerConfig:
    delete_other_assigned: bool = False
    should_share: bool | None = None
    raise_exceptions: bool = True
    # in some cases we don't want to throw an error when assigning already assigned UW - for example in autoassignment
    already_assigned_error: bool = True

    def merge(self, other: Optional["UnderwriterAssignerConfig"]) -> "UnderwriterAssignerConfig":
        if not other:
            return self
        if other.delete_other_assigned:
            self.delete_other_assigned = other.delete_other_assigned
        return self

    @staticmethod
    def determine_config(
        config: Optional["UnderwriterAssignerConfig"], default_config: "UnderwriterAssignerConfig"
    ) -> "UnderwriterAssignerConfig":
        config = config or default_config
        return copy(config.merge(default_config))


class BaseUnderwriterAssigner:
    _default_config = UnderwriterAssignerConfig()

    DEFAULT_SOURCE_PRIORITY = 0
    SOURCES_PRIORITY = {
        SubmissionUserSource.API: 6,
        SubmissionUserSource.MANUAL: 5,
        SubmissionUserSource.SYNC: 4,
        SubmissionUserSource.EMAIL: 3,
        SubmissionUserSource.RECOMMENDATIONS: 2,
        SubmissionUserSource.AUTO: 1,
    }

    # in the general case we are not able to automatically assign underwriters
    def auto_assign_underwriters(
        self, submission: Submission, config: UnderwriterAssignerConfig | None = None
    ) -> list[SubmissionUser]:
        return []

    def assign_underwriters(
        self,
        requested_user_ids: list[int],
        submission: Submission,
        sharing_permission: PermissionType | None = None,
        config: UnderwriterAssignerConfig | None = None,
        source: SubmissionUserSource | None = SubmissionUserSource.AUTO,
        change_log: list[UnderwriterChange] | None = None,
    ) -> list[SubmissionUser]:
        config = UnderwriterAssignerConfig.determine_config(config, self._default_config)

        organization = Organization.query.get_or_404(submission.organization_id)
        allow_multiple_uws_setting = (
            organization.settings.allow_multiple_assigned_underwriters_per_submission if organization.settings else True
        )

        if allow_multiple_uws_setting is None or allow_multiple_uws_setting == True:
            limit_to_one_uw = False
        else:
            limit_to_one_uw = True

        delete_other_assigned = config.delete_other_assigned if source != SubmissionUserSource.MANUAL else False

        logger.info(
            "Delete other assigned users setting",
            submission_id=submission.id,
            delete_other_assigned=delete_other_assigned,
        )

        if limit_to_one_uw:
            logger.info("Limit to one UW enforced for the submission", submission_id=submission.id)
            delete_other_assigned = True
            if len(requested_user_ids) > 1:
                # For AUTO source it's possible that we'll find more than 1 UW in the email body
                # we still want to handle such scenario
                if source == SubmissionUserSource.AUTO:
                    requested_user_ids = requested_user_ids[:1]
                elif config.raise_exceptions:
                    raise MultipleUsersAssignmentError(submission.id)
                else:
                    logger.error(
                        "Cannot assign more than one user to the submission",
                        submission_id=submission.id,
                        requested_user_ids=requested_user_ids,
                    )
                    return []

        new_submission_users: list[SubmissionUser] = []
        existing_submission_users: list[SubmissionUser] = []
        found_users = User.query.filter(User.id.in_(requested_user_ids)).all()
        found_user_ids = {u.id for u in found_users}

        for requested_user_id in requested_user_ids:
            if requested_user_id not in found_user_ids:
                if config.raise_exceptions:
                    raise UserNotFoundError(requested_user_id)
                else:
                    logger.error("Requested user was not found", user_id=requested_user_id)

        change_log = change_log or []
        for user in found_users:
            if user.cross_organization_access:
                if config.raise_exceptions:
                    raise CrossOrgUserAssignmentError(user.id)
                else:
                    logger.error("Cannot assign cross organization user to any submission", user_id=user.id)
                    return []

            existing_user = SubmissionUser.query.filter_by(submission_id=submission.id, user_id=user.id).first()
            if existing_user:
                if not _handle_already_assigned_user(existing_user, config):
                    return []
                existing_submission_users.append(existing_user)
            else:
                new_submission_users.append(
                    SubmissionUser(submission_id=submission.id, user_id=user.id, source=source, user=user)
                )
                change_log.append(
                    UnderwriterChange(
                        change_type=UnderwriterChangeType.ADDED,
                        source=source or SubmissionUserSource.AUTO,
                        underwriter_user_id=user.id,
                        underwriter_user_email=user.email,
                    )
                )

        unwanted_underwriters = []
        # we want to delete other submission_users if the flag is set to True and we found other users to assign
        # otherwise we might end up deleting all users and not assigning anyone
        if delete_other_assigned and (new_submission_users or existing_submission_users):
            unwanted_underwriters = [
                u for u in submission.assigned_underwriters if u.user_id not in requested_user_ids  # type: ignore
            ]
            # in case we want just one UW we want to make sure it's going to be highest prio one
            if unwanted_underwriters and limit_to_one_uw:
                new_user_source_priority = self.SOURCES_PRIORITY.get(source, self.DEFAULT_SOURCE_PRIORITY)
                max_assigned_users_source_priority = max(
                    self.SOURCES_PRIORITY.get(u.source, self.DEFAULT_SOURCE_PRIORITY)
                    for u in submission.assigned_underwriters
                )
                if max_assigned_users_source_priority > new_user_source_priority:
                    logger.warning(
                        "Higher priority UW already assigned, not assigning users",
                        submission_id=submission.id,
                        requested_user_ids=requested_user_ids,
                        source=source,
                        max_assigned_users_source_priority=max_assigned_users_source_priority,
                        new_user_source_priority=new_user_source_priority,
                    )
                    return []

            change_log.extend(self.remove_uws(unwanted_underwriters, submission))

        should_share = config.should_share
        if config.should_share is None:
            if submission.is_verification_required and not submission.is_verified:
                should_share = False
                logger.info("Not sharing submission with underwriters", submission_id=submission.id)
            else:
                should_share = True
                logger.info("Sharing submission with underwriters", submission_id=submission.id)
        else:
            logger.info(
                "Should_share is not none",
                submission_id=submission.id,
                should_share=should_share,
                config=config,
                default_config=self._default_config,
            )

        for submission_user in new_submission_users:
            add_underwriter_to_submission(
                submission, submission_user, should_share=should_share, sharing_permission=sharing_permission
            )

        # existing users at this point means they are aligned with the newly assigned source - we can update the source
        # for them to the highest priority of the two
        for submission_user in existing_submission_users:
            if (
                not submission_user.source
                or self.SOURCES_PRIORITY[source] > self.SOURCES_PRIORITY[submission_user.source]
            ):
                submission_user.source = source

        try:
            db.session.commit()
        except IntegrityError as e:
            db.session.rollback()
            if isinstance(e.orig, psycopg2.errors.UniqueViolation):  # type: ignore
                _handle_already_assigned_user(new_submission_users[-1], config)
                return []
            raise e
        except Exception as e:
            db.session.rollback()
            raise e

        # we only want to create an event if something actually changed i.e. new user was assigned or one was deleted
        if new_submission_users or (unwanted_underwriters and delete_other_assigned):
            uw_changed_payload = SubmissionUnderwritersChangedPayload(
                change_maker_user_id=current_user.id,
                change_maker_user_email=current_user.email,
                submission_id=submission.id,
                report_id=submission.reports[0].id,
                changes=change_log,
            )
            current_app.event_service.handle_submission_event(
                SubmissionEvent.SUBMISSION_USERS_UPDATED, submission, additional_data=asdict(uw_changed_payload)
            )

        return new_submission_users + existing_submission_users

    def remove_uws(self, users_to_remove: list[SubmissionUser], submission: Submission) -> list[UnderwriterChange]:
        change_log = []
        for underwriter in users_to_remove:
            change_log.append(
                UnderwriterChange(
                    change_type=UnderwriterChangeType.DELETED,
                    source=underwriter.source or SubmissionUserSource.AUTO,
                    underwriter_user_id=underwriter.user_id,
                    underwriter_user_email=underwriter.user.email,
                )
            )
            submission.assigned_underwriters.remove(underwriter)

        ids_to_remove = [u.user_id for u in users_to_remove]
        logger.info("Removing permissions", ids_to_remove=ids_to_remove, submission_id=submission.id)
        ReportPermission.query.filter(
            ReportPermission.report_id == submission.report_id,
            ReportPermission.grantee_user_id.in_(ids_to_remove),
        ).delete()

        return change_log

    def _get_user_id_by_email(self, email: str) -> int | None:
        user = User.query.filter(func.lower(User.email) == email.lower().strip()).first()
        return user.id if user else None


def _handle_already_assigned_user(submission_user: SubmissionUser, config: UnderwriterAssignerConfig) -> bool:
    if not config.already_assigned_error:
        return True

    if config.raise_exceptions:
        raise UserAlreadyAssignedError(submission_user.user_id, submission_user.submission_id)
    else:
        logger.warning(
            "User is already assigned to the submission",
            user_id=submission_user.user_id,
            submission_id=submission_user.submission_id,
        )
        return False
