[project]
name = "paragon_ims_api_client"
version = "25.5.26.23899.dev0"
description = "Paragon IMS API Client"
authors = [{ name = "Kalepa Tech" }]
requires-python = ">=3.11,<3.12"
readme = "README.md"
dependencies = [
    "zeep>=4.2.1,<5",
    "measurement==4.0a8",
    "lxml>=5.0.0,<6",
    "python-dateutil>=2.8.2,<3",
    "retrying>=1.3.3,<2",
    "xmltodict>=0.13.0,<0.14",
    "infrastructure-common",
]

[dependency-groups]
kalepa = ["common", "infrastructure-common", "static-common"]
dev = ["pytest>=7.2.0,<8"]

[tool.uv.sources]
common = { index = "kalepi" }
infrastructure-common = { index = "kalepi" }
static-common = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[tool.uv]
default-groups = ["kalepa", "dev"]

[tool.hatch.build.targets.sdist]
include = [
    "paragon_ims_api_client",
    "paragon_ims_api_client/cached_wsdls/paragon_ims_zeep_cache_sqlite.db",
]

[tool.hatch.build.targets.wheel]
include = [
    "paragon_ims_api_client",
    "paragon_ims_api_client/cached_wsdls/paragon_ims_zeep_cache_sqlite.db",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 120
target-version = ['py311']

[tool.isort]
profile = "black"
skip = ["__init__.py"]

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
line-length = 120
target-version = "py38"
extend-exclude = ["test/", "**/__init__.py", "playground.py"]
