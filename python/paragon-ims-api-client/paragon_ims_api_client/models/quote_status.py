from dataclasses import dataclass
from enum import Enum
from typing import Optional

from paragon_ims_api_client.models.utils import (
    convert_to_boolean,
    convert_value,
    filter_and_log_unknown_fields,
)


class QuoteStatus(Enum):
    """
    This is a set of possible quote status IDs as extracted from IMSQuoteStatuses from prod IMS.
    We should use this enum in our business operations.
    """

    SUBMITTED = 1
    QUOTED = 2
    BOUND = 3
    DECLINED = 4
    LOST = 5
    NOTICE_OF_CANCELLATION = 6
    PENDING_CANCELLATION = 7
    PENDING_REINSTATEMENT = 8
    UNBOUND_ENDORSEMENT = 9
    VOID_ENDORSEMENT = 10
    NOT_TAKEN_UP = 11
    CANCELLED = 12
    INCOMPLETE = 13
    VOID = 14
    LOST_ON_BORROWER = 15
    UNBOUND_CORRECTION = 16
    NOT_RENEWED = 17
    INDICATED = 25
    UNBOUND_NON_RENEWAL = 26
    UNBOUND_NON_RENEWAL_RESCINDED = 27
    NON_RENEWAL_RESCINDED = 28
    CREATE_SUPPORTING_LINES = 30
    PENDING_ADDITIONAL_INFORMATION = 101
    PRODUCER_REQUEST_TO_BIND = 102
    INSPECTION_REQUEST = 103
    ISSUE_UNDERWRITING_NOC = 104
    UNKNOWN1 = 131
    UNKNOWN = 132
    REFERRED_TO_CARRIER = 201
    WORKING = 202
    INTERNAL_REFERRAL = 203
    PENDING_REFERRAL_UPDATE = 204


class QuoteDeclinedStatusReason(Enum):
    """
    This is a set of possible quote status reason IDs as extracted from IMSQuoteStatuses from prod IMS.
    We should use this enum in our business operations.
    """

    POOR_LOSS_EXPERIENCE = 2
    CLASS_OF_BUSINESS = 3
    POOR_INSPECTION = 4
    POOR_FINANCIALS = 5
    NOT_ENOUGH_INFORMATION_PROVIDED = 6
    OTHER = 30
    BLOCKED_BY_BROKERAGE = 43
    BOR = 45
    NON_RENEWAL_UNDERWRITING_REASONS = 50
    NON_RENEWAL_DUE_TO_LOSSES = 51
    BELOW_MINIMUM_PREMIUM = 57
    BLOCKED_AT_MARKET = 58
    BLOCKED_AT_INSURANCE_CARRIER = 59
    INCOMPLETE = 60
    NO_MARKET_FOR_CLASS_OF_BUSINESS = 61
    INFORMATION_REQUESTED_NOT_PROVIDED = 62
    NOT_ENOUGH_TIME_TO_PROCESS = 63
    NOT_REVIEWED_BY_UW = 64
    POOR_CLASS_FIT = 65
    PREMIUM_TARGET_TOO_LOW_INDICATION_TOO_HIGH = 66
    REFERRED_AND_DECLINED_BY_CARRIER = 67
    RETRACTED_REQUESTED_TO_CLOSE_BY_BROKER = 68
    UNACCEPTABLE_CHANGE_IN_PAYROLL = 69
    UNACCEPTABLE_EXPOSURES = 70
    UNACCEPTABLE_FINANCIALS = 71
    UNACCEPTABLE_LOSS_MOD_HISTORY = 72
    UNACCEPTABLE_NEW_VENTURE_PRIOR_COVERAGE = 73
    UNACCEPTABLE_RISK_MANAGEMENT_CHARACTERISTICS = 74
    UNKNOWN_OTHER = 75
    NO_PRODUCER_AGREEMENT = 77
    BELOW_MINIMUM_PREM = 81
    CLASS_OF_BUSINESS_EXPOSURE = 88
    DOES_NOT_MEET_UW_GUIDELINES = 189
    INACTIVE_BROKER = 224
    FEIN_BLOCKED = 229
    PRODUCER_NOT_ELIGIBLE_FOR_CLASS_OF_BUSINESS = 230


class QuoteLostStatusReason(Enum):
    """
    This is a set of possible quote status reason IDs as extracted from IMSQuoteStatuses from prod IMS.
    We should use this enum in our business operations.
    """

    ACCOUNT_PREVIOUSLY_SUBMITTED = 1
    NOT_COMPETITIVE = 7
    LOST_TO_ANOTHER_BROKER = 8
    INSURED_SOLD_BUSINESS = 47
    REFERRAL_NOT_APPROVED = 48
    COVERAGE_NO_LONGER_NEEDED_OR_DESIRED = 52
    OTHER = 53
    AGENT_MOVED_TO_COMPETITOR = 82
    AGENT_LOST_ACCOUNT = 84
    NO_RESPONSE_FROM_BROKER = 191


class QuoteNotTakenUpStatusReason(Enum):
    """
    This is a set of possible quote status reason IDs as extracted from IMSQuoteStatuses from prod IMS.
    We should use this enum in our business operations.
    """

    NOT_COMPETITIVE = 9
    LOST_TO_ANOTHER_BROKER = 10
    NEEDED_COVERAGE_WE_COULD_NOT_PROVIDE = 11
    NO_RESPONSE_TO_REQUEST_FOR_ADDITIONAL_INFO = 12
    OUT_OF_BUSINESS = 46
    OTHER = 55
    CURRENT_CARRIER_RELATIONSHIP = 186
    COVERAGE_NO_LONGER_NEEDED = 187
    DUPLICATE_SUBMISSION = 188


@dataclass
class IMSQuoteStatus:
    """
    IMSQuoteStatus is a dataclass that represents the IMS defined quote status. There are hundreds of such IMS
    quote statuses because they seem to combine true status with reason (which is optional). For example
    there are about 50 "Declined" statuses with the same ID = 4 but with different reasons.
    """

    quote_status_id: int
    quote_status: str
    reason_required: Optional[bool] = False
    user_selectable: Optional[bool] = True
    reason: Optional[str] = None
    reason_id: Optional[int] = None
    status_event: Optional[str] = None
    reason_event: Optional[str] = None

    @staticmethod
    def parse(quote_status_dict: dict) -> "IMSQuoteStatus":
        filtered_data = filter_and_log_unknown_fields(
            raw_data=quote_status_dict, data_class=IMSQuoteStatus, prefix_for_logging="IMSQuoteStatus"
        )

        convert_value(filtered_data, "quote_status_id", int)
        convert_to_boolean(filtered_data, "reason_required")
        convert_to_boolean(filtered_data, "user_selectable")
        convert_value(filtered_data, "reason_id", int)

        return IMSQuoteStatus(**filtered_data)
