import json
import os
import uuid
from typing import Any

import pytest
from common.clients.azure import AzureCustomModelClient, AzureReadClient
from infrastructure_common.logging import get_logger
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_type import FileType

from src.models.classifiers.document_classifier.classified_file import ClassifiedFile
from src.models.classifiers.document_classifier.pdf_doc_classifier import (
    PDFDocumentClassifierV2,
)
from src.models.classifiers.document_classifier.processor import (
    DocumentSubmissionProcessor,
)
from src.models.classifiers.document_classifier.submission_document_classifier import (
    SubmissionDocumentClassifier,
)
from src.models.classifiers.document_classifier.utils import detect_document_type

logger = get_logger()

classifier = SubmissionDocumentClassifier(
    base_model_dir="data/models/submission_document_classifier",
    pdf_classifier=PDFDocumentClassifierV2(
        base_model_dir="data/models/pdf_classifier_v2",
        model_version="v2_with_quote",
        model_filename="model_210325.pkl",
        preprocessor_filename="preprocessor_210325.pkl",
        class_mapping={
            "acord": ClassificationDocumentType.ACORD_FORM,
            "auto_financial": ClassificationDocumentType.AUTO_DEALER_FINANCIAL_STATEMENT,
            "bor": ClassificationDocumentType.BROKER_OF_RECORD_LETTER,
            "budget": ClassificationDocumentType.BUDGET,
            "site_plan": ClassificationDocumentType.UNKNOWN,
            "project_schedule": ClassificationDocumentType.PROJECT_SCHEDULE,
            "corp_tree": ClassificationDocumentType.ORG_CHART,
            "cover_sheet": ClassificationDocumentType.COVER_SHEET,
            "directors_officers": ClassificationDocumentType.DIRECTORS_AND_OFFICERS,
            "drivers": ClassificationDocumentType.DRIVERS,
            "financial": ClassificationDocumentType.FINANCIAL_STATEMENT,
            "geotech": ClassificationDocumentType.UNKNOWN,
            "ifta": ClassificationDocumentType.IFTA,
            "loss_run": ClassificationDocumentType.LOSS_RUN,
            "mvr": ClassificationDocumentType.MVR,
            "safety_manual": ClassificationDocumentType.SAFETY_MANUAL,
            "supplemental": ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
            "unknown": ClassificationDocumentType.UNKNOWN,
            "vehicles": ClassificationDocumentType.VEHICLES,
            "xmod": ClassificationDocumentType.WORK_COMP_EXPERIENCE,
            "xmod_summary": ClassificationDocumentType.EMOD_SUMMARY,
            "equipment": ClassificationDocumentType.EQUIPMENT,
            "quote": ClassificationDocumentType.QUOTE,
            "policy": ClassificationDocumentType.QUOTE,
            "handbook": ClassificationDocumentType.EMPLOYEE_HANDBOOK,
            "nis": ClassificationDocumentType.UNKNOWN,
        },
        feature_list=[
            "num_of_lines",
            "num_of_words",
            "num_of_question_marks",
            "num_of_dollars",
            "num_of_usd",
            "lowercase_density",
            "uppercase_density",
            "alpha_density",
            "numeric_density",
            "CARDINAL",
            "DATE",
            "EVENT",
            "FAC",
            "GPE",
            "LANGUAGE",
            "LAW",
            "LOC",
            "MONEY",
            "NORP",
            "ORDINAL",
            "ORG",
            "PERCENT",
            "PERSON",
            "PRODUCT",
            "QUANTITY",
            "TIME",
            "WORK_OF_ART",
            "like_id",
            "monetary_count",
            "vin_like",
            "avg_line_length",
            "avg_word_length",
            "page_text",
            "previous_page",
        ],
    ),
)

azure_client = AzureCustomModelClient(
    base_url="dummy",
    subscription_key="dummy",
    model_id="prebuilt-read",
)

processor = DocumentSubmissionProcessor(
    classifier=classifier,
    azure_client=azure_client,
    cloudmersive_converter=None,
    n_jobs=1,
    local_run=True,
)


def ocr_result(ocr_filename):
    with open(os.path.join("tests/data/processing/", ocr_filename)) as f:
        return json.load(f)


def process_and_check(
    mocker: Any,
    pdf_filename: str,
    ocr_filename: str,
    entry_classified_file: ClassifiedFile | None,
    file_type: FileType,
    classification: ClassificationDocumentType,
    filename_classification: ClassificationDocumentType,
    page_classification: list[ClassificationDocumentType] | ClassificationDocumentType,
):
    mocker.patch(
        "src.models.classifiers.document_classifier.text_extraction._run_azure_ocr",
        return_value=ocr_result(ocr_filename) if ocr_filename else None,
    )

    mocker.patch(
        "common.clients.azure.AzureCognitiveServicesClient._get_read_api_result", return_value={"status": "failed"}
    )

    classified_file = entry_classified_file or ClassifiedFile(
        file_type=FileType.UNKNOWN,
        classification=None,
        processing_state="NOT_CLASSIFIED",
        file_name=pdf_filename,
        presigned_url=None,
        organization_id=3,
        file_path=os.path.join("tests/data/processing/", pdf_filename),
        document_type=detect_document_type(pdf_filename),
        submission_id=str(uuid.uuid4()),
    )

    output = processor.process_files([classified_file])
    assert output[0].file_type == file_type
    assert output[0].classification == classification
    assert output[0].filename_classification == filename_classification
    if type(page_classification) is not list:
        assert all(item == page_classification for item in output[0].page_level_classifications)
    else:
        assert output[0].page_level_classifications == page_classification


def process_and_check_page_counts(mocker: Any, pdf_filename: str, ocr_filename: str, number_of_pages: int | None):
    mocker.patch(
        "src.models.classifiers.document_classifier.text_extraction._run_azure_ocr",
        return_value=ocr_result(ocr_filename),
    )

    mocker.patch(
        "common.clients.azure.AzureCognitiveServicesClient._get_read_api_result", return_value={"status": "failed"}
    )

    classified_file = ClassifiedFile(
        file_type=FileType.UNKNOWN,
        classification=None,
        processing_state="NOT_CLASSIFIED",
        file_name=pdf_filename,
        presigned_url=None,
        organization_id=3,
        file_path=os.path.join("tests/data/processing/", pdf_filename),
        document_type=detect_document_type(pdf_filename),
        submission_id=str(uuid.uuid4()),
    )

    output = processor.process_files([classified_file])

    assert len(output) == 1
    assert output[0].additional_info.number_of_pages == number_of_pages


@pytest.mark.parametrize(
    "pdf_filename, ocr_filename, file_type, classification, filename_classification, page_classification",
    [
        (
            "CA MOD .91 - Sciolex Corporation.pdf",
            "CA MOD .91 - Sciolex Corporation.json",
            FileType.WORK_COMP_EXPERIENCE,
            ClassificationDocumentType.EMOD_SUMMARY_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.EMOD_SUMMARY,
        ),
        (
            (
                "Lot 10 Multifamily Owner LLC + LD&D Ventures, LLC - DoMo Cass Square - Aon Budget Review"
                " 03-26-25_anon.xlsx"
            ),
            None,
            FileType.BUDGET,
            ClassificationDocumentType.BUDGET_SPREADSHEET,
            ClassificationDocumentType.BUDGET,
            [],
        ),
        (
            "AWR_Acord_101.pdf",
            "AWR_Acord_101.json",
            FileType.ACORD_FORM,
            ClassificationDocumentType.ACORD_101,
            ClassificationDocumentType.ACORD_FORM,
            ClassificationDocumentType.ACORD_101,
        ),
        (
            "FS_12.31.2023_Outen_Motors_(FCA)_2024.pdf",
            "FS_12.31.2023_Outen_Motors_(FCA)_2024.json",
            FileType.FINANCIAL_STATEMENT,
            ClassificationDocumentType.AUTO_DEALER_FINANCIAL_STATEMENT_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.AUTO_DEALER_FINANCIAL_STATEMENT,
        ),
        (
            "EX BOR - Layer 2.pdf",
            "EX BOR - Layer 2.json",
            FileType.BROKER_OF_RECORD_LETTER,
            ClassificationDocumentType.BROKER_OF_RECORD_LETTER_PDF,
            ClassificationDocumentType.BROKER_OF_RECORD_LETTER,
            ClassificationDocumentType.BROKER_OF_RECORD_LETTER,
        ),
        (
            "Budget.pdf",
            "Budget.json",
            FileType.BUDGET,
            ClassificationDocumentType.BUDGET_PDF,
            ClassificationDocumentType.BUDGET,
            ClassificationDocumentType.BUDGET,
        ),
        (
            "37_supplement.pdf",
            "37_supplement.json",
            FileType.SUPPLEMENTAL_FORM,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ),
        (
            "Xos Structure Chart.pdf",
            "Xos Structure Chart.json",
            FileType.COMPANY_STRUCTURE,
            ClassificationDocumentType.ORG_CHART_PDF,
            ClassificationDocumentType.ORG_CHART,
            ClassificationDocumentType.ORG_CHART,
        ),
        (
            "Zen Easton, Casualty Account Summary, 5.1.24.pdf",
            "Zen Easton, Casualty Account Summary, 5.1.24.json",
            FileType.COVER_SHEET,
            ClassificationDocumentType.COVER_SHEET_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.COVER_SHEET,
        ),
        (
            "2. 2024-03-28 WCS Staff & Corporate Officers List.pdf",
            "2. 2024-03-28 WCS Staff & Corporate Officers List.json",
            FileType.DIRECTORS_AND_OFFICERS,
            ClassificationDocumentType.DIRECTORS_AND_OFFICERS_PDF,
            ClassificationDocumentType.DIRECTORS_AND_OFFICERS,
            ClassificationDocumentType.DIRECTORS_AND_OFFICERS,
        ),
        (
            "DLs.pdf",
            "DLs.json",
            FileType.DRIVERS,
            ClassificationDocumentType.DRIVERS_PDF,  # somewhat correct, list with scans of driver license
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.DRIVERS,
        ),
        (
            "Drivers List.pdf",
            "Drivers List.json",
            FileType.DRIVERS,
            ClassificationDocumentType.DRIVERS_PDF,
            ClassificationDocumentType.DRIVERS,
            ClassificationDocumentType.DRIVERS,
        ),
        (
            "Auto_Schedule.pdf",
            "Auto_Schedule.json",
            FileType.MERGED,
            ClassificationDocumentType.MERGED,
            ClassificationDocumentType.VEHICLES,
            [ClassificationDocumentType.VEHICLES] + [ClassificationDocumentType.EQUIPMENT] * 5,
        ),
        (
            "Balance Sheet & Profit Loss Statement.pdf",
            "Balance Sheet & Profit Loss Statement.json",
            FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
            ClassificationDocumentType.FINANCIAL_STATEMENT,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT,
        ),
        (
            "GeoTech Report.pdf",
            "GeoTech Report.json",
            FileType.GEOTECH_REPORT,
            ClassificationDocumentType.GEOTECH_REPORT_PDF,
            ClassificationDocumentType.GEOTECH_REPORT,
            ClassificationDocumentType.GEOTECH_REPORT,
        ),
        (
            "NV Supplement 2024.pdf",
            "NV Supplement 2024.json",
            FileType.MERGED,
            ClassificationDocumentType.MERGED,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
            [ClassificationDocumentType.SUPPLEMENTAL_APPLICATION] * 1
            + [ClassificationDocumentType.EMPLOYEE_HANDBOOK] * 13,
        ),
        (
            "TransWest_DOT_Driver_Policy.pdf",
            "TransWest_DOT_Driver_Policy.json",
            FileType.MERGED,
            ClassificationDocumentType.MERGED,
            ClassificationDocumentType.DRIVERS,
            [ClassificationDocumentType.SAFETY_MANUAL, ClassificationDocumentType.DRIVERS],
        ),
        (
            "Q3 IFTA 2023.pdf",
            "Q3 IFTA 2023.json",
            FileType.IFTA,
            ClassificationDocumentType.IFTA_PDF,
            ClassificationDocumentType.IFTA,
            ClassificationDocumentType.IFTA,
        ),
        (
            "Great American Umbrella Loss Runs 2019-2024.pdf",
            "Great American Umbrella Loss Runs 2019-2024.json",
            FileType.LOSS_RUN,
            ClassificationDocumentType.LOSS_RUN_PDF,
            ClassificationDocumentType.LOSS_RUN,
            ClassificationDocumentType.LOSS_RUN,
        ),
        (
            "Loss_Runs-Farmers_for_terms_2021-2023_valued_9-28-23_re._Pkg_(Canyon_View_Apts)_(01962875xB65D2).pdf",
            "Loss_Runs-Farmers_for_terms_2021-2023_valued_9-28-23_re._Pkg_(Canyon_View_Apts)_(01962875xB65D2).json",
            FileType.LOSS_RUN_SUMMARY,
            ClassificationDocumentType.LOSS_SUMMARY_PDF,
            ClassificationDocumentType.LOSS_RUN,
            ClassificationDocumentType.LOSS_SUMMARY,
        ),
        (
            "Wild Ox Com. Auto Ren. Quote.pdf",
            "Wild Ox Com. Auto Ren. Quote.json",
            FileType.QUOTE,
            ClassificationDocumentType.QUOTE_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.QUOTE,
        ),
        (
            "2024 BMW X5 Binder.pdf",
            "2024 BMW X5 Binder.json",
            FileType.UNKNOWN,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.UNKNOWN,
        ),
        (
            "Petru_Chiperi_8-29-23.pdf",
            "Petru_Chiperi_8-29-23.json",
            FileType.MVR,
            ClassificationDocumentType.MVR_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.MVR,
        ),
        (
            "2a._2024_Auto_Named_Insured_Schedule.pdf",  # wrong
            "2a._2024_Auto_Named_Insured_Schedule.json",
            FileType.VEHICLES,
            ClassificationDocumentType.VEHICLES_PDF,
            ClassificationDocumentType.VEHICLES,
            ClassificationDocumentType.VEHICLES,
        ),
        (
            "100226473_2022-2024 Viasat Europe Limited - FLI Loss Runs - val 05.30.24 - Intact.pdf",
            "100226473_2022-2024 Viasat Europe Limited - FLI Loss Runs - val 05.30.24 - Intact.json",
            FileType.LOSS_RUN,
            ClassificationDocumentType.LOSS_RUN_NO_CLAIM_PDF,
            ClassificationDocumentType.LOSS_RUN,
            ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ),
        (
            "Rev_LNG_-_MVRs_46_REV_MVR_Nicole_Bradshaw_-_2024.pdf",
            "Rev_LNG_-_MVRs_46_REV_MVR_Nicole_Bradshaw_-_2024.json",
            FileType.MVR,
            ClassificationDocumentType.MVR_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.MVR,
        ),
        (
            "Versa_Org_Chart_2024.pdf",
            "Versa_Org_Chart_2024.json",
            FileType.DIRECTORS_AND_OFFICERS,
            ClassificationDocumentType.DIRECTORS_AND_OFFICERS_PDF,
            ClassificationDocumentType.ORG_CHART,
            ClassificationDocumentType.DIRECTORS_AND_OFFICERS,
        ),
        (
            "Dolan_etal_-_GL_WC_Auto_Named_Insured__Location_Schedules.pdf",
            "Dolan_etal_-_GL_WC_Auto_Named_Insured__Location_Schedules.json",
            FileType.VEHICLES,
            ClassificationDocumentType.VEHICLES_PDF,
            ClassificationDocumentType.VEHICLES,
            ClassificationDocumentType.VEHICLES,
        ),
        (
            "98547904_Project Celtic - P2 Preliminary 670 Construction Schedule 03.12.2024.pdf",
            "98547904_Project Celtic - P2 Preliminary 670 Construction Schedule 03.12.2024.json",
            FileType.PROJECT_SCHEDULE,
            ClassificationDocumentType.PROJECT_SCHEDULE_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.PROJECT_SCHEDULE,
        ),
        (
            "Co Quote Starstone.pdf",
            "Co Quote Starstone.json",
            FileType.QUOTE,
            ClassificationDocumentType.QUOTE_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.QUOTE,
        ),
        (
            "Swinerton-Cover-TOC_SAFETY.pdf.pdf",
            "Swinerton-Cover-TOC_SAFETY.pdf.json",
            FileType.SAFETY_MANUAL,
            ClassificationDocumentType.SAFETY_MANUAL_PDF,
            ClassificationDocumentType.COVER_SHEET,
            [
                ClassificationDocumentType.SAFETY_MANUAL,
                ClassificationDocumentType.SAFETY_MANUAL,
                ClassificationDocumentType.SAFETY_MANUAL,
                ClassificationDocumentType.SAFETY_MANUAL,
                ClassificationDocumentType.SAFETY_MANUAL,
                ClassificationDocumentType.SAFETY_MANUAL,
            ],
        ),
        (
            "Site Plan and Abbrev.pdf",
            "Site Plan and Abbrev.json",
            FileType.SITE_REPORT,
            ClassificationDocumentType.SITE_REPORT,
            ClassificationDocumentType.SITE_REPORT,
            ClassificationDocumentType.SITE_REPORT,
        ),
        (
            "Auto Rental Application (003) - FL only.pdf",
            "Auto Rental Application (003) - FL only.json",
            FileType.SUPPLEMENTAL_FORM,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ),
        (
            "Pay_Plan_Addendum_for_Vehicle_Theft_Control.pdf",
            "Pay_Plan_Addendum_for_Vehicle_Theft_Control.json",
            FileType.VEHICLES,
            ClassificationDocumentType.VEHICLES_PDF,
            ClassificationDocumentType.VEHICLES,
            ClassificationDocumentType.VEHICLES,
        ),
        (
            "7-1-2024 Current List of Auto's.pdf",
            "7-1-2024 Current List of Auto's.json",
            FileType.BROKER_OF_RECORD_LETTER,
            ClassificationDocumentType.BROKER_OF_RECORD_LETTER_PDF,
            ClassificationDocumentType.VEHICLES,
            ClassificationDocumentType.BROKER_OF_RECORD_LETTER,
        ),
        (
            "Auto List .pdf",
            "Auto List .json",
            FileType.VEHICLES,
            ClassificationDocumentType.VEHICLES_PDF,
            ClassificationDocumentType.VEHICLES,
            ClassificationDocumentType.VEHICLES,
        ),
        (
            "E_Mod.pdf",
            "E_Mod.json",
            FileType.WORK_COMP_EXPERIENCE,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE,
        ),
        (
            "11_-_Sparrer_Organization_-_Financial_Stmt_-_11.24_CONSOLIDATED_FINANCIAL_STATEMENT_Pages__283_99_29.pdf",
            "11_-_Sparrer_Organization_-_Financial_Stmt_-_11.24_CONSOLIDATED_FINANCIAL_STATEMENT_Pages__283_99_29.json",
            FileType.MERGED,
            ClassificationDocumentType.MERGED,
            ClassificationDocumentType.FINANCIAL_STATEMENT,
            [ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT] * 4
            + [ClassificationDocumentType.FINANCIAL_STATEMENT] * 93,
        ),
        (
            "wisconsin_3.pdf",
            "wi_form_2.json",
            FileType.MERGED,
            ClassificationDocumentType.MERGED,
            ClassificationDocumentType.UNKNOWN,
            [ClassificationDocumentType.WORK_COMP_EXPERIENCE_WI, ClassificationDocumentType.UNKNOWN],
        ),
        (
            "wisconsin_emod.pdf",
            "wi_form.json",
            FileType.WORK_COMP_EXPERIENCE,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE_WI_PDF,
            ClassificationDocumentType.UNKNOWN,
            [ClassificationDocumentType.WORK_COMP_EXPERIENCE_WI, ClassificationDocumentType.WORK_COMP_EXPERIENCE_WI],
        ),
        (
            "2025_Experience_Mod_Worksheet-_Globe_Parking_UNKNOWN_Pages_(1_1).pdf",
            "pa_025_Experience_Mod_Worksheet_ocr_result.json",
            FileType.WORK_COMP_EXPERIENCE,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE_PA_PDF,
            ClassificationDocumentType.UNKNOWN,
            [ClassificationDocumentType.WORK_COMP_EXPERIENCE_PA],
        ),
        (
            "2025-pccpap-1.pdf",
            "pa_2025-pccpap-1_ocr_result.json",
            FileType.MERGED,
            ClassificationDocumentType.MERGED,
            ClassificationDocumentType.UNKNOWN,
            [
                ClassificationDocumentType.WORK_COMP_EXPERIENCE_PA,
                ClassificationDocumentType.UNKNOWN,
            ],  # 2nd has no EMOD_KEYWORDS
        ),
        (
            "Experience Mod Wksht 2025 085.pdf",
            "mn_ocr_result.json",
            FileType.WORK_COMP_EXPERIENCE,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE_MN_PDF,
            ClassificationDocumentType.UNKNOWN,
            [ClassificationDocumentType.WORK_COMP_EXPERIENCE_MN, ClassificationDocumentType.WORK_COMP_EXPERIENCE_MN],
        ),
        (
            "ACORD_Form_20250422-083605_ACORD_139_2015_12.pdf",
            "",
            FileType.ACORD_FORM,
            ClassificationDocumentType.ACORD_139,
            ClassificationDocumentType.ACORD_FORM,
            ClassificationDocumentType.ACORD_139,
        ),
    ],
)
def test_processing(
    mocker: Any,
    pdf_filename: str,
    ocr_filename: str,
    file_type: FileType,
    classification: ClassificationDocumentType,
    filename_classification: ClassificationDocumentType,
    page_classification: ClassificationDocumentType,
):
    process_and_check(
        mocker=mocker,
        pdf_filename=pdf_filename,
        ocr_filename=ocr_filename,
        entry_classified_file=None,
        file_type=file_type,
        classification=classification,
        filename_classification=filename_classification,
        page_classification=page_classification,
    )


@pytest.mark.parametrize(
    "pdf_filename, ocr_filename, number_of_pages",
    [("FS_12.31.2023_Outen_Motors_(FCA)_2024.pdf", "FS_12.31.2023_Outen_Motors_(FCA)_2024.json", 4)],
)
def test_processing_for_page_count(mocker: Any, pdf_filename: str, ocr_filename: str, number_of_pages: int | None):
    process_and_check_page_counts(
        mocker=mocker, pdf_filename=pdf_filename, ocr_filename=ocr_filename, number_of_pages=number_of_pages
    )


@pytest.mark.parametrize(
    (
        "pdf_filename, ocr_filename, file_type, classification, expected_classification, filename_classification,"
        " page_classification"
    ),
    [
        (
            "100226473_2022-2024 Viasat Europe Limited - FLI Loss Runs - val 05.30.24 - Intact.pdf",
            "100226473_2022-2024 Viasat Europe Limited - FLI Loss Runs - val 05.30.24 - Intact.json",
            FileType.LOSS_RUN,
            ClassificationDocumentType.ACORD_FORM,
            ClassificationDocumentType.LOSS_RUN_NO_CLAIM_PDF,
            ClassificationDocumentType.LOSS_RUN,
            ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ),
        (
            "24-25_XCEED_Logistics_Inc_WC_Submission.pdf",
            "24-25_XCEED_Logistics_Inc_WC_Submission.json",
            FileType.WORK_COMP_EXPERIENCE,
            ClassificationDocumentType.ACORD_FORM,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE_CA_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE_CA,
        ),
        (
            "Bridges_Equipment_10-29.pdf",
            "Bridges_Equipment_10-29.json",
            FileType.ACORD_FORM,
            ClassificationDocumentType.LOSS_RUN_PDF,
            ClassificationDocumentType.ACORD_127,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.ACORD_127,
        ),
        (
            "Surge Lafayette 24.25 Founders LL supp.pdf",
            "Surge Lafayette 24.25 Founders LL supp.json",
            FileType.SUPPLEMENTAL_FORM,
            ClassificationDocumentType.LOSS_RUN_PDF,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
            ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION,
        ),
    ],
)
def test_processing_with_given_file_type(
    mocker: Any,
    pdf_filename: str,
    ocr_filename: str,
    file_type: FileType,
    classification: ClassificationDocumentType,
    expected_classification: ClassificationDocumentType,
    filename_classification: ClassificationDocumentType,
    page_classification: ClassificationDocumentType,
):
    entry_classified_file = ClassifiedFile(
        file_type=file_type,
        classification=classification,
        processing_state="NOT_CLASSIFIED",
        file_name=pdf_filename,
        presigned_url=None,
        organization_id=3,
        file_path=os.path.join("tests/data/processing/", pdf_filename),
        document_type=detect_document_type(pdf_filename),
        submission_id=str(uuid.uuid4()),
    )

    process_and_check(
        mocker=mocker,
        pdf_filename=pdf_filename,
        ocr_filename=ocr_filename,
        entry_classified_file=entry_classified_file,
        file_type=file_type,
        classification=expected_classification,
        filename_classification=filename_classification,
        page_classification=page_classification,
    )


@pytest.mark.parametrize(
    (
        "pdf_filename, ocr_filename, file_type, classification, expected_classification, filename_classification,"
        " page_classification"
    ),
    [
        (
            "100226473_2022-2024 Viasat Europe Limited - FLI Loss Runs - val 05.30.24 - Intact.pdf",
            "100226473_2022-2024 Viasat Europe Limited - FLI Loss Runs - val 05.30.24 - Intact.json",
            FileType.ACORD_FORM,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
            ClassificationDocumentType.ACORD_FORM,
            ClassificationDocumentType.LOSS_RUN,
            ClassificationDocumentType.ACORD_FORM,
        ),
        (
            "24-25_XCEED_Logistics_Inc_WC_Submission.pdf",
            "24-25_XCEED_Logistics_Inc_WC_Submission.json",
            FileType.LOSS_RUN,
            ClassificationDocumentType.ACORD_FORM,
            ClassificationDocumentType.LOSS_RUN_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.LOSS_RUN,
        ),
        (
            "Bridges_Equipment_10-29.pdf",
            "Bridges_Equipment_10-29.json",
            FileType.SUPPLEMENTAL_FORM,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ),
        (
            "Surge Lafayette 24.25 Founders LL supp.pdf",
            "Surge Lafayette 24.25 Founders LL supp.json",
            FileType.WORK_COMP_EXPERIENCE,
            ClassificationDocumentType.LOSS_RUN,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE_PDF,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE,
        ),
    ],
)
def test_processing_with_file_type_user_override(
    mocker: Any,
    pdf_filename: str,
    ocr_filename: str,
    file_type: FileType,
    classification: ClassificationDocumentType,
    expected_classification: ClassificationDocumentType,
    filename_classification: ClassificationDocumentType,
    page_classification: ClassificationDocumentType,
):
    entry_classified_file = ClassifiedFile(
        file_type=file_type,
        classification=classification,
        processing_state="NOT_CLASSIFIED",
        file_name=pdf_filename,
        presigned_url=None,
        organization_id=3,
        file_path=os.path.join("tests/data/processing/", pdf_filename),
        document_type=detect_document_type(pdf_filename),
        submission_id=str(uuid.uuid4()),
    )

    process_and_check(
        mocker=mocker,
        pdf_filename=pdf_filename,
        ocr_filename=ocr_filename,
        entry_classified_file=entry_classified_file,
        file_type=file_type,
        classification=expected_classification,
        filename_classification=filename_classification,
        page_classification=page_classification,
    )
