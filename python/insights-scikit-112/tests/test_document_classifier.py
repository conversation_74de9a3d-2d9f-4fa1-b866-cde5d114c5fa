import ast
import pickle

import pandas as pd
import pytest
from sklearn.metrics import classification_report
from static_common.enums.classification_document_type import ClassificationDocumentType

from src.models.classifiers.document_classifier.acord import (
    backfill_acord_classification,
)
from src.models.classifiers.document_classifier.classified_file import (
    ClassifiedFile,
    DocumentType,
    ProcessingMethod,
)
from src.models.classifiers.document_classifier.industry_detection import (
    detect_construction_supplementals,
    detect_loss_runs,
    detect_project_specific_supplementals,
    detect_residential_supplementals,
    is_cab_report,
)
from src.models.classifiers.document_classifier.pdf_doc_classifier import (
    PDFDocumentClassifierV2,
)
from src.models.classifiers.document_classifier.spreadsheet_classifier_v2 import (
    SpreadsheetClassifierV2,
)
from src.models.classifiers.document_classifier.submission_document_classifier import (
    FilenameDocumentClassifier,
    SubmissionDocumentClassifier,
    incorporate_filename_classification,
)
from src.models.classifiers.document_classifier.text_extraction import (
    TextExtractionResult,
    TextItem,
    open_and_clean_pdf_using_bytes,
)
from src.models.classifiers.document_classifier.utils import (
    detect_document_type,
    enum_file_extension,
    smooth_predictions,
    split_pdf,
)


@pytest.fixture
def classifier() -> SubmissionDocumentClassifier:
    return SubmissionDocumentClassifier(
        "data/models/submission_document_classifier",
        pdf_classifier=PDFDocumentClassifierV2(
            base_model_dir="data/models/pdf_classifier_v2",
            model_version="v2_upsampled_supplementals",
            model_filename="model_210325.pkl",
            preprocessor_filename="preprocessor_210325.pkl",
            class_mapping={
                "acord": ClassificationDocumentType.ACORD_FORM,
                "auto_financial": ClassificationDocumentType.AUTO_DEALER_FINANCIAL_STATEMENT,
                "bor": ClassificationDocumentType.BROKER_OF_RECORD_LETTER,
                "budget": ClassificationDocumentType.BUDGET,
                "site_plan": ClassificationDocumentType.UNKNOWN,
                "project_schedule": ClassificationDocumentType.PROJECT_SCHEDULE,
                "corp_tree": ClassificationDocumentType.ORG_CHART,
                "cover_sheet": ClassificationDocumentType.COVER_SHEET,
                "directors_officers": ClassificationDocumentType.DIRECTORS_AND_OFFICERS,
                "drivers": ClassificationDocumentType.DRIVERS,
                "financial": ClassificationDocumentType.FINANCIAL_STATEMENT,
                "geotech": ClassificationDocumentType.UNKNOWN,
                "ifta": ClassificationDocumentType.IFTA,
                "loss_run": ClassificationDocumentType.LOSS_RUN,
                "mvr": ClassificationDocumentType.MVR,
                "safety_manual": ClassificationDocumentType.UNKNOWN,
                "supplemental": ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
                "unknown": ClassificationDocumentType.UNKNOWN,
                "vehicles": ClassificationDocumentType.VEHICLES,
                "xmod": ClassificationDocumentType.WORK_COMP_EXPERIENCE,
                "xmod_summary": ClassificationDocumentType.EMOD_SUMMARY,
                "equipment": ClassificationDocumentType.EQUIPMENT,
                "quote": ClassificationDocumentType.QUOTE,
                "policy": ClassificationDocumentType.QUOTE,
                "handbook": ClassificationDocumentType.EMPLOYEE_HANDBOOK,
                "nis": ClassificationDocumentType.UNKNOWN,
            },
            feature_list=[
                "num_of_lines",
                "num_of_words",
                "num_of_question_marks",
                "num_of_dollars",
                "num_of_usd",
                "lowercase_density",
                "uppercase_density",
                "alpha_density",
                "numeric_density",
                "CARDINAL",
                "DATE",
                "EVENT",
                "FAC",
                "GPE",
                "LANGUAGE",
                "LAW",
                "LOC",
                "MONEY",
                "NORP",
                "ORDINAL",
                "ORG",
                "PERCENT",
                "PERSON",
                "PRODUCT",
                "QUANTITY",
                "TIME",
                "WORK_OF_ART",
                "like_id",
                "monetary_count",
                "vin_like",
                "avg_line_length",
                "avg_word_length",
                "page_text",
                "previous_page",
            ],
        ),
    )


@pytest.fixture
@pytest.mark.skip(reason="only for local run")
def benchmark_data():
    df = pd.read_csv("data/benchmark/doc_classification_benchmark.csv")

    df.classification = df.classification.apply(ast.literal_eval)
    df.text_result = df.text_result.apply(ast.literal_eval)

    df.text_result = df.text_result.apply(lambda x: [TextExtractionResult.from_json(y) for y in x])
    return df


def get_classified_file(text_list: list[str], predictions: list[ClassificationDocumentType] | None) -> ClassifiedFile:
    text_results = [
        TextExtractionResult(
            text_items=[TextItem(x_min=0, y_min=0, x_max=0, y_max=0, text=text, block_no=0)],
            words=[TextItem(x_min=0, y_min=0, x_max=0, y_max=0, text=word, block_no=0) for word in text.split()],
            page_width=0,
            page_height=0,
            joined_text=text,
        )
        for text in text_list
    ]
    page_level_confidences = ([1] * len(predictions)) if predictions else []
    return ClassifiedFile(
        text_results=text_results,
        file_name="file",
        page_level_classifications=predictions,
        page_level_confidences=page_level_confidences,
    )


@pytest.mark.skip(reason="only for local run")
def test_classification_result_benchmark(classifier, benchmark_data):
    import src.models.classifiers.document_classifier.industry_detection as industry_detection

    industry_detection.azure_fr_read_client = None

    expected, results, df_results = [], [], []
    for ind, row in benchmark_data.iterrows():
        fname_classification = FilenameDocumentClassifier.predict(row.filename, org_id=3)
        classified_file = ClassifiedFile(
            text_results=row.text_result, file_name=row.filename, filename_classification=fname_classification
        )
        result = classifier.pdf_predict(classified_file)
        result = incorporate_filename_classification(result)
        assert len(result.page_level_classifications) == len(row.classification)
        expected.extend(row.classification)
        results.extend(result.page_level_classifications)
        df_results.append([x.value for x in result.page_level_classifications])
    benchmark_data["result"] = df_results
    report = classification_report(expected, results, output_dict=True)
    assert report


@pytest.mark.parametrize(
    "text, filename, expected",
    [
        ("Loss run file Claim Number Date of Loss", "lr.pdf", ClassificationDocumentType.LOSS_RUN_NO_CLAIM),
        (
            "Nothing specific in here this is not that file",
            "supp_app.pdf",
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ),
        ("Nothing specific in here this is not that file", "2023 BUDGETS.PDF", ClassificationDocumentType.BUDGET),
        ("Form 5500", "20240702123236NAL0003528387001.PDF", ClassificationDocumentType.ERISA_FORM_5500),
        ("Form 5500-SF", "20240702123236NAL0003528387001.PDF", ClassificationDocumentType.ERISA_FORM_5500_SF),
    ],
)
def test_classification_result_basic(
    text: str, filename: str, expected: ClassificationDocumentType, classifier: SubmissionDocumentClassifier
):
    fname_classification = FilenameDocumentClassifier.predict(filename, org_id=3)
    classified_file = get_classified_file([text], None)
    classified_file.filename_classification = fname_classification
    classified_file.processing_method = ProcessingMethod.CLASSIFIER
    result = classifier.pdf_predict(classified_file)
    result = incorporate_filename_classification(result)
    assert result.page_level_classifications[0] == expected


def test_project_specific():
    text_list = [
        "blabla",
        "Project Specific oip",
        "Project Specific owners interest",
        "acord",
        "blabla",
        "Project Specific supplemental",
        "acord",
        "Project Specific wrap-up application",
        "Project Specific project & wrap",
    ]
    predictions = [
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
    ]
    out = detect_project_specific_supplementals(get_classified_file(text_list, predictions))
    assert out.page_level_classifications == [
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION,
    ]


@pytest.mark.parametrize(
    "test_input, expected",
    [
        ("Vehicles List.xlsx", ClassificationDocumentType.VEHICLES),
        ("vehlist.csv", ClassificationDocumentType.VEHICLES),
        ("86543191_Auto List.xlsx", ClassificationDocumentType.VEHICLES),
        ("Equip Lists.xlsx", ClassificationDocumentType.VEHICLES),
        ("Auto Policy.xlsx", ClassificationDocumentType.UNKNOWN),
        ("Auto Schedule 9.2.22.xls", ClassificationDocumentType.VEHICLES),
        ("BJB Drivers.xlsx", ClassificationDocumentType.DRIVERS),
        ("driv", ClassificationDocumentType.UNKNOWN),
        ("Springbreak Farms Drivers.xlsx", ClassificationDocumentType.DRIVERS),
        ("driverlist.csv", ClassificationDocumentType.DRIVERS),
        ("supp_oip.pdf", ClassificationDocumentType.SUPPLEMENTAL_APPLICATION),
        ("Ken's Towing & Service Driver List.xlsx", ClassificationDocumentType.DRIVERS),
        ("SOV Template.xlsx", ClassificationDocumentType.SOV),
        ("ParsCo LLC sov.xlsx", ClassificationDocumentType.SOV),
        ("Rodco Trucking, LLC-SOV.xlsx", ClassificationDocumentType.SOV),
        ("LR_22_23.pdf", ClassificationDocumentType.UNKNOWN),
        ("super driver sov", ClassificationDocumentType.SOV),
        ("Argo - Owners Interest - AC-APP095-0718.pdf", ClassificationDocumentType.SUPPLEMENTAL_APPLICATION),
        ("Project Specific application.pdf", ClassificationDocumentType.SUPPLEMENTAL_APPLICATION),
        ("supplemental app", ClassificationDocumentType.SUPPLEMENTAL_APPLICATION),
        ("GL Loss Run 9-1-16 to 8-31-18.PDF", ClassificationDocumentType.LOSS_RUN),
        ("CVLR 17-22 UMB.pdf", ClassificationDocumentType.UNKNOWN),
        ("23 Auto Loss Summary_5 years.xlsx", ClassificationDocumentType.LOSS_SUMMARY),
        ("Loss Summary Overview.xlsx", ClassificationDocumentType.LOSS_SUMMARY),
        ("Loss Run Supplemental Acord", ClassificationDocumentType.UNKNOWN),
        ("GL_ACORD_0.pdf", ClassificationDocumentType.ACORD_FORM),
        ("Prelim Excess - Acord App.pdf", ClassificationDocumentType.ACORD_FORM),
        ("87626363_WEIS SUB 2023.pdf", ClassificationDocumentType.UNKNOWN),
        ("Driving Services, General Liability Submission 1.1.23.pdf", ClassificationDocumentType.UNKNOWN),
        (
            "Wolfspeed (HIBS Project) Site Maps - Location & Exploration Plans.pdf",
            ClassificationDocumentType.SITE_REPORT,
        ),
        ("Vantage Concepts LLC Claims Summary 2022.xlsx", ClassificationDocumentType.LOSS_SUMMARY),
        ("SAFETY MANUAL.DOC", ClassificationDocumentType.SAFETY_MANUAL),
        ("email_body.pdf", ClassificationDocumentType.EMAIL),
        ("email_body.xlsx", ClassificationDocumentType.UNKNOWN),
        ("email.msg", ClassificationDocumentType.RAW_EMAIL),
        ("email.eml", ClassificationDocumentType.RAW_EMAIL),
    ],
)
def test_filename_classification(test_input, expected):
    assert FilenameDocumentClassifier.predict(test_input, 1) == expected


@pytest.mark.parametrize(
    "test_input_filename, test_input_prediction, expected",
    [
        ("Vehicles List.xlsx", ClassificationDocumentType.VEHICLES, ClassificationDocumentType.VEHICLES_SPREADSHEET),
        (
            "SuppForm.pdf",
            ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION,
            ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_PDF,
        ),
        ("drivers.docx", ClassificationDocumentType.DRIVERS, ClassificationDocumentType.DRIVERS_EDITABLE_DOC),
        ("driverslist.xlsm", ClassificationDocumentType.DRIVERS, ClassificationDocumentType.DRIVERS_SPREADSHEET),
        ("Loss Run.PDF", ClassificationDocumentType.LOSS_RUN, ClassificationDocumentType.LOSS_RUN_PDF),
        (
            "Loss Summary.xlsx",
            ClassificationDocumentType.LOSS_SUMMARY,
            ClassificationDocumentType.LOSS_SUMMARY_SPREADSHEET,
        ),
        (
            "Block 45 NV5 Geotech Report No 2.pdf",
            ClassificationDocumentType.GEOTECH_REPORT,
            ClassificationDocumentType.GEOTECH_REPORT_PDF,
        ),
    ],
)
def test_extension_enhancement(test_input_filename, test_input_prediction, expected):
    doc_type = detect_document_type(test_input_filename)
    assert (
        enum_file_extension(
            ClassifiedFile(classification=test_input_prediction, document_type=doc_type), test_input_filename
        )
        == expected
    )


@pytest.mark.parametrize(
    "text_list, predictions, expected",
    [
        (
            [
                "General Contractor's Questionnaire general contractor",
                """
                Do you use scaffolding? Concrete plumbing steel work lead abatement contracting services
                paving grading of land crane work hot air welding
                """,
            ],
            [ClassificationDocumentType.SUPPLEMENTAL_APPLICATION] * 2,
            [ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION] * 2,
        ),
        (
            ["General Contractor's Questionnaire", "Do you use masks and gloves?"],
            [ClassificationDocumentType.SUPPLEMENTAL_APPLICATION] * 2,
            [ClassificationDocumentType.SUPPLEMENTAL_APPLICATION] * 2,
        ),
        (
            [
                "General Contractor's Questionnaire general contractor Habitational Supplemental",
                """
                Do you use scaffolding? Concrete plumbing steel work lead abatement contracting services
                paving grading of land crane work hot air welding
                """,
            ],
            [ClassificationDocumentType.SUPPLEMENTAL_APPLICATION] * 2,
            [ClassificationDocumentType.SUPPLEMENTAL_APPLICATION] * 2,
        ),
    ],
)
def test_detect_construction_supplementals(text_list, predictions, expected):
    cf = get_classified_file(text_list, predictions)
    assert detect_construction_supplementals(cf).page_level_classifications == expected


def test_detect_loss_runs():
    text_list = [
        "blabla",
        "Workers' Compensation Policy: Policy Term",
        "Workers' Compensation Policy:",
        "Disclaimer is not a run",
        "Not a project but supp",
        "Loss: Date:",
        "Something; Valuation Date: 27-03-2022",
        "just sup",
        "this is sup",
        "Loss Run Detail: Date of Loss",
        "Bla bla claim $50.00",
        "bla bla claim",
        "Something else",
        "Not a Loss Run",
        "Claim Count",
        "Total Loss Incurred $100.00",
        "Something",
        "Losses",
        "Paid Reserve Incurred Loss $5.00",
    ]
    predictions = [
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
    ]
    cf = get_classified_file(text_list, predictions)
    out = detect_loss_runs(cf)
    assert out.page_level_classifications == [
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.LOSS_SUMMARY,
        ClassificationDocumentType.LOSS_SUMMARY,
        ClassificationDocumentType.LOSS_SUMMARY,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.LOSS_SUMMARY,
    ]


def test_detect_loss_runs_assumed():
    text_list = [
        "blabla",
        "Workers' Compensation Policy: Policy Term",
        "Workers' Compensation Policy:",
        "Disclaimer is not a run",
    ]
    predictions = [
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
    ]
    cf = get_classified_file(text_list, predictions)
    out = detect_loss_runs(cf, assume_is_loss_run=True)
    assert out.page_level_classifications == [
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
    ]


def test_detect_loss_runs_assumed_no_lr():
    text_list = [
        "blabla",
        "Loss Run Summary Incurred Claim Count Reserve Paid",
        "No Claims",
        "No Claims",
    ]
    predictions = [
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
    ]
    cf = get_classified_file(text_list, predictions)
    out = detect_loss_runs(cf, assume_is_loss_run=True)
    assert out.page_level_classifications == [
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ClassificationDocumentType.LOSS_SUMMARY,
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
    ]


def test_detect_loss_runs_not_assumed():
    text_list = [
        "blabla",
        "Workers' Compensation Policy: Policy Term",
        "Workers' Compensation Policy:",
        "Disclaimer is not a run",
    ]
    predictions = [
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
    ]
    cf = get_classified_file(text_list, predictions)
    out = detect_loss_runs(cf, assume_is_loss_run=False)
    assert out.page_level_classifications == [
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.UNKNOWN,
        ClassificationDocumentType.UNKNOWN,
    ]


def test_detect_loss_summaries_assumed():
    text_list = [
        "blabla",
        "Workers' Compensation Policy: Policy Term",
        "Workers' Compensation Policy:",
        "Disclaimer paid total incurred",
    ]
    predictions = [
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.LOSS_RUN,
    ]
    cf = get_classified_file(text_list, predictions)
    out = detect_loss_runs(cf, assume_is_loss_run=True)
    assert out.page_level_classifications == [
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
        ClassificationDocumentType.LOSS_SUMMARY,
    ]


def test_residential():
    text_list = [
        "blabla",
        "Residential Property",
        "smth",
        "lol",
        "acord",
        "blabla",
        "just sup",
        "this is sup",
        "project-specific supplemental",
        "acord",
        "Habitational Risks - Supp App",
        "project & wrap",
    ]
    predictions = [
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
    ]
    out = detect_residential_supplementals(get_classified_file(text_list, predictions))
    assert out.page_level_classifications == [
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.LOSS_RUN,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.ACORD_FORM,
        ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION,
        ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION,
    ]


@pytest.mark.parametrize(
    "input, expected, limit",
    [
        (
            ClassifiedFile(
                page_level_classifications=[
                    ClassificationDocumentType.ACORD_FORM,
                    ClassificationDocumentType.ACORD_FORM,
                    ClassificationDocumentType.LOSS_RUN,
                ],
                page_level_confidences=[0.991, 0.991, 0.9],
            ),
            [
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.ACORD_FORM,
            ],
            None,
        ),
        (
            ClassifiedFile(
                page_level_classifications=[
                    ClassificationDocumentType.ACORD_FORM,
                    ClassificationDocumentType.ACORD_FORM,
                    ClassificationDocumentType.LOSS_RUN,
                ],
                page_level_confidences=[0.991, 0.991, 0.991],
            ),
            [
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.LOSS_RUN,
            ],
            None,
        ),
        (
            ClassifiedFile(
                page_level_classifications=[
                    ClassificationDocumentType.ACORD_FORM,
                    ClassificationDocumentType.LOSS_RUN,
                    ClassificationDocumentType.ACORD_FORM,
                    ClassificationDocumentType.ACORD_FORM,
                ],
                page_level_confidences=[0.8, 0.97, 0.991, 0.991],
            ),
            [
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.ACORD_FORM,
            ],
            None,
        ),
        (
            ClassifiedFile(
                page_level_classifications=[
                    ClassificationDocumentType.IFTA,
                    ClassificationDocumentType.UNKNOWN,
                    ClassificationDocumentType.IFTA,
                    ClassificationDocumentType.ACORD_130,
                    ClassificationDocumentType.APPLIED_130,
                    ClassificationDocumentType.ACORD_130,
                ],
                page_level_confidences=[0.8, 0.8, 0.8, 0.8, 0.8, 0.8],
            ),
            [
                ClassificationDocumentType.IFTA,
                ClassificationDocumentType.IFTA,
                ClassificationDocumentType.IFTA,
                ClassificationDocumentType.ACORD_130,
                ClassificationDocumentType.APPLIED_130,
                ClassificationDocumentType.ACORD_130,
            ],
            ClassificationDocumentType.IFTA,
        ),
        (
            ClassifiedFile(
                page_level_classifications=[
                    ClassificationDocumentType.IFTA,
                    ClassificationDocumentType.UNKNOWN,
                    ClassificationDocumentType.IFTA,
                    ClassificationDocumentType.ACORD_130,
                    ClassificationDocumentType.APPLIED_130,
                    ClassificationDocumentType.ACORD_130,
                ],
                page_level_confidences=[0.8, 0.8, 0.8, 0.8, 0.8, 0.8],
            ),
            [
                ClassificationDocumentType.IFTA,
                ClassificationDocumentType.IFTA,
                ClassificationDocumentType.IFTA,
                ClassificationDocumentType.ACORD_130,
                ClassificationDocumentType.ACORD_130,
                ClassificationDocumentType.ACORD_130,
            ],
            None,
        ),
        (
            ClassifiedFile(
                page_level_classifications=[
                    ClassificationDocumentType.ACORD_FORM,
                    ClassificationDocumentType.LOSS_RUN,
                    ClassificationDocumentType.ACORD_FORM,
                    ClassificationDocumentType.ACORD_FORM,
                ],
                page_level_confidences=[0.8, 0.992, 0.991, 0.991],
            ),
            [
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.LOSS_RUN,
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.ACORD_FORM,
            ],
            None,
        ),
    ],
)
def test_smoothing_prediction(input, expected, limit):
    assert smooth_predictions(input, limit) == expected


@pytest.mark.parametrize(
    "cl_file, expected_classifications, expected_confidences",
    [
        (
            ClassifiedFile(
                filename_classification=ClassificationDocumentType.VEHICLES,
                page_level_classifications=[ClassificationDocumentType.VEHICLES, ClassificationDocumentType.UNKNOWN],
            ),
            [ClassificationDocumentType.VEHICLES, ClassificationDocumentType.UNKNOWN],
            [],
        ),
        (
            ClassifiedFile(
                filename_classification=ClassificationDocumentType.ACORD_FORM,
                page_level_classifications=[ClassificationDocumentType.ACORD_125, ClassificationDocumentType.UNKNOWN],
            ),
            [ClassificationDocumentType.ACORD_125, ClassificationDocumentType.UNKNOWN],
            [],
        ),
        (
            ClassifiedFile(
                filename_classification=ClassificationDocumentType.ACORD_FORM,
                page_level_classifications=[ClassificationDocumentType.UNKNOWN, ClassificationDocumentType.UNKNOWN],
            ),
            [ClassificationDocumentType.ACORD_FORM, ClassificationDocumentType.ACORD_FORM],
            [],
        ),
        (
            ClassifiedFile(
                filename_classification=ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
                page_level_classifications=[ClassificationDocumentType.VEHICLES, ClassificationDocumentType.UNKNOWN],
                page_level_confidences=[0.5, 0.5],
            ),
            [ClassificationDocumentType.VEHICLES, ClassificationDocumentType.SUPPLEMENTAL_APPLICATION],
            [0.5, 0.5],
        ),
        (
            ClassifiedFile(
                filename_classification=ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
                page_level_classifications=[ClassificationDocumentType.VEHICLES, ClassificationDocumentType.VEHICLES],
                page_level_confidences=[0.5, 0.5],
            ),
            [ClassificationDocumentType.SUPPLEMENTAL_APPLICATION, ClassificationDocumentType.SUPPLEMENTAL_APPLICATION],
            [0.5, 0.5],
        ),
    ],
)
def test_incorporating_filename_classification(cl_file, expected_classifications, expected_confidences):
    output = incorporate_filename_classification(cl_file)
    assert output.page_level_classifications == expected_classifications
    assert output.page_level_confidences == expected_confidences


@pytest.mark.skip("Skipped because pickles need updating after refactoring common packages")
def test_split_acord_pdfs():
    # TODO refactor code so that this test doesn't need to be this crazy
    input_classfied_file_pkl_path = "tests/data/acord_file_before_split.pickle"
    after_split_expected_files_pkl_path = "tests/data/acord_files_after_split.pickle"
    before_split_file_name = "2023 GL app.pdf"
    after_split_expected_file_names = [
        "2023 GL app_ACORD_125_Pages_(1_2)_(4_4)_(6_6).pdf",
        "2023 GL app_APPLIED_125_CIS_2009_08.pdf",
        "2023 GL app_APPLIED_125_PCIS_2009_08.pdf",
        "2023 GL app_ACORD_126_Pages_(7_9)_(11_11).pdf",
        "2023 GL app_ACORD_829_LOB_2009_05.pdf",
    ]

    with open(input_classfied_file_pkl_path, "rb") as handle:
        input_classified_file = pickle.load(handle)

    input_classified_file.file_name = before_split_file_name
    input_classified_file.pdf_document, _ = open_and_clean_pdf_using_bytes(input_classified_file.file_bytes)
    with open(after_split_expected_files_pkl_path, "rb") as handle:
        after_split_expected_files = pickle.load(handle)

    after_split_actual_files = split_pdf(input_classified_file)

    assert before_split_file_name == input_classified_file.file_name
    assert after_split_expected_file_names == [
        classified_file.file_name for classified_file in after_split_actual_files
    ]
    assert after_split_actual_files == after_split_expected_files


@pytest.mark.parametrize(
    "input, expected",
    [
        (
            TextExtractionResult(
                words=[
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Central", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Analysis", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Bureau,", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Inc", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Copyright", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="2022", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="for", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Prepared", block_no=1),
                ],
                text_items=[],
                page_height=100,
            ),
            True,
        ),
        (
            TextExtractionResult(
                words=[
                    TextItem(x_min=0, x_max=0, y_min=20, y_max=30, text="Central", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Analysis", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Bureau,", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Inc", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Copyright", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="2022", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="for", block_no=1),
                    TextItem(x_min=0, x_max=0, y_min=91, y_max=97, text="Prepared", block_no=1),
                ],
                text_items=[],
                page_height=100,
            ),
            False,
        ),
    ],
)
def test_cab_detection(input, expected):
    assert is_cab_report(input) == expected


@pytest.mark.parametrize(
    "filename, use_fallback, expected",
    [
        ("2024-2025 Auto Questionnaire.pdf - Adobe Acrobat (003)", True, DocumentType.PDF),
        ("2024-2025 Auto Questionnaire.pdf - Adobe Acrobat (003)", False, DocumentType.OTHER),
        ("SOME FILE.PDF", False, DocumentType.PDF),
        ("File_1.pdf.zip", False, DocumentType.ARCHIVE),
        ("2024-2025 Auto Questionnaire.xlsx - Adobe Acrobat (003).PDF", False, DocumentType.PDF),
    ],
)
def test_detect_document_type(filename, use_fallback, expected):
    assert detect_document_type(filename, use_fallback) == expected


@pytest.mark.parametrize(
    "text, classification, expected",
    [
        [
            ["page 1 of 3", "page 2 of 3", "page 3 of 3"],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.ACORD_125,
            ],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
            ],
        ],
        [
            ["page 1 of 3", "page 2 of 3", "page 3 of 3"],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
                ClassificationDocumentType.ACORD_125,
            ],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
                ClassificationDocumentType.ACORD_125,
            ],
        ],
        [
            ["page 1 of 5", "page 2 of 5", "page 3 of 5", "page 4 of 5", "page 5 of 5"],
            [
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.ACORD_125,
            ],
            [
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
            ],
        ],
        [
            ["page1of5", "page 2 of 5", "page 3 of 5", "page 4    of    5", "page 5 of 5"],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
            ],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
            ],
        ],
        [
            ["", "page 2 of 5", "page 3 of 5", "page 4 of 5", "page 5 of 5"],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
            ],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
            ],
        ],
        [
            ["page 1 of 5", "", "page 3 of 5", "page 4 of 5", "page 5 of 5"],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
            ],
            [
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.UNKNOWN,
            ],
        ],
    ],
)
def test_backfill_acord_classification(text, classification, expected):
    classfied_file = get_classified_file(text, classification)
    classfied_file.page_level_additional_info = [[], [], [], []]
    classfied_file = backfill_acord_classification(classfied_file)
    assert classfied_file.page_level_classifications == expected
