import itertools
import operator
import os
import re
from collections import Counter, defaultdict
from dataclasses import replace
from io import BytesIO

import pandas as pd
from common.clients.azure import AzureCustomModelClient, AzureReadClient
from common.utils.value_cleanup import clean_numerical
from datascience_common.classifiers.fact_identification.sov import (
    REGULAR_EXPRESSIONS_SOV,
)
from datascience_common.classifiers.fact_identification.text_matching import (
    RegexMatcher,
    match,
)
from file_processing.ocr_utils import get_table_dfs_from_pdf
from file_processing.utils import is_potential_vin
from fuzzywuzzy import fuzz
from infrastructure_common.logging import get_logger
from nltk import sent_tokenize, word_tokenize
from pymupdf import Document
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.organization import ExistingOrganizations
from static_common.models.file_additional_info import FileAdditionalInfo
from static_common.utils.file_type_and_classification_mappings import (
    CLASSIFICATION_TO_FILE_TYPE,
)

from src.models.classifiers.document_classifier.acord import (
    backfill_acord_classification,
    detect_acord_version,
    is_acord_35,
    is_acord_101,
    is_acord_125,
    is_acord_126,
    is_acord_127,
    is_acord_128,
    is_acord_130,
    is_acord_131,
    is_acord_139,
    is_acord_140,
    is_acord_160,
    is_acord_163,
    is_acord_175,
    is_acord_211,
    is_acord_823,
    is_acord_829,
    is_acord_vehicle_schedule,
    is_applied_98,
    is_applied_125,
    is_applied_126,
    is_applied_130,
    is_ofappinfcni,
    is_ofschhaz,
)
from src.models.classifiers.document_classifier.classified_file import (
    ClassifiedFile,
    DocumentType,
)
from src.models.classifiers.document_classifier.constants import (
    CAB_REPORT_VERTICAL_BBOX_THRESHOLD,
    CONSOLIDATED_FINANCIAL_STATEMENT_TITLE_FIRST_CHARS,
    EEOC_MATCH_RATIO,
    IFTA_KEYWORDS,
    IFTA_THRESHOLD,
    MAX_EEOC_LINES,
    MIN_CONSTRUCTION_SCORE,
    PROJECT_SPECIFIC_SUPP_TITLE_FIRST_CHARS,
    RESIDENTIAL_SUPP_TITLE_FIRST_CHARS,
    RESTAURANT_SUPP_TITLE_FIRST_CHARS,
    SUPP_TITLE_FIRST_CHARS,
)
from src.models.classifiers.document_classifier.erisa_5500 import detect_erisa_5500
from src.models.classifiers.document_classifier.loss_run import (
    NoClaimStatus,
    has_loss_run_traits,
    has_monetary_data,
    is_loss_summary,
    is_special_snowflake,
    no_claim_status,
)
from src.models.classifiers.document_classifier.spreadsheet_classifier import (
    _DRIVERS_PHRASE_CONFIG,
    _VEHICLES_PHRASE_CONFIG,
    SpreadsheetClassifier,
)
from src.models.classifiers.document_classifier.text_extraction import (
    TextExtractionResult,
    clean_text,
    open_and_clean_pdf_using_bytes,
)
from src.models.classifiers.document_classifier.utils import (
    df_to_excel_bytes,
    safe_delete_pages,
    simple_page_cutting,
    smooth_predictions,
)

logger = get_logger()

azure_client = AzureReadClient(
    base_url=os.getenv("AZURE_READ_ANALYZE_ENDPOINT", ""),
    subscription_key=os.getenv("AZURE_READ_SUBSCRIPTION_KEY", ""),
)
azure_layout_client = AzureCustomModelClient(
    base_url=os.getenv("AZURE_READ_ANALYZE_ENDPOINT", ""),
    subscription_key=os.getenv("AZURE_READ_SUBSCRIPTION_KEY", ""),
    model_id="prebuilt-document",
)

azure_fr_read_client = AzureCustomModelClient(
    base_url=os.getenv("AZURE_READ_ANALYZE_ENDPOINT", ""),
    subscription_key=os.getenv("AZURE_READ_SUBSCRIPTION_KEY", ""),
    model_id="prebuilt-read",
)

DELIMITERS = [";", ",", " ", "/"]

EMOD_THRESHOLD = 0.8
WORKERS_COMP_KEYWORDS = ["workers' compensation", "workers compensation", "ncci", "authorized classes"]
EMOD_KEYWORDS = ["experience rating", "rating worksheet", "experience modification"]
CALIFORNIA_KEYWORDS_1 = [
    "workers compensation experience rating form",
    "workers compensation insurance rating bureau of california",
    "bureau number",
    "effective date",
    "issue date",
    "experience modification",
    "insurer",
    "insurer group",
    "policy number",
    "issuing office",
    "experience period",
    "subrogation",
    "joint claim",
    "partially fraudulent",
]
CALIFORNIA_KEYWORDS_2 = [
    "calculation of your experience modification",
    "data used for experience rating and the experience period",
    "about the experience rating form",
    "summary of payrolls and expected losses",
    "find out more",
    "about the wcirb",
    "workers compensation insurance rating bureau of california",
]
INDIANA_KEYWORDS = [
    "workers compensation experience rating",
    "rating produced by indiana compensation rating bureau",
    "risk name",
    "risk id",
    "rating effective date",
    "production date",
    "state",
    "exp prim",
    "act inc",
    "act prim",
]
NEW_JERSEY_KEYWORDS = [
    "new jersey workers compensation",
    "experience rating data",
    "experience period",
    "rating effective",
    "abbreviated wording",
    "catastrophe",
    "manual premium subject",
    "total pay",
    "total payroll",
    "modified losses",
    "total modified",
    "expected losses",
    "risk modified losses",
    "credibilities",
    "final adjusted losses",
    "ppap",
    "njccpap",
    "table a",
    "total incurred",
]
NEW_YORK_KEYWORDS_SET_1 = [
    "workers compensation rating worksheet",
    "combinable group id",
    "rating effective date",
    "issue date",
    "status",
]
NEW_YORK_KEYWORDS_SET_2 = [
    "modification factor",
    "coverage id",
    "carrier id",
    "policy number",
    "policy period",
    "carrier name",
    "exposure and expected losses",
    "claims and actual losses",
]
NEW_YORK_KEYWORDS_SET_3 = ["risk name", "address", "city", "state", "zip code"]
NEW_YORK_KEYWORDS_SET_4 = [
    "exposure and expected losses",
    "claims and actual losses",
    "injury",
    "claim number",
    "class code",
]
NCCI_KEYWORDS_SET_1 = [
    "workers compensation experience rating",
    "risk name",
    "risk id",
    "rating effective date",
    "production date",
    "state",
]
NCCI_KEYWORDS_SET_2 = [
    "exp excess",
    "exp prim",
    "act exc losses",
    "ballast",
    "act inc losses",
    "act prim",
    "primary losses",
    "stabilizing value",
    "ratable excess",
    "arap",
    "flarap",
    "sarap",
    "maarap",
    "exp mod",
]
NCCI_KEYWORDS_SET_3 = [
    "firm id",
    "firm name",
    "carrier",
    "policy no",
    "eff date",
    "exp date",
    "code",
    "elr",
    "payroll",
    "expected",
    "exp prim",
    "claim data",
    "ij",
    "of",
    "act inc",
    "act prim",
]
NCCI_KEYWORDS_SET_4 = [
    "firm",
    "rpt no",
    "eff date",
    "carrier name",
    "policy no",
    "contingent rating",
    "the attached experience rating was produced using data only available to ncci",
    "unit statistical reports to issue a complete rating in accordance with the experience rating plan manual",
    "this rating applies in all states that have approved the application of contingent experience rating",
    "in all other states this rating provided for information purposes only",
    "revised rating will be issued in accordance with the appropriate experience rating plan manual rules",
]
NCCI_KEYWORDS_SET_5 = [
    "wt",
    "exp excess",
    "expected",
    "exp prim",
    "act exc",
    "ballast",
    "act inc losses",
    "act prim",
]

NCCI_KEYWORDS_SET_6 = [
    "ncci experience rating worksheet",
    "risk name",
    "risk id",
    "rating effective date",
    "production date",
    "state",
]
NCCI_KEYWORDS_SET_7 = [
    "exp excess",
    "exp primary",
    "act excess",
    "ballast",
    "act incurred",
    "act primary",
    "primary losses",
    "stabilizing value",
    "ratable excess",
    "arap",
    "flarap",
    "sarap",
    "maarap",
    "modifier",
]
NCCI_KEYWORDS_SET_8 = [
    "firm code",
    "firm name",
    "carrier",
    "policy number",
    "effective date",
    "expiration date",
    "class",
    "elr",
    "payroll",
    "exp loss",
    "exp prim",
    "claim number",
    "injury",
    "act inc",
    "act prim",
    "premium",
]
PENNSYLVANIA_KEYWORDS = ["pennsylvania compensation rating bureau", "pennsylvania workers compensation"]
PENNSYLVANIA_KEYWORDS_2 = ["split point", "final modification", "loss free mod", "maximum mod", "all divided by"]
MINNESOTA_KEYWORDS = ["minnesota workers compensation experience rating"]
WISCONSIN_KEYWORDS = [
    "wisconsin workers compensation experience rating",
    "wisconsin workers compensation experience rating data",
    "wisconsin experience ratings",
]

CONSTRUCTION_SUPPLEMENTAL_PHRASES = [
    "general contractor",
    "scaffolding",
    "eifs",
    "site protection",
    "demolition",
    "concrete construction",
    "asbestos",
    "project name",
    "name of project",
    "remodel",
    "artisan contractor",
    "project address",
    "project location",
    "term of project",
    "below grade",
    "below ground",
    "excavation",
    "contractor license",
    "general contractor",
    "construction manager",
    "contracting",
    "abatement",
    "construction cost",
    "contractor renewal",
    "ground up",
    "owners interest",
    "prime contractor",
    "project manager",
    "project description",
    "project specific",
    "new construction",
    "crane",
    "blasting",
    "safety program",
    "site inspection",
    "drilling",
    "welding",
    "brazing",
    "oil or gas",
    "piping",
    "bridge work",
    "grading of land",
    "land grading",
    "bio remediation",
    "maximum height",
    "maximum depth",
    "power lines",
    "underpinning",
    "wrecking",
    "gas mains",
    "landscaping",
    "snowplowing",
    "snow and ice removal",
    "paving",
    "masonry",
    "tree trimming",
    "tree care",
    "airport",
    "underground",
    "concrete contractor",
    "tree care",
    "structural renovation",
    "contracting operation",
    "torch down",
    "hot air welding",
    "roofing contractor",
    "pollution",
    "bitumen",
]

CONSTRUCTION_NEGATIVE_PHRASES_FIRST_PAGE = [
    "workers compensation",
]

CONSTRUCTION_NEGATIVE_PHRASES = [
    "volunteer labor",
    "healthcare provider",
]

PROJECT_SPECIFIC_SUPPLEMENTAL_CONFIDENT_PHRASES = {
    "project specific",
}

PROJECT_SPECIFIC_SUPPLEMENTAL_PHRASES = {
    "project details",
    "project description",
    "project duration",
    "project construction",
    "project participants",
    "project history",
    "project information",
    "project type",
    "project team",
    "project information",
    "start date of project",
    "project start date",
    "completion date of project",
    "project address",
    "project cost",
    "project name",
    "term of project",
    "project description",
    "owners interest",
}

PROJECT_SPECIFIC_SUPPLEMENTAL_NEGATIVE_PHRASES = {
    "list all major projects",
    "list 5 projects",
    "list current projects",
    "describe current projects",
}

PROJECT_WRAP_UP_FORM_NAMES = {
    "wrap up supplemental application",
    "wrap project",
    "wrap up project specific",
    "wrap up application",
    "wrap application",
    "project wrap",
    "wrap up",
    "ocip",
    "ccip",
    "owner controlled interest program",
    "contractor controlled interest program",
}

PROJECT_OWNERS_INTEREST_FORM_NAMES = {
    "owners interest",
    "owner s interest",
    "oip",
}

PROJECT_SPECIFIC_OWNER_GC_FORM_NAMES = {
    "project specific application",
    "project application",
    "project specific",
    "constructor project",
    "owner gc",
    "project information form",
}

RESIDENTIAL_REAL_ESTATE_FORM_NAMES = {
    "apartment single family",
    "condominium and homeowners",
    "real estate program supplemental",
    "real estate property managers supplemental",
    "real estate supplemental",
    "real estate questionnaire",
    "real estate application",
    "apartment supplemental",
    "apartment application",
    "residential property",
    "condominium supplemental",
    "condominium application",
    "habitational risk",
    "habitational supplemental",
    "habitational questionnaire",
    "habitational application",
    "property supplemental",
    "residential location supplemental",
    "residential location application",
}

RESTAURANT_SUPPLEMENTAL_PHRASES = {
    "restaurant",
    "tavern",
    "liquor",
    "caterer",
    "hospitality",
}

RESTAURANT_SUPPLEMENTAL_PHRASES_NO_TITLE = {
    ("liquor liability", "does the building have apartments", "assault and battery"),
    ("fine dining", "casual dining", "annual liquor sales", "table service"),
}

LOSS_RUN_WORDS = {"loss", "losses", "claim", "claims", "claimant", "clmt", "incident", "accident", "incurred"}

LOSS_RUN_PHRASES = {
    (
        "workers compensation policy",
        "policy term",
    ),
    ("summary for account",),
    ("valued as of",),
    ("valuation date",),
    ("coverage did not exist for this reporting period",),
}
LOSS_RUN_DISQUALIFIERS = {
    ("experience rating",),
}

CONSOLIDATED_FINANCIAL_STATEMENT_KEYWORDS = {
    "accounts payable",
    "accrued expenses",
    "beginning cash and cash equivalents",
    "capital and related financing activities",
    "cash and cash equivalents",
    "cash beginning of year",
    "cash from financing activities",
    "cash from investing activities",
    "cash from operating activities",
    "cash equivalents",
    "cash paid for interest",
    "cash increase",
    "change in net position",
    "changes in net assets",
    "comprehensive income",
    "comprehensive loss",
    "consolidated net loss",
    "cost of sales",
    "cost of goods sold",
    "current assets",
    "current liabilities",
    "current profit",
    "decrease in assets",
    "deferred tax",
    "ebitda",
    "ending cash and cash equivalents",
    "ending net position",
    "gross margin",
    "gross profit",
    "gross income",
    "income from operations",
    "interest expense",
    "interest income",
    "income tax",
    "investment securities",
    "members equity",
    "net assets",
    "net cash",
    "net equity",
    "net earnings",
    "net income",
    "net income loss",
    "net increase decrease in cash",
    "net loss",
    "net operating income",
    "net other income",
    "net profit",
    "net position",
    "net savings",
    "net increase in cash",
    "net decrease in cash",
    "net ordinary income",
    "noncurrent liabilities",
    "noncash financing activities",
    "noncash investing and financing activities",
    "operating activities",
    "operating expense",
    "operating income",
    "operating profit",
    "operating revenueother assets",
    "other revenue",
    "other income",
    "other investments",
    "other expense",
    "partners capital",
    "retained earnings",
    "revenue",
    "service revenue",
    "sales expenses",
    "statements of cash flownet income",
    "stock activity",
    "total asset",
    "total capital",
    "total cash",
    "total cogs",
    "total comprehensive income",
    "total comprehensive profit",
    "total current asset",
    "total current liabilities",
    "total equity",
    "total expense",
    "total g&a expenses",
    "total income",
    "total liabilities",
    "total liabilities and equity",
    "total liabilities equity",
    "total long term liabilities",
    "total net position",
    "total non operating income",
    "total liabilities and deferred inflows",
    "total noncurrent asset",
    "total operating expenses",
    "total operating revenues",
    "total other asset",
    "total other expenses",
    "total other income",
    "total revenue",
    "total revenues and gross profit",
    "total sales",
    "total stockholders equity",
}

INTRO_PAGE_PARTIAL = {"index", "table of contents"}
BALANCE_SHEET_PHRASES = {
    "statements of comprehensive loss",
    "balance sheet",
    "cash flow statement",
    "changes in stockholders equity",
    "changes in shareholders equity",
    "changes in members equity",
    "changes in stockholder s deficit",
    "changes in retained earnings",
    "changes in owners equity",
    "statement of financial position",
    "statements of financial position",
    "statement of income",
    "statements of income",
    "statement of comprehensive income",
    "statements of comprehensive income",
    "statement of comprehensive loss",
    "statements of comprehensive loss",
    "statements of assets",
    "statement of operations",
    "statements of operations",
    "statement of cash flow",
    "statements of cash flow",
    "operating summary",
    "statement of stockholder",
    "statements of stockholder",
    "statement of shareholder",
    "statements of shareholder",
    "statement of partners equity",
    "statements of partners equity",
    "statement of changes in cashflow",
    "statements of changes in cashflow",
    "statement of changes in partners capital",
    "statements of changes in partners capital",
    "statement of member",
    "statements of member",
    "statement of retained earnings",
    "statements of retained earnings",
    "statement of functional expenses",
    "statements of functional expenses",
    "income statement",
    "profit and loss",
    "profit loss",
    "statement of activities",
    "statements of activities",
    "liabilities and stockholders equity",
}
CONSOLIDATED_FINANCIAL_STATEMENT_NEGATIVE_UPPER_TEXT_KEYWORDS = {"notes to"}

FINANCIAL_STATEMENTS_PHRASES = {
    *BALANCE_SHEET_PHRASES,
    *CONSOLIDATED_FINANCIAL_STATEMENT_KEYWORDS,
    "financial statement",
    "financial report",
    "sales",
}
CONSOLIDATED_FINANCIAL_STATEMENT_NEGATIVE_KEYWORDS = {
    "foreign currency risk",
    "legal counsel",
    "fair value",
    "investment in other cooperatives",
    "warranty reserves",
    "employee benefit plans",
    "basis of presentation",
    "net assets available for benefits",
    "retirement plan",
    "401 k plan",
    "401 k savings plan",
    "statements of fiduciary net position",
    "unaudited supplemental information",
}

CAB_WORDS = {
    "central",
    "analysis",
    "bureau",
    "copyright",
    "prepared",
    "for",
}

_FIRST_PAGE_TEXTS = {"page 1 of"}

TRANSPORTATION_POSTIVE_PHRASES = {"vehicle", "driver", "truck", "trailer", "miles", "licence", "tractor", "equipment"}
TRANSPORTATION_NEGATIVE_PHRASES = {"ifta"}

EEOC_REFERENCE_PHRASES = [
    {"U.S. EQUAL EMPLOYMENT OPPORTUNITY COMMISSION (EEOC)", "EEOC Standard Form 100 (SF 100)"},
    {"EQUAL EMPLOYMENT OPPORTUNITY", "SECTION B - COMPANY IDENTIFICATION", "SECTION C - TEST FOR FILING REQUIREMENT"},
]
EEOC_ANY_PAGE = {
    "Senior Level Officials and Managers",
    "Level Officials and Managers",
    "Professionals",
    "Technicians",
    "Sales Workers",
    "Administrative Support Workers",
    "Craft Workers",
    "Operatives",
    "Laborers and Helpers",
    "Service Workers",
    "hispanic",
    "hawai",
    "native american",
    "asian",
}

QUOTE_NAMES = ["quote", "proposal"]

LETTER_OF_INTENT_PHRASES = [
    "letter of intent",
]

INVESTMENT_MEMORANDUM_MATCHER = RegexMatcher(
    phrases=[
        ("confidential", "information", "presentation"),
        ("confidential", "information", "overview"),
        ("confidential", "information", "memo"),
        ("deal", "memo"),
        ("deal", "presentation"),
        ("investment", "presentation"),
        ("acquisition", "overview"),
        ("transaction", "overview"),
        ("investment", "screen"),
        ("investment", "overview"),
        ("business", "overview"),
        ("transaction", "details"),
        ("executive", "summary"),
    ],
    characters_before=500,
    characters_between=20,
)

INVESTMENT_JOINDER_PHRASES = [
    "joinder agreement",
    "investment joinder",
    "insurer joinder",
    "certificate of joinder",
    "joinder motion",
]

NDA_PHRASES = [
    "confidentiality agreement",
    "non disclosure agreement",
    "non disclosure acknowledgement",
    "confidentiality acknowledgement",
]

PURCHASE_AGREEMENT_MATCHER = RegexMatcher(
    phrases=[("purchase", "agreement")], characters_before=300, characters_between=20
)

NDA_MAX_CHARS = 200
LETTER_OF_INTENT_MAX_CHARS = 300
INVESTMENT_MEMO_MAX_CHARS = 500
INVESTMENT_MEMO_MAX_PAGES = 3
PURCHASE_AGREEMENT_MAX_CHARS = 300


def num_keywords_in_text(text: str, keywords: set[str]) -> int:
    return sum(keyword in text for keyword in keywords)


def has_phrase_with_upper_first_letter(text: str, defined_phrases: set[str]):
    phrases = [phrase for phrase in defined_phrases if phrase in clean_text(text)]
    for phrase in phrases:
        matches = re.findall(phrase, text, re.IGNORECASE)
        for match_ in matches:
            match_clean = clean_text(match_, lower=False)
            if all([word[0].isupper() for word in match_clean.split()]):
                return True


def construction_score(first_page_text: str, text: str, filename: str) -> int:
    filename_score = 5 if "contractor" in filename.lower() or "construction" in filename.lower() else 0
    lower_text = text.lower()
    first_page_lower = first_page_text.lower()
    if "contractors supplemental" not in first_page_lower and any(
        phrase in first_page_lower for phrase in CONSTRUCTION_NEGATIVE_PHRASES_FIRST_PAGE
    ):
        return 0
    if any(phrase in lower_text for phrase in CONSTRUCTION_NEGATIVE_PHRASES):
        return 0
    phrases_found = [phrase for phrase in CONSTRUCTION_SUPPLEMENTAL_PHRASES if phrase in lower_text]
    logger.warning("SCORE", score=len(phrases_found) + filename_score)
    return len(phrases_found) + filename_score


def is_cab_report(text_result: TextExtractionResult) -> bool:
    valid_texts = [
        text_item
        for text_item in text_result.words
        if text_item.y_min >= CAB_REPORT_VERTICAL_BBOX_THRESHOLD * text_result.page_height
    ]
    joined_text = " ".join(ti.text for ti in valid_texts).lower()
    return all(phrase in joined_text.lower() for phrase in CAB_WORDS)


def is_eeoc(text_result: TextExtractionResult) -> bool:
    lines = []
    for text_item in text_result.text_items[:MAX_EEOC_LINES]:
        lines.extend(text_item.text.split("\n"))
    present_phrases = [x for x in EEOC_ANY_PAGE if x.lower() in text_result.joined_text.lower()]
    if len(present_phrases) > 0.9 * len(EEOC_ANY_PAGE):
        return True
    for phrase_set in EEOC_REFERENCE_PHRASES:
        phrase_missed = False
        for eeoc_phrase in phrase_set:
            if not any(fuzz.ratio(line, eeoc_phrase) > EEOC_MATCH_RATIO for line in lines):
                phrase_missed = True
        if not phrase_missed:
            return True
    return False


def is_file_construction_specific(text_results: list[TextExtractionResult], filename: str) -> bool:
    # At least 2 occurrences or two different matches of phrases
    if not text_results:
        return False
    joined_texts = " ".join(text_result.joined_text for text_result in text_results)
    return (
        construction_score(clean_text(text_results[0].joined_text), clean_text(joined_texts), filename=filename)
        >= MIN_CONSTRUCTION_SCORE
    ) and not is_residential_supp(text_results[0].joined_text)


def get_is_restaurant_supplemental(text: str) -> bool:
    sample = clean_text(text[:RESTAURANT_SUPP_TITLE_FIRST_CHARS])
    if any([restaurant_phrase in sample for restaurant_phrase in RESTAURANT_SUPPLEMENTAL_PHRASES]):
        return True
    full_text = clean_text(text)
    return any(
        all(phrase in full_text for phrase in phrase_combo) for phrase_combo in RESTAURANT_SUPPLEMENTAL_PHRASES_NO_TITLE
    )


def get_is_project_wrap_up(text: str) -> bool:
    sample = clean_text(text[:PROJECT_SPECIFIC_SUPP_TITLE_FIRST_CHARS])
    return any([project_specific_name in sample for project_specific_name in PROJECT_WRAP_UP_FORM_NAMES])


def is_project_specific_supplemental(text_list: list[TextExtractionResult]) -> bool:
    for text_el in text_list:
        text = text_el.joined_text

        if any([phrase in clean_text(text) for phrase in PROJECT_SPECIFIC_SUPPLEMENTAL_NEGATIVE_PHRASES]):
            return False

    text_ = text_list[0].joined_text[:SUPP_TITLE_FIRST_CHARS]
    if has_phrase_with_upper_first_letter(text_, PROJECT_SPECIFIC_SUPPLEMENTAL_CONFIDENT_PHRASES):
        return True

    matched_phrases = set()
    for text_el in text_list:
        text = text_el.joined_text
        text = clean_text(text)
        for phrase in PROJECT_SPECIFIC_SUPPLEMENTAL_PHRASES:
            if phrase in text:
                matched_phrases.add(phrase)
    if len(matched_phrases) >= 4:
        return True
    return False


def get_is_project_owners_interest(text: str) -> bool:
    sample = clean_text(text[:PROJECT_SPECIFIC_SUPP_TITLE_FIRST_CHARS])
    return any([project_specific_name in sample for project_specific_name in PROJECT_OWNERS_INTEREST_FORM_NAMES])


def get_is_project_specific_owner_gc(text: str) -> bool:
    sample = clean_text(text[:PROJECT_SPECIFIC_SUPP_TITLE_FIRST_CHARS])
    return any([project_specific_name in sample for project_specific_name in PROJECT_SPECIFIC_OWNER_GC_FORM_NAMES])


def is_residential_supp(text: str) -> bool:
    sample = clean_text(text[:RESIDENTIAL_SUPP_TITLE_FIRST_CHARS])
    return any([project_specific_name in sample for project_specific_name in RESIDENTIAL_REAL_ESTATE_FORM_NAMES])


def is_loss_run(text: str) -> bool:
    cleaned_text = clean_text(text)
    words = cleaned_text.split()

    for loss_run_disqualifier in LOSS_RUN_DISQUALIFIERS:
        if all([phrase in cleaned_text for phrase in loss_run_disqualifier]):
            return False

    for loss_run_word in LOSS_RUN_WORDS:
        if loss_run_word in words:
            return True
    for loss_run_phrase in LOSS_RUN_PHRASES:
        if all([phrase in cleaned_text for phrase in loss_run_phrase]):
            return True
    return False


def detect_construction_supplementals(classified_file: ClassifiedFile) -> ClassifiedFile:
    groups = [
        [item[0] for item in group[1]]
        for group in itertools.groupby(
            enumerate(classified_file.page_level_classifications), key=operator.itemgetter(1)
        )
    ]
    for group in groups:
        file_text_results = [
            text_result for ind, text_result in enumerate(classified_file.text_results) if ind in group
        ]
        group_classification = classified_file.page_level_classifications[min(group)]
        is_construction_supp = (
            group_classification == ClassificationDocumentType.SUPPLEMENTAL_APPLICATION
            and is_file_construction_specific(file_text_results, classified_file.file_name)
        )
        if not is_construction_supp:
            continue
        for ind in group:
            classified_file.page_level_classifications[ind] = (
                ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION
            )
            classified_file.page_level_confidences[ind] = 1
    return classified_file


def detect_residential_supplementals(classified_file: ClassifiedFile) -> ClassifiedFile:
    started_supplemental, is_residential = False, False
    for ind, (pred, text_result) in enumerate(
        zip(classified_file.page_level_classifications, classified_file.text_results)
    ):
        new_prediction, text = pred, text_result.joined_text
        if pred == ClassificationDocumentType.SUPPLEMENTAL_APPLICATION and not started_supplemental:
            started_supplemental = True
            is_residential = is_residential_supp(text)
        if pred == ClassificationDocumentType.SUPPLEMENTAL_APPLICATION and started_supplemental and is_residential:
            new_prediction = ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION
        else:
            started_supplemental = False
        classified_file.page_level_classifications[ind] = new_prediction
    return classified_file


def detect_restaurant_supplementals(classified_file: ClassifiedFile) -> ClassifiedFile:
    groups = [
        [item[0] for item in group[1]]
        for group in itertools.groupby(
            enumerate(classified_file.page_level_classifications), key=operator.itemgetter(1)
        )
    ]
    for group in groups:
        text = classified_file.text_results[min(group)].joined_text
        if classified_file.page_level_classifications[
            min(group)
        ] == ClassificationDocumentType.SUPPLEMENTAL_APPLICATION and get_is_restaurant_supplemental(text):
            for ind in group:
                classified_file.page_level_classifications[ind] = (
                    ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION
                )
                classified_file.page_level_confidences[ind] = 1
    return classified_file


def detect_project_specific_supplementals(classified_file: ClassifiedFile) -> ClassifiedFile:
    groups = [
        [item[0] for item in group[1]]
        for group in itertools.groupby(
            enumerate(classified_file.page_level_classifications), key=operator.itemgetter(1)
        )
    ]

    for group in groups:
        file_text_results = [
            text_result for ind, text_result in enumerate(classified_file.text_results) if ind in group
        ]
        group_classification = classified_file.page_level_classifications[min(group)]

        is_project_wrap_up, is_project_owners_interest, is_project_specific_owner_gc = False, False, False

        for text_result in file_text_results:
            text = text_result.joined_text

            if get_is_project_wrap_up(text):
                is_project_wrap_up = True
            elif get_is_project_owners_interest(text):
                is_project_owners_interest = True
            elif get_is_project_specific_owner_gc(text):
                is_project_specific_owner_gc = True

        is_project_specific_supp = (
            group_classification == ClassificationDocumentType.SUPPLEMENTAL_APPLICATION
            and is_project_specific_supplemental(file_text_results)
        )
        if not is_project_specific_supp:
            continue

        for ind in group:
            if is_project_wrap_up:
                classified_file.page_level_classifications[ind] = (
                    ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION
                )
            elif is_project_owners_interest:
                classified_file.page_level_classifications[ind] = (
                    ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION
                )
            elif is_project_specific_owner_gc:
                classified_file.page_level_classifications[ind] = (
                    ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION
                )
            else:
                classified_file.page_level_classifications[ind] = (
                    ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION
                )
            classified_file.page_level_confidences[ind] = 1
    return classified_file


def has_vehicles_or_drivers_phrases(text: str) -> bool:
    cleaned_text = clean_text(text)
    if any(phrase in cleaned_text for phrase in TRANSPORTATION_NEGATIVE_PHRASES):
        return False
    elif any(phrase in cleaned_text for phrase in TRANSPORTATION_POSTIVE_PHRASES):
        return True
    else:
        return False


def pages_with_tables(new_document: Document) -> bool:
    pages_wise_tables = get_table_dfs_from_pdf(
        BytesIO(new_document.write(no_new_id=True)),
        use_tables_from_azure=False,
        use_azure_for_native_pdfs_ocr=True,
        azure_client=azure_client,
        azure_layout_client=azure_layout_client,
    )
    req_pages = set()
    for first_page, last_page, table in pages_wise_tables:
        req_pages.update(set(range(first_page, last_page + 1)))

    return req_pages


def get_pages_to_extract_tables_from(
    classified_file: ClassifiedFile,
    filter_pages_with_img2table=False,
) -> tuple[Document | None, dict[int, int]]:
    if not classified_file.text_results:
        logger.warning("No text available to extract tables from PDF")
        return None, {}

    new_document, _ = open_and_clean_pdf_using_bytes(classified_file.file_bytes)
    pages_for_layout = (
        pages_with_tables(new_document) if filter_pages_with_img2table else set(range(1, new_document.page_count + 1))
    )

    orig_inds = []

    for ind in range(len(classified_file.page_level_classifications) - 1, -1, -1):
        pred = classified_file.page_level_classifications[ind]
        is_acord = False
        if (
            classified_file.page_level_additional_info
            and classified_file.page_level_additional_info[ind]
            and classified_file.page_level_additional_info[ind].acord
        ):
            is_acord = True
        text_result = classified_file.text_results[ind]
        text = text_result.joined_text
        if (
            (ind + 1 in pages_for_layout)
            and (
                (
                    pred
                    in [
                        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
                    ]
                    and has_vehicles_or_drivers_phrases(text)
                )
                or pred in (ClassificationDocumentType.VEHICLES, ClassificationDocumentType.DRIVERS)
            )
            and not is_acord
        ):
            orig_inds.append(ind)
        else:
            safe_delete_pages(new_document, from_page=ind, to_page=ind)

    orig_inds = orig_inds[::-1]
    orig_inds = {i: orig_ind for i, orig_ind in enumerate(orig_inds)}
    return new_document, orig_inds


def remove_selected_unselected(txt: str) -> str:
    return (
        txt.replace("\n:unselected:", "")
        .replace("\n:selected:", "")
        .replace(":unselected:", "")
        .replace(":selected:", "")
    )


def process_col_name(txt: str) -> str:
    txt = re.sub("_+", " ", txt)
    txt = re.sub("-+", " ", txt)
    txt = re.sub("&+", " ", txt)
    txt = re.sub(" +", " ", txt)
    txt = txt.strip()
    return txt


def extract_potential_vin(txt: str) -> str:
    out = ""
    for delimiter in DELIMITERS:
        txt_split_1 = txt.split(delimiter)
        for cand_1 in txt_split_1:
            for delimiter_2 in DELIMITERS:
                txt_split_2 = cand_1.split(delimiter_2)
                for cand_2 in txt_split_2:
                    if is_potential_vin(cand_2):
                        if len(cand_2) >= len(out):
                            out = cand_2
    return out


def split_columns(df: pd.DataFrame) -> pd.DataFrame:
    for col in df.columns:
        for delimiter in DELIMITERS:
            if delimiter not in col:
                continue
            for i, col_subs in enumerate(col.split(delimiter)):
                if not match(col_subs, REGULAR_EXPRESSIONS_SOV[FactSubtypeID.VEHICLE_INFORMATION_NUMBER]):
                    continue
                temp = df[col].apply(lambda x: extract_potential_vin(x) if type(x) == str else "")
                curr_num_vals = len([val for val in temp if val])
                if not curr_num_vals:
                    continue
                if "VIN" in df.columns:
                    existing_num_vals = len([val for val in df["VIN"] if val])
                    if curr_num_vals < existing_num_vals:
                        continue
                df["VIN"] = temp

    return df


def extract_tables_as_spreadsheet_classified_files(
    classified_file: ClassifiedFile, table_dfs: list[pd.DataFrame], orig_inds: dict[int, int]
) -> list[ClassifiedFile]:
    extracted_spreadsheet_classified_files = []
    table_dfs = sorted(table_dfs, key=lambda x: x[0])

    table_no = 1
    curr_page = -1

    dfs_by_cols = defaultdict(list)
    classifs_by_cols = {}
    min_page_by_cols = {}
    max_page_by_cols = {}
    non_empty_cols_by_cols = {}
    num_non_empty_by_cols = {}

    for first_page, last_page, df in table_dfs:
        if first_page == curr_page:
            table_no += 1
        else:
            table_no = 1
            curr_page = first_page
        is_drivers = SpreadsheetClassifier._predict_type(
            "",
            " ".join(df.columns),
            df.columns,
            _DRIVERS_PHRASE_CONFIG,
            ClassificationDocumentType.DRIVERS,
            empty_columns=[],
        )
        if classified_file.organization_id != 9:
            is_drivers = False

        is_vehicles = SpreadsheetClassifier._predict_type(
            "",
            " ".join(df.columns),
            df.columns,
            _VEHICLES_PHRASE_CONFIG,
            ClassificationDocumentType.VEHICLES,
            empty_columns=[],
        )

        orig_first_page_ind = orig_inds[first_page - 1]
        orig_first_page = orig_first_page_ind + 1
        orig_last_page_ind = orig_inds[last_page - 1]
        orig_last_page = orig_last_page_ind + 1

        if is_drivers:
            classification = ClassificationDocumentType.DRIVERS_SPREADSHEET

        if is_vehicles:
            classification = ClassificationDocumentType.VEHICLES_SPREADSHEET

        if is_drivers or is_vehicles:
            df.columns = [process_col_name(col) for col in df.columns]
            col_counts = Counter(df.columns)
            for col in df.columns:
                if col and col_counts[col] == 1:
                    df[col] = df[col].map(lambda x: remove_selected_unselected(x) if type(x) == str else x)
            key = "".join([col.replace(" ", "") for col in df.columns]) + str(df.shape[1])
            dfs_by_cols[key].append(df)
            classifs_by_cols[key] = classification
            min_page_by_cols[key] = min(min_page_by_cols.get(key, float("inf")), orig_first_page)
            max_page_by_cols[key] = max(max_page_by_cols.get(key, -1), orig_last_page)
            num_non_empty = len([col for col in df.columns if col])
            if num_non_empty > num_non_empty_by_cols.get(key, -1):
                num_non_empty_by_cols[key] = num_non_empty
                non_empty_cols_by_cols[key] = list(df.columns)

            drivers_or_vehicles_changed_to_merged = False

            for page_ind in range(orig_first_page - 1, orig_last_page):
                if classified_file.organization_id == 9 and classified_file.page_level_classifications[page_ind] in [
                    ClassificationDocumentType.DRIVERS
                ]:
                    classified_file.page_level_classifications[page_ind] = ClassificationDocumentType.MERGED
                    drivers_or_vehicles_changed_to_merged = True
                    if is_vehicles:
                        logger.warning(
                            f"vehicles table found in page {page_ind + 1} classified as drivers",
                            file_id=classified_file.file_id,
                        )
                if classified_file.page_level_classifications[page_ind] in [ClassificationDocumentType.VEHICLES]:
                    classified_file.page_level_classifications[page_ind] = ClassificationDocumentType.MERGED
                    classified_file.page_level_confidences[page_ind] = 1
                    drivers_or_vehicles_changed_to_merged = True
                    if is_drivers:
                        logger.warning(
                            f"drivers table found in page {page_ind + 1} classified as vehicles",
                            file_id=classified_file.file_id,
                        )

            if drivers_or_vehicles_changed_to_merged:
                classified_file.classification = ClassificationDocumentType.MERGED
                classified_file.file_type = CLASSIFICATION_TO_FILE_TYPE[ClassificationDocumentType.MERGED]

    for cols in dfs_by_cols:
        for df in dfs_by_cols[cols]:
            df.columns = non_empty_cols_by_cols[cols]
        df = pd.concat(dfs_by_cols[cols])
        df = split_columns(df)
        classification = classifs_by_cols[cols]
        min_page = min_page_by_cols[cols]
        max_page = max_page_by_cols[cols]
        new_classified_file = replace(
            classified_file,
            file_name=f"Extracted_from_{classified_file.file_name}_Pages_from_{min_page}_to_{max_page}_{classification.value}.xlsx",
            file_id=None,
            s3_key=None,
            file_type=CLASSIFICATION_TO_FILE_TYPE[classification],
            document_type=DocumentType.SPREADSHEET,
            is_internal=True,
            parent_file_id=(classified_file.file_id if classified_file.file_id else classified_file.parent_file_id),
            classification=classification,
            file_bytes=df_to_excel_bytes(df),
            page_level_classifications=[],
            page_level_confidences=[],
            page_level_additional_info=[],
            additional_info=None,
            split_points=[],
            text_results=None,
            internal_notes=[f"File created from {classified_file.file_name}"],
            initial_classification=classification,
        )

        extracted_spreadsheet_classified_files.append(new_classified_file)

    return extracted_spreadsheet_classified_files


def extract_vehicles_and_drivers_tables_as_spreadsheets(
    classified_file: ClassifiedFile,
) -> list[ClassifiedFile]:
    if not classified_file.file_bytes or not classified_file.text_results:
        return []
    try:
        new_document, orig_inds = get_pages_to_extract_tables_from(classified_file, filter_pages_with_img2table=False)
    except Exception as e:
        logger.warning(f"Error extracting tables from PDF: {e}", file_id=classified_file.file_id, exc_info=True)
        return []

    extracted_spreadsheet_classified_files = []

    if orig_inds:
        new_document_data = None
        try:
            new_document_data = new_document.write(no_new_id=True)
        except Exception:
            logger.warning("Failed to write PDF with old method", file_id=classified_file.file_id)
        selected_pages = [[page_ind, page_ind] for _, page_ind in orig_inds.items()]
        # running simplified method to cut the pages from document to potentially replace it
        simple_result = simple_page_cutting(
            classified_file.file_bytes, selected_pages=selected_pages, file_id=classified_file.file_id
        )
        # simplified method always runs but so far is only used as fallback
        new_document_data = new_document_data or simple_result

        if not new_document_data:
            logger.warning("Failed to write PDF with both methods", file_id=classified_file.file_id)
            return []
        table_dfs = get_table_dfs_from_pdf(
            BytesIO(new_document_data),
            use_tables_from_azure=True,
            use_azure_for_native_pdfs_ocr=True,
            azure_client=azure_client,
            azure_layout_client=azure_layout_client,
        )
        extracted_spreadsheet_classified_files = extract_tables_as_spreadsheet_classified_files(
            classified_file, table_dfs, orig_inds
        )

    return extracted_spreadsheet_classified_files


def is_emod(text: str) -> bool:
    is_workers_comp = any([keyword in text for keyword in WORKERS_COMP_KEYWORDS])
    is_exp_mod = any([keyword in text for keyword in EMOD_KEYWORDS])
    return is_workers_comp and is_exp_mod


def is_california_emod(text: str) -> bool:
    return (
        sum([keyword in text for keyword in CALIFORNIA_KEYWORDS_1]) / len(CALIFORNIA_KEYWORDS_1) > EMOD_THRESHOLD
        or sum([keyword in text for keyword in CALIFORNIA_KEYWORDS_2]) / len(CALIFORNIA_KEYWORDS_2) > EMOD_THRESHOLD
    )


def is_new_jersey_emod(text: str) -> bool:
    return sum([keyword in text for keyword in NEW_JERSEY_KEYWORDS]) / len(NEW_JERSEY_KEYWORDS) > EMOD_THRESHOLD


def is_new_york_emod(text: str) -> bool:
    return (
        sum([keyword in text for keyword in NEW_YORK_KEYWORDS_SET_1]) / len(NEW_YORK_KEYWORDS_SET_1) > EMOD_THRESHOLD
    ) and (
        (sum([keyword in text for keyword in NEW_YORK_KEYWORDS_SET_2]) / len(NEW_YORK_KEYWORDS_SET_2) > EMOD_THRESHOLD)
        or (
            sum([keyword in text for keyword in NEW_YORK_KEYWORDS_SET_3]) / len(NEW_YORK_KEYWORDS_SET_3)
            > EMOD_THRESHOLD
        )
        or sum([keyword in text for keyword in NEW_YORK_KEYWORDS_SET_4]) / len(NEW_YORK_KEYWORDS_SET_4) > EMOD_THRESHOLD
    )


def is_ncci_emod(text: str) -> bool:
    return (
        (sum([keyword in text for keyword in NCCI_KEYWORDS_SET_1]) / len(NCCI_KEYWORDS_SET_1) > EMOD_THRESHOLD)
        and (
            (sum([keyword in text for keyword in NCCI_KEYWORDS_SET_2]) / len(NCCI_KEYWORDS_SET_2) > EMOD_THRESHOLD)
            or sum([keyword in text for keyword in NCCI_KEYWORDS_SET_3]) / len(NCCI_KEYWORDS_SET_3) > EMOD_THRESHOLD
            or sum([keyword in text for keyword in NCCI_KEYWORDS_SET_4]) / len(NCCI_KEYWORDS_SET_4) > EMOD_THRESHOLD
            or sum([keyword in text for keyword in NCCI_KEYWORDS_SET_5]) / len(NCCI_KEYWORDS_SET_5) > EMOD_THRESHOLD
        )
    ) or (
        (sum([keyword in text for keyword in NCCI_KEYWORDS_SET_6]) / len(NCCI_KEYWORDS_SET_6) > EMOD_THRESHOLD)
        and (
            (sum([keyword in text for keyword in NCCI_KEYWORDS_SET_7]) / len(NCCI_KEYWORDS_SET_7) > EMOD_THRESHOLD)
            or sum([keyword in text for keyword in NCCI_KEYWORDS_SET_8]) / len(NCCI_KEYWORDS_SET_8) > EMOD_THRESHOLD
        )
    )


def is_pennsylvania_emod(text: str) -> bool:
    return (
        any(keyword in text for keyword in PENNSYLVANIA_KEYWORDS)
        or sum([keyword in text for keyword in PENNSYLVANIA_KEYWORDS_2]) / len(PENNSYLVANIA_KEYWORDS_2) > EMOD_THRESHOLD
    )


def is_minnesota_emod(text: str) -> bool:
    return sum([keyword in text for keyword in MINNESOTA_KEYWORDS]) / len(MINNESOTA_KEYWORDS) > EMOD_THRESHOLD


def is_wisconsin_emod(text: str) -> bool:
    return any(keyword in text for keyword in WISCONSIN_KEYWORDS)


def is_first_page(text: str) -> bool:
    return any(phrase in text for phrase in _FIRST_PAGE_TEXTS)


def detect_cab_reports(classified_file: ClassifiedFile) -> ClassifiedFile:
    for ind, (pred, text_result) in enumerate(
        zip(classified_file.page_level_classifications, classified_file.text_results)
    ):
        if is_cab_report(text_result):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.CAB_REPORT
            classified_file.page_level_confidences[ind] = 1
    return classified_file


def detect_eeoc(classified_file: ClassifiedFile) -> ClassifiedFile:
    for ind, (pred, text_result) in enumerate(
        zip(classified_file.page_level_classifications, classified_file.text_results)
    ):
        if is_eeoc(text_result):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.EEOC
            classified_file.page_level_confidences[ind] = 1
    return classified_file


def detect_emods(classified_file: ClassifiedFile, assume_true: bool = False) -> ClassifiedFile:
    for ind, (pred, text_result) in enumerate(
        zip(classified_file.page_level_classifications, classified_file.text_results)
    ):
        if not assume_true and pred not in [
            ClassificationDocumentType.UNKNOWN,
            ClassificationDocumentType.LOSS_RUN,
            ClassificationDocumentType.LOSS_SUMMARY,
            ClassificationDocumentType.WORK_COMP_EXPERIENCE,
        ]:
            continue
        text = clean_text(text_result.joined_text)
        if is_emod(text):
            emod_detected = True
            if is_california_emod(text):
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.WORK_COMP_EXPERIENCE_CA
                classified_file.page_level_confidences[ind] = 1
            elif is_new_jersey_emod(text):
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.WORK_COMP_EXPERIENCE_NJ
                classified_file.page_level_confidences[ind] = 1
            elif is_new_york_emod(text):
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.WORK_COMP_EXPERIENCE_NY
                classified_file.page_level_confidences[ind] = 1
            elif is_pennsylvania_emod(text):
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.WORK_COMP_EXPERIENCE_PA
                classified_file.page_level_confidences[ind] = 1
                logger.info("File classified as WORK_COMP_EXPERIENCE_PA", file_id=classified_file.file_id)
            elif is_minnesota_emod(text):
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.WORK_COMP_EXPERIENCE_MN
                classified_file.page_level_confidences[ind] = 1
                logger.info("File classified as WORK_COMP_EXPERIENCE_MN", file_id=classified_file.file_id)
            elif is_wisconsin_emod(text):
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.WORK_COMP_EXPERIENCE_WI
                classified_file.page_level_confidences[ind] = 1
                logger.info("File classified as WORK_COMP_EXPERIENCE_WI", file_id=classified_file.file_id)
            elif is_ncci_emod(text):
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.WORK_COMP_EXPERIENCE
                classified_file.page_level_confidences[ind] = 1
            else:
                emod_detected = False
            if emod_detected and is_first_page(text) and ind > 0:
                classified_file.split_points.append(ind)
        elif assume_true:
            logger.warning("Xmod type not detected but file type set to Xmod", file_id=classified_file.file_id)
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.WORK_COMP_EXPERIENCE
            classified_file.page_level_confidences[ind] = 1
    return classified_file


def check_if_consolidated_financial_statement(
    text: str, text_result: TextExtractionResult, is_previous_page_consolidated: bool
) -> tuple[bool, bool]:
    text = clean_text(text)
    may_be_intro = num_keywords_in_text(text, INTRO_PAGE_PARTIAL)
    num_financial_statement_names = num_keywords_in_text(text, BALANCE_SHEET_PHRASES)
    has_negative_keywords = (
        num_keywords_in_text(
            text[:CONSOLIDATED_FINANCIAL_STATEMENT_TITLE_FIRST_CHARS],
            CONSOLIDATED_FINANCIAL_STATEMENT_NEGATIVE_KEYWORDS,
        )
        > 0
    )

    num_of_consolidated_statement_phrases = sum(
        [keyword in text for keyword in CONSOLIDATED_FINANCIAL_STATEMENT_KEYWORDS]
    )
    num_monetary_data = len(
        {clean_numerical(word.text) for word in text_result.words if clean_numerical(word.text) is not None}
    )
    _MONETARY_NORMAL_TH = 10
    _PHRASES_WITH_TITLE_NORMAL_TH = 2
    _MONETARY_DATA_TH = _MONETARY_NORMAL_TH if not is_previous_page_consolidated else 4
    _PHRASES_WITH_TITLE_TH = _PHRASES_WITH_TITLE_NORMAL_TH if not is_previous_page_consolidated else 1

    text_long_sentences = " ".join(
        [line.text for line in text_result.text_items if line.x_max - line.x_min > text_result.page_width / 2]
    )
    word_count_in_sentences = [len(word_tokenize(sentence)) for sentence in sent_tokenize(text_long_sentences)]
    long_sentences_count = len([word_cnt for word_cnt in word_count_in_sentences if word_cnt > 6])

    has_upper_text_negative_phrase = False
    text_upper = "\n".join([line.text for line in text_result.text_items if line.y_max < text_result.page_height * 0.3])

    for phrase in CONSOLIDATED_FINANCIAL_STATEMENT_NEGATIVE_UPPER_TEXT_KEYWORDS:
        has_upper_text_negative_phrase = has_upper_text_negative_phrase or any(
            [clean_text(text_line).startswith(phrase) for text_line in text_upper.split("\n")]
        )

    if (
        (may_be_intro and num_financial_statement_names >= 2)
        or num_financial_statement_names >= 3
        or num_monetary_data < _MONETARY_DATA_TH
        or has_negative_keywords
        or long_sentences_count >= 3
        or has_upper_text_negative_phrase
    ):
        return False, False
    elif any(
        [phrase in text[:CONSOLIDATED_FINANCIAL_STATEMENT_TITLE_FIRST_CHARS] for phrase in BALANCE_SHEET_PHRASES]
    ) and (num_of_consolidated_statement_phrases >= _PHRASES_WITH_TITLE_TH):
        # Second variable returns if lifted conditions were met.
        if (
            num_monetary_data < _MONETARY_NORMAL_TH
            or num_of_consolidated_statement_phrases < _PHRASES_WITH_TITLE_NORMAL_TH
        ):
            return True, True
        else:
            return True, False
    elif num_of_consolidated_statement_phrases >= 4:
        if num_monetary_data < _MONETARY_NORMAL_TH:
            return True, True
        else:
            return True, False
    else:
        return False, False


def detect_consolidated_financial_statements(classified_file: ClassifiedFile) -> ClassifiedFile:
    if not classified_file.page_level_additional_info:
        classified_file.page_level_additional_info = [None] * len(classified_file.text_results)
    can_use_lifted_conditions = False
    for ind, (pred, text_result) in enumerate(
        zip(classified_file.page_level_classifications, classified_file.text_results)
    ):
        text = text_result.joined_text or ""
        if len(text) < 200 and "supplementary information" in text.lower():
            logger.info(
                "Supplementary information page detected",
                file_id=classified_file.file_id,
                submission_id=classified_file.submission_id,
                page_num=ind + 1,
            )
            break
        res = check_if_consolidated_financial_statement(text, text_result, can_use_lifted_conditions)
        # We do not want to use lifted conditions more than once for consecutive pages
        can_use_lifted_conditions = res[0] and not res[1]
        if pred == ClassificationDocumentType.FINANCIAL_STATEMENT and res[0]:
            classified_file.page_level_classifications[ind] = (
                ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT
            )
            classified_file.page_level_confidences[ind] = 1

    # Backfill consolidated pages
    last_consolidated_page = None
    for ind, (pred, text_result) in enumerate(
        zip(classified_file.page_level_classifications, classified_file.text_results)
    ):
        if pred == ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT:
            if last_consolidated_page is not None and 1 < ind - last_consolidated_page <= 4:
                for i in range(1, ind - last_consolidated_page):
                    if (
                        classified_file.page_level_classifications[ind - i]
                        == ClassificationDocumentType.FINANCIAL_STATEMENT
                    ):
                        classified_file.page_level_classifications[ind - i] = (
                            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT
                        )
                        classified_file.page_level_confidences[ind - i] = 1
            last_consolidated_page = ind

    return classified_file


def _is_conifer_policy(text_result: TextExtractionResult) -> bool:
    unique_items = set()
    if "conifer" in text_result.joined_text.lower():
        unique_items.add("conifer")
    for item in text_result.text_items:
        if (
            item.y_max < text_result.page_height * 0.6
            and any([phrase in item.text.lower() for phrase in ["insurance", "policy", "documentation"]])
            and fuzz.ratio(item.text.split("\n")[0].lower(), "insurance policy documentation") > 95
        ):
            unique_items.add("insurance policy documentation")
        if (
            item.x_max > text_result.page_width * 0.8
            and item.y_max < text_result.page_height * 0.15
            and "@" in item.text
            and fuzz.ratio(item.text.split("@")[-1].lower(), "coniferinsurance.com") > 95
        ):
            unique_items.add("email")
        if (
            item.x_min < text_result.page_width * 0.1
            and item.y_max < text_result.page_height * 0.5
            and any([phrase in item.text.lower() for phrase in ["agency", "name"]])
            and fuzz.ratio(item.text.split("\n")[0].lower(), "agency name") > 95
        ):
            unique_items.add("agency name")
        if "thank you for the confidence" in item.text.lower():
            unique_items.add("thank you for the confidence")
    return len(unique_items) >= 4


def detect_conifer_policy(classified_file: ClassifiedFile) -> ClassifiedFile:
    policy_started = False
    for idx, text_result in enumerate(classified_file.text_results):
        if (not policy_started and _is_conifer_policy(text_result)) or (
            policy_started
            and classified_file.page_level_classifications[idx]
            in [
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.POLICY,
                ClassificationDocumentType.QUOTE,
                ClassificationDocumentType.GENERAL_LIABILITY_QUOTE,
            ]
        ):
            if not policy_started:
                logger.info(
                    "Identified Conifer policy",
                    file_id=classified_file.file_id,
                    submission_id=classified_file.submission_id,
                    page_num=idx + 1,
                )
            page_level_info = classified_file.page_level_additional_info[idx]
            if page_level_info:
                page_level_info.policy_carrier = "CONIFER"
            else:
                page_level_info = FileAdditionalInfo(policy_carrier="CONIFER")
            classified_file.page_level_additional_info[idx] = page_level_info
            classified_file.page_level_classifications[idx] = ClassificationDocumentType.POLICY
            policy_started = True
        else:
            policy_started = False
    return classified_file


def detect_acords(classified_file: ClassifiedFile, assume_true: bool = False) -> ClassifiedFile:
    if not classified_file.page_level_additional_info:
        classified_file.page_level_additional_info = [None] * len(classified_file.text_results)
    for ind, (pred, text_result) in enumerate(
        zip(classified_file.page_level_classifications, classified_file.text_results)
    ):
        acord_version = detect_acord_version(text_result)
        if pred == ClassificationDocumentType.ACORD_FORM and acord_version is None:
            logger.warning(
                "File classified as ACORD but no version detected",
                file_id=classified_file.file_id,
                misclassified_result=text_result.joined_text,
            )
        if acord_version and classified_file.page_level_additional_info[ind] is None:
            classified_file.page_level_additional_info[ind] = FileAdditionalInfo(acord=acord_version)
        elif acord_version:
            classified_file.page_level_additional_info[ind].acord = acord_version
        if not assume_true and pred == ClassificationDocumentType.ACORD_FORM and acord_version is None:
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.UNKNOWN
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_vehicle_schedule(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_129
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_163(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.DRIVERS
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_101(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_101
            classified_file.page_level_confidences[ind] = 1
            if ind > 0:
                classified_file.split_points.extend([ind, ind + 1])
        elif acord_version and is_acord_125(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_125
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_126(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_126
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_127(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_127
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_128(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_128
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_823(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_823
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_829(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_829
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_140(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_140
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_131(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_131
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_130(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_130
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_139(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_139
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_160(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_160
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_applied_130(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.APPLIED_130
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_211(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_211
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_applied_98(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.APPLIED_98
            if ind > 0:
                classified_file.split_points.extend([ind, ind + 1])
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_applied_126(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.APPLIED_126
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_applied_125(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.APPLIED_125
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_ofappinfcni(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.OFAPPINFCNI
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_ofschhaz(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.OFSCHHAZ
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_35(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_35
            classified_file.page_level_confidences[ind] = 1
        elif acord_version and is_acord_175(acord_version):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_175
            classified_file.page_level_confidences[ind] = 1
        # other classification but acord version extracted
        elif acord_version and acord_version.version_id.startswith("A"):  # skip OF classifications
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_FORM
            classified_file.page_level_confidences[ind] = 1
        elif (
            acord_version
            and is_acord_130(acord_version)
            and classified_file.page_level_classifications[ind] == ClassificationDocumentType.SUPPLEMENTAL_APPLICATION
        ):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.ACORD_130
            classified_file.page_level_confidences[ind] = 1

    try:
        classified_file = backfill_acord_classification(classified_file)
    except:
        logger.exception("Backfilling acord classification failed", classified_file=classified_file)
    return classified_file


def detect_loss_runs(classified_file: ClassifiedFile, assume_is_loss_run: bool = False) -> ClassifiedFile:
    """
    For sequences of pages that are loss runs we additionally check for 3 things:
    - is file a no-claim file? all pages need to have either a trait of a no-claim or no monetary info
    - is file a special snowflake? all pages need to conform to a template - not a loss run
    - does at least one page have traits of loss details? if not a single one has - not a loss run
    """
    no_claim_no_monetary = [False] * len(classified_file.page_level_classifications)
    is_page_loss_run = [False] * len(classified_file.page_level_classifications)
    is_page_special_snowflake = [False] * len(classified_file.page_level_classifications)
    has_page_loss_run_traits = [False] * len(classified_file.page_level_classifications)
    is_page_loss_summary = [False] * len(classified_file.page_level_classifications)
    for ind, (pred, text_result) in enumerate(
        zip(classified_file.page_level_classifications, classified_file.text_results)
    ):
        text = text_result.joined_text
        if not assume_is_loss_run and pred == ClassificationDocumentType.LOSS_RUN and not is_loss_run(text):
            classified_file.page_level_classifications[ind] = ClassificationDocumentType.UNKNOWN
            classified_file.page_level_confidences[ind] = 1
        elif pred == ClassificationDocumentType.LOSS_RUN:
            is_page_loss_run[ind] = True
            is_page_special_snowflake[ind] = is_special_snowflake(text_result)
            has_page_loss_run_traits[ind] = has_loss_run_traits(text_result)
            is_page_loss_summary[ind] = is_loss_summary(text_result)
            no_claim = no_claim_status(text_result)
            no_claim_no_monetary[ind] = no_claim == NoClaimStatus.DEFINITELY or (
                no_claim != NoClaimStatus.DISQUALIFIED
                and (
                    not has_monetary_data(text_result)
                    or classified_file.page_level_classifications[ind] == ClassificationDocumentType.UNKNOWN
                )
            )
    start_idx = None
    for ind, is_lr in enumerate(is_page_loss_run):
        if is_lr:
            start_idx = ind if start_idx is None else start_idx
        else:
            if start_idx is not None:
                num_entries = ind - start_idx
                if all(
                    _has_no_detail_data(nc, ls, lrt)
                    for nc, ls, lrt in zip(
                        no_claim_no_monetary[start_idx:ind],
                        is_page_loss_summary[start_idx:ind],
                        has_page_loss_run_traits[start_idx:ind],
                    )  # all pages are either no-claim or loss summary - use split
                ):
                    for idx in range(start_idx, start_idx + num_entries):
                        classified_file.page_level_classifications[idx] = _determine_no_loss_type(
                            is_page_loss_summary[idx],
                            has_page_loss_run_traits[idx],
                            assume_is_loss_run,
                        )
                        classified_file.page_level_confidences[idx] = 1
                elif all(no_claim_no_monetary[start_idx:ind]) and any(has_page_loss_run_traits[start_idx:ind]):
                    classified_file.page_level_classifications[start_idx:ind] = [
                        ClassificationDocumentType.LOSS_RUN_NO_CLAIM
                    ] * num_entries
                    classified_file.page_level_confidences[start_idx:ind] = [1] * num_entries
                elif (
                    not any(has_page_loss_run_traits[start_idx:ind]) and any(is_page_loss_summary[start_idx:ind])
                ) or all(is_page_special_snowflake[start_idx:ind]):
                    classified_file.page_level_classifications[start_idx:ind] = [
                        ClassificationDocumentType.LOSS_SUMMARY
                    ] * num_entries
                    classified_file.page_level_confidences[start_idx:ind] = [1] * num_entries
                elif not any(has_page_loss_run_traits[start_idx:ind]):
                    classification = ClassificationDocumentType.UNKNOWN
                    if assume_is_loss_run:
                        # assumed loss run but no loss run traits, not a summary but also no monetary data
                        # so assume no-claim
                        classification = (
                            ClassificationDocumentType.LOSS_RUN_NO_CLAIM
                            if all(no_claim_no_monetary[start_idx:ind])
                            else ClassificationDocumentType.LOSS_RUN
                        )
                    classified_file.page_level_classifications[start_idx:ind] = [classification] * num_entries
                    classified_file.page_level_confidences[start_idx:ind] = [1] * num_entries
            start_idx = None
    if start_idx is not None:
        num_entries = len(no_claim_no_monetary) - start_idx
        if all(
            _has_no_detail_data(nc, ls, lrt)
            for nc, ls, lrt in zip(
                no_claim_no_monetary[start_idx:], is_page_loss_summary[start_idx:], has_page_loss_run_traits[start_idx:]
            )  # all pages are either no-claim or loss summary - use split
        ):
            for idx in range(start_idx, start_idx + num_entries):
                classified_file.page_level_classifications[idx] = _determine_no_loss_type(
                    is_page_loss_summary[idx],
                    has_page_loss_run_traits[idx],
                    assume_is_loss_run,
                )
                classified_file.page_level_confidences[idx] = 1
        elif all(no_claim_no_monetary[start_idx:]) and any(has_page_loss_run_traits[start_idx:]):
            classified_file.page_level_classifications[start_idx:] = [
                ClassificationDocumentType.LOSS_RUN_NO_CLAIM
            ] * num_entries
            classified_file.page_level_confidences[start_idx:] = [1] * num_entries
        elif (not any(has_page_loss_run_traits[start_idx:]) and any(is_page_loss_summary[start_idx:])) or all(
            is_page_special_snowflake[start_idx:]
        ):
            classified_file.page_level_classifications[start_idx:] = [
                ClassificationDocumentType.LOSS_SUMMARY
            ] * num_entries
            classified_file.page_level_confidences[start_idx:] = [1] * num_entries
        elif not any(has_page_loss_run_traits[start_idx:]):
            classification = ClassificationDocumentType.UNKNOWN
            if assume_is_loss_run:
                # assumed loss run but no loss run traits, not a summary but also no monetary data so assume no-claim
                classification = (
                    ClassificationDocumentType.LOSS_RUN_NO_CLAIM
                    if all(no_claim_no_monetary[start_idx:])
                    else ClassificationDocumentType.LOSS_RUN
                )
            classified_file.page_level_classifications[start_idx:] = [classification] * num_entries
            classified_file.page_level_confidences[start_idx:] = [1] * num_entries
    return classified_file


def _has_no_detail_data(no_claim_no_monetary: bool, is_loss_summary: bool, has_loss_run_traits: bool) -> bool:
    return (is_loss_summary and not has_loss_run_traits) or no_claim_no_monetary


def _determine_no_loss_type(
    is_loss_summary: bool, has_loss_run_traits: bool, assume_loss_run: bool = False
) -> ClassificationDocumentType:
    if is_loss_summary and not has_loss_run_traits:
        return ClassificationDocumentType.LOSS_SUMMARY
    if not assume_loss_run and not has_loss_run_traits:
        return ClassificationDocumentType.UNKNOWN
    return ClassificationDocumentType.LOSS_RUN_NO_CLAIM


def is_gl_quote(text_results: list[TextExtractionResult]) -> bool:
    for ind, text_result in enumerate(text_results):
        cleaned_text = clean_text(text_result.joined_text)
        if "general liability" not in cleaned_text:
            continue
        if "excess of" in cleaned_text:
            return False
        gl_position = cleaned_text.find("general liability")
        if "endorsement changes" in cleaned_text[:gl_position]:
            return False
        if "optional" in cleaned_text[:gl_position]:
            return False
        return True
    return False


def is_nda(text: str) -> bool:
    return any(keyword in text for keyword in NDA_PHRASES)


def is_joinder(text: str) -> bool:
    return any(keyword in text for keyword in INVESTMENT_JOINDER_PHRASES)


def _detect_nda(text_results: list[TextExtractionResult]) -> list[ClassificationDocumentType]:
    nda_started, joinder_started = False, False
    result_classification = []
    for text_result in text_results:
        cleaned_text = clean_text(text_result.joined_text)[:NDA_MAX_CHARS]
        if not nda_started and not joinder_started:
            if is_joinder(cleaned_text):
                joinder_started = True
                result_classification.append(ClassificationDocumentType.INVESTMENT_JOINDER)
            elif is_nda(cleaned_text):
                nda_started = True
                result_classification.append(ClassificationDocumentType.NDA)
            else:
                result_classification.append(ClassificationDocumentType.UNKNOWN)
        elif joinder_started:
            result_classification.append(ClassificationDocumentType.INVESTMENT_JOINDER)
        elif nda_started:
            if is_joinder(cleaned_text):
                joinder_started = True
                result_classification.append(ClassificationDocumentType.INVESTMENT_JOINDER)
            else:
                result_classification.append(ClassificationDocumentType.NDA)
        else:
            result_classification.append(ClassificationDocumentType.UNKNOWN)
    return result_classification


def detect_letter_of_intent(classified_file: ClassifiedFile) -> ClassifiedFile:
    groups = [
        [item[0] for item in group[1]]
        for group in itertools.groupby(
            enumerate(classified_file.page_level_classifications), key=operator.itemgetter(1)
        )
    ]
    for group in groups:
        if classified_file.page_level_classifications[group[0]] != ClassificationDocumentType.UNKNOWN:
            continue
        cleaned_first_page = clean_text(classified_file.text_results[0].joined_text)[:LETTER_OF_INTENT_MAX_CHARS]
        if any(keyword in cleaned_first_page for keyword in LETTER_OF_INTENT_PHRASES):
            for ind in group:
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.LETTER_OF_INTENT
                classified_file.page_level_confidences[ind] = 0.5
    return classified_file


def detect_nda(classified_file: ClassifiedFile) -> ClassifiedFile:
    groups = [
        [item[0] for item in group[1]]
        for group in itertools.groupby(
            enumerate(classified_file.page_level_classifications), key=operator.itemgetter(1)
        )
    ]
    for group in groups:
        if classified_file.page_level_classifications[group[0]] != ClassificationDocumentType.UNKNOWN:
            continue
        group_text_results = [classified_file.text_results[ind] for ind in group]
        result_classification = _detect_nda(group_text_results)
        for ind, classification in zip(group, result_classification):
            if classification == ClassificationDocumentType.UNKNOWN:
                continue
            classified_file.page_level_classifications[ind] = classification
            classified_file.page_level_confidences[ind] = 0.5
    return classified_file


def _is_investment_memo(text_results: list[TextExtractionResult]) -> bool:
    for text_result in text_results:
        cleaned_text = clean_text(text_result.joined_text)[:INVESTMENT_MEMO_MAX_CHARS]
        if INVESTMENT_MEMORANDUM_MATCHER.match(cleaned_text):
            return True
    return False


def _is_purchase_agreement(text_results: list[TextExtractionResult]) -> bool:
    cleaned_text = clean_text(text_results[0].joined_text)[:PURCHASE_AGREEMENT_MAX_CHARS]
    if PURCHASE_AGREEMENT_MATCHER.match(cleaned_text):
        return True
    return False


def detect_iftas(classified_file: ClassifiedFile) -> ClassifiedFile:
    for ind, (pred, confidence, text_result) in enumerate(
        zip(
            classified_file.page_level_classifications,
            classified_file.page_level_confidences,
            classified_file.text_results,
        )
    ):
        if pred != ClassificationDocumentType.UNKNOWN and confidence > 0.5:
            continue

        text = clean_text(text_result.joined_text)
        classified_file.page_level_confidences[ind] = 0.9

        for state, keywords in IFTA_KEYWORDS.items():
            if sum([keyword in text for keyword in keywords]) / len(keywords) > IFTA_THRESHOLD:
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.IFTA
                break

    new_predictions = smooth_predictions(classified_file, limit_to_classification=ClassificationDocumentType.IFTA)
    classified_file.page_level_classifications = new_predictions

    return classified_file


def detect_investment_memo(classified_file: ClassifiedFile) -> ClassifiedFile:
    modified_page_level = [
        (
            ClassificationDocumentType.UNKNOWN
            if classification in [ClassificationDocumentType.DIRECTORS_AND_OFFICERS]
            else classification
        )
        for classification in classified_file.page_level_classifications
    ]
    groups = [
        [item[0] for item in group[1]]
        for group in itertools.groupby(enumerate(modified_page_level), key=operator.itemgetter(1))
    ]
    for group in groups:
        if classified_file.page_level_classifications[group[0]] != ClassificationDocumentType.UNKNOWN:
            continue
        group_text_results = [classified_file.text_results[ind] for ind in group][:INVESTMENT_MEMO_MAX_PAGES]
        if _is_investment_memo(group_text_results):
            for ind in group:
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.INVESTMENT_MEMORANDUM
                classified_file.page_level_confidences[ind] = 0.5
    return classified_file


def detect_purchase_agreement(classified_file: ClassifiedFile) -> ClassifiedFile:
    groups = [
        [item[0] for item in group[1]]
        for group in itertools.groupby(
            enumerate(classified_file.page_level_classifications), key=operator.itemgetter(1)
        )
    ]
    for group in groups:
        if classified_file.page_level_classifications[group[0]] != ClassificationDocumentType.UNKNOWN:
            continue
        group_text_results = [classified_file.text_results[ind] for ind in group][:PURCHASE_AGREEMENT_MAX_CHARS]
        if _is_purchase_agreement(group_text_results):
            for ind in group:
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.PURCHASE_AGREEMENT_PDF
                classified_file.page_level_confidences[ind] = 0.5
    return classified_file


def is_quote(text_results: list[TextExtractionResult]) -> bool:
    quote_words = ["quote", "proposal", "specimen"]
    quote_phrases = ["in return for the payment of the premium"]
    text = " ".join([clean_text(text_result.joined_text) for text_result in text_results])
    return any(phrase in text for phrase in quote_phrases) or any(word in text.split() for word in quote_words)


def _should_classify_policy(classified_file: ClassifiedFile) -> bool:
    return classified_file.organization_id in [
        ExistingOrganizations.KalepaTest.value,
        ExistingOrganizations.MarkelDemo.value,
    ]


def detect_quotes(classified_file: ClassifiedFile) -> ClassifiedFile:
    if classified_file.document_type != DocumentType.PDF:
        return classified_file
    quote_by_name = any(phrase in classified_file.file_name.lower() for phrase in QUOTE_NAMES)
    groups = [
        [item[0] for item in group[1]]
        for group in itertools.groupby(
            enumerate(classified_file.page_level_classifications), key=operator.itemgetter(1)
        )
    ]
    for group in groups:
        if classified_file.page_level_classifications[group[0]] != ClassificationDocumentType.QUOTE:
            continue
        group_text_results = [classified_file.text_results[ind] for ind in group]
        is_group_quote = is_quote(group_text_results)
        if not (is_group_quote or quote_by_name):
            for ind in group:
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.UNKNOWN
                classified_file.page_level_confidences[ind] = 0.5
        elif is_gl_quote(group_text_results):
            for ind in group:
                classified_file.page_level_classifications[ind] = ClassificationDocumentType.GENERAL_LIABILITY_QUOTE
                classified_file.page_level_confidences[ind] = 0.5
    return classified_file


def detect_industry_specific_documents(
    classified_file: ClassifiedFile, base_classification: ClassificationDocumentType | None = None
) -> ClassifiedFile:
    if not classified_file.text_results:
        return classified_file

    assume_acord = base_classification == ClassificationDocumentType.ACORD_FORM
    assume_loss_run = base_classification == ClassificationDocumentType.LOSS_RUN

    if not base_classification or assume_acord:
        classified_file = detect_acords(classified_file, assume_true=assume_acord)

    if not base_classification or assume_loss_run:
        classified_file = detect_loss_runs(classified_file, assume_is_loss_run=assume_loss_run)

    if not base_classification:
        classified_file = detect_cab_reports(classified_file)
        classified_file = detect_eeoc(classified_file)
        classified_file = detect_iftas(classified_file)

    if not base_classification or base_classification == ClassificationDocumentType.SUPPLEMENTAL_APPLICATION:
        classified_file = detect_project_specific_supplementals(classified_file)
        classified_file = detect_construction_supplementals(classified_file)
        classified_file = detect_residential_supplementals(classified_file)
        classified_file = detect_restaurant_supplementals(classified_file)

    if not base_classification or base_classification == ClassificationDocumentType.WORK_COMP_EXPERIENCE:
        classified_file = detect_emods(classified_file)

    if not base_classification or (
        base_classification == ClassificationDocumentType.FINANCIAL_STATEMENT
        and classified_file.classification != ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT
    ):
        classified_file = detect_consolidated_financial_statements(classified_file)

    if not base_classification:
        classified_file = detect_erisa_5500(classified_file)
        classified_file = detect_quotes(classified_file)
        if classified_file.organization_id in [
            ExistingOrganizations.KalepaTest.value,
            ExistingOrganizations.KalepaDemo.value,
            ExistingOrganizations.KalepaNewDemo.value,
        ]:
            classified_file = detect_purchase_agreement(classified_file)
            classified_file = detect_letter_of_intent(classified_file)
            classified_file = detect_nda(classified_file)
            classified_file = detect_investment_memo(classified_file)
        classified_file = detect_conifer_policy(classified_file)

    return classified_file
