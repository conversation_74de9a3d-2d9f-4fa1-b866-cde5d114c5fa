import re
from collections.abc import Callable

from infrastructure_common.logging import get_logger
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.models.file_additional_info import AcordClassificationInfo

from src.models.classifiers.document_classifier.classified_file import ClassifiedFile
from src.models.classifiers.document_classifier.constants import (
    ACORD_FORM_VERTICAL_BBOX_THRESHOLD,
    ACORD_RIGHT_HORTIZONTAL_BBOX_THRESHOLD,
)
from src.models.classifiers.document_classifier.constants_acords import (
    INVALID_ACORDS_MAPPING,
    VALID_ACORDS,
    AllSeenAcordsVersions,
)
from src.models.classifiers.document_classifier.text_extraction import (
    TextExtractionResult,
    TextItem,
)

ACORD_BASE_REGEX = re.compile(r"ACORD\s\d{1,3}\s\(\d{1,4}.\d{2}\)")
ACORD_DELIMITER_REGEX = re.compile(r"ACORD\s\d{1,3}\s[A-Z]{1,4}\s\(\d{1,4}.\d{2}\)")
ACORD_DELIMITER_AT_THE_END_REGEX = re.compile(r"ACORD\s\d{1,3}\s\(\d{1,4}.\d{2}\)\s[A-Z]{2}(\s|$)")
ACORD_LOOSE_REGEX = re.compile(r"ACORD\s\d{1,4}\s\(?\d{1,4}.\d{2}\)?")
APPLIED_BASE_REGEX = re.compile(r"APPLIED\s\d{1,3}\s\(\d{4}.\d{2}\)")
APPLIED_DELIMITER_REGEX = re.compile(r"APPLIED\s\d{1,3}[A-Z]{2,5}\s\(\d{4}.\d{2}\)")
OF_REGEX = re.compile(r"(^|\s)OF[A-Z]{6,10}")
OF_VERSION_REGEX = re.compile(r"(^|\s)OF[A-Z]{6,10}\s\(\d{4}.\d{2}\)")

_INVALID_ACORD_CHARS_REGEX = r"[^a-zA-Z\d\s/()]"
_REMOVE_ADDITIONAL_CHARS_FROM_ACORD_REV_REGEX = r"(ACORD\s\d{1,3}\s\(\d{1,4}).+?(\d{2}\))"
_REMOVE_ADDITIONAL_CHARS_FROM_APPLIED_REV_REGEX = r"(ACORD\s\d{1,3}\s\(\d{1,4}).?(\d{2}\))"
_SPACE_ACORD_FROM_DIGIT_REGEX = re.compile(r"(?i)\bACORD[.\s]*(\d)")
_REMOVE_SPACES_IN_DATES = re.compile(r"\(\s*(\d+)\s*([/.])\s*(\d+)\s*\)")
_SPACE_DIGIT_FROM_OPEN_PAREN_REGEX = re.compile(r"(\d)\(")

logger = get_logger()


def _correct_spacing(input_str: str) -> str:
    text = _SPACE_ACORD_FROM_DIGIT_REGEX.sub(r"ACORD \1", input_str)
    text = _REMOVE_SPACES_IN_DATES.sub(r"(\1\2\3)", text)
    text = _SPACE_DIGIT_FROM_OPEN_PAREN_REGEX.sub(r"\1 (", text)
    return text


def _get_form_year_and_version(form_date_version: str) -> tuple[str, str]:
    form_date_version = form_date_version.lstrip("(").rstrip(")")
    if "/" in form_date_version:
        form_year, form_version = form_date_version.split("/")
        return form_year, form_version
    return form_date_version[:-3], form_date_version[-2:]


def _get_valid_text_items(text_items: list[TextItem], page_width: float, page_height: float) -> list[TextItem]:
    valid_texts = [
        text_item
        for text_item in text_items
        if text_item.y_min >= ACORD_FORM_VERTICAL_BBOX_THRESHOLD * page_height
        and text_item.x_min <= ACORD_RIGHT_HORTIZONTAL_BBOX_THRESHOLD * page_width
    ]
    return valid_texts


def _get_texts_from_valid_area(
    text_result: TextExtractionResult, get_text_items_fn: Callable = _get_valid_text_items
) -> list[str]:
    valid_paragraphs = get_text_items_fn(
        text_result.text_items, page_width=text_result.page_width, page_height=text_result.page_height
    )
    blocks = {paragraph.block_no for paragraph in valid_paragraphs}

    words_by_blocks = [word for word in text_result.words if word.block_no in blocks]
    valid_words = get_text_items_fn(
        words_by_blocks, page_width=text_result.page_width, page_height=text_result.page_height
    )

    merged_texts = []
    for block_id in blocks:
        merged_texts.append(" ".join([w.text for w in valid_words if w.block_no == block_id]))
    return merged_texts


def _clean_text_before_matching(text: str) -> str:
    text = re.sub(r"\(\s+", "(", text)
    text = re.sub(r"\s+\)", ")", text)
    return text


def detect_acord_version(text_result: TextExtractionResult) -> AcordClassificationInfo | None:
    texts = _get_texts_from_valid_area(text_result)
    for text in texts:
        if result := extract_acord_version(text):
            return validate_acord_version(result)


def validate_acord_version(extracted_info: AcordClassificationInfo | None) -> AcordClassificationInfo | None:
    if not extracted_info:
        return None

    try:
        version_enum = AllSeenAcordsVersions(extracted_info.version_id)
    except ValueError:
        logger.warning("Previously Unseen ACORD version", acord_version_text=extracted_info.version_id)
        return extracted_info

    if version_enum in INVALID_ACORDS_MAPPING:
        mapped_version_enum = INVALID_ACORDS_MAPPING[version_enum]
        if mapped_version_enum is None:
            logger.warning("ACORD version invalid, no mapped replacement", acord_version_text=version_enum.value)
            return None

        valid_info = VALID_ACORDS.get(mapped_version_enum)
        if valid_info:
            return valid_info
        else:
            logger.warning("ACORD version previously seen but unverified", acord_version_text=version_enum.value)
            return None

    if version_enum in VALID_ACORDS:
        return extracted_info

    logger.warning(
        "ACORD version is known but not in valid or invalid maps. Passing as-is.", acord_version_text=version_enum.value
    )

    return extracted_info


def extract_acord_version(text: str, use_seen: bool = True) -> AcordClassificationInfo | None:
    try:
        text = _clean_text_before_matching(text)
        text = re.sub(_INVALID_ACORD_CHARS_REGEX, " ", text)
        text = _correct_spacing(text)
        text = re.sub(_REMOVE_ADDITIONAL_CHARS_FROM_ACORD_REV_REGEX, r"\g<1>/\g<2>", text)
        text = re.sub(_REMOVE_ADDITIONAL_CHARS_FROM_APPLIED_REV_REGEX, r"\g<1>/\g<2>", text)

        if match := re.search(ACORD_DELIMITER_AT_THE_END_REGEX, text):
            return _acord_delimiter_at_the_end_form_extraction(match.group(0))
        elif match := re.search(ACORD_BASE_REGEX, text):
            return _acord_base_form_extraction(match.group(0))
        elif match := re.search(ACORD_DELIMITER_REGEX, text):
            return _acord_delimiter_form_extraction(match.group(0))
        elif match := re.search(APPLIED_BASE_REGEX, text):
            return _acord_applied_form_extraction(match.group(0))
        elif match := re.search(APPLIED_DELIMITER_REGEX, text):
            return _acord_applied_delimiter_form_extraction(match.group(0))
        elif "OFFICER" in text:
            return None
        elif match := re.search(OF_VERSION_REGEX, text):
            return _acord_of_date_form_extraction(match.group(0).strip())
        elif match := re.search(OF_REGEX, text):
            return _acord_of_form_extraction(match.group(0).strip())
        elif match := re.search(ACORD_LOOSE_REGEX, text):
            try:
                result = _acord_base_form_extraction(match.group(0))
            except ValueError:
                return None
            logger.warning("Unusual ACORD version number extracted", acord_version_text=text, result=result)
            return result

    except ValueError:
        logger.exception("Unable to parse a likely valid ACORD version", acord_version_text=text)
        return None


def _acord_base_form_extraction(text: str) -> AcordClassificationInfo:
    """
    Extracting information from format ACORD 125 (2009/16)
    """
    split_text = text.split()
    if len(split_text) == 3:
        _, form_number, form_date_version = split_text
        form_number = int(form_number)
        form_year, form_version = _get_form_year_and_version(form_date_version)
    elif len(split_text) == 4:
        _, form_number, form_year, form_version = split_text
        form_number = int(form_number)
        form_year = int(form_year)
        form_year, form_version = _get_form_year_and_version(f"{form_year}/{form_version}")
    else:
        raise ValueError("Unable to parse a valid ACORD version")
    return AcordClassificationInfo(
        version_id=f"ACORD_{form_number}_{form_year}_{form_version}".upper(),
        form_name=f"ACORD_{form_number}",
        form_year=int(form_year),
        form_version=form_version,
    )


def _acord_delimiter_form_extraction(text: str) -> AcordClassificationInfo:
    """
    Extracting information from format ACORD 125 OH (2009/16)
    """
    split_text = text.split()
    form_date_version, form_number, form_delimiter = "", "", ""
    if len(split_text) == 4:
        _, form_number, form_delimiter, form_date_version = text.split()
    elif len(split_text) == 5:
        _, form_number, form_delimiter, form_year, form_version = text.split()
        form_date_version = f"{form_year}/{form_version}"
    else:
        raise ValueError("Unable to parse a valid ACORD version")
    form_year, form_version = _get_form_year_and_version(form_date_version)
    return AcordClassificationInfo(
        version_id=f"ACORD_{form_number}_{form_delimiter}_{form_year}_{form_version}".upper(),
        form_name=f"ACORD_{form_number}",
        form_year=int(form_year),
        form_version=form_version,
        form_delimiter=form_delimiter,
    )


def _acord_delimiter_at_the_end_form_extraction(text: str) -> AcordClassificationInfo:
    """
    Extracting information from format ACORD 125 (2009/16) OH
    """
    split_text = text.split()
    if len(split_text) == 4:
        _, form_number, form_date_version, form_delimiter = split_text
    elif len(split_text) == 5:
        _, form_number, form_year, form_version, form_delimiter = text.split()
        form_date_version = f"{form_year}/{form_version}"
    else:
        raise ValueError("Unable to parse a valid ACORD version")
    form_year, form_version = _get_form_year_and_version(form_date_version)
    return AcordClassificationInfo(
        version_id=f"ACORD_{form_number}_{form_delimiter}_{form_year}_{form_version}".upper(),
        form_name=f"ACORD_{form_number}",
        form_year=int(form_year),
        form_version=form_version,
        form_delimiter=form_delimiter,
    )


def _acord_applied_form_extraction(text: str) -> AcordClassificationInfo:
    """
    Extracting information from format APPLIED 125ONI (2009/16)
    """
    split_text = text.split()
    if len(split_text) == 3:
        _, form_number, form_date_version = split_text
        form_year, form_version = _get_form_year_and_version(form_date_version)
    elif len(split_text) == 4:
        _, form_number, form_year, form_version = split_text
        form_year, form_version = _get_form_year_and_version(f"{form_year}/{form_version}")
    else:
        raise ValueError("Unable to parse a valid ACORD version")

    return AcordClassificationInfo(
        version_id=f"APPLIED_{form_number}_{form_year}_{form_version}".upper(),
        form_name=f"APPLIED_{form_number}",
        form_year=int(form_year),
        form_version=form_version,
    )


def _acord_applied_delimiter_form_extraction(text: str) -> AcordClassificationInfo:
    """
    Extracting information from format APPLIED 125ONI (2009/16)
    """
    split_text = text.split()
    if len(split_text) == 3:
        _, form_number_delimiter, form_date_version = text.split()
        form_year, form_version = _get_form_year_and_version(form_date_version)
    elif len(split_text) == 4:
        _, form_number_delimiter, form_year, form_version = text.split()
        form_year, form_version = _get_form_year_and_version(f"{form_year}/{form_version}")
    else:
        raise ValueError("Unable to parse a valid ACORD version")
    form_number, form_delimiter = "", ""
    for ind, c in enumerate(form_number_delimiter):
        if c.isnumeric():
            form_number += c
        else:
            form_delimiter = form_number_delimiter[ind:]
            break
    return AcordClassificationInfo(
        version_id=f"APPLIED_{form_number}_{form_delimiter}_{form_year}_{form_version}".upper(),
        form_name=f"APPLIED_{form_number}",
        form_year=int(form_year),
        form_version=form_version,
        form_delimiter=form_delimiter,
    )


def _acord_of_form_extraction(text: str) -> AcordClassificationInfo:
    """
    Extracting information from format OFXXXXXX
    """
    return AcordClassificationInfo(version_id=text.upper(), form_name=text.upper())


def _acord_of_date_form_extraction(text: str) -> AcordClassificationInfo:
    """
    Extracting information from format OFXXXXXX (2009/16)
    """
    split_text = text.split()
    if len(split_text) == 2:
        form_name, form_date_version = text.split()
        form_year, form_version = _get_form_year_and_version(form_date_version)
    elif len(split_text) == 3:
        form_name, form_year, form_version = text.split()
        form_year, form_version = _get_form_year_and_version(f"{form_year}/{form_version}")
    else:
        raise ValueError("Unable to parse a valid ACORD version")
    return AcordClassificationInfo(
        version_id=f"{form_name}_{form_year}_{form_version}".upper(),
        form_name=form_name.upper(),
        form_year=int(form_year),
        form_version=form_version,
    )


def is_acord_vehicle_schedule(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_129"


def is_acord_101(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_101"


def is_acord_125(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_125"


def is_acord_126(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_126"


def is_acord_127(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_127"


def is_acord_823(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_823"


def is_acord_829(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_829"


def is_acord_140(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_140"


def is_acord_131(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_131"


def is_acord_130(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_130"


def is_acord_139(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_139"


def is_acord_160(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_160"


def is_acord_163(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_163"


def is_applied_130(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "APPLIED_130"


def is_acord_211(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_211"


def is_acord_35(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_35"


def is_acord_175(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_175"


def is_acord_128(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "ACORD_128"


def is_applied_98(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "APPLIED_98"


def is_applied_126(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "APPLIED_126"


def is_applied_125(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "APPLIED_125"


def is_ofappinfcni(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "OFAPPINFCNI"


def is_ofschhaz(acord_version: AcordClassificationInfo) -> bool:
    return acord_version.form_name == "OFSCHHAZ"


def _get_page_text_candidates(text_items: list[TextItem], page_height: float, **kwargs) -> list[TextItem]:
    valid_texts = [
        text_item for text_item in text_items if text_item.y_min >= ACORD_FORM_VERTICAL_BBOX_THRESHOLD * page_height
    ]
    return valid_texts


def _get_page_number(text_result: TextExtractionResult) -> tuple[int, int] | None:
    page_texts = _get_texts_from_valid_area(text_result, get_text_items_fn=_get_page_text_candidates)
    for text in page_texts:
        match = re.search(r"page\s*(\d+)\s*of\s*(\d+)", text.lower())
        if match:
            return int(match.group(1)), int(match.group(2))
    return None


def _is_acord(classification: ClassificationDocumentType) -> bool:
    if re.search("ACORD_[1-9]", classification.value) or re.search("APPLIED_[1-9]", classification.value):
        return True
    return False


def backfill_acord_classification(classified_file: ClassifiedFile) -> ClassifiedFile:
    last_classification = -1
    last_val_idx = -1

    for i, classification in enumerate(classified_file.page_level_classifications):
        if classification != ClassificationDocumentType.UNKNOWN and classification != last_classification:
            last_classification = classification
            last_val_idx = i
        if classification == last_classification and i - 1 != last_val_idx:
            page_text_now = _get_page_number(classified_file.text_results[i])
            page_text_last = _get_page_number(classified_file.text_results[last_val_idx])

            if (
                page_text_now
                and page_text_last is not None
                and _is_acord(classification)
                and page_text_now[1] == page_text_last[1]
                and page_text_now[0] - (i - last_val_idx) == page_text_last[0]
            ):
                back_idx = i - 1
                while back_idx > last_val_idx:
                    classified_file.page_level_classifications[back_idx] = classification
                    classified_file.page_level_additional_info[back_idx] = classified_file.page_level_additional_info[
                        last_val_idx
                    ]
                    back_idx -= 1
    return classified_file
