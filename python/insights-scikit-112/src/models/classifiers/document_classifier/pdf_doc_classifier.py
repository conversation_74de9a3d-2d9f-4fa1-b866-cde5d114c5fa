import itertools
import operator
import os
import re
from abc import ABC, abstractmethod

import en_core_web_sm
import joblib
import numpy as np
import pandas as pd
from file_processing.constants import VIN_REGEX_STRICT
from file_processing.pdf_utils.text_extraction import TextExtractionR<PERSON>ult
from infrastructure_common.logging import get_logger
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_type import FileType
from static_common.utils.file_type_and_classification_mappings import (
    FILE_TYPE_TO_CLASSIFICATION,
)

from src.models.classifiers.document_classifier.classified_file import (
    ClassifiedFile,
    ProcessingMethod,
)
from src.models.classifiers.document_classifier.industry_detection import (
    detect_industry_specific_documents,
)
from src.models.classifiers.document_classifier.loss_run import is_monetary
from src.models.classifiers.document_classifier.utils import smooth_predictions

logger = get_logger()

FIRST_PAGE = "first_page"


class PDFDocumentClassifier(ABC):
    def __init__(self, base_model_dir: str):
        self.base_model_dir = base_model_dir

    @abstractmethod
    def predict(self, classified_file: ClassifiedFile) -> ClassifiedFile:
        pass

    @abstractmethod
    def dry_predict(self, classified_file: ClassifiedFile) -> None:
        pass


class PDFDocumentClassifierV2(PDFDocumentClassifier):
    """
    DO NOT MODIFY OR FIX BUGS WITHIN THIS CLASS WITHOUT RETRAINING THE CLASSIFIER
    """

    _GLOBAL_CONFIDENCE_THRESHOLD = 0.6
    _LOCAL_CONFIDENCE_THRESHOLD = 0.5

    def __init__(
        self,
        base_model_dir: str,
        model_version: str,
        model_filename: str = "pdf_model_all_data.pkl",
        preprocessor_filename: str = "preprocessor_all_data.pkl",
        class_mapping: dict[str, ClassificationDocumentType] | None = None,
        feature_list: list[str] | None = None,
    ):
        super().__init__(base_model_dir)
        self.model_version = model_version
        self.nlp = en_core_web_sm.load()
        self._ner_labels = list(self.nlp.get_pipe("ner").labels)
        try:
            self.pdf_model = joblib.load(os.path.join(base_model_dir, model_filename))
            self.preprocessor = joblib.load(os.path.join(base_model_dir, preprocessor_filename))
        except Exception as e:
            logger.exception("Failed to load pdf model", exc_info=e)
            self.pdf_model = None
            self.preprocessor = None

        if class_mapping is None:
            self.class_mapping = {
                "acord": ClassificationDocumentType.ACORD_FORM,
                "auto_financial": ClassificationDocumentType.AUTO_DEALER_FINANCIAL_STATEMENT,
                "bor": ClassificationDocumentType.UNKNOWN,  # temporarily due to noticed misclassification examples
                "budget": ClassificationDocumentType.UNKNOWN,  # temporarily due to noticed misclassification examples
                "corp_tree": ClassificationDocumentType.ORG_CHART,
                "cover_sheet": ClassificationDocumentType.COVER_SHEET,
                "directors_officers": ClassificationDocumentType.DIRECTORS_AND_OFFICERS,
                "drivers": ClassificationDocumentType.DRIVERS,
                "financial": ClassificationDocumentType.FINANCIAL_STATEMENT,
                "geotech": ClassificationDocumentType.UNKNOWN,
                # avoiding random pages as geotech - fallback to filename
                "ifta": ClassificationDocumentType.IFTA,
                "loss_run": ClassificationDocumentType.LOSS_RUN,
                "mvr": ClassificationDocumentType.MVR,
                "safety_manual": ClassificationDocumentType.SAFETY_MANUAL,
                "supplemental": ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
                "unknown": ClassificationDocumentType.UNKNOWN,
                "vehicles": ClassificationDocumentType.VEHICLES,
                "xmod": ClassificationDocumentType.WORK_COMP_EXPERIENCE,
                "xmod_summary": ClassificationDocumentType.EMOD_SUMMARY,
            }
        else:
            self.class_mapping = class_mapping
        if not feature_list:
            self.train_features = [
                "num_of_lines",
                "num_of_words",
                "num_of_question_marks",
                "num_of_dollars",
                "num_of_usd",
                "lowercase_density",
                "uppercase_density",
                "alpha_density",
                "numeric_density",
                "CARDINAL",
                "DATE",
                "EVENT",
                "FAC",
                "GPE",
                "LANGUAGE",
                "LAW",
                "LOC",
                "MONEY",
                "NORP",
                "ORDINAL",
                "ORG",
                "PERCENT",
                "PERSON",
                "PRODUCT",
                "QUANTITY",
                "TIME",
                "WORK_OF_ART",
                "like_id",
                "monetary_count",
                "vin_like",
                "page_text",
                "previous_page",
            ]
        else:
            self.train_features = feature_list

    @staticmethod
    def _is_like_id(value: str) -> bool:
        allowed_chars = ["-", "_"]
        return (
            len(value) >= 6
            and any(c.isdigit() for c in value)
            and all(c.isnumeric() or (c.isupper() and c.isalpha()) or c in allowed_chars for c in value)
        )

    @staticmethod
    def _count_monetary_text(text_result):
        return sum([1 for word in text_result.words if is_monetary(word.text)])

    def count_id_like_text(self, text_result):
        return sum([1 for word in text_result.words if self._is_like_id(word.text)])

    @staticmethod
    def _count_vin_like(text_result):
        return sum([1 for word in text_result.words if re.match(VIN_REGEX_STRICT, word.text)])

    def _create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        # bugged implementation - will be fixed in separate PR with a new model version
        df["num_of_lines"] = df.text_data.apply(lambda x: len("text_items"))
        df["num_of_words"] = df.text_data.apply(lambda x: len("text_items"))
        df["avg_line_length"] = df.text_data.apply(lambda x: np.mean([len(y.text) for y in x.text_items]))
        df["avg_word_length"] = df.text_data.apply(lambda x: np.mean([len(y.text) for y in x.words]))
        df["num_of_question_marks"] = df.text_data.apply(lambda x: x.joined_text.count("?"))
        df["num_of_dollars"] = df.text_data.apply(lambda x: x.joined_text.count("$"))
        df["num_of_usd"] = df.text_data.apply(lambda x: x.joined_text.count("USD"))
        df["lowercase_density"] = df.text_data.apply(
            lambda x: len([c for c in x.joined_text if c.islower()]) / len(x.joined_text) if len(x.joined_text) else 0
        )
        df["uppercase_density"] = df.text_data.apply(
            lambda x: len([c for c in x.joined_text if c.isupper()]) / len(x.joined_text) if len(x.joined_text) else 0
        )
        df["alpha_density"] = df.text_data.apply(
            lambda x: len([c for c in x.joined_text if c.isalpha()]) / len(x.joined_text) if len(x.joined_text) else 0
        )
        df["numeric_density"] = df.text_data.apply(
            lambda x: len([c for c in x.joined_text if c.isnumeric()]) / len(x.joined_text) if len(x.joined_text) else 0
        )
        df["like_id"] = df.text_data.apply(self.count_id_like_text)
        df["monetary_count"] = df.text_data.apply(self._count_monetary_text)
        df["vin_like"] = df.text_data.apply(self._count_vin_like)
        df["ner_entities"] = df.text_data.apply(self._get_entities)
        for ner_label in self._ner_labels:
            df[ner_label] = df.ner_entities.apply(
                lambda entities: sum(1 for ent in entities if ent.label_ == ner_label)
            )
        df.fillna(0, inplace=True)
        return df

    def _create_dataframe(self, classified_file: ClassifiedFile):
        df = pd.DataFrame(
            {
                "text_data": classified_file.text_results,
                "page_text": [text_res.joined_text for text_res in classified_file.text_results],
                "page_num": list(range(len(classified_file.text_results))),
            }
        )
        return self._create_features(df)

    def _get_entities(self, text_result: TextExtractionResult):
        try:
            return self.nlp(text_result.joined_text).ents
        except:
            return []

    def _apply_confidence_filter(
        self,
        classes: list[ClassificationDocumentType],
        confidences: list[float],
        file_id: str | None,
    ) -> list[ClassificationDocumentType]:
        groups = [
            [item[0] for item in group[1]]
            for group in itertools.groupby(enumerate(classes), key=operator.itemgetter(1))
        ]
        if len(groups) == 1:
            return classes
        group_averages = [np.mean([confidences[i] for i in group]) for group in groups]
        if all(
            [
                avg < self._GLOBAL_CONFIDENCE_THRESHOLD or classes[groups[idx][0]] == ClassificationDocumentType.UNKNOWN
                for idx, avg in enumerate(group_averages)
            ]
        ):
            return [ClassificationDocumentType.UNKNOWN for _ in classes]
        for idx, group in enumerate(groups):
            if (
                group_averages[idx] < self._GLOBAL_CONFIDENCE_THRESHOLD
                and classes[group[0]] != ClassificationDocumentType.UNKNOWN
            ):
                logger.warning(
                    "Low confidence classification",
                    file_id=file_id,
                    avg_confidence=group_averages[idx],
                    classification=classes[group[0]],
                    pages=group,
                )
                if group_averages[idx] < self._LOCAL_CONFIDENCE_THRESHOLD:
                    for page in group:
                        classes[page] = ClassificationDocumentType.UNKNOWN
        return classes

    def _predict(self, classified_file: ClassifiedFile) -> tuple[list[str], list[float]]:
        predictions = []
        if classified_file.text_results:
            df = self._create_dataframe(classified_file)
            previous_prediction = FIRST_PAGE
            for ind in df.index:
                current_df = df.loc[[ind]]
                current_df["previous_page"] = previous_prediction
                processed_data = self.preprocessor.transform(current_df)
                probas = self.pdf_model.predict_proba(processed_data)
                predicted_class = self.pdf_model.classes_[np.argmax(probas)]
                predictions.append((predicted_class, np.max(probas)))
                previous_prediction = predicted_class
        else:
            predictions = [("unknown", 0)]
        for ind, text_result in enumerate(classified_file.text_results):
            if (
                not text_result
                or not text_result.joined_text
                or " " not in text_result.joined_text
                or not any([c.isalpha() for c in text_result.joined_text])
            ):
                predictions[ind] = ("unknown", 0)  # other/unknown class for empty pages
        classes = [self.class_mapping[prediction[0]] for prediction in predictions]
        confidences = [prediction[1] for prediction in predictions]
        with_confidence_filter = self._apply_confidence_filter(
            classes, confidences, file_id=classified_file.file_id or classified_file.parent_file_id
        )
        logger.info(
            "Predicted classes",
            file_id=classified_file.file_id,
            file_name=classified_file.file_name,
            after_confidence_filter=with_confidence_filter,
            classes=classes,
            raw_classes=[prediction[0] for prediction in predictions],
            confidences=[prediction[1] for prediction in predictions],
            filename_based_classification=classified_file.filename_classification,
            model_version=self.model_version,
            organization_id=classified_file.organization_id,
            parent_file_id=classified_file.parent_file_id,
        )
        return with_confidence_filter, confidences

    @staticmethod
    def _predict_from_base_classification(
        classified_file: ClassifiedFile, base_classification: ClassificationDocumentType
    ) -> tuple[list[str], list[float]]:
        if not classified_file.text_results:
            classified_file.classification = base_classification
            return [], []

        classes = [base_classification] * len(classified_file.text_results)
        confidences = [1] * len(classified_file.text_results)
        return classes, confidences

    def predict(self, classified_file: ClassifiedFile) -> ClassifiedFile:
        base_classification = (
            FILE_TYPE_TO_CLASSIFICATION.get(FileType(classified_file.file_type), ClassificationDocumentType.UNKNOWN)
            if classified_file.processing_method == ProcessingMethod.FROM_FILE_TYPE
            else None
        )
        classes, confidences = (
            self._predict(classified_file)
            if classified_file.processing_method == ProcessingMethod.CLASSIFIER
            else self._predict_from_base_classification(classified_file, base_classification)
        )
        classified_file.page_level_classifications = classes
        classified_file.page_level_confidences = confidences
        smoothed_predictions = smooth_predictions(classified_file, confidence_threshold=0.5)
        smoothing_changes = [
            {
                "original": page_classification.value,
                "smoothed": smoothed_page.value,
                "page_num": page_num + 1,
                "confidence": confidences[page_num],
            }
            for page_num, (page_classification, smoothed_page) in enumerate(zip(classes, smoothed_predictions))
            if page_classification != smoothed_page
        ]
        if smoothing_changes:
            logger.info("Smoothing changes", changes=smoothing_changes, file_id=classified_file.file_id)
        harsh_smoothing = smooth_predictions(classified_file, confidence_threshold=0.85)
        if harsh_smoothing:
            logger.info(
                "Potential harsher smoothing changes",
                changes=[
                    {
                        "original": page_classification.value,
                        "smoothed": smoothed_page.value,
                        "page_num": page_num + 1,
                        "confidence": confidences[page_num],
                    }
                    for page_num, (page_classification, smoothed_page) in enumerate(zip(classes, harsh_smoothing))
                    if page_classification != smoothed_page
                ],
                file_id=classified_file.file_id,
            )
        classified_file.page_level_classifications = smoothed_predictions
        classified_file.classification_confidence = np.mean(confidences)
        classified_file = detect_industry_specific_documents(classified_file, base_classification=base_classification)

        return classified_file

    def dry_predict(self, classified_file: ClassifiedFile) -> None:
        self._predict(classified_file)
