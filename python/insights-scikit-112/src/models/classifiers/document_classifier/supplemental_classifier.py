from infrastructure_common.logging import get_logger
from llm_common.clients import get_llm_client
from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import GeminiRequestParams
from static_common.enums.supplemental_category import SupplementalCategory
from static_common.models.file_additional_info import SupplementalClassificationInfo
from static_common.models.openai import Chat<PERSON><PERSON>pletion<PERSON>rompt
from static_common.schemas.openai import ChatCompletionPromptSchema

from src.models.classifiers.document_classifier.classified_file import ClassifiedFile

logger = get_logger()

PAGES_TO_CONSIDER = 2  # Number of pages to consider for classification


class LLMSupplementalClassifier:
    _INSTRUCTION = """
    Categorize the supplemental application into one of the following categories:
    GENERAL_CONSTRUCTION - general construction-related, ex. general contractor, construction management etc
    SPECIALIZED_CONSTRUCTION - construction-related but specialized, ex. landscaping, demolition etc
    CONSTRUCTION_PROJECT - application concerning a specific construction project
    MANUFACTURING - manufacturing-related, ex. factory, product liability etc
    WORKERS_COMPENSATION - application for workers compensation insurance
    HOTEL_MOTEL - application for insurance of a hotel/motel
    RESTAURANT_BAR - application for insurance of a restaurant or bar
    LIQUOR_LIABILITY - application for liquor liability insurance
    CANNABIS - application for cannabis-related business
    REAL_ESTATE - related to management of a real estate - habitational, apartments etc
    RETAIL - retail-related, ex. grocery store, clothing store etc
    PROFESSIONAL_SERVICES - professional services, ex. legal, accounting, consulting etc
    HEALTHCARE - healthcare-related, ex. medical practice, dental practice etc
    CONSTRUCTION_MANAGEMENT - construction management-related, ex. project management, construction consulting etc
    TRANSPORTATION - transportation-related, ex. logistics, freight forwarding etc
    AGRICULTURE - agriculture-related, ex. farming, ranching etc
    RENEWAL_APPLICATION - renewal of an existing insurance policy
    NEW_BUSINESS_APPLICATION - new business application for insurance
    HIRED_NON_OWNED_AUTO - application for hired and non-owned auto insurance
    OTHER - any other category not listed above

    The response should follow the JSON format:
    {
        "category": "GENERAL_CONSTRUCTION"  # one of the categories listed above
        "description": "This application is related to general construction activities,
        such as building, renovation, and maintenance of structures." # max 150 characters description of the category
    }
    """

    def _create_prompt(self, file_text: str) -> ChatCompletionPrompt:
        prompt_sequence = {
            "messages": [
                {"role": "system", "content": self._INSTRUCTION},
                {
                    "role": "user",
                    "content": f"""
                    {file_text}
                    """,
                },
            ]
        }
        return ChatCompletionPromptSchema().load(prompt_sequence)

    def classify_supplemental(self, classified_file: ClassifiedFile) -> ClassifiedFile:
        try:
            return self._classify_supplemental(classified_file)
        except Exception:
            logger.error("Error during supplemental classification.", exc_info=True)
            return classified_file

    def _classify_supplemental(self, classified_file: ClassifiedFile) -> ClassifiedFile:
        first_two_pages_text = " ".join(
            [text_result.joined_text for text_result in classified_file.text_results[:PAGES_TO_CONSIDER]]
        )
        supplemental_classification = self.get_llm_classification(first_two_pages_text)
        if not supplemental_classification:
            return classified_file
        try:
            category = SupplementalCategory(supplemental_classification["category"])
        except ValueError:
            logger.warning(
                "LLM did not return a valid category for supplemental classification.",
                file_id=classified_file.file_id,
                category=supplemental_classification.get("category"),
                description=supplemental_classification.get("description"),
            )
            return classified_file
        if classified_file.additional_info.supplemental is None:
            classified_file.additional_info.supplemental = SupplementalClassificationInfo()
        if supplemental_classification:
            classified_file.additional_info.supplemental.category = category
            classified_file.additional_info.supplemental.category_description = supplemental_classification[
                "description"
            ]
        return classified_file

    def get_llm_classification(self, file_text: str) -> dict | None:
        prompt = self._create_prompt(file_text)
        llm_params = [
            GeminiRequestParams(
                model=LLMModel.GEMINI_2_0_FLASH,
                max_tokens=10000,
                return_json=True,
                raise_exceptions=False,
                use_cache=False,
            ),
        ]
        response = get_llm_client().get_llm_response(
            llm_params, prompt, call_origin="insights-scikit-112::supplemental_categories"
        )
        return response
