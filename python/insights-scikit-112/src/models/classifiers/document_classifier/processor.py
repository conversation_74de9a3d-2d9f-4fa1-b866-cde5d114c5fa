import os
import re
from collections import Counter, defaultdict
from dataclasses import replace
from io import BytesIO

import requests
from common.clients.azure import AzureCustomModelClient
from common.clients.utils import retry_all
from file_processing.exceptions import AllSheetsEmptyException
from file_processing.models.workbook_metadata import Spreadsheet<PERSON><PERSON><PERSON><PERSON><PERSON>ult
from file_processing.spreadsheet_parser import Spreadsheet<PERSON>arser
from infrastructure_common.logging import get_logger
from static_common.enums.ally_auto import AllyAutoSheetNames
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from static_common.models.file_additional_info import FileAdditionalInfo
from static_common.utils.file_type_and_classification_mappings import (
    CLASSIFICATION_TO_FILE_TYPE,
    FILE_TYPE_TO_CLASSIFICATION,
)

from src.models.classifiers.document_classifier.classified_file import (
    ClassifiedFile,
    DocumentType,
    ProcessingMethod,
)
from src.models.classifiers.document_classifier.constants import MAX_SPLIT_TABLES
from src.models.classifiers.document_classifier.doc_to_pdf import CloudmersiveConverter
from src.models.classifiers.document_classifier.email_detection import is_email
from src.models.classifiers.document_classifier.industry_detection import (
    extract_vehicles_and_drivers_tables_as_spreadsheets,
)
from src.models.classifiers.document_classifier.multithreading import multi_thread
from src.models.classifiers.document_classifier.spreadsheet_parser import (
    xlsx_with_selected_sheets,
)
from src.models.classifiers.document_classifier.submission_document_classifier import (
    FilenameDocumentClassifier,
    SubmissionDocumentClassifier,
    incorporate_filename_classification,
)
from src.models.classifiers.document_classifier.supplemental_classifier import (
    LLMSupplementalClassifier,
)
from src.models.classifiers.document_classifier.text_extraction import (
    extract_text_from_document,
    open_and_clean_pdf_using_bytes,
)
from src.models.classifiers.document_classifier.utils import (
    OMAHA_ORG_ID,
    enhance_prediction_with_extension,
    load_file_bytes,
    split_pdf,
)
from src.utils import (
    convert_image_bytes_to_pdf_bytes,
    set_initial_classification_fields,
)

logger = get_logger()


@retry_all
def download_file(index: int, classified_file: ClassifiedFile, output: list):
    req = requests.get(classified_file.presigned_url)
    req.raise_for_status()
    output[index] = req.content


def extract_pdf_document_and_text_task(
    index: int, classified_file: ClassifiedFile, output: list, azure_client: AzureCustomModelClient
):
    try:
        pdf_document, file_locked = open_and_clean_pdf_using_bytes(classified_file.file_bytes)
        text_results = extract_text_from_document(
            pdf_document, classified_file.file_bytes, classified_file.file_id, azure_client, is_file_locked=file_locked
        )
        classified_file.is_locked = file_locked
    except Exception as e:
        logger.warning("Failed to extract text from PDF file", exc_info=e)
        if "cannot save with zero pages" in str(e):
            logger.warning("Invalid PDF: zero pages loaded", exc_info=e)
        else:
            logger.error("Failed to extract text from PDF file")
        pdf_document = None
        text_results = []
    output[index] = (pdf_document, text_results)


class DocumentSubmissionProcessor:
    def __init__(
        self,
        classifier: SubmissionDocumentClassifier,
        azure_client: AzureCustomModelClient,
        cloudmersive_converter: CloudmersiveConverter,
        n_jobs: int = 8,
        local_run: bool = False,
    ):
        self.classifier = classifier
        self.spreadsheet_parser = SpreadsheetParser(return_raw_dfs=True)
        self.azure_client = azure_client
        self.n_jobs = n_jobs
        self.cloudmersive_converter = cloudmersive_converter
        self.local_run = local_run
        self._logger = logger

    def load_files_content(self, classified_files: list[ClassifiedFile]) -> None:
        applicable_files_to_load_bytes = [
            cf
            for cf in classified_files
            if cf.file_bytes is None and DocumentType.requires_content_for_classification(cf.document_type)
        ]
        file_responses = (
            load_file_bytes(classified_files)
            if self.local_run
            else multi_thread(applicable_files_to_load_bytes, download_file)
        )
        for classified_file, file_response in zip(applicable_files_to_load_bytes, file_responses):
            classified_file.file_bytes = file_response

        pdf_files = [
            cf
            for cf in classified_files
            if (cf.text_results is None or cf.pdf_document is None) and cf.document_type == DocumentType.PDF
        ]

        pdf_extract_results = multi_thread(
            pdf_files,  # force using bytes
            extract_pdf_document_and_text_task,
            self.azure_client,
            chunk_size=5,
        )

        for classified_file, pdf_extract_result in zip(pdf_files, pdf_extract_results):
            classified_file.pdf_document = pdf_extract_result[0]
            classified_file.text_results = pdf_extract_result[1]
            classified_file.page_level_additional_info = [None] * len(pdf_extract_result[1])

    @staticmethod
    def _determine_processing_method(
        classified_file: ClassifiedFile,
    ) -> None:
        classification = classified_file.classification
        file_type = classified_file.file_type
        if file_type is None:
            logger.warning(
                "Received file with empty FileType",
                classification=classification,
                file_type=file_type,
                origin=classified_file.origin,
                processing_state=classified_file.processing_state,
                submission_id=classified_file.submission_id,
                file_id=classified_file.file_id,
            )
        file_type_from_classification = CLASSIFICATION_TO_FILE_TYPE.get(classification, FileType.UNKNOWN)
        is_file_type_specified = file_type is not None and file_type != FileType.UNKNOWN
        is_loss_run_to_reclassify = file_type == FileType.LOSS_RUN and (
            classification == ClassificationDocumentType.LOSS_RUN_PDF
            or (
                classification == ClassificationDocumentType.LOSS_RUN_NO_CLAIM_PDF
                and classified_file.sensible_status in ["INVALID", "NO_LOSSES", "NO_DOCUMENTS_FOUND"]
            )
        )
        is_classified_consolidated_financial_statement_with_matched_file_type = (
            classification == ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF
            and file_type == FileType.FINANCIAL_STATEMENT
        )

        classified_file.processing_method = (
            ProcessingMethod.FROM_FILE_TYPE
            if (
                (not classification and is_file_type_specified)
                or (classification and file_type_from_classification != file_type)
                or is_loss_run_to_reclassify
            )
            else (
                ProcessingMethod.ALREADY_CLASSIFIED
                if classification
                and (
                    file_type_from_classification == file_type
                    or is_classified_consolidated_financial_statement_with_matched_file_type
                )
                else ProcessingMethod.CLASSIFIER
            )
        )

    @staticmethod
    def _set_file_types(classified_files: list[ClassifiedFile]) -> list[ClassifiedFile]:
        for classified_file in classified_files:
            # do not change file type for files of unsupported types
            if (
                classified_file.classification == ClassificationDocumentType.UNKNOWN
                and classified_file.file_type is not None
                and classified_file.file_type != FileType.UNKNOWN
            ):
                continue
            if classified_file.classification and not (
                classified_file.file_type == FileType.SOV
                and classified_file.is_internal
                and classified_file.classification == ClassificationDocumentType.SOV_SPREADSHEET
            ):
                classified_file.file_type = CLASSIFICATION_TO_FILE_TYPE[classified_file.classification]
        return classified_files

    @staticmethod
    def _get_documents_to_classify(
        classified_files: list[ClassifiedFile],
    ) -> list[ClassifiedFile]:
        def _is_document(classified_file: ClassifiedFile):
            classifications_for_document_from_image = [
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
                ClassificationDocumentType.ACORD_FORM,
                ClassificationDocumentType.VEHICLES,
                ClassificationDocumentType.LOSS_RUN,
                ClassificationDocumentType.OTHER,
                ClassificationDocumentType.IFTA,
                ClassificationDocumentType.UNKNOWN,
            ]
            return classified_file.document_type in [DocumentType.PDF, DocumentType.DOC] or (
                classified_file.document_type == DocumentType.IMAGE
                and classified_file.filename_classification in classifications_for_document_from_image
            )

        return [cf for cf in classified_files if _is_document(cf)]

    def process_files(self, classified_files: list[ClassifiedFile]) -> list[ClassifiedFile]:
        self._logger = self._logger.bind(
            file_id=classified_files[0].file_id,
            submission_id=classified_files[0].submission_id,
            s3_key=classified_files[0].s3_key,
        )
        for classified_file in classified_files:
            self._determine_processing_method(classified_file)
        files_to_classify = [
            cf for cf in classified_files if cf.processing_method != ProcessingMethod.ALREADY_CLASSIFIED
        ]
        self.load_files_content(files_to_classify)
        for classified_file in files_to_classify:
            prediction = FilenameDocumentClassifier.predict(classified_file.file_name, classified_file.organization_id)
            classified_file.filename_classification = prediction

        documents = self._get_documents_to_classify(files_to_classify)
        classified_documents, spreadsheets_from_documents = self._handle_documents(documents)
        classifications = [
            cf
            for cf in files_to_classify
            if cf.document_type
            in [DocumentType.SPREADSHEET, DocumentType.OTHER, DocumentType.HTML_DOC, DocumentType.ARCHIVE]
            and cf.processing_method != ProcessingMethod.FROM_FILE_TYPE
        ]
        classifications.extend(classified_documents + spreadsheets_from_documents)

        sheets = [
            replace(cf)
            for cf in files_to_classify
            if cf.document_type == DocumentType.SPREADSHEET and cf.processing_method == ProcessingMethod.CLASSIFIER
        ]
        spreadsheet_classifications = self._handle_spreadsheets(sheets)

        for classified_file in classifications + spreadsheet_classifications:
            if not classified_file.initial_classification:
                set_initial_classification_fields(classified_file)
        classifications = self._resolve_spreadsheet_predictions(spreadsheet_classifications, classifications)
        classifications = self._add_missing_classifications(classifications, files_to_classify)

        classifications = self._set_file_types(classifications)
        classifications = self.classify_supplemental_types(classifications)

        classifications = enhance_prediction_with_extension(classifications)
        return classifications

    @staticmethod
    def classify_supplemental_types(classified_files: list[ClassifiedFile]):
        classified_files = [
            (
                LLMSupplementalClassifier().classify_supplemental(cf)
                if cf.file_type == FileType.SUPPLEMENTAL_FORM and cf.document_type.PDF
                else cf
            )
            for cf in classified_files
        ]
        return classified_files

    @staticmethod
    def _add_missing_classifications(
        classified_files: list[ClassifiedFile], files_to_classify: list[ClassifiedFile]
    ) -> list[ClassifiedFile]:
        classified_files = [
            cf
            for cf in classified_files
            if not (cf.document_type == DocumentType.DOC and cf.processing_method == ProcessingMethod.FROM_FILE_TYPE)
        ]

        for file_to_classify in files_to_classify:
            matching_id = [cf for cf in classified_files if cf.file_id == file_to_classify.file_id]
            if not matching_id:
                file_to_classify.classification = (
                    FILE_TYPE_TO_CLASSIFICATION.get(
                        FileType(file_to_classify.file_type), ClassificationDocumentType.UNKNOWN
                    )
                    if file_to_classify.processing_method == ProcessingMethod.FROM_FILE_TYPE
                    else (
                        file_to_classify.filename_classification
                        if file_to_classify.filename_classification
                        else ClassificationDocumentType.UNKNOWN
                    )
                )
                classified_files.append(file_to_classify)
        return classified_files

    @staticmethod
    def _resolve_spreadsheet_predictions(
        sheets: list[ClassifiedFile], classifications: list[ClassifiedFile]
    ) -> list[ClassifiedFile]:
        """
        This function resolves duplicate predictions between filename-based predictions and those
        coming from the classifier. It may occur that a file can be classified based on its filename
        but contains additional information that can fall under different categories. The following function
        handles cases:
            - The file had a single sheet/was csv and that classification matches filename-based classification
                file_id is removed from filename-based classification
            - The file had a single sheet/was csv and that classification doesn't match filename-based classification
                file_id is removed from spreadsheet classifications
            - The file had multiple sheets and at least one of them matches filename-based classification
                file_id is removed from filename-based classification
            - The file had multiple sheets and none match the filename-based classification
                file_id is removed from spreadsheet classifications and NOT new files list
                as in new files there were other types uncovered
        The function removed the 2 dicts with classifications after reducing them (new files are never modified)
        """
        to_remove_from_classifications, to_remove_from_sheets = set(), set()
        internal_sov = []
        for sheet_file in sheets:
            if not sheet_file.file_id:
                continue
            filename_classifications = [
                cl.filename_classification for cl in classifications if cl.file_id == sheet_file.file_id
            ]
            if not filename_classifications:
                continue
            if filename_classifications[0] == ClassificationDocumentType.UNKNOWN:
                to_remove_from_classifications.add(sheet_file.file_id)
            elif sheet_file.classification == filename_classifications[0]:
                to_remove_from_classifications.add(sheet_file.file_id)
            else:
                derived = [cl for cl in sheets if cl.parent_file_id == sheet_file.file_id]
                if any([d.classification == filename_classifications[0] for d in derived]):
                    to_remove_from_classifications.add(sheet_file.file_id)
                else:
                    if sheet_file.classification == ClassificationDocumentType.MERGED:
                        classified_sheets = [d.spreadsheet_parsing_results[0].sheet_name for d in derived]
                        internal_sov.append((sheet_file, classified_sheets))
                        to_remove_from_classifications.add(sheet_file.file_id)
                    else:
                        to_remove_from_sheets.add(sheet_file.file_id)

        classifications = [cl for cl in classifications if cl.file_id not in to_remove_from_classifications]
        sheets = [cl for cl in sheets if cl.file_id not in to_remove_from_sheets]
        internal_sovs = [
            DocumentSubmissionProcessor._create_file_with_remaining_sheets(cl_file, sheets)
            for cl_file, sheets in internal_sov
        ]
        classifications.extend(sheets)
        classifications.extend(sov for sov in internal_sovs if sov is not None)
        for classification in classifications:
            if (
                classification.classification is None
                or classification.classification == ClassificationDocumentType.UNKNOWN
            ) and classification.filename_classification:
                classification.classification = classification.filename_classification
                classification.initial_classification = classification.filename_classification
                classification.initial_classification_confidence = 0.5
        return classifications

    @staticmethod
    def _create_file_with_remaining_sheets(
        classified_file: ClassifiedFile, excluded_sheets: list[str]
    ) -> ClassifiedFile | None:
        if not classified_file.spreadsheet_parsing_results:
            raise ValueError("ClassifiedFile has not been parsed")
        all_sheet_names = {sr.sheet_name for sr in classified_file.spreadsheet_parsing_results}
        included_sheets = all_sheet_names - set(excluded_sheets)
        if not included_sheets:
            logger.warning("No sheets left in file to add as Internal SOV")
            return None
        extension = classified_file.file_name.split(".")[-1]
        xlsx_filename = classified_file.file_name.replace(
            f".{extension}", f"_{classified_file.filename_classification.value}.xlsx"
        )
        new_classified_file = replace(
            classified_file,
            classification=classified_file.filename_classification,
            s3_key=None,
            file_bytes=xlsx_with_selected_sheets(classified_file.spreadsheet_parsing_results, included_sheets).read(),
            is_internal=classified_file.filename_classification == ClassificationDocumentType.SOV,
            parent_file_id=classified_file.file_id,
            initial_classification=classified_file.filename_classification,
            initial_classification_confidence=0.5,  # decreasing confidence as there's no evidence apart from file name
            file_id=None,
            file_name=xlsx_filename,
            internal_notes=[f"File created from {classified_file.file_id} by removing classified sheets"],
            spreadsheet_parsing_results=[
                sheet for sheet in classified_file.spreadsheet_parsing_results if sheet.sheet_name in included_sheets
            ],
        )
        return new_classified_file

    def _convert_documents(
        self, classified_files: list[ClassifiedFile]
    ) -> tuple[list[ClassifiedFile], list[ClassifiedFile]]:
        to_classify, classified = [], []
        for classified_file in classified_files:
            if classified_file.document_type == DocumentType.PDF:
                to_classify.append(classified_file)
                continue
            pdf_bytes = None
            if classified_file.document_type == DocumentType.DOC:
                pdf_bytes = self.cloudmersive_converter.convert_doc_to_pdf(classified_file.file_bytes)
            elif classified_file.document_type == DocumentType.IMAGE:
                pdf_bytes = convert_image_bytes_to_pdf_bytes(classified_file.file_bytes)

            if not pdf_bytes:  # in case of error in conversion
                classified_file.classification = ClassificationDocumentType.UNKNOWN
                classified.append(classified_file)
                continue

            extension = classified_file.file_name.split(".")[-1]
            pdf_filename = classified_file.file_name.replace(f".{extension}", ".pdf")
            pdf_document, _ = open_and_clean_pdf_using_bytes(pdf_bytes)
            text_results = extract_text_from_document(
                pdf_document, pdf_bytes, None, self.azure_client, classified_file.s3_key
            )
            new_classified_file = replace(
                classified_file,
                file_name=pdf_filename,
                file_bytes=pdf_bytes,
                file_id=None,
                document_type=DocumentType.PDF,
                s3_key=None,
                parent_file_id=classified_file.file_id,
                presigned_url=None,
                internal_notes=["File generated as a result of conversion to PDF."],
                is_split_disabled=True,
                is_internal=True,
                pdf_document=pdf_document,
                text_results=text_results,
            )
            to_classify.append(new_classified_file)
            classified_file.classification = ClassificationDocumentType.UNKNOWN
            classified.append(classified_file)
        return to_classify, classified

    @staticmethod
    def _should_split(classified_file: ClassifiedFile) -> bool:
        if classified_file.is_split_disabled:
            return False
        multiple_classifications = len(Counter(classified_file.page_level_classifications)) > 1
        multiple_acords = (
            len(
                {
                    add_info.acord.version_id
                    for add_info in classified_file.page_level_additional_info
                    if add_info and add_info.acord
                }
            )
            > 1
        )
        return multiple_classifications or multiple_acords or len(classified_file.split_points)

    def _handle_documents(
        self, document_files: list[ClassifiedFile]
    ) -> tuple[list[ClassifiedFile], list[ClassifiedFile]]:
        """
        Function handling documents (PDFs, doc/docx, and image files) from submission.
        It downloads file content, converts files to PDFs if needed, triggers
        text extraction and inference of document type in a page-by-page manner.
        For documents in which there are multiple pages detected, the file is split into
        multiple new files.
        """
        spreadsheet_classified_files = []
        files_for_classification, classified_files = self._convert_documents(document_files)
        self._handle_omaha_emails(files_for_classification, classified_files)
        for classified_file in files_for_classification:
            self.classifier.pdf_predict(classified_file)
            if classified_file.processing_method == ProcessingMethod.CLASSIFIER:
                incorporate_filename_classification(classified_file)
            if self._should_try_extract_vehicle_and_driver_tables(classified_file):
                spreadsheet_classified_files.extend(
                    extract_vehicles_and_drivers_tables_as_spreadsheets(classified_file)
                )
            classified_files.append(classified_file)
            if self._should_split(classified_file):
                classified_files.extend(split_pdf(classified_file))
                # Base file from which smaller files are extracted is marked as type Merged
                classified_file.classification = ClassificationDocumentType.MERGED
                classified_file.file_type = CLASSIFICATION_TO_FILE_TYPE[ClassificationDocumentType.MERGED]
            else:
                self._assign_classification(classified_file)
                if classified_file.page_level_additional_info:
                    classified_file.additional_info = classified_file.page_level_additional_info[0]
            classified_file.file_type = CLASSIFICATION_TO_FILE_TYPE[classified_file.classification]
            if classified_file.pdf_document:
                if not classified_file.additional_info:
                    classified_file.additional_info = FileAdditionalInfo()
                classified_file.additional_info.number_of_pages = classified_file.pdf_document.page_count

        return classified_files, spreadsheet_classified_files

    @staticmethod
    def _handle_omaha_emails(files_for_classification: list[ClassifiedFile], classified_files: list[ClassifiedFile]):
        for classified_file in files_for_classification[:]:
            if (
                classified_file.processing_method == ProcessingMethod.CLASSIFIER
                and classified_file.organization_id == OMAHA_ORG_ID
                and is_email(classified_file)
            ):
                classified_file.classification = ClassificationDocumentType.EMAIL
                files_for_classification.remove(classified_file)
                classified_files.append(classified_file)

    def _assign_classification(self, classified_file: ClassifiedFile) -> None:
        is_supplemental_file_type_classification = (
            classified_file.file_type == FileType.SUPPLEMENTAL_FORM
            and classified_file.processing_method == ProcessingMethod.FROM_FILE_TYPE
        )
        if classified_file.page_level_classifications:
            if is_supplemental_file_type_classification:
                classified_file.classification = self._get_supplemental_classification(classified_file)
            else:
                classified_file.classification = self._get_most_common_classification(classified_file)
        elif classified_file.processing_method == ProcessingMethod.CLASSIFIER:
            classified_file.classification = ClassificationDocumentType.UNKNOWN

    @staticmethod
    def _get_most_common_classification(classified_file: ClassifiedFile) -> ClassificationDocumentType:
        return Counter(classified_file.page_level_classifications).most_common(1)[0][0]

    @staticmethod
    def _get_supplemental_classification(classified_file: ClassifiedFile) -> ClassificationDocumentType:
        priority_supplemental_classifications = [
            ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION,
            ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION,
            ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION,
            ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION,
            ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION,
        ]
        for supplemental_classification in priority_supplemental_classifications:
            if supplemental_classification in classified_file.page_level_classifications:
                return supplemental_classification
        return ClassificationDocumentType.SUPPLEMENTAL_APPLICATION

    @staticmethod
    def _should_try_extract_vehicle_and_driver_tables(classified_file: ClassifiedFile) -> bool:
        if classified_file.processing_method == ProcessingMethod.FROM_FILE_TYPE:
            file_type_classification = FILE_TYPE_TO_CLASSIFICATION.get(
                FileType(classified_file.file_type), ClassificationDocumentType.UNKNOWN
            )
            return file_type_classification in [
                ClassificationDocumentType.VEHICLES,
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
            ] or (
                file_type_classification == ClassificationDocumentType.DRIVERS and classified_file.organization_id == 9
            )
        return classified_file.processing_method == ProcessingMethod.CLASSIFIER

    def _handle_spreadsheets(self, classified_files: list[ClassifiedFile]) -> list[ClassifiedFile]:
        file_responses = (
            load_file_bytes(classified_files) if self.local_run else multi_thread(classified_files, download_file)
        )
        for classified_file, file_response in zip(classified_files, file_responses):
            classified_file.file_bytes = file_response

        predictions: list[list[SpreadsheetParsingResult]] = [
            self._handle_spreadsheet(classified_file) for classified_file in classified_files
        ]
        new_files = []
        for ind, prediction_result in enumerate(predictions):
            classified_files[ind].additional_info = classified_files[ind].additional_info or FileAdditionalInfo()
            classified_files[ind].additional_info.number_of_tables = len(prediction_result)
            classified_files[ind].additional_info.number_of_sheets = len({p.sheet_idx for p in prediction_result})

            if len(prediction_result) <= 1:
                classified_files[ind].classification = prediction_result[0].prediction
                classified_files[ind].classification_confidence = prediction_result[0].confidence
                classified_files[ind].spreadsheet_parsing_results = prediction_result
                continue
            parent_filename, parent_extension = os.path.splitext(classified_files[ind].file_name)
            conf_sum = 0
            conf_count = 0
            any_child_added = False
            ally_auto_supplemental_predictions = []
            for prediction in prediction_result:
                prediction = self._verify_organization_specific_predictions(  # noqa: PLW2901
                    classified_files[ind].organization_id, prediction
                )

                if (
                    prediction.prediction == ClassificationDocumentType.UNKNOWN
                    and classified_files[ind].organization_id != ExistingOrganizations.MarkelDemo.value
                ):
                    continue
                # ally auto supplemental consists of multiple sheets, so we need to merge them
                if prediction.prediction == ClassificationDocumentType.ALLY_AUTO_SUPPLEMENTAL:
                    ally_auto_supplemental_predictions.append(prediction)
                    continue

                workbook_data_result = prediction.workbook_as_bytes(self._logger)

                if workbook_data_result is None:
                    self._logger.warning(
                        "Failed to create child file from single sheet. No workbook data found.",
                        parent_file_id=classified_files[ind].file_id,
                        prediction=prediction.prediction,
                    )
                    continue

                file_extension, file_bytes = workbook_data_result

                spreadsheet_filename = f"{parent_filename}_{prediction.sheet_name}_{prediction.prediction}{parent_extension}.{file_extension.value}"  # noqa: E501
                conf_sum += prediction.confidence
                conf_count += 1
                new_file_additional_info = replace(
                    classified_files[ind].additional_info or FileAdditionalInfo(),
                    number_of_tables=1,
                    number_of_sheets=1,
                )
                new_files.append(
                    replace(
                        classified_files[ind],
                        file_id=None,
                        s3_key=None,
                        file_name=spreadsheet_filename,
                        file_bytes=file_bytes,
                        classification=prediction.prediction,
                        classification_confidence=prediction.confidence,
                        parent_file_id=classified_files[ind].file_id,
                        spreadsheet_parsing_results=[prediction],
                        additional_info=new_file_additional_info,
                        internal_notes=[
                            f"File generated by splitting file {classified_files[ind].file_id} in the "
                            "classification process."
                        ],
                        is_hidden=prediction.is_hidden,
                    )
                )
                any_child_added = True

            classified_files[ind].classification = (
                ClassificationDocumentType.MERGED if any_child_added else ClassificationDocumentType.UNKNOWN
            )
            classified_files[ind].spreadsheet_parsing_results = prediction_result
            if conf_count:
                classified_files[ind].classification_confidence = conf_sum / conf_count

            if ally_auto_supplemental_predictions:
                if merged_file := self._create_merged_ally_auto_supplemental(
                    classified_files[ind], ally_auto_supplemental_predictions
                ):
                    new_files.append(merged_file)
                    conf_sum += sum(p.confidence for p in ally_auto_supplemental_predictions)
                conf_count += len(ally_auto_supplemental_predictions)

        classified_files.extend(new_files)
        return classified_files

    @staticmethod
    def _verify_organization_specific_predictions(
        org_id, prediction: SpreadsheetParsingResult
    ) -> SpreadsheetParsingResult:
        if org_id == ExistingOrganizations.KalepaTest.value:
            return prediction

        if prediction.prediction in [
            ClassificationDocumentType.ALLY_AUTO_SUPPLEMENTAL,
            ClassificationDocumentType.ALLY_AUTO_PROPERTY_SOV,
        ]:
            if org_id == ExistingOrganizations.Paragon.value:
                return prediction
            else:
                prediction.prediction = ClassificationDocumentType.UNKNOWN
                return prediction

        return prediction

    @staticmethod
    def _create_merged_ally_auto_supplemental(
        parent_file: ClassifiedFile, predictions: list[SpreadsheetParsingResult]
    ) -> ClassifiedFile | None:
        general_info_found = False
        renewal_questionnaire_found = False
        questionnaire_found = False

        sheet_names_to_merge = []

        for parsing_result in parent_file.spreadsheet_parsing_results:
            if AllyAutoSheetNames.GENERAL_INFO.is_name_matching(parsing_result.sheet_name):
                general_info_found = True
                sheet_names_to_merge.append(parsing_result.sheet_name)
            if AllyAutoSheetNames.RENEWAL_QUESTIONNAIRE.is_name_matching(parsing_result.sheet_name):
                renewal_questionnaire_found = True
                sheet_names_to_merge.append(parsing_result.sheet_name)
            if AllyAutoSheetNames.DEALER_UW_QUESTIONNAIRE.is_name_matching(parsing_result.sheet_name):
                questionnaire_found = True
                sheet_names_to_merge.append(parsing_result.sheet_name)

        if not (general_info_found or renewal_questionnaire_found) or not questionnaire_found:
            logger.error("Incomplete data for Ally Auto Supplemental")
            return None

        return replace(
            parent_file,
            file_id=None,
            s3_key=None,
            file_name=f"{parent_file.file_name}_ally_auto_questionnaire_.xlsx",
            file_bytes=xlsx_with_selected_sheets(
                parent_file.spreadsheet_parsing_results,
                sheet_names_to_merge,
            ).read(),
            classification=ClassificationDocumentType.ALLY_AUTO_SUPPLEMENTAL,
            classification_confidence=min([p.confidence for p in predictions]),
            parent_file_id=parent_file.file_id,
            spreadsheet_parsing_results=predictions,
            internal_notes=[f"File generated by splitting file {parent_file.file_id} in the classification process."],
        )

    def _handle_spreadsheet(
        self, classified_file: ClassifiedFile, consolidate_sheets_for: list[ClassificationDocumentType] | None = None
    ) -> list[SpreadsheetParsingResult]:
        if consolidate_sheets_for is None:
            consolidate_sheets_for = [
                ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT,
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
            ]
        try:
            spreadsheet_parsing_results = self.spreadsheet_parser.parse_spreadsheet(
                filename=classified_file.file_name,
                file=BytesIO(classified_file.file_bytes),
                split_tables=True,
                apply_postprocessing=True,
            )
            if len(spreadsheet_parsing_results) > MAX_SPLIT_TABLES:
                logger.warning("Extracted too many sheets from the file", file_id=classified_file.file_id)
                spreadsheet_parsing_results = self.spreadsheet_parser.parse_spreadsheet(
                    filename=classified_file.file_name,
                    file=BytesIO(classified_file.file_bytes),
                    split_tables=False,
                    apply_postprocessing=True,
                )
            # file-processing returns [] when it cannot open a file and exception is fired
            if not spreadsheet_parsing_results:
                logger.warning("Couldn't parse data from spreadsheet.", file_id=classified_file.file_id)
                return [
                    SpreadsheetParsingResult(
                        sheet_idx=0,
                        sheet_name=None,
                        prediction=ClassificationDocumentType.UNKNOWN,
                        column_names=None,
                        df=None,
                        confidence=0,
                    )
                ]

        except AllSheetsEmptyException:
            self._logger.info("All sheets in the file are empty")
            return [
                SpreadsheetParsingResult(
                    sheet_idx=0,
                    sheet_name=None,
                    prediction=ClassificationDocumentType.EMPTY,
                    column_names=None,
                    df=None,
                    confidence=0,
                )
            ]
        except Exception as e:
            exception_message = str(e)
            if "Unsupported format, or corrupt file: Expected BOF record" in exception_message:
                # Those files are corrupted and can't be opened
                self._logger.warning("Couldn't parse file", exc_info=e)
            else:
                self._logger.exception("Couldn't parse file")
            return [
                SpreadsheetParsingResult(
                    sheet_idx=0,
                    sheet_name=None,
                    prediction=ClassificationDocumentType.UNKNOWN,
                    column_names=None,
                    df=None,
                    confidence=0,
                )
            ]
        column_names = [parsing_result.column_names for parsing_result in spreadsheet_parsing_results]
        sheet_names = [parsing_result.sheet_name for parsing_result in spreadsheet_parsing_results]
        dfs = [parsing_result.df for parsing_result in spreadsheet_parsing_results]
        predictions, confidences = self.classifier.spreadsheet_predict(
            classified_file=classified_file,
            column_names=column_names,
            sheet_names=sheet_names,
            dfs=dfs,
            parsing_results=spreadsheet_parsing_results,
        )

        has_csf = any(pred in consolidate_sheets_for for pred in predictions)
        if has_csf and len(predictions) > 1:
            return self._parse_some_sheets_without_split_tables(
                classified_file,
                spreadsheet_parsing_results,
                sheet_names,
                predictions,
                confidences,
                consolidate_sheets_for,
            )

        for pred, conf, spreadsheet_parsing_result in zip(predictions, confidences, spreadsheet_parsing_results):
            spreadsheet_parsing_result.prediction = pred
            spreadsheet_parsing_result.confidence = conf

        return spreadsheet_parsing_results

    def _parse_some_sheets_without_split_tables(
        self,
        classified_file: ClassifiedFile,
        spreadsheet_parsing_results: list[SpreadsheetParsingResult],
        sheet_names: list[str],
        predictions: list[str],
        confidences: list[float],
        consolidate_sheets_for: list[ClassificationDocumentType],
    ) -> list[SpreadsheetParsingResult]:
        spreadsheet_parsing_results_without_split = self.spreadsheet_parser.parse_spreadsheet(
            filename=classified_file.file_name,
            file=BytesIO(classified_file.file_bytes),
            split_tables=False,
            apply_postprocessing=True,
        )

        original_sheet_names = [re.sub(r"_table_\d+", "", sheet_name) for sheet_name in sheet_names]
        res_grouped = defaultdict(list)
        for original_sheet_name, pred, conf, spreadsheet_parsing_result in zip(
            original_sheet_names, predictions, confidences, spreadsheet_parsing_results
        ):
            res_grouped[original_sheet_name].append((pred, conf, spreadsheet_parsing_result))

        modified_spreadsheet_parsing_results = []
        for sheet_name, res in res_grouped.items():
            preds = [el[0] for el in res]

            if any(pred in consolidate_sheets_for for pred in preds):
                parsing_result_without_split = [
                    parsing_result
                    for parsing_result in spreadsheet_parsing_results_without_split
                    if parsing_result.sheet_name == sheet_name
                ][0]
                predictions, confidences = self.classifier.spreadsheet_predict(
                    classified_file=classified_file,
                    column_names=[parsing_result_without_split.column_names],
                    sheet_names=[parsing_result_without_split.sheet_name],
                    dfs=[parsing_result_without_split.df],
                    parsing_results=[parsing_result_without_split],
                )
                parsing_result_without_split.prediction = predictions[0]
                parsing_result_without_split.confidence = confidences[0]
                modified_spreadsheet_parsing_results.append(parsing_result_without_split)
            else:
                for el in res:
                    pred, conf, spreadsheet_parsing_result = el
                    spreadsheet_parsing_result.prediction = pred
                    spreadsheet_parsing_result.confidence = conf
                    modified_spreadsheet_parsing_results.append(spreadsheet_parsing_result)
        return modified_spreadsheet_parsing_results
