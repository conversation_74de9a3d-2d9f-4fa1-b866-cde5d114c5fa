import os
from io import BytesIO

import fitz
import numpy as np
import pandas as pd
from copilot_client_v3.models.file import File
from infrastructure_common.logging import get_logger
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_type import FileType
from static_common.models.file_additional_info import FileAdditionalInfo
from static_common.schemas.file_additional_info import FileAdditionalInfoSchema
from static_common.utils.file_type_and_classification_mappings import (
    CLASSIFICATION_TO_FILE_TYPE,
)

from src.models.classifiers.document_classifier.classified_file import (
    ClassifiedFile,
    DocumentType,
)
from src.models.classifiers.document_classifier.constants import (
    ARCHIVE_EXTENSIONS,
    HTML_EXTENSIONS,
)
from src.models.classifiers.document_classifier.text_extraction import (
    _CYCLE_IN_PAGE_TREE,
    _RECURSION_ERROR,
    open_and_clean_pdf_using_bytes,
)

logger = get_logger()

SHEET_EXTENSIONS = {"xls", "xlsb", "xlsx", "csv", "xlsm"}
DOC_EXTENSIONS = {"doc", "docm", "docx", "odt"}
TEXT_EXTENSIONS = {"txt", "rtf"}
PDF_EXTENSIONS = {"pdf"}
IMAGE_EXTENSIONS = {"png", "jpg", "jpeg"}

OMAHA_ORG_ID = 45

MERGE_PAGES_CLASSES = [ClassificationDocumentType.WORK_COMP_EXPERIENCE_CA]


def get_file_type(classification: ClassificationDocumentType) -> FileType:
    return CLASSIFICATION_TO_FILE_TYPE[classification]


def is_sheet_file(file_name, check_inside: bool = False) -> bool:
    if check_inside:
        return any(["." + extension in str(file_name).lower() for extension in SHEET_EXTENSIONS if file_name])
    return any([str(file_name).lower().endswith(extension) for extension in SHEET_EXTENSIONS if file_name])


def is_doc_file(file_name, check_inside: bool = False) -> bool:
    if check_inside:
        return any(["." + extension in str(file_name).lower() for extension in DOC_EXTENSIONS if file_name])
    return any([str(file_name).lower().endswith(extension) for extension in DOC_EXTENSIONS if file_name])


def is_image_file(file_name, check_inside: bool = False) -> bool:
    if check_inside:
        return any(["." + extension in str(file_name).lower() for extension in IMAGE_EXTENSIONS if file_name])
    return any([str(file_name).lower().endswith(extension) for extension in IMAGE_EXTENSIONS if file_name])


def is_editable_document(file_name, check_inside: bool = False) -> bool:
    return is_doc_file(file_name, check_inside) or is_text_file(file_name, check_inside)


def is_text_file(file_name, check_inside: bool = False) -> bool:
    if check_inside:
        return any(["." + extension in str(file_name).lower() for extension in TEXT_EXTENSIONS if file_name])
    return any([str(file_name).lower().endswith(extension) for extension in TEXT_EXTENSIONS if file_name])


def is_pdf_file(file_name, check_inside: bool = False) -> bool:
    if check_inside:
        return any(["." + extension in str(file_name).lower() for extension in PDF_EXTENSIONS if file_name])
    return any([str(file_name).lower().endswith(extension) for extension in PDF_EXTENSIONS if file_name])


def is_html_file(filename: str, check_inside: bool = False) -> bool:
    if check_inside:
        return any(["." + extension in str(filename).lower() for extension in HTML_EXTENSIONS if filename])
    return filename.lower().endswith(tuple(HTML_EXTENSIONS))


def is_archive(filename: str, check_inside: bool = False) -> bool:
    if check_inside:
        return any(["." + extension in str(filename).lower() for extension in ARCHIVE_EXTENSIONS if filename])
    return filename.lower().endswith(tuple(ARCHIVE_EXTENSIONS))


def smooth_predictions(
    classified_file: ClassifiedFile,
    limit_to_classification: ClassificationDocumentType | None = None,
    confidence_threshold: float = 0.99,
) -> list[ClassificationDocumentType]:
    new_preds = []
    predictions = classified_file.page_level_classifications
    for ind, (pred, conf) in enumerate(zip(predictions, classified_file.page_level_confidences)):
        current_pred = pred
        can_be_changed = (
            not limit_to_classification
            or current_pred == ClassificationDocumentType.UNKNOWN
            and (ind == 0 or new_preds[ind - 1] == limit_to_classification)
        )
        if (
            ind > 0
            and ind < (len(predictions) - 1)
            and pred != predictions[ind - 1]
            and pred != predictions[ind + 1]
            and predictions[ind - 1] == predictions[ind + 1]
            and (conf < confidence_threshold or current_pred == ClassificationDocumentType.UNKNOWN)
            and can_be_changed
        ):
            current_pred = new_preds[ind - 1]
        if (
            ind == (len(predictions) - 1)
            and ind > 0
            and predictions[ind] != predictions[ind - 1]
            and (conf < confidence_threshold or current_pred == ClassificationDocumentType.UNKNOWN)
            and can_be_changed
        ):
            current_pred = new_preds[ind - 1]
        new_preds.append(current_pred)
    return new_preds


def _determine_filename(
    classified_file: ClassifiedFile,
    classification: ClassificationDocumentType,
    start_ind: int,
    combined_page_ranges: str,
) -> str:
    parent_filename, parent_extension = os.path.splitext(classified_file.file_name)
    if classification == ClassificationDocumentType.ACORD_FORM:
        acord_version = None
        if classified_file.additional_info and classified_file.additional_info.acord:
            acord_version = classified_file.additional_info.acord
        elif classified_file.page_level_additional_info and classified_file.page_level_additional_info[start_ind]:
            acord_version = classified_file.page_level_additional_info[start_ind].acord
        if acord_version:
            return f"{parent_filename}_{acord_version.version_id}{parent_extension}"
    # format '_Pages' is being used downstream in https://github.com/Kalepa/file-processing/pull/219/files
    return f"{parent_filename}_{classification.value}_Pages{combined_page_ranges}{parent_extension}"


def _new_classified_file(
    classified_file: ClassifiedFile,
    classification: ClassificationDocumentType,
    file_bytes: bytes,
    page_ranges: list[list[int]],
) -> ClassifiedFile:
    additional_info = classified_file.page_level_additional_info[page_ranges[0][0]]
    page_level_classifications = []
    page_level_confidences = []
    page_level_additional_info = []
    text_results = []
    combined_page_ranges = ""
    for start_page, end_page in page_ranges:
        page_level_classifications.extend(classified_file.page_level_classifications[start_page : end_page + 1])
        page_level_confidences.extend(classified_file.page_level_confidences[start_page : end_page + 1])
        page_level_additional_info.extend(classified_file.page_level_additional_info[start_page : end_page + 1])
        text_results.extend(classified_file.text_results[start_page : end_page + 1])
        combined_page_ranges += f"_({start_page + 1}_{end_page + 1})"
    if not additional_info:
        additional_info = FileAdditionalInfo()
    additional_info.number_of_pages = len(page_level_classifications)
    classification_confidence = np.nanmean(page_level_confidences) if page_level_confidences else 1
    return ClassifiedFile(
        file_id=None,
        file_name=_determine_filename(classified_file, classification, page_ranges[0][0], combined_page_ranges),
        submission_id=classified_file.submission_id,
        origin=classified_file.origin,
        classification=classification,
        document_type=classified_file.document_type,
        file_type=CLASSIFICATION_TO_FILE_TYPE[classification],
        file_bytes=file_bytes,
        additional_info=additional_info,
        page_level_classifications=page_level_classifications,
        page_level_confidences=page_level_confidences,
        classification_confidence=classification_confidence,
        initial_classification=classified_file.initial_classification,
        initial_classification_confidence=classified_file.initial_classification_confidence,
        page_level_additional_info=page_level_additional_info,
        parent_file_id=classified_file.file_id,
        text_results=text_results,
        presigned_url=classified_file.presigned_url,
        internal_notes=[f"File generated by splitting file {classified_file.file_id} in the classification process."],
        sensible_status=classified_file.sensible_status,
    )


def simple_page_cutting(
    file_bytes: bytes,
    file_id: str,
    selected_pages: list[list[int]] | None = None,
) -> bytes | None:
    try:
        doc = fitz.open(stream=file_bytes, filetype="pdf")
        new_pdf = fitz.open()  # Create a new empty PDF
        for pages in selected_pages:
            new_pdf.insert_pdf(doc, from_page=pages[0], to_page=pages[1])

        new_pdf_bytes = new_pdf.write(no_new_id=True)
        return new_pdf_bytes
    except Exception:
        logger.warning(
            "Simple PDF splitting method failed", file_id=file_id, selected_pages=selected_pages, exc_info=True
        )
        return None


def split_pdf(classified_file: ClassifiedFile) -> list[ClassifiedFile]:
    """
    Split PDF file based on prediction list - consecutive same assignments are saved to a single file.
    """
    if len(classified_file.page_level_classifications) == 0:
        return []
    new_documents, start_ind = [], 0
    current_type = classified_file.page_level_classifications[0]
    current_add_info = classified_file.page_level_additional_info[0]
    end_idx = len(classified_file.page_level_classifications) - 1
    merge_pages_class_wise_pages = {}
    for ind, (pred, add_info) in enumerate(
        zip(
            [*classified_file.page_level_classifications, "dummy"],
            [*classified_file.page_level_additional_info, "dummy"],
        )
    ):
        if pred != current_type or current_add_info != add_info or ind in classified_file.split_points:
            try:
                merge_pages_class = None
                if (
                    ind not in classified_file.split_points
                    and pred not in [ClassificationDocumentType.ACORD_101, ClassificationDocumentType.APPLIED_98]
                    and current_type.name[:5] == "ACORD"
                ):
                    version_id = current_add_info.acord.version_id if current_add_info else current_type.name
                    merge_pages_class = (version_id, current_type)
                elif current_type in MERGE_PAGES_CLASSES:
                    merge_pages_class = (current_type.name, current_type)
                if not merge_pages_class:
                    # We open the copy of PDF and delete the pages instead of inserting the pages we want to keep
                    # because insert_pdf doesn't maintain widgets (form editors) and we want to keep them.
                    new_document, _ = open_and_clean_pdf_using_bytes(classified_file.file_bytes)

                    if ind <= end_idx:
                        safe_delete_pages(new_document, ind, end_idx)
                    if start_ind > 0:
                        safe_delete_pages(new_document, 0, start_ind - 1)

                    simple_result = simple_page_cutting(
                        file_bytes=classified_file.file_bytes,
                        selected_pages=[[start_ind, ind - 1]],
                        file_id=classified_file.file_id,
                    )

                    try:
                        new_file_bytes = new_document.write(no_new_id=True, deflate=True, deflate_images=True)
                    except Exception:
                        logger.warning(
                            "Failed to write PDF file by old method",
                            start_ind=start_ind,
                            end_ind=ind - 1,
                            file_id=classified_file.file_id,
                            exc_info=True,
                        )
                        new_file_bytes = simple_result

                    if not new_file_bytes:
                        logger.warning(
                            "No method worked to save the new file",
                            file_id=classified_file.file_id,
                            start_ind=start_ind,
                            end_ind=ind - 1,
                        )

                    new_documents.append(
                        _new_classified_file(
                            classified_file,
                            current_type,
                            new_file_bytes,
                            [[start_ind, ind - 1]],
                        )
                    )
                elif merge_pages_class:
                    if merge_pages_class not in merge_pages_class_wise_pages:
                        merge_pages_class_wise_pages[merge_pages_class] = [[start_ind, ind - 1]]
                    else:
                        merge_pages_class_wise_pages[merge_pages_class].append([start_ind, ind - 1])

                if ind == end_idx + 1:
                    for merge_pages_class, page_indexes in merge_pages_class_wise_pages.items():
                        new_document, _ = open_and_clean_pdf_using_bytes(classified_file.file_bytes)
                        to_page = end_idx
                        for start, end in page_indexes[::-1] + [[-1, -1]]:
                            from_page = end + 1
                            if from_page <= to_page:
                                safe_delete_pages(new_document, from_page, to_page)
                            to_page = start - 1

                        simple_result = simple_page_cutting(
                            file_bytes=classified_file.file_bytes,
                            selected_pages=merge_pages_class_wise_pages[merge_pages_class],
                            file_id=classified_file.file_id,
                        )
                        try:
                            new_file_bytes = new_document.write(no_new_id=True, deflate=True, deflate_images=True)
                        except:
                            logger.warning(
                                "Failed to write PDF file by old method",
                                start_ind=start_ind,
                                end_ind=ind - 1,
                                file_id=classified_file.file_id,
                            )
                            new_file_bytes = simple_result

                        if not new_file_bytes:
                            logger.warning(
                                "No method worked to save the new file",
                                file_id=classified_file.file_id,
                                start_ind=start_ind,
                                end_ind=ind - 1,
                            )

                        classification = merge_pages_class[1]
                        new_documents.append(
                            _new_classified_file(
                                classified_file,
                                classification,
                                new_file_bytes,
                                merge_pages_class_wise_pages[merge_pages_class],
                            )
                        )

            except RuntimeError as e:
                if str(e) not in [_RECURSION_ERROR, _CYCLE_IN_PAGE_TREE]:
                    raise
                else:
                    logger.warning(f"Failed to extract document from pages {start_ind} to {ind - 1} due to {e}")
            start_ind, current_type, current_add_info = ind, pred, add_info

    return new_documents


def safe_delete_page(document: fitz.Document, pno: int):
    try:
        document.delete_page(pno)
    except RuntimeError as e:
        if str(e) == "cycle in page tree":
            logger.warning("Cycle in page tree. Skipping page deletion", pno=pno)
        elif "cannot find page" in str(e):
            logger.warning("Cannot find page to delete, skipping", pno=pno)
        else:
            raise


def safe_delete_pages(document: fitz.Document, from_page: int, to_page: int):
    try:
        document.delete_pages(from_page=from_page, to_page=to_page)
    except RuntimeError as e:
        if str(e) == "cycle in page tree":
            logger.warning("Cycle in page tree. Skipping page deletion", from_page=from_page, to_page=to_page)
        elif "cannot find page" in str(e):
            logger.warning("Cannot find pages to delete, skipping", from_page=from_page, to_page=to_page)
        else:
            raise


def enum_file_extension(classified_file: ClassifiedFile, file_name: str):
    prediction = classified_file.classification
    if not prediction or prediction == ClassificationDocumentType.MERGED:
        return prediction
    if classified_file.document_type == DocumentType.PDF and f"{prediction.name}_PDF" in ClassificationDocumentType:
        return ClassificationDocumentType(f"{prediction.name}_PDF")
    if (
        classified_file.document_type == DocumentType.DOC
        and f"{prediction.name}_EDITABLE_DOC" in ClassificationDocumentType
    ):
        return ClassificationDocumentType(f"{prediction.name}_EDITABLE_DOC")
    if (
        classified_file.document_type == DocumentType.SPREADSHEET
        and f"{prediction.name}_SPREADSHEET" in ClassificationDocumentType
    ):
        return ClassificationDocumentType(f"{prediction.name}_SPREADSHEET")
    return prediction


def enhance_prediction_with_extension(
    classified_files: list[ClassifiedFile],
) -> list[ClassifiedFile]:
    for classified_file in classified_files:
        classified_file.classification = enum_file_extension(classified_file, classified_file.file_name)
    return classified_files


def detect_document_type(filename: str, use_fallback: bool = True) -> DocumentType:
    if is_pdf_file(filename):
        return DocumentType.PDF
    if is_doc_file(filename):
        return DocumentType.DOC
    if is_image_file(filename):
        return DocumentType.IMAGE
    if is_sheet_file(filename):
        return DocumentType.SPREADSHEET
    if is_html_file(filename):
        return DocumentType.HTML_DOC
    if is_archive(filename):
        return DocumentType.ARCHIVE
    if use_fallback:
        return _detect_document_type_fallback(filename)
    return DocumentType.OTHER


def _detect_document_type_fallback(filename: str) -> DocumentType:
    if is_pdf_file(filename, check_inside=True):
        return DocumentType.PDF
    if is_doc_file(filename, check_inside=True):
        return DocumentType.DOC
    if is_image_file(filename, check_inside=True):
        return DocumentType.IMAGE
    if is_sheet_file(filename, check_inside=True):
        return DocumentType.SPREADSHEET
    if is_html_file(filename, check_inside=True):
        return DocumentType.HTML_DOC
    return DocumentType.OTHER


def file_to_classified_file(file_object: File) -> ClassifiedFile:
    return ClassifiedFile(
        file_id=file_object.id,
        s3_key=file_object.s3_key,
        file_name=file_object.name,
        submission_id=file_object.submission_id,
        organization_id=file_object.organization_id,
        file_type=file_object.user_file_type or file_object.file_type,
        document_type=detect_document_type(file_object.name),
        is_internal=file_object.is_internal,
        parent_file_id=file_object.parent_file_id,
        classification=file_object.classification,
        additional_info=(
            FileAdditionalInfoSchema().load(file_object.additional_info) if file_object.additional_info else None
        ),
        origin=file_object.origin,
        presigned_url=file_object.presigned_url,
        sensible_status=file_object.sensible_status,
        processing_state=file_object.processing_state,
        classification_confidence=1,
        initial_classification=file_object.initial_classification,
        initial_classification_confidence=file_object.initial_classification_confidence,
        is_hidden=file_object.hidden,
        is_locked=file_object.is_locked,
    )


def load_file_bytes(classified_files: list[ClassifiedFile]) -> list[bytes]:
    result = []
    for classified_file in classified_files:
        with open(classified_file.file_path, "rb") as f:
            result.append(f.read())
    return result


def clean_df_invalid_characters(df: pd.DataFrame) -> pd.DataFrame:
    return df.applymap(lambda x: x.encode("unicode_escape").decode("utf-8") if isinstance(x, str) else x)


def df_to_excel_bytes(df: pd.DataFrame, header=True) -> bytes:
    buf = BytesIO()
    df = clean_df_invalid_characters(df)
    df.to_excel(buf, header=header, index=False)
    buf.seek(0)
    return buf.read()
