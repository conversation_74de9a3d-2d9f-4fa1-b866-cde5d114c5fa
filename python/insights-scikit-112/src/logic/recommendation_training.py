import json
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

import pandas as pd
from dateutil.parser import parse
from infrastructure_common.logging import get_logger
from interpret.glassbox import ExplainableBoostingClassifier
from sklearn.metrics import roc_auc_score
from static_common.enums.organization import ExistingOrganizations
from static_common.models.recommendation_model import RecommendationModel

from src.scoring_utils import EBMPredictionExplanation

logger = get_logger()

CANDIDATE_COLUMNS = {
    "fni_state",
    "fni_county",
    "primary_naics",
    "bpp_sum",
    "two_digits_primary_naics",
    "years_in_business_avg",
    "payroll",
    "brokerage_name",
    "contractor_submission_type",
    "broker_name",
    "tiv_sum",
    "building_age_avg",
    "building_size_sum",
    "building_value_sum",
    "osha_violation_count",
    "employee_count_sum",
    "lot_size_avg",
    "number_of_registered_drivers_sum",
    "number_of_stories_avg",
    "number_of_units_avg",
    "number_of_vehicles_sum",
    "occupied_area_sum",
    "project_subcontractors_cost_sum",
    "structures_count_avg",
    "project_percentage_of_work_commercial_avg",
    "project_percentage_of_exterior_work_avg",
    "project_percentage_of_interior_work_avg",
    "coverage_str",
    "number_of_basements_sum",
    "number_of_basements_avg",
    "number_of_bathrooms_avg",
    "number_of_bedroom_avg",
    "number_of_doctors_sum",
    "number_of_elevators_avg",
    "number_of_guards_sum",
    "number_of_guards_avg",
    "number_of_hospital_beds_sum",
    "number_of_nurses_sum",
    "number_of_projects_sum",
    "number_of_registered_drivers_sum",
    "number_of_rooms_sum",
    "number_of_shifts_sum",
    "number_of_shifts_avg",
    "number_of_stories_avg",
    "number_of_swimming_pools_sum",
    "number_of_units_sum",
    "number_of_units_avg",
    "number_of_vehicles_sum",
    "number_of_watercraft_sum",
    "distance_to_fire_hydrant_avg",
    "distance_to_fire_hydrant_max",
    "number_of_swimming_pools_sum",
    "distance_to_closest_premises_in_submission_max",
    "distance_to_closest_premises_in_submission_avg",
    "distance_to_body_of_water_max",
    "distance_to_body_of_water_avg",
    "primary_state",
    "sales",
    "is_renewal",
    "excess_attachment_point",
    "excess_limit",
    "proximity_to_effectivedate",
    "days_to_quote",
    "historical_broker_bind_rate",
    "construction_bind_rate",
    "non_construction_bind_rate",
    "is_construction",
    # "real_estate_bind_rate",
    # "non_real_estate_bind_rate",
    # "is_real_estate",
    # "food_accommodation_bind_rate",
    # "non_food_accommodation_bind_rate",
    # "is_food_accommodation",
}

FUNCTIONAL_COLUMNS = {
    "is_quoted",
    "stage",
}

MODEL_FEATURE_TO_INPUT_PARAMS = {
    "historical_broker_bind_rate": ["broker_name"],
    "construction_bind_rate": ["broker_name"],
    "non_construction_bind_rate": ["broker_name"],
    "real_estate_bind_rate": ["broker_name"],
    "non_real_estate_bind_rate": ["broker_name"],
    "food_accommodation_bind_rate": ["broker_name"],
    "non_food_accommodation_bind_rate": ["broker_name"],
    "is_construction": ["two_digits_primary_naics"],
    "is_real_estate": ["two_digits_primary_naics"],
    "is_food_accommodation": ["two_digits_primary_naics"],
    "proximity_to_effectivedate": ["effective_date", "received_date"],
    "days_to_quote": ["received_date", "quoted_date"],
    "coverage_str": ["coverages"],
}

NAICS_TO_BIND_RATE_FEATURE = {
    "construction_bind_rate": "is_construction",
    "non_construction_bind_rate": "is_construction",
    "real_estate_bind_rate": "is_real_estate",
    "non_real_estate_bind_rate": "is_real_estate",
    "food_accommodation_bind_rate": "is_food_accommodation",
    "non_food_accommodation_bind_rate": "is_food_accommodation",
}

SCORE_IMPACT_CUTOFF = 0.075


class Preprocessor:
    @staticmethod
    def parse_date(value: Any):
        try:
            return parse(value, dayfirst=False)
        except Exception:
            return None

    @staticmethod
    def parse_coverage(coverages_str):
        if not coverages_str:
            return None
        data = json.loads(coverages_str)
        return " ".join([f'{cvg["coverage_type"]} {cvg["name"]}' for cvg in data])

    @staticmethod
    def get_bind_rate(df: pd.DataFrame, agent_name: str):
        agent_stuff = df[(df.broker_name == agent_name)]
        relevant_data = df.loc[agent_stuff.index]
        if len(agent_stuff) == 0:
            return None
        return len(relevant_data[relevant_data.stage == "QUOTED_BOUND"]) / len(agent_stuff)


class Trainer:
    MIN_POSITIVE_CASES = 100
    DAYS_OFFSET_FOR_TRAINING = 30

    def __init__(self, df: pd.DataFrame, organization_id: int, user_group: str, only_quoted: bool = True):
        self.preprocessor = Preprocessor()
        self.df = df
        self.organization_id = organization_id
        self.user_group = user_group
        self.only_quoted = only_quoted
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.model = None
        self.pre_selection_model = None
        self.split_column = "received_date"
        if self.user_group:
            self._filter_user_group()

    def _filter_user_group(self):
        self.df["user_group_id"] = self.df.user_groups.apply(
            lambda x: json.loads(x)[0]["id"] if isinstance(x, str) else None
        )
        self.df = self.df[self.df.user_group_id == self.user_group]

    @staticmethod
    def get_days_to_eff(row):
        try:
            parsed_date_eff = parse(row.effective_date, dayfirst=False)
            parsed_date_received = parse(row.received_date, dayfirst=False)
            delta = parsed_date_eff - parsed_date_received
            return delta.days
        except Exception:
            return None

    @staticmethod
    def get_days_to_quote(row):
        try:
            parsed_date_quoted = parse(row.quoted_date, dayfirst=False)
            parsed_date_received = parse(row.received_date, dayfirst=False)
            delta = parsed_date_quoted - parsed_date_received
            return delta.days
        except Exception:
            return None

    @staticmethod
    def fill_na_values(df: pd.DataFrame):
        for col in CANDIDATE_COLUMNS:
            if df[col].dtype == "float64":
                df[col] = df[col].fillna(-1000)

        for col in CANDIDATE_COLUMNS:
            if df[col].dtype == "object":
                df[col] = df[col].fillna("")

    def get_bind_rate_map(self, df: pd.DataFrame) -> dict[tuple[str, str], float]:
        bind_rate_map = {}
        for feature, filtering_column in NAICS_TO_BIND_RATE_FEATURE.items():
            if filtering_column not in df.columns:
                continue
            filtered_df = df[~df[filtering_column]] if feature.startswith("non") else df[df[filtering_column]]
            for broker in filtered_df.broker_name.unique():
                bind_rate_map[(feature, broker)] = self.preprocessor.get_bind_rate(df=filtered_df, agent_name=broker)
        return bind_rate_map

    def process_features(self):
        self.df["is_quoted"] = self.df.stage.apply(lambda x: x and "QUOTED" in x)
        self.df["parsed_effective_date"] = self.df.effective_date.apply(self.preprocessor.parse_date)
        self.df["days_to_quote"] = self.df.apply(self.get_days_to_quote, axis=1)
        self.df[self.df.parsed_effective_date < "2025-01-15"].stage.value_counts()
        self.df["coverage_str"] = self.df.coverages.apply(self.preprocessor.parse_coverage)
        self.df["proximity_to_effectivedate"] = self.df.apply(self.get_days_to_eff, axis=1)
        self.df["is_bound"] = self.df.stage.apply(lambda x: "QUOTED_BOUND" in x)
        self.df["is_construction"] = self.df.two_digits_primary_naics.apply(lambda x: True if x == "23" else False)
        self.df["is_real_estate"] = self.df.two_digits_primary_naics.apply(lambda x: True if x == "53" else False)
        self.df["is_food_accommodation"] = self.df.two_digits_primary_naics.apply(
            lambda x: True if x == "72" else False
        )
        bind_rate_map = {
            broker: self.preprocessor.get_bind_rate(df=self.df, agent_name=broker)
            for broker in self.df.broker_name.unique()
        }
        # Map the precomputed bind rates to the DataFrame
        self.df["historical_broker_bind_rate"] = self.df.broker_name.map(bind_rate_map)
        bind_rate_map = self.get_bind_rate_map(self.df)
        # Assign precomputed bind rates to the DataFrame
        for feature in NAICS_TO_BIND_RATE_FEATURE.keys():
            self.df[feature] = self.df.apply(lambda row: bind_rate_map.get((feature, row.broker_name), None), axis=1)

    def get_split_date(self, column="received_date"):
        sorted_df = self.df.sort_values(by=column)
        sorted_df["cumulative_positive"] = sorted_df.is_bound.cumsum()
        total_positive_cases = sorted_df.is_bound.sum()
        fraction = 0.8
        split_date = None
        while fraction > 0.6 and split_date is None:
            split_point = total_positive_cases * fraction
            valid_elems = sorted_df[(sorted_df.cumulative_positive >= split_point) & (~sorted_df[column].isna())]
            if not valid_elems.empty:
                split_date = valid_elems[column].iloc[0]
            else:
                fraction -= 0.1
        return split_date

    def train_test_set_split(self, df: pd.DataFrame):
        split_date = self.get_split_date(self.split_column)
        if not split_date:
            logger.warning("No split date found, trying with created_date")
            self.split_column = "created_date"
            split_date = self.get_split_date(self.split_column)
        if not split_date:
            raise ValueError("No split date found")
        logger.info("Splitting the data", split_date=split_date)

        X_train, y_train = (
            df[df[self.split_column] <= split_date][list(CANDIDATE_COLUMNS) + list(FUNCTIONAL_COLUMNS)],
            df[df[self.split_column] <= split_date].is_bound,
        )
        X_test, y_test = (
            df[df[self.split_column] > split_date][list(CANDIDATE_COLUMNS) + list(FUNCTIONAL_COLUMNS)],
            df[df[self.split_column] > split_date].is_bound,
        )
        # overwriting the historical bind rate with the one from the training set
        global_bind_rate_map = {
            broker: self.preprocessor.get_bind_rate(df=X_train, agent_name=broker)
            for broker in X_train.broker_name.unique()
        }
        # Map the precomputed bind rates to the DataFrame
        X_train["historical_broker_bind_rate"] = X_train.broker_name.map(global_bind_rate_map)
        X_test["historical_broker_bind_rate"] = X_test.broker_name.map(global_bind_rate_map)

        bind_rate_map = self.get_bind_rate_map(X_train)

        for feature in NAICS_TO_BIND_RATE_FEATURE:
            X_train[feature] = X_train.apply(lambda row: bind_rate_map.get((feature, row.broker_name), None), axis=1)
            X_test[feature] = X_test.apply(lambda row: bind_rate_map.get((feature, row.broker_name), None), axis=1)

        self.fill_na_values(X_train)
        self.fill_na_values(X_test)

        train_mask = X_train.is_quoted
        test_mask = X_test.is_quoted
        self.X_train = X_train if not self.only_quoted else X_train[train_mask]
        self.X_test = X_test if not self.only_quoted else X_test[test_mask]
        self.y_train = y_train if not self.only_quoted else y_train[train_mask]
        self.y_test = y_test if not self.only_quoted else y_test[test_mask]
        logger.info(
            "Training data stats",
            training_samples=len(self.y_train),
            training_positive_cases=sum(self.y_train),
            test_cases=len(self.y_test),
            positive_test_cases=sum(self.y_test),
        )

    def get_auc(self, training_columns: list[str]) -> float:
        auc = roc_auc_score(self.y_test, self.model.predict_proba(self.X_test[training_columns])[:, 1])
        return auc

    def get_auc_full_data(self, training_columns: list[str]) -> float:
        mask = self.df.is_quoted & (
            self.df[self.split_column] < (datetime.now().date() - timedelta(days=self.DAYS_OFFSET_FOR_TRAINING))
        )
        auc = roc_auc_score(self.df.is_bound[mask], self.model.predict_proba(self.df[mask][training_columns])[:, 1])
        return auc

    def train(self, training_columns: list[str]) -> None:
        ebm = ExplainableBoostingClassifier()
        ebm.fit(self.X_train[training_columns], self.y_train)
        self.model = ebm

    def train_on_full_data(self, training_columns: list[str]) -> None:
        ebm = ExplainableBoostingClassifier()
        thirty_days_ago = datetime.now().date() - timedelta(days=self.DAYS_OFFSET_FOR_TRAINING)
        mask = self.df.is_quoted & (self.df[self.split_column] < thirty_days_ago)
        logger.info("Training on full data", number_of_cases=sum(mask))
        ebm.fit(self.df[mask][training_columns], self.df.is_bound[mask])
        self.model = ebm

    def get_top_features(self) -> list[str]:
        explanations_raw = self.model.explain_global()
        explanations = EBMPredictionExplanation.from_dict(explanations_raw.data())
        top_features_by_score = sorted(
            zip(explanations.scores, explanations.names), key=lambda x: abs(x[0]), reverse=True
        )
        selected_features = []
        for score, name in top_features_by_score:
            if abs(score) < SCORE_IMPACT_CUTOFF:
                break
            new_features = [elem.strip() for elem in name.split("&")]
            for feature in new_features:
                if feature not in selected_features:
                    selected_features.append(feature)
        return selected_features

    @staticmethod
    def get_input_params(selected_features: list[str]) -> list[str]:
        result = []
        for feature in selected_features:
            if feature in MODEL_FEATURE_TO_INPUT_PARAMS:
                result.extend(MODEL_FEATURE_TO_INPUT_PARAMS[feature])
            else:
                result.append(feature)
        return result

    def get_model_name(self) -> str:
        org_name = ExistingOrganizations(self.organization_id).name.replace(" ", "_")
        return f"model_{org_name}_{str(uuid.uuid4())[-12:]}.pkl"

    def run_pipeline(self) -> RecommendationModel:
        training_process_id = str(uuid.uuid4())
        result_model = RecommendationModel(
            organization_id=self.organization_id, user_group=uuid.UUID(self.user_group) if self.user_group else None
        )
        log = logger.bind(organization_id=self.organization_id, training_process_id=training_process_id)
        try:
            self.process_features()
            self.fill_na_values(self.df)
            self.train_test_set_split(self.df)
            positive_cases = sum(self.y_train) + sum(self.y_test)
            if positive_cases < self.MIN_POSITIVE_CASES:
                log.warning("Not enough positive cases to train the model", number_of_cases=positive_cases)
                result_model.error = f"Not enough positive cases to train the model, ref {training_process_id}"
                return result_model
            self.train(list(CANDIDATE_COLUMNS))
            auc = self.get_auc(list(CANDIDATE_COLUMNS))
            log.info("AUC before feature selection", auc=auc)
            self.pre_selection_model = self.model

            selected_features = self.get_top_features()
            self.train(selected_features)
            auc = self.get_auc(selected_features)
            log.info("AUC after feature selection", auc=auc, selected_features=selected_features)
            try:
                self.train_on_full_data(selected_features)
                auc_full = self.get_auc_full_data(selected_features)
                log.info("AUC on full data", auc=auc_full)
            except Exception as e:
                log.warning("Failed to calculate AUC on full data", exc_info=e)
            return RecommendationModel(
                organization_id=self.organization_id,
                user_group=uuid.UUID(self.user_group) if self.user_group else None,
                support=len(self.y_test),
                auc_score=auc,
                input_parameters=self.get_input_params(selected_features),
                model_parameters=selected_features,
                classification_threshold=0.5,
                model_type=self.model.__class__.__name__,
                model_file_name=self.get_model_name(),
            )
        except Exception as e:
            log.exception("Error during recommendation model training", exc_info=e)
            result_model.error = f"Error during recommendation model training: {e}, ref {training_process_id}"
            return result_model
