from pathlib import Path
from test.fact_subtypes.fixtures import (
    field_subtype_selection,
    llm_field_subtypes_selection,
    llm_multiple_fields_subtypes_selection,
)
from test.fact_subtypes.test_field_type_inference import field_type_inference
from test.fact_subtypes.test_matcher import (
    fact_subtypes,
    fact_subtypes_matcher,
    sov_enum_matcher,
)
from typing import Any, List

import pytest
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.parent import ParentType
from static_common.models.file_onboarding import (
    FactSubtypeSuggestion,
    ResolvedDataField,
    ResolvedDataValue,
)
from static_common.models.matching_metadata import MatcherType, MatchingMetadata

from datascience_common.classifiers.fact_identification.enum_matching import (
    EnumMatchResult,
)
from datascience_common.fact_subtypes.field_subtype_selection.multiple_fields_subtype_selection import (
    MultipleFieldsSubtypeSelection,
)
from datascience_common.fact_subtypes.field_subtype_selection.single_field_subtype_selection import (
    SingleFieldSubtypeSelection,
)

USED_FIXTURES = (
    field_type_inference,
    fact_subtypes_matcher,
    sov_enum_matcher,
    fact_subtypes,
    field_subtype_selection,
    llm_field_subtypes_selection,
    llm_multiple_fields_subtypes_selection,
)

CURRENT_DIR = Path(__file__).parent


@pytest.mark.parametrize(
    "field_name, field_values, expected",
    [
        ("Year Built", ["1990"], FactSubtypeID.YEAR_BUILT),
    ],
)
def test_assign_fact_subtype_llm(
    field_name: str,
    field_values: List[Any],
    expected: FactSubtypeID,
    llm_field_subtypes_selection: SingleFieldSubtypeSelection,
) -> None:
    llm_field_subtypes_selection._fact_subtype_matcher._enum_matcher.set_propositions(
        {
            FactSubtypeID.YEAR_BUILT: EnumMatchResult(
                confidence=0.9,
                evidence=["Fetched Year Built"],
            )
        }
    )
    mutable_field = ResolvedDataField(
        name=field_name,
        values=[ResolvedDataValue(value=value) for value in field_values],
        value_type=None,
    )

    llm_field_subtypes_selection.assign_fact_subtype(
        mutable_field=mutable_field,
        parent_type=ParentType.STRUCTURE,
    )
    assert mutable_field.fact_subtype_id == expected
    assert mutable_field.observed_name == field_name
    assert mutable_field.matching_metadata == MatchingMetadata(
        matcher_type=MatcherType.LLM,
        parser_type=None,
        matching_explanation={
            "closest_embedding_value": ["Fetched Year Built"],
        },
        dropped_explanation=None,
    )


def test_do_not_delete_suggestions_if_field_was_not_matched(
    llm_multiple_fields_subtypes_selection: MultipleFieldsSubtypeSelection,
) -> None:
    llm_multiple_fields_subtypes_selection._fact_subtype_matcher._enum_matcher.set_propositions(
        {
            FactSubtypeID.BUILDING_VALUE: EnumMatchResult(
                confidence=1.0,
                evidence=["Fetched Building Value"],
            ),
        }
    )
    mutable_fields = [
        ResolvedDataField(
            name="Property Section - Building$",
            values=[ResolvedDataValue(value="100 000")],
            value_type=None,
        ),
        ResolvedDataField(
            name="Property Section - Building: Deductible $",
            values=[ResolvedDataValue(value="100 000")],
            value_type=None,
        ),
    ]

    llm_multiple_fields_subtypes_selection._field_subtype_selection._matcher.field_names_to_not_match = [
        "Property Section - Building$"
    ]

    llm_multiple_fields_subtypes_selection.assign_fact_subtypes(
        mutable_fields=mutable_fields,
        field_name_to_parent_type={field.name: ParentType.STRUCTURE for field in mutable_fields},
    )

    assert mutable_fields[0].fact_subtype_id is None
    assert mutable_fields[0].fact_subtype_suggestions == [
        FactSubtypeSuggestion(
            fact_subtype_id=FactSubtypeID.BUILDING_VALUE,
            field_type=FieldType.INTEGER,
            name_score=1.0,
            values_score=0.5,
            confidence=0.5,
            explanations=["Fetched Building Value"],
        ),
    ]

    assert mutable_fields[1].fact_subtype_id == FactSubtypeID.BUILDING_VALUE
    assert mutable_fields[1].fact_subtype_suggestions == [
        FactSubtypeSuggestion(
            fact_subtype_id=FactSubtypeID.BUILDING_VALUE,
            field_type=FieldType.INTEGER,
            name_score=1.0,
            values_score=0.5,
            confidence=0.5,
            explanations=["Fetched Building Value"],
        ),
    ]
