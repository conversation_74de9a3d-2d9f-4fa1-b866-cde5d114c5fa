from test.utils import get_fact_subtypes

import pytest
from facts_client.model.fact_subtype import FactSubtype
from static_common.enums.fact_subtype import FactSubtypeID

from datascience_common.classifiers.fact_identification.enum_matching import EnumMatcher
from datascience_common.classifiers.fact_identification.fact_embedding_matching import (
    EmbeddingFactsIdentifier,
    FakeEmbeddingFactsIdentifier,
)
from datascience_common.classifiers.fact_identification.llm_matcher import (
    FakeLLMFactSubtypeMatcher,
)
from datascience_common.classifiers.fact_identification.sov import SOVFactsIdentifier
from datascience_common.classifiers.fact_identification.supplementals.supplemental_matching import (
    TemplateSupplementalFactsIdentifier,
)
from datascience_common.fact_subtypes.field_name_matching.fact_subtype_matcher import (
    FactSubtypeMatcher,
)
from datascience_common.fact_subtypes.field_name_matching.field_name_match_scorer import (
    FieldNameMatchScorer,
)
from datascience_common.fact_subtypes.field_subtype_selection.field_subtype_llm_selection import (
    FieldSubtypeLLMSelection,
)
from datascience_common.fact_subtypes.field_subtype_selection.field_subtype_selection import (
    FieldSubtypeSelection,
    IFieldSubtypeSelection,
)
from datascience_common.fact_subtypes.field_subtype_selection.multiple_fields_subtype_selection import (
    MultipleFieldsSubtypeSelection,
)
from datascience_common.fact_subtypes.field_subtype_selection.single_field_subtype_selection import (
    SingleFieldSubtypeSelection,
)
from datascience_common.fact_subtypes.field_type_inference.field_type_inference import (
    FieldTypeInference,
)


@pytest.fixture
def field_type_inference() -> FieldTypeInference:
    return FieldTypeInference()


@pytest.fixture(scope="session")
def fact_subtypes() -> list[FactSubtype]:
    return get_fact_subtypes()


@pytest.fixture(scope="session")
def fact_subtype_id_to_fact_subtype(fact_subtypes: list[FactSubtype]) -> dict[FactSubtypeID, FactSubtype]:
    return {fact_subtype.id: fact_subtype for fact_subtype in fact_subtypes}


@pytest.fixture(scope="session")
def sov_enum_matcher(fact_subtypes: list[FactSubtype]) -> EnumMatcher:
    return SOVFactsIdentifier(fact_subtypes=fact_subtypes)


@pytest.fixture(scope="session")
def fact_subtypes_matcher(sov_enum_matcher: EnumMatcher) -> FactSubtypeMatcher:
    return FactSubtypeMatcher(enum_matcher=sov_enum_matcher)


@pytest.fixture(scope="session")
def field_subtype_selection(
    fact_subtypes: list[FactSubtype],
) -> IFieldSubtypeSelection:
    return FieldSubtypeSelection(
        fact_subtypes=fact_subtypes, field_name_match_scorer=FieldNameMatchScorer.create(fact_subtypes=fact_subtypes)
    )


@pytest.fixture(scope="session")
def single_field_subtype_selection(
    fact_subtypes: list[FactSubtype],
) -> SingleFieldSubtypeSelection:
    return SingleFieldSubtypeSelection.create(fact_subtypes=fact_subtypes)


@pytest.fixture(scope="session")
def single_field_subtype_selection_supplemental(fact_subtypes: list[FactSubtype]) -> SingleFieldSubtypeSelection:
    return SingleFieldSubtypeSelection.create(
        fact_subtypes=fact_subtypes, enum_matcher=TemplateSupplementalFactsIdentifier()
    )


@pytest.fixture(scope="session")
def multiple_fields_subtype_selection(
    fact_subtypes: list[FactSubtype],
) -> MultipleFieldsSubtypeSelection:
    return MultipleFieldsSubtypeSelection.create(fact_subtypes=fact_subtypes)


@pytest.fixture(scope="session")
def field_name_match_scorer(fact_subtypes: list[FactSubtype]) -> FieldNameMatchScorer:
    return FieldNameMatchScorer.create(fact_subtypes=fact_subtypes)


@pytest.fixture(scope="session")
def multiple_field_subtype_selection_supplemental(fact_subtypes: list[FactSubtype]) -> MultipleFieldsSubtypeSelection:
    return MultipleFieldsSubtypeSelection.create(
        fact_subtypes=fact_subtypes, enum_matcher=TemplateSupplementalFactsIdentifier()
    )


@pytest.fixture(scope="session")
def llm_field_subtypes_selection(
    fact_subtypes: list[FactSubtype],
) -> SingleFieldSubtypeSelection:
    return SingleFieldSubtypeSelection.create(
        fact_subtypes=fact_subtypes,
        enum_matcher=FakeEmbeddingFactsIdentifier(),
        field_subtype_selection=FieldSubtypeLLMSelection(
            fact_subtypes=fact_subtypes, matcher=FakeLLMFactSubtypeMatcher()
        ),
    )


@pytest.fixture(scope="session")
def llm_multiple_fields_subtypes_selection(
    fact_subtypes: list[FactSubtype],
) -> MultipleFieldsSubtypeSelection:
    return MultipleFieldsSubtypeSelection.create(
        fact_subtypes=fact_subtypes,
        enum_matcher=FakeEmbeddingFactsIdentifier(),
        field_subtype_selection=FieldSubtypeLLMSelection(
            fact_subtypes=fact_subtypes, matcher=FakeLLMFactSubtypeMatcher()
        ),
    )
