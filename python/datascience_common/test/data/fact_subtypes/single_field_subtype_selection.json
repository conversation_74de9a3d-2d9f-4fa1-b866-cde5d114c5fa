[{"field_name": "Activity", "field_values": ["31226000"], "fact_subtype_id": null}, {"field_name": "Distributors- Food Or Drink (r)", "field_values": ["31226000"], "fact_subtype_id": null}, {"field_name": "aaaaaS Fbbbbb", "field_values": ["31226000"], "fact_subtype_id": null}, {"field_name": "aaaaaSFbbbbb", "field_values": ["31226000"], "fact_subtype_id": null}, {"field_name": "Total Food Sales", "field_values": [18000222], "fact_subtype_id": "FOOD_SALES"}, {"field_name": "Number of employees", "field_values": [2, 3, 1], "fact_subtype_id": "EMPLOYEE_COUNT"}, {"field_name": "Total number of employees", "field_values": [2, 3, 1], "fact_subtype_id": "EMPLOYEE_COUNT"}, {"field_name": "Year Roof covering last fully replaced", "field_values": ["1990"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Wiring", "field_values": [2023, 2014], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Wiring", "field_values": [2024], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Updates: HVAC/Roof/ Plumbing/Electrical", "field_values": [2021, 2023], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Year Upgraded Electrical, HVAC, Plumbing (YYYY)", "field_values": [2021, 2022], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Electrical Work?", "field_values": ["Yes"], "fact_subtype_id": "PROJECT_ELECTRICAL_WORK"}, {"field_name": "Building Sq. footage", "field_values": [13200], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Square Footage Client/Bldg", "field_values": [2098, 3300], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "(Calc) Avg Bldg Cost / SF", "field_values": [100, 200], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Cost/Sq Ft", "field_values": [100, 200], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Building TIV ($/sq ft)", "field_values": [200, 300], "fact_subtype_id": "TIV_PER_SQ_FT"}, {"field_name": "Building TIV sq ft", "field_values": [200, 300], "fact_subtype_id": "TIV_PER_SQ_FT"}, {"field_name": "TIV/Sq Ft)", "field_values": [200, 300], "fact_subtype_id": "TIV_PER_SQ_FT"}, {"field_name": "Building ITV ($/sq ft)", "field_values": [200, 300], "fact_subtype_id": "ITV"}, {"field_name": "ITV (Price/sq.ft.)", "field_values": [200, 250], "fact_subtype_id": "ITV"}, {"field_name": "Real Property Value ($)", "field_values": [250000, 1000000], "fact_subtype_id": "RPV"}, {"field_name": "$ Change in: Replacement Cost", "field_values": [255112], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Replacement Cost", "field_values": [10000000], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Replacement Cost Building", "field_values": [76000222], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Total Area Sq. Ft.", "field_values": [45000], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Security camera in place [Y/N]", "field_values": ["no"], "fact_subtype_id": "SECURITY_CAMERAS"}, {"field_name": "Year Built", "field_values": ["not a year"], "fact_subtype_id": null}, {"field_name": "Electrical", "field_values": ["Yes", "No"], "fact_subtype_id": null}, {"field_name": "Year Built", "field_values": [null], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Protection Class", "field_values": ["P4", "P2"], "fact_subtype_id": "FIRE_PROTECTION_CLASS"}, {"field_name": "Protection Class Code", "field_values": ["1", "3", "2"], "fact_subtype_id": "ISO_FIRE_PROTECTION_CLASS"}, {"field_name": "PPC", "field_values": ["1", "3", "2"], "fact_subtype_id": "ISO_FIRE_PROTECTION_CLASS"}, {"field_name": "FEIN", "field_values": ["990222941"], "fact_subtype_id": "FEIN"}, {"field_name": "Smoke Detection", "field_values": ["Yes", "No"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Year built", "field_values": ["2010", "2011", "2012"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Effective Yr Blt", "field_values": [2007], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Building", "field_values": ["1", "2"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Buildings", "field_values": ["3", "4"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "# Buildings", "field_values": ["3", "4"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Building.1", "field_values": [15, 27, 29], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Num blds", "field_values": ["3", "4"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Structures Count", "field_values": ["3", "4"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Total Sales", "field_values": [10000000, 21370000], "fact_subtype_id": "TOTAL_SALES"}, {"field_name": "Total Revenue", "field_values": [10000000, 21370000], "fact_subtype_id": "TOTAL_SALES"}, {"field_name": "Number of Units", "field_values": [10, 20, 30], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apt Unit Count", "field_values": [119], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of Student occupied units?", "field_values": [0], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Loss of Rents", "field_values": [50000000], "fact_subtype_id": "LOSS_OF_RENT_INCOME"}, {"field_name": "Loss of Business Income", "field_values": [100000000], "fact_subtype_id": "LOSS_OF_INCOME"}, {"field_name": "Type of wiring (alum, cop, prot alum.)", "field_values": ["1 - Copper"], "fact_subtype_id": "WIRING"}, {"field_name": "Wiring", "field_values": ["240 3-phase"], "fact_subtype_id": "WIRING"}, {"field_name": "Business Income & Extra Expense", "field_values": ["5700000"], "fact_subtype_id": "BI_EE"}, {"field_name": "Business Income/Extra Expense: Limit", "field_values": ["5700000"], "fact_subtype_id": "TIV"}, {"field_name": "Wiring Type", "field_values": ["1 - Copper", "3 - Aluminum - Pigtailed", "2 - Copper"], "fact_subtype_id": "WIRING"}, {"field_name": "PERCENTAGE (%) OF BUILDING OCCUPIED", "field_values": [0.9, 0.8], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "OCCP", "field_values": ["37%", "80%"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "OCCP", "field_values": ["37", "80"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "OCCP", "field_values": ["37.0", "80.0"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "OCCP", "field_values": [37.0, 80.0], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "OCCP", "field_values": [37, 80], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "PROPERTY_DESCRIPTION", "field_values": [211, 215, 58], "fact_subtype_id": null}, {"field_name": "PROPERTY_DESCRIPTION", "field_values": [0.65, 0.64], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "Occupied SQFT", "field_values": [30300, 9279], "fact_subtype_id": "OCCUPIED_AREA"}, {"field_name": "Occupied Area", "field_values": [30300, 9279], "fact_subtype_id": "OCCUPIED_AREA"}, {"field_name": "BPP (Replacement Cost)", "field_values": [500000], "fact_subtype_id": "BPP"}, {"field_name": "BPP/Contents", "field_values": [500000], "fact_subtype_id": "BPP"}, {"field_name": "BPP / Contents Value", "field_values": [500000], "fact_subtype_id": "BPP"}, {"field_name": "Contents/BPP", "field_values": [500000], "fact_subtype_id": "BPP"}, {"field_name": "Contents BPP", "field_values": [500000], "fact_subtype_id": "BPP"}, {"field_name": "BPP Replacement Cost", "field_values": [500000], "fact_subtype_id": "BPP"}, {"field_name": "Content/BPP Values", "field_values": [500000], "fact_subtype_id": "BPP"}, {"field_name": "BPP (CONTENTS)", "field_values": [500000], "fact_subtype_id": "BPP"}, {"field_name": "$ Building", "field_values": [10000000], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building", "field_values": ["1_907_457", "3_213_769"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building.1", "field_values": ["2427120"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building", "field_values": ["83475", "1407945"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building", "field_values": ["0", "0", "0", "700_000", "900_000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building", "field_values": ["12_000_000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building", "field_values": ["6_800_000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building", "field_values": ["0", "0", "0", "0", "6800000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building", "field_values": [0, 0, 0, 0, 6800000], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building", "field_values": [0, 0, 0, 0, 6800000, 111111, 65432], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building.1", "field_values": [0, 0, 0, 0, 561600], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Blanket Buildings", "field_values": [0, 0, 0, 0, 2355078], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Blanket Buildings", "field_values": ["2355078"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "BLDG (36 UnitS)", "field_values": [0, 0, 0, 0, 2355078], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "BLDG (36 UnitS)", "field_values": ["2355078"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building & GL", "field_values": [0, 0, 0, 0, 561600], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building & GL", "field_values": ["3750000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building / I&B", "field_values": [0, 0, 0, 0, 561600], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building / I&B", "field_values": ["3750000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "12 Buildings", "field_values": [0, 0, 0, 0, 561600], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "12 Buildings", "field_values": ["3750000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "ISO Construction Type", "field_values": ["<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "ISO Construction Type", "field_values": ["Masonry non combustible"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "ISO Const", "field_values": [], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "ISO Construction", "field_values": [1, 2, 2, 3], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Const Type", "field_values": ["<PERSON>ame", "NC"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Const Type", "field_values": ["JM"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Const Type", "field_values": [1, 1, 2, 2], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Type of Construction", "field_values": ["Steel"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "BUILDING CONST Type", "field_values": ["Non-Combustible", "Jo<PERSON>"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Type of Constr", "field_values": ["MNC"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction", "field_values": ["Masonry Non-Combustible"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction", "field_values": ["Non Combustible"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction", "field_values": ["MNC", "FR", "MNC", "MNC", "MNC"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction class description", "field_values": ["Concrete block"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction class", "field_values": ["Wooden Frame w/Steel Exterior"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction class description", "field_values": ["<PERSON>"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction", "field_values": ["5", "5", "2"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Pool [Y/N]", "field_values": ["No"], "fact_subtype_id": "HAS_SWIMMING_POOL"}, {"field_name": "Pool", "field_values": [0, 1, 0], "fact_subtype_id": "HAS_SWIMMING_POOL"}, {"field_name": "Pool", "field_values": [0, 1], "fact_subtype_id": "HAS_SWIMMING_POOL"}, {"field_name": "Swimming Pool (unit)", "field_values": [81, 24, 1, 44], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Swimming Pools (other)", "field_values": [50], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Pool Num", "field_values": [0, 1, 2, 3], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "# of Swimming Pools", "field_values": [1, 2, null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Pools", "field_values": [0, 1, 2], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "sprinkler_percentage", "field_values": [0], "fact_subtype_id": "SPRINKLER_AREA_COVERAGE_PERCENT"}, {"field_name": "sprinkler_percentage", "field_values": ["0"], "fact_subtype_id": "SPRINKLER_AREA_COVERAGE_PERCENT"}, {"field_name": "Sprinklers", "field_values": [0, 100], "fact_subtype_id": "SPRINKLER_AREA_COVERAGE_PERCENT"}, {"field_name": "Sprinklered", "field_values": [0, 100, 50], "fact_subtype_id": "SPRINKLER_AREA_COVERAGE_PERCENT"}, {"field_name": "sprinklers", "field_values": ["y", "n", "y"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinklers", "field_values": [0, 1, 1], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinklers", "field_values": [0, 0, 0], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinklers", "field_values": ["YES", "NO"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Garage Sprinkler Y/N", "field_values": ["Yes", "No"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinklered (Y/N) ", "field_values": ["N"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "100% Sprinklered  Y or N", "field_values": ["Yes", "No"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "% Sprinklered", "field_values": ["Yes", "No"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinklers", "field_values": ["F", "F", "F"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinkler System", "field_values": ["No sprinkler heads"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "<PERSON><PERSON>", "field_values": ["Y", "N"], "fact_subtype_id": null}, {"field_name": "Fire Alarm", "field_values": ["Local & Central Station"], "fact_subtype_id": "FIRE_ALARM_TYPE"}, {"field_name": "Fire Alarm (Central Station or other)", "field_values": ["Central"], "fact_subtype_id": "FIRE_ALARM_TYPE"}, {"field_name": "Fire Alarm", "field_values": ["No", "Yes"], "fact_subtype_id": "HAS_FIRE_ALARM"}, {"field_name": "Fire Alarm Type", "field_values": ["Yes", "Central\nStation", "Local"], "fact_subtype_id": "FIRE_ALARM_TYPE"}, {"field_name": "Burglar Alarm", "field_values": ["B -Burglar"], "fact_subtype_id": "BURGLAR_ALARM_TYPE"}, {"field_name": "Central Station Fire Alarm System? (Yes / No)", "field_values": ["Yes"], "fact_subtype_id": "HAS_FIRE_ALARM"}, {"field_name": "Burglar Alarms If Yes - Type", "field_values": ["NO", "YES", "C"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Alarmed", "field_values": ["Yes"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Monitored Fire Heat Alarm", "field_values": ["Yes", "Yes"], "fact_subtype_id": "HAS_FIRE_ALARM"}, {"field_name": "Fire Alarm (Central Station or other)", "field_values": ["Yes", "Yes"], "fact_subtype_id": "HAS_FIRE_ALARM"}, {"field_name": "Smoke Alarms", "field_values": ["Yes", "No"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Smoke Detectors", "field_values": ["Wired", "Yes", "Pending"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Type of Smoke Detectors in Units?", "field_values": ["B/HW"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Smoke Detectors", "field_values": ["Yes", "Wired", "Pending"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "FIRE PROTECTION (Sprinklers & Smoke Detectors)", "field_values": ["Fire hydrants, hoses", "Fire extinguishers", "smoke detectors"], "fact_subtype_id": "FIRE_PROTECTION"}, {"field_name": "Type of Smoke Detectors", "field_values": ["Hardwired"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Heating Type", "field_values": ["Other"], "fact_subtype_id": "HEATING_TYPE"}, {"field_name": "Liquor License Holders", "field_values": ["WLS Beverage Company", "WLS Beverage Company"], "fact_subtype_id": "ALCOHOL_SERVED"}, {"field_name": "BI / Extra Expense 2024-2025", "field_values": ["0", "2000000", "0", "0", "0"], "fact_subtype_id": "BI_EE"}, {"field_name": "BI P/LIMIT ($)", "field_values": ["2000000.0"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "BI w/ Extra Expense and Rental Value", "field_values": ["525000", "4830950", "273980", "240000", "509155"], "fact_subtype_id": "BI_EE"}, {"field_name": "Business Income & EE Including Loss of Rents A", "field_values": ["200000.0"], "fact_subtype_id": "BI_EE"}, {"field_name": "BI / Extra Expense", "field_values": ["525000", null], "fact_subtype_id": "BI_EE"}, {"field_name": "Construction Description (provide further details on construction features)", "field_values": ["<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction", "field_values": ["<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Cons", "field_values": ["noncombust", "steel", "concrete block"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "CON", "field_values": ["JM", "JM", "JM", "JM"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description (provide further details on construction features)", "field_values": ["MNC"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description (provide further details on construction features)", "field_values": ["Frame (ISO 1)", "Frame (ISO 1)", "Frame (ISO 1)", "Frame (ISO 1)", "Frame (ISO 1)"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description (provide further details on construction features)", "field_values": ["FRAME"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description (provide further details on construction features)", "field_values": ["MNC", null], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Exterior Construction Type (Wood Shake NOT ALLOWED)", "field_values": ["Siding", null], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Quality", "field_values": ["1 - Certified Design", null], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description (ie Frame, JM, NC, MNC, Fire resistive, Modified Fire Resistive, etc)", "field_values": ["<PERSON>ame", null], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description (provide further details on construction features)", "field_values": ["<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Liquor License Holders", "field_values": ["WLS Beverage Company", "WLS Beverage Company"], "fact_subtype_id": "ALCOHOL_SERVED"}, {"field_name": "BI / Extra Expense 2024-2025", "field_values": ["0", "2000000", "0", "0", "0"], "fact_subtype_id": "BI_EE"}, {"field_name": "BI w/ Extra Expense and Rental Value", "field_values": ["5100.0"], "fact_subtype_id": "BI_EE"}, {"field_name": "BI & Extra exp", "field_values": ["480000", "525000", "240000", "240000", "273980"], "fact_subtype_id": "BI_EE"}, {"field_name": "Business Income & EE Including Loss of Rents A", "field_values": ["6000000.0", null], "fact_subtype_id": "BI_EE"}, {"field_name": "24-25 BI/EE", "field_values": ["1366666.**********"], "fact_subtype_id": "BI_EE"}, {"field_name": "BI & Extra Exp Value", "field_values": ["525000", "480000", "273980", "240000", "509155"], "fact_subtype_id": "BI_EE"}, {"field_name": "BI / Extra Expense", "field_values": ["70000.0"], "fact_subtype_id": "BI_EE"}, {"field_name": "2025-2026 Business Interruption & Extra Expense", "field_values": ["475761", "475761", "475761", "475761"], "fact_subtype_id": "BI_EE"}, {"field_name": "2025 BI Value", "field_values": ["731670.4791744242", "635517.8618810766", "296789.2318918852", "426600.***********", "284337.***********"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "2023 BI Reported", "field_values": ["2551000", "2148000", "848000", "1174000", "680000"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "BI/EE.1", "field_values": ["14016000", null], "fact_subtype_id": "BI_EE"}, {"field_name": "BI / EE Coverage Basis", "field_values": ["1/4 Mo-Limit", null], "fact_subtype_id": "BI_EE"}, {"field_name": "Business Income/ Extra Expense", "field_values": ["11811942.576", "838009.44", "4090284.169", "1955355.359", "239431.268"], "fact_subtype_id": "BI_EE"}, {"field_name": "Rent(BI?)", "field_values": ["5800000", "717528", "538146", "717528", "807219"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "BI & Extra exp value", "field_values": ["240887", "289013", "223605", "328499", "165496"], "fact_subtype_id": "BI_EE"}, {"field_name": "B/I EE", "field_values": ["12.0"], "fact_subtype_id": "BI_EE"}, {"field_name": "BI w/Extra Expense 18 mths", "field_values": [null], "fact_subtype_id": "BI_EE"}, {"field_name": "BI/Extra Expense", "field_values": ["1500000", "1000000", "500000"], "fact_subtype_id": "BI_EE"}, {"field_name": "Bi/EE", "field_values": [null], "fact_subtype_id": "BI_EE"}, {"field_name": "BI / Extra Expesne", "field_values": ["1600000"], "fact_subtype_id": "BI_EE"}, {"field_name": "BI with Extra Expense", "field_values": ["75000.0", null], "fact_subtype_id": "BI_EE"}, {"field_name": "BI & EXTRA EXP", "field_values": ["300000.0", "300000.0", "300000.0", "300000.0"], "fact_subtype_id": "BI_EE"}, {"field_name": "Biz Income", "field_values": ["942801", "1182138"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Business Income w/ EE", "field_values": ["500000.0", null], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "BI & Extra Expe Value", "field_values": ["11962708.67"], "fact_subtype_id": "BI_EE"}, {"field_name": "Contents (incl I&B, BPP, Inventory, AR, EDP) (Replacement $$)", "field_values": ["100000", "130000", "52020", "38217", "46211"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property", "field_values": [null], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property INCLUDING Stock", "field_values": ["250000.0"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property Incl. stock and in", "field_values": ["1825000.0"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property including stock a", "field_values": ["1500000.0"], "fact_subtype_id": "BPP"}, {"field_name": "Expiring - Business Personal Property", "field_values": ["246420", "81410"], "fact_subtype_id": "BPP"}, {"field_name": "Personal Property", "field_values": ["764726.0"], "fact_subtype_id": "BPP"}, {"field_name": "Personal Property  of Others", "field_values": ["3339.6935823398962", "3218.539281573289", "3296.9166863665528", "2487.814213186541", "2310.9373768757105"], "fact_subtype_id": "BPP"}, {"field_name": "2025-2026 Personal Property", "field_values": ["1200000", "600000", "3000", "Delete", "50000"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Prop", "field_values": ["164700.0"], "fact_subtype_id": "BPP"}, {"field_name": "EQSL (Bldg, BPP, BI)", "field_values": ["2537152.0", "3039296.0", "1321517.0", "1994121.0", "2121305.0"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property.1", "field_values": ["102749.4588", "149411.8128", "129991.733", "83870.691", "122356.848"], "fact_subtype_id": "BPP"}, {"field_name": "BUSINESS PERSONAL PROP", "field_values": ["10000", null], "fact_subtype_id": "BPP"}, {"field_name": "BPP Including computers", "field_values": ["300000.0"], "fact_subtype_id": "BPP"}, {"field_name": "Business personal property except stock", "field_values": ["321000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Scheduled Personal Property", "field_values": ["65000.0", "5389.0"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Proerty", "field_values": ["50000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Buiness Personal Property", "field_values": ["50000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property Coins. %", "field_values": ["0.8", "0.8", "0.8"], "fact_subtype_id": "BPP"}, {"field_name": "Expiring Business Pers. Prop.", "field_values": ["300000", "50000"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property including Stock.1", "field_values": ["900000", "200000", "900000"], "fact_subtype_id": "BPP"}, {"field_name": "Business personal property", "field_values": ["1640000.0"], "fact_subtype_id": "BPP"}, {"field_name": "Personal Property.1", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal proeprty", "field_values": ["110500"], "fact_subtype_id": "BPP"}, {"field_name": "Bus. Pers. Property", "field_values": ["1007500"], "fact_subtype_id": "BPP"}, {"field_name": "Gift Shop Business Personal Property", "field_values": ["15000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property Limit", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "BPP"}, {"field_name": "Bldg & BPP  Co- Ins.", "field_values": ["0.9", null], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property RC Value.1", "field_values": ["50000"], "fact_subtype_id": "BPP"}, {"field_name": "Guests' Personal Property", "field_values": ["5000.0"], "fact_subtype_id": "BPP"}, {"field_name": "BPP + Equip", "field_values": ["525000", "347000", "120000", "100000"], "fact_subtype_id": "BPP"}, {"field_name": "24-25 Business Personal Property including EDP", "field_values": ["1000000", null], "fact_subtype_id": "BPP"}, {"field_name": "Office/Warehouse - Business Personal Property", "field_values": ["50000"], "fact_subtype_id": "BPP"}, {"field_name": "2025-2026 Business Personal Property", "field_values": ["300000", "150000", "150000", null], "fact_subtype_id": "BPP"}, {"field_name": "2024 Business Personal Property", "field_values": ["3050000", "0", "3050000", "15000"], "fact_subtype_id": "BPP"}, {"field_name": "BPP (3)", "field_values": ["0", null], "fact_subtype_id": "BPP"}, {"field_name": "Personal Property of others", "field_values": ["10000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property Inc Stock", "field_values": ["231100.0"], "fact_subtype_id": "BPP"}, {"field_name": "BPP - Office Building BPP", "field_values": ["50000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Poprety", "field_values": [null], "fact_subtype_id": "BPP"}, {"field_name": "Total Blanket BPP (Includes EDP, Personal Property of Others, Inventory, Contents, TI&B and M&E)", "field_values": ["5034821.33", "372712.39", "10000", "987828", "1009360"], "fact_subtype_id": "BPP"}, {"field_name": "Personal Property Of Others", "field_values": ["200000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Pers prop", "field_values": ["550000", "405200", null], "fact_subtype_id": "BPP"}, {"field_name": "Bus Pers  Property", "field_values": ["1423095", "637708", "858133", "878707", "703525"], "fact_subtype_id": "BPP"}, {"field_name": "BPP", "field_values": ["25000.0", "25000.0"], "fact_subtype_id": "BPP"}, {"field_name": "BIZ PERS PROPERTY", "field_values": ["12978789", "15702633", "10663112", "19068335", "7849304"], "fact_subtype_id": "BPP"}, {"field_name": "Personal Proeprty", "field_values": ["8000", "5500", "1136", "16000", "30000"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property - Blanket", "field_values": ["54100.0", "54100.0", "21700.0", "54100.0"], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property Blanket", "field_values": ["32500.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property (Fuel)", "field_values": ["15000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "BPP including Stock", "field_values": ["0", "0", "0"], "fact_subtype_id": "BPP"}, {"field_name": "Bldgs and BPP Excluding Stock, ME and Computers", "field_values": ["0", "0", "0"], "fact_subtype_id": "BPP"}, {"field_name": "Building and BPP", "field_values": ["0", "0", "0"], "fact_subtype_id": "BPP"}, {"field_name": "Orig SOV Business Personal Property 2024", "field_values": ["26600", null], "fact_subtype_id": "BPP"}, {"field_name": "Bus. Personal Prop.", "field_values": ["18150000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Bus Personal Prop", "field_values": ["0"], "fact_subtype_id": "BPP"}, {"field_name": "Bus Personal Property included", "field_values": ["15000.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property & Truck Scales", "field_values": [null], "fact_subtype_id": "BPP"}, {"field_name": "Business Personal Property & Scales", "field_values": ["87130.0", null], "fact_subtype_id": "BPP"}, {"field_name": "Customers' Personal Property", "field_values": ["500000.0", "500000.0"], "fact_subtype_id": "BPP"}, {"field_name": "Updated Business Pers. Prop. Value", "field_values": ["950000"], "fact_subtype_id": "BPP"}, {"field_name": "2024-2025 Business Personal  Property/Stock", "field_values": ["5900000", "350000"], "fact_subtype_id": "BPP"}, {"field_name": "2025-2026 Business Personal  Property/Stock", "field_values": ["5900000", "350000"], "fact_subtype_id": "BPP"}, {"field_name": "2022-2023 BPP", "field_values": ["89824", "0", "0", "0", "0"], "fact_subtype_id": "BPP"}, {"field_name": "2023 - 2024 BPP", "field_values": ["89824", "0", "0", "0", "0"], "fact_subtype_id": "BPP"}, {"field_name": "Construction Description (provide further details on construction features)", "field_values": ["<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction", "field_values": ["<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Cons", "field_values": ["noncombust", "steel", "concrete block"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "CON", "field_values": ["JM", "JM", "JM", "JM", "JM"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description", "field_values": ["JM", "JM", "JM", "JM", "JM"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Contruction", "field_values": ["<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description:", "field_values": [" rigid steel frame, steel beams, brick fa√ßade  "], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Exterior Construction Type (Wood Shake NOT ALLOWED)", "field_values": ["Siding", "Siding", "Siding", "Siding", "Siding"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description (ie Frame, JM, NC, MNC, Fire resistive, Modified Fire Resistive, etc)", "field_values": ["<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "HEAT", "field_values": ["2014", "2014", "2014", "2014", "2014"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "Heating Update", "field_values": [null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "Heating Yr:", "field_values": ["2015", "2006", "2006", null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "HEATING", "field_values": ["2024", "2024", "2024", null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "Heating / Responsibility", "field_values": ["1996 U/O"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "Heat.", "field_values": ["2013", null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "Heat", "field_values": ["2012", "2000", "2010", "2015"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "HVAC Age", "field_values": ["2008"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "Yr HVAC", "field_values": ["2024"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "Heating Yr", "field_values": ["2011"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "HVAC Update", "field_values": ["2018 addition- installed"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_HEATING_YEAR"}, {"field_name": "PLUMB", "field_values": ["2014", "2014", "2014", "2014", "2014"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plumbing", "field_values": ["2000", "2000", "2000", "2000", "2000"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plumb Yr:", "field_values": ["2007", "2009", "2011"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plumbing Update", "field_values": [null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plunbing Updated", "field_values": ["2016", "2016", "2016", "2016", "2016"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plumbing Yr:", "field_values": ["2014", "2014", "2014", null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "New Plumbing", "field_values": ["2021", "2021", "2021", "2021", "2021"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "PLUMBING", "field_values": ["2024", "2024", "2024", null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plumbing / Reponsibility", "field_values": ["1996 plumbing "], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "plumbing", "field_values": ["2022", null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Orig SOV Plumbing Update", "field_values": ["2008", "2015", "2015", "2015", "2015"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plumbing/ HVAC", "field_values": ["2022", "Partial - 2011", "2012"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "<PERSON><PERSON><PERSON>", "field_values": ["2023", "2023"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plbg Update", "field_values": ["2005", "2000", "2001", "2001"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plumbing Age", "field_values": ["2018", "2018", "2018"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Yr Plumbing", "field_values": ["2024"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plumbing Yr", "field_values": ["2005"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "Plumbing Pipe", "field_values": ["2003", "1986", "2003"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"}, {"field_name": "<PERSON><PERSON><PERSON> covering last repl", "field_values": ["2017", "2017", "2017", "2017", "2017"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Roof Yr:", "field_values": ["2014", "2006", "2006", "2006", "2022"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Age of Roof", "field_values": ["2008", "2008", "2008", "2008", "2008"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Roof Update", "field_values": [null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "<PERSON><PERSON><PERSON> Covering last repl", "field_values": ["2017", "2005", "2021", "2023", "2018"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "<PERSON><PERSON><PERSON> covering last replaced", "field_values": ["2017", "2005", "2021", "2023", "2018"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Roofing Yr:", "field_values": ["2003", "2002", "2002", null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Roof Update Year", "field_values": ["2007", "2007", "2019", "2021"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "ROOFING IMPROVEMENT", "field_values": ["2016", "2011", null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Roof Year Fully Updated", "field_values": ["2008", "2015", "2015", "2014", "2016"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Rooofing Updated", "field_values": ["2018", "2018", "2018", "2018", "2018"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Roof <PERSON>", "field_values": ["2024", "2024"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "<PERSON><PERSON>", "field_values": ["2024"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "<PERSON><PERSON><PERSON> Covering Last Repl", "field_values": ["2008", "2014", "2010", "2014", "2014"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Roofing Improvement", "field_values": ["1985", "1990"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "YR OF ROOF", "field_values": ["2017", "2005", "2018", "2017", "2007"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Year of last full roof replacement", "field_values": ["2017", "2017", "2017", "2017", "2017"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "<PERSON><PERSON>r", "field_values": ["2018"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Year Roof covering last fully replaced", "field_values": ["2019", "2019", "2019", "2019", "2019"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_ROOFING_YEAR"}, {"field_name": "Wiring", "field_values": ["As needed", "As needed"], "fact_subtype_id": "WIRING"}, {"field_name": "Elect Year", "field_values": [null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Wiring Yr:", "field_values": ["2007", "2009", "2011"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Wiring Update", "field_values": [null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Electrical Updated", "field_values": ["2023"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "WIRING", "field_values": ["2012", null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Elect  Year", "field_values": [null], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Electrical - Copper or Aluminium", "field_values": ["1995", "1995", "1995", "1995", "1995"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Wiring Update Year", "field_values": ["2007", "2007", "2019", "2021"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Orig SOV Wiring Update", "field_values": ["2008", "2015", "2015", "2015", "2015"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Electrical Age", "field_values": ["2000", "2000"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Yr Electrical", "field_values": ["2024"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Wiring Yr", "field_values": ["2005"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Year Wiring Updated", "field_values": ["2007", "2007", "2009", "2009", "2010-2012"], "fact_subtype_id": "BUILDING_IMPROVEMENTS_WIRING_YEAR"}, {"field_name": "Basement Square Footage (total)", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Garaged Sq Ft Above Ground", "field_values": ["1904", "1904", "3640", "3808", "1904"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Livable/heated Sq Ft", "field_values": ["7418", "7418", "14662", "14836", "7418"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Garaged Sq Ft Below Ground", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Sq Ft", "field_values": ["11836", "4033", "3414", "4189", "100"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Basement / Storage Square Footage", "field_values": ["7006", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Residential  Square Footage", "field_values": ["1200", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Residential Square Footage", "field_values": ["350911", "253099"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Clubhouse Community Bldg Square Footage", "field_values": [null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Office Square Footage", "field_values": ["2500", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Clubhouse / Community Bldg Square Footage", "field_values": ["7006", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Gross Building Square Feet", "field_values": ["6165.510204081633", "6144.326530612245", "5098.989795918367", "6165.510204081633", "10765.295918367347"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Square Footage.1", "field_values": ["40743", "60823", "95600", "48384", "48384"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Building Sq Ft", "field_values": ["15686", "15371", "15246", "3006", "3006"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "TOTAL Gross Square Footage", "field_values": ["312000", "191,612.9 SF"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Retail Square Footage", "field_values": ["1558", "7788.2"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "BASEMENT SQUARE FOOTAGE", "field_values": ["3023", "3023", "3023", "3023", "3023"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Gross Building Square Footage", "field_values": ["76000", "43000", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Residential Sqft", "field_values": ["211703"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Parking Square Footage", "field_values": [null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "TOTAL  SQARE FOOTAGE", "field_values": ["130049", "124700", "1200", "1800", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Building Square Footage", "field_values": ["441423", "287299"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "LRO Square footalge", "field_values": ["1500", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total  Building  S. F.", "field_values": [null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Parking sq/ft", "field_values": ["85000", "105000", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Rentable sq/ft", "field_values": ["19246", "10856", "6500", "3750", "14700"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Basement Square Footage", "field_values": ["8927"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Clubhouse Square Footage", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Garage Square Footage", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Building Square Footage", "field_values": ["3000", "480", "2100", "2100", "2400"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total SF", "field_values": ["288095"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Parking Square footage", "field_values": ["112500"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Livable Square Footage", "field_values": ["14512", "14512", "8904", "14512", "13032"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "LRO Square Footage", "field_values": [null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Habitational SqFt", "field_values": ["381810", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Parking SqFt", "field_values": ["201343"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Sq Ft.1", "field_values": ["431000", "20000", "336000", "2650", "2775"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "SF", "field_values": ["100677"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Square Feet", "field_values": ["30000", "9270", "2000", "15000"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total sq./ft.", "field_values": ["93907"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Orig SOV Square Footage", "field_values": ["1300", "1217", "1000", "1932", "1334"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Bldg Sq. Ft.", "field_values": ["0", "0", "0", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Mfg. Sq. Ft.", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Office Sq. Ft.", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Approximate Sq.Fts", "field_values": ["5000", "30000", "16500", "13000", "15000"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "S/F", "field_values": ["159040", "60504", "151797", "122345", "567109"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Retail Square Feet", "field_values": ["1984"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Building Square Feet", "field_values": ["106800", "88356", "106800", "3600", "119279"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "OFFICE SQ.FT.", "field_values": ["0", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "WAREHOUSE SQ.FT.", "field_values": ["0", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Square Footage Parking", "field_values": ["83000", "0", "0", "0", "0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Square Footage Building", "field_values": ["200000", "596553", "680082", "388580", "569779"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Ind/Comm/Ret SqFt", "field_values": ["0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Residential Sq Ft", "field_values": ["61455", "61455", "3084"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "SQUARE FOOTAGE VERITAS", "field_values": ["84040", "84040", "84040", "44449", "15540"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Residential sq. ft.   Residential gross sq. ft. (excluding garage)", "field_values": ["50000", "67599", "16632", "36300", "53270"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Residential SQFT", "field_values": ["92530", "52000", "25080", "32272", "77525"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Rentable Square Footage", "field_values": ["94106", "72561", "0", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Building Common Area Sq. Ft.", "field_values": ["0", "0", "0", "18315", "0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Sq. Ft. Residential", "field_values": ["16408", "91254", "73347", "16560", "82300"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Building Area - SF", "field_values": ["798000"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Building SQ Ft", "field_values": ["10984", "10583", "13691", "14078", "12182"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Parking Square Feet", "field_values": ["88498", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Sq", "field_values": ["t120,410", "10800", "9630", "9440", "9630"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Sq.1", "field_values": ["118155", "10800", "9630", "9440", "9630"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Bldg Square Footage", "field_values": ["120000", "3000", "72383"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Rentable SQ FT", "field_values": ["23084", "27340", "36738", "23610", "23610"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Bldg Total Sq'", "field_values": ["110824", "71391", "71391", "110694", "110694"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Sq Ft", "field_values": ["130", "130", "130", "130", "130"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Bldg SqFtg", "field_values": ["16420", "7500", "8208", "16300", "8636"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Parking Lot Sq Ft", "field_values": ["3800", "5000", "70000", "57000", "39000"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Sq Footage Parking", "field_values": ["125421", "100000"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total  Building Square Feet (less parking lot, excl NNN Sq Ft):", "field_values": ["108138"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Acres", "field_values": ["0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "TOTAL SQUARE FEET OCCUPIED", "field_values": ["104964", "117537", "28063", "29000", "20000"], "fact_subtype_id": "OCCUPIED_AREA"}, {"field_name": "Sq Ft.4", "field_values": ["0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Sq Ft.2", "field_values": ["0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Sq Ft.3", "field_values": ["0"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Square Footage Office", "field_values": ["5466"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "<PERSON><PERSON>s", "field_values": [null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Bldg. Sq. Ft.", "field_values": ["12240", "9720"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Above Ground Sqft", "field_values": ["10547", "10637", "8450", "8759", "13093"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Garage SF", "field_values": ["950", "906", "906", "906", "906"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "LIVING SF", "field_values": ["3962", "4084", "4084", "4096", "4084"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Total Building Gross Square Footage", "field_values": ["264000"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "SQ FT.1", "field_values": ["431800", "257510", "1445921"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "SQ FT Buildings", "field_values": ["14656", "3372", "31304"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "SQ FT total", "field_values": ["1872306", "260882", "2623154", "1737748", "392994"], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Contruction Type", "field_values": ["Non-Combustible Tilt-up conc and steel deck roof ISO-3", "Non-Combustible ISO-3", "Non-Combustible Tilt-up conc and steel deck roof ISO-3", null], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Bldg Desc", "field_values": ["Manufacturing & Office", "So. Warehouse", "Mfg and office", "Pipe Steel Warehouse", "Warehouse"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Building Shape", "field_values": ["Irregular", "Irregular"], "fact_subtype_id": "BUILDING_TYPE"}, {"field_name": "Building Type (stories)", "field_values": ["Irregular", "multi story"], "fact_subtype_id": "BUILDING_TYPE"}, {"field_name": "Building Details", "field_values": ["Apt", "Apt", "Apt", "Apt", "Apt"], "fact_subtype_id": "BUILDING_TYPE"}, {"field_name": "Bldg Description", "field_values": ["Office", "Office & Light Fabrication", "Dwelling", "Office", "Office & Warehouse"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Valuation", "field_values": ["ACV", "RC", "RC", "RC", "ACV"], "fact_subtype_id": "BUILDING_VALUATION_METHOD"}, {"field_name": "Valuation (RC, ACV)", "field_values": ["RC", "RC", "RC"], "fact_subtype_id": "BUILDING_VALUATION_METHOD"}, {"field_name": "Building Valuation", "field_values": ["RC"], "fact_subtype_id": "BUILDING_VALUATION_METHOD"}, {"field_name": "2025-2026  Building", "field_values": ["20000000", "300000", "150000", "3900000", "5200000"], "fact_subtype_id": null}, {"field_name": "24-25 Building Values (100% of ITV $)", "field_values": ["20000000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Insured Bldg. value", "field_values": ["20000000", "300000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Bldg Cov", "field_values": ["544400", "544400", "544400", "2816800", "4997600"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Current 2023 Building Values", "field_values": ["2958000", "2550000", "100000", "150000", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Values 2025", "field_values": ["43035298.8"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building TIV", "field_values": ["6348500", "1275000", "425000", "900000", "425000"], "fact_subtype_id": "TIV"}, {"field_name": "Garage Building Value", "field_values": ["2934720", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "INCREASED BUILDING VALUES $185", "field_values": ["21551760", "20720000", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Blanket Building", "field_values": ["32280000.0"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Bldg.1", "field_values": ["2850000", "2105300", "3016870", "3158000", "3158000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Current Building", "field_values": ["1805000", "10125772", "2917107", "28828725"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Travelers Building", "field_values": ["1922632", "15657777", "4173380", "40936313"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Bldg Limit", "field_values": ["5917875", "6939875", "17474750", "8277000", "6219000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building - PRIMARY", "field_values": ["5000000.0"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Buiding Limit", "field_values": ["1224506", "735930", "722552", "693685", "668667"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "building amount", "field_values": ["1510850", "1510850", "1510850", "3067000", "3067000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building  Value  (RATING)", "field_values": ["74500000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Avg. Building Value", "field_values": ["74500000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "2023-2024 Building", "field_values": ["13266000", "8207496", "11880000", "11880000"], "fact_subtype_id": null}, {"field_name": "2025-2026 Building", "field_values": ["13266000", "8207496", "11880000", "11880000"], "fact_subtype_id": null}, {"field_name": "2025 Building Value", "field_values": ["2471000", "1703910", "2185430", "2929550"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Bldg $", "field_values": ["3306058", "3415521", "1405431", "3175704", "1526640"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Bldg Value #", "field_values": ["1600000", "1600000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Value 2024-2025", "field_values": ["7205000", "870000", "174000", "1200000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Projected 2024 Total Building Value", "field_values": ["780000", "23100000", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Blvd Val", "field_values": ["14000000", "832000", "250000", "0", "52000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Values.1", "field_values": ["40000000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building(s) Real Property Value ($)", "field_values": ["1001250", "1001250", "1991250", "1991250", "1001250"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 2", "field_values": ["16468946.0"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building: Value_1", "field_values": ["699540", "693360", "1278407", "253292"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Total", "field_values": ["204000", "658000", "25500", "25500", "204000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "BUILDGING", "field_values": ["3014760", "3014760", "3548160", "3014760", "3548160"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Value:", "field_values": ["488800", "425600", "425600", "488800", "488800"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building limit", "field_values": ["5799607", "3943287", "3858128"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Value (5)", "field_values": ["3", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building - US 2024-2025", "field_values": ["21500000", "10936696", "0", "0", "0"], "fact_subtype_id": null}, {"field_name": "Renewal Building", "field_values": ["2626278", "5768649", "4293263", "7239406", "4515000"], "fact_subtype_id": null}, {"field_name": "Building Value", "field_values": ["1735584.0", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Buiding Value", "field_values": ["2097932.0", "4159547.0"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Value wanted by Travelers:", "field_values": ["822927.05", "1302383.5", "873390.1", "463938.2", "1251575.6"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 8", "field_values": [null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 6", "field_values": [null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 5", "field_values": ["5980290.0"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 7", "field_values": [null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 10", "field_values": [null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 3", "field_values": ["5503446.0", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 11", "field_values": [null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 4", "field_values": ["5503446.0", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building 9", "field_values": [null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "2024 Bldg Limits", "field_values": ["6836900", "5316800", "6680500", "6680500", "6608500"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Value - 2021 (A)", "field_values": ["10341000", "1131000", "20801000", "2577000", "956000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Value - 2023 (A)", "field_values": ["11075211", "1211301", "22277871", "2759967", "1023876"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Value - 2020 (A)", "field_values": ["10389449", "1177335", "22081609", "2076457", "860912"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Building Amount", "field_values": ["7000000", "4200000", "14000000", "750000", "3630950"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Orig SOV Building Value 2024", "field_values": ["247300", "1068400", "1068400", "1068400", "1068400"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "BUILDING #7", "field_values": [null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "BUILDING #32", "field_values": [null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "BUILDING #10", "field_values": ["5051", "5052", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "BUILDING #27", "field_values": ["5065"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "BUILDING #24", "field_values": ["5020", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Structure Value", "field_values": ["75000", "75000", "75000", "50000", "125000"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "2024 Phly Building Values", "field_values": ["5255913", "2897827", null], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "2024 Building", "field_values": ["5075000", "2344400", "2835300", "2344400", "2344400"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "2025-2026 Bldg Value", "field_values": ["8091721", "763263"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "2024-2025 Bldg Value", "field_values": ["8091721", "763263"], "fact_subtype_id": "BUILDING_VALUE"}, {"field_name": "Burglary Score", "field_values": ["C", "B", "C", "B", "D"], "fact_subtype_id": "BURGLARY_GRADE"}, {"field_name": "Alarm", "field_values": ["Central", "Central", "Central", "Central", "Central"], "fact_subtype_id": "BURGLAR_ALARM_TYPE"}, {"field_name": "ALARM:  CS/LOCAL", "field_values": ["Local"], "fact_subtype_id": "BURGLAR_ALARM_TYPE"}, {"field_name": "2025-2026 Business Income", "field_values": ["1000000", "22574000", "EXCLUDED", "Delete", null], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Business Income.1", "field_values": ["Blanket", "56000", "56000", "56000", "56000"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Annual Gross Business Income", "field_values": ["1000000", "1000000", "1000000", "1000000", "1000000"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Blanket BI", "field_values": ["200000", "325000", "650000", "600000", "250000"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Included in BI", "field_values": ["195752", "189630", "179591", "4455002"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Expiring BI", "field_values": ["131580", "335630", "387674", "471000", "369000"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Renewal BI", "field_values": ["139680", "370038", "428338", "507000", "460056"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "BI Val", "field_values": ["416000", "416000", "0", "0", "0"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "BI TIV ($)", "field_values": ["1000000"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "BI 2024 (Estimate Only Based on Normal ROR and TTD Revenue)", "field_values": ["453653.1", "297973.***********", "231549.***********", "319879.0899999999", null], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "BIZ INCOME", "field_values": ["5000000", null], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Orig SOV Business Income", "field_values": ["79166", "79166", "79166", "79166"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Annualized Rents/ Business Income", "field_values": ["1238982.8205600001", "2123687.808975", "1103732.257284", "1987873.53087", "4428886.413269999"], "fact_subtype_id": "BUSINESS_INCOME"}, {"field_name": "Liability Carrier:", "field_values": ["written under Mill Paper Box", "written under Mill Paper Box", "Maxum Indemnity #BDG-3080813-01 3/31/24-3/31/25", "Maxum Indemnity #BDG-3080813-01 3/31/24-3/31/25", "POME #041653 6/20/24-6/20/25"], "fact_subtype_id": "CARRIER_OPERATION"}, {"field_name": "Commercial LRO- Sq.Ft.", "field_values": ["0", "0", null], "fact_subtype_id": "COMMERCIAL_AREA_SIZE"}, {"field_name": "Approx.SQ", "field_values": ["70629", null], "fact_subtype_id": "BUILDING_SIZE"}, {"field_name": "Building_Class", "field_values": ["Masonry Joist", "Masonry Joist", "Masonry Joist", "Masonry Joist", "Masonry Joist"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "CONST TYPE", "field_values": ["Non-Combust", "Non-Combust", "Non-Combust", "Non-Combust", "Non-Combust"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "ISO description", "field_values": ["Fire Resistive"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "FRAME/MAS", "field_values": ["<PERSON>ame", null], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Exterior Construction Type", "field_values": ["Cement Siding", "Cement Siding", "Cement Siding", "Cement Siding", "Cement Siding"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Type of Construction", "field_values": ["MNC", "MNC", "MNC", "MNC", "MNC"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction type If Other", "field_values": ["Non Combustible", "Non Combustible", "Non Combustible", "Non Combustible", "Non Combustible"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Const. Class", "field_values": ["Jo<PERSON>", "Jo<PERSON>", "Jo<PERSON>", "Jo<PERSON>", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Building Class", "field_values": ["<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Orig SOV Construction Type", "field_values": ["Modified F/R", "Modified F/R", "Modified F/R", "MNC", null], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Const. Type", "field_values": ["Frame <PERSON>", "MNC", "Fire Resistive", "<PERSON>ame", "JM"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "ISO Const Code", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "ISO Construction Class", "field_values": [null], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Type 1 Desc", "field_values": ["Reinforced Masonry", "Reinforced Masonry", "Reinforced Masonry", "Reinforced Masonry", "Reinforced Masonry"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Type                 (ISO Classification)", "field_values": ["JM", "JM"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Contructon Type", "field_values": ["<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "CNST TYPE", "field_values": ["Fire Resistive"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Description (Walls & Roof)", "field_values": ["<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame", "<PERSON>ame"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Cons. Type", "field_values": ["MNC", "MNC", "MNC", "MNC", "MNC"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "ISO Type of Construction", "field_values": ["<PERSON>ame", "<PERSON>ame", "Fire Resisitve     ISO 6"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Construction Details", "field_values": ["The building is a conventional wood-and-steel-framed structure with steel columns,concrete tilt-up walls and wood beams.", "Concrete tilt-up", "The building foundation generally consists of pre-stressed precast reinforced concrete pilesnclustered in groups below concrete pile caps which are interconnected by concrete gradenbeams. The superstructure consists of wide-flanged steel frame construction with pre-nNorthridge type moment-frame connections at the perimeter serving as the primary lateralnforce-resisting system.", "Building exterior is comprised of painted concrete tilt-up wall panels", "Concrete tilt-up wall panels"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "AIR Construction Type", "field_values": ["Steel - Steel", "Steel - Steel", "Steel - Steel", "Steel - Steel"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Arch Construction Type", "field_values": ["Building-NC", "Building-NC", "Building-NC", "Building-NC"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "ROOF CONSTRUCTION", "field_values": ["COMPOSITION SHINGLE", "COMPOSITION SHINGLE"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Construction Class", "field_values": ["<PERSON>ame", "D", "D", "D", "D"], "fact_subtype_id": "CONSTRUCTION_CLASS"}, {"field_name": "Contents Value (1)", "field_values": ["2380000", "550000", "6312000", "15860000"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents  Value  (RATING)", "field_values": ["50000"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "2025-2026 Contents", "field_values": ["700000", "250000", "100000", "100000"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Machinery & Equipment", "field_values": ["2019664", "1232012", "1305899", "291236", "320000"], "fact_subtype_id": "EQUIPMENT_LIMIT"}, {"field_name": "M&E", "field_values": ["2019664", "1232012", "1305899", "291236", "320000"], "fact_subtype_id": "EQUIPMENT_LIMIT"}, {"field_name": "Equipment", "field_values": ["560000", "5300000", null], "fact_subtype_id": "EQUIPMENT_LIMIT"}, {"field_name": "IIVENTORY CONTENTS", "field_values": ["4200000", "8500000", null], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents Per Room", "field_values": ["0", "0", "0", "0", "18086.202898550724"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Machinery and Equipment (RC)", "field_values": ["18086"], "fact_subtype_id": "EQUIPMENT_LIMIT"}, {"field_name": "Computer Equipment", "field_values": ["Column22", "Column22", "474000", null], "fact_subtype_id": null}, {"field_name": "Contents Including Inventory", "field_values": ["1500000", "4714500", "2428300", "3494500", "5702000"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents Towmotors/ Warehouse", "field_values": ["0", "205000", "112000", "202000", "325000"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents Office Equip", "field_values": ["1500000", "182000", "67300", "110000", "175000"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents Value - 2023 (B)", "field_values": ["1427643", "69615", "1871037", "21420", "111384"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents Value - 2021 (B)", "field_values": ["1333000", "65000", "1747000", "20000", "104000"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents Value - 2024 (B)", "field_values": ["1534716.2249999999", "74836.125", "2011364.775", "23026.5", "119737.79999999999"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents Value - 2022 (B)", "field_values": ["1359660", "66300", "1781940", "20400", "106080"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents Value - 2020 (B)", "field_values": ["1641822", "85972", "2224520", "22687", "117017"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Contents Value - 2019 (B)", "field_values": ["1641822", "85972", "2224520", "22687", "117017"], "fact_subtype_id": "CONTENT_VALUE"}, {"field_name": "Machinery and Equipment (Generator)", "field_values": [null], "fact_subtype_id": "EQUIPMENT_LIMIT"}, {"field_name": "Main-tenance Contract", "field_values": [null], "fact_subtype_id": "CONTRACTOR_AGREEMENT_DESCRIPTION"}, {"field_name": "Dollar per Square Foot", "field_values": ["228", "245", "215", "215", "358"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Cost  Per Sq", "field_values": ["130", "130", "130", "130", null], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Cost per sq ft", "field_values": ["131.09320699708454", "103.79964466639457", "118.34906152241919", "104", "100"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "DOLLAR / SQ FT", "field_values": ["145"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "$ per Sq Ft wanted by Travelers:", "field_values": ["114.95", "114.95", "114.95", "114.95", "114.95"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "LIVING PRICE PER SQ FT", "field_values": ["346.36", "346.36", "346.36", "346.36", "346.36"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Cost Per Square", "field_values": ["250", "250", "200", "250", "250"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Cost/SQ", "field_values": ["154", "152", "$125, $170", "172", "132"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Renewal Value per Sq Ft", "field_values": ["237.64", "237.64", "237.64", "237.64", "237.64"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "2023-2024 $/sqft", "field_values": ["0", null], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "2022-2023 $/sqft", "field_values": [null], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Renewal  Cost Per. Sq. Ft.", "field_values": ["0", "0", null], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "psf", "field_values": ["113", "113", "113", "113", "113"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Value PSF", "field_values": ["148.75904195466967", "210.98732806036213", "144.50704094580792", "156.9042985664653", "96.12100512320077"], "fact_subtype_id": "COST_PER_SQ_FT"}, {"field_name": "Distance to nearest coastal, body of Water (miles)", "field_values": ["4 miles", "4", "4", "4", "4"], "fact_subtype_id": "DISTANCE_TO_BODY_OF_WATER"}, {"field_name": "Distance to Fire Station (16)", "field_values": ["0.98", null], "fact_subtype_id": "DISTANCE_TO_FIRE_STATION"}, {"field_name": "BDATE", "field_values": ["1971-10-01 00:00:00", "1988-01-20 00:00:00", "1977-11-20 00:00:00", "1986-12-18 00:00:00", "1997-02-11 00:00:00"], "fact_subtype_id": "DRIVER_DATE_OF_BIRTH"}, {"field_name": "Date Of Birth", "field_values": ["01/30/XXXX", "01/30/XXXX", "11/03/XXXX", "11/03/XXXX"], "fact_subtype_id": "DRIVER_DATE_OF_BIRTH"}, {"field_name": "Driver Date of Birth", "field_values": ["12/31/1981", "04/08/1970", "01/13/2002", "12/22/2002", "12/18/1987"], "fact_subtype_id": "DRIVER_DATE_OF_BIRTH"}, {"field_name": "DOB.1", "field_values": ["1977-11-19 00:00:00", "1964-11-05 00:00:00", "1969-10-02 00:00:00", "1974-03-03 00:00:00", "1993-12-16 00:00:00"], "fact_subtype_id": "DRIVER_DATE_OF_BIRTH"}, {"field_name": "Effective", "field_values": ["2017-09-25 00:00:00", "2017-03-30 00:00:00", "2024-06-13 00:00:00", "2024-07-02 00:00:00", "2023-08-29 00:00:00"], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "Job Rehire Date", "field_values": ["2022-08-29 00:00:00", "2012-03-05 00:00:00", null], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "HireDate", "field_values": ["06182007", "06182007", "10182004"], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "Column 5", "field_values": ["2016-08-26 00:00:00", "2013-08-16 00:00:00", "2007-07-30 00:00:00", "2016-02-29 00:00:00", "2020-11-16 00:00:00"], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "Added", "field_values": [null], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "Original Hire Date", "field_values": ["2023-10-02 00:00:00", "1993-08-16 00:00:00", "2022-11-01 00:00:00", "2014-03-17 00:00:00", "2007-06-01 00:00:00"], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "HIRED", "field_values": ["2023-08-07 00:00:00", "2023-03-13 00:00:00", "2023-06-01 00:00:00", "2023-08-07 00:00:00", "2023-12-19 00:00:00"], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "<PERSON><PERSON>", "field_values": ["6/5/2023", "5/1/2023", "12/30/2023", "4/1/2024", "4/12/2021"], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "Seniority Date", "field_values": ["2001-04-11 00:00:00", "2022-10-10 00:00:00", "2019-05-06 00:00:00", "2023-10-02 00:00:00", "2022-01-10 00:00:00"], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "Hire Date.1", "field_values": ["2023-05-08 00:00:00", "2016-03-09 00:00:00", "2024-06-25 00:00:00", "2024-06-25 00:00:00", "2024-09-16 00:00:00"], "fact_subtype_id": "DRIVER_DATE_OF_HIRE"}, {"field_name": "Department - Name - Current", "field_values": ["Service", "Service", "New Vehicle Sales - Comm", "Parts", "New Vehicle Sales - Comm"], "fact_subtype_id": "DRIVER_DEPARTMENT"}, {"field_name": "Department - Current", "field_values": ["1 - Administrative", "1 - Administrative", "1 - Administrative", "1 - Administrative", "1 - Administrative"], "fact_subtype_id": "DRIVER_DEPARTMENT"}, {"field_name": "Department Name", "field_values": ["Rhode Island Hourly", "Coastal Ports- Hourly", "Coastal Ports- Hourly", "Coastal Ports- Hourly", "Coastal Ports- Hourly"], "fact_subtype_id": "DRIVER_DEPARTMENT"}, {"field_name": "<PERSON>. Name", "field_values": ["ENRICO", "<PERSON><PERSON><PERSON>", "Luz", "<PERSON>", "JOSE"], "fact_subtype_id": "DRIVER_FIRST_NAME"}, {"field_name": "<PERSON><PERSON> Name", "field_values": ["ACKER", "<PERSON>ston", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ARENIVAR-RODRIGUEZ"], "fact_subtype_id": "DRIVER_LAST_NAME"}, {"field_name": "Surname", "field_values": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "fact_subtype_id": "DRIVER_LAST_NAME"}, {"field_name": "RATING CLASS", "field_values": ["8380", "8380", "8810", "8380", "8380"], "fact_subtype_id": "DRIVER_LICENSE_CLASS"}, {"field_name": "License", "field_values": ["REGULAR-D", "CDL-B", "MED CARD", "CDL-A", "CDL-A"], "fact_subtype_id": "DRIVER_LICENSE_CLASS"}, {"field_name": "Lic. Class", "field_values": ["B", "B", "D", "A", "A"], "fact_subtype_id": "DRIVER_LICENSE_CLASS"}, {"field_name": "Class code A,B,C,D", "field_values": ["A", "B", "C", "C", "D"], "fact_subtype_id": "DRIVER_LICENSE_CLASS"}, {"field_name": "DL EXP", "field_values": ["2028-03-10 00:00:00", "2026-12-28 00:00:00", "2026-07-14 00:00:00", "2025-04-04 00:00:00", "2025-11-22 00:00:00"], "fact_subtype_id": "DRIVER_LICENSE_EXPIRATION_DATE"}, {"field_name": "EXP Drivers License", "field_values": ["2028-03-08 00:00:00", "2030-12-01 00:00:00", "2028-01-11 00:00:00", "2027-06-04 00:00:00", "2026-03-11 00:00:00"], "fact_subtype_id": "DRIVER_LICENSE_EXPIRATION_DATE"}, {"field_name": "DL Expire", "field_values": ["2025-12-15 00:00:00", "2024-08-11 00:00:00", "2028-01-22 00:00:00", "2025-09-10 00:00:00", "2027-05-15 00:00:00"], "fact_subtype_id": "DRIVER_LICENSE_EXPIRATION_DATE"}, {"field_name": "Lic Expire", "field_values": ["2027-01-14 00:00:00", "2024-12-30 00:00:00", "2025-07-02 00:00:00", "2027-05-03 00:00:00", "2027-01-05 00:00:00"], "fact_subtype_id": "DRIVER_LICENSE_EXPIRATION_DATE"}, {"field_name": "LicExpDate", "field_values": ["45939", "11330", null], "fact_subtype_id": "DRIVER_LICENSE_EXPIRATION_DATE"}, {"field_name": "License Expiry", "field_values": ["2027-12-10 00:00:00", "2028-06-21 00:00:00", "2027-03-09 00:00:00", "2028-06-06 00:00:00", "2025-04-13 00:00:00"], "fact_subtype_id": "DRIVER_LICENSE_EXPIRATION_DATE"}, {"field_name": "Expiration.1", "field_values": ["2025-12-21 00:00:00", "2025-04-26 00:00:00", "2025-12-29 00:00:00", "2024-11-27 00:00:00", "2024-12-08 00:00:00"], "fact_subtype_id": "DRIVER_LICENSE_EXPIRATION_DATE"}, {"field_name": "Driver's icense Number", "field_values": ["A436-3577-0426-05"], "fact_subtype_id": "DRIVER_LICENSE_NUMBER"}, {"field_name": "Licensed", "field_values": ["Column3", "MT", "MT", "MT", "MO"], "fact_subtype_id": "DRIVER_LICENSE_STATE"}, {"field_name": "ST", "field_values": ["OH", "OH", "OH", "OH", "OH"], "fact_subtype_id": "DRIVER_LICENSE_STATE"}, {"field_name": "State/Province", "field_values": [null], "fact_subtype_id": "DRIVER_LICENSE_STATE"}, {"field_name": "List B Issuing Authority", "field_values": ["RI", "ME", "ME", "ME", "ME"], "fact_subtype_id": "DRIVER_LICENSE_STATE"}, {"field_name": "State", "field_values": ["MN", "MN", "MN", "MN", "MN"], "fact_subtype_id": "DRIVER_LICENSE_STATE"}, {"field_name": "State Issued", "field_values": ["CT", "CT", "FL", "FL", "CT"], "fact_subtype_id": "DRIVER_LICENSE_STATE"}, {"field_name": "Issued By", "field_values": ["SC", "UT", "NM", "WY", "TX"], "fact_subtype_id": "DRIVER_LICENSE_STATE"}, {"field_name": "DL State.1", "field_values": ["NC", "IN", "TX", "TX", "TN"], "fact_subtype_id": "DRIVER_LICENSE_STATE"}, {"field_name": "MN", "field_values": ["MN", "IL", "NV", "MN", "NY"], "fact_subtype_id": "DRIVER_LICENSE_STATE"}, {"field_name": "Employee Loc", "field_values": ["PLQ", "PLQ", "PLQ", "PLQ", "PLQ"], "fact_subtype_id": "DRIVER_LOCATION"}, {"field_name": "Branch", "field_values": ["Augusta Branch", "Augusta Branch", "Augusta Branch", "Augusta Branch", "Augusta Branch"], "fact_subtype_id": "DRIVER_LOCATION"}, {"field_name": "Location of Driver", "field_values": ["HI&S Newburgh, NY", "HI&S Newburgh, NY", "HI&S Newburgh, NY", "Hampton Inn Harriman", "Hampton Inn Harriman"], "fact_subtype_id": "DRIVER_LOCATION"}, {"field_name": "MAR STAT", "field_values": ["M"], "fact_subtype_id": "DRIVER_MARTIAL_STATUS"}, {"field_name": "*MAR STAT", "field_values": ["Mn"], "fact_subtype_id": "DRIVER_MARTIAL_STATUS"}, {"field_name": "MI", "field_values": ["P", "N", null], "fact_subtype_id": "DRIVER_MIDDLE_NAME"}, {"field_name": "Payroll Name", "field_values": ["<PERSON><PERSON><PERSON>, <PERSON>", "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "<PERSON><PERSON><PERSON><PERSON>, <PERSON>"], "fact_subtype_id": "DRIVER_NAME"}, {"field_name": "Full Name", "field_values": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "fact_subtype_id": "DRIVER_NAME"}, {"field_name": "Drivers Schedule", "field_values": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "fact_subtype_id": "DRIVER_NAME"}, {"field_name": "Column 2", "field_values": ["<PERSON>, <PERSON>", "<PERSON><PERSON>, <PERSON>", "<PERSON>, <PERSON>", "<PERSON>, <PERSON>", "<PERSON><PERSON>, Ryan"], "fact_subtype_id": "DRIVER_NAME"}, {"field_name": "PAYROLL NAME", "field_values": ["ABERNATHY, MARC", "ALBRIGHT, LUCAS", "ALICEA FIGUEROA, ROBERTO", "ALICEA FIGUEROA, WILBERTO", "ALVAREZ, DANIEL"], "fact_subtype_id": "DRIVER_NAME"}, {"field_name": "Position - Current", "field_values": ["Accounting", "Lube Tech", "BDC Agent", "Sales Consultan", "FI Manager"], "fact_subtype_id": "DRIVER_ROLE"}, {"field_name": "Job", "field_values": ["Accounts Payable", "Office Manager", "Title Clerk", "Accounting Assistant", "Business Manager"], "fact_subtype_id": "DRIVER_ROLE"}, {"field_name": "Job <PERSON>", "field_values": ["ACCOUNTING", "BDC", "BILLING CLERK", "CASHIER", "CLERICAL"], "fact_subtype_id": "DRIVER_ROLE"}, {"field_name": "Job Description", "field_values": ["Owner", "Owner", "Admin Manager", "Clerk", "Clerk"], "fact_subtype_id": "DRIVER_ROLE"}, {"field_name": "Drv Type", "field_values": ["Driver", "Driver", "Driver", "Driver", "Driver"], "fact_subtype_id": "DRIVER_ROLE"}, {"field_name": "FULL/PART TIME", "field_values": ["RFT", "RFT", "RFT", "RFT", "RPT"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "Employment Type", "field_values": ["FULL TIME", "PART TIME", "PART TIME", "FULL TIME", "PART TIME"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "Full/Part Time", "field_values": ["F", "F", "F", "P", "P"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "Employment Type - Current", "field_values": ["RFT", "RFT", "RFT", "RFT", "RFT"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "PT/FT", "field_values": ["F", "F", "F", "F", "P"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "DOL_Status", "field_values": ["Full-Time", "Full-Time", "Full-Time", "Full-Time", "Full-Time"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "Full/Part", "field_values": ["Full", "Part", "Part", "Full", "Part"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "Worker Category Description", "field_values": ["Regular Full-Time", "Regular Full-Time", "Regular Full-Time", "Regular Full-Time", "Regular Full-Time"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "FT or PT", "field_values": ["FT", "FT", "PT", "FT", "FT"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "Driving Status", "field_values": ["Active", "Active", "Active", "Active", "Active"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "Employment Status", "field_values": ["EMPLOYED", "EMPLOYED", "EMPLOYED", "EMPLOYED", "EMPLOYED"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "Full/ Part", "field_values": ["Full", "Full", "Full", "Full", "Full"], "fact_subtype_id": "DRIVER_STATUS"}, {"field_name": "exp year dr", "field_values": ["3.0383561643835617", "5.761643835616439", "6.512328767123288", "6.67945205479452", "3.9835616438356163"], "fact_subtype_id": "DRIVER_YEARS_OF_EXPERIENCE"}, {"field_name": "Year of Experience", "field_values": ["2+", "2+", "2+", "2+", "2+"], "fact_subtype_id": "DRIVER_YEARS_OF_EXPERIENCE"}, {"field_name": "Exp", "field_values": ["6", "8", "4", "10+", "10+"], "fact_subtype_id": "DRIVER_YEARS_OF_EXPERIENCE"}, {"field_name": "Exp.1", "field_values": ["10+", "10+", "10+", "6", "4"], "fact_subtype_id": "DRIVER_YEARS_OF_EXPERIENCE"}, {"field_name": "YEAR LIC", "field_values": ["1989", "1986", "1987", "1982", "1988"], "fact_subtype_id": "DRIVER_YEAR_STARTED_DRIVING"}, {"field_name": "Start Date", "field_values": ["2023-10-03 00:00:00", "2024-10-21 00:00:00", "2023-11-13 00:00:00", "2021-05-01 00:00:00", "2024-09-16 00:00:00"], "fact_subtype_id": "DRIVER_YEAR_STARTED_DRIVING"}, {"field_name": "EDP Hardware", "field_values": ["30000", null], "fact_subtype_id": "EDP"}, {"field_name": "EDP Software", "field_values": ["10000", null], "fact_subtype_id": "ED_SOFTWARE_LIMIT"}, {"field_name": "EDP - Software", "field_values": ["60000"], "fact_subtype_id": "ED_SOFTWARE_LIMIT"}, {"field_name": "Building Elevation", "field_values": [null], "fact_subtype_id": "ELEVATION"}, {"field_name": "Total Employees", "field_values": ["11", "16", "8", "0", "3"], "fact_subtype_id": "EMPLOYEE_COUNT"}, {"field_name": "Employees", "field_values": ["75", null], "fact_subtype_id": "EMPLOYEE_COUNT"}, {"field_name": "Total Employees for this location and named insured", "field_values": ["529", "38", "371", "21", "49"], "fact_subtype_id": "EMPLOYEE_COUNT"}, {"field_name": "Legal Entity", "field_values": ["The Pointe Village Homes", null], "fact_subtype_id": "ENTITY_TYPE"}, {"field_name": "Entity", "field_values": ["BEI-Beach", "BEI-Beach", "BEI-Beach", "BEI-Beach", "BEI-Beach"], "fact_subtype_id": "ENTITY_TYPE"}, {"field_name": "Exterior Walls - CLADSYS", "field_values": ["Brick", "Tilt Up C", "Block", "Tilt Wall", "Brick"], "fact_subtype_id": "EXTERIOR_WALL_FEATURES"}, {"field_name": "Predominant Exterior Wall/Cladding", "field_values": ["Vinyl Siding", "Vinyl Siding", "Vinyl Siding", "Vinyl Siding", "Vinyl Siding"], "fact_subtype_id": "EXTERIOR_WALL_FEATURES"}, {"field_name": "Building Exterior - Other", "field_values": [null], "fact_subtype_id": "EXTERIOR_WALL_FEATURES"}, {"field_name": "Building Exterior", "field_values": ["Brick", "Precast Concrete", null], "fact_subtype_id": "EXTERIOR_WALL_FEATURES"}, {"field_name": "Exterior Building Surface", "field_values": ["Stucco", "Stucco", "Stucco", "Stucco", "Stucco"], "fact_subtype_id": "EXTERIOR_WALL_FEATURES"}, {"field_name": "FED. ID #", "field_values": ["13-3744274", "87-3871829", "82-1104941", "87-3871829", null], "fact_subtype_id": "FEIN"}, {"field_name": "FED ID#", "field_values": ["88-4389629"], "fact_subtype_id": "FEIN"}, {"field_name": "Fire Alarms", "field_values": [null], "fact_subtype_id": "FIRE_ALARM_MANUFACTURER"}, {"field_name": "Smoke Alarm", "field_values": ["Hard Wire w/nBattery back up", "Battery", "Battery", "Battery", "Hard Wire w/nBattery back up"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Alarm System", "field_values": ["LOCAL", "LOCAL", "LOCAL", "LOCAL", "LOCAL"], "fact_subtype_id": "BURGLAR_ALARM_TYPE"}, {"field_name": "Type of Alarms (i.e. Burglary, Fire, Water)", "field_values": ["Burglar, Fire", "Burglar, Fire", null], "fact_subtype_id": null}, {"field_name": "Fire Alarm Type", "field_values": ["good alarm", null], "fact_subtype_id": "FIRE_ALARM_TYPE"}, {"field_name": "Alarms?", "field_values": ["Battery", "Battery", "Battery", "Battery", "Battery"], "fact_subtype_id": "BURGLAR_ALARM_TYPE"}, {"field_name": "Fire Safety Systems", "field_values": ["Hard Wired Smoke Detectors", "Hard Wired Smoke Detectors", "Hard Wired Smoke Detectors", "Hard Wired Smoke Detectors", "Hard Wired Smoke Detectors"], "fact_subtype_id": "FIRE_ALARM_TYPE"}, {"field_name": "Alarms", "field_values": ["yes", "Yes", "Yes", "Yes", "yes"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Manual Pull Fire Alarm Y/N", "field_values": ["N"], "fact_subtype_id": "HAS_FIRE_ALARM"}, {"field_name": "Fire Alarm", "field_values": ["strong", null], "fact_subtype_id": "HAS_FIRE_ALARM"}, {"field_name": "Fire District name", "field_values": ["Acadia", "Acadia", "Acadia", "Acadia", "Acadia"], "fact_subtype_id": "FIRE_DISTRICT"}, {"field_name": "Premises fire protection", "field_values": ["Wet System Sprinkler", "Wet System Sprinkler", null], "fact_subtype_id": "FIRE_PROTECTION"}, {"field_name": "Fire Protection", "field_values": ["Yes", "No"], "fact_subtype_id": null}, {"field_name": "Fire Protection", "field_values": ["4", "4", "4", null], "fact_subtype_id": null}, {"field_name": "Garaged State", "field_values": ["AL", "AL", "AL", "AL", "AL"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Unit  State/Province", "field_values": ["TX", "TX", "CA", "CA", "TX"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Garage Location:", "field_values": ["GA", "FL", "FL", "GA"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Garage Location (City and State)", "field_values": ["Jupiter", "Jupiter", "Jupiter", "Hobe Sound", "Camden"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "State.1", "field_values": ["WY", "ND", "ND", "ND", "ND"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "State of Issuance", "field_values": ["Illinois", "Illinois", "Illinois", null], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Garaging ST", "field_values": ["NV", "NV", "NV", "NV", "NV"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Gar. State", "field_values": ["CT", "CT", "CT", "CT", "CT"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Garaging State", "field_values": ["GA", "MI", "FL", "MI", "VA"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "State of Vehicle Registration", "field_values": ["IN", "IA", null], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Location state", "field_values": ["PA", "PA", "PA", "PA", "TN"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "State Territoty", "field_values": ["GA", "GA", "GA", "GA", "GA"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Garage Location (State)", "field_values": ["DC", "DC", "DC", "DC"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Location State", "field_values": ["VA", "VA", "VA", "VA", "VA"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "ST Garaging", "field_values": ["CA", "CA", "CA"], "fact_subtype_id": "FLEET_OPERATION_STATES"}, {"field_name": "Flood", "field_values": ["no", null], "fact_subtype_id": "FLOOD_RISK"}, {"field_name": "Flood Zone", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "FLOOD_ZONE"}, {"field_name": "Flood (Y/N)", "field_values": ["N", null], "fact_subtype_id": "FLOOD_ZONE"}, {"field_name": "Flood Zone Desc", "field_values": ["AE", "X", "X", "X", "X"], "fact_subtype_id": "FLOOD_ZONE"}, {"field_name": "FLOOD ZONE (if multiple zones apply, please note highest risk zone)", "field_values": ["Yes", "Yes", null], "fact_subtype_id": "FLOOD_ZONE"}, {"field_name": "FLOOD ZONE", "field_values": ["X", "X", "X", "X", "X"], "fact_subtype_id": "FLOOD_ZONE"}, {"field_name": "Flood Zones (Can be completed by AJG)", "field_values": ["X", "X", "X", "X"], "fact_subtype_id": "FLOOD_ZONE"}, {"field_name": "FLOOD ZONE (if multiple zones apply, please note)", "field_values": ["X", "X", "X", "X"], "fact_subtype_id": "FLOOD_ZONE"}, {"field_name": "Foundations", "field_values": ["30253", "30253", "30253", "30253", "30253"], "fact_subtype_id": "FOUNDATION_TYPE"}, {"field_name": "Frame-Foundation Connection", "field_values": ["1", null], "fact_subtype_id": "FOUNDATION_TYPE"}, {"field_name": "Equipment Bracing = MECHELEC", "field_values": [null], "fact_subtype_id": "FOUNDATION_TYPE"}, {"field_name": "Full-Time", "field_values": ["9", "11", "4", "5", "7"], "fact_subtype_id": "FT_EMPLOYEES_COUNT"}, {"field_name": "2025-26 Max No. of FTE Any One Shift/Time", "field_values": ["6", "215", "150"], "fact_subtype_id": "FT_EMPLOYEES_COUNT"}, {"field_name": "2025-26  Max No. of FTE/ Any One Shift/Time", "field_values": ["280", "6", "2", "4"], "fact_subtype_id": "FT_EMPLOYEES_COUNT"}, {"field_name": "Full Time", "field_values": ["30", null], "fact_subtype_id": "FT_EMPLOYEES_COUNT"}, {"field_name": "Location", "field_values": ["1", "2", "3", "4", "5"], "fact_subtype_id": "FT_EMPLOYEES_COUNT"}, {"field_name": "GL", "field_values": ["3647"], "fact_subtype_id": "GL_CODE"}, {"field_name": "GL PREMIUM", "field_values": ["5774"], "fact_subtype_id": "GL_CODE"}, {"field_name": "Total Building S. F.", "field_values": ["4000", "78455", null], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Gross Sq.Ft Incl Basement & Garage", "field_values": ["15048", "15048", "15048", "15048", "15048"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Total Sq ft", "field_values": ["8048", "8048", "8848", "8304", "8048"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Total building area", "field_values": ["7500", "27000", "2000", "2000", "7500"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Multi-Family Gross Square Footage", "field_values": ["225663", "237547", "69245", "81587", "48355"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Gross SF", "field_values": ["182713"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "TOTAL SQ FT", "field_values": ["130000", "61712", "29330", "42612", "96400"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Gross Building  Area Sq Ft", "field_values": ["18554"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "TSF", "field_values": ["20000", "37200", "2426", "2450", "2500"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Gross Building Area", "field_values": ["85149"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Warehouse (sq ft)", "field_values": ["9974", "0", "0", "15801", "9683"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Sales Floor  (sq ft)", "field_values": ["28186", "4945", "33719", "18305"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Sq Ft Parking", "field_values": ["120000", "25000", "131600", "38261"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Total Unit Sq Ft Valued", "field_values": ["6170", "6408", "6170", "6408", "6170"], "fact_subtype_id": "GROSS_AREA"}, {"field_name": "Weight", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "GVWR"}, {"field_name": "Gross Weight", "field_values": ["36000", "26000", "26000", "60010", "36000"], "fact_subtype_id": "GVWR"}, {"field_name": "Hail  Risk   Level", "field_values": ["Moderate", "Moderate", "Low", "Low", "Moderate"], "fact_subtype_id": "HAIL_RISK"}, {"field_name": "ADR", "field_values": ["115", "110", "112", "128", "122"], "fact_subtype_id": "HAS_ADR_PRESENT"}, {"field_name": "Guest rooms have accessible balconies?", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "HAS_BALCONY"}, {"field_name": "Basement: Sq. Ft.", "field_values": ["65728", "6724", "8504"], "fact_subtype_id": "HAS_BASEMENT"}, {"field_name": "Bsmnt", "field_values": ["N", null], "fact_subtype_id": "HAS_BASEMENT"}, {"field_name": "Basement", "field_values": ["102-Basement WITH Flood Protection", "102-Basement WITH Flood Protection", "102-Basement WITH Flood Protection", "102-Basement WITH Flood Protection", "102-Basement WITH Flood Protection"], "fact_subtype_id": "HAS_BASEMENT"}, {"field_name": "Basement Area", "field_values": [null], "fact_subtype_id": "HAS_BASEMENT"}, {"field_name": "Burglar (Yes or No)", "field_values": ["No"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Burlgar Alarm", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Central Burglar", "field_values": ["Y"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Burgular Alarm?", "field_values": ["Y", "Y", "Y", "Y", "Y"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Burglar", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Buglar Alarm", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "<PERSON><PERSON><PERSON>", "field_values": ["Y"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Video Surveillance/CCTV", "field_values": ["Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_CCTV"}, {"field_name": "INTERIOR: Video Surveillance (24/7 w/ 14 Day Backup)", "field_values": ["Yes"], "fact_subtype_id": "HAS_CCTV"}, {"field_name": "EXTERIOR: Video Surveillance (24/7 w/ 14 Day Backup)", "field_values": ["Yes"], "fact_subtype_id": "HAS_CCTV"}, {"field_name": "INTERIOR: Video Surveillance (w/ 14 hrs recording min?)", "field_values": ["Y"], "fact_subtype_id": "HAS_CCTV"}, {"field_name": "EXTERIOR: Video Surveillance (w/ 14 hrs recording min?)", "field_values": ["Y"], "fact_subtype_id": "HAS_CCTV"}, {"field_name": "DAY CARE FACILITIES ONSITE?", "field_values": ["no", "no", "no", "no", "no"], "fact_subtype_id": "HAS_DAY_CARE"}, {"field_name": "Day Care Ctr on Premises (Y/N)", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_DAY_CARE"}, {"field_name": "Day Care on Premises?", "field_values": ["No"], "fact_subtype_id": "HAS_DAY_CARE"}, {"field_name": "Daycare (Y/N)", "field_values": ["N", null], "fact_subtype_id": "HAS_DAY_CARE"}, {"field_name": "Daycares, before or after school programs, tutoring, other childcare services, or other operations involving children?", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "HAS_DAY_CARE"}, {"field_name": "Elev.", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_ELEVATORS"}, {"field_name": "Elevators?", "field_values": ["6 passenger elevators, n1 MRL passenger elevator n1 service elevators", "10n(There are 9 passenger elevators. There is also 1 freight elevatornManufactured by Otis.)", null], "fact_subtype_id": "HAS_ELEVATORS"}, {"field_name": "Elevator (other)", "field_values": ["1", null], "fact_subtype_id": "HAS_ELEVATORS"}, {"field_name": "Evacuation Plan with annual reminders to tenants", "field_values": ["Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_EMERGENCY_OR_EVACUATION_PLAN"}, {"field_name": "BUILDING WIDE FIRE ALARM?", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_FIRE_ALARM"}, {"field_name": "Local Fire Alarm", "field_values": ["no", "no", "no", "no", "NO"], "fact_subtype_id": "HAS_FIRE_ALARM"}, {"field_name": "Central Station Alarm", "field_values": ["Yes"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Central Alarm System", "field_values": ["Yes"], "fact_subtype_id": "HAS_FIRE_ALARM"}, {"field_name": "Alarm on each floor?", "field_values": ["no"], "fact_subtype_id": "HAS_BURGLAR_ALARM"}, {"field_name": "Central Station", "field_values": ["no", "no", "no", "no", "no"], "fact_subtype_id": null}, {"field_name": "Hosted  Liquor", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "HAS_LIQUOR_LICENSE"}, {"field_name": "Any Past Assault or Rape Claims?", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "HAS_PAST_LOSSES_FOR_ABUSE_MOLESTATION_DISCRIMINATION"}, {"field_name": "Pools and recreation   pool   marked depth", "field_values": ["Yes"], "fact_subtype_id": "HAS_POOL_DEPTH_MARKERS"}, {"field_name": "Depths marked  on the pools Y/N", "field_values": ["Yes"], "fact_subtype_id": "HAS_POOL_DEPTH_MARKERS"}, {"field_name": "Present", "field_values": ["Y", null], "fact_subtype_id": "HAS_POOL_DEPTH_MARKERS"}, {"field_name": "Depths Marked", "field_values": ["Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_POOL_DEPTH_MARKERS"}, {"field_name": "Board", "field_values": ["N", null], "fact_subtype_id": "HAS_POOL_DIVING_BOARD"}, {"field_name": "Fences around  the pools Y/N", "field_values": ["Yes"], "fact_subtype_id": "HAS_POOL_FENCE"}, {"field_name": "Fenced", "field_values": ["Y", null], "fact_subtype_id": "HAS_POOL_FENCE"}, {"field_name": "Lifeguard on Duty    Y/N", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_POOL_LIFEGUARD"}, {"field_name": "Lifeguard? (Yes/No)", "field_values": [null], "fact_subtype_id": "HAS_POOL_LIFEGUARD"}, {"field_name": "If yes, Lifeguard details", "field_values": ["No"], "fact_subtype_id": "HAS_POOL_LIFEGUARD"}, {"field_name": "Pools and recreation   pool   posted rules/disclaimers", "field_values": ["Yes"], "fact_subtype_id": "HAS_POOL_RULES_POSTED"}, {"field_name": "Rules Posted", "field_values": ["Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_POOL_RULES_POSTED"}, {"field_name": "Rules/ Disclaimers  marked at the pools Y/N", "field_values": ["Yes"], "fact_subtype_id": "HAS_POOL_RULES_POSTED"}, {"field_name": "Rules / Disclaimers Posted", "field_values": ["Yes", "Yes"], "fact_subtype_id": "HAS_POOL_RULES_POSTED"}, {"field_name": "Posted", "field_values": ["Y", null], "fact_subtype_id": "HAS_POOL_RULES_POSTED"}, {"field_name": "Gated Doors?", "field_values": ["Yes", null], "fact_subtype_id": "HAS_POOL_SELF_LOCKING_GATES"}, {"field_name": "Stab-Lok Circuit Breakers and Panels", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_POOL_SELF_LOCKING_GATES"}, {"field_name": "Self Lock Pool Gate", "field_values": ["Yes", null], "fact_subtype_id": "HAS_POOL_SELF_LOCKING_GATES"}, {"field_name": "Gated", "field_values": ["Y", null], "fact_subtype_id": "HAS_POOL_SELF_LOCKING_GATES"}, {"field_name": "FENCED AND SECURED", "field_values": ["YES", "YES", "YES", "YES", "YES"], "fact_subtype_id": "HAS_PREMISES_FENCE"}, {"field_name": "Fenced (Y/N)", "field_values": ["Y", null], "fact_subtype_id": "HAS_PREMISES_FENCE"}, {"field_name": "Fully Fenced or Enclosed y/n", "field_values": ["Y", "Y", "Y", "Y", "Y"], "fact_subtype_id": "HAS_PREMISES_FENCE"}, {"field_name": "If yes, is it fenced?", "field_values": ["Yes"], "fact_subtype_id": "HAS_PREMISES_FENCE"}, {"field_name": "Fences", "field_values": ["No"], "fact_subtype_id": "HAS_PREMISES_FENCE"}, {"field_name": "Pull Chords / Medical Response", "field_values": ["No"], "fact_subtype_id": "HAS_PULL_CORDS_IN_EACH_UNIT"}, {"field_name": "Pull Cords  Yes / No", "field_values": ["No", "No"], "fact_subtype_id": "HAS_PULL_CORDS_IN_EACH_UNIT"}, {"field_name": "Security", "field_values": [null], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Onsite Security", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Onsite Guard Serv.", "field_values": ["No", "No", "No", "No"], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Security or Courtesy Patrol?", "field_values": ["No"], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Guards", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Are there any bouncers or third-party security provided?", "field_values": ["No", "No"], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Watchmen or Security Guards? (Yes / No)", "field_values": ["Yes", "Yes", null], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Security   third-party security guards", "field_values": ["Yes", null], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "GUARDS", "field_values": ["N", "Y", "N"], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "SECURITY", "field_values": ["N", "N", "N"], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Watchmen or  Security Guards  (Yes / No)", "field_values": ["No", "No", "No"], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Watchmen of Security Guards", "field_values": ["Yes"], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "Security or Courtesy Patrol", "field_values": ["Yes  ", "Yes  ", "Yes  ", "Yes  ", "Yes  "], "fact_subtype_id": "HAS_SECURITY_GUARDS"}, {"field_name": "SMOKE DETECT", "field_values": ["Battery", "Battery", "Battery", "Battery", "Battery"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Carbon Monoxide? (Yes / No)", "field_values": ["Yes", null], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Smoke detectors in All Sleeping Areas & Halls", "field_values": ["Yes"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Battery operated smoke detectors", "field_values": ["Y", "Y", "Y", "Y", "Y"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Smoke Detectors in Common Areas", "field_values": ["Yes", "No", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Permanently installed smoke detectors in each unit?", "field_values": ["Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Smoke Detectors and Carbon Monoxide Detectors in Common Areas", "field_values": ["Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Smoke / Carbon Monoxide Detectors", "field_values": ["Smoke/ Battery-Powered", "Smoke/ Battery-Powered", "Smoke/ Battery-Powered", "Smoke/ Battery-Powered", "Smoke/ Battery-Powered"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Smoke Alarm Power", "field_values": ["Hard Wired", "Battery", "HW & Battery", "Battery", "Battery"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "% Hard wired Smk Det", "field_values": ["Battery", "Mixed Battery and Hard-wired - some units may have battery only", "Battery only"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Smoke Detection", "field_values": ["Hard wired w battery backup in common areas & bedroooms"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Smoke detector (Hard Wired or Battery)", "field_values": ["YES", "YES", "YES", "YES", "YES"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Auto-matic Smoke Detect", "field_values": ["Yes", "Yes", "Yes", "Yes", "No"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "CO2 Detectors in each unit", "field_values": ["Yes"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Identify Type of Smoke Det", "field_values": ["yes", "yes"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Smoke Detectors in All Sleeping Areas & Halls", "field_values": ["Yes     ", "Yes     ", "Yes     ", "Yes     ", "Yes     "], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "H/W Smoke Detector", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SMOKE_DETECTOR"}, {"field_name": "Fire Sprinklered", "field_values": ["Yes", "No", "No", "No", "Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprk?", "field_values": ["Yes", "Yes", "1"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinklered  (100%/Partially  /Not Sprinklered)", "field_values": ["Partially", "Partially", "Partially"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Building Sprinklered (Yes/No)?", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "NFPA 13 Sprinklers?", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "<PERSON><PERSON>rink-lered Y/N", "field_values": ["N", "N", "Y", "Y", "Y"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "SPRKLR", "field_values": ["yes", "yes", "yes", "yes", null], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinklered", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinkler.1", "field_values": ["Yes", "Yes", null], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "SPRNK Y/N", "field_values": ["N", null], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Spklrd?", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Fully Sprinklered", "field_values": ["Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprklr", "field_values": ["Yes", null], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Orig SOV Sprinklered", "field_values": ["Yes", "Yes", "Yes", null], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Fire Protection (Sprinklers - Wet/Dry)", "field_values": ["Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Fire Extinguishing Sprinkler System?", "field_values": ["No", "No", "No", "No"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Building Sprinklered?", "field_values": ["Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprnk", "field_values": ["Y", "N", "Y"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "<PERSON>y <PERSON>rinklered (yes/no)", "field_values": ["Yes", "Yes", "Yes", "Yes", "No"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprkld", "field_values": ["Yes", "No", "No", "Yes", null], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Fully Sprinklered?", "field_values": ["Yes", "Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Fire Sprinklered?", "field_values": ["N", "N", "N", "Y", "N"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "SPRINKLERED_1", "field_values": ["Y", "Y", "N", "Y"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Y/N)", "field_values": ["0", "0"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprnklr", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "AUTO SPRINKLER SYSTEM? What %?", "field_values": ["Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "% Sprinklered", "field_values": ["Yes ", "Yes ", "Yes ", "Yes ", "Yes "], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprklrs", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprnkld?", "field_values": ["N", "N", null], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Spkred?", "field_values": ["n", "n", "n", "y", "n"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprlk", "field_values": ["Y", "Y", "Y"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Orig SOV Fire Sprinklers", "field_values": ["Y", "Y", "Y", "Y", "Y"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "SPKLD", "field_values": ["Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>- lered (Y/N)", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprkld Yes/No", "field_values": ["Y", "Y", "N", "Y", "Y"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinklered  or Alarms", "field_values": ["Sprinklered", "Smoke Dect. Hard Wire", null], "fact_subtype_id": "BURGLAR_ALARM_TYPE"}, {"field_name": "Orig SOV Sprinklers", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Are Bldgs Sprinkled? (Y/N)", "field_values": ["Y"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprinkler (None/Wet/Dry)", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprnk (Y/N)", "field_values": ["N", "N", "N", "N"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Area Sprinklered?", "field_values": ["Yes", "Yes", "No", "No", "No"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "SPRK", "field_values": ["YES", "YES", "NO", "NO", "YES"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "IsSprinklered", "field_values": ["Y", "Y"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Any Subsidized Housing (not applicable in NY)", "field_values": ["N"], "fact_subtype_id": "HAS_SUBSIDIARIES"}, {"field_name": "Subsidized Housing?  Y or N", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_SUBSIDIARIES"}, {"field_name": "Subsized", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "HAS_SUBSIDIARIES"}, {"field_name": "Pool(s) Yes or No Indicate Number", "field_values": ["n", "n", null], "fact_subtype_id": "HAS_SWIMMING_POOL"}, {"field_name": "Pools and recreation   pool   life safety equipment", "field_values": ["Yes", "Yes", "Yes", "Yes", null], "fact_subtype_id": "HAS_SWIMMING_POOL"}, {"field_name": "AL", "field_values": ["X", "X", "X", "X", "X"], "fact_subtype_id": "HAS_VEHICLE_AUTO_LIABILITY_COVERAGE"}, {"field_name": "Comp/Collision?", "field_values": ["N", "N", "N", "N", "Y"], "fact_subtype_id": "HAS_VEHICLE_COLLISION_COVERAGE"}, {"field_name": "Coll", "field_values": ["X", "X", "X", "X", "X"], "fact_subtype_id": "HAS_VEHICLE_COLLISION_COVERAGE"}, {"field_name": "PIP Each Person", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "HAS_VEHICLE_PIP_COVERAGE"}, {"field_name": "Dmg", "field_values": ["Y", "Y", "Y", "Y", "Y"], "fact_subtype_id": "HAS_VEHICLE_PROPERTY_DAMAGE_COVERAGE"}, {"field_name": "Un/Under Insured Motorist", "field_values": ["Y", "Y", "Y", "Y", "Y"], "fact_subtype_id": "HAS_VEHICLE_UNDERINSURED_MOTORIST_COVERAGE"}, {"field_name": "TOTAL UNITS:", "field_values": ["9"], "fact_subtype_id": "HUD_UNITS_COUNT"}, {"field_name": "# of Residential Units", "field_values": ["7"], "fact_subtype_id": "HUD_UNITS_COUNT"}, {"field_name": "Dwellings- One Family Lessor Risk (other)", "field_values": ["1"], "fact_subtype_id": "HUD_UNITS_COUNT"}, {"field_name": "Dwellings-one-family(lessor's Risk Only) (unit)", "field_values": ["1", null], "fact_subtype_id": "HUD_UNITS_COUNT"}, {"field_name": "OFFICE EXPENSES", "field_values": ["0", "15000", "0", "15000"], "fact_subtype_id": "INVENTORY_VALUE"}, {"field_name": "Max Inv", "field_values": ["0", "4770000", "2332000", "3710000", "5936000"], "fact_subtype_id": "INVENTORY_VALUE"}, {"field_name": "410A Extra Inventory @ 11-11-24", "field_values": ["465646.95720000006", "510767.284", "281112.48139999993", null], "fact_subtype_id": "INVENTORY_VALUE"}, {"field_name": "ISO Protection Class", "field_values": ["1", "2", "3", "4", "5"], "fact_subtype_id": "ISO_FIRE_PROTECTION_CLASS"}, {"field_name": "ISO Class", "field_values": ["1499", "33499", "21499", "1499", "1499"], "fact_subtype_id": "ISO_VEHICLE_RATING"}, {"field_name": "ITV.1", "field_values": ["455.4624931222107"], "fact_subtype_id": "ITV"}, {"field_name": "License #", "field_values": ["BEV6805559 11CT", "BEV1505977 4COP", "BEV1507633", "BEV6307019 4COP", "AR30836 LID80373"], "fact_subtype_id": "LATEST_LICENSE_NUMBER"}, {"field_name": "City:", "field_values": ["New York"], "fact_subtype_id": "LENDER_CITY"}, {"field_name": "Named Insured", "field_values": ["Shook & Fletcher Services, LLC", "Vulcan Industrial Contractors Co., LLC", "Vesta Industrial Contractors, Inc.", "WK Services Co., LLC", "Shook & Fletcher Supply Co., Inc."], "fact_subtype_id": "LENDER_NAME"}, {"field_name": "Account Name", "field_values": ["Jack Thompson Investments, Inc ", "Jack Thompson Investments, Inc ", "Jack Thompson Investments, Inc ", "Jack Thompson Investments, Inc ", "Jack Thompson Investments, Inc "], "fact_subtype_id": "LENDER_NAME"}, {"field_name": "Entity Name", "field_values": ["Dovecoast Housing LP", "Dovecoast Housing LP", "Dovecoast Housing LP", "Dovecoast Housing LP", "Dovecoast Housing LP"], "fact_subtype_id": "LENDER_NAME"}, {"field_name": "NAMED INSURED", "field_values": ["684 Owners Corp."], "fact_subtype_id": "LENDER_NAME"}, {"field_name": "Named Insured(s)", "field_values": ["Brandywine Tree and Shrub LLC; LRK LLC", "Brandywine Tree and Shrub LLC; LRK LLC"], "fact_subtype_id": "LENDER_NAME"}, {"field_name": "<PERSON><PERSON>", "field_values": ["Simmons Bank", "Simmons Bank", "Simmons Bank", "Simmons Bank", "Simmons Bank"], "fact_subtype_id": "LENDER_NAME"}, {"field_name": "Mortgagee", "field_values": ["Premier Bank, 301 Central Ave, Osseo MN 55369", "Bank of the West ISAOAn13300 Crossroads Pkwy N La Puente, CA 917846"], "fact_subtype_id": "LENDER_NAME"}, {"field_name": "State:", "field_values": ["NY"], "fact_subtype_id": "LENDER_STATE"}, {"field_name": "Location Address: Street", "field_values": ["16-42-50 Bridgewater St.", "80-120 Apollo Street,", "902-24 <PERSON><PERSON><PERSON>.", "944 Meeker Ave.", "952-56 <PERSON><PERSON>er Ave."], "fact_subtype_id": "LENDER_STREET"}, {"field_name": "Street Address:", "field_values": ["360 West 22nd Street"], "fact_subtype_id": "LENDER_STREET"}, {"field_name": "LOCATION", "field_values": ["684 Broadway"], "fact_subtype_id": "LENDER_STREET"}, {"field_name": "Street Name & Number", "field_values": ["2486 NW WESTOVER"], "fact_subtype_id": "LENDER_STREET"}, {"field_name": "Lender Contact Info", "field_values": ["601 E. Third Street Little Rock, AR 72201", "601 E. Third StreetnLittle Rock, AR 72201", "601 E. Third Street Little Rock, AR 72201", "601 E. Third StreetnLittle Rock, AR 72201", "601 E. Third Street Little Rock, AR 72201"], "fact_subtype_id": "LENDER_STREET"}, {"field_name": "Street Address", "field_values": ["2-100 Cedar Lane", "2-10 <PERSON> Lane", "12-20 Cedar Lane", "22-28 <PERSON> Lane", "30 Cedar Lane"], "fact_subtype_id": "LENDER_STREET"}, {"field_name": "Liquor Liability (r)", "field_values": ["716000"], "fact_subtype_id": "LIQUOR_LIMIT_AGGREGATE"}, {"field_name": "Liq %", "field_values": ["0.05000013000283406", "0.049999860741000234", "0.05000014164346075", "0.050000121147802744"], "fact_subtype_id": "LIQUOR_SALES_PERCENTAGE"}, {"field_name": "Loc Number", "field_values": ["1", "1", "2", "2", "3"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Prop. #", "field_values": ["7800", "5200", "8000", "8200", "4800"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Building No.", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Loc Nbr", "field_values": ["1", "1", "1"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "No.", "field_values": ["1", "2", "3", "4", "5"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Number", "field_values": ["1", "2", "3", "4", "5"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Physical Building #", "field_values": ["1148", "1144", "1143", "3918", "3914"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "NO.", "field_values": ["1", "4", "5", "6", "7"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Blg #", "field_values": ["1"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Location ID", "field_values": ["1", "2", "3", "4", "6"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Prop #", "field_values": ["1", "2", "3", "4"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Location Nbr", "field_values": ["1-1", "1-1", "1-1", "1-1", "1-1"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Internal Sort", "field_values": ["1", "2", "3", "4", "5"], "fact_subtype_id": "LOCATION_NUMBER"}, {"field_name": "Work location name", "field_values": ["AR01 - Paws n Rec - Bentonville", "CA01 - Metrodog - CA", "FL02 - Paws n Rec - Pearl", "FL03 - Paws n Rec - St. Pete", "FL04 - Paws n Rec - Lemon"], "fact_subtype_id": "LOCATION_TYPE"}, {"field_name": "City / Sub / Rural?", "field_values": ["City", "City", "City", "City", "City"], "fact_subtype_id": "LOCATION_TYPE"}, {"field_name": "Property Address", "field_values": ["100 Lenox Dr", "100 Lenox Dr", "989 Lenox Dr", "989 Lenox Dr", "993 Lenox Dr"], "fact_subtype_id": "LOCATION_TYPE"}, {"field_name": "HVAC Location", "field_values": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "fact_subtype_id": "LOCATION_TYPE"}, {"field_name": "Location City", "field_values": ["Great Falls"], "fact_subtype_id": "LOCATION_TYPE"}, {"field_name": "Actual Loss Sustained Business Income & Extra", "field_values": ["2500000.0"], "fact_subtype_id": "LOSS_OF_INCOME"}, {"field_name": "Loss of Income.1", "field_values": ["5600000"], "fact_subtype_id": "LOSS_OF_INCOME"}, {"field_name": "Loss of Business Income/Rents  - 12 Months", "field_values": ["180093669", "0", "257607343", "153816768", "27469835"], "fact_subtype_id": "LOSS_OF_INCOME"}, {"field_name": "Loss Income/ Extra Expense", "field_values": ["250000", "50000", "50000", "50000", "150000"], "fact_subtype_id": "LOSS_OF_INCOME"}, {"field_name": "2025-2026 Loss of Rents", "field_values": ["360000", "Delete", null], "fact_subtype_id": "LOSS_OF_RENT_INCOME"}, {"field_name": "LOT SF", "field_values": ["16514", "12480", "13761", "10509", "6434"], "fact_subtype_id": "LOT_SIZE"}, {"field_name": "SF (Lot)", "field_values": ["4842.3", "4839.6", "4746.5", "4421.2", "4048.2"], "fact_subtype_id": "LOT_SIZE"}, {"field_name": "Yr. Bldg updated - req'd if > 30 Yrs", "field_values": ["2002", "1990", "1990", "1990", "2000"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Yr Bldg upgraded - Major Exterior Update (mandatory if >25 yrs old)", "field_values": ["2007", "2007", "2007", "2007", "2007"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Yr Bldg updated (Mand if >25 yrs)", "field_values": ["2022", "2022", "2022", "2022", "2022"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Year: Updated", "field_values": ["2003", "2006", "2004"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Year of Updates", "field_values": ["2019", "2019", "2019", "2019", "2019"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Year Renovated", "field_values": [null], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Year Rehab", "field_values": ["2020", "2020", "2020", "2020", "2020"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "YEAR BLDG UPDATED", "field_values": ["2005", "2012", "2010"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Year Building Upd", "field_values": ["2018", "2018", "2018"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Year Upgrade", "field_values": ["2016", "2007", "2011  & 2016", "Will be 2019", "2003"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Year  Updated", "field_values": ["2023", "2019", "2019", "2022", "2023"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Major Renovations", "field_values": [null], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Year Rehabbed", "field_values": ["2018"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Year Acquired", "field_values": ["2023", "2023", "2023", "2023", "2023"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Building Updates", "field_values": ["2012"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Yr Bldg updated (Mand if >25 yrs).1", "field_values": [null], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "YEAR RENOVATED", "field_values": ["1995"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Updates", "field_values": ["2013", "2013", "2013", "2013", "2013"], "fact_subtype_id": "MOST_RECENT_RENOVATION_WORK_YEAR"}, {"field_name": "Murder Score", "field_values": ["C", "C", "C", "C", "C"], "fact_subtype_id": "MURDER_GRADE"}, {"field_name": "# Basm'ts", "field_values": ["0", "0", "0", "0"], "fact_subtype_id": "NUMBER_OF_BASEMENTS"}, {"field_name": "Room Count", "field_values": ["70", "60"], "fact_subtype_id": "NUMBER_OF_BEDROOM"}, {"field_name": "Bedroom", "field_values": ["3", "2", "3", "3", "2"], "fact_subtype_id": "NUMBER_OF_BEDROOM"}, {"field_name": "ELEVATORS (unit)", "field_values": ["4"], "fact_subtype_id": "NUMBER_OF_ELEVATORS"}, {"field_name": "Pass. Elevators", "field_values": ["1"], "fact_subtype_id": "NUMBER_OF_ELEVATORS"}, {"field_name": "Total Elevators Incl Freight", "field_values": ["8", "75", "8", "8"], "fact_subtype_id": "NUMBER_OF_ELEVATORS"}, {"field_name": "Elevator (unit)", "field_values": ["1", "1"], "fact_subtype_id": "NUMBER_OF_ELEVATORS"}, {"field_name": "All Other Employees", "field_values": ["143", "5", "10", "10", "10"], "fact_subtype_id": "NUMBER_OF_EMPLOYEES_RANGE"}, {"field_name": "2nd Shift", "field_values": ["157", "157", "187", "187", "109"], "fact_subtype_id": "NUMBER_OF_EMPLOYEES_RANGE"}, {"field_name": "4th Shift", "field_values": ["40", "40", null], "fact_subtype_id": "NUMBER_OF_EMPLOYEES_RANGE"}, {"field_name": "3rd Shift", "field_values": ["193", "193", "2", "2", null], "fact_subtype_id": "NUMBER_OF_EMPLOYEES_RANGE"}, {"field_name": "Other", "field_values": ["105", "105", "27", "27"], "fact_subtype_id": "NUMBER_OF_EMPLOYEES_RANGE"}, {"field_name": "Employee Benefits Liability", "field_values": ["2"], "fact_subtype_id": "NUMBER_OF_EMPLOYEES_RANGE"}, {"field_name": "Approx. No. of Employees/ shift @ Location (mfg AND admin)", "field_values": ["44", "15", "12", "32", "15"], "fact_subtype_id": "NUMBER_OF_EMPLOYEES_RANGE"}, {"field_name": "Bed Count", "field_values": ["100", "48", "86", "45", "68"], "fact_subtype_id": "NUMBER_OF_HOSPITAL_BEDS"}, {"field_name": "Various AZ locations - employee housing", "field_values": ["5", null], "fact_subtype_id": "NUMBER_OF_LOCATIONS"}, {"field_name": "Various CA locations - employee housing", "field_values": ["7"], "fact_subtype_id": "NUMBER_OF_LOCATIONS"}, {"field_name": "TOTAL # OF EE'S AT THIS LOCATION", "field_values": ["7", "209", "39", "7"], "fact_subtype_id": "NUMBER_OF_LOCATIONS"}, {"field_name": "Total Number of Locations (unit)", "field_values": ["49"], "fact_subtype_id": "NUMBER_OF_LOCATIONS"}, {"field_name": "Hotel (rooms)", "field_values": ["829"], "fact_subtype_id": "NUMBER_OF_ROOMS"}, {"field_name": "Rooms", "field_values": ["174", "114", "104", "200"], "fact_subtype_id": "NUMBER_OF_ROOMS"}, {"field_name": "# Hotels", "field_values": ["1", "1", "1", "1"], "fact_subtype_id": "NUMBER_OF_ROOMS"}, {"field_name": "Number of hotel rooms", "field_values": ["58", "68", "77", "81", "92"], "fact_subtype_id": "NUMBER_OF_ROOMS"}, {"field_name": "Hotel w pool, less than 4 stories (unit)", "field_values": ["112"], "fact_subtype_id": "NUMBER_OF_ROOMS"}, {"field_name": "Number of Rooms", "field_values": [null], "fact_subtype_id": "NUMBER_OF_ROOMS"}, {"field_name": "Original Mfg Seating Capacity/", "field_values": ["5", "5", "5", "5"], "fact_subtype_id": "NUMBER_OF_SEATS"}, {"field_name": "Original Mfg Seating Capacity", "field_values": ["5", "5", "5", "5", "5"], "fact_subtype_id": "NUMBER_OF_SEATS"}, {"field_name": "# of Shifts", "field_values": ["1", "3", "3", null], "fact_subtype_id": "NUMBER_OF_SHIFTS"}, {"field_name": "# Stories", "field_values": ["1", "1", "1", "2", "1"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "# of Stories", "field_values": ["1", "1 or 2", "1 or 2", "1 or 2", "1 or 2"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "STORY HT", "field_values": ["2", null], "fact_subtype_id": null}, {"field_name": "Floor #", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "Store", "field_values": ["8"], "fact_subtype_id": null}, {"field_name": "# of St.", "field_values": ["4", "4", "4", "4", "4"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "# of St", "field_values": ["2", "2", "2", "2", "1"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "2025-2026 No. of Stories In Building (>100 EE any one time)", "field_values": ["2", "2"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "# Floor", "field_values": ["8"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "Bldg Story", "field_values": ["1", null], "fact_subtype_id": null}, {"field_name": "STORE#", "field_values": ["4", "4", "7", "7", "16"], "fact_subtype_id": null}, {"field_name": "STOR #", "field_values": ["4", "4", "7", "7", "16"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "STORE #", "field_values": ["4", "4", "7", "7", "16"], "fact_subtype_id": null}, {"field_name": "Number of Stories (26)", "field_values": [null], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "Store Sales", "field_values": ["5,954", "10 000"], "fact_subtype_id": null}, {"field_name": "Garage # of Stories", "field_values": ["0", "3", "0", "0", "0"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "Story", "field_values": ["2", "2", "2"], "fact_subtype_id": null}, {"field_name": "Number of story", "field_values": ["1", "1", "2", "1", "2"], "fact_subtype_id": "NUMBER_OF_STORIES"}, {"field_name": "Store Sales", "field_values": ["2", "2", "2", "2", "2"], "fact_subtype_id": null}, {"field_name": "Swimming Pool (other)", "field_values": ["2"], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "# of Pools", "field_values": ["0", null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "SWIMMING OUTDOOR NO DIVING (other)", "field_values": ["1", null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Swimming Pools (other)", "field_values": ["2", null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Swimming pool (unit)", "field_values": [3, 4, null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Pools (other)", "field_values": ["2"], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Pool (other)", "field_values": ["1"], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "pool (other)", "field_values": ["1"], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Pools and recreation   pool   # of jacuzzis", "field_values": ["1", "1", "1", "1", null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "SWIMMING POOLS - NOC (other)", "field_values": ["3", null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Swimming Outdoor No Div ...", "field_values": ["2"], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Swimming Pool", "field_values": ["3"], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "Swimming Pools - 2280 Business Park Blvd, Donaldsonville LA (other)", "field_values": ["1", null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "SWIMMING POOLS (unit)", "field_values": [2, null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "# of Pools 2", "field_values": [null], "fact_subtype_id": "NUMBER_OF_SWIMMING_POOLS"}, {"field_name": "# of Units with Program?", "field_values": ["0", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Unit #", "field_values": ["2 units", "2 units ", "2 units", "4 units", "4 units"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Number of Units", "field_values": ["57"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# OF UNITS.1", "field_values": ["107", "1", "4", "4", "4"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of Unit", "field_values": ["14", "14", "14", "2", "2"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of Mercantile Units", "field_values": [null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apt Units", "field_values": ["89", "72", "96", "78", "161"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "APTS", "field_values": [null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of units", "field_values": ["320", "224", "224", "146", "140"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of units.1", "field_values": ["140"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apartments", "field_values": ["12", "6", "6", "16", "16"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of habitational units Occupied", "field_values": ["98", "98", "98", "98", "98"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of condo owners that rent out their condo", "field_values": ["10", "10", "10", "10", "10"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Total Planned Units", "field_values": ["140"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Applicable  Units", "field_values": ["84", "32", "7", "66", "71"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of Subsidized Apartment or Condo. / Coop. Units:", "field_values": ["0", "0", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# Apartment  Units:", "field_values": ["0", "0", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# Hotel/Motel Units:", "field_values": ["0", "0", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apartments (unit)", "field_values": ["4", "4"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Section 8 Units", "field_values": ["61", "47", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Senior Units", "field_values": [null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of Multi-Family Units (Property)", "field_values": ["280", "297", "88", "71", "50"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# Unit", "field_values": ["56", "56", "88"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "SECTION 8 UNITS", "field_values": ["0", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "CONDO UNITS", "field_values": ["0", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Unit Number", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apartment Buildings or Hotels Time-Sharing - Less Than 4 Stories (unit)", "field_values": ["61"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Residential Units", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Condominiums - Residential (Association Risk Only) (unit)", "field_values": ["300"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Dwelling - One Family 217 Crockett Circle (unit)", "field_values": ["1"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Condominiums - residential - (association risk only) (unit)", "field_values": ["15"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apartments - Applewood at the Coves (unit)", "field_values": [null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apartments - Ashton Place (unit)", "field_values": [null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Other Affordable Units", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of Student Units", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of Section  8 Units", "field_values": ["99", "99", "62", "62"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of Market Rate Units", "field_values": ["1", "1", "0", "0", "45"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of  Section 42 Units", "field_values": ["100", "100", "0", "0"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of Senior/ Disabled Units", "field_values": ["100", "100", "0", "0", "6"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of All units - Apartments and Commercial", "field_values": [null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Residential  Units", "field_values": ["921", "83", "1276", "4"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apartment Buildings - 548 W Johnson St Madison, WI 53703 (unit)", "field_values": ["36", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Dwelling-four family - 533 Conklin PI Madison, WI 53703 (unit)", "field_values": ["4"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apartment Buildings - 305 N Frances St Madison, WI 53703 (unit)", "field_values": ["82", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Apartment Buildings - 530 W Johnson Madison, WI 53703 (unit)", "field_values": ["45", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "RESIDENTIAL # OF UNITS", "field_values": ["16", "16", "31", "6", "12"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Condos (unit)", "field_values": ["20", "8"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Total Residential Units", "field_values": ["20", "8"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Commercial Units", "field_values": ["0", "0", "1"], "fact_subtype_id": "NUMBER_OF_COMMERCIAL_UNITS"}, {"field_name": "# of Commercial Units", "field_values": ["0", "0", "1"], "fact_subtype_id": "NUMBER_OF_COMMERCIAL_UNITS"}, {"field_name": "# of Commercial Units (unit)", "field_values": [10], "fact_subtype_id": "NUMBER_OF_COMMERCIAL_UNITS"}, {"field_name": "# of Residential Units", "field_values": ["0", "0", "1"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "no of Commercial Units", "field_values": ["0", "0", "1"], "fact_subtype_id": "NUMBER_OF_COMMERCIAL_UNITS"}, {"field_name": "number of Commercial Units", "field_values": ["0", "0", "1"], "fact_subtype_id": "NUMBER_OF_COMMERCIAL_UNITS"}, {"field_name": "Unit Count", "field_values": ["203", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "DWELLINGS-ONE-FAMILY (LESSOR'S RISK ONLY) (unit)", "field_values": ["1"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Condominium association - Residential- Assoc Risk Only (unit)", "field_values": ["96"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "UNIT", "field_values": ["10", "29", "29", "29", "34"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# units - residential", "field_values": ["110"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Number of Market Rent Units", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Total Number of Residential Units", "field_values": ["30", "30", "30", "30", "30"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "APT. UNITS", "field_values": ["0", "0", "0", "0", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "HOTEL UNITS", "field_values": ["0", "0", "0", "0", null], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Retail Units", "field_values": ["0", "0", "0", "0"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of TX Units", "field_values": ["60", "72", "65", "80", "32"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Student Housing Unit count or %", "field_values": ["0.36", "0.94", "0.2", "0.6", "0.32"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "# of OK Units", "field_values": ["52", "60", "276", "124", "100"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Unit", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "NUMBER_OF_UNITS"}, {"field_name": "Alliant Occupancy", "field_values": ["Commercial", "Commercial", "Walls-In", "Commercial", "Commercial"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "ACIC Occupancy", "field_values": [null], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "ACIC_Occupancy", "field_values": ["Building & Contents", null], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Ocupancy", "field_values": ["Church Sanctuary", "Storage Building", "Ministries of Jesus", "Educational Building and Church office", "UCO College House - Boys"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "PROPERTY_DESCRIPTION", "field_values": ["V1101 - Office - Office 1 (Low)", "V1101 - Office - Office 1 (Low)", "V1101 - Office - Office 1 (Low)", "V1101 - Office - Office 1 (Low)", "V1101 - Office - Office 1 (Low)"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Breakdown of Tenants", "field_values": [null], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Occupancy Description from SOV", "field_values": ["Occupancy Description from SOV"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Occupancy Details", "field_values": ["Machinery or Equipment Dealers - Repair of heavy machinery without motors or with motors over 6 horsepower", "Machinery or Equipment Dealers - Repair of heavy machinery without motors or with motors over 6 horsepower", "Machinery or Equipment Dealers - Repair of heavy machinery without motors or with motors over 6 horsepower", "Machinery or Equipment Dealers - Repair of heavy machinery without motors or with motors over 6 horsepower", "Machinery or Equipment Dealers - Repair of heavy machinery without motors or with motors over 6 horsepower"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "WKFC Detailed Occupancy", "field_values": ["Dwellings", "Dwellings", "Dwellings", "Dwellings", "Dwellings"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Rating Occupancy/  Construction", "field_values": ["Habitational - JM / NC", "Habitational - JM / NC", "Habitational - JM / NC", "Habitational - JM / NC", "Habitational - JM / NC"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Commercial - LRO -Occupancy", "field_values": ["0", "0"], "fact_subtype_id": null}, {"field_name": "Occupied", "field_values": ["Closed to public during renovations"], "fact_subtype_id": null}, {"field_name": "Original Occupancy", "field_values": ["Condo residential", "Apartment", "Condo residential", "Condo residential", "Apartment"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Occupancy Scheme", "field_values": ["ATC", "ATC", "ATC", "ATC", "ATC"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Occupancy Description", "field_values": ["Industrial Building", "Industrial Building", "Office/Warehouse Building", "Industrial Building"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "GL Occupancy", "field_values": ["Non-Fl Retail", "Non-Fl Residential"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Orig SOV Occupancy", "field_values": ["Clubhouse", "Apartments", "Apartments", "Apartments", "Apartments"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "OCCUPANCY CATEGORY", "field_values": ["WHEEL/TIRE MFG", "WHEEL/TIRE MFG", "WHEEL/TIRE MFG", "WHEEL/TIRE MFG", "WHEEL/TIRE MFG"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "% Occupied", "field_values": ["10"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "TENANT", "field_values": ["Bar K", null], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "% Student", "field_values": ["50", "90", "90", "0", "30"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Elderly", "field_values": ["0", "0", "0", "1", "0.16"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% of the Building Occupied", "field_values": ["0.95", "0.95", "0.95", "0.95", "0.95"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Senior", "field_values": ["0", "0", "0", "0"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Student Occupied", "field_values": ["0", "0", "0", "0"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% OCP", "field_values": ["1"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Occupancy", "field_values": ["Hotel", "Hotel", "Hotel", "Hotel", "Hotel"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Elderly Occupancy", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Student Occupancy", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "%  Student Housing", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "%  Senior Housing", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "%  Assisted Living", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Assisted Living", "field_values": ["0", null], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Student Housing", "field_values": ["0", null], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Market Rate", "field_values": ["100", null], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Section 8", "field_values": ["0", null], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Senior Housing", "field_values": ["0", null], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "% Section 42 (LIHTC)", "field_values": ["0", null], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "Percentage Occupied", "field_values": [null], "fact_subtype_id": "OCCUPATION_PERCENT"}, {"field_name": "Above Ground Sq.Ft.", "field_values": ["12160", "12160", "12160", "12160", "12160"], "fact_subtype_id": "OCCUPIED_AREA"}, {"field_name": "Parking Sqft", "field_values": ["63251", "115310"], "fact_subtype_id": "OCCUPIED_AREA"}, {"field_name": "Square Footage Occupied by Insd", "field_values": ["2025", "2025", "2025", "2025", "2025"], "fact_subtype_id": "OCCUPIED_AREA"}, {"field_name": "Detailed Description", "field_values": ["Manufacturing installation flex space", "Manufacturing installation flex space", "Manufacturing installation flex space", "Manufacturing installation flex space"], "fact_subtype_id": "OPERATIONS"}, {"field_name": "Desc.", "field_values": ["Apts", "Apts", "Apts", "Apts", "Apts"], "fact_subtype_id": "OPERATIONS"}, {"field_name": "Operation", "field_values": ["Bar", "Bar", "Bar"], "fact_subtype_id": "OPERATIONS"}, {"field_name": "operation", "field_values": ["Truck Sales & Service", "Truck Sales & Service", "Truck Sales & Service", "Truck Sales & Service", "Truck Sales & Service"], "fact_subtype_id": "OPERATIONS"}, {"field_name": "General Liability Classification", "field_values": ["Apartments", "Apartments", "Apartments", "Apartments", "Apartments"], "fact_subtype_id": "OPERATION_CLASSIFICATION"}, {"field_name": "Classification", "field_values": ["Hotel-All Employees", "Hotel-All Employees", "Hotel-All Employees", "Hotel-All Employees", "Hotel-All Employees"], "fact_subtype_id": "OPERATION_CLASSIFICATION"}, {"field_name": "CLASS", "field_values": ["APT", "APT", "APT", "APT", "APT"], "fact_subtype_id": "OPERATION_CLASSIFICATION"}, {"field_name": "2023 TIV Reported", "field_values": ["6443000", "3062000", "2037000", "2682000", "2493000"], "fact_subtype_id": "OTHER_VALUE_TIV"}, {"field_name": "Owning Entity", "field_values": ["Solana Beeler Park Apartments Investors, LLC", "Solana Beeler Park Apartments Investors, LLC", "Solana Beeler Park Apartments Investors, LLC"], "fact_subtype_id": "OWNER"}, {"field_name": "Dealer", "field_values": ["VATLAND CHRYSLER", "VATLAND HONDA", "VATLAND HONDA", "VATLAND CHRYSLER", "VATLAND CHRYSLER"], "fact_subtype_id": "OWNER"}, {"field_name": "Owning Entity Name", "field_values": ["Silent Enterprises LLC", "Silent Enterprises LLC", "Country Holdings 2 LLC", "MAR - <PERSON><PERSON>", "Mon Ami Properties LLC"], "fact_subtype_id": "OWNER"}, {"field_name": "Ownership 3", "field_values": [null], "fact_subtype_id": "OWNER"}, {"field_name": "Ownership 2", "field_values": [null], "fact_subtype_id": "OWNER"}, {"field_name": "Ownership 1", "field_values": ["24th Pillsbury Associates, LLC", "24th Pillsbury Associates, LLC", "Cedar Commons Investment Fund 1, LLC", "Cedar Commons Investment Fund 1, LLC", "Cedar Commons Investment Fund 1, LLC"], "fact_subtype_id": "OWNER"}, {"field_name": "GARAGE PARKING SPACES", "field_values": ["6", null], "fact_subtype_id": "PARKING_SPACES_COUNT"}, {"field_name": "STRUCTURE PARKING SPACES", "field_values": ["198"], "fact_subtype_id": "PARKING_SPACES_COUNT"}, {"field_name": "Parking Space", "field_values": ["0", "381"], "fact_subtype_id": "PARKING_SPACES_COUNT"}, {"field_name": "PARKING-SPACE COUNTS ‚Ä¢‚Ä¢ MAXIMUM ‚Ä¢‚Ä¢ assigned for insured's use: Self-park", "field_values": [null], "fact_subtype_id": "PARKING_SPACES_COUNT"}, {"field_name": "# of Garages", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "PARKING_SPACES_COUNT"}, {"field_name": "OUT DOOR PARKING - SQ FT", "field_values": ["0"], "fact_subtype_id": "PARKING_SPACES_COUNT"}, {"field_name": "PARKING TYPE", "field_values": [null], "fact_subtype_id": "PARKING_TYPES"}, {"field_name": "Type of Parking", "field_values": ["Open Lot", "Open Lot", "Open Surface and Covered", null], "fact_subtype_id": "PARKING_TYPES"}, {"field_name": "Policy - Total", "field_values": ["2968212", "1474704", "1478856", "1859670", "2099673"], "fact_subtype_id": "POLICY_NUMBER"}, {"field_name": "PRO Policy #", "field_values": ["RT Specialty: VBA955661 00", "RT Specialty: VBA955661 01", "RT Specialty: VBA955661 02", "RT Specialty: VBA955661 03", "RT Specialty: VBA955661 04"], "fact_subtype_id": "POLICY_NUMBER"}, {"field_name": "License Num", "field_values": ["AVL90S", "AVL90S", "AVL92S", "AVL92S"], "fact_subtype_id": "POLICY_NUMBER"}, {"field_name": "Policy", "field_values": ["1", "1", "1", "1", "2"], "fact_subtype_id": "POLICY_NUMBER"}, {"field_name": "Related policy", "field_values": ["Business Automobile", "Business Automobile", "Business Automobile", "Business Automobile", "Business Automobile"], "fact_subtype_id": "POLICY_NUMBER"}, {"field_name": "Has any policy or coverage been declined, cancelled or non-renewed during the prior 3 years (not applicable in MO)", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "POLICY_OR_COVERAGE_DECLINED_CANCELED_PAST_THREE_YEARS"}, {"field_name": "Fence Height", "field_values": ["5'", null], "fact_subtype_id": "POOL_FENCE_HEIGHT"}, {"field_name": "Highest Number of Stories", "field_values": ["3", "3", "3", "3", "3"], "fact_subtype_id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"}, {"field_name": "Acq  Date", "field_values": ["2021-04-01 00:00:00", "2021-04-01 00:00:00", "2021-06-03 00:00:00"], "fact_subtype_id": "PREMISES_DATE_ADDED"}, {"field_name": "DATE", "field_values": ["2024-12-22 00:00:00"], "fact_subtype_id": "PREMISES_DATE_ADDED"}, {"field_name": "Owned since", "field_values": ["2023", "2023", "2023", "2023", "2023"], "fact_subtype_id": "PREMISES_DATE_ADDED"}, {"field_name": "PURCHASED", "field_values": ["2021-12-01 00:00:00", "2014", "2018-01-01 00:00:00"], "fact_subtype_id": "PREMISES_DATE_ADDED"}, {"field_name": "Interest", "field_values": ["Owner", "Owner", "Owner", "Owner", "Owner"], "fact_subtype_id": "PREMISES_INTEREST_TYPE"}, {"field_name": "Structure Description", "field_values": ["Owner's House", "Chapel", "Grandma's House", "Cook House", "Lodge"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "NOTES ON MAJOR RENO", "field_values": ["The property received a significant renovation in 2012. The renovation improvements were widespread across the property and included most interior and exterior finishes and major systems.", null], "fact_subtype_id": "PREMISES_NOTES"}, {"field_name": "Other Revs  Notes", "field_values": [null], "fact_subtype_id": "PREMISES_NOTES"}, {"field_name": "NOTES AND CHANGES", "field_values": ["No Running Water - reserved for <PERSON>'s personal use & are used occassionally", null], "fact_subtype_id": "PREMISES_NOTES"}, {"field_name": "Additional Notes on Building Breakdown", "field_values": ["Vacant 5,185 sq ft -  Built out for Art Gallery (2 residential apartments above) Building Owned by Condo Association ", "10 units total, 5 vacant units office/retail mixed-use parking lot space count # - ?", "Vacant 5 total units - 1,800 sq ft - Commercial Space, 1st Floor Vacant - 1X Res unit 700 sq ft, 1st floor,  1x res unit 700 sq foot 2nd floor 2x res units 800 sqft, 2nd 3rd floor/attic space and basement not included in sq footage", "2 units 2,700 sq ft - Restaurant with basement included in sq footage 1,500 sq ft - Apartment ", "Vacant - Old Bank Building  2,264 square foot finished basement  surrounded by location 51"], "fact_subtype_id": "PREMISES_NOTES"}, {"field_name": "Update Notes:", "field_values": ["Our policy is to inspect the plumbing, wiring, and HVAC systems any time we have a change in tenants. Otherwise, if a tenant calls with an issue, we'll address it then. This year we have replaced several rooftop HVAC units at 5350 S 129th. That building has also had a number of updates to the wiring and plumbing systems. That building was vacant for several years before we purchased it and the systems needed a great deal of work. The building has been updated and is 80% occupied at this time. The big news concerning the condition of our buildings relates to the roofs. All of our buildings received a new roof sometime between late 2022 and early 2024. Some of these were funded by our our previous insurer. The balance were at our expense. The new roofs are all TPO on new insulation.", null], "fact_subtype_id": "PREMISES_NOTES"}, {"field_name": "Notes/Updates", "field_values": ["plumbing &electrical updated in 2021 ", null], "fact_subtype_id": "PREMISES_NOTES"}, {"field_name": "Notes/Comments", "field_values": ["Ace of Blades -- Per section 12 of lease agreement the Landlord agrees it will maintain all-risk property insurance policy insuring the building.", "Ace of Blades -- Per section 12 of lease agreement the Landlord agrees it will maintain all-risk property insurance policy insuring the building.   ", null], "fact_subtype_id": "PREMISES_NOTES"}, {"field_name": "Gift Shops", "field_values": ["239348"], "fact_subtype_id": "PRODUCT_ANNUAL_GROSS_SALES"}, {"field_name": "Gross Sales - Fabrics", "field_values": ["40000000"], "fact_subtype_id": "PRODUCT_ANNUAL_GROSS_SALES"}, {"field_name": "Gross Sales Paper Mfg.", "field_values": ["205714"], "fact_subtype_id": "PRODUCT_ANNUAL_GROSS_SALES"}, {"field_name": "Description of Work", "field_values": ["Plumbing demo, Steam traps", "Replacing pl fixuters on exsing roughing ", "Plumbing Remodeling", "New Construction", "Plumbing Remodeling"], "fact_subtype_id": "PRODUCT_COMPLIES_WITH_STANDARDS"}, {"field_name": "Appartment - Garden (unit)", "field_values": ["100"], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Apartment Buildings -- Garden (Condominium) (unit)", "field_values": ["1"], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Apartment Buildnigs (other)", "field_values": [null], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "WATER FEATURE (unit)", "field_values": ["1", null], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Park Onwed Units Rented to Others", "field_values": [null], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Market Units", "field_values": ["24", "89", null], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Section 42 Units", "field_values": ["209", "214", null], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Primary livestock - Cattle/Bison - Custom Fed (unit)", "field_values": ["12000"], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Secondary Livestock - Cattle Bison (unit)", "field_values": ["2000"], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Composite - Manufacturing ... (unit)", "field_values": ["2413", null], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Condominiums - Residential - (Association Risk Only) (other)", "field_values": ["219"], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Condominiums-residential (association risk only) (unit)", "field_values": ["57"], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "Additional Insured(s) (unit)", "field_values": ["1"], "fact_subtype_id": "PRODUCT_NUMBER_OF_UNITS"}, {"field_name": "ALARM SYSTEM", "field_values": ["No", "No", "No", "No", null], "fact_subtype_id": "PROJECT_ALARM_SYSTEMS_WORK"}, {"field_name": "Central  Alarm", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "PROJECT_ALARM_SYSTEMS_WORK"}, {"field_name": "Alarms ?", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "PROJECT_ALARM_SYSTEMS_WORK"}, {"field_name": "BOILER MACHINERY COVERAGE (Y/N)", "field_values": ["Y", "Y", "Y", "Y", "Y"], "fact_subtype_id": "PROJECT_BOILER_WORK"}, {"field_name": "BOILER", "field_values": ["Yes", "Yes", null], "fact_subtype_id": "PROJECT_BOILER_WORK"}, {"field_name": "Boiler Y/N", "field_values": ["N", "Y", "Y", "N", "N"], "fact_subtype_id": "PROJECT_BOILER_WORK"}, {"field_name": "# of Stories Below Grade", "field_values": ["0", "0", "0"], "fact_subtype_id": "PROJECT_DEPTH_OF_WORK"}, {"field_name": "Estimated Completion", "field_values": ["2024-12-16 00:00:00", "2026-09-08 00:00:00"], "fact_subtype_id": "PROJECT_END_DATE"}, {"field_name": "COMPLETION DATE", "field_values": ["Feb 23'", "Jan 23'", "May 23'", "June 23'", "June 23'"], "fact_subtype_id": "PROJECT_END_DATE"}, {"field_name": "rate/1,000 of Contract Cost (Project specific)", "field_values": ["44000000"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Total Job Cost (total cost)", "field_values": ["6495000"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Contractor Subcontracted Work in conection with construction, reconstruction, repair or erection - (total cost)", "field_values": ["89123761"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Contractors - subcontracted work in connection with building construction, reconstruction, repai (total cost)", "field_values": ["1725000", null], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "TOTAL CONTRACT PRICE", "field_values": ["6328932.17", "1971739.43", "1751835.65", "1564414.45", "4881117.23"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Ground Up (total cost)", "field_values": ["101488832"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Construction Cost (total cost)", "field_values": ["7500000"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Est. CV", "field_values": ["98808148"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Construction Costs - Painted Tree Phase II (total cost)", "field_values": ["27000000"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Hard Costs", "field_values": ["132863998"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Hard (Contract) Costs (costs)", "field_values": ["26476000"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Contractors (total cost)", "field_values": ["2550000"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Hard Costs (total cost)", "field_values": ["12500000"], "fact_subtype_id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}, {"field_name": "Roof System", "field_values": ["Steel", "Steel", "TPO on Steel deck", "TPO on Steel deck", "Metal"], "fact_subtype_id": "PROJECT_OTHER_ROOFING_METHODS"}, {"field_name": "type of plumbing?", "field_values": ["Plumbing removed.  Elevator shaft (no elevator car), rooftop heating & air conditioning units remain.", "Copper", "Copper", "Copper"], "fact_subtype_id": "PROJECT_PLUMBING_WORK"}, {"field_name": "Estimated Project Start", "field_values": ["2023-07-13 00:00:00", "2023-09-08 00:00:00"], "fact_subtype_id": "PROJECT_START_DATE"}, {"field_name": "START DATE", "field_values": ["May 22'", "April 22'", "June 22'", "Aug 22'", "Dec 21'"], "fact_subtype_id": "PROJECT_START_DATE"}, {"field_name": "Construction Start Date", "field_values": ["2024-06-01 00:00:00"], "fact_subtype_id": "PROJECT_START_DATE"}, {"field_name": "Sub-Contractors Cost ((c))", "field_values": ["120000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work connection with building - one or two family dwellings (total cost)", "field_values": ["7000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "General Contractor-Subcontractors (Resid) (total cost)", "field_values": ["3800000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "General Contractor-Subcontractors (Comml) (total cost)", "field_values": ["200000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted Work - 1-2 Story Family Dwellings (total cost)", "field_values": ["1450000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted work in connection with construction, reconstruction, erection, or repair - not buildings - not otherwise classified (NOC) (total cost)", "field_values": ["25000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors- Subcontract ... (total cost)", "field_values": ["3623391"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "CONTRACTORS-SUBCONTRACTED (total cost)", "field_values": ["1900000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "CONTR-SUB-REPAIR-N/BLDG-NO (total cost)", "field_values": ["500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors -Subcontracted work-inconnection with building construction, reconstruction, repair or erection-one or two family dwellings (total cost)", "field_values": ["2400000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-subcontracted work in connection with construction, reconstruction -NOC (total cost)", "field_values": ["800000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work connection with construction, reconstruction, erection or repair - (total cost)", "field_values": ["238402"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subbed Work(+39.08MM in material) NOT RATED (total cost)", "field_values": ["70000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-subcontracted (total cost)", "field_values": ["6087000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Wok - in connection with construction, Reconstruction, Erection or repair- (total cost)", "field_values": ["7000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Work In connection with street or Highway Construction, or repair, n (total cost)", "field_values": ["2700000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Project Specific (total cost)", "field_values": ["27000000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subs (total cost)", "field_values": ["85000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Work In Connection With Building Construction, Reconstruction, Rep (total cost)", "field_values": ["3400000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - sub contracted work connection with construction, reconstruction, erection or repair (total cost)", "field_values": ["1000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work - connection with construction, reconstruction, erection or repair (total cost)", "field_values": ["35323"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontractors Costs (r)", "field_values": ["300000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "sub-cost", "field_values": ["200000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted work - construction, repair, erection - 1 or 2 family dwellings (total cost)", "field_values": ["23000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "CONTRACTORS SUB STREET HIGHWAY,NOT ELEVA (total cost)", "field_values": ["5000000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work - connection with building construction, reconstruction, repai (total cost)", "field_values": ["3500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub Costs (other)", "field_values": ["150000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted Work - Commercial and Residental NOC (total cost)", "field_values": ["680000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontract ...", "field_values": ["7000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub cost (other)", "field_values": ["162000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors Sub of Buildings (total cost)", "field_values": ["15000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Work - In Connection With Construction, Reconstruction, Erection or Repair-Not Buildings (total cost)", "field_values": ["700000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub contractor cost (other)", "field_values": ["2500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontractors - NOC (total cost)", "field_values": ["100000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontractor (total cost)", "field_values": ["40000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-subcontracte ... (total cost)", "field_values": ["200000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-subcontracted work in connection with construction, reconstruction, repair or erection o (total cost)", "field_values": ["2000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracting Cost", "field_values": ["4000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors -Subcontracted Work in Connection Reconstruction Repair Erection (total cost)", "field_values": ["8470000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted Costs", "field_values": ["30000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work - in connection with construction, reconstruction, erection or repair - not buildings - NOC (total cost)", "field_values": ["120036"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub Contractors (total cost)", "field_values": ["1450000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Work In Connection With Construction, Reconstruction, Ere (total cost)", "field_values": ["375000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work- in connection with construction", "field_values": ["4000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-Subcontract ... (total cost)", "field_values": ["27000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted Costs (other)", "field_values": ["300000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontractors (cost to insured)", "field_values": ["200000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work in connection with oil and gas field construction, reconstruction (total cost)", "field_values": ["25000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub-Contractor (total cost)", "field_values": ["225000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors- Subcontracted Work- In Connection With Construction, Reconstruction, Erection Or Repair- Not Buildings (total cost)", "field_values": ["200000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-Subcont Work ... (other)", "field_values": ["275000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontract ...", "field_values": ["5000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractor (other)", "field_values": ["1462000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted Work - 1 or 2 Family Dwellings (total cost)", "field_values": ["3349615", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Work In Connection With Construction, Reconstruction, Repair or Erection (total cost)", "field_values": ["5000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work in connection with construction, reconstruction, erection or repair - (total cost)", "field_values": ["7000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Work In Connection with Construction, Reconstruction, erec (total cost)", "field_values": ["2500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub-Contracted Work - Nevada (total cost)", "field_values": ["450000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work - in connection with construction, reconstruction, repair or erection - not buildings - not otherwise classified (totcost)", "field_values": ["225000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted Cost (other)", "field_values": ["200000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-not Building (total cost)", "field_values": ["4500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors -- Subcontracted Work -- In Connection with Building Construction, Reconstruction, Rep (total cost)", "field_values": ["10000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontractors", "field_values": ["772000", "53400"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "91585 - Contractors Sub-Contracted Work", "field_values": ["1487500"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted Work (appended)", "field_values": ["145000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-Suncontracted Work (total cost)", "field_values": ["1000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Insured subcontractors (total cost)", "field_values": ["800000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subs (total cost)", "field_values": ["600000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work - connection with oil and gas field construction, reconstruction or re (total cost)", "field_values": ["850000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors -- Subcontracted Work -- in Connection with Construction, Reconstruction, Rep (total cost)", "field_values": ["251400", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Work In Connection With Construction, Reconstruction, Erection Or Repai", "field_values": ["7630000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Of Buildings (total cost)", "field_values": ["6500000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-Subcontracted Work in connection with Street or Highway const, Reconstruction or Repair (total cost)", "field_values": ["8080454"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work- buildings - one or two family dwellings- contractors office/storage (total cost)", "field_values": ["6000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work - in connection with building construction, reconstruction, repair or erection - one or two family dwellings (grsales)", "field_values": ["475000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "CONTRACTORS SUB NOT BUILDING NOC (total cost)", "field_values": ["30000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted Work", "field_values": ["175000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontractors (other)", "field_values": ["500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Constn Subcontracted in Connection with Constn or reconstn -- not buildings (total cost)", "field_values": ["300000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Construction Costs (total cost)", "field_values": ["160000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Residential Land Development - Cost of Subcontractors (total cost)", "field_values": ["8400000", "2100000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "SUB COSTS", "field_values": ["220000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub Cost", "field_values": ["100000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted labor (total cost)", "field_values": ["2500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted (total cost)", "field_values": ["15000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "SUBS", "field_values": ["1500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "SUBCOSTS", "field_values": ["10151827", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work - connection with construction, reconstruction, repair or erecti (total cost)", "field_values": ["85000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Work (other)", "field_values": ["200000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Oil or Gas Well Servicing by Contractor (3)", "field_values": ["6000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-Subcontracted Reconstruction, Repair (total cost)", "field_values": ["300000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "HVAC Contractor (total cost)", "field_values": ["75000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors -- Subcontracted Work -- In Connection with Construction, Reconstruction, Rep (total cost)", "field_values": ["4000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work - in connection with construction, reconstruction, repair or erection of buildings NOC (total cost)", "field_values": ["19000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Subcontracted Costs - in connection with building construction, reconstruction, repair or erection - one or two family dwellings (total cost)", "field_values": ["1800000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub-Contract Cost", "field_values": ["600000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - Subcontracted Work In Connection With Construction, Reconstruction, Repair Or Erectio (total cost)", "field_values": ["69600000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "C0NTRACTOR'S - SUBS (total cost)", "field_values": ["1700000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "CONTRACTORS SUBCONTRACTED WORK - IN CONNECTION WITH BUIL (premium basis payroll)", "field_values": ["1775496", "1775496"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "DESCRIPTION SubCosts (total cost)", "field_values": ["1000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work in connection with bridge, tunnel or elevated street or highway cons (total cost)", "field_values": ["375000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors - subcontracted work - connection with construction, reconstruction, repair or erection o (total cost)", "field_values": ["1500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub contracted cost", "field_values": ["19000002400", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "CONTRACTORS-SUB WK/CONST,REP,ERE,-NO/BLDG-N OC (total cost)", "field_values": ["1500000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-Subcontracted work - inconection with construction, reconstruction, erection or repair - (total cost)", "field_values": ["2850000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-Sub Cont (total cost)", "field_values": ["550000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractor - Subcontractor (total cost)", "field_values": ["3000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Sub Cost (other)", "field_values": ["1200000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors- sub work- in connection with construction, reconstruction, erection of buildings- NOC (total cost)", "field_values": ["103068"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-one Or Two ...", "field_values": ["4978000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors-Executive Su ...", "field_values": ["1245000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors- Of Buildings", "field_values": ["40000000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors- Of Buildings (other)", "field_values": ["40000000", null], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Insulation Contractor-Sub contracting cost (total cost)", "field_values": ["550000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "Contractors -subcontracted work - connection with construction, reconstruction, repair or erection o (total cost)", "field_values": ["5000"], "fact_subtype_id": "PROJECT_SUBCONTRACTORS_COST"}, {"field_name": "EFIS?", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "PROJECT_USE_OF_EIFS"}, {"field_name": "Structural EIFS (Exterior Insulation and Finish System)", "field_values": ["Yes", "Yes"], "fact_subtype_id": "PROJECT_USE_OF_EIFS"}, {"field_name": "Is there any EIFS on the building? (Y or N)", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "PROJECT_USE_OF_EIFS"}, {"field_name": "Shatter Proof Windows?", "field_values": ["Yes", null], "fact_subtype_id": "PROJECT_WATER_PROOFING_WORK"}, {"field_name": "Gated Windows?", "field_values": ["Yes", null], "fact_subtype_id": "PROJECT_WINDOW"}, {"field_name": "Description of Location", "field_values": ["Home Office", "Engineering", "Fab Shop", "Mechanic's Shop", "Truck Body Plant"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Building description", "field_values": ["Dairy Queen Store#91", "Dairy Queen Store#3 ", "Dairy Queen Store#5 ", "Dairy Queen Store#23 ", "Dairy Queen Store#41 "], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Building description (# of units)", "field_values": ["Four-plex", "Four-plex", "Four-plex", "Four-plex", "Four-plex"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Location/Description", "field_values": ["Onix Delray Beach Condominiums"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Building Description", "field_values": ["Corporate Office", "Branch", "Branch", "Branch", "Branch"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Building Desc.", "field_values": ["Apartment Building"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Location Description", "field_values": ["DEN - Englewood Marketplace", "DEN - Pioneer Hills", "STL - <PERSON> (9610)", "BIL - Lava Island Billings"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "BLDG Description", "field_values": ["residential co-op"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Property Description", "field_values": ["APT", "APT", "APT", "APT", "APT"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Description of Property", "field_values": ["Office Park", "Office Park", "Office Park", "Office Park", "Office Park"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Property description", "field_values": ["Distribution Center/Warehouse", "Storage Warehouse", "Distribution Center/Warehouse", "Distribution Center/Warehouse", "Office/Warehouse"], "fact_subtype_id": "PROPERTY_DESCRIPTION"}, {"field_name": "Occupant", "field_values": ["Owner <PERSON><PERSON>"], "fact_subtype_id": null}, {"field_name": "Operations", "field_values": ["Repair", "Body Shop"], "fact_subtype_id": "OPERATIONS"}, {"field_name": "Property Type II", "field_values": ["Garden", "Garden", "Garden", "Garden", "Urban Garden"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Apartment Type", "field_values": ["Garden Style", "Garden Style", "Garden Style", "Garden Style"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Type of Fuses (S-Type)", "field_values": [null], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Facility Type", "field_values": ["Office & Storage", "Vacant Property", "Office & Storage", "Corporate Headquarters, Office & Storage", "Office & Storage"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Rental Type", "field_values": ["Conventional", "Conventional", "Conventional", "Affordable", "Affordable"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Type of Commercial Tenants", "field_values": [null], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Property Manager", "field_values": ["<PERSON>", "Franklin Group", "Franklin Group", "Franklin Group", "Franklin Group"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Propety Name", "field_values": ["Serenity - Location 1", "Serenity - Location 12", "Serenity - Location 4", "Serenity - Location 16", "Serenity - Location 8"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "PROPERTY TYPE 2", "field_values": [null], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Property Type_1", "field_values": ["Building", "Eqpt", "Pool/Spa", "Outdoor Prop", "Other Structures"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "PROPERTY COVERAGES", "field_values": ["Personal Propety of Insured", "Building", "Personal Propety of Insured", "Building", "Personal Propety of Insured"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Coverage Type", "field_values": ["Limit"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Property Type", "field_values": ["Owned (50% ownership)"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Property Type 2", "field_values": ["0008374", "20404027", "5008418"], "fact_subtype_id": "PROPERTY_TYPE"}, {"field_name": "Part-Time", "field_values": ["8", "15", "18", "16", "17"], "fact_subtype_id": "PT_EMPLOYEES_COUNT"}, {"field_name": "Part Time", "field_values": ["5", "6"], "fact_subtype_id": "PT_EMPLOYEES_COUNT"}, {"field_name": "Annual Rental Income", "field_values": ["10_000"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Avg Rent per Unit", "field_values": ["2728", null], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Excess Rent Value", "field_values": ["1134921", "393012", "120494", "569771", "25990"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Rent Value (Actuals)", "field_values": ["4061460", "1283040", "1008955", "3094536", "545791"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Rent Value", "field_values": ["2926539", "890028", "888461", "2524765", "519801"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Monthly Rent Amt/2023", "field_values": ["44950", "15500", "9810", "13050", "8400"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Rental Income.1", "field_values": ["188786", "1166255.4", "1209198.72", "1605682.2", "1724403.36"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Annual Rent (18 Mo)", "field_values": ["11416000", "8767000", "11530000", "15267000"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "BI/Rental Income 2024-2025", "field_values": ["715000", "60000", "50000", "105000", "40000"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Average Montlhly Rental Rate", "field_values": ["1100"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Rent Income (31)", "field_values": ["5998752", null], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Annual   Rent", "field_values": ["3337980", "231000", "1148400", "456000", "2265600"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Annual Rent", "field_values": ["3337980", "436800", "231000", "250800"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Rent Amount", "field_values": ["703225", "233000", "1000000", "540000", "335000"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Rent $/Month", "field_values": ["111277.5", null], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "2023-2024 Rental Income", "field_values": ["19372726.064", "19583481.556", "16833560.74", "16186683.812", "11201968.928000003"], "fact_subtype_id": "RENT_INCOME"}, {"field_name": "Building (Replacement $$)", "field_values": ["1195260", "1200000", "765000", "326400", "0"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "2024-2025 Bldg. Limit replacement Cost", "field_values": ["1565600", "1393200", "1731600", "1393200", "1565600"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Replacement Value", "field_values": ["1304750", "2621060", "2609500", "2609500", "2609500"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Permanent Building Replacement Cost Value - Inclusive of ALL Buildings  (Do not include land values)", "field_values": ["66224180", "67924951", "11010942", "10606310", "6430167"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Building Replacement Value", "field_values": ["6805000"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Replacement Cost", "field_values": ["7588694"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Bldg RC", "field_values": ["5000000", "9725433", "375000", "688908", "720312"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Replacement Cost 2017-2018", "field_values": ["3961440", "3961440", "3961440", "3961440", "3961440"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Replacement Cost per Sites.1", "field_values": ["1054130", "1113751", "14384687"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Replacement Cost per Sites Total", "field_values": ["2757054", "1223351", "23360261", "9350000", "1021400"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Replacement Cost per Sites Buildings", "field_values": ["833900", "109600", "2100000", "7350000", "56400"], "fact_subtype_id": "REPLACEMENT_COST"}, {"field_name": "Forcible Robbery Score", "field_values": ["C", "C", "C", "C", "D"], "fact_subtype_id": "ROBBERY_GRADE"}, {"field_name": "Roof Strapped", "field_values": ["5", null], "fact_subtype_id": "ROOF_ANCHOR"}, {"field_name": "Roof strapped (if ISO 1 OR 2)", "field_values": ["1-Metal/Bolt Anchors", "1-Metal/Bolt Anchors", "1-Metal/Bolt Anchors", "1-Metal/Bolt Anchors", "1-Metal/Bolt Anchors"], "fact_subtype_id": "ROOF_ANCHOR"}, {"field_name": "Roof Frame/Roof Deck", "field_values": ["101-<PERSON><PERSON> Reinforced Conc", null], "fact_subtype_id": "ROOF_ANCHOR"}, {"field_name": "<PERSON><PERSON>", "field_values": ["Unknown/Default", "Unknown/Default", "Unknown/Default", "Structurally Connected", "Unknown/Default"], "fact_subtype_id": "ROOF_ANCHOR"}, {"field_name": "Roof Anchorage  (if ISO 1 OR 2 or any other with wood framed roof)", "field_values": ["105-Structural", null], "fact_subtype_id": "ROOF_ANCHOR"}, {"field_name": "Roof Framing/Structure", "field_values": ["1-Poured concrete", "1-Poured concrete", "1-Poured concrete", "4-Light steel purlin", "4-Light steel purlin"], "fact_subtype_id": "ROOF_ANCHOR"}, {"field_name": "<PERSON><PERSON>", "field_values": ["5", null], "fact_subtype_id": "ROOF_ANCHOR"}, {"field_name": "<PERSON><PERSON>ng", "field_values": ["0-Unknown", "1-Poured concrete", "2-Precast concrete", "3-Heavy Steel Frame", null], "fact_subtype_id": "ROOF_ANCHOR"}, {"field_name": "Roof Equipment Hurricane Bracing", "field_values": ["3=N/A No Roof Equipment", "3=N/A No Roof Equipment", "3=N/A No Roof Equipment", "3=N/A No Roof Equipment", "1=Properly installed with adequate anchorage"], "fact_subtype_id": "ROOF_COVER"}, {"field_name": "Roof Covering", "field_values": ["4", null], "fact_subtype_id": "ROOF_COVER"}, {"field_name": "Type of Roof Covering", "field_values": ["104-Built-up roof NO gravel WITHOUT presence of gutters", null], "fact_subtype_id": "ROOF_COVER"}, {"field_name": "Roof Material", "field_values": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "fact_subtype_id": "ROOF_COVER"}, {"field_name": "Roof Covering - US T/H & All Other Countries Hurricane", "field_values": ["Built-up 4", null], "fact_subtype_id": "ROOF_COVER"}, {"field_name": "Shape Configuration", "field_values": ["1=Regular", "1=Regular", "1=Regular", "1=Regular", "2=Irregular"], "fact_subtype_id": "ROOF_SHAPE"}, {"field_name": "Shape of roof", "field_values": ["2-Hip/Combination", "2-Hip/Combination", "2-Hip/Combination", "2-Hip/Combination", "2-Hip/Combination"], "fact_subtype_id": "ROOF_SHAPE"}, {"field_name": "<PERSON><PERSON><PERSON> of Roof", "field_values": ["2", null], "fact_subtype_id": "ROOF_SHAPE"}, {"field_name": "<PERSON><PERSON>", "field_values": ["9", "9", "9", "9"], "fact_subtype_id": "ROOF_SHAPE"}, {"field_name": "Roof Geometry", "field_values": ["2", null], "fact_subtype_id": "ROOF_SHAPE"}, {"field_name": "Roof Covering Type", "field_values": ["Shingle", "Tile", "Shingle", "Shingle", "Shingle"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Roof Construction", "field_values": ["Tar & Gravel", "Concrete Tile", "Compos. 55 MPH", "<PERSON><PERSON>", "<PERSON><PERSON>"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Roof Con-struction", "field_values": ["Shingle", "Shingle", "Shingle", "Shingle", "Shingle"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "<PERSON><PERSON>", "field_values": ["TPO", "EPDM / TPO"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Roof type If Other", "field_values": ["Poly", "Poly", "Poly", "Poly", "METAL"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "<PERSON><PERSON> Constr", "field_values": ["** Shingles 80%; TPO 19.75%; Metal .25%", null], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Roofing Material", "field_values": ["Asphalt/Fi<PERSON>lass", "Asphalt/Fi<PERSON>lass", "Asphalt/Fi<PERSON>lass", "Asphalt/Fi<PERSON>lass", "Asphalt/Fi<PERSON>lass"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "ROOF & Type", "field_values": ["1999", "1989", "2005", "2000", "2002"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Roof Deck", "field_values": ["Wood Planks", "Wood Planks", "Wood Planks", "Wood Planks", "Wood Planks"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Roof Style & Material", "field_values": ["Flat Rubber Membrane", null], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Roof Type - Other", "field_values": ["Modified Bitumen", null], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "<PERSON><PERSON>", "field_values": ["108-<PERSON>", "102-Precast Concrete", "108-<PERSON>,108-<PERSON>ame", "108-<PERSON>", "108-<PERSON>"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Orig SOV Roof Type", "field_values": ["Shingle", "Shingle", "Shingle", "Shingle", "Shingle"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_values": ["6 - <PERSON><PERSON>", null], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Roof Style", "field_values": ["Shingled", "Shingled", "Shingled", "Shingled", "Shingled"], "fact_subtype_id": "ROOF_TYPE"}, {"field_name": "Est. Annual Receipts", "field_values": ["1930000", "1930000", "2500000", "2500000", "2000000"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "Original Est. Annual Receipts", "field_values": ["1930000", "2500000", "2000000", "2360000", "1820000"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "2020 Est Annual Receipts - REVISED", "field_values": ["1284868.4", "2052660.76", "1627496.61", "1708585.8900000001", "1431341.56"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "2023 Est Annual Receipts", "field_values": ["3000000", "2300000", "2000000", "1800000", "1800000"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "2025 Est Annual Receipts", "field_values": ["3100000", "3340000", "2440000", "1920000", "1790000"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "Estimated Exposure", "field_values": ["96", "2811", "2791", "80", "10000"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "Estimated Exposure.1", "field_values": ["96", "2811", "2791", "80", "10000"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "Estimated Annual Receipts (r)", "field_values": ["5800000"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "Estimated Annual Delivery Revenues", "field_values": ["250000", "250000"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "Estimated Annual Catering Revenues", "field_values": ["500000", "500000"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "Projected SHL Revenue in 2025", "field_values": ["31609", "21371", "15000", "0", "30433"], "fact_subtype_id": "SALES_ESTIMATE"}, {"field_name": "SURVELLANCE CAMERAS", "field_values": ["YES", "YES", "YES", "YES", "YES"], "fact_subtype_id": "SECURITY_CAMERAS"}, {"field_name": "Security System", "field_values": ["Yes", "Yes", "Yes", null], "fact_subtype_id": "SECURITY_CAMERAS"}, {"field_name": "Cameras in Parking Garage? (Yes / No)", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "SECURITY_CAMERAS"}, {"field_name": "CAM", "field_values": [null], "fact_subtype_id": "SECURITY_CAMERAS"}, {"field_name": "Cameras", "field_values": ["yea"], "fact_subtype_id": "SECURITY_CAMERAS"}, {"field_name": "Type of Smoke Detector?   (Hard-Wired or Battery-Powered).1", "field_values": ["Hard-Wired", "Battery-Powered", null], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Smoke Detectors Battery or Hardwired", "field_values": ["Hard"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Type of Smoke Detectors in Common Areas?", "field_values": ["B/HW", "B/HW", "B/HW", "B/HW", "B/HW"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "COPE   smoke alarm type", "field_values": ["Hard Wired", "HW & Battery", "HW & Battery", "Hard Wired"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Smoke detectors in common areas?", "field_values": ["Battery", "Battery", "Battery", "Battery", "Battery"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "SMOKE ALARM TYPE", "field_values": ["Hardwired", "Hardwired", "Hardwired", "Hardwired", "Hardwired"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Smoke Alarm Type (HW/Battery/Both)", "field_values": ["Hard Wired", null], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Type Smoke", "field_values": ["Hard Wire", "Hard Wire", "Hard Wire", "Hard Wire", "Hard Wire"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Smoke Detector", "field_values": ["Battery ", null], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Smoke Det type", "field_values": ["Hard-Wired", "Hard-Wired", null], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Hallway Smoke Detectors", "field_values": ["HARD WIRED", null], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Building Sprinkler Percentage", "field_values": ["Partial"], "fact_subtype_id": "SPRINKLER"}, {"field_name": "Sprinklered or Partially Sprinklered", "field_values": ["Full", null], "fact_subtype_id": "SPRINKLER"}, {"field_name": "Protection/ Sprinklered", "field_values": ["Dry Sprinkler", "Dry Sprinkler", "Dry Sprinkler"], "fact_subtype_id": "SPRINKLER"}, {"field_name": "Sprinkler Type", "field_values": [null], "fact_subtype_id": "SPRINKLER"}, {"field_name": "Sprinklered?", "field_values": ["FULLY", "FULLY", "FULLY", "FULLY", "FULLY"], "fact_subtype_id": "SPRINKLER"}, {"field_name": "Sprinklered (Type)", "field_values": ["Wet", "Wet", "Wet", "Wet"], "fact_subtype_id": "SPRINKLER"}, {"field_name": "Sprinkler System (Yes/No & Percentage)", "field_values": ["100% - new fire sprinkler system with new pump aug 2024"], "fact_subtype_id": "SPRINKLER"}, {"field_name": "% Sprinkered", "field_values": ["1"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprklr %", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "% of Fire Sprinkler Coverage", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "% Sprinklers Needed", "field_values": ["Yes", null], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "% of Building that has Sprinkler System", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "SPRINKLER_AREA_COVERAGE_PERCENT"}, {"field_name": "<PERSON><PERSON>", "field_values": ["1"], "fact_subtype_id": "SPRINKLER_AREA_COVERAGE_PERCENT"}, {"field_name": "% of Building protected by Sprinklers", "field_values": ["1"], "fact_subtype_id": "SPRINKLER_AREA_COVERAGE_PERCENT"}, {"field_name": "% Sprink", "field_values": ["0", null], "fact_subtype_id": "HAS_SPRINKLERS"}, {"field_name": "Sprk", "field_values": ["1", "1", null], "fact_subtype_id": "SPRINKLER_COUNT"}, {"field_name": "Sprnkl %", "field_values": ["1"], "fact_subtype_id": "SPRINKLER_COUNT"}, {"field_name": "# OF BLDGS.1", "field_values": ["26", null], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Bldg or premises - bank or office, merc or mft LRO (unit)", "field_values": [null], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Bldgs or prems - Bank or office, etal (other)", "field_values": [null], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "# of Buidlings", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "# of BLD", "field_values": ["1", "3", "1", "1", "1"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Building - Lessors Risk (other)", "field_values": ["1", null], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Is This a Multi-Tenant Building?", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "# OF OTHER BUILDINGS", "field_values": ["1", null], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Bldg.", "field_values": ["1", null], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Building count   total # of buildings", "field_values": ["15", "2", "8", "0", "14"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Bldgs", "field_values": ["2", "4", "1", "2", "1"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "# BLD", "field_values": ["1"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Building count   # of ancillary buildings", "field_values": ["0", "2", "0", "0", "0"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Count", "field_values": ["112", "28", "29", "31", "32"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Build. No.", "field_values": ["1", "1", "1", "1", "1"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Buildig No.", "field_values": ["1", "1"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Bld #", "field_values": ["1", "2", "3", "1", "2"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Structure #", "field_values": ["1"], "fact_subtype_id": "STRUCTURES_COUNT"}, {"field_name": "Subsidized", "field_values": ["0"], "fact_subtype_id": "SUBSIDIZED_PERCENT"}, {"field_name": "Subsidized Housing", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "SUBSIDIZED_PERCENT"}, {"field_name": "Subsidized Housing %", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "SUBSIDIZED_PERCENT"}, {"field_name": "% Section 42 Subsidized Units", "field_values": ["0", "0", "0", "0"], "fact_subtype_id": "SUBSIDIZED_PERCENT"}, {"field_name": "% Section 8 Subsidized Units", "field_values": ["0", "0", "0.18", "<5%"], "fact_subtype_id": "SUBSIDIZED_PERCENT"}, {"field_name": "SUBSIDIZED Affordable/ Tax Credit", "field_values": ["0.95", "0.95", "0.95", "0.95", "0.95"], "fact_subtype_id": "SUBSIDIZED_PERCENT"}, {"field_name": "# Subsidized", "field_values": ["94", "49", "275"], "fact_subtype_id": "SUBSIDIZED_PERCENT"}, {"field_name": "% Section 8 Subsidized", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "SUBSIDIZED_PERCENT"}, {"field_name": "% Section 42 Subsidized", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "SUBSIDIZED_PERCENT"}, {"field_name": "Tenants I&B", "field_values": ["500000"], "fact_subtype_id": "TENANTS_IMPROVEMENTS_AND_BETTERMENTS"}, {"field_name": "Tets Betterments & Improvements", "field_values": ["10000.0"], "fact_subtype_id": "TENANTS_IMPROVEMENTS_AND_BETTERMENTS"}, {"field_name": "Improvments & Betterments", "field_values": ["250000", "0", "0", "0", "0"], "fact_subtype_id": "TENANTS_IMPROVEMENTS_AND_BETTERMENTS"}, {"field_name": "Improvement & Betterments", "field_values": ["2480000", "1850000", "0"], "fact_subtype_id": "TENANTS_IMPROVEMENTS_AND_BETTERMENTS"}, {"field_name": "Total Ins Value", "field_values": ["601400", "601400", "677900", "3112300", "5428400"], "fact_subtype_id": "TIV"}, {"field_name": "% TOTAL", "field_values": ["0", "0", null], "fact_subtype_id": "TIV"}, {"field_name": "Totals", "field_values": ["3339480", "4625925", "6944634", "3748967", "3696360"], "fact_subtype_id": "TIV"}, {"field_name": "Total Ins. Value", "field_values": ["18592773", "29255238", "90834806", "74659165", "55110651"], "fact_subtype_id": "TIV"}, {"field_name": "INCREASED TIV", "field_values": ["23661060", "22348359", null], "fact_subtype_id": "TIV"}, {"field_name": "Total Insurance Values (TIV)", "field_values": ["20470765", "67210304", "17062505", "59662854", "53178850"], "fact_subtype_id": "TIV"}, {"field_name": "Revised insured  values", "field_values": ["45207608", "*********.6", "*********.24", "*********.49", "16779351"], "fact_subtype_id": "TIV"}, {"field_name": "100%  Insurable  Values", "field_values": ["911776", "911776", "911776", "1036576", "719528"], "fact_subtype_id": "TIV"}, {"field_name": "24-25 TIV", "field_values": ["3716666.*********"], "fact_subtype_id": "TIV"}, {"field_name": "State Auto TIV", "field_values": ["51356050"], "fact_subtype_id": "TIV"}, {"field_name": "Travelers TIV", "field_values": ["11945126", "275000", "1916564", "13597062", "49461188"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insurable Values.1", "field_values": ["100000", "2850000", "250000", "500000", "3375000"], "fact_subtype_id": "TIV"}, {"field_name": "TOTAL  TIV  (RATING)", "field_values": ["76850000"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Value.1", "field_values": ["45307572", "17860464", "16612291", "41808936", "12890167"], "fact_subtype_id": "TIV"}, {"field_name": "Excess Total Insured Value", "field_values": ["11131033", "2203432", "3518589.455", "8084171", "2890167"], "fact_subtype_id": "TIV"}, {"field_name": "Total Policy TIV", "field_values": ["14000", "14000", "451500", "32000", "32000"], "fact_subtype_id": "TIV"}, {"field_name": "2025 TIV Value", "field_values": ["4246598.*********", "1386937.3518810766", "2365569.6918918854", "3101792.*********", "3631224.*********"], "fact_subtype_id": "TIV"}, {"field_name": "TIV/Unit", "field_values": ["152322.875", "196528.125", "161950.84", "157078.525", null], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Values.1", "field_values": ["5891286", "26486380.4", "40159198.72", "36663557.2", "61724403.36"], "fact_subtype_id": "TIV"}, {"field_name": "TOTAL INSURABLE VALUES - TIV", "field_values": ["215670476", "1952000", "1161900", "0"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insurance Values", "field_values": ["10932523"], "fact_subtype_id": "TIV"}, {"field_name": "TIV2024-2025", "field_values": ["7920000", "930000", "274000", "105000", "1290000"], "fact_subtype_id": "TIV"}, {"field_name": "2025 TIV", "field_values": ["931041.28", "698280.9600000001", "1163801.6", "1163801.6", "698280.9600000001"], "fact_subtype_id": "TIV"}, {"field_name": "Blanket Building TIV", "field_values": ["90629801", "3973200", "18102304"], "fact_subtype_id": "TIV"}, {"field_name": "2024 TIV", "field_values": ["8882308.24", "7114089.84", "8383472.24", "1721415.28"], "fact_subtype_id": "TIV"}, {"field_name": "TIV - Harvested Plants", "field_values": ["0"], "fact_subtype_id": "TIV"}, {"field_name": "TIV - Flowering Plants", "field_values": ["0"], "fact_subtype_id": "TIV"}, {"field_name": "TIV - Imma<PERSON> Seedlings", "field_values": ["0"], "fact_subtype_id": "TIV"}, {"field_name": "Total Crop TIV", "field_values": ["0"], "fact_subtype_id": "TIV"}, {"field_name": "TIV - Seeds", "field_values": ["0"], "fact_subtype_id": "TIV"}, {"field_name": "TIV - Mother Plants/ Clone Producers", "field_values": ["0"], "fact_subtype_id": "TIV"}, {"field_name": "TIV - Vegetative Plants", "field_values": ["0"], "fact_subtype_id": "TIV"}, {"field_name": "TIV (33)", "field_values": ["62087652", null], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Values =A+B+C.6", "field_values": ["12502854", "1280916", "24148908", "2781387", "1135260"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Values =A+B+C.4", "field_values": ["11674000", "1196000", "22548000", "2597000", "1060000"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Values =A+B+C.8", "field_values": ["14112596.4525", "1445833.9349999998", "27258079.905", "3139490.5762500004", "1281424.7249999999"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Values =A+B+C.2", "field_values": ["12031271", "1263307", "24306128", "2099144", "977929"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Values =A+B+C.5", "field_values": ["11907480", "1219920", "22998960", "2648940", "1081200"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Values =A+B+C.7", "field_values": ["13440568.049999999", "1376984.7", "25960076.099999998", "2989991.025", "1220404.5"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Values =A+B+C.3", "field_values": ["11674000", "1196000", "22548000", "2597000", "1060000"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insured Values =A+B+C.1", "field_values": ["12031271", "1263307", "24306128", "2099144", "977929"], "fact_subtype_id": "TIV"}, {"field_name": "2025 Insurable Value", "field_values": ["76562888"], "fact_subtype_id": null}, {"field_name": "TOTAL TIV (with crop coverage)", "field_values": ["4166100"], "fact_subtype_id": "TIV"}, {"field_name": "TIV - Pre-Flowering Plants", "field_values": ["292500"], "fact_subtype_id": "TIV"}, {"field_name": "TIV - Clones/Pre-Vegetative", "field_values": ["16100"], "fact_subtype_id": "TIV"}, {"field_name": "2019 MidOak TIV", "field_values": ["1794531", "1803406", "1458730", "1487417", "2209864"], "fact_subtype_id": "TIV"}, {"field_name": "2021 Dist TIV", "field_values": ["2892899", "2893108", "2163634", "2158162", "2956164"], "fact_subtype_id": "TIV"}, {"field_name": "2020 Dist TIV", "field_values": ["2836175", "2836380", "2121210", "2115845", "2898200"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insurance Value", "field_values": ["90033620", "1134000", "8573808", "2951262", "21466755"], "fact_subtype_id": "TIV"}, {"field_name": "Dec 2023 - Dec 2024 Total Insured Values", "field_values": ["243278686.32", "148994091.78", "140792063.7", "136726107.06", "139738706.64000002"], "fact_subtype_id": "TIV"}, {"field_name": "Total Insurable", "field_values": ["867727", "2156360", "1312808", "1858484", "1273104"], "fact_subtype_id": "TIV"}, {"field_name": "Total  Insurance  Value", "field_values": ["98096000", "2854000", "29052000", "39121000", "12818000"], "fact_subtype_id": "TIV"}, {"field_name": "Per Sq Ft.", "field_values": ["68.29538683167992", "73.45869191049914", "68.29538683167992", "70.54185456595265", "68.29538683167992"], "fact_subtype_id": null}, {"field_name": "State Auto RCV", "field_values": ["177.964924074933"], "fact_subtype_id": null}, {"field_name": "Current RCV per Sq Ft", "field_values": ["241.1811865312667", "0", "122.18420957368504", "121.29847394902075", "118.36395549351289"], "fact_subtype_id": null}, {"field_name": "EE Tools", "field_values": ["400000", "50000", "200000", "50000"], "fact_subtype_id": "TOOL_VALUE"}, {"field_name": "Tornado Risk   Level", "field_values": ["Average", "Average", "Elevated", "Elevated", "Elevated"], "fact_subtype_id": "TORNADO_RISK"}, {"field_name": "Power Unit Type", "field_values": ["Extra Heavy Tractor", "Extra Heavy Tractor", "Extra Heavy Tractor", "Extra Heavy Tractor", "Extra Heavy Tractor"], "fact_subtype_id": "TRAILER_BODY_TYPE"}, {"field_name": "Building Ordice Increased Cost of Constru", "field_values": ["5000000.0"], "fact_subtype_id": "TYPICAL_JOB_COST"}, {"field_name": "Building Ordice Demolition Cost", "field_values": ["2500000.0"], "fact_subtype_id": "TYPICAL_JOB_COST"}, {"field_name": "Food Product MFR", "field_values": ["500000.0", null], "fact_subtype_id": "TYPICAL_JOB_COST"}, {"field_name": "USDOT #", "field_values": ["3782889", "3782889", "3782889", "3782889", "4214303"], "fact_subtype_id": "USDOTS"}, {"field_name": "Vacant land (other)", "field_values": ["5", "13", "1", "1"], "fact_subtype_id": "VACANT_LAND_SIZE"}, {"field_name": "Vacant Land (other)", "field_values": [null], "fact_subtype_id": "VACANT_LAND_SIZE"}, {"field_name": "Vacant Land Total Acres", "field_values": [null], "fact_subtype_id": "VACANT_LAND_SIZE"}, {"field_name": "Column1", "field_values": [null], "fact_subtype_id": "VEHICLES_NOTES"}, {"field_name": "Garage Street Address 1", "field_values": ["916 Kendrick Ave", "916 Kendrick Ave", "916 Kendrick Ave", "916 Kendrick Ave", "916 Kendrick Ave"], "fact_subtype_id": "VEHICLES_NOTES"}, {"field_name": "Garage Country", "field_values": ["USA", "USA", "USA", "USA", "USA"], "fact_subtype_id": "VEHICLES_NOTES"}, {"field_name": "Driver Last", "field_values": [null], "fact_subtype_id": "VEHICLES_NOTES"}, {"field_name": "Garage County Name", "field_values": ["BENTON", "BENTON", "BENTON", "BENTON", "BENTON"], "fact_subtype_id": "VEHICLES_NOTES"}, {"field_name": "Territory", "field_values": ["18", "5", "18", "5", "18"], "fact_subtype_id": "VEHICLE_ADDITIONAL_INTEREST"}, {"field_name": "Cust#", "field_values": ["V53", "V54", "V55", "V58", "V59"], "fact_subtype_id": "VEHICLE_ADDITIONAL_INTEREST"}, {"field_name": "Additonal Interests - AI/LP/EL", "field_values": ["BMO Harris Bank, NA, It's Successors &/or Assigns P.O. Box 35704 Billings MT 59110", "BMO Harris Bank, NA, It's Successors &/or Assigns P.O. Box 35704 Billings MT 59111", "BMO Harris Bank, NA, It's Successors &/or Assigns P.O. Box 35704 Billings MT 59112", "BMO Harris Bank, NA, It's Successors &/or Assigns P.O. Box 35704 Billings MT 59116", "BMO Harris Bank, NA, It's Successors &/or Assigns P.O. Box 35704 Billings MT 59119"], "fact_subtype_id": "VEHICLE_ADDITIONAL_INTEREST"}, {"field_name": "Stated Amount", "field_values": ["227359", "354643", "325809", "433710", "382686"], "fact_subtype_id": "VEHICLE_AMOUNT_OF_INSURANCE"}, {"field_name": "STATED AMT", "field_values": ["99000", "13500", "74000", "10000"], "fact_subtype_id": "VEHICLE_AMOUNT_OF_INSURANCE"}, {"field_name": "TOTAL", "field_values": ["13613", "592", "12870", "505"], "fact_subtype_id": "VEHICLE_AMOUNT_OF_INSURANCE"}, {"field_name": "Primary Class", "field_values": ["232", "681", "681", "681", "681"], "fact_subtype_id": "VEHICLE_BODY_CLASS"}, {"field_name": "Class Code (EPIC)", "field_values": ["Extra Heavy Truck", "Extra Heavy Truck", "Extra Heavy Truck", "Extra Heavy Truck", "Extra Heavy Truck"], "fact_subtype_id": "VEHICLE_CLASS_CODE"}, {"field_name": "Special industry class", "field_values": ["83", "83", "83", "83", "83"], "fact_subtype_id": "VEHICLE_CLASS_CODE"}, {"field_name": "Vehicle Class", "field_values": ["1499", "1499", "1499", "1499", "1499"], "fact_subtype_id": "VEHICLE_CLASS_CODE"}, {"field_name": "Auto Class Code.1", "field_values": ["14", "14", "14", "14", "14"], "fact_subtype_id": "VEHICLE_CLASS_CODE"}, {"field_name": "Veh Class", "field_values": ["73980", "73980", "73980", "03419", "73980"], "fact_subtype_id": "VEHICLE_CLASS_CODE"}, {"field_name": "<PERSON><PERSON>", "field_values": ["Yes", null], "fact_subtype_id": "VEHICLE_COLLISION_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "Collision Deduction", "field_values": ["1000", "1000", "1000", "1000", "1000"], "fact_subtype_id": "VEHICLE_COLLISION_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "COLL", "field_values": ["25", "550", "474", "514"], "fact_subtype_id": "VEHICLE_COLLISION_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "COL", "field_values": ["1000", "1000", null], "fact_subtype_id": "VEHICLE_COLLISION_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "Deductibles Coll", "field_values": ["1000", "1000", "1000", "1000"], "fact_subtype_id": "VEHICLE_COLLISION_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "Collision Ded (No Ded = No Cvg)", "field_values": ["1000", "1000", "1000", "1000", "1000"], "fact_subtype_id": "VEHICLE_COLLISION_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "Ded Comp", "field_values": ["Yes", null], "fact_subtype_id": "VEHICLE_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "COMP", "field_values": ["25", "142", "118", "122"], "fact_subtype_id": "VEHICLE_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "COM", "field_values": ["1000", "1000", null], "fact_subtype_id": "VEHICLE_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "Comp", "field_values": ["Varies", "Varies", "Varies", "Varies"], "fact_subtype_id": "VEHICLE_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"}, {"field_name": "Date Acquired", "field_values": ["2019-08-08 00:00:00", "2016-07-06 00:00:00", "2020-02-18 00:00:00", "2019-03-08 00:00:00", "2018-07-12 00:00:00"], "fact_subtype_id": "VEHICLE_DATE_ADDED"}, {"field_name": "Delivery Date", "field_values": ["2023-03-10 00:00:00", "2023-03-10 00:00:00", "2023-01-20 00:00:00", "2023-01-20 00:00:00", "2022-10-19 00:00:00"], "fact_subtype_id": "VEHICLE_DATE_ADDED"}, {"field_name": "Purchase Dt", "field_values": ["09/03/2021", "04/03/2017", "04/18/2017", null], "fact_subtype_id": "VEHICLE_DATE_ADDED"}, {"field_name": "Acquired", "field_values": ["2014-12-30 00:00:00", "2014-12-30 00:00:00", "2015-02-07 00:00:00", "2021-12-27 00:00:00", "2021-12-27 00:00:00"], "fact_subtype_id": "VEHICLE_DATE_ADDED"}, {"field_name": "ADD", "field_values": [null], "fact_subtype_id": "VEHICLE_DATE_ADDED"}, {"field_name": "Unit Description", "field_values": ["2020 TOYO HIGHLANDER", "2020 TOYO HIGHLANDER", "2018 TOYO HIGHLANDER", "2018 TOYO HIGHLANDER", "2018 TOYO HIGHLANDER"], "fact_subtype_id": "VEHICLE_DESCRIPTION"}, {"field_name": "Unit Subtype Description", "field_values": ["SUV, Compact/Mid-Size", "SUV, Compact/Mid-Size", "SUV, Compact/Mid-Size", "SUV, Compact/Mid-Size", "SUV, Compact/Mid-Size"], "fact_subtype_id": "VEHICLE_DESCRIPTION"}, {"field_name": "Business Unit Description", "field_values": ["EIT Eastern PA Delivery", "EIT Western PA Delivery", "EIT Western PA Delivery", "EIT Western PA Delivery", "EIT Western PA Delivery"], "fact_subtype_id": "VEHICLE_DESCRIPTION"}, {"field_name": "Covered Auto Description Year/Make/Model", "field_values": ["2014 LAMAR GOOSENECK TRAILER", "2013 ATOKA TRAILER", "2005 LONE STAR CAR TRAILER", "2018 KENWORTH T800 WINCH&EQUIP", "2018 KENWORTH T800 WINCH&EQUIP"], "fact_subtype_id": "VEHICLE_DESCRIPTION"}, {"field_name": "Private Passenger Vehicles", "field_values": ["Light Trucks", "Medium Trucks", "Heavy Trucks"], "fact_subtype_id": "VEHICLE_DESCRIPTION"}, {"field_name": "SCHEDULE OF COVERED AUTOS YOU OWN Description", "field_values": ["2014 FORD F150", "2016 FORD F550 SUPER DUTY"], "fact_subtype_id": "VEHICLE_DESCRIPTION"}, {"field_name": "Automobile Description", "field_values": ["Cushman Shuttle GE", "Cushman Shuttle GE", "Cushman Shuttle GE", "Cushman Shuttle GE", "Cushman Shuttle GE"], "fact_subtype_id": "VEHICLE_DESCRIPTION"}, {"field_name": "Owned By", "field_values": ["ECMD Owned", "ECMD Owned", "ECMD Owned", "ECMD Owned", "ECMD Owned"], "fact_subtype_id": "VEHICLE_ENTITY_OWNER"}, {"field_name": "Registrant Name", "field_values": [null], "fact_subtype_id": "VEHICLE_ENTITY_OWNER"}, {"field_name": "FAS VIN", "field_values": ["NM0LS6BN9AT017475", "NM0LS7E71J1367925", "2FMDA5142TBB18627", null], "fact_subtype_id": "VEHICLE_INFORMATION_NUMBER"}, {"field_name": "COMP PRE", "field_values": ["1617", "192", "1439", "149"], "fact_subtype_id": "VEHICLE_INSURANCE_PREMIUM"}, {"field_name": "COLL PRE", "field_values": ["2281", "201", "1809", "157"], "fact_subtype_id": "VEHICLE_INSURANCE_PREMIUM"}, {"field_name": "Short Term lease (under 6 months): (Yes/No)", "field_values": ["Yes", "Yes", "Yes", "Yes", "Yes"], "fact_subtype_id": "VEHICLE_LEASED"}, {"field_name": "Manufacturer (Make)", "field_values": ["Audi", "Tesla", "Land Rover", "Ford"], "fact_subtype_id": "VEHICLE_MAKE"}, {"field_name": "Make", "field_values": ["VOLV", "VOLV", "VOLV", "VOLV", "GMC"], "fact_subtype_id": "VEHICLE_MAKE"}, {"field_name": "Mfg", "field_values": ["PET", "PET"], "fact_subtype_id": "VEHICLE_MANUFACTURER"}, {"field_name": "Manufacturer", "field_values": ["FORD", "FORD", "FORD", "FORD", "FORD"], "fact_subtype_id": "VEHICLE_MANUFACTURER"}, {"field_name": "MFG", "field_values": ["FORD", "FORD", "DODGE", "ARIS", "CARGO"], "fact_subtype_id": "VEHICLE_MANUFACTURER"}, {"field_name": "Model Description", "field_values": ["S60", "S60", "S60", "S60", "ACADIA"], "fact_subtype_id": "VEHICLE_MODEL"}, {"field_name": "Model", "field_values": ["F-150", "F150", "F-150", "F-150", "F-150"], "fact_subtype_id": "VEHICLE_MODEL"}, {"field_name": "Model.1", "field_values": ["F-150", "338", "NRR", "T880", "FTR"], "fact_subtype_id": "VEHICLE_MODEL"}, {"field_name": "Model Name", "field_values": ["TRANSIT CONNECT", "TRANSIT CONNECT", "TRANSIT CONNECT", "TRANSIT CONNECT", "TRANSIT CONNECT"], "fact_subtype_id": "VEHICLE_MODEL"}, {"field_name": "Model_1", "field_values": ["Truck (LT)", "Impala (PP)", "F250 (LT)", "Impala (PP)", "Impala (PP)"], "fact_subtype_id": "VEHICLE_MODEL"}, {"field_name": "TRUCK MODEL", "field_values": ["GR64F", "GR64B", "ACX64", "MRU 613", "MRU 613"], "fact_subtype_id": "VEHICLE_MODEL"}, {"field_name": "Column 1", "field_values": ["2023", "2023", "2023", "2023", "2023"], "fact_subtype_id": "VEHICLE_MODEL_YEAR"}, {"field_name": "YR", "field_values": ["NON-COMBUSTIBLE", "NON-COMBUSTIBLE", "NON-COMBUSTIBLE", "2006", "2006"], "fact_subtype_id": "VEHICLE_MODEL_YEAR"}, {"field_name": "Model Year", "field_values": ["2023", "2023", "2023", "2023", "2023"], "fact_subtype_id": "VEHICLE_MODEL_YEAR"}, {"field_name": "Manufacture Year", "field_values": ["2018", "2020", "2023", "2021"], "fact_subtype_id": "VEHICLE_MODEL_YEAR"}, {"field_name": "Yr", "field_values": ["2010", "2018", "2016", "1996", "2017"], "fact_subtype_id": "VEHICLE_MODEL_YEAR"}, {"field_name": "EquipDescription", "field_values": ["2015 Western Star 4700SF Dump Truck", "2017 Western Star 4700SF Dump Truck", "2017 Western Star 4700SF Dump Truck", "2017 Western Star 4700SF Dump Truck", "2017 Western Star 4700SF Dump Truck"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Unit#", "field_values": ["A002", "A003", "A004", "A005", "A011"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Account Description", "field_values": ["2007 Isuzu NRR", "2021 Chevy Silverado", "2019 Hino 195-DC", "2013 Dodge Ram", "2008 Volvo XC-90"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Notes - RE", "field_values": ["6/9/16: Instructed by TN DMV to put letter on letterhead asking for name correction, send to Davidson County Clerk's Office, no fee.  Letter send out 6/9.n6/6/16: <PERSON> said this vehicle was purchased about a year ago.  The name on the title is his mistake.  He said he would resubmit the paperwork, but it was submitted when the van was purchased.n6/2/16: Received title in the mail, no record of this vehicle.  Emailed Donna for info/documentation of purchase.  Vehicle title says HO&P instead of HP&O. Old PCC - 463000; 008400nAcquisition - Superior Orthotics (<PERSON>)", "02.07.19 Received Reg. exp 04.30.20 I do have the title; 08.08.18 I do not have the title, but I do have pics now. 07.23.18 This was purchased without my knowledge. Handed off to Fixed assets as they are taking vehichles back. <PERSON>", "Golf Cart for security guard", "11/12/15- <PERSON> working on getting the name changed. 6/12/15-<PERSON> submitted the registration renewal online.  In about 3 weeks I'll submit documentation with FL DMV to change name from Dobi Systems to Hanger.12/19/14- Emailed <PERSON> asking for a copy of the new registration. 8/8/14- DL listing updated. 8/4/14- Req'd copy of valid registration from <PERSON>. 6/17/14- <PERSON> said he would renew the vehicle registration 6/18/14. 5/22/14- <PERSON> confirmed he has title.  I emailed <PERSON> asking for POA and direction on how to change the title from Dobi Symplex to Hanger. <PERSON> sent us an email 6/5/13 regarding renewal.  <PERSON> mailed out check 6/14/13; 7/1/13 sent another email to <PERSON> regarding status of registration.  8/21/13 sent another reminder. Per email from <PERSON> dated 8/21/13, vehicle not re-registered this year. 2013-HPO (POA for business name change will be made next year, see emails in binder).", "Need to transfer title from Scheck & Siress to Hanger Acquired in the Scheck & Siress acquistion in Apr 2020"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "License and Tax", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Contract Mileage", "field_values": ["0", "0", "15000", "15000", "15000"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Service Charge", "field_values": ["0", "0", "495", "495", "495"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Months In Service", "field_values": ["81", "81", "9", "9", "9"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Lease Type", "field_values": ["COV", "COV", "Equity Lease - Fixed", "Equity Lease - Fixed", "Equity Lease - Fixed"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Entered Mileage", "field_values": ["88754", "88754", "0", "0", "0"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Cust Name", "field_values": ["Ace of Blades, PLLC", "Ace of Blades, PLLC", "Ace of Blades, PLLC", "Ace of Blades, PLLC", "Ace of Blades, PLLC"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Total Lease Charge/Mo", "field_values": ["0", "0", "34.65", "34.65", "34.65"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Master <PERSON><PERSON>", "field_values": ["626593", "626593", "626593", "626593", "626593"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Use Tax Amt/Mo", "field_values": ["0", "0", "3.29", "3.29", "3.29"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Lease Term", "field_values": ["12", "12", "12", "12"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Lease End Date", "field_values": ["2024-12-31 00:00:00", "2024-12-31 00:00:00", "2024-12-31 00:00:00", "2024-12-31 00:00:00"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Current RBV", "field_values": ["0", "0", "817.7", "817.7", "817.7"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Calculated Mileage Date", "field_values": ["2024-09-17 00:00:00", "2024-09-17 00:00:00", "2024-09-11 00:00:00", "2024-09-11 00:00:00", "2024-09-18 00:00:00"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Full Maint Rate/Mo", "field_values": ["0", "0", "0", "0", "0"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Entered Mileage Date", "field_values": ["2023-09-01 00:00:00", "2023-09-01 00:00:00", null], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "License Exp. Date", "field_values": ["2025-01-31 00:00:00", "2025-01-31 00:00:00", "2025-01-31 00:00:00", "2025-01-31 00:00:00"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Calculated Mileage", "field_values": ["117574", "117574", "186276", "186276", "90135"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Dep Pct", "field_values": ["0", "0", "0.01", "0.01", "0.01"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Cust Num", "field_values": ["633710", "633710", "633710", "633710", "633710"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Total Rent/Mo", "field_values": ["0", "0", "37.97", "37.97", "37.97"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Dep Amt/Mo", "field_values": ["0", "0", "0.03", "0.03", "0.03"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Estimated Current Odometer", "field_values": ["117643", "117643", "186354", "186354", "90135"], "fact_subtype_id": "VEHICLE_NOTE"}, {"field_name": "Owned/Leased", "field_values": ["NO", "NO", "NO", "NO", "NO"], "fact_subtype_id": "VEHICLE_OWNED"}, {"field_name": "Vehicle License Plate #", "field_values": ["MYW6918", "MYW6918", "9GEN379", "9GEN379", "KWK7996"], "fact_subtype_id": "VEHICLE_PLATE_NUMBER"}, {"field_name": "Tag #", "field_values": ["8H17836", "8H17836", "XC-800U", "RCS3314", "RCS3314"], "fact_subtype_id": "VEHICLE_PLATE_NUMBER"}, {"field_name": "Plate Exp", "field_values": ["2025-09-30 00:00:00", "2025-09-30 00:00:00", "2025-09-30 00:00:00", "2025-09-30 00:00:00", "2025-09-30 00:00:00"], "fact_subtype_id": "VEHICLE_PLATE_NUMBER"}, {"field_name": "Plate #", "field_values": ["683287ST", "SP674ACC", "SP711LMK", "634872ST", "SP444TSH"], "fact_subtype_id": "VEHICLE_PLATE_NUMBER"}, {"field_name": "LICENSE: #", "field_values": ["N0-PLATE", "PMM5423", "TK287NHA", "TK286NHA", "TK821NEO"], "fact_subtype_id": "VEHICLE_PLATE_NUMBER"}, {"field_name": "tag", "field_values": ["KJC-6106", null], "fact_subtype_id": "VEHICLE_PLATE_NUMBER"}, {"field_name": "Plate", "field_values": ["Y8437S", "CIY5816", "No Plates", "ID94VZ", "BP40253"], "fact_subtype_id": "VEHICLE_PLATE_NUMBER"}, {"field_name": "STATE OF ISSUANCE", "field_values": ["TX", "TX", "TX", "GA", "IL"], "fact_subtype_id": "VEHICLE_PLATE_STATE"}, {"field_name": "Tag State", "field_values": ["NC", "NC", "IN", "KS", "KS"], "fact_subtype_id": "VEHICLE_PLATE_STATE"}, {"field_name": "Reg. State", "field_values": ["CA", "CA", "CA", "CA", "CA"], "fact_subtype_id": "VEHICLE_PLATE_STATE"}, {"field_name": "Lic State/Prov", "field_values": ["MA", "MA", "VA", "VA", "NJ"], "fact_subtype_id": "VEHICLE_PLATE_STATE"}, {"field_name": "STATE TITLED", "field_values": ["AL", "AL", "AL", "AL", "AL"], "fact_subtype_id": "VEHICLE_PLATE_STATE"}, {"field_name": "Vehicle Radius", "field_values": ["Local (<50)", "Local (<50)", "Local (<50)", "Local (<50)", "Local (<50)"], "fact_subtype_id": "VEHICLE_RADIUS"}, {"field_name": "<PERSON><PERSON>", "field_values": ["Local", "Local", "Local", "Local", "Local"], "fact_subtype_id": "VEHICLE_RADIUS"}, {"field_name": "<PERSON><PERSON> 0-50 50-200 >200", "field_values": ["50-200", "0-100"], "fact_subtype_id": "VEHICLE_RADIUS"}, {"field_name": "Radius (mi)", "field_values": ["250", "250", "250", "250", "250"], "fact_subtype_id": "VEHICLE_RADIUS"}, {"field_name": "Radius of Operations", "field_values": ["Intermediate", "Intermediate", "Intermediate", "Intermediate", "Intermediate"], "fact_subtype_id": "VEHICLE_RADIUS"}, {"field_name": "Eq St", "field_values": ["AV", "AV", "AV", "AV", "AV"], "fact_subtype_id": "VEHICLE_STATUS"}, {"field_name": "Vehicle Status", "field_values": ["ACTIVE", "ACTIVE", "ACTIVE", "ACTIVE", "ACTIVE"], "fact_subtype_id": "VEHICLE_STATUS"}, {"field_name": "Unit Type Code", "field_values": ["LT", "LT", "LT", "LT", "LT"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Eq Type", "field_values": ["Transit Van", "Transit Van", "Transit Van", "Transit Van", "Transit Van"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Unit Type", "field_values": ["Power Unit", "Power Unit", "Power Unit", "Power Unit", "Power Unit"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Auto Type", "field_values": ["Heavy Truck", "Heavy Truck", "Heavy Truck", "Trailer", "Heavy Truck"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Vehicle type", "field_values": ["COML", "COML", "COML", "COML", "COML"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Veh Type", "field_values": ["ATV", "TK", "TK", "TK", "TKTR"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Asset Type", "field_values": ["Trailer", "Tractor", "Straight Truck", "Yard Truck", "Trailer"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Category", "field_values": ["Passenger Car", "Passenger Car", "Truck 20' or Larger", "<PERSON><PERSON> van", "Truck 20' or Larger"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Sub Type", "field_values": ["Heavy Truck Tractor", "Heavy Truck Tractor", "Heavy Truck Tractor", "Heavy Truck Tractor", "Heavy Truck Tractor"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "PrimaryType", "field_values": ["Commercial Auto", "Commercial Auto", "Commercial Auto", "Commercial Auto", "Commercial Auto"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "SecondaryType", "field_values": ["Trailers", "Light Truck Truck", "Medium Truck Truck", "Light Truck Truck", "Light Truck Truck"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Vehicle Type", "field_values": ["Truck", "Truck", "Truck", "Truck", "Truck"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Autos", "field_values": ["NISSAN NV", "NISSAN NV", "FORD F450"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Body type", "field_values": ["T", "T", "T", "T", "T"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Service Type", "field_values": ["Commuter", "Commuter", "Commuter", "Commuter", "Commuter"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "EQUIPMENT TYPE", "field_values": ["1106-MAUSTON", "1022-WADSWORTH", "1018-WAUCONDA", "1022-WADSWORTH", "1302-JACKSON"], "fact_subtype_id": "VEHICLE_TYPE"}, {"field_name": "Vehicle Use (Commercial Or Personal Or Both)", "field_values": ["Commercial", "Commercial", "Commercial", "Commercial", "Commercial"], "fact_subtype_id": "VEHICLE_USE"}, {"field_name": "Vehicle Use (See Below)", "field_values": ["Service", "Service", "Service", "Service", "Service"], "fact_subtype_id": "VEHICLE_USE"}, {"field_name": "Operations / Use", "field_values": ["Used for employee travel to and from job sites as well as running errands for clients as necessary", "Used for employee travel to and from job sites as well as running errands for clients as necessary", "Used for employee travel to and from job sites as well as running errands for clients as necessary", "Used for employee travel to and from job sites as well as running errands for clients as necessary", "Used for employee travel to and from job sites as well as running errands for clients as necessary"], "fact_subtype_id": "VEHICLE_USE"}, {"field_name": "Equipment/Use Type", "field_values": ["Small Shuttle", "Golf Cart", "Golf Cart", "Shuttle II", "Shuttle II"], "fact_subtype_id": "VEHICLE_USE"}, {"field_name": "Use Service", "field_values": ["X", "X", "X", "X", "X"], "fact_subtype_id": "VEHICLE_USE"}, {"field_name": "NEW", "field_values": ["46806", "43592", "103000", "32000"], "fact_subtype_id": "VEHICLE_VALUE_NEW"}, {"field_name": "OC", "field_values": ["38175", "45500", "45500", "120000", "127340"], "fact_subtype_id": "VEHICLE_VALUE_NEW"}, {"field_name": "Price", "field_values": ["0", "0", "3000", "2250", "4435"], "fact_subtype_id": "VEHICLE_VALUE_NEW"}, {"field_name": "Cost", "field_values": ["27000", "4166.67", "22500", "27000"], "fact_subtype_id": "VEHICLE_VALUE_NEW"}, {"field_name": "Aluminum Wiring in Units?", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": null}, {"field_name": "Any Aluminum Wiring", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": null}, {"field_name": "Aluminum Wiring Yes/No", "field_values": ["NO"], "fact_subtype_id": null}, {"field_name": "Aluminum  Wiring             Y or N", "field_values": ["N", null], "fact_subtype_id": "WIRING"}, {"field_name": "Electrical Type Copper, alumnium, pig tail?", "field_values": ["Copper", "Copper", "Copper", "Copper"], "fact_subtype_id": "WIRING"}, {"field_name": "Any Aluminum Wiring?", "field_values": ["N", "N", "N", "N", "N"], "fact_subtype_id": "WIRING"}, {"field_name": "Wiring_1", "field_values": ["AN", "AN", "AN", "AN", "AN"], "fact_subtype_id": "WIRING"}, {"field_name": "Aluminum Wiring (Y/N)", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": null}, {"field_name": "Any aluminum wiring?", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": null}, {"field_name": "Aluminum Wiring?", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": null}, {"field_name": "Aluminum Wiring Y/N", "field_values": ["N"], "fact_subtype_id": "WIRING"}, {"field_name": "Aluminum Wiring", "field_values": ["No", "No", "No", "No", "No"], "fact_subtype_id": null}, {"field_name": "Electrical Wiring Type", "field_values": ["100% Copper", "100% Copper", "100% Copper", "100% Copper", "100% Copper"], "fact_subtype_id": "WIRING"}, {"field_name": "Alum wiring", "field_values": ["No", "No", null], "fact_subtype_id": null}, {"field_name": "Circuit breakers", "field_values": ["Yes", "Yes", null], "fact_subtype_id": null}, {"field_name": "Electrical type", "field_values": ["Circuit breakers"], "fact_subtype_id": "WIRING"}, {"field_name": "Electrical", "field_values": ["Copper", "Copper", "Copper", "Copper", "Copper"], "fact_subtype_id": "WIRING"}, {"field_name": "Type of Wiring", "field_values": ["Copper", "Copper", "Copper", "Copper", "Copper"], "fact_subtype_id": "WIRING"}, {"field_name": "Hard Wired or Battery Smoke Detectors", "field_values": ["hardwired", "hardwired", "hardwired", "hardwired", "hardwired"], "fact_subtype_id": "SMOKE_DETECTOR_TYPE"}, {"field_name": "Electrical System Type (Circuit Breaker or Fuse)", "field_values": ["Circuit Breaker and Fuses", "Circuit Breaker and Fuses", "Circuit Breaker and Fuses", "Circuit Breaker and Fuses", "Circuit Breaker and Fuses"], "fact_subtype_id": "WIRING"}, {"field_name": "Year Built.1", "field_values": ["1970", "1972", "1971", "1973", "1972"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Building Age", "field_values": ["1970", "1972", "1971"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Year Constructed", "field_values": ["1969"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Building Year", "field_values": ["2001", "2002", "2003", "1991", "1991"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "YEAR BUILD", "field_values": ["1987", "1880", "1972", "1886", "1924"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Year Built Ranges", "field_values": ["1970 - 1979", "1970 - 1979", "1970 - 1979", "Pre-1950", null], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Construction Year", "field_values": ["1990", "2007", "2006"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Year of Construction:", "field_values": ["1972", null], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Orig SOV Year Built", "field_values": ["2002", "1970", "1970", "1970", "1970"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Year of construction", "field_values": ["1960", "1971", "1966", "1971", "1982"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Year Built (36)", "field_values": [null], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "<PERSON><PERSON>", "field_values": ["2023", "2011", "2011", "2020", "2014"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Average Year Built", "field_values": ["2016", "1998", "1998", "2014", "2020"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "YEAR  BUILT", "field_values": ["2004", null], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Orig Year Built", "field_values": ["1971", "1971", "1971", "1971", "1971"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Year or Decade  Built", "field_values": ["1928", "1900", "1989", "1981", "1981"], "fact_subtype_id": "YEAR_BUILT"}, {"field_name": "Tenant Improvements & Betterments", "field_values": [10000.2], "fact_subtype_id": "TENANTS_IMPROVEMENTS_AND_BETTERMENTS"}, {"field_name": "Total head count @ location", "field_values": ["100_000"], "fact_subtype_id": null}]