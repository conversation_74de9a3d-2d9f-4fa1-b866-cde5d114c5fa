import json
import random
import string
from functools import partial
from pathlib import Path
from test.fact_subtypes.fixtures import (
    fact_subtypes,
    fact_subtypes_matcher,
    sov_enum_matcher,
)
from typing import Iterable
from unittest.mock import MagicMock

import pytest
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.parent import ParentType

from datascience_common.classifiers.fact_identification.construcion import (
    SupplementalFactsIdentifier,
)
from datascience_common.classifiers.fact_identification.sov import (
    REGULAR_EXPRESSIONS_SOV,
    SOVFactsIdentifier,
)
from datascience_common.classifiers.fact_identification.supplementals.supp_fields import (
    SuppField,
)
from datascience_common.classifiers.fact_identification.supplementals.supplemental_matching import (
    TemplateSupplementalFactsIdentifier,
)
from datascience_common.fact_subtypes.field_name_matching.fact_subtype_matcher import (
    FactSubtypeMatcher,
    MatchingResult,
)

CURRENT_DIR = Path(__file__).parent

used_fixtures = (sov_enum_matcher, fact_subtypes_matcher, fact_subtypes)


@pytest.fixture
def supplemental_facts_identifier():
    return TemplateSupplementalFactsIdentifier()


@pytest.mark.parametrize(
    "question",
    [
        "Applicant uses subcontractors",
        "Do you use subcontractors",
        "The applicant uses subcontractors",
    ],
)
def test_supplemental_subconstractors_used_positive(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.SUBCONTRACTORS_USED]


@pytest.mark.parametrize(
    "question", ["Applicant:", "Applicant's Name:", "First Name Insured", "Full name of applicant"]
)
def test_supplemental_applicant_name(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.APPLICANT_NAME]


@pytest.mark.parametrize(
    "question",
    ["Address:", "Mailing Address", "Business Address:   ", "Insured Address: ___", "Policy Mailing Address"],
)
def test_supplemental_address_name(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.APPLICANT_NAME]


@pytest.mark.parametrize(
    "question",
    ["Address:", "Mailing Address", "Business Address:   "],
)
def test_supplemental_address_name(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.APPLICANT_ADDRESS]


@pytest.mark.parametrize("question", ["Zip Code"])
def test_supplemental_zip_code(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.APPLICANT_ZIP]


@pytest.mark.parametrize(
    "question", ["   Is scaffolding owned, rented or erected?", "Do you use scaffolding?", "Scaffolding Installation"]
)
def test_supplemental_scaffolding(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.PROJECT_SCAFFOLDING]


@pytest.mark.parametrize(
    "question",
    [
        "Is any heavy equipment, including cranes, owned or operated?",
        "Are cranes operated on your jobsites",
        "Will cranes be used?",
    ],
)
def test_supplemental_crane_work(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.PROJECT_CRANE_WORK]


@pytest.mark.parametrize(
    "question",
    [
        "	Wrecking / Demolition",
        "demolition",
        "Do you perform any exterior or structural demolition?",
        "Demolition work",
        "Wrecking / Demolition / Dismantling",
        "Will the project involve any demolition of existing structures?",
        "Will you be doing any demolition work other than remodeling?",
    ],
)
def test_supplemental_demolition(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.PROJECT_DEMOLITION_WORK]


@pytest.mark.parametrize(
    "question",
    [
        "	Any work performed below grade",
        "Will you or any subcontractor perform work below grade?",
        "Do you perform any work below ground level?",
        "Have you performed or will you or your subcontractors perform any work below grade (basements)?",
        "Do you perform work below grade level (i.e. Moving dirt or working below grade level)?",
    ],
)
def test_supplemental_below_grade(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert [result == SuppField.PROJECT_BELOW_GRADE]


@pytest.mark.parametrize(
    "question",
    [
        "Maximum number of stories, Do you perform any exterior work over one story?",
        "Do you perform work above two stories in height? ",
        "Do you perform work above two stories in height?",
        "If yes, maximum number of stories",
        "# of Stories",
    ],
)
def test_supplemental_above_stories(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert [result == SuppField.APPLICANT_NAME]


@pytest.mark.parametrize(
    "question",
    [
        "	Length of time in business",
        "How many years of experience do you have in the contracting business?",
        "Years in business under current name",
        "State the years in business",
        "Account Years in Business",
    ],
)
def test_supplemental_years_in_business(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.YEARS_IN_BUSINESS]


@pytest.mark.parametrize(
    "question",
    [
        "	Who is responsible for managing the quality control program?",
        "Do you have a Quality Control (QC) Program?",
        "Does the General Contractor / Insured have a Quality Control Program?",
        "Does the Named Insured have a Quality Control Program in effect to monitor all construction activities?",
    ],
)
def test_supplemental_quality_control(question, supplemental_facts_identifier):
    result = supplemental_facts_identifier.identify(value=question)
    assert result == [SuppField.PROJECT_QUALITY_CONTROL_PROGRAM]


@pytest.fixture(scope="session")
def sov_facts_identifier(fact_subtypes) -> SOVFactsIdentifier:
    return SOVFactsIdentifier(fact_subtypes=fact_subtypes)


@pytest.fixture(scope="session")
def identify(sov_facts_identifier):
    return partial(sov_facts_identifier.identify, parent_type=ParentType.BUSINESS)


@pytest.fixture(scope="session")
def provide_propositions(sov_facts_identifier):
    return partial(sov_facts_identifier.provide_propositions, parent_type=ParentType.BUSINESS)


@pytest.mark.parametrize(
    "question",
    [
        "Total Area",
        "TOTAL BLDG SQ. FT. ",
        "Sq Footage",
        "square footage",
        "area / sq feet",
        "sq. feet",
        "permanent square footage",
        "Area",
        "SQFT",
    ],
)
def test_sov_building_size(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result == FactSubtypeID.BUILDING_SIZE


@pytest.mark.parametrize(
    "question",
    ["Value per Sq Footage", "Cost of sq. feet", "COSTPERSQ. FT."],
)
def test_sov_not_building_size(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result != FactSubtypeID.BUILDING_SIZE


@pytest.mark.parametrize(
    "question",
    [
        "Stories",
        "# of stories",
        "floors",
        "no. of stories",
        "num of stories",
        "# of stories",
        "#stories",
        "stories",
    ],
)
def test_sov_number_of_stories(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result == FactSubtypeID.NUMBER_OF_STORIES


@pytest.mark.parametrize(
    "question",
    ["Contents", "Content Value", "$Cnts", "Ctnt Limit"],
)
def test_sov_content_value(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result == FactSubtypeID.CONTENT_VALUE


@pytest.mark.parametrize(
    "value, expected",
    [
        (
            "constr type",
            [
                MatchingResult(FactSubtypeID.CONSTRUCTION_CLASS),
            ],
        ),
        (
            "constr. type",
            [
                MatchingResult(FactSubtypeID.CONSTRUCTION_CLASS),
            ],
        ),
        (
            "construction type",
            [
                MatchingResult(FactSubtypeID.CONSTRUCTION_CLASS),
            ],
        ),
        (
            "constr",
            [
                MatchingResult(FactSubtypeID.CONSTRUCTION_CLASS),
            ],
        ),
        (
            "construction class",
            [MatchingResult(FactSubtypeID.CONSTRUCTION_CLASS)],
        ),
        (
            "ISO Const",
            [MatchingResult(FactSubtypeID.CONSTRUCTION_CLASS)],
        ),
    ],
)
def test_sov_construction_class(
    fact_subtypes_matcher: FactSubtypeMatcher, value: str, expected: list[MatchingResult]
) -> None:
    result: list[MatchingResult] = sorted(
        fact_subtypes_matcher.match(value=value, parent_type=ParentType.STRUCTURE), key=lambda x: x.fact_subtype_id
    )
    result_without_evidence = [
        MatchingResult(fact_subtype_id=x.fact_subtype_id, value_transformer=x.value_transformer, evidence=None)
        for x in result
    ]
    assert result_without_evidence == sorted(expected, key=lambda x: x.fact_subtype_id)


@pytest.mark.parametrize(
    "question",
    [
        "number of units",
        "no. of units",
        "units",
        "# units",
        "# of units",
        "# of Condominium",
        "# Apartment Units",
        "# of Habitational Units" 'TOTAL # APT UNITS "if any"',
        "Apartment Buildings (Unit)",
        "Number of Units (Residential) Rooms (Hotels)" "# \nUnits",
        "Numberof Units",
        "Number of Other Residential Units",
        "No. of Units",
        "Market  # of units",
        "Hotel Units (Unit)",
        "# Hab Units",
        "COPE Information : # of Units",
        "Apt. Units",
        "# Apt  Units",
        "Apartment Building - garden (Unit)",
        "Number of Units (for Apartments)",
        "Apartment Building- (added for rental exposure) (Unit)",
        "Number of Units",
        "APARTMENT BUILDING - NOC (Unit)",
    ],
)
def test_sov_number_of_units(question, identify):
    result = identify(value=question, field_type=FieldType.INTEGER)
    assert result == FactSubtypeID.NUMBER_OF_UNITS


@pytest.mark.parametrize(
    "question",
    [
        "TIV",
        "total insured value",
        "total insured values",
        "TOTAL INSURABLE VALUE",
        "Total",
        "TOTALVALUE",
        "Total value",
        "total property value",
        "Total by Loc./Bldg",
    ],
)
def test_sov_tiv(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result == FactSubtypeID.TIV


@pytest.mark.parametrize(
    "question",
    [
        "Inactive",
    ],
)
def test_sov_not_tiv(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result != FactSubtypeID.TIV


@pytest.mark.parametrize(
    "question",
    ["ITV"],
)
def test_sov_itv(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result == FactSubtypeID.ITV


@pytest.mark.parametrize(
    "question",
    ["line of business", "business line", "line business"],
)
def test_sov_line_of_business(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.LINE_OF_BUSINESS


@pytest.mark.parametrize(
    "question",
    ["# of spaces", "number of parking spaces", "parking spaces"],
)
def test_sov_parking_spaces_count(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result == FactSubtypeID.PARKING_SPACES_COUNT


@pytest.mark.parametrize(
    "question, answer",
    [
        ("Any use of EIFS?", "PROJECT_USE_OF_EIFS"),
        ("Scaffolding?", "PROJECT_SCAFFOLDING"),
        ("Roof work?", "PROJECT_ROOF_WORK"),
        ("Crane work?", "PROJECT_CRANE_WORK"),
        ("Demolition work?", "PROJECT_DEMOLITION_WORK"),
        ("Below grade?", "PROJECT_BELOW_GRADE"),
        ("Work above 7 stories?", "PROJECT_WORK_ABOVE_SEVEN_STORIES"),
        ("Mold removal?", "PROJECT_MOLD_REMOVAL"),
        ("Asbestos or other hazardous material removal?", "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL"),
        ("Subcontractors Used?", "PROJECT_SUBCONTRACTORS_USED"),
        ("Safety Program?", "PROJECT_SAFETY_PROGRAM"),
        ("Quality Control Program?", "PROJECT_QUALITY_CONTROL_PROGRAM"),
        ("Site Inspection Program?", "PROJECT_SITE_INSPECTION_PROGRAM"),
        ("Blasting work?", "PROJECT_BLASTING_WORK"),
        ("Excavation work?", "PROJECT_EXCAVATION_WORK"),
        ("EPDM?", "PROJECT_EPDM_HOT_OR_COLD"),
    ],
)
def test_sov_project_construction_bool(question, answer, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result.value == answer

    result = identify(value=question, field_type=FieldType.BOOLEAN)
    assert result.value == answer


@pytest.mark.parametrize(
    "question, answer",
    [
        ("Subcontractor cost in $", "PROJECT_SUBCONTRACTORS_COST"),
        ("Estimated Construction Cost in $ - Project", "PROJECT_ESTIMATED_CONSTRUCTION_COST"),
        ("% of Work Subcontracted to Others", "PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS"),
        ("% of Work as a GC", "PROJECT_PERCENTAGE_OF_WORK_AS_GC"),
        ("% of Work as a Subcontractor", "PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR"),
        ("% of Residential Work", "PROJECT_PERCENTAGE_OF_WORK_RESIDENTIAL"),
        ("% of Commercial Work", "PROJECT_PERCENTAGE_OF_WORK_COMMERCIAL"),
        ("% of Industrial Work", "PROJECT_PERCENTAGE_OF_WORK_INDUSTRIAL"),
        ("% of Public Work", "PROJECT_PERCENTAGE_OF_WORK_PUBLIC"),
        ("Years in business", "YEARS_IN_BUSINESS"),
        ("Total Sales in $", "TOTAL_SALES"),
        ("Project height in ft", "PROJECT_HEIGHT_IN_FT"),
        ("Project height in stories", "PROJECT_HEIGHT_IN_STORIES"),
        ("Maximum height of work in ft", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT"),
        ("Maximum height of work in stories", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"),
    ],
)
def test_sov_project_construction_numeric(question, answer, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result.value == answer


@pytest.mark.parametrize(
    "question",
    ["State", "State Registered"],
)
def test_sov_vehicle_plate_state(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.VEHICLE)
    assert result == FactSubtypeID.VEHICLE_PLATE_STATE


@pytest.mark.parametrize(
    "question",
    ["Class", "Equipment Class", "Body Class", "Accounting Class", "Acc Class"],
)
def test_sov_vehicle_body_class(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.VEHICLE)
    assert result == FactSubtypeID.VEHICLE_BODY_CLASS


@pytest.mark.parametrize(
    "question",
    ["State", "State Licensed", "License State", "State of Driver's License", "CDLState", "CDL_State", "DLState"],
)
def test_sov_driver_license_state(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.DRIVER)
    assert result == FactSubtypeID.DRIVER_LICENSE_STATE


@pytest.mark.parametrize(
    "question",
    [
        "Driver's License No.",
        "License No.",
        "License Number",
        "CDLNumber",
        "D/L #",
        "DriverLicense",
        "DL #",
        "DL",
        "CDL",
        "Driver Lic",
        "Driver's License",
        "Driver License",
        "Driver License Number",
    ],
)
def test_sov_driver_license_number(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.DRIVER)
    assert result == FactSubtypeID.DRIVER_LICENSE_NUMBER


@pytest.mark.parametrize(
    "question",
    ["Employee Name", "Driver Name", "Drivers Name", "Name", "* Name", "Drivers Name"],
)
def test_sov_driver_name(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.DRIVER)
    assert result == FactSubtypeID.DRIVER_NAME


@pytest.mark.parametrize(
    "question",
    ["Driver No."],
)
def test_sov_not_driver_name(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.DRIVER)
    assert result != FactSubtypeID.DRIVER_NAME


def test_sov_driver_type_is_matched(identify):
    result = identify(value="Driver Type", field_type=FieldType.TEXT, parent_type=ParentType.DRIVER)
    assert result is FactSubtypeID.DRIVER_ROLE


@pytest.mark.parametrize(
    "question",
    ["First Name", "First"],
)
def test_sov_driver_first_name(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.DRIVER)
    assert result == FactSubtypeID.DRIVER_FIRST_NAME


@pytest.mark.parametrize(
    "question",
    ["Last Name", "Last"],
)
def test_sov_driver_last_name(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.DRIVER)
    assert result == FactSubtypeID.DRIVER_LAST_NAME


@pytest.mark.parametrize(
    "question",
    ["Zip", "Zip Code", "Zip Code*", "Zip/Postal", "ZipCode"],
)
@pytest.mark.parametrize("field_type", [FieldType.TEXT, FieldType.NUMBER, FieldType.INTEGER])
@pytest.mark.parametrize("parent_type", [ParentType.STRUCTURE, ParentType.STRUCTURE, ParentType.BUSINESS])
def test_sov_zip_code(question, field_type, parent_type, identify):
    result = identify(value=question, field_type=field_type, parent_type=parent_type)
    assert result == FactSubtypeID.ZIP_CODE


@pytest.mark.parametrize(
    "question",
    ["Lender Zip", "Lender Zip Code"],
)
@pytest.mark.parametrize("field_type", [FieldType.TEXT, FieldType.NUMBER, FieldType.INTEGER])
def test_sov_lender_zip_code(question, field_type, identify):
    result = identify(value=question, field_type=field_type, parent_type=ParentType.STRUCTURE)
    assert result == FactSubtypeID.LENDER_ZIP_CODE


@pytest.mark.parametrize(
    "question", ["Distance to coast", "Distance from coast", "Distance to saltwater", "Distance from saltwater"]
)
@pytest.mark.parametrize("field_type", [FieldType.TEXT, FieldType.NUMBER, FieldType.INTEGER])
def test_sov_distance_to_coast(question, field_type, identify):
    result = identify(value=question, field_type=field_type, parent_type=ParentType.STRUCTURE)
    assert result == FactSubtypeID.DISTANCE_TO_COAST


@pytest.mark.parametrize("parent_type", [ParentType.PREMISES, ParentType.STRUCTURE, ParentType.VEHICLE])
def test_sov_owner(parent_type, identify):
    result = identify(value="Owner", field_type=FieldType.TEXT, parent_type=parent_type)
    assert result == FactSubtypeID.OWNER


@pytest.mark.parametrize("question", ["Note", "Vehicle Note", "notes"])
def test_sov_vehicle_note(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.VEHICLE)
    assert result == FactSubtypeID.VEHICLE_NOTE


@pytest.mark.parametrize("question", ["desc", "description", "vehicle description"])
def test_sov_vehicle_desc(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.VEHICLE)
    assert result == FactSubtypeID.VEHICLE_DESCRIPTION


@pytest.mark.parametrize(
    "question, field_type",
    [
        ("Has Basement", FieldType.TEXT),
        ("Basement", FieldType.TEXT),
        ("Has Basement", FieldType.BOOLEAN),
        ("Basement", FieldType.BOOLEAN),
    ],
)
def test_sov_has_basement(question, field_type, identify):
    result = identify(value=question, field_type=field_type, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.HAS_BASEMENT


@pytest.mark.parametrize(
    "question", ["Sub %", "% Sub", "Sub Percent", "Subsidized %", "% Subsidized", "Subsidized Percent"]
)
def test_sov_subsidized_percent(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.SUBSIDIZED_PERCENT


@pytest.mark.parametrize("question", ["% Occupied", "Occupied %", "% Occ", "Occ %"])
def test_sov_not_occupancy(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.BUSINESS)
    assert result != FactSubtypeID.PROPERTY_DESCRIPTION


@pytest.mark.parametrize(
    "question",
    [
        "Occ %",
        "% Occ",
        "Occ Percent",
        "Occupation %",
        "% Occupation",
        "Occupation Percent",
        "Occp",  # not FieldType.TEXT
        "% Occupied",
        "PERCENT OCCUPIED",
        "Occupancy %",
    ],
)
def test_sov_occupation_percent(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.OCCUPATION_PERCENT


@pytest.mark.parametrize("question", ["Middle Name", "Middle"])
def test_sov_driver_middle_name(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.DRIVER)
    assert result == FactSubtypeID.DRIVER_MIDDLE_NAME


@pytest.mark.parametrize("question", ["Wiring", "Wire", "Wire type"])
def test_sov_wiring(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.WIRING


@pytest.mark.parametrize(
    "question", ["YOC", "Yr Blt", "Yr built", "built", "*Orig Year Built", "Year Built", "Effective Yr Blt'"]
)
def test_sov_year_built(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.YEAR_BUILT


@pytest.mark.parametrize(
    "question",
    [
        "$Bldgs",
        "Bldg Value",
        "BLDG@ 100%",
        "Real Property Value ($)",
        "   bldg   value ($)",
        "Building Coverage Limit",
        "Building Coverage: Limit",
        "Building Value",
        "Building Valuation",
        "Building",
        "Buildings",
        "Building Limit",
        "Real Property",
        "Property",
        "Building",
        "Property Value",
    ],
)
def test_sov_building_value(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.BUILDING_VALUE


@pytest.mark.parametrize(
    "question",
    [
        "Building Maintenance",
        "Building Frame to Foundation Connection",
        "Building Foundation",
        "Building Replacement Value",
        "# of Buildings",
    ],
)
def test_sov_is_not_building_value(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result != FactSubtypeID.BUILDING_VALUE


@pytest.mark.parametrize("question", ["# Story", "#Story", "# Stor", "#Stor", "*# of Stories"])
def test_sov_stories_count(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.NUMBER_OF_STORIES


@pytest.mark.parametrize("question", ["sqft", "Square Footage Total"])
def test_sov_building_size(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.BUILDING_SIZE


@pytest.mark.parametrize(
    "question",
    [
        "Sprinkler",
        "AUTO SPRINKLER SYSTEM?",
        "Sprinklers ISO Approved?",
    ],
)
def test_sov_sprinklers(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.HAS_SPRINKLERS


@pytest.mark.parametrize(
    "question",
    [
        "% OF SPRINKLER SYSTEM",
        "% sprinkled",
        "Percentage Sprinklered",
        "Sprinklers %",
        "Sprinkler %",
        "Sprinklered %",
        "Sprinkled %",
        "SPKLR %",
        "% SPKLR",
        "SPRNKLR %",
        "% SPRNKLR",
        "SPRINKLR %",
        "% SPRINKLR",
        "% SPRINKLERED",
        "% Sprinkled",
        "AUTO SPRINKLER SYSTEM? What %?",
        "PERCENT SPRINKLERED",
        "Percent Sprinklered",
        "Sprinklers area coverage percentage",
        "sprinkler_percentage",
        "Sprinkler %\n(Type of Sprinkler)",
    ],
)
def test_sov_sprinkler_area_coverage_percent(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert FactSubtypeID.SPRINKLER_AREA_COVERAGE_PERCENT in result


@pytest.mark.parametrize(
    "question",
    [
        "SPRINKLERED",
        "Sprinklered",
        "Sprinklered?",
        "Sprkl'd",
        "UnitsSprinklered",
        "Sprinklered\n(Y/N)",
        "Sprinkler",
        "Sprinklered",
        "Sprklrd",
        "Has Sprinklers",
        "Sprinklers?",
        "Has Sprinkler",
        "Sprink?",
    ],
)
def test_sov_has_sprinklers_boolean(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.BOOLEAN, parent_type=ParentType.PREMISES)
    assert FactSubtypeID.HAS_SPRINKLERS in result


@pytest.mark.parametrize(
    "question",
    [
        "Sprinkler Count",
        "Number Sprinkler",
        "Number of Sprinklers",
        "# sprinklers",
        "sprinklers #",
        "count of sprinklers",
        "sprinklers num",
        "num of sprinklers",
    ],
)
def test_sov_sprinkler_count(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.INTEGER, parent_type=ParentType.PREMISES)
    assert FactSubtypeID.SPRINKLER_COUNT in result


@pytest.mark.parametrize("question", ["Fire Protection", "Fire Suppression"])
def test_sov_fire_protection(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.FIRE_PROTECTION


@pytest.mark.parametrize(
    "question",
    [
        "COPE Information : ISO Protection Class",
        "Fire: ISO Prot. Class",
        "ISO Class",
        "ISO Fire Protection Class",
        "ISO Prot. Class",
        "ISO Prot Class (1-10)",
        "ISO Protection Class",
        "Protection Class",
        "ISO PC",
    ],
)
def test_sov_fire_protection_class(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.INTEGER)
    assert FactSubtypeID.ISO_FIRE_PROTECTION_CLASS in result


@pytest.mark.parametrize(
    "question",
    [
        "Fire Protection Class",
        "Fire Protection Class (AAIS)",
        "PC",
        "PC Code",
        "Protection Class",
        "Public Protection Class",
    ],
)
def test_sov_fire_protection(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.FIRE_PROTECTION_CLASS


@pytest.mark.parametrize("question", ["Installs Burglar Alarm", "burglar alarm", "cs burglar alarm"])
def test_sov_burglar_alarm_type(question, identify, sov_facts_identifier):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.BURGLAR_ALARM_TYPE


@pytest.mark.parametrize(
    "value, expected",
    [
        (
            "Has Burglar Alarm",
            [
                MatchingResult(FactSubtypeID.HAS_BURGLAR_ALARM),
            ],
        ),
        (
            "Burglar Alarmm",
            [
                MatchingResult(FactSubtypeID.BURGLAR_ALARM_TYPE),
                MatchingResult(FactSubtypeID.HAS_BURGLAR_ALARM),
            ],
        ),
        (
            "Burglar Alarm?",
            [
                MatchingResult(FactSubtypeID.BURGLAR_ALARM_TYPE),
                MatchingResult(FactSubtypeID.HAS_BURGLAR_ALARM),
            ],
        ),
        (
            "Central Station Burglar Alarm?",
            [
                MatchingResult(FactSubtypeID.BURGLAR_ALARM_TYPE),
                MatchingResult(FactSubtypeID.HAS_BURGLAR_ALARM),
            ],
        ),
        (
            "Burglar CS",
            [
                MatchingResult(FactSubtypeID.HAS_BURGLAR_ALARM),
            ],
        ),
    ],
)
def test_sov_has_burglar_alarm(
    fact_subtypes_matcher: FactSubtypeMatcher, value: str, expected: list[MatchingResult]
) -> None:
    result = sorted(
        fact_subtypes_matcher.match(value=value, parent_type=ParentType.STRUCTURE), key=lambda x: x.fact_subtype_id
    )
    result_without_evidence = [
        MatchingResult(fact_subtype_id=result.fact_subtype_id, value_transformer=result.value_transformer)
        for result in result
    ]
    assert result_without_evidence == sorted(expected, key=lambda x: x.fact_subtype_id)


@pytest.mark.parametrize("question", ["Burglar Alarm Type", "Burglar Type", "Alarm Type"])
def test_sov_burglar_alarm_type(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.BURGLAR_ALARM_TYPE


@pytest.mark.parametrize(
    "question", ["Smoke Detector", "Has Smoke Detector", "Smoke/Carbon Monoxide Detectors (operational)"]
)
def test_sov_has_smoke_detector(question, identify):
    result = identify(value=question, field_type=FieldType.BOOLEAN)
    assert result == FactSubtypeID.HAS_SMOKE_DETECTOR
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.HAS_SMOKE_DETECTOR


@pytest.mark.parametrize(
    "value, expected",
    [
        (
            "Smoke Detector Type",
            [
                MatchingResult(FactSubtypeID.SMOKE_DETECTOR_TYPE),
                MatchingResult(FactSubtypeID.HAS_SMOKE_DETECTOR),
            ],
        ),
        (
            "Smoke Detectors Type",
            [
                MatchingResult(FactSubtypeID.SMOKE_DETECTOR_TYPE),
                MatchingResult(FactSubtypeID.HAS_SMOKE_DETECTOR),
            ],
        ),
    ],
)
def test_sov_smoke_detector_type(
    fact_subtypes_matcher: FactSubtypeMatcher, value: str, expected: list[MatchingResult]
) -> None:
    result = sorted(
        fact_subtypes_matcher.match(value=value, parent_type=ParentType.STRUCTURE), key=lambda x: x.fact_subtype_id
    )
    result_without_evidence = [
        MatchingResult(fact_subtype_id=result.fact_subtype_id, value_transformer=result.value_transformer)
        for result in result
    ]

    assert result_without_evidence == sorted(expected, key=lambda x: x.fact_subtype_id)


@pytest.mark.parametrize("question", ["Has Fire Alarm", "Fire Alarms?", "Fire Alarms (operational)"])
def test_sov_has_fire_alarm(question, identify):
    result = identify(value=question, field_type=FieldType.BOOLEAN)
    assert result == FactSubtypeID.HAS_FIRE_ALARM
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.HAS_FIRE_ALARM


@pytest.mark.parametrize("question", ["Operations"])
def test_sov_operations(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.OPERATIONS


@pytest.mark.parametrize("question", ["Fire Alarm Type", "Fire Alarms Type"])
def test_sov_fire_alarm_type(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.FIRE_ALARM_TYPE


@pytest.mark.parametrize("question", ["$ Other", "$Other"])
def test_sov_other_tiv(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.OTHER_VALUE_TIV


@pytest.mark.parametrize(
    "question",
    [
        "BI",
        "BI @ 100%",
        "BI Value",
        "Business INTERRUPTION",
        "BI ( Applies to all location)",
        "VALUE/RENTS INCOME",
    ],
)
def test_sov_bi(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.BUSINESS_INCOME


@pytest.mark.parametrize(
    "question",
    [
        "$BI/EE",
        "BI/EE/Rents",
        "$BI/EE/Rents",
        "BI incl. EE Coverage Limit",
        "BI incl. EE Coverage: Limit",
        "Loss of Use",
    ],
)
def test_sov_bi_ee(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.BI_EE


@pytest.mark.parametrize(
    "question",
    [
        "BPP",
        "Business Personal Property",
        "BPP Renewal",
        "BPP Coverage: Limit",
        "Business Property",
    ],
)
def test_sov_bpp(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.BPP


@pytest.mark.parametrize("question", ["Rent Income", "Rents", "$Rents"])
def test_sov_rent_income(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.RENT_INCOME


@pytest.mark.parametrize(
    "question",
    [
        "COST PER SQ FOOT",
        "Cost per Sq Ft",
        "Cost Per Square Foot",
        "Cost per SqFt",
        "Cost/SF",
        r"Cost\SQFT.",
        "Cost/Sq. Ft.",
        "Cost/SqFt",
        "Valuation Per Sq. Foot",
        "A) Square Footage: (B) Value per Sq Ft",
        "$ / Sq. Ft.",
        "$ / Sq. Ft",
        "$ / sqft.",
        "2023 Cost per Square Foot",
        "2012 Cost per Square Foot",
        "$/SQFT",
        "$/Sqft",
        "$/SF",
        "$/Foot",
        r"$\Foot",
        "$ Sq Ft",
        "$ Sq Ft.",
        "$ Per Square foot",
        "$ per Sq. Ft.",
        "$ per SF" "$ per SF.",
        "$ / SQ. FT",
        "Price Per Sq Ft",
        "COSTPERSQ. FT.",
        "$/SQ FT.",
    ],
)
def test_sov_cost_per_sq_ft(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.TEXT)
    assert FactSubtypeID.COST_PER_SQ_FT in result


@pytest.mark.parametrize(
    "question",
    ["YR Roof Built", "UPDATES: ROOF", " Updates: Roofing, Year of Roof Update"],
)
def test_sov_building_improvemnts_roofing_year(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.BUILDING_IMPROVEMENTS_ROOFING_YEAR


@pytest.mark.parametrize(
    "question",
    ["UPDATES: PLUMB", "Updates: Plumbing", "Year of Plumbing Update", "Updatesplumbing"],
)
def test_sov_building_improvemnts_plumbing_year(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.BUILDING_IMPROVEMENTS_PLUMBING_YEAR


@pytest.mark.parametrize(
    "question",
    ["UPDATES: HVAC", "Updates: HVAC", "Year of HVAC Update", "Updates: Heating"],
)
def test_sov_building_improvemnts_heating_year(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.BUILDING_IMPROVEMENTS_HEATING_YEAR


@pytest.mark.parametrize(
    "question",
    ["UPDATES: ELEC", "Updates: Wiring", "Year of Electrical Update", "Updates: Wire"],
)
def test_sov_building_improvemnts_wiring_year(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT)
    assert result == FactSubtypeID.BUILDING_IMPROVEMENTS_WIRING_YEAR


@pytest.mark.parametrize(
    "question",
    ["Description", "Property Description", "Building Description", "Building Name and Use"],
)
def test_sov_property_description(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.TEXT, parent_type=ParentType.STRUCTURE)
    assert FactSubtypeID.PROPERTY_DESCRIPTION in result


@pytest.mark.parametrize(
    "question,parent_type",
    [("Description", None), ("Description", ParentType.BUSINESS)],
)
def test_sov_not_match_property_description_for_non_premise(question, parent_type, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=parent_type)
    assert result != FactSubtypeID.PROPERTY_DESCRIPTION


@pytest.mark.parametrize(
    "question",
    ["EDP", "VALUE EDP", "EDP VALUE", "ELECTRONIC DATA PROCESSING", "ELECTRONIC DATA PROCESSING VALUE"],
)
def test_sov_edp(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.BUSINESS)
    assert result == FactSubtypeID.EDP


@pytest.mark.parametrize(
    "question",
    ["Year of Roof Update", "Roof Update Year", "Roof Year", "Roofing Year"],
)
def test_sov_building_improvements_roofing_year(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.BUILDING_IMPROVEMENTS_ROOFING_YEAR


@pytest.mark.parametrize(
    "question",
    ["Year of Plumbing Update", "Plumbing Update Year", "Plumb Year", "Plumbing Year"],
)
def test_sov_building_improvements_plumbing_year(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.BUILDING_IMPROVEMENTS_PLUMBING_YEAR


@pytest.mark.parametrize(
    "question",
    [
        "Year of Electrical Update",
        "Year of Wiring Update",
        "Wiring Update Year",
        "Electrical Update Year",
        "Electric Year",
        "Electrical Year",
        "Wiring Year",
    ],
)
def test_sov_building_improvements_wiring_year(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.BUILDING_IMPROVEMENTS_WIRING_YEAR


@pytest.mark.parametrize(
    "question",
    [
        "Year of Heating Update",
        "Year of HVAC Update",
        "Heating Update Year",
        "HVAC Update Year",
        "Heating Year",
        "HVAC Year",
    ],
)
def test_sov_building_improvements_heating_year(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.BUILDING_IMPROVEMENTS_HEATING_YEAR


@pytest.mark.parametrize(
    "question",
    [
        "Have limits for fences, signs, and light poles been included in the Business Personal Property Limit if over 1,000 feet from the building?"
    ],
)
def test_sov_fence_bpp_limits(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.FENCE_BPP_LIMITS


@pytest.mark.parametrize(
    "question",
    ["Have you reported Special Equipment limits in the reported Building Limit?"],
)
def test_sov_special_equipment_limit(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.SPECIAL_EQUIPMENT_LIMIT


@pytest.mark.parametrize(
    "question",
    ["Employee Tools: Limit", "Tool Value"],
)
def test_sov_tool_value(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.TOOL_VALUE


@pytest.mark.parametrize(
    "question",
    ["# of Techs", "# of Technicians", "Technicians", "Techs"],
)
def test_sov_technicians(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.TECHNICIANS


@pytest.mark.parametrize(
    "question",
    ["AR: Limit", "Ar Limit", "Accounts Receivable", "Accounts Receivable: Limit"],
)
def test_sov_ar_limit(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.AR_LIMIT


@pytest.mark.parametrize(
    "question",
    ["Valuables Limit", "Valuable Limit", "Valuable Papers & Storage Limit"],
)
def test_sov_valuables_limit(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.VALUABLES_LIMIT


@pytest.mark.parametrize(
    "question",
    [
        "ED - Hardware/Media Limit (If limit is greater than $500,000, list limits by location)",
        "ED Hardware Limit",
        "Hardware Limit",
    ],
)
def test_sov_ed_hardware_limit(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.ED_HARDWARE_LIMIT


@pytest.mark.parametrize(
    "question",
    [
        "ED - Extra Expense Limit (If limit is greater than $500,000, list limits by location)",
        "ED Extra Limit",
        "Extra Limit",
        "Extra Expense Limit",
    ],
)
def test_sov_ed_extra_limit(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.ED_EXTRA_LIMIT


@pytest.mark.parametrize(
    "question",
    ["Special Classes Limit", "Special Limit"],
)
def test_sov_special_limit(question, sov_facts_identifier):
    result = sov_facts_identifier.identify(
        value=question, field_type=FieldType.NUMBER, parent_types={ParentType.SUBMISSION}
    )
    assert result == FactSubtypeID.SPECIAL_LIMIT


@pytest.mark.parametrize(
    "question",
    [
        "Have you reported a Limit for Charging Stations in the reported Building Limit?",
        "Charging Station Limit",
        "Has Charging Station Limit",
    ],
)
def test_sov_has_charging_station_limit(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.HAS_CHARGING_STATION_LIMIT


@pytest.mark.parametrize(
    "question",
    ["total revenue", "revenues", "revenue", "net revenue", "total operating revenues", "Food Revenue"],
)
def test_sov_revenue(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.TOTAL_SALES


@pytest.mark.parametrize(
    "question",
    ["total current assets", "current assets", "total curr. assets", "total current assets "],
)
def test_sov_current_assets(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.CURRENT_ASSETS


@pytest.mark.parametrize(
    "question",
    ["total other assets", "other assets", "other assets - net"],
)
def test_sov_other_assets(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.OTHER_ASSETS


@pytest.mark.parametrize(
    "question",
    ["total assets", "totals assets"],
)
def test_sov_total_assets(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.TOTAL_ASSETS


@pytest.mark.parametrize(
    "question",
    [
        "total current liabilities",
        "current liabilities",
        "tot. curr. liab.",
        "total current liabilities ",
    ],
)
def test_sov_current_liabilities(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.CURRENT_LIABILITIES


@pytest.mark.parametrize(
    "question",
    ["total liabilities", "total liabilities and deferred inflows"],
)
def test_sov_total_liabilities(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.TOTAL_LIABILITIES


@pytest.mark.parametrize(
    "question",
    [
        "net operating income (loss)",
        "operating income or (loss)",
        "income from operations",
        "net operating income",
        "operating income",
        "operating income (loss)",
        "operating (loss) income",
        "operating expenses (exclusive of items shown separately below)",
    ],
)
def test_sov_operating_income(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert FactSubtypeID.OPERATING_INCOME in result


@pytest.mark.parametrize(
    "question",
    [
        "net income (loss)",
        "net income",
        "net income (loss) before taxes",
        "net income or (loss)",
        "net income (exhibit b)",
        "consolidated net (loss) income",
        "net income before taxes",
        "total net income",
    ],
)
def test_sov_net_income(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.NET_INCOME


@pytest.mark.parametrize(
    "question",
    [
        "net increase/(decrease) in cash",
        "change in cash and cash equivalents",
        "net increase in cash and cash equivalents",
        "net increase (decrease) in cash",
        "net (decrease) increase in cash and cash equivalents",
        "net change in cash and cash equivalents",
        "increase (decrease) in cash and cash equivalents",
        "net increase in cash",
        "net increase in cash and equivalents",
        "increase in cash",
        "decrease in cash",
        "increase (decrease) in cash",
        "net increase (decrease) in cash, cash equivalents and restricted cash",
        "net (decrease) in cash and cash equivalents",
    ],
)
def test_sov_cashflow(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.CASHFLOW


@pytest.mark.parametrize(
    "question",
    [
        "net retained earnings",
        "retained earnings",
        "retained earning (deficit)",
        "retained earnings, december 31, 2019",
        "retained earnings, december 31, 2020",
        "retained earnings (deficit)",
        "retained earnings - beginning of year",
        "retained earnings - end of year",
        "retained earnings, beginning",
        "retained earnings, ending",
        "retained earnings/(deficit)",
        "retained earnings/ytd",
        "retained earnings - current year",
        "retained earnings (deficit) - beginning of year",
        "retained earnings (deficit) - end of year",
        "retained earnings - current",
        "retained earnings - beginning",
        "retained earnings - ending",
        "retained earnings/equity/pic ",
    ],
)
def test_sov_retained_earnings(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.RETAINED_EARNINGS


@pytest.mark.parametrize(
    "question",
    [
        "total stockholders' equity",
        "total equity",
        "total stockholder's equity",
        "equity",
        "total stockholders` equity",
        "total shareholders' equity",
        "stockholders' equity",
        "total stockholders' equity (deficit)",
        "total equity (deficit)",
        "total shares and equity",
        "total stockholder`s equity (deficit)",
        "stockholder`s equity, december 31, 2021",
    ],
)
def test_sov_equity(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.EQUITY


@pytest.mark.parametrize(
    "question",
    [
        "other income",
        "total other (income)/ exp.",
        "total other income",
        "total other income and expense",
        "other income (expense), net",
        "total other income, net",
        "other income / (expenses)",
        "total other income (expense)",
        "other income - net",
        "total other income - net",
        "net other income",
        "total other income (expense), net",
        "total other income and (expenses)",
        "total other income/(expense)",
        "total other income/expenses",
    ],
)
def test_sov_other_income(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.OTHER_INCOME


@pytest.mark.parametrize(
    "question",
    ["gross profit", "gross margin", "estimated gross profit to date"],
)
def test_sov_gross_profit(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.GROSS_PROFIT


@pytest.mark.parametrize(
    "question",
    [
        "other current & long-term liabilities",
        "total other liabilities",
        "other liabilities",
    ],
)
def test_sov_other_liabilities(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert result == FactSubtypeID.OTHER_LIABILITIES


@pytest.mark.parametrize(
    "question",
    [
        "net sales",
    ],
)
def test_sov_net_sales(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result == FactSubtypeID.NET_SALES


def test_alcohol_served_negative(identify):
    result = identify(value="Liquor Limit Requested", field_type=FieldType.TEXT)
    assert result != FactSubtypeID.ALCOHOL_SERVED


def test_employee_count_negative(identify):
    result = identify(value="Do all employees have alcohol training?", field_type=FieldType.NUMBER)
    assert result != FactSubtypeID.EMPLOYEE_COUNT


@pytest.mark.parametrize(
    "question",
    [
        "Total # Employees",
        "# Total Employees\nper Location",
        "Total # F/T Empl.",
        "TOTAL NUMBER of BUDGETED\nEMPLOYEES: 2023/24",
        "Total Number of Employees",
        "TOTAL NUMBER OF ON SITE EMPLOYEES",
        "Total # of Full Time Employees @ Location",
        "employee count",
        "employees count",
    ],
)
def test_employee_count(question, identify):
    result = identify(value="Total # of Employees", field_type=FieldType.NUMBER)
    assert result == FactSubtypeID.EMPLOYEE_COUNT


@pytest.mark.parametrize(
    "question",
    ["Year of License", "Year of the License", "First Driving Year", "License Year"],
)
def test_driver_year_started_driving(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.DRIVER)
    assert FactSubtypeID.DRIVER_YEAR_STARTED_DRIVING in result


@pytest.mark.parametrize(
    "question",
    [
        "Stock",
        "Stock Value",
        "Inventory \u5e93\u5b58\u4ef7\u503c",
        "STOCK",
        "Inventory",
        "Stock                        Value",
        "Stock/Inventory",
        "Stock/Inventory Value",
        "Stock Values  (RC)",
    ],
)
def test_inventory_value(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.BUSINESS)
    assert FactSubtypeID.INVENTORY_VALUE in result


@pytest.mark.parametrize(
    "question",
    [
        "Eqpt. & B&M",
        "Machinery & Equipment",
        "M&E Values",
        "Machinery and Equipment",
        "Equipment",
        "Machinery & Equip.",
    ],
)
def test_equipment_limit(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.EQUIPMENT)
    assert FactSubtypeID.EQUIPMENT_LIMIT in result


def test_limit(provide_propositions):
    result = provide_propositions(value="Limit", field_type=FieldType.NUMBER, parent_type=ParentType.BUSINESS)
    assert FactSubtypeID.EQUIPMENT_LIMIT not in result
    assert FactSubtypeID.TIV in result


def test_value(provide_propositions):
    result = provide_propositions(value="Value", field_type=FieldType.NUMBER, parent_type=ParentType.BUSINESS)
    assert FactSubtypeID.EQUIPMENT_LIMIT not in result
    assert FactSubtypeID.TIV in result


@pytest.mark.parametrize(
    "question",
    ["TIB", "TIB's", "TI&B", "TIB (Buildout) Value"],
)
def test_sov_tib(question, provide_propositions):
    result = provide_propositions(value=question, field_type=FieldType.NUMBER, parent_type=ParentType.PREMISES)
    assert FactSubtypeID.TENANTS_IMPROVEMENTS_AND_BETTERMENTS in result


@pytest.mark.parametrize(
    "question", ["What is vacant (in ft) area?", "vacant land size", "vacant in acres", "vacant size"]
)
def test_vacant_land_size(question, identify):
    result = identify(value=question, field_type=FieldType.NUMBER)
    assert result == FactSubtypeID.VACANT_LAND_SIZE


@pytest.mark.parametrize("question", ["FEIN", "Federal Employer Identification Number"])
def test_fein(question, identify):
    result = identify(value=question, field_type=FieldType.TEXT_ARRAY)
    assert result == FactSubtypeID.FEIN


@pytest.mark.parametrize(
    "parent_types, matches",
    [
        (None, False),
        ({ParentType.BUSINESS, ParentType.STRUCTURE, ParentType.PREMISES, ParentType.VEHICLE}, True),
        ({ParentType.STRUCTURE, ParentType.PREMISES}, True),
        ({ParentType.BUSINESS, ParentType.DRIVER}, False),
        (set(), False),
    ],
)
def test_sov_owner(parent_types, matches, sov_facts_identifier):
    result = sov_facts_identifier.identify(value="Owner", field_type=FieldType.TEXT, parent_types=parent_types)
    assert result == FactSubtypeID.OWNER if matches else result != FactSubtypeID.OWNER


def test_total_sales(sov_facts_identifier):
    result = sov_facts_identifier.identify(
        value="gross sales", field_type=FieldType.NUMBER, parent_types={ParentType.BUSINESS}
    )
    assert result == FactSubtypeID.TOTAL_SALES


def _get_column_identification_test_data() -> Iterable[tuple[str, FactSubtypeID, ParentType]]:
    with open(CURRENT_DIR / "../data/column_identification_examples.json") as f:
        identification_objects = json.load(f)
        result = [
            (
                test_object["column_name"],
                FactSubtypeID(test_object["fact_subtype_id"]),
                ParentType(test_object["parent_type"]),
            )
            for test_object in identification_objects
        ]
        return iter(result)


@pytest.mark.parametrize("field_name, fact_subtype_id, parent_type", _get_column_identification_test_data())
def test_generic_identify(field_name, fact_subtype_id, parent_type, sov_facts_identifier):
    result = sov_facts_identifier.identify(value=field_name, parent_types={parent_type})
    assert result == fact_subtype_id


def randomize_spaces(text):
    def random_replacement():
        replacements = list(string.punctuation) + [p + " " for p in string.punctuation] + [" "]
        return random.choice(replacements)

    return "".join(random_replacement() if char == " " else char for char in text)


@pytest.mark.parametrize("field_name, fact_subtype_id, parent_type", _get_column_identification_test_data())
def test_generic_identify_random_punctuation(field_name, fact_subtype_id, parent_type, sov_facts_identifier):
    random.seed(0)
    field_name = randomize_spaces(field_name)
    result = sov_facts_identifier.identify(value=field_name, parent_types={parent_type})
    assert result == fact_subtype_id


@pytest.mark.parametrize("regexes", [REGULAR_EXPRESSIONS_SOV])
def test_all_phrases_are_tuple(regexes) -> None:
    for fact, matchers in regexes.items():
        for matcher in matchers:
            if hasattr(matcher, "phrases"):
                for phrase in matcher.phrases:
                    assert isinstance(phrase, tuple), f"{fact} {phrase}"


def test_sov_facts_identifier_copes_with_not_known_subtype():
    """
    Test that the SOV facts identifier can cope with a fact subtype that is not known.
    """
    fact_subtypes = MagicMock(id="NOT_KNOWN_SUBTYPE_2137", is_deprecated=False, is_selectable=True)
    sov_facts_identifier = SOVFactsIdentifier([fact_subtypes])
