[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.setuptools.package-data]
"*" = ["*.onnx", "*.typed"]

[project]
name = "datascience_common"
version = "25.5.27.32285.dev0"
description = "Kalepa data science common package"
authors = [{ name = "Kalepa Tech" }]
readme = "README.md"
requires-python = ">=3.11,<3.12"

dependencies = [
    "en-core-web-sm",
    "negspacy~=1.0.4",
    "python-dateutil>=2.8.1, <3.0.0",
    "PyMuPDFb!=1.23.7",
    "pymupdf>=1.22.5,!=1.23.7,<2.0.0",
    "werkzeug>0.0.0",
    "scikit-learn>0.1.0, <2.0.0",
    "sentry-sdk~=1.0",
    "numpy>1.0.0, <2.0.0",
    "joblib>0.0.0",
    "fuzzywuzzy~=0.18",
    "python-levenshtein>0.12.2",
    "redshift-connector[full]~=2.0.909",
    # forked Spacy 3.4.1 with Pydantic v2 compatibility
    "spacy>=3.4.5,<3.5",
    "pandas>=1.5.3, <3",
    "sshtunnel~=0.4.0",
    "paramiko>=3.4.1",
    "measurement==4.0a8",
    "pyxlsb>=1.0.10",
    "common>0.0.0",
    "copilot-client-v3>0.0.0",
    "entity-resolution-service-client>0.0.0",
    "entity-resolution-service-client-v3>0.0.0",
    "facts-client>0.0.0",
    "facts-client-v2>0.0.0",
    "infrastructure-common>0.0.0",
    "static-common>0.0.0",
    "xlrd>0.0.0",
    "pinecone[grpc]~=5.0",
]


[project.optional-dependencies]
lob-classifier = [
    "onnxruntime==1.16.3",
    "tensorflow==2.15.1",
    "tensorflow-hub==0.16.1",
    "tensorflow-cpu==2.15.1; sys_platform=='darwin' and platform_machine!='arm64' and platform_machine!='aarch64'",
    "tensorflow-cpu==2.15.1; sys_platform=='linux' and platform_machine!='arm64' and platform_machine!='aarch64'",
    "tensorflow-cpu-aws==2.15.1; sys_platform=='linux' and (platform_machine=='arm64' or platform_machine=='aarch64')",
    "tensorflow-io-gcs-filesystem==0.36.0",
    "h5py==3.10.0",
]


[dependency-groups]
dev = [
    "pytest~=7.2.0",
    "openpyxl~=3.1.2",
    "xlsxwriter~=3.2.0",
    "pytest-xdist[psutil]~=3.6.1",
]
kalepa = [
    "copilot-client-v3>0.0.0",
    "entity-resolution-service-client-v3>0.0.0",
    "facts-client>0.0.0",
    "facts-client-v2>0.0.0",
    "infrastructure-common>0.0.0",
    "static-common>0.0.0",
    "onnxruntime>0.0.0",
    "xlrd>0.0.0",
]

[tool.uv.sources]
copilot-client-v3 = { index = "kalepi" }
entity-resolution-service-client-v3 = { index = "kalepi" }
facts-client = { index = "kalepi" }
facts-client-v2 = { index = "kalepi" }
infrastructure-common = { index = "kalepi" }
onnxruntime = { index = "kalepi" }
static-common = { index = "kalepi" }
xlrd = { index = "kalepi" }
en-core-web-sm = { index = "kalepi" }
spacy = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[tool.uv]
package = true
default-groups = ["dev", "kalepa"]

[tool.black]
line-length = 120
target-version = ['py38']

[tool.isort]
profile = "black"
skip = ["__init__.py"]

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
line-length = 120
target-version = "py38"
extend-exclude = ["test/", "examples/", "**/__init__.py"]
