import re
import traceback
from typing import Dict, List, Optional, Sequence, Set, Union

from common.utils.logging import log_fact_subtype_mapping_attempt
from facts_client.model.fact_subtype import FactSubtype
from infrastructure_common.logging import get_logger
from static_common.constants import NON_FLEET_PARENT_TYPES
from static_common.enums.enum import StrEnum
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.parent import ParentType

from datascience_common.classifiers.fact_identification.common.constants import (
    BUILDING_KEYWORDS,
    BUSINESS_KEYWORDS,
    CONSTRUCTION_KEYWORDS,
    DETECTION_KEYWORDS,
    FOOT_KEYWORDS,
    IMPROVEMENT_KEYWORDS,
    INCOME_KEYWORDS,
    INSURANCE_KEYWORDS,
    LIVING_SPACES_KEYWORDS,
    PROPERTY_KEYWORDS,
    SPRINKLER_KEYWORDS,
    SQFT_KEYWORDS,
    SQUAR<PERSON>_KEYWORDS,
    YES_OR_NO_KEYWORDS,
)
from datascience_common.classifiers.fact_identification.common.employees_count_regexes import (
    FSID_TO_EMPLOYEES_COUNT_REGEXES,
)
from datascience_common.classifiers.fact_identification.enum_matching import EnumMatcher
from datascience_common.classifiers.fact_identification.fact_matching import (
    CUSTOM_PARENT_RESTRICTIONS,
)
from datascience_common.classifiers.fact_identification.regex_factory import (
    AdjacentWords,
    RegexFactory,
    Word,
    WordSynonyms,
)
from datascience_common.classifiers.fact_identification.text_matching import (
    EXACT_MATCH_RATIO_THRESHOLD,
    HIGH_FUZZY_MATCH_RATIO_THRESHOLD,
    MODERATE_FUZZY_MATCH_RATIO_THRESHOLD,
    VERY_HIGH_FUZZY_MATCH_RATIO_THRESHOLD,
    ExactMatcher,
    Matcher,
    RegexMatcher,
    RegexPatternMatcher,
)
from datascience_common.fact_subtypes.utils import SKIP_WORD
from datascience_common.utils.performance_utils import lazy_variable

logger = get_logger()
regex_factory = RegexFactory()


"""
Some general notes on defining regex for subtypes matching. Please be careful when adding the regex.
There are few things that can go wrong:
RegexMatcher([("Marcin",)]) - it will match any string that has Marcin within - it should be rare to use this  -
use ExactMatcher instead
ExactMatcher - has a threshold parameter for string similarity- consider using 100% most of the time
$ and others a special sign for regex and they need to be excluded with \
Add test in test generic
"""


@lazy_variable(dict)
def REGULAR_EXPRESSIONS_SOV() -> Dict[FactSubtypeID, List[Union[Matcher, re.Pattern]]]:
    return {
        FactSubtypeID.ZIP_CODE: [re.compile(r"(^(?!.*lend).*zip.*$)", re.IGNORECASE)],
        FactSubtypeID.YEAR_BUILT: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["year", "yr", "age"]),
                            WordSynonyms(
                                [
                                    *BUILDING_KEYWORDS,
                                    "built",
                                    "blt",
                                    "build",
                                    "completed",
                                    "bult",
                                    "construction",
                                    "constructed",
                                ]
                            ),
                        ]
                    ),
                    ("BUILT",),
                    ("ORIG", "YEAR", "BUILT"),
                    ("YOC",),
                ],
                negative=["Roof", "Roofing", "Coverage", "Limit", "footage"],
                characters_before=15,
                characters_between=10,
            )
        ],
        FactSubtypeID.GROSS_AREA: [
            RegexMatcher(
                [
                    *regex_factory([WordSynonyms(["Total", "Gross"]), WordSynonyms(["AREA"])]),
                    *regex_factory([Word("Acreage")]),
                ],
                negative=["BUILDING", "BLDG", "BLD"],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher(
                [
                    ("TOTAL", "BUILDING", "AREA"),
                    ("AREA", "TOTAL", "BUILDING"),
                    ("Area",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.BUILDING_SIZE: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms([*SQFT_KEYWORDS, "area", "acres"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["total", "space", "bldg", "building", SKIP_WORD]),
                            WordSynonyms(["sq", "square"]),
                            WordSynonyms(["foot", "feet", "ft", "footage", SKIP_WORD]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["Shopping Center", "occupied", "building", "bldg"]),
                            WordSynonyms(["area"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["total", "living", "garage", "space", "bldg", "building", "dwelling"]),
                            WordSynonyms(SQFT_KEYWORDS),
                        ]
                    ),
                    ("Total", "BLD", "SQ"),
                    ("Bld", "Net", "Sq", "Ft"),
                ],
                negative=[
                    "PER",
                    "COST",
                    "COSTPERSQ",
                    "$",
                    "commercial",
                    "Number",
                    "auto",
                    "autos",
                    "fence",
                    "Business Personal Property",
                    "vacant",
                    "fire",
                    "alarm",
                    "occupied",
                ],
                characters_before=25,
                characters_between=10,
            ),
        ],
        FactSubtypeID.OCCUPIED_AREA: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(
                                [
                                    "occupied",
                                ]
                            ),
                            WordSynonyms(["Area", SKIP_WORD]),
                            WordSynonyms(["sqft", "sq", "ft", SKIP_WORD]),
                        ]
                    )
                ],
                negative=["%", "percent"],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_SPRINKLERS: [
            re.compile(
                r"(SPRKLRD|SPRINK|SPRKL\'D|SPRNKLRD|SPRINKERED|SPRINKLERED|SPRINKLER){1}|((\?|SPRINKLERS|SPRINKLER|SPRNKLR|HAS)+\s*(\?|SPRINKLERS|SPRINKLER|SPRNKLR|HAS)+){1}|UNITS\s*(SPRKLRD|SPRKL\'D|SPRNKLRD|SPRINKERED|SPRINKLERED|SPRINK|SPRINKLER){1}",
                re.IGNORECASE,
            ),
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(SPRINKLER_KEYWORDS),
                            WordSynonyms(YES_OR_NO_KEYWORDS),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(SPRINKLER_KEYWORDS),
                            WordSynonyms(
                                ["type", "information", "fire", "fire extinguishing", "fire protection", SKIP_WORD]
                            ),
                        ]
                    ),
                ],
                characters_before=10,
                characters_between=10,
                negative=[
                    "NUMBER",
                    "COUNT",
                    "PERCENT",
                ],
            ),
        ],
        FactSubtypeID.SPRINKLER_AREA_COVERAGE_PERCENT: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(SPRINKLER_KEYWORDS),
                            WordSynonyms(["%", "PERCENTAGE", "PERCENT", "COVERAGE", "AREA"]),
                        ]
                    ),
                ],
                characters_before=15,
                characters_between=30,
            ),
        ],
        FactSubtypeID.SPRINKLER_COUNT: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(SPRINKLER_KEYWORDS),
                            WordSynonyms(["#", "NUMBER", "NUM", "COUNT", "TOTAL"]),
                        ]
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.NUMBER_OF_UNITS: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(
                                [
                                    *LIVING_SPACES_KEYWORDS,
                                    "applicable",
                                    "section",
                                    "senior",
                                    "hotel",
                                    "housing",
                                    "rental",
                                    "planned",
                                    "affordable",
                                    "office",
                                    "residential",
                                    "total residential",
                                    "retail",
                                ]
                            ),
                            WordSynonyms(["unit", "units"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["#", "No", "Number", "count"]),
                            WordSynonyms(["occupied", "of", SKIP_WORD]),
                            WordSynonyms(LIVING_SPACES_KEYWORDS),
                        ]
                    ),
                ],
                characters_before=20,
                characters_between=60,
                negative=["COST", "SALES", "SALE", "$"],
            ),
            ExactMatcher(
                [
                    *regex_factory([WordSynonyms(LIVING_SPACES_KEYWORDS)]),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.NUMBER_OF_COMMERCIAL_UNITS: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["#", "No", "Number", "count", SKIP_WORD]),
                            WordSynonyms(["occupied", "of", SKIP_WORD]),
                            Word("commercial"),
                            WordSynonyms(["unit", "units"]),
                        ]
                    ),
                ],
                characters_before=20,
                characters_between=60,
                negative=["COST", "SALES", "SALE", "$"],
            ),
        ],
        FactSubtypeID.NUMBER_OF_STORIES: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["STORIES", "STORY", r"ST\b", r"STOR\b", "FLOORS", "FLRS", "FLOOR"]),
                            WordSynonyms(["OF", SKIP_WORD]),
                            WordSynonyms(["#", "NUMBER", "NO", "NUM"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["STORIES", "FLOORS", "FLRS"]),
                        ]
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.NUMBER_OF_SWIMMING_POOLS: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["number", "#", "no"]),
                            WordSynonyms(["of", SKIP_WORD]),
                            WordSynonyms(["pool", "pools", "swimming pools"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["swim", "swimming"]),
                            WordSynonyms(["pool", "pools"]),
                            WordSynonyms(["unit", SKIP_WORD]),
                        ]
                    ),
                    ("pool", "count"),
                    ("pool", "num"),
                    ("Pool", "Indicate Number"),
                    ("pool", "other"),
                    ("Swimming", "outdoor"),
                ],
                characters_before=10,
                characters_between=50,
            ),
            ExactMatcher([("Pool",), ("Pools",)], threshold=VERY_HIGH_FUZZY_MATCH_RATIO_THRESHOLD),
        ],
        FactSubtypeID.HAS_SECURITY_GUARDS: [
            RegexMatcher(
                [
                    ("SECURITY", "GUARD"),
                    ("GUARD", "SERVICE"),
                    ("A/S",),
                    ("armed", "security"),
                    ("Security", "Provided"),
                    ("Security", "Employed"),
                    ("Security", "Y/N"),
                    ("Guards", "Y/N"),
                ],
                characters_before=10,
                characters_between=15,
            )
        ],
        FactSubtypeID.BURGLAR_ALARM: [
            RegexMatcher([("INSTALLS", "BURGLAR", "ALARM")], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.BURGLAR_ALARM_TYPE: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["Alarm", "Alarmm"]),
                            WordSynonyms(["Burglar", SKIP_WORD]),
                            WordSynonyms(["Type", SKIP_WORD]),
                            WordSynonyms(["Central Station", "Station", SKIP_WORD]),
                        ]
                    ),
                    *regex_factory([WordSynonyms(["Burglar"]), WordSynonyms(["Type"])]),
                ],
                characters_before=10,
                characters_between=10,
                negative=["FIRE", "SMOKE", "year", "has", "have"],
            )
        ],
        FactSubtypeID.HAS_BURGLAR_ALARM: [
            ExactMatcher([("ALARM",), ("ALARMED",), ("BURGLAR",)], threshold=EXACT_MATCH_RATIO_THRESHOLD),
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["Has", SKIP_WORD]),
                            WordSynonyms(["alarm?", "alarm", "alarmed"]),
                            WordSynonyms(["BURGLAR", SKIP_WORD]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["Central Station", "CS"]),
                            WordSynonyms(["BURGLAR"]),
                            WordSynonyms(["ALARM", SKIP_WORD, "alarm?"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["BURGLAR"]),
                            WordSynonyms([*YES_OR_NO_KEYWORDS, "central"]),
                        ]
                    ),
                ],
                characters_before=50,
                characters_between=20,
                negative=["FIRE", "SMOKE", "TYPE", "INSTALL", "INSTALLS", "type"],
            ),
        ],
        FactSubtypeID.BURGLARY_GRADE: [
            RegexMatcher([("Burglary", "Score")], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.HAS_SMOKE_DETECTOR: [
            re.compile(r"(?=.*smoke)(?=.*detector)(?!.*type)(.+)", re.IGNORECASE),
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(
                                [
                                    "smoke",
                                    "smk",
                                    "automatic smoke",
                                    "auto-matic smoke",
                                    "carbon monoxide",
                                    "CO2",
                                    "smoke / carbon monoxide",
                                ]
                            ),
                            WordSynonyms([*DETECTION_KEYWORDS, "alarm", "alarms", "hard wired", "wired", "batt"]),
                        ]
                    ),
                    ("Carbon", "Monoxide", "Detector"),
                    ("Carbon", "Monoxide"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.SMOKE_DETECTOR_TYPE: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["smoke", "smk", "carbon monoxide", "smoke / carbon monoxide"]),
                            WordSynonyms([*DETECTION_KEYWORDS, "alarm", "hard wired", "wired", "batt", SKIP_WORD]),
                            WordSynonyms(["type"]),
                        ]
                    ),
                ],
                characters_before=10,
                characters_between=10,
                negative=["FIRE", "BURGLAR"],
            ),
            ExactMatcher([("SMOKE",)], threshold=EXACT_MATCH_RATIO_THRESHOLD),
        ],
        FactSubtypeID.HAS_FIRE_ALARM: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["Has", SKIP_WORD]),
                            WordSynonyms(["alarm?", "alarm", "alarmed"]),
                            WordSynonyms(["fire", SKIP_WORD]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["Central Station", "CS"]),
                            WordSynonyms(["fire"]),
                            WordSynonyms(["alarm", "alarm?", SKIP_WORD]),
                        ]
                    ),
                    ("CENTRAL", "STATION", "ALARM", "SYSTEM"),
                    ("CENTRAL", "STATION", "FIRE"),
                    ("FIRE", "ALARM"),
                    ("CENTRAL", "STATION", "ALARM"),
                    ("FIRE", "ALARMS"),
                    ("Alarm", "Y", "N"),
                    ("FIRE", "ALARM", "Building"),
                    ("FIRE", "ALARM", "Bldg"),
                ],
                characters_before=20,
                characters_between=20,
                negative=["BURGLAR", "SMOKE", "TYPE"],
            )
        ],
        FactSubtypeID.FIRE_ALARM_TYPE: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["fire"]),
                            WordSynonyms(["alarm", "safety"]),
                            WordSynonyms(["type", "system", "systems", SKIP_WORD]),
                        ]
                    )
                ],
                characters_before=10,
                characters_between=10,
                negative=["BURGLAR", "SMOKE"],
            )
        ],
        FactSubtypeID.GL_CODE: [
            RegexMatcher(
                [("GL", "Code"), ("GL", "Class")],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.FOUNDATION_TYPE: [
            RegexMatcher(
                [
                    ("Foundation",),
                ],
                characters_before=15,
                characters_between=10,
            )
        ],
        FactSubtypeID.ROOF_TYPE: [
            RegexMatcher(
                [
                    ("ROOF", "TYPE"),
                    ("ROOFING", "MATERIAL"),
                    ("ROOF", "COVERING"),
                    ("ROOF", "Const"),
                    ("ROOF", "COMPOSITION"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.ROOF_SHAPE: [
            RegexMatcher([("ROOF", "SHAPE"), ("Roof", "Geometry")], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.LIQUOR_SALES: [
            RegexMatcher(
                [
                    ("LIQUOR", "SALES"),
                    ("LIQUOR", "REVENUE"),
                    ("Liq", "Rev"),
                    ("alcohol", "receipts"),
                    ("liquor", "receipts"),
                    ("alcohol", "sales"),
                ],
                characters_before=50,
                characters_between=50,
            )
        ],
        FactSubtypeID.ALCOHOL_SERVED: [
            RegexMatcher(
                [("LIQUOR",)], characters_before=10, characters_between=10, negative=["Limit", "Sales", "Receipts"]
            )
        ],
        FactSubtypeID.CONSTRUCTION_CLASS: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(CONSTRUCTION_KEYWORDS),
                            WordSynonyms(["class", "type", "iso", "description", "quality", "details"]),
                        ],
                        permutate=True,
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(BUILDING_KEYWORDS),
                            WordSynonyms(["class", "type"]),
                        ],
                        permutate=True,
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(BUILDING_KEYWORDS),
                            WordSynonyms(CONSTRUCTION_KEYWORDS),
                            WordSynonyms([SKIP_WORD, "class", "type"]),
                        ],
                        permutate=True,
                    ),
                    *regex_factory([WordSynonyms(["FRAME/MAS", "iso description", "building_class"])]),
                ],
                characters_before=10,
                characters_between=10,
                negative=[
                    "COST",
                    "PER",
                    "$",
                    "%",
                    "condition",
                    "contents",
                    "content",
                    "Subcontractors",
                    "program",
                    "income",
                ],
            ),
            ExactMatcher(
                [
                    *regex_factory(
                        [WordSynonyms(CONSTRUCTION_KEYWORDS)],
                    ),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.CONSTRUCTION_CODE: [
            RegexMatcher(
                [
                    ("CONSTRUCTION", "CODE"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.FIRE_DISTRICT: [
            RegexMatcher(
                [
                    (
                        "Central",
                        "Station",
                        "Fire",
                    ),
                    (
                        "Local",
                        "Station",
                        "Fire",
                    ),
                    ("Central", "Station", "Fire", "Alarm"),
                    ("Local", "Station", "Fire", "Alarm"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.FIRE_PROTECTION: [
            RegexMatcher(
                [("FIRE", "PROTECTION"), ("FIRE", "ALARM"), ("FIRE", "SUPPRESSION")],
                negative=["ISO", "CLASS"],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.FIRE_PROTECTION_CLASS: [
            RegexMatcher(
                [("PROTECTION", "CLASS"), ("PC",), ("Prot", "Class")],
                characters_before=10,
                characters_between=10,
                negative=["ISO", "PPC", "PCT"],
            )
        ],
        FactSubtypeID.ISO_FIRE_PROTECTION_CLASS: [
            RegexMatcher(
                [
                    ("ISO", "PROT", "CLASS"),
                    ("ISO", "PROTECTION", "CLASS"),
                    ("ISO", "PC"),
                    ("ISO", "Class"),
                    ("Protection", "Class"),
                    ("PPC",),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PARKING_SPACES_COUNT: [
            RegexMatcher(
                [
                    ("PARKING", "SPACES"),
                    ("#", "SPACES"),
                    ("PARKING", "SPOTS"),
                    ("PARKING", "UNIT"),
                    ("#", "of", "Parking", "Spots"),
                ],
                characters_before=20,
                characters_between=20,
            )
        ],
        FactSubtypeID.PARKING_FEES: [
            RegexMatcher(
                [
                    ("PAID", "PARKING"),
                ],
                characters_before=20,
                characters_between=20,
            )
        ],
        FactSubtypeID.NUMBER_OF_ROOMS: [
            RegexMatcher([("#", "ROOM"), ("No", "of", "Rooms")], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.BUILDING_VALUE: [
            RegexPatternMatcher(
                patterns=[
                    r"(Building|Buildings)\s*\d+(?:,\d{3})*(?:\.\d+)?",  # Building 12345
                    r"\d+\s*(Building|Buildings)",  # 12345 Building
                    r"BLDG\s*\(\s*\d+(?:,\d{3})*(?:\.\d+)?\s+Unit[Ss]?\s*\)",  # BLDG (123 Units)
                    r"BLDGS\s*\(\s*\d+(?:,\d{3})*(?:\.\d+)?\s+Unit[Ss]?\s*\)",  # BLDGS (123 Units)
                    r"\(\s*\d+(?:,\d{3})*(?:\.\d+)?\s+Unit[Ss]?\s*\)\s*BLDG",  # (123 Units) BLDG
                    r"\(\s*\d+(?:,\d{3})*(?:\.\d+)?\s+Unit[Ss]?\s*\)\s*BLDGS",  # (123 Units) BLDGS
                ],
                negative=["ITV", "TIV", "BPP", "Business personal property", "#", "of", "frame", "# of Buildings"],
            ),
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms([SKIP_WORD, "total", "insured"]),
                            WordSynonyms(BUILDING_KEYWORDS),
                            WordSynonyms(
                                [
                                    "val",
                                    "value",
                                    "values",
                                    "valuation",
                                    "limit",
                                    "coverage",
                                    "cov",
                                    r"\$",
                                    "amount",
                                    "total",
                                ]
                            ),
                        ],
                        permutate=True,
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(BUILDING_KEYWORDS),
                            WordSynonyms(
                                [
                                    "1",
                                    "blanket",
                                    "current",
                                    "travelers",
                                    "primary",
                                    "warehouse",
                                    "office",
                                ]
                            ),
                        ],
                        permutate=True,
                    ),
                    ("REAL", "PROPERTY", "VALUE"),
                    ("Real", "Property"),
                    ("BLDG", "@", "100"),
                    ("Building", "replacement", "cost"),
                    ("replacement", "cost"),
                ],
                negative=[
                    "EQUIPMENT",
                    "#",
                    "no",
                    "(Unit)",
                    "BPP",
                    "Business personal property",
                    "Business Property",
                    "personal property",
                    "contents",
                    "condition",
                    "description",
                    "Replacement cost",
                    "Y / N",
                    "total property value",
                    "total by",
                    "num",
                    "auto",
                    "number",
                    "name and use",
                    "maintenance",
                    "replacement value",
                    "frame",
                    "# of Buildings",
                    "improvements",
                    "betterments",
                    "tiv",
                    "elevation",
                ],
                characters_before=20,
                characters_between=10,
            ),
            ExactMatcher(
                [
                    *regex_factory([WordSynonyms(BUILDING_KEYWORDS)]),
                    (r"\$ Building",),
                    ("Building & GL",),
                    ("Building / I&B",),
                    ("Bldg", "value"),
                ],
                threshold=HIGH_FUZZY_MATCH_RATIO_THRESHOLD,
                negative=["# of Buildings"],
            ),
        ],
        FactSubtypeID.HAS_FITNESS_CENTER: [RegexMatcher([("FITNESS",)], characters_before=10, characters_between=10)],
        FactSubtypeID.OCCUPATION_PERCENT: [
            ExactMatcher(
                [("OCCP",), ("Percent OCCUPIED",)], negative=["number", "# of"], threshold=EXACT_MATCH_RATIO_THRESHOLD
            ),
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["occ", "OCCUPANCY", "OCCUPIED", "occp"]),
                            WordSynonyms(["%", "perc", "percent", "percentage"]),
                            WordSynonyms([SKIP_WORD, "BUILDING"]),
                        ]
                    ),
                ],
                characters_before=10,
                characters_between=10,
                negative=["number", "# of"],
            ),
        ],
        FactSubtypeID.CONTENT_VALUE: [
            RegexMatcher(
                [
                    ("CONTENTS",),
                    ("BUILDING", "CONTENTS", "TOTAL"),
                    ("Content",),
                    ("Content", "Value"),
                    (r"\$", "CNTS"),
                    ("Ctnt",),
                ],
                characters_before=10,
                characters_between=10,
                negative=["BUSINESS", "EDP", "SALES", "BPP"],
            ),
            RegexMatcher(
                [
                    ("Content", "Value"),
                ],
                characters_before=50,
                characters_between=50,
                negative=["BUSINESS", "EDP", "SALES", "BPP"],
            ),
        ],
        FactSubtypeID.BUILDING_IMPROVEMENTS_VALUE: [
            RegexMatcher(
                [
                    ("BUILDING", "IMPROVEMENTS"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.LAND_USE: [
            RegexMatcher(
                [
                    (
                        "Building",
                        "Usage",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.SECURITY_CAMERAS: [
            RegexMatcher([("SECURITY", "CAMERAS"), ("CAMERA",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.DISTANCE_TO_FIRE_STATION: [
            RegexMatcher(
                [
                    *regex_factory(
                        [WordSynonyms(["Dist", "Distance"]), WordSynonyms(["Fire"]), WordSynonyms(["Station"])]
                    )
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DISTANCE_TO_BODY_OF_WATER: [
            RegexMatcher(
                [*regex_factory([Word("Water"), WordSynonyms(["Body"]), WordSynonyms(["Distance", "Dist", "Dist_"])])],
            )
        ],
        FactSubtypeID.DISTANCE_TO_FIRE_HYDRANT: [
            RegexMatcher(
                [
                    ("Dist", "fire", "hydr"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.EMPLOYEE_COUNT: [
            RegexMatcher(
                [
                    ("EMPLOYEE", "COUNT"),
                    ("#", "EMPLOYEE"),
                    ("NUMBER", "EMPLOYEE"),
                    ("#", "empl"),
                    ("NO", "OF", "EMPLOYEES"),
                ],
                characters_before=33,
                characters_between=10,
                negative=["alcohol", "training"],
            )
        ],
        FactSubtypeID.RPV: [RegexMatcher([("REAL PROPERTY VALUE",)], characters_before=10, characters_between=10)],
        FactSubtypeID.BUILDING_TYPE: [
            RegexMatcher(
                [*regex_factory([WordSynonyms(BUILDING_KEYWORDS), WordSynonyms(["type", "shape", "details"])])],
                characters_before=10,
                characters_between=10,
                negative=[
                    "description",
                ],
            )
        ],
        FactSubtypeID.OPERATIONS: [
            RegexMatcher(
                [("OPERATIONS",), ("DESCRIPTION", "OF", "OPERATIONS")],
                negative=["RADIUS"],
                characters_before=20,
                characters_between=10,
            )
        ],
        FactSubtypeID.OPEN_TO_PUBLIC_AREA: [
            RegexMatcher(
                [
                    (
                        "open",
                        "to",
                        "public",
                    ),
                ],
                characters_before=20,
                characters_between=10,
            )
        ],
        FactSubtypeID.PROPERTY_RIGHT_OF_USE: [
            RegexMatcher(
                [("OWN", "LEASE"), ("OWNED", "OR", "MANAGED"), ("OWNED", "OR", "MGD"), ("OWNED", "OR", "MG D")],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.TIV: [
            ExactMatcher(
                [("TOTAL",), ("TOTALS",), ("TOTALVALUE",), ("LIMIT",), ("Value",)],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
                negative=["security"],
            ),
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["total", "revised", "100%"]),
                            WordSynonyms([*INSURANCE_KEYWORDS, "property"]),
                            WordSynonyms(["value", "values", SKIP_WORD]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(BUILDING_KEYWORDS),
                            WordSynonyms(["tiv"]),
                        ]
                    ),
                    (r"\bTIV\b",),
                    (r"\bTIV\d+",),
                    ("TOTAL", "TIV"),
                    ("LOCATION", "TOTAL"),
                    ("TOTAL", "Loc"),
                    ("TOTAL", "Bldg"),
                ],
                negative=["BI", "Tool", "Inactive", "effective", "sq ft", "sqft", "sq", "ft", "security", "count"],
                characters_before=25,
                characters_between=15,
            ),
        ],
        FactSubtypeID.ITV: [
            RegexMatcher([("ITV",), ("BUILDING", "ITV")], characters_before=33, characters_between=33, negative=["TIV"])
        ],
        FactSubtypeID.RCP: [RegexMatcher([("RCP",)], characters_before=10, characters_between=10)],
        FactSubtypeID.FRS: [RegexMatcher([("FRS",)], characters_before=10, characters_between=10)],
        FactSubtypeID.POLICY_NUMBER: [
            RegexMatcher([("POLICY", "NUMBER"), ("POLICY", "#")], characters_before=33, characters_between=10)
        ],
        FactSubtypeID.PARKING_VALET: [
            RegexMatcher([("VALET", "PARKING")], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.LENDER_ZIP_CODE: [RegexMatcher([("LENDER", "ZIP")], characters_before=10, characters_between=10)],
        FactSubtypeID.LENDER_STREET: [
            RegexMatcher([("LENDER", "STREET")], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.LENDER_CITY: [RegexMatcher([("LENDER", "CITY")], characters_before=10, characters_between=10)],
        FactSubtypeID.LENDER_STATE: [RegexMatcher([("LENDER", "STATE")], characters_before=10, characters_between=10)],
        FactSubtypeID.LENDER_NAME: [RegexMatcher([("LENDER", "NAME")], characters_before=10, characters_between=10)],
        FactSubtypeID.LOSS_OF_RENT_INCOME: [
            RegexMatcher(
                [("LOSS", "RENT", "INCOME"), ("LOSS", "RENT"), ("LOSS", "OF", "RENTS")],
                characters_before=10,
                characters_between=10,
                negative=["BI/EE", "VALUE/RENTS", "BUSINESS INCOME"],
            )
        ],
        FactSubtypeID.LOSS_OF_INCOME: [
            RegexMatcher([("LOSS", "BUSINESS", "INCOME")], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.SALES_ESTIMATE: [
            RegexMatcher(
                [
                    ("SALE", "ESTIMATE"),
                    (
                        "Projected",
                        "Sales",
                    ),
                    ("Estimated", "Annual", "Revenue"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DELIVERY_SALES: [
            RegexMatcher([("SALE", "DELIVERY")], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.HOTEL_SALES: [RegexMatcher([("SALES", "HOTEL")], characters_before=10, characters_between=10)],
        FactSubtypeID.TOTAL_SALES: [
            ExactMatcher([(r"total sales in \\$",)]),
            ExactMatcher(
                [
                    ("total revenue",),
                    ("revenues",),
                    ("revenue",),
                    ("net revenue",),
                    ("total operating revenues",),
                    ("gross sales",),
                    (
                        "tot",
                        "rev",
                    ),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "TOTAL",
                        "SALES",
                    ),
                    ("REVENUE",),
                ],
                negative=["LIQUOR"],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.FOOD_SALES: [
            RegexMatcher(
                [
                    ("RESTAURANT", "SALES"),
                    ("FOOD", "RECEIPTS"),
                    ("Restaurant", "RECEIPTS"),
                    ("Restaurants", "w/table"),
                    ("Restarurant", "Sales"),
                    ("food", "sales"),
                    ("total", "food", "sales"),
                ],
                characters_before=33,
                characters_between=25,
            )
        ],
        FactSubtypeID.NET_SALES: [
            ExactMatcher(
                [
                    ("net sales",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "NET",
                        "SALES",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.CURRENT_ASSETS: [
            ExactMatcher(
                [
                    ("total curr assets",),
                    ("total current assets",),
                    ("total current assets ",),
                    ("current assets",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "CURRENT",
                        "ASSETS",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.OTHER_ASSETS: [
            ExactMatcher(
                [
                    ("total oth assets",),
                    ("total other assets",),
                    ("total other assets ",),
                    ("other assets",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "OTHER",
                        "ASSETS",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.TOTAL_ASSETS: [
            ExactMatcher(
                [
                    ("total assets",),
                    ("totals assets",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "TOTAL",
                        "ASSETS",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.CURRENT_LIABILITIES: [
            ExactMatcher(
                [
                    ("total current liabilities",),
                    ("tot curr liab",),
                    ("total current liabilities ",),
                    ("current liabilities",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "CURRENT",
                        "LIABILITIES",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.OTHER_LIABILITIES: [
            ExactMatcher(
                [
                    ("total other liabilities",),
                    ("tot oth liab",),
                    ("total other liabilities ",),
                    ("other liabilities",),
                    ("other current & long term liabilities",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "OTHER",
                        "LIABILITIES",
                    ),
                    (
                        "LIABILITY",
                        "ALL LOCATIONS",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.TOTAL_LIABILITIES: [
            ExactMatcher(
                [
                    ("total liabilities",),
                    ("tot liab",),
                    ("total liabilities ",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "TOTAL",
                        "LIABILITIES",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.OPERATING_INCOME: [
            ExactMatcher(
                [
                    ("income from operations",),
                    ("net operating income",),
                    ("operating income",),
                    ("operating income loss",),
                    ("operating loss income",),
                    ("operating income or loss",),
                    ("net operating income loss",),
                    ("operating expenses exclusive of items shown separately below",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "OPERATING",
                        "INCOME",
                    ),
                    ("INCOME", "FROM", "OPERATIONS"),
                    ("OPERATING", "EXPENSES"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.OTHER_INCOME: [
            ExactMatcher(
                [
                    ("total other income exp",),
                    ("total other income",),
                    ("total other income and expense",),
                    ("other income expense net",),
                    ("total other income net",),
                    ("total other income expense",),
                    ("total other income expense net",),
                    ("total other income and expenses",),
                    ("total other income expenses",),
                    ("net other income",),
                    ("other income",),
                    ("other income expenses",),
                    ("other income net",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "OTHER",
                        "INCOME",
                    ),
                ],
                negative=["RENT"],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.NET_INCOME: [
            ExactMatcher(
                [
                    ("total net income",),
                    ("net income exhibit b",),
                    ("net income",),
                    ("net income before taxes",),
                    ("net income loss",),
                    ("net income loss before taxes",),
                    ("net income or loss",),
                    ("consolidated net loss income",),
                    ("total income",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "NET",
                        "INCOME",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.CASHFLOW: [
            ExactMatcher(
                [
                    ("net increase decrease in cash",),
                    ("net increase in cash and cash equivalents",),
                    ("net decrease increase in cash and cash equivalents",),
                    ("net change in cash and cash equivalents",),
                    ("net increase in cash",),
                    ("net increase in cash and equivalents",),
                    ("net increase decrease in cash cash equivalents and restricted cash",),
                    ("net decrease in cash and cash equivalents",),
                    ("change in cash and cash equivalents",),
                    ("increase decrease in cash and cash equivalents",),
                    ("increase in cash",),
                    ("decrease in cash",),
                    ("increase decrease in cash",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("CASHFLOW",),
                    ("INCREASE", "IN", "CASH"),
                    ("DECREASE", "IN", "CASH"),
                    ("Cash Flow", "Deficit", r"\$"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.RETAINED_EARNINGS: [
            ExactMatcher(
                [
                    ("net retained earnings",),
                    ("retained earnings",),
                    ("retained earnings current year",),
                    ("retained earnings current",),
                    ("retained earnings ytd",),
                    ("retained earnings deficit",),
                    ("retained earning deficit",),
                    ("retained earnings deficit beginning of year",),
                    ("retained earnings deficit end of year",),
                    ("retained earnings beginning of year",),
                    ("retained earnings end of year",),
                    ("retained earnings beginning",),
                    ("retained earnings ending",),
                    ("retained earnings equity pic ",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "RETAINED",
                        "EARNINGS",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.EQUITY: [
            ExactMatcher(
                [
                    ("total stockholders equity",),
                    ("total stockholder s equity",),
                    ("total shareholders equity",),
                    ("total shares and equity",),
                    ("total equity",),
                    ("stockholders equity",),
                    ("equity",),
                    ("total net worth",),
                    ("total stockholder s equity deficit",),
                    ("total stockholders equity deficit",),
                    ("total equity deficit",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [("TOTAL", "NET", "WORTH")],
                negative=["SPECIAL", "DATE"],
                characters_before=10,
                characters_between=10,
            ),
            RegexMatcher(
                [("stockholder", "equity")],
            ),
        ],
        FactSubtypeID.GROSS_PROFIT: [
            ExactMatcher(
                [
                    ("gross profit",),
                    ("gross margin",),
                    ("estimated gross profit to date",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "GROSS",
                        "PROFIT",
                    ),
                    (
                        "GROSS",
                        "MARGIN",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.RENT_INCOME: [
            RegexMatcher(
                [
                    ("RENTS",),
                    ("RENTAL",),
                    *regex_factory(
                        [
                            WordSynonyms(["rent", "rental", "rents"]),
                            WordSynonyms(
                                [
                                    *INCOME_KEYWORDS,
                                    "value",
                                    "total",
                                    "\\$",
                                    "avg",
                                    "average",
                                    "annual",
                                    "monthly",
                                    "amount",
                                    "month",
                                ]
                            ),
                        ]
                    ),
                ],
                characters_before=15,
                characters_between=15,
                negative=[r"\$BI/EE/Rents", "BI/EE/Rents", "VALUE/RENTS", "LOSS", "BUSINESS INCOME"],
            ),
            ExactMatcher(
                [("RENT",), ("Monthly", "Rent")],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.OTHER_VALUE_TIV: [RegexMatcher([("Other", r"\$")], characters_before=10, characters_between=10)],
        FactSubtypeID.DISTANCE_TO_COAST: [
            RegexMatcher(
                [
                    (
                        "DISTANCE",
                        "COAST",
                    ),
                    (
                        "DISTANCE",
                        "SALTWATER",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.FLOOD_ZONE: [
            RegexMatcher(
                [
                    (
                        "FLOOD",
                        "ZONE",
                    ),
                    (
                        "FL",
                        "ZONE",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher([("Flood",)], threshold=EXACT_MATCH_RATIO_THRESHOLD),
        ],
        FactSubtypeID.BI_EE: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["BI", "B/I", "business income", "business interruption"]),
                            WordSynonyms([SKIP_WORD, "including", "incl", "w/", "with"]),
                            WordSynonyms(["EE", "extra expense", "extra exp", "Ex Exp", "rental w/extra expense"]),
                            WordSynonyms([SKIP_WORD, "loss of rents", "limit", "value"]),
                        ],
                        permutate=False,
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(BUSINESS_KEYWORDS),
                            WordSynonyms(["income/ee", "income/ext", "income/extra expense", "w/ extra expense"]),
                        ],
                    ),
                    ("BI/EE", "VALUE"),
                    ("BUSINESS", "INCOME", "EXTRA", "EXPENSE"),
                    ("Bus", "Inc", "Extra", "Exp"),
                    ("BI", "w/", "Extra", "Expense"),
                    ("Bus", "Extra", "Expense"),
                ],
                negative=[
                    "EDP",
                    "property",
                    "poprety",
                    "BPP",
                    "update",
                    "year",
                    "Bldg",
                    "Sq",
                    "square",
                    "s/f",
                    "sf",
                    "class",
                    "contract",
                    "Extra Expense Limit",
                    "tool",
                    "tools",
                    "stories",
                ],
                characters_before=20,
                characters_between=10,
            ),
            ExactMatcher(
                [
                    ("BI incl EE Coverage Limit",),
                    ("business income ee including loss of rents a",),
                    ("Business Interruption Inc Extra Expense",),
                    ("Loss of Use",),
                    ("Earnings/Extra Expense - (All Buildings)",),
                    ("Earnings/Extra Expense",),
                    ("income/ee",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.CONTRACTOR_AGREEMENT_DESCRIPTION: [
            RegexMatcher(
                [*regex_factory([Word("Contract"), WordSynonyms(["Maintenance", "Main-tenance", SKIP_WORD])])],
                characters_before=10,
                characters_between=10,
                negative=["used", "SUBCONTRACTOR"],
            ),
        ],
        FactSubtypeID.BUSINESS_INCOME: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(BUSINESS_KEYWORDS),
                            WordSynonyms([*INCOME_KEYWORDS, "interruption"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["BI", "business income"]),
                            WordSynonyms(["limit", "total", "value", "incl"]),
                        ]
                    ),
                    ("ANNUAL", "INCOME"),
                    ("VALUE/RENTS", "INCOME"),
                ],
                characters_before=20,
                characters_between=10,
                negative=["LOSS", "birth", "Year", "Rental income", "date", "extra expense", "extra exp", "ext exp"],
            ),
            RegexMatcher(
                [("BI",), ("BI", "@", "100%"), ("BI", "EE", "Rents"), (r"\$", "BI", "EE")],
                characters_before=10,
                characters_between=2,
                negative=["update", "year", "driver", "rental"],
            ),
            ExactMatcher([("Busin",)], threshold=EXACT_MATCH_RATIO_THRESHOLD),
        ],
        FactSubtypeID.PAYROLL: [RegexMatcher([("PAYROLL",)], characters_before=50, characters_between=50)],
        FactSubtypeID.BPP: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(BUSINESS_KEYWORDS),
                            WordSynonyms(["per", "pers", "personal"]),
                            WordSynonyms(PROPERTY_KEYWORDS),
                        ],
                        permutate=False,
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["per", "pers", "personal"]),
                            WordSynonyms(PROPERTY_KEYWORDS),
                        ],
                        permutate=False,
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(BUSINESS_KEYWORDS),
                            WordSynonyms(["PP"]),
                        ],
                        permutate=False,
                    ),
                    ("BPP",),
                    ("BPP", "including"),
                    ("Business", "Property"),
                    ("Personal", "Property", "Value"),
                    ("Pers", "Prop", "Val"),
                    ("Business", "PP"),
                ],
                characters_before=33,
                characters_between=33,
            )
        ],
        FactSubtypeID.EDP: [
            RegexMatcher(
                [
                    ("EDP",),
                    *regex_factory(
                        [
                            WordSynonyms(["electronic"]),
                            WordSynonyms(["data"]),
                            WordSynonyms(["process", "processing", "endorsement"]),
                        ],
                    ),
                ],
                negative=["software"],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.ED_SOFTWARE_LIMIT: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["EDP"]),
                            WordSynonyms(["software"]),
                        ],
                    ),
                ],
                negative=["bpp"],
            ),
        ],
        FactSubtypeID.BUILDZOOM_SCORE: [
            RegexMatcher([("BUILDZOOM", "SCORE")], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.BUILDING_AGE: [
            RegexMatcher(
                [
                    ("BLD", "AGE"),
                    ("BUILDING", "AGE"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.VEHICLE_INFORMATION_NUMBER: [
            RegexMatcher(
                [("VIN",), ("VIN", "#"), ("VIN", "NUMBER"), ("VEHICLE", "NUMBER"), ("SERIAL",), ("VEHICLE", "ID")],
                negative=["CENTER", "MISSING"],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.VEHICLE_PLATE_STATE: [
            ExactMatcher([("state",), ("state2",)], threshold=EXACT_MATCH_RATIO_THRESHOLD),
            RegexMatcher(
                [
                    ("STATE", "PLATE"),
                    ("LICENSE", "STATE"),
                    (
                        "REGISTERED",
                        "STATE",
                    ),
                    (
                        "REGISTRATION",
                        "STATE",
                    ),
                    ("STATE OF REGISTRATION",),
                    ("PLATE STATE",),
                    ("ST REG",),
                    ("STATE REG",),
                    (
                        "STATE",
                        "REGIST",
                    ),
                    (
                        "LP",
                        "STATE",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_PLATE_NUMBER: [
            RegexMatcher(
                [
                    ("PLATE", "NUMBER"),
                    ("PLATE", "NO"),
                    ("PLATE", "#"),
                    ("LICENSE", "PLATE"),
                    ("PLATE",),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.VEHICLE_VALUE_NEW: [
            RegexMatcher(
                [("NEW", "PRICE"), ("NEW", "VALUE"), ("NEW", "COST"), ("PRICE",), ("COST",)],
                negative=["CENTER"],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.VEHICLE_VALUE_CURRENT: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["Stated"]),
                            WordSynonyms(["Amount"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            Word("Value"),
                            WordSynonyms(["Vehicle", SKIP_WORD]),
                        ]
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.VEHICLE_MODEL_YEAR: [RegexMatcher([("YEAR",)], characters_before=10, characters_between=10)],
        FactSubtypeID.VEHICLE_MODEL: [
            RegexMatcher(
                [
                    ("MODEL",),
                    (
                        "Vehicle",
                        "Model",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.VEHICLE_TYPE: [
            RegexMatcher(
                [
                    *regex_factory(
                        [WordSynonyms(["Vehicle", "veh", "Autos", "Auto", "Body"]), WordSynonyms(["type", SKIP_WORD])],
                    ),
                    *regex_factory(
                        [WordSynonyms(["equipment", "eq"]), WordSynonyms(["type"])],
                    ),
                ],
                characters_before=10,
                characters_between=10,
                negative=[
                    "plate",
                    "color",
                    "make",
                    "value",
                    "note",
                    "description",
                    "body class",
                ],
            ),
            ExactMatcher([("type",)], threshold=EXACT_MATCH_RATIO_THRESHOLD),
        ],
        FactSubtypeID.VEHICLE_MAKE: [RegexMatcher([("MAKE",)], characters_before=10, characters_between=10)],
        FactSubtypeID.VEHICLE_BODY_CLASS: [
            RegexMatcher(
                [
                    ("BODY", "CLASS"),
                    ("ACCOUNTING", "CLASS"),
                    ("Vehicle", "Class"),
                    ("Body", "type", "code"),
                    (
                        "Use",
                        "Class",
                    ),
                    (
                        "size",
                        "Class",
                    ),
                    (
                        "primary",
                        "Class",
                    ),
                    (
                        "desc",
                        "Class",
                    ),
                    ("Classification",),
                    (
                        "Body",
                        "Other",
                    ),
                    ("Body", "sym"),
                    (
                        "body",
                        "style",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher(
                [
                    ("Class",),
                    ("Body Class",),
                    ("Equipment class",),
                    ("body",),
                    ("acc class",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.VEHICLE_DESCRIPTION: [
            ExactMatcher(
                [
                    ("Description",),
                    ("desc",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Vehicle", "desc"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_NOTE: [
            RegexMatcher(
                [
                    ("Note",),
                    ("Description",),
                ],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher(
                [
                    ("Notes",),
                    ("Note",),
                    ("Comment",),
                    ("Comments",),
                    ("Updates",),
                    ("Update",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.ENGINE_MANUFACTURER: [
            RegexMatcher(
                [
                    ("ENGINE", "MFG"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.GVWR: [
            RegexMatcher(
                [
                    ("WEIGHT",),
                    ("GROSS VEHICLE WEIGHT RATING", "GVWR"),
                    ("GROSS VEHICLE WEIGHT",),
                    ("GVWR",),
                    ("GVW",),
                    ("GROSS", "WGHT"),
                    ("G V W",),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.OTHER_TRAILER_INFO: [
            RegexMatcher(
                [
                    ("COMMENTS",),
                    ("Information",),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.FUEL_TYPE_PRIMARY: [
            ExactMatcher(
                [
                    ("Fuel",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.FLEET_OPERATION_STATES: [
            ExactMatcher(
                [
                    ("State/Prov",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "Region",
                        "in",
                        "service",
                    ),
                    ("Information",),
                    (
                        "Licensing",
                        "state",
                    ),
                    ("Garage state",),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.TRAILER_TYPE: [
            ExactMatcher(
                [
                    ("trailer",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "TYPE",
                        "OF",
                        "TRAILER",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.TRAILER_LENGTH: [
            RegexMatcher(
                [
                    (
                        "Length",
                        "Trailer",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_LICENSE_NUMBER: [
            RegexMatcher(
                [
                    ("LIC", "NUMBER"),
                    ("LIC", "#"),
                    ("LIC", "NUM"),
                    ("LICENSE", "NUMBER"),
                    ("LICENSE", "NO"),
                    (
                        "LICENSE",
                        "#",
                    ),
                    ("LICENSE", "DRIVER", "NUMBER"),
                    ("LICENSE", "DRIVER", "NO"),
                    ("LICENSE", "DRIVER", "#"),
                    ("CDL", "NUMBER"),
                    ("D/L", "#"),
                    ("DL", "#"),
                    ("DL", "NUMBER"),
                    ("DriverLicense",),
                    (
                        "DRV",
                        "LIC",
                    ),
                    ("DRIVER", "#"),
                ],
                characters_before=11,
                characters_between=11,
            ),
            RegexMatcher(
                [
                    ("DRIVER", "LICENSE"),
                    ("DRIVER", "LIC"),
                ],
                characters_before=0,
                characters_between=10,
            ),
            ExactMatcher(
                [
                    ("CDL",),
                    ("DL",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.COMMERCIAL_AREA_SIZE: [
            RegexMatcher(
                [("Commercial", "Area", "Size"), ("Retail", "SQ", "FT"), ("Commercial", "SQ"), ("Retail", "SQ")],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.CARRIER_OPERATION: [
            RegexMatcher(
                [
                    ("Carrier",),
                    ("Carrier", "Property"),
                    ("Lead", "Carrier", "Agreed"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_DEPARTMENT: [
            ExactMatcher(
                [
                    ("dept",),
                    ("department",),
                    ("branch",),
                    ("division",),
                    ("div",),
                    ("dept #",),
                    ("DEPT NO",),
                    ("Company Dept",),
                ],
            ),
            RegexMatcher(
                [
                    ("Department", "Code"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.DRIVER_ENTITY: [
            ExactMatcher(
                [
                    ("Entity",),
                    ("Current Entity",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            )
        ],
        FactSubtypeID.DRIVER_LICENSE_CLASS: [
            ExactMatcher(
                [
                    ("class",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("License", "Class"),
                    ("DL", "Class"),
                    ("License", "Type"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.DRIVER_LICENSE_EXPIRATION_DATE: [
            ExactMatcher(
                [
                    ("expiration",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Expiration", "Date"),
                    ("DL", "Exp", "Date"),
                    ("License", "expiration"),
                    ("Exp", "Date"),
                    ("DL", "Expiration"),
                    ("DL", "Ex", "Date"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.DRIVER_LOCATION: [
            ExactMatcher(
                [
                    ("Location",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Employee", "Location"),
                    ("Location", "Address"),
                ],
                characters_before=10,
                characters_between=70,
            ),
        ],
        FactSubtypeID.DRIVER_MARTIAL_STATUS: [
            RegexMatcher(
                [
                    ("MAR", "STAT"),
                    ("Martial", "Status"),
                    ("Marital", "Status"),
                    ("MARITAL", "ST"),
                    ("MARTIAL", "ST"),
                    ("M", "SINGLE"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_ROLE: [
            ExactMatcher(
                [
                    ("title",),
                    ("position",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Driver", "Type"),
                    ("Job", "Title"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.DRIVER_STATUS: [
            ExactMatcher(
                [
                    ("Status",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            )
        ],
        FactSubtypeID.DRIVERS_NOTES: [
            ExactMatcher(
                [
                    ("Notes",),
                    ("Note",),
                    ("COMMENTS",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            )
        ],
        FactSubtypeID.NUMBER_OF_SEATS: [
            RegexMatcher(
                [
                    ("Seating",),
                    ("Seating", "Capacity"),
                    ("Seated", "Capacity"),
                ],
                characters_before=10,
                characters_between=15,
            )
        ],
        FactSubtypeID.HAS_CCTV: [
            ExactMatcher(
                [
                    ("CCTV",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Security", "Camera"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_ELEVATORS: [
            RegexMatcher(
                [
                    ("Elevator", "Has"),
                    ("Elevator", "Present"),
                    ("Elevators", "Y/N"),
                    ("Elevator", "Y/N"),
                ],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher(
                [
                    ("Elevators",),
                    ("Elevator",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.LOSS_PAYEE: [RegexMatcher([("Loss", "Payee")])],
        FactSubtypeID.NUMBER_OF_BATHROOMS: [
            ExactMatcher(
                [
                    ("Bathrooms",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Bath", "Number"),
                    ("Number", "Bathroom"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.NUMBER_OF_BEDROOM: [
            ExactMatcher(
                [
                    ("Bedrooms",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Bedroom", "Number"),
                    ("Number", "Bedroom"),
                    ("Bedroom", "Counts"),
                    ("#", "Bed"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.NUMBER_OF_BASEMENTS: [
            RegexMatcher(
                [
                    ("#", "Basements"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.NUMBER_OF_ELEVATORS: [
            ExactMatcher(
                [
                    ("# of Elevators",),
                    ("Elevators",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [("of", "Elev"), ("#", "Elev"), ("#", "Lift"), ("Number", "of", "Elevators")],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.PREMISES_DATE_ADDED: [
            ExactMatcher(
                [
                    ("In Service Date",),
                    ("Acquisition Date",),
                    ("Delivery Date",),
                    ("Added",),
                    ("Date Added",),
                    ("Date purchased",),
                    ("Year Purchased",),
                    ("Mgmt Start Date",),
                    ("Management Start Date",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Purchase", "Date"),
                    ("Date", "Added"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.PREMISES_DIVISION: [
            ExactMatcher(
                [
                    ("dept",),
                    ("department",),
                    ("branch",),
                    ("division",),
                    ("div",),
                    ("dept #",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Department", "Code"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_PREMISES_FENCE: [
            ExactMatcher(
                [
                    ("Parking Fenced",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher([("Premises", "Fence"), ("Property", "Fence"), ("Perimeter", "Fence"), ("Surround", "Fence")]),
        ],
        FactSubtypeID.PREMISES_NOTES: [
            ExactMatcher(
                [
                    *regex_factory([WordSynonyms(["Notes", "Note", "USI notes", "comments", "notes/comments"])]),
                    ("Building Detail",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["Notes", "Note"]),
                            WordSynonyms(
                                [
                                    "reno",
                                    "renovation",
                                    "remodel",
                                    "other",
                                    "changes",
                                    "additional",
                                    "vacant",
                                    "update",
                                    "info",
                                    "information",
                                ]
                            ),
                        ]
                    )
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.PREMISES_STATUS: [
            ExactMatcher(
                [("Status",), ("Current Status",)],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            )
        ],
        FactSubtypeID.HAS_PULL_CORDS_IN_EACH_UNIT: [
            ExactMatcher(
                [
                    ("Are pull cords in use within each unit",),
                ],
                threshold=99,
            )
        ],
        FactSubtypeID.ROOF_ANCHOR: [
            RegexMatcher(
                [("Roof", "Anchor"), ("Roof", "Deck", "Attachment"), ("Roof", "Fram")],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.VEHICLE_ADDITIONAL_INTEREST: [
            RegexMatcher(
                [
                    ("Vehicle", "Additional", "Interest"),
                ],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher(
                [
                    ("Additional Interest",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.VEHICLE_AMOUNT_OF_INSURANCE: [
            ExactMatcher(
                [
                    ("Amount of Ins",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Amount", "Insurance"),
                    ("A", "M", "T", "Insurance"),
                    ("A", "M", "T", "Insured"),
                    ("A", "M", "T", "Stated"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_VEHICLE_APD_COVERAGE: [
            ExactMatcher(
                [
                    ("APD",),
                    ("APD Physical Damage",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "Incl",
                        "APD",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_VEHICLE_AUTO_LIABILITY_COVERAGE: [
            ExactMatcher(
                [
                    ("AL",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("AL", "Liability"),
                    ("auto", "Liability"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_CLASS_CODE: [
            ExactMatcher(
                [
                    ("Code",),
                    ("Class",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Class", "Code"),
                    ("Class", "Rate"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_COLLISION_COMPREHENSIVE_DEDUCTIBLE_COVERAGE: [
            ExactMatcher(
                [
                    ("Coll Ded",),
                    ("Ded Coll",),
                    ("Collision",),
                    ("Collision Deductible",),
                    ("Collision Ded",),
                    ("Deductible",),
                    ("Coll",),
                    ("Collision Deductibe",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Coll", "ded"),
                ],
                characters_before=10,
                characters_between=5,
                negative=["deductible", "comp", "comprehensive"],
            ),
        ],
        FactSubtypeID.HAS_VEHICLE_COLLISION_COVERAGE: [
            ExactMatcher(
                [
                    ("Collision",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Collision", "coverage"),
                ],
                characters_before=10,
                characters_between=10,
                negative=["deductible", "comp", "comprehensive"],
            ),
        ],
        FactSubtypeID.HAS_VEHICLE_COLLISION_WAIVER_COVERAGE: [
            RegexMatcher(
                [
                    ("Waiver", "Collision"),
                    ("Wiver", "Collision"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.VEHICLE_COLOR: [
            ExactMatcher(
                [
                    ("Color",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [("Vehicle", "color"), ("exterior", "color")],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_VEHICLE_COMPREHENSIVE_COVERAGE: [
            RegexMatcher(
                [
                    ("Comp", "Collision", "Coverage"),
                ],
                characters_before=10,
                characters_between=10,
                negative=[
                    "deductible",
                ],
            )
        ],
        FactSubtypeID.VEHICLE_COMPREHENSIVE_DEDUCTIBLE_COVERAGE: [
            ExactMatcher(
                [
                    ("Comp Ded",),
                    ("comp",),
                    ("Comprehensive Deductible",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "ded",
                        "comp",
                    )
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_DATE_ADDED: [
            ExactMatcher(
                [
                    ("In Service Date",),
                    ("Acquisition Date",),
                    ("Delivery Date",),
                    ("Added",),
                    ("Date Added",),
                    ("Date purchased",),
                    ("Year Purchased",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Purchase", "Date"),
                    ("Date", "Added"),
                    (
                        "Purch",
                        "date",
                    ),
                    (
                        "Start",
                        "date",
                    ),
                    (
                        "Add",
                        "date",
                    ),
                    (
                        "serv",
                        "date",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_DIVISION: [
            ExactMatcher(
                [
                    ("dept",),
                    ("department",),
                    ("branch",),
                    ("division",),
                    ("div",),
                    ("dept #",),
                    ("Dep Pct",),
                ],
                threshold=MODERATE_FUZZY_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Department", "Code"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_ENTITY_OWNER: [
            ExactMatcher(
                [
                    ("Named Insured",),
                    ("Entity",),
                    ("Entity Under Which Titled",),
                    ("Company",),
                    ("Entity Under Which the Vehicle is Titled",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("company", "name"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_INSURANCE_PREMIUM: [
            ExactMatcher(
                [
                    ("Premium",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Motorist", "Prem"),
                    ("Liab", "Prem"),
                    ("Insurance", "Prem"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_LEASED: [
            ExactMatcher(
                [
                    ("Leased",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Leased", "Y", "N"),
                    ("Leased", "No"),
                ],
                characters_before=10,
                characters_between=22,
            ),
        ],
        FactSubtypeID.VEHICLE_LESSOR: [
            ExactMatcher(
                [
                    ("Lessor",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            )
        ],
        FactSubtypeID.HAS_VEHICLE_LIABILITY_COVERAGE: [
            ExactMatcher(
                [("Liab",), ("Liability",)],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Liab", "Count"),
                    ("Liability", "Count"),
                    ("Liability", "Coverage"),
                    ("Liability", "Only"),
                    ("Liab", "Cov"),
                ],
                characters_before=10,
                characters_between=10,
                negative=[
                    "deductible",
                ],
            ),
        ],
        FactSubtypeID.HAS_VEHICLE_MEDICAL_PAYMENTS_COVERAGE: [
            ExactMatcher(
                [
                    ("Medical Payments",),
                    ("Med Pay",),
                    ("MP",),
                    ("Med",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Medical", "Payments", "Coverage"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_OWNED: [
            ExactMatcher(
                [
                    ("Owned",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            )
        ],
        FactSubtypeID.HAS_VEHICLE_PIP_COVERAGE: [
            ExactMatcher(
                [
                    ("PIP",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("PIP", "Coverage"),
                    (
                        "Additional",
                        "PIP",
                    ),
                ],
                characters_before=10,
                characters_between=10,
                negative=["deductible"],
            ),
        ],
        FactSubtypeID.VEHICLE_PIP_DEDUCTIBLE_COVERAGE: [
            RegexMatcher(
                [("PIP", "deductible"), ("Person", "deductible"), ("OTCDeductible",)],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.HAS_VEHICLE_PROPERTY_DAMAGE_COVERAGE: [
            ExactMatcher(
                [
                    ("Physical Damage",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Physical", "Damage", "Coverage"),
                    ("Phys", "Dmg", "Y", "N"),
                    ("Phys", "Dam", "Y", "N"),
                ],
                characters_before=10,
                characters_between=22,
            ),
        ],
        FactSubtypeID.VEHICLE_RADIUS: [
            ExactMatcher(
                [
                    ("Radius",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("operating", "radius"),
                    ("Radius", "Use"),
                    ("Radius", "Driven"),
                    ("Driving", "Radius"),
                    ("Radius", "of", "Operation"),
                    ("Radius", "Op"),
                    ("Radius", "Miles"),
                ],
                characters_before=10,
                characters_between=22,
            ),
        ],
        FactSubtypeID.VEHICLE_REPLACEMENT_COST: [
            RegexMatcher(
                [
                    ("Replacement", "Cost"),
                    ("Replacement", "Value"),
                    ("Purchase", "Cost"),
                    ("Original", "Cost", "New"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.REPLACEMENT_COST: [
            RegexMatcher(
                [
                    ("Replacement", "Cost"),
                    (r"\$", "Change", "Replacement", "Cost"),
                    *regex_factory(
                        [
                            WordSynonyms(["limit", SKIP_WORD]),
                            WordSynonyms(["replacement"]),
                            WordSynonyms(["cost", "value"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["building", "bldg"]),
                            WordSynonyms(["rc", "replacement cost", "replacement value", "replacement"]),
                        ]
                    ),
                ],
                characters_before=10,
                characters_between=10,
                negative=["BPP"],
            )
        ],
        FactSubtypeID.VEHICLE_STATUS: [
            ExactMatcher(
                [
                    ("Status",),
                    ("Vacant",),
                    ("Leased",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Status", "Rehab"),
                    ("Owned", "Leased"),
                    ("Unit", "Status"),
                    ("Vehicle", "Status"),
                    ("Service", "Status"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_VEHICLE_UNDERINSURED_MOTORIST_COVERAGE: [
            ExactMatcher(
                [
                    ("UM",),
                    ("UM/UIM",),
                    ("UIM Seelction",),
                    ("Unins Motorist",),
                    ("UnderIns Motorist",),
                    ("Uninsured Motorist",),
                    ("U/M",),
                    ("UIM",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [("Underinsured", "Motorist"), ("Uninsured", "Motorist"), ("Business", "use")],
                characters_before=22,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLE_USE: [
            RegexMatcher(
                [
                    *regex_factory(
                        [WordSynonyms(["Use", "Usage"]), WordSynonyms(["type", SKIP_WORD, "Vehicle"])],
                    )
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.VEHICLES_NOTES: [
            ExactMatcher(
                [
                    ("Notes",),
                    ("Note",),
                    ("Comment",),
                    ("Comments",),
                    ("Updates",),
                    ("Update",),
                    ("Remarks",),
                    ("ADDITIONAL MODEL/MAKE COMMENTS",),
                ],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            )
        ],
        FactSubtypeID.VEHICLE_SERIES: [
            RegexMatcher(
                [
                    ("SE#",),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.HAS_POOL_LIFEGUARD: [
            ExactMatcher(
                [
                    ("Pool with Lifeguard",),
                    ("Lifeguard",),
                ],
                threshold=VERY_HIGH_FUZZY_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Pool", "Lifeguard"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_POOL_SELF_LOCKING_GATES: [
            ExactMatcher(
                [
                    ("Pool with Self Locking Gates",),
                    ("Self", "Locking", "Gates"),
                    ("Self", "Latching", "Gates"),
                ],
                threshold=VERY_HIGH_FUZZY_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    ("Pool", "Locking", "Gates"),
                    ("Pool", "Gates"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_POOL_FENCE: [
            ExactMatcher(
                [
                    ("Pool Fenced",),
                ],
            ),
            RegexMatcher(
                [
                    ("Pool", "Fence"),
                ],
                characters_before=50,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_POOL_RULES_POSTED: [
            ExactMatcher(
                [
                    ("Pool Rules Posted",),
                ],
            ),
            RegexMatcher(
                [
                    ("Pool", "Rules"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_POOL_DIVING_BOARD: [
            ExactMatcher(
                [
                    ("Diving Board",),
                ],
            ),
            RegexMatcher(
                [
                    ("Pool", "Diving", "Board"),
                    ("Diving", "Platform"),
                    (
                        "Diving",
                        "Board",
                    ),
                ],
                characters_before=33,
                characters_between=33,
            ),
        ],
        FactSubtypeID.HAS_POOL_DEPTH_MARKERS: [
            ExactMatcher(
                [
                    *regex_factory([Word("depth"), WordSynonyms(["markers", "marked", "markings"])]),
                ],
            ),
            RegexMatcher(
                [
                    ("Pool", "Depth", "Markers"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.POOL_FENCE_HEIGHT: [
            ExactMatcher(
                [
                    ("Height of Pool Fence",),
                ],
            ),
            RegexMatcher(
                [
                    ("Pool", "Fence", "Height"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_FORMALIZED_STAFF_TRAINING: [
            RegexMatcher(
                [
                    (
                        "Has",
                        "Formalized",
                        "Staff",
                        "Training",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PT_EMPLOYEES_COUNT: [
            RegexMatcher(
                [
                    *regex_factory([AdjacentWords(["part", "time"]), Word("employee")], permutate=True),
                    *regex_factory([Word("pt"), Word("employee")], permutate=True),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.FT_EMPLOYEES_COUNT: [
            RegexMatcher(
                [
                    *regex_factory([AdjacentWords(["full", "time"]), Word("employee")], permutate=True),
                    *regex_factory([Word("ft"), Word("employee")], permutate=True),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.VOLUNTEERS_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.VOLUNTEERS_EMPLOYEES_COUNT
        ],
        FactSubtypeID.LEASED_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.LEASED_EMPLOYEES_COUNT],
        FactSubtypeID.INTERNS_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.INTERNS_EMPLOYEES_COUNT],
        FactSubtypeID.FOREIGN_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.FOREIGN_EMPLOYEES_COUNT],
        FactSubtypeID.US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.US_EMPLOYEES_COUNT],
        FactSubtypeID.FT_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.FT_US_EMPLOYEES_COUNT],
        FactSubtypeID.PT_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.PT_US_EMPLOYEES_COUNT],
        FactSubtypeID.FT_NON_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.FT_NON_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.PT_NON_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.PT_NON_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.INHOUSE_COUNSEL_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.INHOUSE_COUNSEL_EMPLOYEES_COUNT
        ],
        FactSubtypeID.FT_CA_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.FT_CA_EMPLOYEES_COUNT],
        FactSubtypeID.PT_CA_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.PT_CA_EMPLOYEES_COUNT],
        FactSubtypeID.CA_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.CA_EMPLOYEES_COUNT],
        FactSubtypeID.FT_TX_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.FT_TX_EMPLOYEES_COUNT],
        FactSubtypeID.PT_TX_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.PT_TX_EMPLOYEES_COUNT],
        FactSubtypeID.TX_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.TX_EMPLOYEES_COUNT],
        FactSubtypeID.UNION_FT_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.UNION_FT_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.UNION_PT_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.UNION_PT_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.UNION_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.UNION_US_EMPLOYEES_COUNT],
        FactSubtypeID.UNION_FT_NON_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.UNION_FT_NON_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.UNION_PT_NON_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.UNION_PT_NON_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.UNION_NON_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.UNION_NON_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.UNION_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.UNION_EMPLOYEES_COUNT],
        FactSubtypeID.NON_UNION_FT_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.NON_UNION_FT_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.NON_UNION_PT_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.NON_UNION_PT_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.NON_UNION_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.NON_UNION_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.NON_UNION_FT_NON_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.NON_UNION_FT_NON_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.NON_UNION_PT_NON_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.NON_UNION_PT_NON_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.NON_UNION_NON_US_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.NON_UNION_NON_US_EMPLOYEES_COUNT
        ],
        FactSubtypeID.NON_UNION_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.NON_UNION_EMPLOYEES_COUNT
        ],
        FactSubtypeID.VOL_TERMINATED_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.VOL_TERMINATED_EMPLOYEES_COUNT
        ],
        FactSubtypeID.INVOL_TERMINATED_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[
            FactSubtypeID.INVOL_TERMINATED_EMPLOYEES_COUNT
        ],
        FactSubtypeID.LAYOFFS_EMPLOYEES_COUNT: FSID_TO_EMPLOYEES_COUNT_REGEXES[FactSubtypeID.LAYOFFS_EMPLOYEES_COUNT],
        FactSubtypeID.INDEPENDENT_CONTRACTOR_US_COUNT: [
            RegexMatcher(
                [
                    ("Number", "US", "Contractors"),
                    ("Number", "US", "Independent", "Contractors"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.INDEPENDENT_CONTRACTOR_NON_US_COUNT: [
            RegexMatcher(
                [
                    ("Number", "Foreign", "Contractors"),
                    ("Number", "Foreign", "Independent", "Contractors"),
                ],
                characters_before=10,
                characters_between=13,
            )
        ],
        FactSubtypeID.INDEPENDENT_CONTRACTOR_COUNT: [
            RegexMatcher(
                [
                    (
                        "Number",
                        "of",
                        "Independent",
                        "Contractors",
                    )
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_LAST_NAME: [
            RegexMatcher([("LAST NAME",), ("LAST", "NAME"), ("LAST",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.DRIVER_MIDDLE_NAME: [
            RegexMatcher(
                [
                    ("DRIVER", "MIDDLE"),
                    ("MIDDLE", "NAME"),
                    ("MIDDLE",),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_FIRST_NAME: [
            RegexMatcher([("FIRST", "NAME"), ("FIRST",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.DRIVER_NAME: [
            ExactMatcher(
                [("driver",), ("employee",)], threshold=EXACT_MATCH_RATIO_THRESHOLD, negative=["driver #", "driver#"]
            ),
            ExactMatcher([("name",)]),
            RegexMatcher(
                [
                    ("EMPLOYEE",),
                    ("DRIVER", "NAME"),
                    ("FIRST", "LAST"),
                    ("FIRST", "LAST", "MIDDLE"),
                    (
                        "NAME",
                        "as",
                        "on",
                        "license",
                    ),
                ],
                characters_before=10,
                characters_between=25,
                negative=["driver #"],
            ),
        ],
        FactSubtypeID.DRIVER_LICENSE_STATE: [
            RegexMatcher(
                [
                    ("LIC", "STATE"),
                    ("LICENSE", "STATE"),
                    ("LICENSE", "DRIVER", "STATE"),
                    ("STATE",),
                    ("LICENSE NUMBER STATE",),
                    ("DL ST",),
                    ("DL", "STATE"),
                    ("STATE", "DRIVER", "LICENSE"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_GENDER: [
            RegexMatcher(
                [
                    ("GENDER",),
                    ("SEX",),
                    (
                        "m",
                        "or",
                        "f",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_AGE: [RegexMatcher([("AGE",)], characters_before=10, characters_between=10)],
        FactSubtypeID.DRIVER_YEARS_OF_EXPERIENCE: [
            RegexMatcher(
                [
                    ("EXPERIENCE",),
                    ("YRS", "EXP"),
                    ("YEARS", "LICENSED"),
                    ("YOE",),
                    ("YR", "EXP"),
                    ("YEARS OF EXP",),
                    ("YEARS", "EXP"),
                    ("COMMERCIAL", "EXPERIENCE"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_YEAR_STARTED_DRIVING: [
            RegexMatcher(
                [
                    ("YEAR", "LIC"),
                    ("YEAR", "LICENSE"),
                    ("FIRST", "DRIVING", "YEAR"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_DATE_OF_BIRTH: [
            RegexMatcher(
                [
                    ("D O B",),
                    ("BIRTH",),
                    ("DOB",),
                    ("Date", "Of", "Birth"),
                    ("Date", "Of", "Birth", "Driver"),
                    ("BDATE",),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.DRIVER_DATE_OF_HIRE: [
            RegexMatcher(
                [("D O H",), ("DATE", "HIRE"), ("DOH",), ("EMP", "DATE"), ("Acquisition", "Date")],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PROJECT_USE_OF_EIFS: [
            RegexMatcher([("use of EIFS",), ("EIFS",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_SCAFFOLDING: [
            RegexMatcher([("Scaffolding",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_ROOF_WORK: [RegexMatcher([("Roof work",)], characters_before=10, characters_between=10)],
        FactSubtypeID.PROJECT_BLASTING_WORK: [
            RegexMatcher([("Blasting work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_EXCAVATION_WORK: [
            RegexMatcher([("Excavation work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_CRANE_WORK: [
            RegexMatcher([("Crane work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_DEMOLITION_WORK: [
            RegexMatcher([("Demolition work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_BELOW_GRADE: [
            RegexMatcher([("Below grade",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_WORK_ABOVE_SEVEN_STORIES: [
            RegexMatcher([("Work above 7 stories",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_MOLD_REMOVAL: [
            RegexMatcher([("Mold removal",), ("MOLD WORK",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL: [
            RegexMatcher(
                [("Asbestos or other hazardous material removal",)], characters_before=10, characters_between=10
            )
        ],
        FactSubtypeID.PROJECT_SUBCONTRACTORS_USED: [
            RegexMatcher([("Subcontractors used",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_START_DATE: [
            RegexMatcher(
                [
                    ("START DATE",),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PROJECT_END_DATE: [
            RegexMatcher(
                [
                    ("COMPLETION DATE",),
                    (
                        "Anticipated",
                        "Completion",
                        "Date",
                    ),
                    ("COMPLETE", "DATE"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PROJECT_SAFETY_PROGRAM: [
            RegexMatcher([("Safety Program",)], characters_before=10, characters_between=10),
            ExactMatcher([("Is there a formal safety program in place",)], threshold=80),
        ],
        FactSubtypeID.PROJECT_QUALITY_CONTROL_PROGRAM: [
            RegexMatcher([("Quality Control Program",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_SITE_INSPECTION_PROGRAM: [
            RegexMatcher(
                [("Site Inspection Program",), ("INSPECTION", "PROGRAM")], characters_before=10, characters_between=10
            )
        ],
        FactSubtypeID.PROJECT_DEPTH_OF_WORK: [
            RegexMatcher([("If below grade max depth",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_SUBCONTRACTORS_COST: [
            RegexMatcher(
                [
                    ("Subcontractor cost in",),
                    ("SUB", "CON", "WORK", "COST"),
                    ("Sub", "Cost", "total", "cost"),
                    ("SUB", "CON", "COST", "COST"),
                    ("Subcontractors", "total", "cost"),
                ],
                characters_before=10,
                characters_between=50,
                permutate_phrases=True,
            )
        ],
        FactSubtypeID.PROJECT_ESTIMATED_CONSTRUCTION_COST: [
            ExactMatcher([(r"estimated construction cost in \\$ project",)], threshold=EXACT_MATCH_RATIO_THRESHOLD),
            RegexMatcher(
                [("Estimated Construction Cost",)], negative=["type"], characters_before=10, characters_between=10
            ),
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS: [
            RegexMatcher(
                [
                    ("Work Subcontracted to Others",),
                    (
                        "%",
                        "Work Subcontracted to Others",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_GC: [
            RegexMatcher([("Work as a GC",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR: [
            RegexMatcher(
                [
                    ("Work as a Subcontractor",),
                    (
                        "%",
                        "Work as a Subcontractor",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_RESIDENTIAL: [
            RegexMatcher([("Residential Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_COMMERCIAL: [
            RegexMatcher(
                [
                    ("Commercial Work",),
                    (
                        "%",
                        "OF",
                        "COMMERCIAL WORK",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_INDUSTRIAL: [
            RegexMatcher([("Industrial Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_PUBLIC: [
            RegexMatcher([("Public Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_NEW_CONSTRUCTION: [
            RegexMatcher([("New Construction",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_REMODELING_OR_REPAIR: [
            RegexMatcher([("Remodeling or Repair",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_HEIGHT_IN_FT: [
            RegexMatcher([("Project height in ft",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_HEIGHT_IN_STORIES: [
            RegexMatcher([("Project height in stories",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT: [
            RegexMatcher([("Maximum height of work in ft",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES: [
            RegexMatcher([("Maximum height of work in stories",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.YEARS_IN_BUSINESS: [
            ExactMatcher([("years in business",)], threshold=VERY_HIGH_FUZZY_MATCH_RATIO_THRESHOLD),
            ExactMatcher([("years of operation",)], threshold=EXACT_MATCH_RATIO_THRESHOLD),
            ExactMatcher(
                [("How many years has the Applicant been in business",)],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
            RegexMatcher(
                [
                    (
                        "Years",
                        "business",
                    ),
                    (
                        "Years",
                        "inbusiness",
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.LINE_OF_BUSINESS: [
            ExactMatcher([*regex_factory([WordSynonyms(["line", "type", "nature"]), Word("of"), Word("business")])]),
            RegexMatcher(
                [
                    (
                        "Line",
                        "business",
                    )
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.PROJECT_HVAC_WORK: [RegexMatcher([("HVAC Work",)], characters_before=10, characters_between=10)],
        FactSubtypeID.PROJECT_PLUMBING_WORK: [
            RegexMatcher([("Plumbing Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_BOILER_WORK: [
            RegexMatcher([("Boiler Work",), ("Boilers",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_ELECTRICAL_WORK: [
            RegexMatcher([("Electrical Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_GAS_LINE_WORK: [
            RegexMatcher([("Gas Line Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_SPRINKLER_SYSTEM_WORK: [
            RegexMatcher([("Sprinkler System Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_SEWER_WORK: [
            RegexMatcher([("Sewer Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_SHEET_METAL_WORK: [
            RegexMatcher([("Sheet Metal Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_WELDING_WORK: [
            RegexMatcher([("Welding Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_GRADING_WORK: [
            RegexMatcher([("Grading Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PAVING_WORK: [
            RegexMatcher([("Paving Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_DRILLING_WORK: [
            RegexMatcher([("Drilling Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_SIDING_WORK: [
            RegexMatcher([("Siding Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_INSULATION_WORK: [
            RegexMatcher([("Insulation Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_WATER_PROOFING_WORK: [
            RegexMatcher([("Water Proofing Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_RAIN_GUTTER_WORK: [
            RegexMatcher([("Rain Gutter Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_CARPENTRY_WORK: [
            RegexMatcher([("Carpentry Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_ASPHALT_SHINGLE: [
            RegexMatcher([("Asphalt Shingle",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_SLATE: [RegexMatcher([("Slate",)], characters_before=10, characters_between=10)],
        FactSubtypeID.PROJECT_TILE: [RegexMatcher([("Tile",)], characters_before=10, characters_between=10)],
        FactSubtypeID.PROJECT_METAL: [RegexMatcher([("Metal",)], characters_before=10, characters_between=10)],
        FactSubtypeID.PROJECT_POLYURETHANE_FOAM: [
            RegexMatcher([("Polyurethane Foam",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_HOT_TAR: [RegexMatcher([("Hot Tar",)], characters_before=10, characters_between=10)],
        FactSubtypeID.PROJECT_TORCH_DOWN: [
            RegexMatcher([("Torch Down",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_HOT_AIR_WELDING: [
            RegexMatcher([("Hot Air Welding",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PILE_DRIVING_WORK: [
            RegexMatcher([("Pile Driving",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_WOOD_SHAKE_SHINGLE: [
            RegexMatcher(
                [("Wood/Shake Shingle",), ("Wood Siding",), ("Wood Shake",)],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PROJECT_MODIFIED_BITUMEN_HOT_OR_COLD: [
            RegexMatcher([("Modified Bitumen",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_EPDM_HOT_OR_COLD: [
            RegexMatcher([("EPDM",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_ON_PITCHED_ROOFS: [
            RegexMatcher([("Work on Pitched Roofs",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_ON_FLAT_ROOFS: [
            RegexMatcher([("Work on Flat Roofs",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_ON_OTHER_ROOFS: [
            RegexMatcher([("Work on Other Roofs",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_HAND_DEMOLITION: [
            RegexMatcher([("Hand Demolition",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_PULL_PUSH_DOWN: [
            RegexMatcher([("Pull/Push down",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_MECHANICAL_DEMOLITION: [
            RegexMatcher([("Mechanical Demolition",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_IMPLOSION_EXPLOSIVES: [
            RegexMatcher([("Implosion/Explosives",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_HYDRODEMOLITION: [
            RegexMatcher([("Hydrodemolition",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WRECKING_BALL: [
            RegexMatcher([("Wrecking Ball",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_URBAN: [
            RegexMatcher([("Urban Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBURBAN: [
            RegexMatcher([("Suburban Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_RURAL: [
            RegexMatcher([("Rural Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_EXTERIOR_WORK: [
            RegexMatcher([("Exterior Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_INTERIOR_WORK: [
            RegexMatcher([("Interior Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_PERCENTAGE_OF_OTHER_DEMOLITION_METHODS: [
            RegexMatcher([("Other Demolition Methods",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_TRAFFIC_LIGHTING_SIGNALS_WORK: [
            RegexMatcher([("Traffic Lightning/Signals Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_AIRPORT_WORK: [
            RegexMatcher([("Airport Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_ALARM_SYSTEMS_WORK: [
            RegexMatcher([("Alarm Systems Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_SOLAR_WORK: [
            RegexMatcher([("Solar Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_TOWER_ANTENNAS_WORK: [
            RegexMatcher([("Tower/Antennas Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PROJECT_FIBER_OPTICS_WORK: [
            RegexMatcher([("Fiber Optics Work",)], characters_before=10, characters_between=10)
        ],
        FactSubtypeID.PRODUCT_INTENDED_USE: [RegexMatcher([("Usage",)], characters_before=10, characters_between=10)],
        FactSubtypeID.HOTEL_AMENITIES: [RegexMatcher([("AMENITIES",)], characters_before=10, characters_between=10)],
        FactSubtypeID.HAS_SUBSIDIARIES: [
            RegexMatcher(
                [],
                characters_before=10,
                characters_between=10,
                negative=["%"],
            )
        ],
        FactSubtypeID.HUD_UNITS_COUNT: [
            RegexMatcher(
                [
                    ("HUD UNIT COUNT",),
                    ("HUD UNITS",),
                    ("HUD UNITS", "#"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.LOCATION_TYPE: [
            RegexMatcher(
                [
                    ("LOCATION TYPE",),
                ],
                negative=["Wire", "Wiring"],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.LOCATION_NUMBER: [
            RegexMatcher(
                [
                    ("LOCATION", "NUMBER"),
                    ("LOCATION", "#"),
                    ("LOC", "#"),
                    ("LOC", "NO"),
                    ("LocationNumber",),
                    ("Loc/Bldg",),
                ],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher([("LOC",)]),
        ],
        FactSubtypeID.PROPERTY_TYPE: [
            RegexMatcher(
                [
                    ("PROPERTY", "TYPE"),
                ],
                negative=["Wire", "Wiring", "Alarm", "DETECTORS", "SMOKE"],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.PROJECT_OTHER_ROOFING_METHODS: [
            RegexMatcher(
                [
                    ("USE OTHER ROOFING METHODS", "?"),
                    ("OTHER", "ROOFING", "METHODS"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.OWNER: [
            RegexMatcher(
                [("Owner",), ("Owned", "Managed"), ("Ownership",)], characters_before=40, characters_between=10
            )
        ],
        FactSubtypeID.SUBSIDIZED_PERCENT: [
            RegexMatcher(
                [
                    ("Sub", "Perc"),
                    ("Sub", "%"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.WIRING: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["wiring", "wire"]),
                            WordSynonyms([SKIP_WORD, "aluminum", "electrical", "alum"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["type", SKIP_WORD]),
                            WordSynonyms(["electrical"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(
                                [
                                    "circuit",
                                ]
                            ),
                            WordSynonyms(["breakers"]),
                        ]
                    ),
                ],
                negative=["Update", "Updates", "Year", "YR"],
                characters_before=15,
                characters_between=10,
            )
        ],
        FactSubtypeID.HAS_BASEMENT: [
            RegexMatcher(
                [
                    ("Basement",),
                    (
                        "Base",
                        "ment",
                    ),
                ],
                characters_before=10,
                characters_between=10,
                negative=["footage", "sqft"],
            )
        ],
        FactSubtypeID.HAS_BALCONY: [
            RegexMatcher(
                [
                    ("Balcony",),
                    ("Balconies",),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.HAS_ARMED_SECURITY: [
            RegexMatcher(
                [
                    (
                        "Security",
                        "armed",
                    ),
                    ("armed", "?"),
                    ("armed", "Y", "N"),
                    ("armed", "guards"),
                ],
                characters_before=25,
                characters_between=33,
                negative=["unarmed"],
            )
        ],
        FactSubtypeID.WEBSITE: [
            RegexMatcher(
                [("Website",), ("OnlineURL",)],
            )
        ],
        FactSubtypeID.COST_PER_SQ_FT: [
            RegexPatternMatcher(
                patterns=[
                    r".*?((19|20)\d{2})*\s*($|valuation|cost|value|price)+\s*(\/|\\|per)*\s*(sq\.*|square)*\s*(sf\.*|ft\.*|sqft\.*|foot|footage)+"
                ],
                negative=["ITV", "TIV"],
            ),
            RegexMatcher(
                phrases=[
                    *regex_factory(
                        [
                            WordSynonyms(["cost", r"\$", "dollar"]),
                            WordSynonyms([SKIP_WORD, "per", "/"]),
                            WordSynonyms(SQFT_KEYWORDS + SQUARE_KEYWORDS + FOOT_KEYWORDS),
                        ]
                    ),
                ],
                negative=["ITV", "TIV"],
            ),
            ExactMatcher([("Cost Per Sq Ft",), ("psf",), ("value", "psf")]),
        ],
        FactSubtypeID.BUILDING_IMPROVEMENTS_ROOFING_YEAR: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms(["roof", "roofing", "rooofing"]),
                            WordSynonyms(IMPROVEMENT_KEYWORDS),
                        ]
                    ),
                ],
                characters_before=33,
                characters_between=33,
            ),
        ],
        FactSubtypeID.BUILDING_IMPROVEMENTS_PLUMBING_YEAR: [
            RegexMatcher(
                [
                    *regex_factory([WordSynonyms(["plumb", "plumbing", "pluming", "plunbing", "Plbg"])]),
                    *regex_factory(
                        [
                            WordSynonyms(["plumb", "plumbing", "pluming", "plunbing", "Plbg"]),
                            WordSynonyms(IMPROVEMENT_KEYWORDS),
                        ]
                    ),
                ],
                characters_before=100,
                characters_between=33,
            ),
        ],
        FactSubtypeID.BUILDING_IMPROVEMENTS_HEATING_YEAR: [
            RegexMatcher(
                [
                    *regex_factory([WordSynonyms(["heat", "heating", "HVAC", "Heat/AC"])]),
                    *regex_factory(
                        [
                            WordSynonyms(["heat", "heating", "HVAC", "Heat/AC"]),
                            WordSynonyms(IMPROVEMENT_KEYWORDS),
                        ]
                    ),
                ],
                characters_before=33,
                characters_between=33,
            )
        ],
        FactSubtypeID.BUILDING_IMPROVEMENTS_WIRING_YEAR: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms([r"ELEC\b", r"elect\b", "wire", "wiring", "electrical", "electric"]),
                            WordSynonyms(IMPROVEMENT_KEYWORDS),
                        ]
                    ),
                ],
                characters_before=33,
                characters_between=33,
            ),
        ],
        FactSubtypeID.BUILDING_CONDITION: [
            RegexMatcher(
                [
                    (
                        "Building",
                        "Condition",
                    ),
                ],
                characters_before=10,
            ),
        ],
        FactSubtypeID.PROPERTY_DESCRIPTION: [
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms([*BUILDING_KEYWORDS, "location", "property", "occupancy"]),
                            WordSynonyms(["desc", "description"]),
                        ]
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["desc", "description"]),
                            Word("of"),
                            WordSynonyms(["operations", "location", "premises"]),
                        ]
                    ),
                    ("building", "name"),
                    ("building", "use"),
                ],
                characters_before=10,
            ),
            ExactMatcher(
                [("description",), ("property description",), ("property_description",)],
                threshold=MODERATE_FUZZY_MATCH_RATIO_THRESHOLD,
            ),
            # Below matchers copied from deprecated OCCUPANCY fact
            ExactMatcher([("OCCP",), ("operations",)], threshold=EXACT_MATCH_RATIO_THRESHOLD),
            RegexMatcher(
                [
                    *regex_factory(
                        [
                            WordSynonyms([r"OCC\b", "Occupancy", "Ocupancy"]),
                            WordSynonyms(["Description", "Code", "type", SKIP_WORD]),
                        ]
                    ),
                    ("Building Use Detail",),
                    ("Tenant",),
                    ("Tenants",),
                ],
                negative=["CLIENT", "AREA", "%", "number", "# of", "Improvement", "Improvements"],
                characters_before=25,
                characters_between=15,
            ),
        ],
        FactSubtypeID.OCCUPANCY_CLASS: [
            RegexMatcher(
                [
                    ("OCCUPANCY", "CLASS"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.OCCUPANCY_TYPE: [
            RegexMatcher(
                [
                    ("OCCUPANCY", "TYPE"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.FENCE_BPP_LIMITS: [
            RegexMatcher(
                [
                    ("FENCE", "LIMIT"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.SPECIAL_EQUIPMENT_LIMIT: [
            RegexMatcher(
                [
                    ("SPECIAL", "EQUIPMENT", "LIMIT"),
                ],
            ),
        ],
        FactSubtypeID.TOOL_VALUE: [
            RegexMatcher(
                [
                    ("TOOL", "VALUE"),
                    ("EMPLOYEE", "TOOLS", "LIMIT"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.TECHNICIANS: [
            RegexMatcher(
                [
                    ("TECHS",),
                    ("TECHNICIANS",),
                    ("#", "OF", "TECHS"),
                    ("#", "OF", "TECHNICIANS"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.AR_LIMIT: [
            RegexMatcher(
                [
                    ("AR", "LIMIT"),
                    ("ACCOUNTS", "RECEIVABLE"),
                    ("ACCOUNTS", "RECEIVABLE", "LIMIT"),
                ],
                characters_before=10,
                characters_between=10,
                negative=["ED", "HARDWARE"],
            ),
        ],
        FactSubtypeID.VALUABLES_LIMIT: [
            RegexMatcher(
                [
                    ("VALUABLE", "LIMIT"),
                    ("VALUABLE", "PAPERS", "STORAGE", "LIMIT"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.ED_HARDWARE_LIMIT: [
            RegexMatcher(
                [
                    ("ED", "LIMIT"),
                    ("HARDWARE", "LIMIT"),
                    ("ED", "HARDWARE", "LIMIT"),
                    ("ED", "MEDIA", "LIMIT"),
                ],
                characters_before=10,
                characters_between=10,
                negative=["EXTRA"],
            ),
        ],
        FactSubtypeID.ED_EXTRA_LIMIT: [
            RegexMatcher(
                [
                    ("EXTRA", "LIMIT"),
                    ("ED", "EXTRA", "LIMIT"),
                    ("ED", "EXTRA", "EXPENSE", "LIMIT"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.SPECIAL_LIMIT: [
            RegexMatcher(
                [
                    ("SPECIAL", "LIMIT"),
                    ("SPECIAL", "CLASSES", "LIMIT"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.HAS_CHARGING_STATION_LIMIT: [
            RegexMatcher(
                [
                    ("LIMIT", "CHARGING", "STATIONS"),
                    ("HAS", "CHARGING", "LIMIT"),
                    ("CHARGING", "STATION", "LIMIT"),
                ],
            ),
        ],
        FactSubtypeID.VACANT_LAND_SIZE: [
            RegexMatcher(
                [("VACANT", "ACRES"), ("VACANT", "LAND"), ("VACANT", "SIZE"), ("VACANT", "AREA"), ("VACANT", "SQFT")],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.EXTERIOR_WALL_FEATURES: [
            RegexMatcher(
                [("Exterior Cladding",)],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.ENTITY_TYPE: [
            RegexMatcher(
                [
                    (
                        "Legal",
                        "Entity",
                        "Desc",
                    ),
                    (
                        "Legal",
                        "status",
                        "Corporation",
                    ),
                    ("Corporate/Business Entity",),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.FEIN: [
            RegexMatcher(
                [
                    ("FEIN",),
                    ("FEDERAL", "EMPLOYER", "IDENTIFICATION", "NUMBER"),
                    ("FEDERAL", "ID"),
                    ("EIN",),
                    ("FED", "ID"),
                    ("FED", "ID#"),
                ],
                characters_before=10,
                characters_between=10,
            )
        ],
        # Ally Auto
        FactSubtypeID.FURNISHED_AUTO_NON_EMPLOYEES: [
            RegexMatcher(
                [("are not relatives", "family members such as college", "autos furnished")],
                characters_before=100,
                characters_between=100,
            )
        ],
        FactSubtypeID.FURNISHED_AUTO: [
            ExactMatcher(
                [("Does the dealer use a furnished auto demonstrator agreement",)],
            )
        ],
        FactSubtypeID.HAS_SOLAR: [
            RegexMatcher(
                [
                    ("Do you have solar panels at your dealerships",),
                    ("Solar", "Panels", "?"),
                    ("Solar", "Panels", "Y or N"),
                ],
                characters_before=33,
                characters_between=33,
            )
        ],
        FactSubtypeID.HAS_TOWING_EQUIPMENT: [
            ExactMatcher([("Does applicant have towing auto transport equipment",)], threshold=80)
        ],
        FactSubtypeID.MANAGEMENT_EXPERIENCE: [
            RegexMatcher(
                [("Management", "experience")],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher([("Describe the overall management experience of the key dealership staff",)], threshold=80),
        ],
        FactSubtypeID.DEBT_TO_EQUITY: [
            RegexMatcher(
                [("Debt", "Equity", "Ratio")],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.RETURN_ON_EQUITY: [
            RegexMatcher(
                [("Return", "Equity")],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.CURRENT_RATIO: [
            RegexMatcher(
                [("Current", "Ratio")],
                characters_before=10,
                characters_between=10,
            )
        ],
        FactSubtypeID.STRUCTURES_COUNT: [
            RegexMatcher(
                phrases=[
                    *regex_factory(
                        [
                            WordSynonyms(["#", "number", "num", "no"]),
                            WordSynonyms(["of", SKIP_WORD]),
                            WordSynonyms(["buildings", "bldgs", "bldings", "blds", "bldg", "bldg's"]),
                        ]
                    ),
                    ("structures", "count"),
                ],
                characters_before=10,
                characters_between=10,
                negative=["Loc. #", "value"],
            ),
            ExactMatcher(
                phrases=[("building",), ("buildings",), ("building", "1")],
                negative=["Loc. #", "value"],
            ),
        ],
        FactSubtypeID.HAS_SWIMMING_POOL: [
            RegexMatcher(
                [("Pool", "Swimming"), ("Pool", "(Y / N)"), ("Pool", "[Y/N]")],
                characters_before=10,
                characters_between=10,
                negative=["Fence"],
            ),
            ExactMatcher([("Pool",), ("Pools",)]),
        ],
        FactSubtypeID.TIV_PER_SQ_FT: [
            RegexMatcher(
                [
                    ("TIV", "sq", "ft"),
                    ("Building", "TIV", "Sq", "Ft"),
                ],
                characters_before=10,
                characters_between=10,
                negative=["COST", "ITV"],
            ),
            ExactMatcher(
                [
                    (
                        "Building",
                        "TIV",
                        "Sq Ft",
                    ),
                    (
                        "TIV",
                        "Sq Ft",
                    ),
                ],
                threshold=VERY_HIGH_FUZZY_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.TENANTS_IMPROVEMENTS_AND_BETTERMENTS: [
            RegexMatcher(
                [
                    ("TENANT", "IMPROVEMENT", "BETTERMENTS"),
                    ("TIB", "Value"),
                    ("TIB", "Limit"),
                    ("TIB", "Amount"),
                ],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher(
                [
                    ("Tenants", "Improvements", "Betterments"),
                    ("TIB",),
                    ("TIB's",),
                    ("TIBs",),
                    ("TI&Bs",),
                    ("TI&B",),
                ],
                threshold=VERY_HIGH_FUZZY_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.INVENTORY_VALUE: [
            RegexMatcher(
                [
                    ("Inventory",),
                    ("Inventories",),
                    ("Stock",),
                    ("Stocks",),
                    ("stock", "value"),
                    ("Inventory", "value"),
                    ("stock", "value"),
                ],
                characters_before=10,
                characters_between=10,
            ),
        ],
        FactSubtypeID.EQUIPMENT_LIMIT: [
            RegexMatcher(
                [
                    ("Equipment", "Machinery"),
                    ("Equipment", "Business"),
                    ("Equip", "Machinery"),
                    ("Eqpt", "B M"),
                    ("M E", "Value"),
                    (
                        "Equipment",
                        "Limit",
                    ),
                    *regex_factory(
                        [
                            WordSynonyms(["equip", "equipment", "mechanical", "machinery"]),
                            WordSynonyms(["limit", "breakdown", "break", "brkdwn"]),
                        ],
                    ),
                ],
                characters_before=10,
                characters_between=10,
            ),
            ExactMatcher(
                [("Equipment",), ("ME",), ("Eqpt",), ("Equip",), ("Machinery",)],
                threshold=EXACT_MATCH_RATIO_THRESHOLD,
            ),
        ],
        FactSubtypeID.BUILDING_VALUATION_METHOD: [
            RegexMatcher(
                [*regex_factory([WordSynonyms([SKIP_WORD, "building", "bldg"]), Word("valuation")])],
                characters_before=10,
                characters_between=10,
            )
        ],
    }


def _log_trace_for_missing_context(
    parent_type: Optional[ParentType], parent_types: Optional[Set[ParentType]], field_type: Optional[FieldType]
) -> None:
    trace = traceback.format_stack()
    logger.info(
        "Missing context in SOVFactsIdentifier",
        trace=trace,
        parent_type_is_missing=parent_type is None,
        parent_types_are_missing=parent_types is None,
        field_type_is_missing=field_type is None,
    )


class SOVFactsIdentifier(EnumMatcher):
    def __init__(self, fact_subtypes: Sequence[FactSubtype], match_on_display_names: bool = True):
        unselectable_fact_subtypes: list[FactSubtypeID] = []
        parent_type_restrictions: dict[FactSubtypeID, set[ParentType]] = {}
        regular_expressions = REGULAR_EXPRESSIONS_SOV.copy()
        known_subtypes = set(FactSubtypeID.__members__)

        for fact_subtype in fact_subtypes:
            if fact_subtype.is_deprecated:
                continue
            if fact_subtype.id not in known_subtypes:
                continue
            if not fact_subtype.is_selectable:
                unselectable_fact_subtypes.append(fact_subtype.id)
            if match_on_display_names and fact_subtype.is_selectable:
                fact_subtype_id = FactSubtypeID[fact_subtype.id]
                matchers = regular_expressions.get(fact_subtype_id, [])
                if not any(getattr(m, "_negative_phrases", None) for m in matchers):
                    matchers.append(ExactMatcher([(fact_subtype.display_name,)], EXACT_MATCH_RATIO_THRESHOLD))
                regular_expressions[fact_subtype_id] = matchers
            if fact_subtype.id in CUSTOM_PARENT_RESTRICTIONS:
                parent_type_restrictions[fact_subtype.id] = CUSTOM_PARENT_RESTRICTIONS[fact_subtype.id]
                continue
            if fact_subtype.default_parent_type:
                parent_types = {fact_subtype.default_parent_type}
                if fact_subtype.default_parent_type in NON_FLEET_PARENT_TYPES:
                    parent_types = NON_FLEET_PARENT_TYPES
                parent_type_restrictions[fact_subtype.id] = parent_types
        super().__init__(
            matchers=regular_expressions,
            ignored_enums=unselectable_fact_subtypes,
            parent_type_restriction=parent_type_restrictions,
        )

    def identify(
        self,
        value: str,
        enums: Optional[Sequence[StrEnum]] = None,
        parent_type: Optional[ParentType] = None,
        field_type: Optional[FieldType] = None,
        parent_types: Optional[Set[ParentType]] = None,
    ) -> Optional[StrEnum]:
        missing_parent_type: bool = parent_type is None and parent_types is None
        if missing_parent_type or field_type is None:
            _log_trace_for_missing_context(parent_type, parent_types, field_type)

        result: Optional[StrEnum] = super().identify(value, enums, parent_type, field_type, parent_types)

        log_fact_subtype_mapping_attempt(
            original_column_name=value,
            fact_subtype_id=result,
            source="sov",
            auto_matched=True,
            parent_type=parent_type,
            parent_types=parent_types,
            field_type=field_type,
        )
        return result


class SOVFactsIdentifierFactory:
    """
    Create singleton instance of SOVFactsIdentifier
    """

    sov_facts_identifier: Optional[SOVFactsIdentifier] = None

    @classmethod
    def create(cls, fact_subtypes: Sequence[FactSubtype]) -> SOVFactsIdentifier:
        if cls.sov_facts_identifier is None:
            cls.sov_facts_identifier = SOVFactsIdentifier(fact_subtypes=fact_subtypes)
        return cls.sov_facts_identifier
