from static_common.constants import NON_FLEET_PARENT_TYPES
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.parent import ParentType

CUSTOM_PARENT_RESTRICTIONS = {
    FactSubtypeID.ZIP_CODE: NON_FLEET_PARENT_TYPES | {ParentType.VEHICLE, ParentType.DRIVER},
    FactSubtypeID.OWNER: NON_FLEET_PARENT_TYPES | {ParentType.VEHICLE},
    FactSubtypeID.POLICY_NUMBER: NON_FLEET_PARENT_TYPES | {ParentType.VEHICLE},
    FactSubtypeID.PROPERTY_DESCRIPTION: {ParentType.STRUCTURE, ParentType.PREMISES},
    FactSubtypeID.NUMBER_OF_SWIMMING_POOLS: NON_FLEET_PARENT_TYPES,
    FactSubtypeID.HAS_SWIMMING_POOL: NON_FLEET_PARENT_TYPES,
    FactSubtypeID.EQUIPMENT_LIMIT: {ParentType.STRUCTURE, ParentType.PREMISES, ParentType.EQUIPMENT},
}
