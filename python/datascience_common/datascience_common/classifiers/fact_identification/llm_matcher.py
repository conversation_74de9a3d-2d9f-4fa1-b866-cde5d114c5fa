import re
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any

from facts_client_v2.model.fact_subtype import FactSubtype
from infrastructure_common.logging import get_logger
from llm_common.clients import get_llm_client
from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import (
    ClaudeRequestParams,
    GPTRequestParams,
    LLMRequestParams,
)
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.file_type import FileType
from static_common.models.file_onboarding import (
    FactSubtypeSuggestion,
    ResolvedDataField,
)
from static_common.models.openai import ChatCompletionPrompt
from static_common.schemas.openai import ChatCompletionPromptSchema

from datascience_common.classifiers.fact_identification.supplementals.field_to_subtype_mapping import (
    FIELD_TO_SUBTYPE_MAPPING,
)
from datascience_common.classifiers.fact_identification.supplementals.supp_fields import (
    <PERSON><PERSON><PERSON>ield,
)
from datascience_common.classifiers.fact_identification.text_matching import (
    clean_field_name,
)
from datascience_common.fact_subtypes.field_name_matching.fact_subtype_matcher import (
    MatchingResult,
)

CHUNK_SIZE = 500

FILE_TYPE_INSTRUCTIONS = {
    (
        FileType.ACORD_FORM,
        FileType.SOV,
    ): """
The file is a SOV (Statement of Values) file, so assign values like "B" to building subtypes.
Entries that refer to a specific building or premises should be assigned to a building value subtype.
""".strip(),
}

# Include these subtypes for all prompts of a certain file type
FILE_TYPE_SUBTYPES = {
    (FileType.ACORD_FORM, FileType.SOV): [
        FactSubtypeID.BUILDING_TYPE,
        FactSubtypeID.BUILDING_SIZE,
        FactSubtypeID.BUILDING_VALUE,
    ]
}


class ILLMFactSubtypeMatcher(ABC):
    @abstractmethod
    def best_candidate(
        self,
        field: ResolvedDataField,
        fact_subtype_suggestions: list[FactSubtypeSuggestion],
        fact_subtype_propositions: list[MatchingResult] | None = None,
        file_type: FileType | None = None,
        parent_file_type: FileType | None = None,
    ) -> FactSubtypeSuggestion | None:
        ...


class LLMFactSubtypeMatcher(ILLMFactSubtypeMatcher):
    @classmethod
    def _create_subtype_match_prompt(
        cls,
        fact_subtypes: dict[str, tuple[FactSubtype, list[str]]],
        key_value_pairs: dict[str, str],
        file_type: FileType | None = None,
        parent_file_type: FileType | None = None,
    ) -> ChatCompletionPrompt:
        fsl = []
        idx = 1
        for key, (fs, explanations) in fact_subtypes.items():
            value_type = fs.accepted_value_type or "string"
            if fs.accepted_value_types:
                value_type = ", ".join(fs.accepted_value_types)
            fsl.append(f"{idx}. subtype: {key}")
            fsl.append(f"{idx}. description: {fs.description}")
            fsl.append(f"{idx}. value type: {value_type}")
            fsl.append(f"{idx}. evidence ({len(explanations)}):")
            for ev in explanations:
                fsl.append(f"    - '{ev}'")
            fsl.append("\n")
            idx += 1

        fact_subtype_list = "\n".join(fsl)

        kvp = []
        idx = 1
        for key, value in key_value_pairs.items():
            kvp.append(f"{idx}. key: {key}")
            kvp.append(f"{idx}. value: {value}")
            fsl.append("\n")
            idx += 1

        key_value_pairs_text = "\n".join(kvp).strip()

        file_type_section = FILE_TYPE_INSTRUCTIONS.get((parent_file_type, file_type), "")

        system_prompt = f"""
You will be given a list of key-value pairs and a list of fact subtypes.
Your task is to determine which fact subtype each key-value pair belongs to, if any.

Select the appropriate fact subtype from the list that accurately describes the type of information
contained in each key-value pair if one of the subtypes fits, otherwise assign null.
If you do not have a subtype for a specific key, you can select
a boolean subtype that indicates the presence of the data the key refers to.

Only select subtypes that apply to the entity in the key - do not use subtypes that describe a business
for keys that describe a person or product, for example.
If the key has multiple parts, the first parts might be less important category or type of information,
while the last part might be the entity.

Do not mix physical addresses and email addresses.
Do not assign phrases relating to insurance coverage specifics (limits, coverages, ...) to general facts.
Do not assign phrases relating to projects to general business facts or vice versa.

{file_type_section}

Assume the following:
 - "Risk" refers to the entity being insured.
 - A building story is approximately 10 feet.
 - Years of experience, years in business and similar are all the same fact.
 - Commonly interchangeable terms are the same fact, such as THC, cannabis, marijuana, etc.
 - Unless a different year is supplied in the key, assume {datetime.now().year} as the current year.
 - For financial subtypes (payroll, sales, ...) be strict with categorization and don't assign totals to specific subcategories or dimensions.
 - For employee count subtypes (total, part time, full time, ...) be strict and don't mix subcategories.
 - If the value is a boolean-like value (e.g. "yes", "no", "true", "false"), try to assign a boolean subtype if it fits.

Give your answer in the following JSON format:
{{
[Key 1]: [Selected Fact Subtype],
[Key 2]: [Selected Fact Subtype],
[Key 3]: [Selected Fact Subtype],
[Key 4]: [Selected Fact Subtype],
...
}}

<fact_subtypes>
{fact_subtype_list}
</fact_subtypes>
""".strip()  # noqa

        prompt_sequence = {
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt,
                },
                {
                    "role": "user",
                    "content": key_value_pairs_text,
                },
            ]
        }
        return ChatCompletionPromptSchema().load(prompt_sequence)  # type: ignore

    def __init__(
        self,
        fact_subtypes: list[FactSubtype] | None = None,
        llm_params: list[LLMRequestParams] | None = None,
        logger: Any | None = None,
    ):
        self.fact_subtypes = [
            fs
            for fs in fact_subtypes
            if fs["description"] and 10 < len(fs["description"]) < 500 and not re.search(r"_[0-9]{4}", fs["id"])
        ]

        self._llm_params = llm_params or [
            GPTRequestParams(
                model=LLMModel.OPENAI_GPT_4O,
                max_tokens=4096,
                return_json=True,
                raise_exceptions=True,
            ),
            ClaudeRequestParams(
                model=LLMModel.CLAUDE_3_7_SONNET,
                max_tokens=4096,
                return_json=True,
                raise_exceptions=True,
            ),
        ]
        self._logger = logger or get_logger()
        self._llm_client = get_llm_client()

        self._subtype_to_field_mapping = {v: k for k, v in FIELD_TO_SUBTYPE_MAPPING.items()}
        if None in self._subtype_to_field_mapping:
            del self._subtype_to_field_mapping[None]
        self._subtype_id_mapping = {FactSubtypeID.try_parse_str(fs["id"]): fs for fs in self.fact_subtypes}
        if None in self._subtype_id_mapping:
            del self._subtype_id_mapping[None]

    def _get_subtype(self, fact_subtype_id: FactSubtypeID) -> FactSubtype:
        return self._subtype_id_mapping.get(fact_subtype_id)

    def _get_supp_field(self, fact_subtype_id: FactSubtypeID) -> SuppField:
        return self._subtype_to_field_mapping.get(fact_subtype_id)

    def best_candidate(
        self,
        field: ResolvedDataField,
        fact_subtype_suggestions: list[FactSubtypeSuggestion],
        fact_subtype_propositions: list[MatchingResult] | None = None,
        file_type: FileType | None = None,
        parent_file_type: FileType | None = None,
    ) -> FactSubtypeSuggestion | None:
        if len(fact_subtype_suggestions) == 0:
            return None

        _logger = self._logger.bind(
            field_name=field.name,
            field_values=[v.value for v in field.values],
            fact_subtype_suggestions=fact_subtype_suggestions,
        )

        props: dict[FactSubtypeID, MatchingResult] = {p.fact_subtype_id: p for p in fact_subtype_propositions or []}
        fs_formatted: dict[str, tuple[FactSubtype, list[str]]] = {}
        for suggestion in fact_subtype_suggestions:
            fact_subtype = self._get_subtype(suggestion.fact_subtype_id)
            fsid = suggestion.fact_subtype_id.name
            if fact_subtype and fsid not in fs_formatted:
                evidence = suggestion.explanations or []
                if suggestion.fact_subtype_id in props:
                    evidence.extend(props[suggestion.fact_subtype_id].evidence or [])
                fs_formatted[fsid] = (fact_subtype, evidence)
        for fs in FILE_TYPE_SUBTYPES.get((parent_file_type, file_type), []):
            fact_subtype = self._get_subtype(fs)
            if fact_subtype and fs.name not in fs_formatted:
                fs_formatted[fs.name] = (fact_subtype, ["Fallback subtype for file type."])

        key_value_pairs: dict[str, str] = {}
        clean_name = clean_field_name(field.name).strip(":")
        key_value_pairs[clean_name] = field.values[0].value if field.values else ""

        prompt_check = self._create_subtype_match_prompt(
            fact_subtypes=fs_formatted,
            key_value_pairs=key_value_pairs,
            file_type=file_type,
        )
        response = self._llm_client.get_llm_response(
            self._llm_params,
            prompt_check,
            call_origin="llm_subtype_matcher",
            use_auto_fallback_models=False,
        )

        if response:
            for item, result in response.items():
                if not result:
                    continue

                fact_subtype_id = FactSubtypeID.try_parse_str(result)
                if not fact_subtype_id:
                    _logger.warning("LLM matcher failed to parse fact subtype ID", result=result)
                    continue

                if clean_name != item:
                    _logger.warning("LLM matcher returned unknown field", field=item, clean_name=clean_name)
                    continue

                candidate = next((s for s in fact_subtype_suggestions if s.fact_subtype_id == fact_subtype_id), None)
                if not candidate:
                    if fact_subtype_id in FILE_TYPE_SUBTYPES.get(file_type, []):
                        fact_subtype = self._get_subtype(fact_subtype_id)
                        field_type = FieldType.try_parse_str(fact_subtype.accepted_value_type)
                        if not field_type:
                            top_suggestion = sorted(fact_subtype_suggestions, key=lambda s: s.confidence)[-1]
                            field_type = top_suggestion.field_type
                        if not field_type:
                            field_type = FieldType.TEXT
                        return FactSubtypeSuggestion(
                            fact_subtype_id=fact_subtype_id,
                            field_type=field_type,
                            confidence=0.5,
                            name_score=0.5,
                            values_score=0.5,
                            explanations=["Fallback subtype for file type."],
                        )
                if candidate:
                    return candidate

        _logger.warning(
            "LLM matcher failed to find a candidate",
            response=response,
        )
        return None


class FakeLLMFactSubtypeMatcher(ILLMFactSubtypeMatcher):
    def __init__(self, field_names_to_not_match: list[str] | None = None) -> None:
        self.field_names_to_not_match = field_names_to_not_match or []

    def best_candidate(
        self,
        field: ResolvedDataField,
        fact_subtype_suggestions: list[FactSubtypeSuggestion],
        fact_subtype_propositions: list[MatchingResult] | None = None,
        file_type: FileType | None = None,
        parent_file_type: FileType | None = None,
    ) -> FactSubtypeSuggestion | None:
        if field.name in self.field_names_to_not_match:
            return None
        return fact_subtype_suggestions[0] if fact_subtype_suggestions else None
