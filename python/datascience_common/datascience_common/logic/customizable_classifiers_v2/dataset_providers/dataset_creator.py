from abc import abstractmethod
from typing import Protocol

from static_common.enums.classification import InputProcessingType

from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
)
from datascience_common.models.customizable_classifiers.dataset import (
    CustomizableClassifierDataset,
)
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)


class IDatasetCreator(Protocol):
    @abstractmethod
    def create_dataset(
        self,
        context: CopilotFileProcessingContext,
        input_processing_type: InputProcessingType,
        resources: CustomizableClassifierPipelineResources,
    ) -> CustomizableClassifierDataset | None:
        ...
