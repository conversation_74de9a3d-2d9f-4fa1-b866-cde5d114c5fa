from datascience_common.logic.customizable_classifiers_v2.dataset_providers.kv_dataset_creator import (
    KVDatasetCreator,
)
from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
)
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)


class WorkCompExperienceDatasetCreator(KVDatasetCreator):
    def _get_dataset_body(
        self, context: CopilotFileProcessingContext, resources: CustomizableClassifierPipelineResources
    ) -> str | None:
        response = resources.copilot_client.copilot_api.get_workers_comp_state_rating_info_for_submission(
            str(context.submission_id), file_id=str(context.file_id)
        )

        if not any(r.category for r in response):
            return None

        categories = []
        for r in response:
            if r.category is not None:
                categories.append(r.category)

        dataset_body_lines = [f"File ID: {context.file_id!s}\n"]
        for category in categories:
            dataset_body_lines.append(f"category: {category}\n")
        return "".join(dataset_body_lines)
