from infrastructure_common.logging import get_logger
from static_common.enums.classification import InputProcessingType
from static_common.enums.file_type import FileType
from structlog.stdlib import Bound<PERSON>ogger

from datascience_common.logic.customizable_classifiers_v2.dataset_providers.dataset_creator import (
    IDatasetCreator,
)
from datascience_common.logic.customizable_classifiers_v2.dataset_providers.email_description_dataset_creator import (
    EmailDescriptionDatasetCreator,
)
from datascience_common.logic.customizable_classifiers_v2.dataset_providers.loss_description_dataset_creator import (
    LossDescriptionDatasetCreator,
)
from datascience_common.logic.customizable_classifiers_v2.dataset_providers.ocr_dataset_creator import (
    OCRDatasetCreator,
)
from datascience_common.logic.customizable_classifiers_v2.dataset_providers.process_data_fields_dataset_creator import (
    ProcessedDataDatasetCreator,
)
from datascience_common.logic.customizable_classifiers_v2.dataset_providers.work_comp_experience_dataset_creator import (  # noqa: E501
    WorkCompExperienceDatasetCreator,
)
from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
)


def get_dataset_creator(
    input_processing_type: InputProcessingType,
    context: CopilotFileProcessingContext | None = None,
    log: BoundLogger = get_logger(),
) -> IDatasetCreator | None:
    if input_processing_type == InputProcessingType.OCR_TEXT:
        return OCRDatasetCreator()
    if input_processing_type == InputProcessingType.KV_PAIRS and context:
        if context.file.file_type == FileType.SUPPLEMENTAL_FORM and context.file.classification.endswith("_PDF"):
            return ProcessedDataDatasetCreator()
        if context.file.file_type == FileType.LOSS_RUN:
            return LossDescriptionDatasetCreator()
        if context.file.file_type == FileType.EMAIL:
            return EmailDescriptionDatasetCreator()
        if context.file.file_type == FileType.WORK_COMP_EXPERIENCE:
            return WorkCompExperienceDatasetCreator()
    log.warning(
        "No dataset creator found for input processing type",
        input_processing_type=input_processing_type,
        context=context,
    )
    return None
