from datascience_common.logic.customizable_classifiers_v2.dataset_providers.kv_dataset_creator import (
    KVDatasetCreator,
)
from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
)
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)


class ProcessedDataDatasetCreator(KVDatasetCreator):
    def _get_dataset_body(
        self, context: CopilotFileProcessingContext, resources: CustomizableClassifierPipelineResources
    ) -> str | None:
        processed_data = context.processed_data
        if not processed_data.fields:
            return None

        dataset_body = "Fields of the supplemental form:\n"
        for field in processed_data.fields:
            for value in field.values:
                dataset_body += f"Supplemental field named '{field.name}' with value {value.value}\n"

        return dataset_body
