from datascience_common.logic.customizable_classifiers_v2.dataset_providers.kv_dataset_creator import (
    KVDatasetCreator,
)
from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
)
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)


class LossDescriptionDatasetCreator(KVDatasetCreator):
    def _get_dataset_body(
        self, context: CopilotFileProcessingContext, resources: CustomizableClassifierPipelineResources
    ) -> str | None:
        response = resources.copilot_client.copilot_api.get_losses(
            str(context.submission_id), file_id=str(context.file_id)
        )
        losses = response.losses

        if not any(loss.claim_description for loss in losses):
            return None

        dataset_body = f"Claim descriptions for the file with ID {context.file_id}:\n"
        dataset_descriptions = []

        for loss in losses:
            if loss.claim_description:
                dataset_descriptions.append(f"Loss ID: {loss.id} Claim description : {loss.claim_description}\n")

        dataset_body += "".join(dataset_descriptions)
        return dataset_body
