from datascience_common.logic.customizable_classifiers_v2.dataset_providers.kv_dataset_creator import (
    KVDatasetCreator,
)
from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
)
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)
from datascience_common.utils.dataset import (
    DESCRIPTION_OF_OPERATIONS_PREFIX,
    EMAIL_DESCRIPTION_PREFIX,
    PROJECT_DESCRIPTION_PREFIX,
)


class EmailDescriptionDatasetCreator(KVDatasetCreator):
    def _get_dataset_body(
        self, context: CopilotFileProcessingContext, resources: CustomizableClassifierPipelineResources
    ) -> str | None:
        submission = resources.copilot_client.copilot_api.get_submission_by_id(str(context.submission_id))

        dataset_body = ""
        if submission.email_description:
            dataset_body += f"{EMAIL_DESCRIPTION_PREFIX} {submission.email_description} "

        if submission.email_project_description:
            dataset_body += f"{PROJECT_DESCRIPTION_PREFIX} {submission.email_project_description} "

        if submission.generated_description_of_operations:
            dataset_body += f"{DESCRIPTION_OF_OPERATIONS_PREFIX} {submission.generated_description_of_operations}"
        elif submission.description_of_operations:
            dataset_body += f"{DESCRIPTION_OF_OPERATIONS_PREFIX} {submission.description_of_operations}"

        if dataset_body:
            return dataset_body
        return None
