import os
from typing import Any

from static_common.enums.classification import InputProcessingType

from datascience_common.logic.customizable_classifiers_v2.dataset_providers.dataset_creator import (
    IDatasetCreator,
)
from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
)
from datascience_common.models.customizable_classifiers.dataset import (
    CustomizableClassifierDataset,
    OCRCustomizableClassifierDataset,
)
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)
from datascience_common.models.dataset import FileDataset
from datascience_common.utils.dataset import _create_document_from_file_extraction
from datascience_common.utils.text_extraction.extract import (
    extract_text_from_copilot_file,
)
from datascience_common.utils.text_extraction.models import TextExtractionResult


class OCRDatasetCreator(IDatasetCreator):
    def create_dataset(
        self,
        context: CopilotFileProcessingContext,
        input_processing_type: InputProcessingType,
        resources: CustomizableClassifierPipelineResources,
    ) -> CustomizableClassifierDataset | None:
        if not (ocr_results := self._get_ocr_results(context, resources)):
            resources.log.warning("Failed to get OCR results", file_id=context.input_id)
            return None

        extraction_result, raw_result = ocr_results

        document = _create_document_from_file_extraction(
            context.file,
            extraction_result,
            business_id=str(context.submission_id),  # Currently, dummy value as it's not used in the business context
            submission_business_id=context.file.submission_business_id,
            organization_id=context.organization_id,
            raw_result=raw_result,
        )

        dataset = FileDataset(
            file_id=context.input_id,
            documents=[document],
            file_classification=context.file_classification,
            raw_ocr_result=raw_result,
        )

        return OCRCustomizableClassifierDataset(input_processing_type=InputProcessingType.OCR_TEXT, dataset=dataset)

    def _get_ocr_results(
        self,
        context: CopilotFileProcessingContext,
        resources: CustomizableClassifierPipelineResources,
    ) -> tuple[list[TextExtractionResult], Any] | None:
        try:
            return extract_text_from_copilot_file(
                file=context.file,
                copilot_data_bucket=os.environ["COPILOT_DATA_BUCKET"],
                copilot_client=resources.copilot_client,
                s3_client=resources.s3_client,
                force_ocr=True,
            )
        except Exception:
            resources.log.exception(
                "Failed to extract text from file",
                file_id=context.input_id,
            )
            return None
