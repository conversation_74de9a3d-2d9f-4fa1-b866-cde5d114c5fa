from abc import ABC, abstractmethod
from uuid import UUID

from facts_client.model.first_party_source import FirstPartySource
from static_common.enums.classification import InputProcessingType
from static_common.enums.source_types import SourceTypeID

from datascience_common.factories import DocumentFactory
from datascience_common.logic.customizable_classifiers_v2.dataset_providers.dataset_creator import (
    IDatasetCreator,
)
from datascience_common.models.customizable_classifiers.context import (
    CopilotFileProcessingContext,
)
from datascience_common.models.customizable_classifiers.dataset import (
    CustomizableClassifierDataset,
    KVPairCustomizableClassifierDataset,
)
from datascience_common.models.customizable_classifiers.pipeline import (
    CustomizableClassifierPipelineResources,
)
from datascience_common.models.dataset import FileDataset


class KVDatasetCreator(ABC, IDatasetCreator):
    def create_dataset(
        self,
        context: CopilotFileProcessingContext,
        input_processing_type: InputProcessingType,
        resources: CustomizableClassifierPipelineResources,
    ) -> CustomizableClassifierDataset | None:
        if not (dataset_body := self._get_dataset_body(context, resources)):
            resources.log.warning("No dataset body found")
            return None

        body_document = DocumentFactory.create_free_text(
            body=dataset_body,
            business_id=UUID(int=0),
            source=FirstPartySource(
                submission_id=str(context.submission_id),
                original_name="Resolved field name",
                source_type_id=SourceTypeID.FIRST_PARTY,
                organization_id=context.organization_id,
                submission_business_id=None,
                file_id=str(context.file_id),
            ),
        )

        dataset = FileDataset(
            file_id=context.file_id,
            file_classification=context.file_classification,
            documents=[body_document],
        )

        return KVPairCustomizableClassifierDataset(
            input_processing_type=input_processing_type,
            dataset=dataset,
            processed_data=context.processed_data,
        )

    @abstractmethod
    def _get_dataset_body(
        self, context: CopilotFileProcessingContext, resources: CustomizableClassifierPipelineResources
    ) -> str | None:
        """
        Abstract method to be implemented by subclasses to extract the dataset body from the context.
        :param context: The processing context containing relevant information.
        :param resources: The resources available for processing.
        :return: The dataset body as a string or None if not applicable.
        """
        pass
