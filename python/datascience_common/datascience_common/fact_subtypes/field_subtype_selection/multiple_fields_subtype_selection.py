from __future__ import annotations

from facts_client.model.fact_subtype import FactSubtype
from infrastructure_common.logging import get_logger
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.file_type import FileType
from static_common.enums.parent import ParentType
from static_common.models.file_onboarding import (
    FactSubtypeSuggestion,
    OnboardedFile,
    ResolvedDataField,
)
from structlog.stdlib import Bo<PERSON><PERSON>ogger

from datascience_common.classifiers.fact_identification.enum_matching import (
    IEnumMatcher,
)
from datascience_common.classifiers.fact_identification.fact_subtype_id_expansion import (
    FACT_SUBTYPE_ID_EXPANSION,
)
from datascience_common.classifiers.fact_identification.sov import (
    SOVFactsIdentifierFactory,
)
from datascience_common.fact_subtypes.conflict_resolution.resolve_conflicts import (
    resolve_conflicts,
)
from datascience_common.fact_subtypes.field_name_matching.fact_subtype_matcher import (
    FactSubtypeMatcher,
    IFactSubtypeMatcher,
    MatchingResult,
)
from datascience_common.fact_subtypes.field_name_matching.field_name_match_scorer import (
    FieldNameMatchScorer,
)
from datascience_common.fact_subtypes.field_subtype_selection.field_subtype_selection import (
    FieldSubtypeSelection,
    IFieldSubtypeSelection,
)
from datascience_common.fact_subtypes.field_subtype_selection.utils import (
    get_field_name_to_parent_type,
    log_duplicated_field_names,
)
from datascience_common.fact_subtypes.field_type_inference.field_inference import (
    FieldInference,
)
from datascience_common.fact_subtypes.field_type_inference.field_type_inference import (
    FieldTypeInference,
    IFieldTypeInference,
)


class MultipleFieldsSubtypeSelection:
    """
    Given a list of fields, this class assigns fact subtypes to them.

    Matching fields to the fact subtypes is a problem of a bipartite graph matching.
    This is a many-to-many relation where each column name could be matched to multiple fact subtypes.
    """

    def __init__(
        self,
        field_subtype_selection: IFieldSubtypeSelection,
        fact_subtype_matcher: IFactSubtypeMatcher,
        field_type_inference: IFieldTypeInference,
        fact_subtypes: list[FactSubtype],
        logger: BoundLogger | None = None,
        log_limit: int = 10_000,
        verbose_logs: bool = True,
    ):
        self._field_subtype_selection = field_subtype_selection
        self._fact_subtype_matcher = fact_subtype_matcher
        self._field_type_inference = field_type_inference
        self._logger = logger or get_logger()
        self._id_to_fact_subtype: dict[FactSubtypeID, FactSubtype] = {
            fact_subtype.id: fact_subtype for fact_subtype in fact_subtypes
        }
        self._log_limit = log_limit
        self._verbose_logs = verbose_logs
        self._current_log_count = 0

    @staticmethod
    def create(
        fact_subtypes: list[FactSubtype],
        logger: BoundLogger | None = None,
        enum_matcher: IEnumMatcher | None = None,
        field_subtype_selection: IFieldSubtypeSelection | None = None,
        verbose_logs: bool = True,
    ) -> MultipleFieldsSubtypeSelection:
        if enum_matcher is None:
            fact_subtype_matcher = FactSubtypeMatcher(
                enum_matcher=SOVFactsIdentifierFactory.create(fact_subtypes),
                fact_subtype_expansion_map=FACT_SUBTYPE_ID_EXPANSION,
            )
        else:
            fact_subtype_matcher = FactSubtypeMatcher(
                enum_matcher=enum_matcher,
                fact_subtype_expansion_map=FACT_SUBTYPE_ID_EXPANSION,
            )

        return MultipleFieldsSubtypeSelection(
            field_subtype_selection=field_subtype_selection
            or FieldSubtypeSelection(
                fact_subtypes=fact_subtypes,
                verbose_logs=verbose_logs,
                field_name_match_scorer=FieldNameMatchScorer.create(fact_subtypes=fact_subtypes),
            ),
            fact_subtype_matcher=fact_subtype_matcher,
            field_type_inference=FieldTypeInference(),
            fact_subtypes=fact_subtypes,
            logger=logger,
            verbose_logs=verbose_logs,
        )

    def _filter_fields_with_no_propositions(
        self,
        fact_subtype_propositions: dict[str, list[MatchingResult]],
        field_name_to_mutable_field: dict[str, ResolvedDataField],
        field_name_to_parent_type: dict[str, ParentType | None],
        file_type: FileType | None = None,
        s3_key: str | None = None,
    ) -> None:
        to_delete = []
        for field_name, propositions in fact_subtype_propositions.items():
            if len(propositions) == 0:
                if self._verbose_logs and self._current_log_count < self._log_limit:
                    self._logger.info(
                        "No propositions for field",
                        field_name=field_name,
                        example_values=[str(x.value) for x in field_name_to_mutable_field[field_name].values[:5]],
                        file_type=file_type,
                        s3_key=s3_key,
                        parent_type=str(field_name_to_parent_type.get(field_name)),
                    )
                    self._current_log_count += 1
                to_delete.append(field_name)

        for field_name in to_delete:
            del fact_subtype_propositions[field_name]

    def _delete_after_fact_is_chosen(
        self,
        chosen_field: str,
        fact_subtype_id: FactSubtypeID,
        field_name_to_matching_subtypes: dict[str, list[FactSubtypeSuggestion]],
        field_name_to_mutable_field: dict[str, ResolvedDataField],
        file_type: FileType | None = None,
        s3_key: str | None = None,
    ) -> None:
        fields_to_delete: list[str] = []
        for field_name, matching_subtypes in field_name_to_matching_subtypes.items():
            new_matching_subtypes: list[FactSubtypeSuggestion] = []
            for match in matching_subtypes:
                if match.fact_subtype_id == fact_subtype_id:
                    match.explanations = [
                        f"This fact could be considered, but fact ({fact_subtype_id!s}) "
                        f"was chosen for another field ({chosen_field})"
                    ]
                    field_name_to_mutable_field[field_name].fact_subtype_suggestions.append(match)
                else:
                    new_matching_subtypes.append(match)

            if not new_matching_subtypes:
                fields_to_delete.append(field_name)
            else:
                field_name_to_matching_subtypes[field_name] = new_matching_subtypes

        for field_name in fields_to_delete:
            if self._current_log_count < self._log_limit:
                self._logger.info(
                    "Removing field name from propositions as fact subtype was chosen for other field.",
                    deleted_field_name=field_name,
                    deleted_field_values=[str(x.value) for x in field_name_to_mutable_field[field_name].values][:5],
                    chosen_field=chosen_field,
                    chosen_field_values=[str(x.value) for x in field_name_to_mutable_field[chosen_field].values][:5],
                    fact_subtype_id=fact_subtype_id,
                    file_type=file_type,
                    s3_key=s3_key,
                )
                self._current_log_count += 1
            del field_name_to_matching_subtypes[field_name]

    def _should_ignore_field_name(self, field_name: str) -> bool:
        is_empty = not field_name
        is_unnamed = field_name.startswith("Unnamed:")
        return is_empty or is_unnamed

    def _assign_facts_to_fields(
        self,
        field_name_to_matching_subtypes: dict[str, list[FactSubtypeSuggestion]],
        field_name_to_mutable_field: dict[str, ResolvedDataField],
        fact_subtype_propositions: dict[str, list[MatchingResult]],
        file_type: FileType | None = None,
        s3_key: str | None = None,
        reassign_name: bool = False,
        parent_file_type: FileType | None = None,
    ) -> None:
        field_names_without_matching_subtypes = [
            field_name
            for field_name, matching_subtypes in field_name_to_matching_subtypes.items()
            if not matching_subtypes
        ]
        for field_name in field_names_without_matching_subtypes:
            del field_name_to_matching_subtypes[field_name]

        while len(field_name_to_matching_subtypes) > 0:
            field_name_with_highest_score: str = max(
                field_name_to_matching_subtypes,
                key=lambda x: field_name_to_matching_subtypes[x][0].confidence,
            )
            mutable_field = field_name_to_mutable_field[field_name_with_highest_score]
            self._field_subtype_selection.assign_to_one_field(
                mutable_field=mutable_field,
                fact_subtype_suggestions=field_name_to_matching_subtypes[field_name_with_highest_score],
                reassign_name=reassign_name,
                fact_subtype_propositions=fact_subtype_propositions[field_name_with_highest_score],
                file_type=file_type,
                parent_file_type=parent_file_type,
            )
            del field_name_to_matching_subtypes[field_name_with_highest_score]
            if mutable_field.fact_subtype_id is not None:
                self._delete_after_fact_is_chosen(
                    chosen_field=field_name_with_highest_score,
                    fact_subtype_id=mutable_field.fact_subtype_id,
                    field_name_to_matching_subtypes=field_name_to_matching_subtypes,
                    field_name_to_mutable_field=field_name_to_mutable_field,
                    s3_key=s3_key,
                    file_type=file_type,
                )

    def assign_fact_subtypes(
        self,
        mutable_fields: list[ResolvedDataField],
        field_name_to_parent_type: dict[str, ParentType | None],
        file_type: FileType | None = None,
        s3_key: str | None = None,
        reassign_name: bool = False,
        duplicate_field_per_proposition: bool = False,
        parent_file_type: FileType | None = None,
    ) -> None:
        field_name_to_mutable_field: dict[str, ResolvedDataField] = {field.name: field for field in mutable_fields}
        fact_subtype_propositions: dict[str, list[MatchingResult]] = {}
        for field in mutable_fields:
            if self._should_ignore_field_name(field_name=field.name):
                continue
            fact_subtype_propositions[field.name] = self._fact_subtype_matcher.match(
                value=field.name, parent_type=field_name_to_parent_type.get(field.name), field_type=None
            )

        self._filter_fields_with_no_propositions(
            fact_subtype_propositions=fact_subtype_propositions,
            field_name_to_mutable_field=field_name_to_mutable_field,
            file_type=file_type,
            s3_key=s3_key,
            field_name_to_parent_type=field_name_to_parent_type,
        )
        field_type_propositions: dict[str, list[dict[FieldType, FieldInference]]] = {
            field.name: self._field_type_inference.infer_field_types(values=[x.value for x in field.values])
            for field in mutable_fields
            if field.name in fact_subtype_propositions
        }

        field_name_to_matching_subtypes: dict[str, list[FactSubtypeSuggestion]] = {
            field.name: self._field_subtype_selection.select_best_subtypes(
                field=field,
                matching_results=fact_subtype_propositions[field.name],
                field_type_propositions=field_type_propositions[field.name],
                file_type=file_type,
                s3_key=s3_key,
            )
            for field in mutable_fields
            if field.name in fact_subtype_propositions
        }
        field_name_to_matching_subtypes = {k: v for k, v in field_name_to_matching_subtypes.items() if v}

        self._assign_facts_to_fields(
            field_name_to_matching_subtypes=field_name_to_matching_subtypes,
            field_name_to_mutable_field=field_name_to_mutable_field,
            file_type=file_type,
            s3_key=s3_key,
            reassign_name=reassign_name,
            fact_subtype_propositions=fact_subtype_propositions,
            parent_file_type=parent_file_type,
        )


def assign_fact_subtypes_to_fields(
    onboarded_file: OnboardedFile,
    fact_subtypes: list[FactSubtype],
    logger: BoundLogger,
    file_type: FileType | None = None,
    field_subtype_selection: IFieldSubtypeSelection | None = None,
    enum_matcher: IEnumMatcher | None = None,
    should_drop_unmatched_fields: bool = False,
    reassign_name: bool = False,
    overwrite_existing_subtypes: bool = False,
    duplicate_field_per_proposition: bool = False,
    verbose_logs: bool = False,
    should_resolve_conflicts: bool = True,
    parent_file_type: FileType | None = None,
) -> None:
    """
    :param should_drop_unmatched_fields: If True, fields that do not match any fact subtype will be dropped
    :param reassign_name: If True field name will be reassigned to display_name from FactSubtype
    """
    log_duplicated_field_names(onboarded_file=onboarded_file, logger=logger)
    multiple_fields_subtype_selection = MultipleFieldsSubtypeSelection.create(
        fact_subtypes=fact_subtypes,
        enum_matcher=enum_matcher,
        field_subtype_selection=field_subtype_selection,
        logger=logger,
        verbose_logs=verbose_logs,
    )
    field_name_to_parent_type = get_field_name_to_parent_type(
        onboarded_file=onboarded_file,
        logger=logger,
    )
    matched_field_ids: set[int] = {id(field) for field in onboarded_file.fields if field.fact_subtype_id is not None}
    mutable_fields = onboarded_file.fields
    if not overwrite_existing_subtypes:
        # In-place filtering, we need cannot create a new list
        matched_fields: list[ResolvedDataField] = [
            field for field in mutable_fields if field.fact_subtype_id is not None
        ]
        mutable_fields[:] = [field for field in mutable_fields if field.fact_subtype_id is None]
    else:
        matched_fields = []

    multiple_fields_subtype_selection.assign_fact_subtypes(
        mutable_fields=mutable_fields,
        field_name_to_parent_type=field_name_to_parent_type,
        file_type=file_type,
        reassign_name=reassign_name,
        duplicate_field_per_proposition=duplicate_field_per_proposition,
        parent_file_type=parent_file_type,
    )
    n_matched_fields = sum(1 for field in mutable_fields if field.fact_subtype_id is not None)
    logger.info("Assigned fact subtypes to fields", n_matched_fields=n_matched_fields, n_fields=len(mutable_fields))

    onboarded_file.fields.extend(matched_fields)
    if should_drop_unmatched_fields:
        onboarded_file.fields = [field for field in onboarded_file.fields if field.fact_subtype_id is not None]

    if should_resolve_conflicts:
        resolve_conflicts(
            mutable_onboarded_file=onboarded_file,
            matched_field_ids=matched_field_ids,
            logger=logger,
            file_type=file_type,
        )
