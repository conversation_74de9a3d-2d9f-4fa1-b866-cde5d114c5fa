from facts_client_v2.model.fact_subtype import FactSubtype
from infrastructure_common.logging import get_logger
from static_common.enums.fields import FieldType
from static_common.models.file_onboarding import ResolvedD<PERSON><PERSON>ield

from datascience_common.fact_subtypes.suggestion.bool_normalizers.bool_llm_normalizer import (
    BoolLLMNormalizer,
    BulkBoolSelectionResponse,
)
from datascience_common.fact_subtypes.suggestion.enum_normalizers.enum_llm_normalizer import (
    BulkEnumSelectionResponse,
    EnumLLMNormalizer,
)
from datascience_common.fact_subtypes.suggestion.enum_normalizers.fact_to_enum import (
    get_enum,
)
from datascience_common.models import StrEnum

logger = get_logger()


def perform_llm_suggestions(
    mutable_field: ResolvedDataField,
    fact_subtype: FactSubtype,
    bool_llm_normalizer: BoolLLMNormalizer,
    enum_llm_normalizer: EnumLLMNormalizer,
) -> None:
    try:
        if enum_type := get_enum(
            fact_subtype_id=mutable_field.fact_subtype_id, organization=None, raise_exception=False
        ):
            _perform_enum_llm_suggestions(mutable_field, enum_type, enum_llm_normalizer)
        elif mutable_field.value_type == FieldType.BOOLEAN:
            _perform_bool_llm_suggestions(
                mutable_field,
                fact_subtype=fact_subtype,
                bool_llm_normalizer=bool_llm_normalizer,
            )
    except Exception as e:
        logger.exception(
            "LLM normalization failed",
            fact_subtype_id=mutable_field.fact_subtype_id,
            error=str(e),
        )


def _perform_bool_llm_suggestions(
    mutable_field: ResolvedDataField,
    fact_subtype: FactSubtype,
    bool_llm_normalizer: BoolLLMNormalizer,
) -> None:
    normalized_values: BulkBoolSelectionResponse = bool_llm_normalizer.normalize_bulk(
        resolved_field=mutable_field, subtype_description=fact_subtype.description
    )
    logger.info(
        "LLM normalized bool values fallback",
        fact_subtype_id=mutable_field.fact_subtype_id,
        values=[(v.input_name, v.input_value, v.selected_bool_value) for v in normalized_values.normalized_values],
    )

    value_to_normalized_value: dict[str, str] = {
        value.input_name: value.selected_bool_value for value in normalized_values.normalized_values
    }

    for value in mutable_field.values:
        value.value = value_to_normalized_value.get(value.observed_name, value.value)


def _perform_enum_llm_suggestions(
    mutable_field: ResolvedDataField,
    enum_type: type[StrEnum],
    enum_llm_normalizer: EnumLLMNormalizer,
) -> None:
    enum_values = [x.value for x in list(enum_type)]

    values_to_normalize: set[str] = set()
    for value in mutable_field.values:
        if value.observed_value in enum_values:
            continue
        is_not_normalized = value.value == value.observed_value
        is_normalized_to_other = value.value != value.observed_value and value.value == "Other"
        if value.observed_value and is_not_normalized or is_normalized_to_other:
            values_to_normalize.add(value.observed_value)

    if len(values_to_normalize) == 0:
        return

    normalized_values: BulkEnumSelectionResponse = enum_llm_normalizer.normalize_bulk(
        values=values_to_normalize,
        enum_values=enum_values,
    )
    logger.info(
        "LLM normalized enum values fallback",
        fact_subtype_id=mutable_field.fact_subtype_id,
        values=[(v.input_value, v.selected_enum_value) for v in normalized_values.normalized_values],
        values_to_normalize=sorted(values_to_normalize),
    )

    value_to_normalized_value: dict[str, str] = {
        value.input_value: value.selected_enum_value for value in normalized_values.normalized_values
    }

    for value in mutable_field.values:
        value.value = value_to_normalized_value.get(value.observed_value, value.value)
