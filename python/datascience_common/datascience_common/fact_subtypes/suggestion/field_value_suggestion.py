from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Any

from facts_client.model.fact_subtype import (  # type: ignore[reportMissingTypeStubs]
    FactSubtype,
)
from infrastructure_common.logging import get_logger
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID
from static_common.enums.fields import FieldType
from static_common.models.file_onboarding import OnboardedFile, ValueSuggestionResult
from structlog.stdlib import BoundLogger

from datascience_common.fact_subtypes.field_subtype_selection.fact_type_to_field_types import (
    get_field_types_for_fact_subtype,
)
from datascience_common.fact_subtypes.field_type_inference.field_inference import (
    FieldConfidence,
    FieldInference,
    InferenceType,
)
from datascience_common.fact_subtypes.field_type_inference.field_type_inference import (
    FieldTypeInference,
    IFieldTypeInference,
)
from datascience_common.fact_subtypes.suggestion.bool_normalizers.bool_llm_normalizer import (
    BoolLLMNormalizer,
)
from datascience_common.fact_subtypes.suggestion.enum_normalizers.enum_llm_normalizer import (
    EnumLLMNormalizer,
)
from datascience_common.fact_subtypes.suggestion.field_value_normalization import (
    FACT_SUBTYPE_ID_TO_NORMALIZER,
    FACT_TYPE_ID_TO_NORMALIZER,
    should_get_original_value,
)
from datascience_common.fact_subtypes.suggestion.llm_field_value_suggestion import (
    perform_llm_suggestions,
)
from datascience_common.fact_subtypes.suggestion.normalizers.field_value_normalizer import (
    FieldValueNormalizationInput,
    IFieldValueNormalizer,
)
from datascience_common.fact_subtypes.suggestion.suggestions_scoring import (
    select_best_value,
)
from datascience_common.fact_subtypes.utils import is_none_value

logger = get_logger()


class FieldValueSuggestion:
    def __init__(
        self,
        field_type_inference: IFieldTypeInference,
        logger: BoundLogger | None = None,
    ) -> None:
        self._field_type_inference = field_type_inference
        self._logger = logger or get_logger()

    def _handle_none_value(self, original_value: Any, fact_subtype: FactSubtype) -> ValueSuggestionResult | None:
        original_value = str(original_value).strip(" \"'")

        if not original_value:
            # We are confident the value should be None
            return ValueSuggestionResult(value=None, confidence=1.0, auto_apply=True)

        if is_none_value(value=original_value):
            # We are confident the value should be None
            return ValueSuggestionResult(value=None, confidence=1.0, auto_apply=True)

        if not fact_subtype:
            # We cannot provide any more guesses
            return ValueSuggestionResult(value=None, confidence=None, auto_apply=None)

        return None

    def _sort_function(self, field_type: FieldType) -> int:
        field_type_to_sort_order = {FieldType.TEXT: 0, FieldType.DATETIME: 1, FieldType.NUMBER: 2, FieldType.INTEGER: 3}
        return field_type_to_sort_order.get(field_type, 3)

    def _get_best_field_type(self, field_types: dict[FieldType, FieldInference[InferenceType]]) -> FieldType:
        best_confidence = max(field_types.values(), key=lambda x: x.confidence.value).confidence.value
        best_field_types = [
            field_type
            for field_type, field_inference in field_types.items()
            if field_inference.confidence.value == best_confidence
        ]

        if len(best_field_types) >= 2:
            best_field_types.sort(key=self._sort_function)

        return best_field_types[0]

    def _suggest_values(
        self,
        fact_subtype: FactSubtype,
        field_inference: FieldInference[InferenceType],
        original_value: Any,
        best_field_type: FieldType,
        allowed_field_types: list[FieldType],
        organization_id: int | None = None,
    ) -> list[ValueSuggestionResult]:
        if field_inference.clean_value_possibilities is None:
            self._logger.warn(
                "No clean values found for field type.",
                chosen_field_inference=field_inference,
            )
            return [ValueSuggestionResult(value=original_value, confidence=1.0, auto_apply=True)]

        normalizer: IFieldValueNormalizer | None = None
        if fact_subtype.id in FACT_SUBTYPE_ID_TO_NORMALIZER:
            normalizer = FACT_SUBTYPE_ID_TO_NORMALIZER[fact_subtype.id]

        elif any([fact_subtype.id in group for group in FACT_SUBTYPE_ID_TO_NORMALIZER]):
            for group in FACT_SUBTYPE_ID_TO_NORMALIZER:
                if fact_subtype.id in group:
                    normalizer = FACT_SUBTYPE_ID_TO_NORMALIZER[group]

        elif fact_subtype.fact_type_id in FACT_TYPE_ID_TO_NORMALIZER:
            normalizer = FACT_TYPE_ID_TO_NORMALIZER[fact_subtype.fact_type_id]

        if normalizer is not None:
            if should_get_original_value(normalizer=normalizer, fact_subtype_id=fact_subtype.id):
                value_suggestion_result = [
                    normalizer(
                        field_value_normalization_input=FieldValueNormalizationInput(
                            field_value=original_value, fact_subtype_id=fact_subtype.id, field_type=best_field_type
                        )
                    )
                ]
                new_field_type: FieldType = (
                    FieldType.TEXT
                    if (FieldType.TEXT in allowed_field_types or len(allowed_field_types) == 0)
                    else allowed_field_types[0]
                )
                for i, v in enumerate(value_suggestion_result):
                    if v.field_type is None:
                        value_suggestion_result[i] = v.assign_field_type(field_type=new_field_type)
                return value_suggestion_result
            else:
                return [
                    normalizer(
                        field_value_normalization_input=FieldValueNormalizationInput(
                            field_value=x, fact_subtype_id=fact_subtype.id, field_type=best_field_type
                        )
                    )
                    for x in field_inference.clean_value_possibilities
                ]
        else:
            # No specific normalizer, return clean values with confidence equal to the confidence of the field type
            return [
                ValueSuggestionResult(
                    value=x,
                    confidence=field_inference.confidence.value,
                    auto_apply=field_inference.confidence == FieldConfidence.HIGH,
                )
                for x in field_inference.clean_value_possibilities
            ]

    def _should_fallback_field_type_to_text(
        self,
        fact_subtype: FactSubtype,
        allowed_field_types: list[FieldType],
        possible_field_types: dict[FieldType, FieldInference[InferenceType]],
    ) -> bool:
        if allowed_field_types == [FieldType.BOOLEAN]:
            # BOOLEAN normalizer maps values like "hard wired" to "Yes",
            # so we should fallback to text if inference is not boolean
            return True

        if fact_subtype.fact_type_id in (FactTypeID.TIME_SERIES,):
            # TIME_SERIES should be parsed as text json
            return True

        if allowed_field_types == [FieldType.TEXT]:
            # If fact accepts text, we should fallback to text
            return True

        if (
            fact_subtype.id == FactSubtypeID.SPRINKLER_AREA_COVERAGE_PERCENT
            and FieldType.BOOLEAN in possible_field_types
        ):
            # SPRINKLER_AREA_COVERAGE_PERCENT can get values like "No" or "Yes" and we don't want to normalize to None
            return True

        return False

    def suggest_values(
        self, fact_subtype: FactSubtype, original_value: Any, organization_id: int | None = None
    ) -> list[ValueSuggestionResult]:
        if (
            suggestion := self._handle_none_value(original_value=original_value, fact_subtype=fact_subtype)
        ) is not None:
            if isinstance(original_value, list):
                return [ValueSuggestionResult(value=original_value, confidence=1.0, auto_apply=True)]
            return [suggestion]

        if isinstance(original_value, list):
            suggestions = [self.suggest_values(fact_subtype=fact_subtype, original_value=x) for x in original_value]
            return [suggestion for sublist in suggestions for suggestion in sublist]

        possible_field_types: dict[
            FieldType, FieldInference[InferenceType]
        ] = self._field_type_inference.infer_field_type(
            value=original_value,
            fact_subtype=fact_subtype,
            allowed_field_types=None,
        )
        allowed_field_types: list[FieldType] = get_field_types_for_fact_subtype(fact_subtype=fact_subtype)

        field_types: dict[FieldType, FieldInference[InferenceType]] = {
            field_type: field_inference
            for field_type, field_inference in possible_field_types.items()
            if field_type in allowed_field_types
        }

        self._logger = self._logger.bind(
            fact_subtype_id=fact_subtype.id,
            fact_type_id=fact_subtype.fact_type_id,
            original_value=original_value,
            field_types=list(field_types.keys()),
            allowed_field_types=allowed_field_types,
        )
        if len(field_types) == 0:
            if self._should_fallback_field_type_to_text(
                fact_subtype=fact_subtype,
                allowed_field_types=allowed_field_types,
                possible_field_types=possible_field_types,
            ):
                field_types = {
                    FieldType.TEXT: FieldInference(
                        confidence=FieldConfidence.LOW, clean_value_possibilities=[str(original_value)]
                    )
                }
            else:
                return [ValueSuggestionResult(value=None, confidence=1.0, auto_apply=True)]

        best_field_type = self._get_best_field_type(field_types=field_types)
        values = self._suggest_values(
            fact_subtype=fact_subtype,
            best_field_type=best_field_type,
            field_inference=field_types[best_field_type],
            allowed_field_types=allowed_field_types,
            original_value=original_value,
            organization_id=organization_id,
        )
        for i, v in enumerate(values):
            if v.field_type is None:
                values[i] = v.assign_field_type(field_type=best_field_type)

        if any(v.value is not None for v in values):
            return values
        else:
            # fallback using other field types
            field_types.pop(best_field_type)
            for fallback_field_type, fallback_field_inference in field_types.items():
                fallback_values = self._suggest_values(
                    fact_subtype=fact_subtype,
                    best_field_type=best_field_type,
                    field_inference=fallback_field_inference,
                    allowed_field_types=allowed_field_types,
                    original_value=original_value,
                    organization_id=organization_id,
                )
                if len(fallback_suggested_values := [x.value for x in fallback_values if x.value is not None]) > 0:
                    self._logger.info(
                        "Fallback value suggestion used.",
                        first_chosen_field_type=best_field_type,
                        fallback_field_type=fallback_field_type,
                        first_suggested_value=fallback_suggested_values[0],
                    )
                    for i, v in enumerate(fallback_values):
                        fallback_values[i] = v.assign_field_type(field_type=best_field_type)
                    return fallback_values

        return values


def perform_suggestions(
    onboarded_file: OnboardedFile,
    id_to_fact_subtype: dict[FactSubtypeID, FactSubtype],
    organization_id: int | None = None,
    fallback_to_llm: bool = False,
) -> None:
    field_value_suggestion = FieldValueSuggestion(
        field_type_inference=FieldTypeInference(),
    )

    bool_llm_normalizer = BoolLLMNormalizer()
    enum_llm_normalizer = EnumLLMNormalizer()
    futures = []
    with ThreadPoolExecutor() as executor:
        for field in onboarded_file.fields:
            if field.fact_subtype_id is None:
                continue

            fact_subtype = id_to_fact_subtype.get(field.fact_subtype_id)
            if fact_subtype is None:
                logger.warning(
                    "Cannot find FactSubtype for fact_subtype_id",
                    fact_subtype_id=field.fact_subtype_id,
                    organization_id=organization_id,
                )
                continue
            for field_value in field.values:
                suggested_values: list[ValueSuggestionResult] = field_value_suggestion.suggest_values(
                    fact_subtype=fact_subtype,
                    original_value=field_value.value,
                    organization_id=organization_id,
                )
                best_suggested_value = select_best_value(
                    values=suggested_values,
                    fact_subtype_id=field.fact_subtype_id,
                    original_value=field_value.value,
                )
                if len(suggested_values) > 1:
                    logger.info(
                        "Multiple suggestions",
                        best_suggested_value=best_suggested_value,
                        suggested_values=[str(x.value) for x in suggested_values],
                        fact_subtype_id=field.fact_subtype_id,
                        field_value=field_value.value,
                        observed_value=field_value.observed_value,
                    )
                field_value.observed_value = field_value.value
                field_value.value = best_suggested_value.value
                field_value.suggested_values = [
                    suggestion for suggestion in suggested_values if suggestion.value != best_suggested_value.value
                ]
                if best_suggested_value.field_type:
                    field.value_type = best_suggested_value.field_type

            if fallback_to_llm:
                fact_subtype = id_to_fact_subtype.get(field.fact_subtype_id)
                if fact_subtype is None:
                    logger.warning(
                        "Fact subtype not found",
                        fact_subtype_id=field.fact_subtype_id,
                        field_name=field.name,
                        field_value=field_value.value,
                    )
                    continue
                futures.append(
                    executor.submit(
                        perform_llm_suggestions,
                        mutable_field=field,
                        fact_subtype=fact_subtype,
                        bool_llm_normalizer=bool_llm_normalizer,
                        enum_llm_normalizer=enum_llm_normalizer,
                    )
                )

        for future in futures:
            try:
                future.result()
            except Exception as e:
                logger.exception("LLM suggestion failed", error=str(e))
