from __future__ import annotations

from datetime import date

from pydantic import BaseModel, Field
from static_common.enums.classification import ClassifierOutputType
from static_common.enums.explanation import ExplanationTypeID
from static_common.models.file_onboarding import (
    BoundingBox,
    CustomizableClassifierEvidence,
)

from datascience_common.enums import BinaryClassificationClassType
from datascience_common.logic.customizable_classifiers import _get_evidence
from datascience_common.models.classifications import BinaryClassification
from datascience_common.models.classifiers.phrase_match_classifier import (
    PhraseMatchClassifierSnippet,
)
from datascience_common.models.dataset import EntityDataset, FileDataset
from datascience_common.models.explanations import ExplanationBuilder


class ClassifierPhrase(BaseModel):
    phrase: str
    weight: float
    excludes: list[str]


class ClassifierTaskInput(BaseModel):
    classifier_name: str
    classifier_description: str
    datasets_key: str
    fact_subtype_id: str | None = None
    phrases: list[ClassifierPhrase] | None = None
    prompt: str | None = None
    output_unit: str | None = None
    output_type: ClassifierOutputType | None = None


class ClassifierFileEvidence(BaseModel):
    document_snippet: str
    snippet_bbox: BoundingBox | None = None
    page: int | None = None
    confidence: float | None = None
    observed_value: str | None = None

    @staticmethod
    def from_customizable_classifier_evidence(
        evidence: CustomizableClassifierEvidence,
    ) -> ClassifierFileEvidence:
        return ClassifierFileEvidence(
            document_snippet=evidence.document_snippet,
            snippet_bbox=evidence.snippet_bbox,
            page=evidence.page,
            confidence=evidence.confidence,
            observed_value=evidence.observed_value,
        )

    def to_customizable_classifier_evidence(self) -> CustomizableClassifierEvidence:
        return CustomizableClassifierEvidence(
            document_snippet=self.document_snippet,
            snippet_bbox=self.snippet_bbox,
            page=self.page,
            confidence=self.confidence,
            observed_value=self.observed_value,
        )


class ClassifierDocumentEvidence(BaseModel):
    class_id: str
    document_id: str
    document_type: str | None = None
    formatted_body: str
    document_snippet: str
    confidence: float | None = None

    @staticmethod
    def from_explanation_builder(explanation_builder: ExplanationBuilder) -> ClassifierDocumentEvidence:
        return ClassifierDocumentEvidence(
            class_id=explanation_builder.class_id,
            document_id=explanation_builder.document.id,
            document_type=explanation_builder.document.document_type_id,
            formatted_body=explanation_builder.formatted_body,
            document_snippet=explanation_builder.snippet,
            confidence=explanation_builder.probability,
        )

    def to_customizable_classifier_evidence(self) -> CustomizableClassifierEvidence:
        return CustomizableClassifierEvidence(document_snippet=self.document_snippet)


class ClassifierTaskOutputValue(BaseModel):
    value: float | bool | str | date
    probability: float | None = None
    file_evidences: list[ClassifierFileEvidence] = Field(default_factory=list)
    document_evidences: list[ClassifierDocumentEvidence] = Field(default_factory=list)

    @staticmethod
    def from_binary_classification(
        binary_classification: BinaryClassification,
        explanation_builders: list[ExplanationBuilder],
        dataset: FileDataset | EntityDataset,
    ) -> ClassifierTaskOutputValue | None:
        if 0.3 < binary_classification.probability < 0.7:
            return None
        value = binary_classification.class_id == BinaryClassificationClassType.YES.name
        result = ClassifierTaskOutputValue(value=value, probability=binary_classification.probability)
        for explanation_builder in explanation_builders:
            if explanation_builder.explanation_type != ExplanationTypeID.DOCUMENT_SNIPPET:
                continue
            if isinstance(dataset, FileDataset):
                evidences = _get_evidence(dataset, None, explanation_builder.snippets, explanation_builder.probability)
                result.file_evidences.extend(
                    [ClassifierFileEvidence.from_customizable_classifier_evidence(e) for e in evidences]
                )
            else:
                result.document_evidences.append(
                    ClassifierDocumentEvidence.from_explanation_builder(explanation_builder)
                )

        return result

    @staticmethod
    def from_value_and_snippets(
        value: float | bool | str | date,
        snippets: list[PhraseMatchClassifierSnippet],
        dataset: FileDataset | EntityDataset,
    ) -> ClassifierTaskOutputValue:
        result = ClassifierTaskOutputValue(value=value)
        evidences = _get_evidence(dataset, None, snippets)
        result.file_evidences = [ClassifierFileEvidence.from_customizable_classifier_evidence(e) for e in evidences]
        return result


class ClassifierTaskOutput(BaseModel):
    values: list[ClassifierTaskOutputValue] = Field(default_factory=list)
