from __future__ import annotations

from typing import Any, Literal

from PIL.Image import Image
from pydantic import BaseModel, Field
from static_common.enums.classification import InputProcessingType
from static_common.models.file_onboarding import OnboardedFile

from datascience_common.models.dataset import EntityDataset, FileDataset
from datascience_common.utils.text_extraction.models import TextExtractionResult


class CustomizableClassifierDataset(BaseModel):
    input_processing_type: InputProcessingType
    dataset: FileDataset | EntityDataset

    model_config = {"arbitrary_types_allowed": True}


class KVPairCustomizableClassifierDataset(CustomizableClassifierDataset):
    input_processing_type: Literal[InputProcessingType.KV_PAIRS]
    processed_data: OnboardedFile | None = None

    model_config = {"arbitrary_types_allowed": True}


class OCRCustomizableClassifierDataset(CustomizableClassifierDataset):
    input_processing_type: Literal[InputProcessingType.OCR_TEXT]
    extraction_results: list[TextExtractionResult] = Field(default_factory=list)
    raw_result: Any = None

    model_config = {"arbitrary_types_allowed": True}


class ImageCustomizableClassifierDataset(CustomizableClassifierDataset):
    input_processing_type: Literal[InputProcessingType.IMAGE]
    images: list[Image] = Field(default_factory=list)

    model_config = {"arbitrary_types_allowed": True}
