from pathlib import Path
import json
import pickle

from common.clients.copilot import <PERSON><PERSON>lotV3<PERSON>lient
from common.clients.facts import FactsClient
from common.clients.facts_v2 import FactsClientV2
from common.clients.finys.finys import FinysClient, FinysClientConfig
from entity_resolution_service_client_v3 import Api<PERSON>lient as ErsV3<PERSON><PERSON>
from entity_resolution_service_client_v3 import Configuration as Ersv3Configuration
from entity_resolution_service_client_v3 import <PERSON>faultApi as ErsV3ApiClient

from report_serializer.config.config import SerializerConfig
from report_serializer.config.kalepa_api_clients import KalepaApiClients
from report_serializer.config.orgs.conifer_finys import CONIFER_FINYS_SERIALIZER_CONFIG
from report_serializer.orgs.conifer.conifer_finys_manual_dict_config import (
    ConiferFinysManualDictSerializableConfig,
)
from report_serializer.results.models.report_repr import ReportRepr
from report_serializer.serializer import get_report_representation, serialize_report
from report_serializer.serializers.manual_dict_serializer import ManualDictSerializer

CACHE_DIR = Path("/tmp/rep_serializer_cache")
CURRENT_PATH = Path(__file__).parent
KALEPA_ENV = "prod"


def _prepare_conifer_config(submission_id: str) -> SerializerConfig:
    conifer_config = CONIFER_FINYS_SERIALIZER_CONFIG.as_template()

    # For staging we want to take submission from Kalepa organization
    if KALEPA_ENV == "stage":
        conifer_config.serialization_main_config.organization_id = 3

    conifer_config.serialization_main_config.submission_id = submission_id

    conifer_config.kalepa_api_clients = KalepaApiClients(
        copilot_v3=_create_copilot_v3_api_client(),
        ers_v3=_create_ers_v3_api_client(),
        facts_v1=_create_facts_v1_api_client(),
        facts_v2=_create_facts_v2_api_client(),
    )

    return conifer_config


def _create_copilot_v3_api_client() -> CopilotV3Client:
    base_url = {
        "dev": "http://copilot-api.dev.int:8080/api/v3.0",
        "stage": "http://copilot-api.stage.int:8080/api/v3.0",
        "prod": "http://copilot-api.prod.int:8080/api/v3.0",
    }
    return CopilotV3Client(base_url=base_url[KALEPA_ENV], cache_client=None)


def _create_ers_v3_api_client() -> ErsV3ApiClient:
    base_url = {
        "dev": "http://entity-resolution.dev.int:8080/api/v3.0",
        "stage": "http://entity-resolution.stage.int:8080/api/v3.0",
        "prod": "http://entity-resolution.prod.int:8080/api/v3.0",
    }
    configuration = Ersv3Configuration(base_url[KALEPA_ENV])
    return ErsV3ApiClient(ErsV3AClient(configuration=configuration))


def _create_facts_v1_api_client() -> FactsClient:
    base_url = {
        "dev": "http://facts-api.dev.int:8080/api/v1.0",
        "stage": "http://facts-api.stage.int:8080/api/v1.0",
        "prod": "http://facts-api.prod.int:8080/api/v1.0",
    }
    return FactsClient(base_url=base_url[KALEPA_ENV], cache_client=None)


def _create_facts_v2_api_client() -> FactsClientV2:
    base_url = {
        "dev": "http://facts-api.dev.int:8080/api/v2.0",
        "stage": "http://facts-api.stage.int:8080/api/v2.0",
        "prod": "http://facts-api.prod.int:8080/api/v2.0",
    }
    return FactsClientV2(base_url=base_url[KALEPA_ENV], cache_client=None)


def _get_sub_id_from_rep_id(report_id: str, cop_client: CopilotV3Client) -> str:
    report = cop_client.get_report(report_id, expand="submissions")
    submission_id = report.submissions[0].id
    return submission_id


def _obtain_report_repr(report_id: str, copilot_v3_client, force_refresh: bool = False) -> ReportRepr:
    if not force_refresh:
        try:
            with open(CACHE_DIR / f"{report_id}.pckl", "rb") as f:
                return pickle.load(f)
        except FileNotFoundError:
            pass

    serializer_config = _prepare_conifer_config(submission_id=_get_sub_id_from_rep_id(report_id, copilot_v3_client))
    report_repr = get_report_representation(serializer_config, "manual-test-json-export")

    CACHE_DIR.mkdir(parents=True, exist_ok=True)

    with open(CACHE_DIR / f"{report_id}.pckl", "wb") as f:
        pickle.dump(report_repr, f)

    return report_repr


if __name__ == "__main__":
    copilot_v3_client = _create_copilot_v3_api_client()

    full_serializer_flow = False
    force_refresh = False
    dump_to_txt_file = False
    finys_integration = False

    #
    # PROD, manual test cherry picks
    #
    report_ids = [
        # "59fb0f04-dd7e-4e8c-a9d5-618db131da4c",  # has DBA
        # "7a34dbfe-750e-4950-9ef2-e31d9288154e",  # HasGL = True
        # "57d4485f-3f62-41af-863b-509ef711aec5",  # has construction class on structures and burglar alarm
        # "1e237893-463a-464a-b1d8-08894e1ff2be",  # has open status on business
        # "256fa59c-b34a-4af1-ac8e-f17e3d2a50a0",  # has lots of premises, construction class
        # "0ae0d37f-4613-4905-a712-62922f9fb15e",  # has tenants improvements and betterments
        "4f744229-4b2c-4361-8d0b-0ed75e614eca",  # has THC license
    ]

    #
    # PROD, client back and forth
    #
    report_ids = [
        # "f8dc5dc0-e562-4732-9520-9a1c7c1062f8",
        # "50a58c9f-e933-4efb-85c5-ea9ff482656d",
        # "de35bb6a-6db6-44a5-ac23-0c733d60a363",
        # "52277581-062b-4eaf-8c82-a31ec8f9d9c9",
        "dffbb214-259f-406c-9585-c5d24407095c",
        # "09cf4038-50fc-418c-91e0-754f40680058",
        # "732b3e93-b016-4d96-9b28-7350f78bdbbf",
        # "6197dcee-31f7-4495-96e5-82f4a2eb9032",
        # "14b3f711-1b43-4dbc-af0e-4aae55557f49",
    ]

    if dump_to_txt_file:
        out_path = Path("report_dump.txt")
        f_out = out_path.open("w", encoding="utf-8")

    if not full_serializer_flow:
        for report_id in report_ids:
            report_repr = _obtain_report_repr(report_id, copilot_v3_client, force_refresh=force_refresh)
            dict_result = ManualDictSerializer(ConiferFinysManualDictSerializableConfig()).serialize(report_repr)
            # print(dict_result)
            jj = json.dumps(dict_result, indent=2)
            print(jj)

            finys_quote_nr = "Integration Disabled"
            if finys_integration:
                finys_url = "https://cohuatservices.finys.com/Policy"
                finys_client_secret = "secret"
                finys_config = FinysClientConfig(
                    base_url=finys_url, client_id="ClientCredentials_Kalepa", client_secret=finys_client_secret
                )
                conifer_finys_client = FinysClient(finys_config)
                create_quote_response = conifer_finys_client.create_quote_from_dict(quote=dict_result)
                print(f"Finys Quote Nr: {create_quote_response.quote_number}")
                finys_quote_nr = create_quote_response.quote_number

            if dump_to_txt_file:
                rep_name = report_repr.report_name
                f_out.write(
                    f"Report ID   : {report_id}\nReport Name : {rep_name}\nFinys Quote Nr: {finys_quote_nr}\nJSON"
                    f" Export :\n{jj}\n"
                    + "-" * 60
                    + "\n\n"
                )
            print(jj)
    else:
        for report_id in report_ids:
            conifer_config = _prepare_conifer_config(
                submission_id=_get_sub_id_from_rep_id(report_id, copilot_v3_client)
            )
            results = serialize_report(conifer_config, call_origin="manual_test")
            json_result = json.dumps(results[0].result_handle, indent=4)
            print(results)
            print(json_result)

    if dump_to_txt_file:
        f_out.close()
