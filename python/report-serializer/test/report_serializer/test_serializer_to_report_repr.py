from test.report_serializer.consts import ARU_ORGANIZATION_ID, ARU_SUBMISSION_ID
from test.report_serializer.mocks import (
    create_mocked_copilot_v3_api_client,
    create_mocked_ers_v3_api_client,
    create_mocked_facts_v1_api_client,
)

from static_common.enums.business import BusinessType

from report_serializer.config.config import SerializerConfig
from report_serializer.config.kalepa_api_clients import KalepaApiClients
from report_serializer.config.orgs.aru import ARU_SERIALIZER_CONFIG
from report_serializer.serializer import get_report_representation


def test_serializer_to_report_repr():
    # Let's for now use ARU config because it sets a lot of details. In the future we should
    # create test data specific for this test.
    submission_id = ARU_SUBMISSION_ID
    aru_config = _prepare_aru_config(submission_id=submission_id, organization_id=ARU_ORGANIZATION_ID)
    report_repr = get_report_representation(aru_config, call_origin="aru_auto_test")

    assert report_repr is not None, "Report representation is None"

    # TODO(anyone): Assert hundreds more fields here, add few if you can during adding newly added ones
    assert report_repr.report_name == "Top Level ARU Sub Name"
    assert report_repr.description_of_operations == "ARU FNI description"
    assert report_repr.agent_code == "Agent Code"
    assert report_repr.agency_code == "Agency Code"

    assert len(report_repr.underwriters) == 1
    assert report_repr.underwriters[0].first_name == "John"
    assert report_repr.underwriters[0].last_name == "Doe"
    assert report_repr.underwriters[0].email == "<EMAIL>"
    assert report_repr.underwriters[0].source == "EMAIL"

    assert len(report_repr.insureds) == 4
    assert report_repr.insureds[0].name == "FNI with ARU requested name"
    assert report_repr.insureds[0].dba_name == "FNI Public Verified DBA Name"
    assert report_repr.insureds[0].phone_number == "2317665173"
    assert report_repr.insureds[0].email == "<EMAIL>"
    assert report_repr.insureds[1].name == "ONI with ARU Inc. requested name"
    assert report_repr.insureds[2].name == "Third insured with ARU Inc. requested name"
    assert report_repr.insureds[3].name == "Hidden third insured with ARU Inc. requested name"
    assert report_repr.insureds[0].business_type == BusinessType.LLC
    assert report_repr.insureds[1].business_type == BusinessType.CORPORATION


def _prepare_aru_config(submission_id: str, organization_id: int) -> SerializerConfig:
    aru_config = ARU_SERIALIZER_CONFIG.as_template()

    aru_config.serialization_main_config.organization_id = organization_id
    aru_config.serialization_main_config.submission_id = submission_id

    aru_config.kalepa_api_clients = KalepaApiClients(
        copilot_v3=create_mocked_copilot_v3_api_client(),
        ers_v3=create_mocked_ers_v3_api_client(),
        facts_v1=create_mocked_facts_v1_api_client(),
    )

    return aru_config
