from test.report_serializer.consts import (
    ARU_FNI_BUSINESS_ID,
    ARU_FNI_PREMISES_ID,
    ARU_FNI_SECOND_PREMISES_ID,
    ARU_ONI_BUSINESS_ID,
    ARU_ONI_PREMISES_ID,
    ARU_THIRD_INSURED_BUSINESS_ID,
    ARU_THIRD_PREMISES_ID,
    PARAGON_FNI_BUSINESS_ID,
    PARAGON_FNI_PREMISES_ID,
    PARAGON_FOURTH_INSURED_BUSINESS_ID,
    PARAGON_FOURTH_PREMISES_ID,
    PARAGON_ONI_BUSINESS_ID,
    PARAGON_ONI_PREMISES_ID,
    PARAGON_THIRD_INSURED_BUSINESS_ID,
    PARAGON_THIRD_PREMISES_ID,
)

from entity_resolution_service_client_v3 import (
    EntitiesEnvelope,
    Entity,
    EntityName,
    EntityPremises,
    ExternalIdentifier,
    Premises,
)
from static_common.enums.entity import EntityNameType, EntityPremisesType


class MockedErsV3ApiClient:
    def bulk_get_entities(self, **kwargs) -> EntitiesEnvelope:
        if kwargs["call_origin"] == "paragon_data.report-serializer":
            entities = [
                Entity(
                    id=PARAGON_FNI_BUSINESS_ID,
                    premises=[
                        EntityPremises(
                            premises_id=PARAGON_FNI_PREMISES_ID,
                            premises=Premises(
                                id=PARAGON_FNI_PREMISES_ID,
                                address_line_1="123 Main St",
                                address_line_2="Suite 100",
                                city="New York",
                                state="NY",
                                postal_code="10001",
                                county="New York",
                                latitude=40.7128,
                                longitude=74.0060,
                            ),
                        )
                    ],
                ),
                Entity(
                    id=PARAGON_ONI_BUSINESS_ID,
                    premises=[
                        EntityPremises(
                            premises_id=PARAGON_ONI_PREMISES_ID,
                            premises=Premises(
                                id=PARAGON_ONI_PREMISES_ID,
                                address_line_1="124 Main St",
                                address_line_2="Suite 101",
                                city="New York",
                                state="NY",
                                postal_code="10001",
                                county="New York",
                                latitude=41.7128,
                                longitude=75.0060,
                            ),
                        )
                    ],
                ),
                Entity(
                    id=PARAGON_THIRD_INSURED_BUSINESS_ID,
                    premises=[
                        EntityPremises(
                            premises_id=PARAGON_THIRD_PREMISES_ID,
                            premises=Premises(
                                id=PARAGON_THIRD_PREMISES_ID,
                                address_line_1="125 Main St",
                                address_line_2="Suite 102",
                                city="New York",
                                state="NY",
                                postal_code="10001",
                                county="New York",
                            ),
                        )
                    ],
                ),
                Entity(
                    id=PARAGON_FOURTH_INSURED_BUSINESS_ID,
                    premises=[
                        EntityPremises(
                            premises_id=PARAGON_FOURTH_PREMISES_ID,
                            premises=Premises(
                                id=PARAGON_FOURTH_PREMISES_ID,
                                address_line_1="126 Main St",
                                address_line_2="Suite 103",
                                city="New York",
                                state="NY",
                                postal_code="10001",
                                county="New York",
                            ),
                        )
                    ],
                ),
            ]
            return EntitiesEnvelope(count=len(entities), entities=entities)

        if kwargs["call_origin"] in [
            "aru_auto_test.report-serializer",
            "aru_auto_test_no_structure_facts.report-serializer",
            "conifer_auto_test.report-serializer",
        ]:
            entities = [
                Entity(
                    id=ARU_FNI_BUSINESS_ID,
                    business_type="LLC",
                    names=[
                        EntityName(
                            is_public=False,
                            is_verified=True,
                            type=EntityNameType.LEGAL_NAME,
                            value="FNI not public verified legal name",
                        ),
                        EntityName(
                            is_public=True,
                            is_verified=True,
                            type=EntityNameType.LEGAL_NAME,
                            value="FNI public verified legal name",
                        ),
                        EntityName(
                            is_public=True,
                            is_verified=True,
                            type=EntityNameType.DBA_NAME,
                            value="FNI public verified DBA name",
                        ),
                        EntityName(
                            is_public=True,
                            is_verified=False,
                            type=EntityNameType.LEGAL_NAME,
                            value="FNI public not verified legal name",
                        ),
                        EntityName(
                            is_public=True,
                            is_verified=True,
                            type=EntityNameType.UNDEFINED,
                            value="FNI public verified undefined name",
                        ),
                    ],
                    premises=[
                        EntityPremises(
                            premises_id=ARU_FNI_PREMISES_ID,
                            type=EntityPremisesType.PHYSICAL_ADDRESS.value,
                            premises=Premises(
                                id=ARU_FNI_PREMISES_ID,
                                address_line_1="123 Main St",
                                address_line_2="Suite 100",
                                city="New York",
                                state="NY",
                                postal_code="10001",
                                county="New York",
                                latitude=40.7128,
                                longitude=74.0060,
                            ),
                        ),
                        EntityPremises(
                            premises_id=ARU_FNI_SECOND_PREMISES_ID,
                            type=EntityPremisesType.MAILING_ADDRESS.value,
                            premises=Premises(
                                id=ARU_FNI_SECOND_PREMISES_ID,
                                address_line_1="666 Main St",
                                address_line_2="Suite 200",
                                city="New York",
                                state="NY",
                                postal_code="10001",
                                county="New York",
                                latitude=40.7128,
                                longitude=74.0060,
                            ),
                        ),
                    ],
                    identifiers=[
                        ExternalIdentifier(type="PHONE", value="2317665173"),
                        ExternalIdentifier(type="EMAIL", value="<EMAIL>"),
                    ],
                ),
                Entity(
                    id=ARU_ONI_BUSINESS_ID,
                    names=[
                        EntityName(
                            is_public=False,
                            is_verified=True,
                            type=EntityNameType.LEGAL_NAME,
                            value="ONI not public verified legal name",
                        ),
                        EntityName(
                            is_public=True,
                            is_verified=False,
                            type=EntityNameType.LEGAL_NAME,
                            value="ONI public not verified legal name",
                        ),
                        EntityName(
                            is_public=True,
                            is_verified=True,
                            type=EntityNameType.DBA_NAME,
                            value="ONI public verified DBA name",
                        ),
                        EntityName(
                            is_public=True,
                            is_verified=True,
                            type=EntityNameType.UNDEFINED,
                            value="ONI public verified undefined name",
                        ),
                    ],
                    premises=[
                        EntityPremises(
                            premises_id=ARU_ONI_PREMISES_ID,
                            premises=Premises(
                                id=ARU_ONI_PREMISES_ID,
                                address_line_1="124 Main St",
                                address_line_2="Suite 101",
                                city=None,
                                state="NY",
                                postal_code="10001",
                                county="New York",
                                latitude=41.7128,
                                longitude=75.0060,
                            ),
                        )
                    ],
                ),
                Entity(
                    id=ARU_THIRD_INSURED_BUSINESS_ID,
                    premises=[
                        EntityPremises(
                            premises_id=ARU_THIRD_PREMISES_ID,
                            premises=Premises(
                                id=ARU_THIRD_PREMISES_ID,
                                address_line_1="125 Main St",
                                address_line_2="Suite 102",
                                city="New York",
                                state="NY",
                                postal_code="10001",
                                county="New York",
                            ),
                        )
                    ],
                ),
            ]
            return EntitiesEnvelope(count=len(entities), entities=entities)

        return None

    def get_entity(self, entity_id, **kwargs) -> Entity:
        pass
