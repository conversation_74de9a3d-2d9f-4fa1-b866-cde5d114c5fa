from datetime import date, datetime, timedelta
from test.report_serializer.consts import (
    ARU_FNI_BUSINESS_ID,
    ARU_ONI_BUSINESS_ID,
    ARU_OR<PERSON><PERSON>ZATION_ID,
    ARU_PRIMARY_NAICS_CODE,
    ARU_REPORT_ID,
    ARU_SUBMISSION_ID,
    ARU_THIRD_INSURED_BUSINESS_ID,
    CONIFER_ORGANIZATION_ID,
    CONIFER_REPORT_ID,
    CONIFER_SUBMISSION_ID,
    PARAGON_FNI_BUSINESS_ID,
    PARAGON_FOURTH_INSURED_BUSINESS_ID,
    PARAGON_ONI_BUSINESS_ID,
    PARAGON_ORG_ID,
    PARAGON_REPORT_ID,
    PARAGON_SU<PERSON>ISSION_ID,
    PARAGON_THIRD_INSURED_BUSINESS_ID,
)
from uuid import UUID

from copilot_client_v3 import (
    AssignedUnderwriter,
    Brokerage,
    BrokerageEmployee,
    Coverage,
    Loss,
    Report,
    RequestedCoverage,
    Submission,
    SubmissionBusiness,
    SubmissionNote,
)
from copilot_client_v3.models.submission_identifier import SubmissionIdentifier
from dateutil.tz import tzutc
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured


class MockedCopilotV3ApiClient:
    def get_submission(
        self,
        submission_id: str | UUID,
        expand: list[str] | None = None,
        enrich_with_ers_data: bool = False,
        call_origin: str = "",
    ) -> Submission | None:
        if str(submission_id) == ARU_SUBMISSION_ID:
            return _aru_submission()

        if str(submission_id) == PARAGON_SUBMISSION_ID:
            return _paragon_submission()

        if str(submission_id) == CONIFER_SUBMISSION_ID:
            return _conifer_submission()

        return None

    def get_submission_notes(self, submission_id: str | UUID, **kwargs) -> list[SubmissionNote]:
        if str(submission_id) == PARAGON_SUBMISSION_ID:
            return _get_mock_notes(PARAGON_SUBMISSION_ID)
        # Add more cases if needed for other submission IDs
        return []

    def get_report(self, report_id: str | UUID, expand: list[str] | None = None) -> Report | None:
        if str(report_id) == ARU_REPORT_ID:
            return _aru_report()

        if str(report_id) == PARAGON_REPORT_ID:
            return _example_report()

        if str(report_id) == CONIFER_REPORT_ID:
            return _conifer_report()

        return None

    def get_all_losses(self, submission_id: str | UUID) -> list[Loss]:
        if str(submission_id) == ARU_SUBMISSION_ID:
            return [
                self._create_loss(
                    id="00000000-0000-0000-0000-000000000001",
                    claim_number="12345",
                    insured_name="John Doe",
                    loss_date=date(2021, 1, 1),
                    claim_date=date(2021, 1, 1),
                    policy_effective_date=date(2021, 1, 1),
                    policy_expiration_date=date(2022, 1, 1),
                    loss_address="123 Main St, Springfield, IL",
                    claim_description="Theft",
                    line_of_business="Property",
                    sum_of_loss_reserve=1000.0,
                    sum_of_alae_reserve=100.0,
                    sum_of_net_paid_loss=500.0,
                    sum_of_net_outstanding_loss=500.0,
                    sum_of_net_paid_alae=50.0,
                    sum_of_net_outstanding_alae=50.0,
                    sum_of_total_net_incurred=5000.0,
                    claim_status="Open",
                    recoveries=0.0,
                    file_id="00000000-0000-0000-0000-000000000001",
                ),
                self._create_loss(
                    id="00000000-0000-0000-0000-000000000002",
                    claim_number="54321",
                    insured_name="Jane Doe",
                    loss_date=date(2021, 2, 1),
                    claim_date=date(2021, 2, 1),
                    policy_effective_date=date(2021, 2, 1),
                    policy_expiration_date=date(2022, 2, 1),
                    loss_address="456 Elm St, Springfield, IL",
                    claim_description="Fire",
                    line_of_business="Property",
                    sum_of_loss_reserve=2000.0,
                    sum_of_alae_reserve=200.0,
                    sum_of_net_paid_loss=1000.0,
                    sum_of_net_outstanding_loss=1000.0,
                    sum_of_net_paid_alae=100.0,
                    sum_of_net_outstanding_alae=100.0,
                    sum_of_total_net_incurred=3000.0,
                    claim_status="Closed",
                    recoveries=100.0,
                    file_id="00000000-0000-0000-0000-000000000002",
                ),
            ]
        return []

    def _create_loss(
        self,
        id: str,
        claim_number: str,
        insured_name: str,
        loss_date: date,
        claim_date: date,
        policy_effective_date: date,
        policy_expiration_date: date,
        loss_address: str,
        claim_description: str,
        line_of_business,
        sum_of_loss_reserve,
        sum_of_alae_reserve,
        sum_of_net_paid_loss,
        sum_of_net_outstanding_loss,
        sum_of_net_paid_alae,
        sum_of_net_outstanding_alae,
        sum_of_total_net_incurred,
        claim_status,
        recoveries,
        file_id,
    ) -> Loss:
        return Loss(
            id=str(id),
            claim_number=claim_number,
            insured_name=insured_name,
            loss_date=loss_date,
            claim_date=claim_date,
            policy_effective_date=policy_effective_date,
            policy_expiration_date=policy_expiration_date,
            loss_address=loss_address,
            claim_description=claim_description,
            line_of_business=line_of_business,
            sum_of_loss_reserve=sum_of_loss_reserve,
            sum_of_alae_reserve=sum_of_alae_reserve,
            sum_of_net_paid_loss=sum_of_net_paid_loss,
            sum_of_net_outstanding_loss=sum_of_net_outstanding_loss,
            sum_of_net_paid_alae=sum_of_net_paid_alae,
            sum_of_net_outstanding_alae=sum_of_net_outstanding_alae,
            sum_of_total_net_incurred=sum_of_total_net_incurred,
            claim_status=claim_status,
            recoveries=recoveries,
            file_id=file_id,
        )


def _paragon_submission() -> Submission:
    return Submission(
        id=PARAGON_SUBMISSION_ID,
        report_ids=[PARAGON_REPORT_ID],
        proposed_effective_date=datetime(2025, 1, 1, 0, 0),
        policy_expiration_date=datetime(2026, 1, 1, 0, 0),
        received_date=datetime(2024, 10, 31, 19, 33, 25, 310848, tzinfo=tzutc()),
        created_at=datetime(2024, 10, 31, 18, 33, 25, 310848, tzinfo=tzutc()),
        assigned_underwriters=[AssignedUnderwriter(name="John Doe", email="<EMAIL>", source="EMAIL")],
        generated_description_of_operations="Example description",
        target_premium=1000.0,
        expired_premium=900.0,
        coverages=[RequestedCoverage(coverage_type="PRIMARY", coverage=Coverage(display_name="Commercial Property"))],
        broker=BrokerageEmployee(name="John Doe", email="<EMAIL>"),
        brokerage=Brokerage(name="Example Brokerage"),
        organization_group="Paragon Best UWs",
        recommendation_v2_action="REFER",
        recommendation_v2_score=0.85,
        stage="QUOTED",
        sic_code="SIC_1234",
        fni_state="CA",
        identifiers=[
            SubmissionIdentifier(
                id="00000000-0000-0000-0000-000000000001",
                identifier_type="policy_number",
                identifier="1234567890",
            ),
            SubmissionIdentifier(
                id="00000000-0000-0000-0000-000000000002",
                identifier_type="quote_number",
                identifier="1234567891",
            ),
        ],
        primary_naics_code="NAICS_123456",
        businesses=[
            SubmissionBusiness(
                business_id=PARAGON_FNI_BUSINESS_ID,
                named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                requested_name="First named insured",
            ),
            SubmissionBusiness(
                business_id=PARAGON_ONI_BUSINESS_ID,
                named_insured=SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED,
                requested_name="Other named insured",
            ),
            SubmissionBusiness(
                business_id=PARAGON_THIRD_INSURED_BUSINESS_ID,
                named_insured=None,
                requested_name="Third insured",
            ),
            SubmissionBusiness(
                business_id=PARAGON_FOURTH_INSURED_BUSINESS_ID,
                named_insured=None,
                requested_name="Fourth insured",
            ),
        ],
    )


def _aru_submission() -> Submission:
    return Submission(
        id=ARU_SUBMISSION_ID,
        report_ids=[ARU_REPORT_ID],
        agent_code="Agent Code",
        agency_code="Agency Code",
        proposed_effective_date=datetime(2025, 1, 1, 0, 0),
        policy_expiration_date=datetime(2026, 1, 1, 0, 0),
        received_date=datetime(2024, 10, 19, 19, 33, 25, 310848, tzinfo=tzutc()),
        created_at=datetime(2024, 10, 25, 22, 33, 25, 310848, tzinfo=tzutc()),
        generated_description_of_operations="ARU FNI description",
        target_premium=1000.0,
        expired_premium=900.0,
        name="Top Level ARU Sub Name",
        assigned_underwriters=[AssignedUnderwriter(name="John Doe", email="<EMAIL>", source="EMAIL")],
        coverages=[
            RequestedCoverage(
                coverage_type="PRIMARY", coverage=Coverage(display_name="Commercial Property"), quoted_premium=1001.0
            )
        ],
        broker=BrokerageEmployee(name="John Doe", email="<EMAIL>"),
        brokerage=Brokerage(name="Example Brokerage"),
        primary_naics_code=ARU_PRIMARY_NAICS_CODE,
        businesses=[
            SubmissionBusiness(
                business_id=ARU_FNI_BUSINESS_ID,
                named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                requested_name="FNI with ARU requested name",
            ),
            SubmissionBusiness(
                business_id=ARU_ONI_BUSINESS_ID,
                named_insured=SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED,
                requested_name="ONI with ARU Inc. requested name",
            ),
            SubmissionBusiness(
                business_id=ARU_THIRD_INSURED_BUSINESS_ID,
                named_insured=None,
                requested_name="Third insured with ARU Inc. requested name",
                hide_property_facts=True,
            ),
            SubmissionBusiness(
                business_id=ARU_THIRD_INSURED_BUSINESS_ID,
                named_insured=None,
                requested_name="Hidden third insured with ARU Inc. requested name",
                hide=True,
            ),
        ],
    )


def _aru_report() -> Report:
    return Report(
        id=ARU_REPORT_ID,
        organization_id=ARU_ORGANIZATION_ID,
        created_at=datetime(2024, 10, 20, 15, 33, 25, 310848, tzinfo=tzutc()),
        email_body="<p>This is <b>HTML</b></p>",
        bundled_reports=[{"id": "0"}],
    )


def _example_report() -> Report:
    return Report(
        id=PARAGON_REPORT_ID, organization_id=PARAGON_ORG_ID, bundled_reports=[], created_at=datetime.now(tzutc())
    )


# Helper function to create mock notes
def _get_mock_notes(submission_id: str | UUID) -> list[SubmissionNote]:
    note_id_1 = "11111111-1111-1111-1111-111111111111"
    note_id_2 = "*************-2222-2222-************"
    now = datetime.now(tzutc())
    return [
        SubmissionNote(
            id=note_id_1,
            submission_id=str(submission_id),
            text="This is the first mock note.",
            created_at=now - timedelta(days=1),
            author_id="123",
            author_name="Test Author 1",
            last_edit_by_id="123",
            last_editor_name="Test Author 1",
            last_edit_at=now - timedelta(hours=12),
            is_generated_note=False,
            is_editable=True,
            is_note=True,
            html_content="<p>This is the first mock note.</p>",
            referred_to_user_ids=["a1", "b2"],
            referrals_closed_to_user_ids=["c3"],
            rule_id="r1",
        ),
        SubmissionNote(
            id=note_id_2,
            submission_id=str(submission_id),
            text="This is the second mock note (generated).",
            created_at=now,
            author_id="456",
            author_name="Generator Bot",
            last_edit_by_id=None,
            last_editor_name=None,
            last_edit_at=None,
            is_generated_note=True,
            is_editable=False,
            is_note=False,  # Example of a non-user note
            html_content="<p>This is the second mock note (generated).</p>",
            referred_to_user_ids=[],
            referrals_closed_to_user_ids=[],
            rule_id="r2",
        ),
    ]


def _conifer_submission() -> Submission:
    return Submission(
        id=CONIFER_SUBMISSION_ID,
        report_ids=[CONIFER_REPORT_ID],
        agent_code="Agent Code",
        agency_code="Agency Code",
        proposed_effective_date=datetime(2025, 1, 1, 0, 0),
        policy_expiration_date=datetime(2026, 1, 1, 0, 0),
        received_date=datetime(2024, 10, 19, 19, 33, 25, 310848, tzinfo=tzutc()),
        created_at=datetime(2024, 10, 25, 22, 33, 25, 310848, tzinfo=tzutc()),
        generated_description_of_operations="Conifer FNI description",
        target_premium=1000.0,
        expired_premium=900.0,
        name="Top Level Conifer Sub Name",
        assigned_underwriters=[AssignedUnderwriter(name="John Doe", email="<EMAIL>", source="EMAIL")],
        coverages=[
            RequestedCoverage(
                coverage_type="PRIMARY", coverage=Coverage(display_name="Commercial Property"), quoted_premium=1001.0
            ),
            RequestedCoverage(
                coverage_type="PRIMARY", coverage=Coverage(display_name="Product Liability"), quoted_premium=1002.0
            ),
        ],
        broker=BrokerageEmployee(name="John Doe", email="<EMAIL>"),
        brokerage=Brokerage(name="Example Brokerage"),
        primary_naics_code=ARU_PRIMARY_NAICS_CODE,
        businesses=[
            SubmissionBusiness(
                business_id=ARU_FNI_BUSINESS_ID,
                named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                requested_name="FNI with Conifer requested name",
            ),
            SubmissionBusiness(
                business_id=ARU_ONI_BUSINESS_ID,
                named_insured=SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED,
                requested_name="ONI with Conifer Inc. requested name",
            ),
            SubmissionBusiness(
                business_id=ARU_THIRD_INSURED_BUSINESS_ID,
                named_insured=None,
                requested_name="Third insured with Conifer Inc. requested name",
                hide_property_facts=True,
            ),
            SubmissionBusiness(
                business_id=ARU_THIRD_INSURED_BUSINESS_ID,
                named_insured=None,
                requested_name="Hidden third insured with Conifer Inc. requested name",
                hide=True,
            ),
        ],
    )


def _conifer_report() -> Report:
    return Report(
        id=CONIFER_REPORT_ID,
        organization_id=CONIFER_ORGANIZATION_ID,
        created_at=datetime(2024, 10, 20, 15, 33, 25, 310848, tzinfo=tzutc()),
        email_body="<p>This is <b>HTML</b></p>",
        bundled_reports=[{"id": "0"}],
    )
