from datetime import datetime
from test.report_serializer.consts import (
    ARU_EQUIPMENT_1_ID,
    ARU_EQUIPMENT_2_ID,
    ARU_FNI_BUSINESS_ID,
    ARU_PREMISES_TO_STRUCTURES,
    ARU_PRIMARY_NAICS_CODE,
    ARU_SUBMISSION_ID,
    ARU_THIRD_STRUCTURE_ID,
    PARAGON_FNI_BUSINESS_ID,
    PARAGON_PREMISES_TO_STRUCTURES,
)
from typing import cast
from uuid import UUID

from facts_client.model.class_probability import ClassProbability
from facts_client.model.classification_observation import ClassificationObservation
from facts_client.model.construction_class_observation import (
    ConstructionClassObservation,
)
from facts_client.model.datetime_observation import DatetimeObservation
from facts_client.model.document import Document
from facts_client.model.fact import Fact
from facts_client.model.fact_subtype import FactSubtype
from facts_client.model.first_party_source import FirstPartySource
from facts_client.model.float_value_observation import FloatValueObservation
from facts_client.model.integer_observation import IntegerObservation
from facts_client.model.license import License
from facts_client.model.measurement_observation import MeasurementObservation
from facts_client.model.naics_code import NaicsCode
from facts_client.model.news import News
from facts_client.model.number_of_stories_observation import NumberOfStoriesObservation
from facts_client.model.relationship import Relationship
from facts_client.model.relationships_observation import RelationshipsObservation
from facts_client.model.short_text_observation import ShortTextObservation
from facts_client.model.website_observation import WebsiteObservation
from static_common.enums.construction import ConstructionClass
from static_common.enums.license_type import LicenseType
from static_common.enums.source_types import SourceTypeID


class MockedFactsV1ApiClientInternalApi:
    def get_fact_subtype(self, fact_subtype_id: str) -> FactSubtype | None:
        if fact_subtype_id == ARU_PRIMARY_NAICS_CODE:
            return FactSubtype(display_name=f"ARU {fact_subtype_id} display name")
        return None

    def get_naics_code(self, naics_code: str) -> NaicsCode | None:
        if naics_code == ARU_PRIMARY_NAICS_CODE:
            return NaicsCode(description=f"ARU {naics_code} description")
        return None


class MockedFactsV1ApiClient:
    def __init__(self, custom_params: dict | None = None):
        self.api = MockedFactsV1ApiClientInternalApi()
        self.custom_params = custom_params or {}

    def get_facts(self, **kwargs) -> list[Fact]:
        parent_type = kwargs.get("parent_type")
        fact_subtype_ids = kwargs.get("fact_subtype_ids", [])
        if kwargs.get("call_origin") == "paragon_data.report-serializer":
            if parent_type == "PREMISES":
                return self._get_paragon_premises_facts(**kwargs)
            elif parent_type == "BUSINESS":
                return self._get_paragon_business_facts(**kwargs)
            elif parent_type == "STRUCTURE":
                return self._get_paragon_structure_facts(**kwargs)
            elif parent_type == "SUBMISSION" and "IS_GARDEN_HABITATIONAL" in fact_subtype_ids:
                return self._get_paragon_submission_facts(self.custom_params.get("is_garden", False), **kwargs)
        if kwargs.get("call_origin") in [
            "aru_auto_test.report-serializer",
            "aru_auto_test_no_structure_facts.report-serializer",
        ]:
            if parent_type == "PREMISES":
                return self._get_aru_premises_facts(**kwargs)
            elif parent_type == "BUSINESS":
                return self._get_aru_business_facts(**kwargs)
            elif parent_type == "STRUCTURE":
                return self._get_aru_structure_facts(**kwargs)
            elif parent_type == "SUBMISSION":
                if fact_subtype_ids == ["EQUIPMENT"]:
                    return self._get_aru_equipment_relations_facts(**kwargs)
            elif parent_type == "EQUIPMENT":
                return self._get_aru_equipment_facts(**kwargs)
        return []

    def get_all_related_facts(self, **kwargs) -> list[Fact]:
        parent_type = kwargs.get("parent_type")
        fact_subtype_id = kwargs.get("fact_subtype_id", None)
        submission_id = kwargs.get("submission_id", None)
        if submission_id and submission_id == ARU_SUBMISSION_ID:
            if parent_type == "SUBMISSION":
                if fact_subtype_id == "EQUIPMENT":
                    return self._get_aru_equipment_custom_facts(**kwargs)
        return []

    def _get_paragon_premises_facts(self, **kwargs):
        fact_subtype_ids = kwargs.get("fact_subtype_ids", [])
        parent_ids = kwargs.get("parent_ids", [])
        facts = []
        if "NAICS_CODE" in fact_subtype_ids:
            for parent_id in parent_ids:
                facts.append(
                    Fact(
                        fact_subtype=FactSubtype(id="NAICS_CODE", fact_type_id="NAICS_CODE"),
                        parent_id=str(parent_id),
                        parent_type="PREMISES",
                        observation=ShortTextObservation(
                            value="123456",
                            fact_type_id="NAICS_CODE",
                            source=_create_first_party_source("123456"),
                        ),
                    )
                )
        elif "STRUCTURES" in fact_subtype_ids:
            for parent_id in parent_ids:
                par_id = str(parent_id)
                if par_id not in PARAGON_PREMISES_TO_STRUCTURES:
                    continue

                structure_1_id = UUID(PARAGON_PREMISES_TO_STRUCTURES[par_id])
                structure_2_id = UUID(int=structure_1_id.int + 1)
                structures = [
                    Relationship(remote_id=str(structure_1_id), display_name="Structure 1"),
                    Relationship(remote_id=str(structure_2_id), display_name="Structure 2"),
                ]
                facts.append(
                    _create_relationships_fact(
                        fact_subtype_id="STRUCTURES",
                        parent_id=par_id,
                        parent_type="PREMISES",
                        children_type="STRUCTURE",
                        children=structures,
                    )
                )
        else:
            for parent_id in parent_ids:
                facts.extend(
                    [
                        Fact(
                            fact_subtype=FactSubtype(id="TIV", fact_type_id="MEASUREMENT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=MeasurementObservation(number=500.0, units="USD", fact_type_id="MEASUREMENT"),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="ADJUSTED_TIV", fact_type_id="MEASUREMENT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=MeasurementObservation(number=600.0, units="USD", fact_type_id="MEASUREMENT"),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="BUILDING_VALUE", fact_type_id="MEASUREMENT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=MeasurementObservation(number=400.0, units="USD", fact_type_id="MEASUREMENT"),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="BUILDING_SIZE", fact_type_id="MEASUREMENT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=MeasurementObservation(
                                number=1000.0, units="SQ_FT", fact_type_id="MEASUREMENT"
                            ),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="BUSINESS_INCOME", fact_type_id="MEASUREMENT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=MeasurementObservation(number=1560.0, units="USD", fact_type_id="MEASUREMENT"),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="OTHER_VALUE_TIV", fact_type_id="FLOAT_VALUE"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=FloatValueObservation(value=123.0, fact_type_id="FLOAT_VALUE"),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="CONTENT_VALUE", fact_type_id="MEASUREMENT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=MeasurementObservation(number=188.0, units="USD", fact_type_id="MEASUREMENT"),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="NUMBER_OF_STORIES", fact_type_id="NUMBER_OF_STORIES"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=NumberOfStoriesObservation(
                                value="2.0",
                                n_stories=2.0,
                                n_basement_stories=1.0,
                                n_attic_stories=1.0,
                                fact_type_id="NUMBER_OF_STORIES",
                            ),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="YEAR_BUILT", fact_type_id="DATETIME"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=DatetimeObservation(
                                second=0, minute=0, hour=0, day=1, month=1, year=2000, fact_type_id="DATETIME"
                            ),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="OCCUPANCY", fact_type_id="SHORT_TEXT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=ShortTextObservation(
                                value="Apartments- Podium",
                                fact_type_id="SHORT_TEXT",
                                source=_create_first_party_source("Farm"),
                            ),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="CONSTRUCTION_CLASS", fact_type_id="CONSTRUCTION_CLASS"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=ConstructionClassObservation(
                                construction_class="Class 2 – Joisted Masonry",  # noqa
                                fact_type_id="CONSTRUCTION_CLASS",
                                source=_create_first_party_source("Joisted Masonry"),
                            ),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="ISO_FIRE_PROTECTION_CLASS", fact_type_id="INTEGER"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=IntegerObservation(
                                value=2, number=2.0, units="ISO_CLASS", fact_type_id="INTEGER"
                            ),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="NUMBER_OF_BUILDINGS", fact_type_id="INTEGER"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=IntegerObservation(
                                value=10, number=10.0, units="NUMBER", fact_type_id="INTEGER"
                            ),
                        ),
                    ]
                )
        return facts

    def _get_paragon_business_facts(self, **kwargs):
        fact_subtype_ids = kwargs.get("fact_subtype_ids", [])
        parent_ids = kwargs.get("parent_ids", [])
        facts = []
        if "NAICS_CODE" in fact_subtype_ids:
            for parent_id in parent_ids:
                facts.append(
                    Fact(
                        fact_subtype=FactSubtype(id="NAICS_CODE", fact_type_id="NAICS_CODE"),
                        parent_id=str(parent_id),
                        parent_type="BUSINESS",
                        observation=ShortTextObservation(
                            value="123456",
                            fact_type_id="NAICS_CODE",
                            source=_create_first_party_source("123456"),
                        ),
                    )
                )
        return facts

    def _get_paragon_submission_facts(self, is_garden: bool, **kwargs):
        submission_id = kwargs.get("submission_id", [])
        observation = ClassificationObservation(
            fact_type_id="BINARY_CLASSIFICATION",
            fact_subtype_id="IS_GARDEN_HABITATIONAL",
            class_probabilities=[
                ClassProbability(class_id="YES", probability=0.8),
                ClassProbability(class_id="NO", probability=0.2),
            ],
        )

        setattr(observation, "predicted_class_id", "YES" if is_garden else "NO")
        setattr(observation, "probability_interpretation", is_garden)

        facts = [
            Fact(
                fact_subtype=FactSubtype(id="IS_GARDEN_HABITATIONAL", fact_type_id="BINARY_CLASSIFICATION"),
                parent_id=str(submission_id),
                parent_type="SUBMISSION",
                observation=observation,
            )
        ]

        return facts

    def _get_paragon_structure_facts(self, **kwargs):
        parent_ids = kwargs.get("parent_ids", [])
        facts = []
        for parent_id in parent_ids:
            facts.extend(
                [
                    Fact(
                        fact_subtype=FactSubtype(id="TIV", fact_type_id="MEASUREMENT"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=MeasurementObservation(number=500.0, units="USD", fact_type_id="MEASUREMENT"),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="ADJUSTED_TIV", fact_type_id="MEASUREMENT"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=MeasurementObservation(number=600.0, units="USD", fact_type_id="MEASUREMENT"),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="BUILDING_VALUE", fact_type_id="MEASUREMENT"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=MeasurementObservation(number=400.0, units="USD", fact_type_id="MEASUREMENT"),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="BUILDING_SIZE", fact_type_id="MEASUREMENT"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=MeasurementObservation(number=1000.0, units="SQ_FT", fact_type_id="MEASUREMENT"),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="BUSINESS_INCOME", fact_type_id="MEASUREMENT"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=MeasurementObservation(number=1560.0, units="USD", fact_type_id="MEASUREMENT"),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="OTHER_VALUE_TIV", fact_type_id="FLOAT_VALUE"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=FloatValueObservation(value=123.0, fact_type_id="FLOAT_VALUE"),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="CONTENT_VALUE", fact_type_id="MEASUREMENT"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=MeasurementObservation(number=188.0, units="USD", fact_type_id="MEASUREMENT"),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="NUMBER_OF_STORIES", fact_type_id="NUMBER_OF_STORIES"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=NumberOfStoriesObservation(
                            value="2.0",
                            n_stories=2.0,
                            n_basement_stories=1.0,
                            n_attic_stories=1.0,
                            fact_type_id="NUMBER_OF_STORIES",
                        ),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="YEAR_BUILT", fact_type_id="DATETIME"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=DatetimeObservation(
                            second=0, minute=0, hour=0, day=1, month=1, year=2000, fact_type_id="DATETIME"
                        ),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="BUILDING_AGE", fact_type_id="MEASUREMENT"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=MeasurementObservation(number=3.0, units="YEARS", fact_type_id="MEASUREMENT"),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="OCCUPANCY", fact_type_id="SHORT_TEXT"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=ShortTextObservation(
                            value="Apartments- Podium",
                            fact_type_id="SHORT_TEXT",
                            source=_create_first_party_source("Farm"),
                        ),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="CONSTRUCTION_CLASS", fact_type_id="CONSTRUCTION_CLASS"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=ConstructionClassObservation(
                            construction_class="Class 2 – Joisted Masonry",  # noqa
                            fact_type_id="CONSTRUCTION_CLASS",
                            source=_create_first_party_source("Joisted Masonry"),
                        ),
                    ),
                    Fact(
                        fact_subtype=FactSubtype(id="ISO_FIRE_PROTECTION_CLASS", fact_type_id="INTEGER"),
                        parent_id=str(parent_id),
                        parent_type="STRUCTURE",
                        observation=IntegerObservation(value=2, number=2.0, units="ISO_CLASS", fact_type_id="INTEGER"),
                    ),
                ]
            )
        return facts

    def _get_aru_premises_facts(self, **kwargs):
        fact_subtype_ids = kwargs.get("fact_subtype_ids", [])
        parent_ids = kwargs.get("parent_ids", [])
        facts = []

        if fact_subtype_ids == ["STRUCTURES"]:
            if kwargs.get("call_origin") == "aru_auto_test_no_structure_facts.report-serializer":
                # For testing case when we just don't have any structure relations and facts and
                # there is fallback to premise level facts
                return []
            for parent_id in parent_ids:
                par_id = str(parent_id)
                if par_id not in ARU_PREMISES_TO_STRUCTURES:
                    continue
                structure_1_id = UUID(ARU_PREMISES_TO_STRUCTURES[par_id])
                structure_2_id = UUID(int=structure_1_id.int + 1)
                structures = [
                    Relationship(remote_id=str(structure_1_id), display_name="Structure 1"),
                    Relationship(remote_id=str(structure_2_id), display_name="Structure 2"),
                ]
                facts.append(
                    _create_relationships_fact(
                        fact_subtype_id="STRUCTURES",
                        parent_id=par_id,
                        parent_type="PREMISES",
                        children_type="STRUCTURE",
                        children=structures,
                    )
                )
        else:
            for parent_id in parent_ids:
                facts.extend(
                    [
                        Fact(
                            fact_subtype=FactSubtype(id="TIV", fact_type_id="MEASUREMENT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=MeasurementObservation(number=3333.0, units="USD", fact_type_id="MEASUREMENT"),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="BUILDING_VALUE", fact_type_id="MEASUREMENT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=MeasurementObservation(number=4444.0, units="USD", fact_type_id="MEASUREMENT"),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="OCCUPANCY", fact_type_id="SHORT_TEXT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=ShortTextObservation(
                                value="Fallback Premises Occupancy",
                                fact_type_id="SHORT_TEXT",
                                source=_create_first_party_source("Farm"),
                            ),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="OCCUPANCY_CLASS", fact_type_id="SHORT_TEXT"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=ShortTextObservation(
                                value="Fallback Premises Occupancy Class",
                                fact_type_id="SHORT_TEXT",
                                source=_create_first_party_source("Farm"),
                            ),
                        ),
                        Fact(
                            fact_subtype=FactSubtype(id="CONSTRUCTION_CLASS", fact_type_id="CONSTRUCTION_CLASS"),
                            parent_id=str(parent_id),
                            parent_type="PREMISES",
                            observation=ConstructionClassObservation(
                                construction_class="Class 6 – Fire Resistive",  # noqa
                                fact_type_id="CONSTRUCTION_CLASS",
                                source=_create_first_party_source("Fallback Construction Class"),
                            ),
                        ),
                    ]
                )
        return facts

    def _get_aru_business_facts(self, **kwargs):
        fact_subtype_ids = kwargs.get("fact_subtype_ids", [])
        parent_ids = kwargs.get("parent_ids", [])
        facts = []

        if "WEBSITE" in fact_subtype_ids:
            parent_id = str(parent_ids[0])
            facts.append(
                _create_website_fact(
                    fact_subtype_id="WEBSITE", parent_id=parent_id, parent_type="BUSINESS", url="http://example.com"
                )
            )
            facts.append(
                _create_integer_fact(
                    fact_subtype_id="YEARS_IN_BUSINESS",
                    parent_id=parent_id,
                    parent_type="BUSINESS",
                    value=10,
                    units="YEARS",
                )
            )
            facts.append(
                _create_integer_fact(
                    fact_subtype_id="YEAR_FOUNDED",
                    parent_id=parent_id,
                    parent_type="BUSINESS",
                    value=2014,
                    units="YEAR",
                ),
            )
        return facts

    def _get_aru_equipment_relations_facts(self, **kwargs):
        fact_subtype_ids = kwargs.get("fact_subtype_ids", [])
        parent_ids = kwargs.get("parent_ids", [])
        facts = []

        if fact_subtype_ids == ["EQUIPMENT"]:
            for parent_id in parent_ids:
                par_id = str(parent_id)
                equipment_1_id = UUID(ARU_EQUIPMENT_1_ID)
                equipment_2_id = UUID(ARU_EQUIPMENT_2_ID)
                equipment = [
                    Relationship(remote_id=str(equipment_1_id), display_name="Equipment 1"),
                    Relationship(remote_id=str(equipment_2_id), display_name="Equipment 2"),
                ]
                facts.append(
                    _create_relationships_fact(
                        fact_subtype_id="EQUIPMENT",
                        parent_id=par_id,
                        parent_type="SUBMISSION",
                        children_type="EQUIPMENT",
                        children=equipment,
                    )
                )
        return facts

    def _get_aru_structure_facts(self, **kwargs):
        parent_ids = kwargs.get("parent_ids", [])
        facts = []
        for parent_id in parent_ids:
            par_id = str(parent_id)
            if par_id == ARU_THIRD_STRUCTURE_ID:
                # simulate lack of structure facts to test empty "structures" element
                continue
            facts.extend(
                [
                    _create_measurement_fact(
                        fact_subtype_id="TIV", parent_id=par_id, parent_type="STRUCTURE", number=500.0, units="USD"
                    ),
                    _create_measurement_fact(
                        fact_subtype_id="BUILDING_VALUE",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        number=400.0,
                        units="USD",
                    ),
                    _create_measurement_fact(
                        fact_subtype_id="BUILDING_SIZE",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        number=1000.0,
                        units="SQ_FT",
                    ),
                    _create_measurement_fact(
                        fact_subtype_id="BI_EE",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        number=123456.0,
                        units="USD",
                    ),
                    _create_measurement_fact(
                        fact_subtype_id="BUSINESS_INCOME",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        number=1560.0,
                        units="USD",
                    ),
                    _create_float_fact(
                        fact_subtype_id="OTHER_VALUE_TIV", parent_id=par_id, parent_type="STRUCTURE", value=123.0
                    ),
                    _create_measurement_fact(
                        fact_subtype_id="BPP", parent_id=par_id, parent_type="STRUCTURE", number=988.0, units="USD"
                    ),
                    _create_measurement_fact(
                        fact_subtype_id="CONTENT_VALUE",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        number=188.0,
                        units="USD",
                    ),
                    _create_number_of_stories_fact(
                        fact_subtype_id="NUMBER_OF_STORIES", parent_id=par_id, parent_type="STRUCTURE", value=2.0
                    ),
                    _create_short_text_fact(
                        fact_subtype_id="PROPERTY_DESCRIPTION",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        value="Property description",
                    ),
                    _create_short_text_fact(
                        fact_subtype_id="PREMISES_INTEREST_TYPE",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        value="Owner",
                    ),
                    _create_datetime_fact(
                        fact_subtype_id="YEAR_BUILT",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        second=0,
                        minute=0,
                        hour=0,
                        day=1,
                        month=1,
                        year=2000,
                    ),
                    _create_measurement_fact(
                        fact_subtype_id="BUILDING_AGE",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        number=3.0,
                        units="YEARS",
                    ),
                    _create_short_text_fact(
                        fact_subtype_id="OCCUPANCY_TYPE",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        value="Farm",
                    ),
                    _create_short_text_fact(
                        fact_subtype_id="OCCUPANCY",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        value="Warehouse",
                    ),
                    _create_short_text_fact(
                        fact_subtype_id="OCCUPANCY_CLASS",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        value="Pet Food Manufacturing",
                    ),
                    _create_construction_class_fact(
                        fact_subtype_id="CONSTRUCTION_CLASS",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        construction_class=ConstructionClass.JOISTED_MASONRY,
                    ),
                    _create_yes_no_binary_classification_fact(
                        fact_subtype_id="HAS_SPRINKLERS", parent_id=par_id, parent_type="STRUCTURE", yes=True
                    ),
                    _create_measurement_fact(
                        fact_subtype_id="INVENTORY_VALUE",
                        parent_id=par_id,
                        parent_type="STRUCTURE",
                        number=5000000.0,
                        units="USD",
                    ),
                ]
            )
        return facts

    def _get_aru_equipment_facts(self, **kwargs):
        parent_ids = kwargs.get("parent_ids", [])
        facts = []
        for parent_id in parent_ids:
            par_id = str(parent_id)
            facts.extend(
                [
                    _create_short_text_fact(
                        fact_subtype_id="EQUIPMENT_SERIAL_NUMBER",
                        parent_id=par_id,
                        parent_type="EQUIPMENT",
                        value=f"Equipment serial number for {par_id}",
                    ),
                    _create_measurement_fact(
                        fact_subtype_id="EQUIPMENT_LIMIT",
                        parent_id=par_id,
                        parent_type="EQUIPMENT",
                        number=256.0,
                        units="USD",
                    ),
                    _create_short_text_fact(
                        fact_subtype_id="EQUIPMENT_DESCRIPTION",
                        parent_id=par_id,
                        parent_type="EQUIPMENT",
                        value=f"Equipment description for {par_id}",
                    ),
                    _create_integer_fact(
                        fact_subtype_id="EQUIPMENT_YEAR",
                        parent_id=par_id,
                        parent_type="EQUIPMENT",
                        value=2,
                        units="YEARS",
                    ),
                    _create_short_text_fact(
                        fact_subtype_id="EQUIPMENT_MANUFACTURER",
                        parent_id=par_id,
                        parent_type="EQUIPMENT",
                        value=f"Equipment manufacturer for {par_id}",
                    ),
                    _create_short_text_fact(
                        fact_subtype_id="EQUIPMENT_MODEL",
                        parent_id=par_id,
                        parent_type="EQUIPMENT",
                        value=f"Equipment model for {par_id}",
                    ),
                    _create_short_text_fact(
                        fact_subtype_id="EQUIPMENT_GARAGE",
                        parent_id=par_id,
                        parent_type="EQUIPMENT",
                        value=f"Equipment garage for {par_id}",
                    ),
                ]
            )
        return facts

    def _get_aru_equipment_custom_facts(self, **kwargs):
        submission_id = str(kwargs.get("parent_id"))
        facts = [
            _create_measurement_fact(
                fact_subtype_id=f"AMOUNT_EQUIPMENT_{submission_id}",
                parent_id=ARU_EQUIPMENT_1_ID,
                parent_type="EQUIPMENT",
                number=19995.0,
                units="USD",
            ),
            _create_measurement_fact(
                fact_subtype_id=f"AMOUNT_EQUIPMENT_{submission_id}",
                parent_id=ARU_EQUIPMENT_2_ID,
                parent_type="EQUIPMENT",
                number=62334.0,
                units="USD",
            ),
        ]
        return facts

    def _get_paragon_licenses(self, **kwargs) -> list[License]:
        return [
            License(
                id="00000000-0000-0000-0000-000000000001",
                license_type=LicenseType.LIQUOR_LICENSE.value,
                parent_id=PARAGON_FNI_BUSINESS_ID,
                status="Active",
                body="License 1",
                document_type_id="LICENSE",
                state="CA",
                number="**********",
                date_issued=datetime(2021, 1, 1),
                expiration_date=datetime(2021, 1, 2),
                types=["Some weird type"],
                parent_type="BUSINESS",
            ),
            License(
                id="00000000-0000-0000-0000-000000000002",
                license_type=LicenseType.LIQUOR_LICENSE.value,
                parent_id=PARAGON_FNI_BUSINESS_ID,
                status="Suspended",
                document_type_id="LICENSE",
                body="License 2",
                state="CA",
                number="**********",
                date_issued=datetime(2022, 1, 1),
                expiration_date=datetime(2022, 1, 2),
                types=["Some weird type x2"],
                parent_type="BUSINESS",
            ),
        ]

    def get_all_documents(self, **kwargs) -> list[Document]:
        parent_ids = kwargs.get("parent_ids", [])
        if ARU_FNI_BUSINESS_ID in parent_ids:
            all_docs = cast(list[Document], self._example_documents())
            if kwargs["document_type_id"] == "NEWS":
                return [doc for doc in all_docs if doc.document_type_id == "NEWS"]
            if kwargs["document_type_id"] == "LICENSE":
                return [doc for doc in all_docs if doc.document_type_id == "LICENSE"]
            return all_docs

        if PARAGON_FNI_BUSINESS_ID in parent_ids:
            return cast(list[Document], self._get_paragon_licenses())

        return []

    def _example_documents(self) -> list[News]:
        return [
            News(
                document_type_id="NEWS",
                parent_id=ARU_FNI_BUSINESS_ID,
                id="00000000-0000-0000-0000-000000000001",
                title="Example News 1",
                body="News 1",
                url="http://example.com/news/1",
                snippet="Snippet 1",
                is_relevant_proba=0.9,
                is_relevant=True,
                is_valid_type=True,
                summary="Summary 1",
                published_at="2021-01-01T00:00:00Z",
                parent_type="BUSINESS",
            ),
            News(
                document_type_id="NEWS",
                parent_id=ARU_FNI_BUSINESS_ID,
                id="00000000-0000-0000-0000-000000000002",
                title="Example News 2",
                body="News 2",
                url="http://example.com/news/2",
                snippet="Snippet 2",
                is_relevant_proba=0.8,
                is_relevant=True,
                is_valid_type=True,
                summary="Summary 2",
                published_at="2022-01-01T00:00:00Z",
                parent_type="BUSINESS",
            ),
            License(
                id="00000000-0000-0000-0000-000000000003",
                license_type=LicenseType.THC_LICENSE.value,
                parent_id=ARU_FNI_BUSINESS_ID,
                status="Active",
                body="THC License 1",
                document_type_id="LICENSE",
                state="CA",
                number="AU 123",
                date_issued=datetime(2021, 1, 1),
                expiration_date=datetime(2021, 1, 2),
                types=["Some weird type"],
                parent_type="BUSINESS",
            ),
            License(
                id="00000000-0000-0000-0000-000000000004",
                license_type=LicenseType.THC_LICENSE.value,
                parent_id=ARU_FNI_BUSINESS_ID,
                status="Active",
                document_type_id="LICENSE",
                body="THC License 2",
                state="CA",
                number="NOT STARTING WITH AU 123",
                date_issued=datetime(2022, 1, 1),
                expiration_date=datetime(2022, 1, 2),
                types=["Some weird type x2"],
                parent_type="BUSINESS",
            ),
            License(
                id="00000000-0000-0000-0000-000000000005",
                license_type=LicenseType.THC_LICENSE.value,
                parent_id=ARU_FNI_BUSINESS_ID,
                status="Suspended",
                document_type_id="LICENSE",
                body="THC License 2",
                state="CA",
                number="**********",
                date_issued=datetime(2022, 1, 1),
                expiration_date=datetime(2022, 1, 2),
                types=["Some weird type x2"],
                parent_type="BUSINESS",
            ),
        ]


def _create_first_party_source(value) -> FirstPartySource:
    return FirstPartySource(source_type_id=SourceTypeID.FIRST_PARTY, original_value=f"Original = {value}")


def _create_measurement_fact(fact_subtype_id, parent_id, parent_type, number, units):
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="MEASUREMENT"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=MeasurementObservation(number=number, units=units, fact_type_id="MEASUREMENT"),
    )


def _create_integer_fact(fact_subtype_id, parent_id, parent_type, value, units):
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="INTEGER"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=IntegerObservation(value=value, number=float(value), units=units, fact_type_id="INTEGER"),
    )


def _create_float_fact(fact_subtype_id, parent_id, parent_type, value):
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="FLOAT_VALUE"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=FloatValueObservation(value=value, fact_type_id="FLOAT_VALUE"),
    )


def _create_number_of_stories_fact(fact_subtype_id, parent_id, parent_type, value):
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="NUMBER_OF_STORIES"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=NumberOfStoriesObservation(
            value=str(value),
            n_stories=value,
            n_basement_stories=1.0,
            n_attic_stories=1.0,
            fact_type_id="NUMBER_OF_STORIES",
        ),
    )


def _create_short_text_fact(fact_subtype_id, parent_id, parent_type, value):
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="SHORT_TEXT"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=ShortTextObservation(
            value=value, fact_type_id="SHORT_TEXT", source=_create_first_party_source(value)
        ),
    )


def _create_relationships_fact(fact_subtype_id, parent_id, parent_type, children_type, children):
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="RELATIONSHIPS"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=RelationshipsObservation(
            fact_type_id="RELATIONSHIPS", children_type=children_type, children=children
        ),
    )


def _create_website_fact(fact_subtype_id, parent_id, parent_type, url):
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="WEBSITE"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=WebsiteObservation(url=url, fact_type_id="WEBSITE"),
    )


def _create_datetime_fact(fact_subtype_id, parent_id, parent_type, second, minute, hour, day, month, year):
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="DATETIME"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=DatetimeObservation(
            second=second, minute=minute, hour=hour, day=day, month=month, year=year, fact_type_id="DATETIME"
        ),
    )


def _create_construction_class_fact(fact_subtype_id, parent_id, parent_type, construction_class):
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="CONSTRUCTION_CLASS"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=ConstructionClassObservation(
            construction_class=construction_class,
            fact_type_id="CONSTRUCTION_CLASS",
            source=_create_first_party_source(construction_class),
        ),
    )


def _create_yes_no_binary_classification_fact(fact_subtype_id, parent_id, parent_type, yes):
    class_probabilities = [
        ClassProbability(class_id="YES", probability=1.0 if yes else 0.0),
        ClassProbability(class_id="NO", probability=0.0 if yes else 1.0),
    ]
    observation = ClassificationObservation(
        fact_type_id="BINARY_CLASSIFICATION",
        fact_subtype_id=fact_subtype_id,
        class_probabilities=class_probabilities,
    )
    setattr(observation, "predicted_class_id", "YES" if yes else "NO")
    setattr(observation, "probability_interpretation", "Yes" if yes else "No")
    return Fact(
        fact_subtype=FactSubtype(id=fact_subtype_id, fact_type_id="BINARY_CLASSIFICATION"),
        parent_id=parent_id,
        parent_type=parent_type,
        observation=observation,
    )
