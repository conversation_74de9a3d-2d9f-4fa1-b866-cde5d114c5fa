from test.report_serializer.consts import CONIFER_ORGANIZATION_ID, CONIFER_SUBMISSION_ID
from test.report_serializer.mocks import (
    create_mocked_copilot_v3_api_client,
    create_mocked_ers_v3_api_client,
    create_mocked_facts_v1_api_client,
    create_mocked_facts_v2_api_client,
)
import difflib
import json

from report_serializer.config.config import SerializerConfig
from report_serializer.config.kalepa_api_clients import KalepaApiClients
from report_serializer.config.orgs.conifer_finys import CONIFER_FINYS_SERIALIZER_CONFIG
from report_serializer.results.result_formats import SerializationResultFormat
from report_serializer.serializer import serialize_report


def test_confier_finys_json_serializer():
    submission_id = CONIFER_SUBMISSION_ID
    conifer_config = _prepare_conifer_config(submission_id=submission_id, organization_id=CONIFER_ORGANIZATION_ID)
    results = serialize_report(conifer_config, call_origin="conifer_auto_test")

    assert results
    assert results[0].format == SerializationResultFormat.DICT
    assert results[0].result_handle

    json_result = json.dumps(results[0].result_handle, indent=4)
    expected_json = """
{
  "AgencyCode": "Agency Code",
  "AgentCode": "Agent Code",
  "BusinessType": "VALUE_LLC",
  "Carrier": "VALUE_Palomar_Excess_and_Surplus_Insurance_Company",
  "CorprateWebAddress": null,
  "EffectiveDate": "2025-01-01T00:00:00",
  "HasGL": "VALUE_No",
  "HasML": "VALUE_No",
  "HasMP": "VALUE_No",
  "HasPL": "VALUE_Yes",
  "HasPR": "VALUE_Yes",
  "InternalContact": "VALUE_NULL",
  "Locations": [
    {
      "Address": {
        "City": "New York",
        "County": "New York",
        "LineOne": "123 Main St",
        "LineTwo": "Suite 100",
        "State": "VALUE_New_York",
        "Zipcode": "10001"
      },
      "AgencyNameCode": null,
      "ConstructionType": "VALUE_NULL",
      "DBA": "FNI Public Verified DBA Name",
      "HasGL": "VALUE_No",
      "HasML": "VALUE_No",
      "HasMP": "VALUE_No",
      "HasPL": "VALUE_Yes",
      "HasPR": "VALUE_Yes",
      "OperationState": "VALUE_NULL",
      "THCPRLocations": [
        {
          "THCPRLocationItems": [
            {
              "AutomaticCentralFireAlarm": "VALUE_NULL",
              "BIAmount": "VALUE_NULL",
              "BPPConstructionType": "VALUE_NULL",
              "BPPLimit": "VALUE_NULL",
              "BPPProtectionClass": "VALUE_NULL",
              "BPPTotalSQFootageBuilding": "VALUE_NULL",
              "BuildingConstructionType": "VALUE_NULL",
              "BuildingLimit": "VALUE_NULL",
              "BuildingProtectionClass": "VALUE_NULL",
              "BuildingSQFootageBuilding": "VALUE_NULL",
              "BuildingYearBuilt": "VALUE_NULL",
              "FinishedStockAmount": "VALUE_NULL",
              "IAndBAmount": "VALUE_NULL",
              "IAndBConstructionType": "VALUE_NULL",
              "IAndBProtectionClass": "VALUE_NULL",
              "IAndBTotalSQFootageBuilding": "VALUE_NULL",
              "IntruderAlarm": "VALUE_NULL"
            }
          ]
        }
      ],
      "THCPLLocations": [
        {
          "THCPLLocationItems": [
            {
              "ApprovedMedical": 1,
              "ApprovedRecreational": 1,
              "THCPLLocationItemLicenses": [
                {
                  "LicenseNumber": "AU 123",
                  "Type": "VALUE_Recreational"
                },
                {
                  "LicenseNumber": "NOT STARTING WITH AU 123",
                  "Type": "VALUE_Medical"
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "Address": {
        "City": null,
        "County": "New York",
        "LineOne": "124 Main St",
        "LineTwo": "Suite 101",
        "State": "VALUE_New_York",
        "Zipcode": "10001"
      },
      "AgencyNameCode": null,
      "ConstructionType": "VALUE_NULL",
      "DBA": "ONI Public Verified DBA Name",
      "HasGL": "VALUE_No",
      "HasML": "VALUE_No",
      "HasMP": "VALUE_No",
      "HasPL": "VALUE_Yes",
      "HasPR": "VALUE_Yes",
      "OperationState": "VALUE_NULL",
      "THCPRLocations": [
        {
          "THCPRLocationItems": [
            {
              "AutomaticCentralFireAlarm": "VALUE_NULL",
              "BIAmount": "VALUE_NULL",
              "BPPConstructionType": "VALUE_NULL",
              "BPPLimit": "VALUE_NULL",
              "BPPProtectionClass": "VALUE_NULL",
              "BPPTotalSQFootageBuilding": "VALUE_NULL",
              "BuildingConstructionType": "VALUE_NULL",
              "BuildingLimit": "VALUE_NULL",
              "BuildingProtectionClass": "VALUE_NULL",
              "BuildingSQFootageBuilding": "VALUE_NULL",
              "BuildingYearBuilt": "VALUE_NULL",
              "FinishedStockAmount": "VALUE_NULL",
              "IAndBAmount": "VALUE_NULL",
              "IAndBConstructionType": "VALUE_NULL",
              "IAndBProtectionClass": "VALUE_NULL",
              "IAndBTotalSQFootageBuilding": "VALUE_NULL",
              "IntruderAlarm": "VALUE_NULL"
            }
          ]
        }
      ],
      "THCPLLocations": [
        {
          "THCPLLocationItems": [
            {
              "ApprovedMedical": 0,
              "ApprovedRecreational": 0
            }
          ]
        }
      ]
    },
    {
      "Address": {
        "City": "New York",
        "County": "New York",
        "LineOne": "125 Main St",
        "LineTwo": "Suite 102",
        "State": "VALUE_New_York",
        "Zipcode": "10001"
      },
      "AgencyNameCode": null,
      "ConstructionType": "VALUE_NULL",
      "DBA": null,
      "HasGL": "VALUE_No",
      "HasML": "VALUE_No",
      "HasMP": "VALUE_No",
      "HasPL": "VALUE_Yes",
      "HasPR": "VALUE_Yes",
      "OperationState": "VALUE_NULL",
      "THCPRLocations": [
        {
          "THCPRLocationItems": [
            {
              "AutomaticCentralFireAlarm": "VALUE_NULL",
              "BIAmount": "VALUE_NULL",
              "BPPConstructionType": "VALUE_NULL",
              "BPPLimit": "VALUE_NULL",
              "BPPProtectionClass": "VALUE_NULL",
              "BPPTotalSQFootageBuilding": "VALUE_NULL",
              "BuildingConstructionType": "VALUE_NULL",
              "BuildingLimit": "VALUE_NULL",
              "BuildingProtectionClass": "VALUE_NULL",
              "BuildingSQFootageBuilding": "VALUE_NULL",
              "BuildingYearBuilt": "VALUE_NULL",
              "FinishedStockAmount": "VALUE_NULL",
              "IAndBAmount": "VALUE_NULL",
              "IAndBConstructionType": "VALUE_NULL",
              "IAndBProtectionClass": "VALUE_NULL",
              "IAndBTotalSQFootageBuilding": "VALUE_NULL",
              "IntruderAlarm": "VALUE_NULL"
            }
          ]
        }
      ],
      "THCPLLocations": [
        {
          "THCPLLocationItems": [
            {
              "ApprovedMedical": 0,
              "ApprovedRecreational": 0
            }
          ]
        }
      ]
    }
  ],
  "MailingAddress": {
    "City": "New York",
    "County": "New York",
    "LineOne": "123 Main St",
    "LineTwo": "Suite 100",
    "State": "VALUE_New_York",
    "Zipcode": "10001"
  },
  "PrimaryContact": {
    "DBA": "FNI Public Verified DBA Name",
    "NameLast": "FNI Public Verified DBA Name",
    "PhoneBusiness": "**********",
    "EmailAddress": "<EMAIL>"
  },
  "THCPRPolicy": {
    "AreLimitsSamePerLocation": "VALUE_NULL"
  },
  "THCPLPolicy": {
    "AreLimitsSamePerLocation": "VALUE_NULL"
  },
  "UnderwritingStatus": "VALUE_READY_TO_QUOTE"
}
    """

    _assert_conifer_json(expected_json, json_result)


def _assert_conifer_json(expected_json, json_result):
    # Parse JSON strings into Python objects
    json_result_obj = json.loads(json_result)
    expected_json_obj = json.loads(expected_json)
    # Serialize back to JSON strings with sorted keys and indentation
    json_result_str = json.dumps(json_result_obj, sort_keys=True, indent=4)
    expected_json_str = json.dumps(expected_json_obj, sort_keys=True, indent=4)
    # Compute and print the diff
    diff = difflib.unified_diff(
        expected_json_str.splitlines(),
        json_result_str.splitlines(),
        fromfile="expected_json",
        tofile="json_result",
        lineterm="",
    )
    if diff:
        diff_str = "\n".join(diff)
        if diff_str.strip():
            print("###########################")
            print("Assertion failed with diff:")
            print("###########################")
            print(diff_str)
    assert json_result_obj == expected_json_obj


def _prepare_conifer_config(submission_id: str, organization_id: int) -> SerializerConfig:
    conifer_config = CONIFER_FINYS_SERIALIZER_CONFIG.as_template()

    conifer_config.serialization_main_config.organization_id = organization_id
    conifer_config.serialization_main_config.submission_id = submission_id

    conifer_config.kalepa_api_clients = KalepaApiClients(
        copilot_v3=create_mocked_copilot_v3_api_client(),
        ers_v3=create_mocked_ers_v3_api_client(),
        facts_v1=create_mocked_facts_v1_api_client(),
        facts_v2=create_mocked_facts_v2_api_client(),
    )

    return conifer_config
