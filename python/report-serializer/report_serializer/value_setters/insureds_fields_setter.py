from abc import ABC, abstractmethod
from typing import Generic, TypeVar
from uuid import UUID

from common.logic.entity_resolution.entity import get_primary_premises
from copilot_client_v3 import Submission
from entity_resolution_service_client_v3 import Default<PERSON>pi as ErsV3ApiClient
from entity_resolution_service_client_v3 import Entity, EntityName
from static_common.enums.entity import EntityNameType, EntityPremisesType
from static_common.enums.external import ExternalIdentifierType
from static_common.enums.submission import SubmissionStage
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured

from report_serializer.config.insureds_config import InsuredNameType, InsuredsConfig
from report_serializer.openapi_utils.ers_api import (
    get_entities_by_ids,
    get_snapshots_by_ids,
)
from report_serializer.results.models.insured import Insured
from report_serializer.results.models.report_repr import ReportRepr
from report_serializer.utils import (
    capitalize_first_letter_preserve_rest,
    try_to_determine_business_type,
)

S = TypeVar("S")


class InsuredsFieldsSetter(ABC, Generic[S]):
    @abstractmethod
    def set_basics(self, submission: S, report_repr: ReportRepr) -> None:
        pass

    def set_premisses_addresses(self, submission: S, report_repr: ReportRepr) -> None:
        pass

    def set_business_types(self, submission: S, report_repr: ReportRepr) -> None:
        pass


class OpenApiInsuredsFieldsSetter(InsuredsFieldsSetter[Submission]):
    def __init__(self, insureds_config: InsuredsConfig, ers_v3_api: ErsV3ApiClient, call_origin: str):
        self.insureds_config = insureds_config
        self.ers_v3_api = ers_v3_api
        self.call_origin = call_origin
        self.entities: dict[UUID, Entity] = {}

    def _update_snapshot_data(self, report_repr: ReportRepr, submission: Submission) -> None:
        """
        When the submission if frozen, the Entities are not taken directly from GET entities call, but we rather use
        snapshots of the entities' states from the moment the submission was frozen.
        """
        if not report_repr.stage or not SubmissionStage.is_frozen(report_repr.stage):
            return

        snapshot_ids = {sb.ers_snapshot_id for sb in submission.businesses if sb.ers_snapshot_id}

        snapshots = get_snapshots_by_ids(ers_v3_api=self.ers_v3_api, snapshot_ids=list(snapshot_ids))
        for snapshot in snapshots:
            entity_id = UUID(snapshot.entity_id)
            self.entities[entity_id] = snapshot.entity

    def set_basics(self, submission: Submission, report_repr: ReportRepr) -> None:
        if not self.insureds_config.set_basic_properties:
            return

        if not self.ers_v3_api:
            raise RuntimeError("ErsV3ApiClient is not set. Make sure it is set in constructor.")

        rr = report_repr

        rr.insureds = []
        for sub_business in submission.businesses:
            is_fni = sub_business.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
            is_oni = sub_business.named_insured == SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED
            insured_initial_name = sub_business.requested_name

            insured = Insured(
                business_id=sub_business.business_id,
                is_fni=is_fni,
                is_oni=is_oni,
                hide=sub_business.hide,
                hide_property_facts=sub_business.hide_property_facts,
                name=insured_initial_name,
                requested_name=insured_initial_name,
                requested_address=sub_business.requested_address,
                report_link=rr,
                named_insured=sub_business.named_insured,
                entity_role=sub_business.entity_role,
            )
            rr.insureds.append(insured)

        # Fetch entities by IDs
        self.entities = get_entities_by_ids(self.ers_v3_api, rr.insured_ids, call_origin=self.call_origin)

        self._update_snapshot_data(report_repr, submission)
        self._update_insured_names(report_repr)
        self._update_insured_details_from_entity(report_repr)

    def _update_insured_names(self, report_repr: ReportRepr) -> None:
        insured_dict = report_repr.insured_dict
        for business_id in report_repr.insured_ids:
            insured = insured_dict[business_id]
            entity = self.entities[UUID(business_id)]
            entity_names: list[EntityName] = entity.names
            if not entity_names:
                continue

            # Filter to only those that are public + verified
            verified_public_names = [n for n in entity_names if n.is_public and n.is_verified]

            legal_name = next((n.value for n in verified_public_names if n.type == EntityNameType.LEGAL_NAME), None)
            dba_name = next((n.value for n in verified_public_names if n.type == EntityNameType.DBA_NAME), None)
            requested_name = insured.requested_name
            public_verified_name = verified_public_names[0].value if verified_public_names else None

            all_names = {
                InsuredNameType.REQUESTED: requested_name,
                InsuredNameType.DBA: capitalize_first_letter_preserve_rest(dba_name) if dba_name else None,
                InsuredNameType.LEGAL: capitalize_first_letter_preserve_rest(legal_name) if legal_name else None,
                InsuredNameType.PUBLIC_VERIFIED: (
                    capitalize_first_letter_preserve_rest(public_verified_name) if public_verified_name else None
                ),
            }

            name_to_set = None
            for name_type in self.insureds_config.name_type_priority:
                name_to_set = all_names.get(name_type)
                if name_to_set:
                    break

            if name_to_set:
                insured.name = name_to_set

            insured.dba_name = all_names[InsuredNameType.DBA]

    def _update_insured_details_from_entity(self, report_repr: ReportRepr) -> None:
        insured_dict = report_repr.insured_dict
        for business_id in report_repr.insured_ids:
            insured = insured_dict[business_id]
            entity = self.entities[UUID(business_id)]
            identifiers = entity.identifiers or []
            for identifier in identifiers:
                match identifier.type:
                    case ExternalIdentifierType.PHONE.value:
                        insured.phone_number = identifier.value
                    case ExternalIdentifierType.EMAIL.value:
                        insured.email = identifier.value

    def set_premisses_addresses(self, submission: S, report_repr: ReportRepr) -> None:
        if not self.insureds_config.set_premisses_addresses:
            return
        if not self.insureds_config.set_basic_properties:
            raise RuntimeError("set_basic_properties must be True to set_premisses_addresses.")

        insured_dict = report_repr.insured_dict
        for business_id in report_repr.insured_ids:
            entity = self.entities[UUID(business_id)]
            if not entity.premises:
                continue
            insured = insured_dict[business_id]

            # Grab details of primary premises
            primary_premises = get_primary_premises(entity)
            if primary_premises.premises:
                pp = primary_premises.premises
                insured.primary_address_line_1 = pp.address_line_1
                insured.primary_address_line_2 = pp.address_line_2
                insured.primary_city = pp.city
                insured.primary_state = pp.state
                insured.primary_zip = pp.postal_code
                insured.primary_county = pp.county
                insured.latitude = pp.latitude
                insured.longitude = pp.longitude
                insured.primary_formatted_address = pp.formatted_address

            # Set initial premises with their IDs
            for entity_premises in entity.premises:
                premises = entity_premises.premises
                premises_repr = insured.get_or_create_premise_by_id(premises.id)
                premises_repr.is_primary = primary_premises.premises and (premises.id == primary_premises.premises.id)
                premises_repr.premises_type = entity_premises.type
                premises_repr.address_line_1 = premises.address_line_1
                premises_repr.address_line_2 = premises.address_line_2
                premises_repr.city = premises.city
                premises_repr.state = premises.state
                premises_repr.zip = premises.postal_code
                premises_repr.county = premises.county
                premises_repr.formatted_address = premises.formatted_address
                if entity_premises.type == EntityPremisesType.MAILING_ADDRESS:
                    insured.mailing_address_line_1 = premises.address_line_1
                    insured.mailing_address_line_2 = premises.address_line_2
                    insured.mailing_city = premises.city
                    insured.mailing_state = premises.state
                    insured.mailing_zip = premises.postal_code
                    insured.mailing_county = premises.county
                    insured.mailing_formatted_address = premises.formatted_address

    def set_business_types(self, submission: S, report_repr: ReportRepr) -> None:
        if not self.insureds_config.set_basic_properties:
            raise RuntimeError("set_basic_properties must be True to set_business_types.")

        insured_dict = report_repr.insured_dict
        for business_id in report_repr.insured_ids:
            entity = self.entities[UUID(business_id)]
            insured = insured_dict[business_id]

            detected_business_type = try_to_determine_business_type(
                insured_requested_name=insured.requested_name,
                ers_business_type=entity.business_type,
                normalize_insured_name_before_check=True,
            )
            insured.business_type = detected_business_type
