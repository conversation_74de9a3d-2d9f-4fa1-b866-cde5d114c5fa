from dataclasses import dataclass, field
from typing import TYPE_CHECKING, final
from uuid import UUID

from static_common.enums.business import BusinessType
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.parent import ParentType
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)

from report_serializer.results.models.fact_mappable import (
    FactMappable,
    FactMappableFieldValueConversionConfig,
    MultiLabelConversionType,
)
from report_serializer.results.models.license import License
from report_serializer.results.models.news import News
from report_serializer.results.models.osha import OshaInspection
from report_serializer.results.models.premise import Premise
from report_serializer.results.models.product_recall import ProductRecall

if TYPE_CHECKING:
    from report_serializer.results.models.report_repr import ReportRepr


@dataclass
@final
class Insured(FactMappable):
    report_link: "ReportRepr"

    business_id: str
    is_fni: bool
    is_oni: bool
    name: str
    dba_name: str | None = None
    business_type: BusinessType | None = None
    entity_role: SubmissionBusinessEntityRole | None = None
    named_insured: SubmissionBusinessEntityNamedInsured | None = None

    primary_address_line_1: str | None = None
    primary_address_line_2: str | None = None
    primary_city: str | None = None
    primary_state: str | None = None
    primary_zip: str | None = None
    primary_county: str | None = None
    primary_formatted_address: str | None = None

    mailing_address_line_1: str | None = None
    mailing_address_line_2: str | None = None
    mailing_city: str | None = None
    mailing_state: str | None = None
    mailing_zip: str | None = None
    mailing_county: str | None = None
    mailing_formatted_address: str | None = None

    phone_number: str | None = None
    email: str | None = None

    hide: bool | None = None
    hide_property_facts: bool | None = None

    product_recalls: list[ProductRecall] | None = None
    osha_inspections: list[OshaInspection] | None = None

    active_in_joint_ventures: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.ACTIVE_IN_JOINT_VENTURES}
    )
    audit_concerns: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.AUDIT_CONCERNS})
    aircraft_exposure: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.AIRCRAFT_EXPOSURE})
    airport_work: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_AIRPORT_WORK})
    any_named_insured_sells_to_other_named_insured: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.ANY_NAMED_INSURED_SELLS_TO_OTHER_NAMED_INSURED}
    )
    bankruptcy: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.BANKRUPTCY})
    bankruptcy_restructuring_liquidation_past_24_months: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.BANKRUPTCY_RESTRUCTURING_LIQUIDATION_PAST_24_MONTHS}
    )
    business_categories: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.BUSINESS_CATEGORIES}
    )
    business_reputation: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.BUSINESS_REPUTATION}
    )
    business_hires_drones: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.BUSINESS_HIRES_DRONES}
    )
    cooking_types: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.COOKING_TYPES})
    closing_layoffs_past_24_months: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.CLOSING_LAYOFFS_PAST_24_MONTHS}
    )
    concrete_printing: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.CONCRETE_PRINTING})
    contemplating_demolition: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.CONTEMPLATING_DEMOLITION}
    )
    contractor_project_types: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.CONTRACTOR_PROJECT_TYPES}
    )
    contractor_services: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.CONTRACTOR_SERVICES}
    )
    contractor_types: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.CONTRACTOR_TYPES})
    contemplating_structural_alterations: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.CONTEMPLATING_STRUCTURAL_ALTERATIONS}
    )
    coverage_from_advertising_agency: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.COVERAGE_FROM_ADVERTISING_AGENCY}
    )
    dangerous_materials: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.DANGEROUS_MATERIALS}
    )
    discontinued_operations: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.DISCONTINUED_OPERATIONS}
    )
    discontinued_products: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.DISCONTINUED_PRODUCTS}
    )
    employee_count: int | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.EMPLOYEE_COUNT})
    entertainment_types: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.ENTERTAINMENT_TYPES}
    )
    fein: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.FEIN})
    fraud_reported: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.FRAUD_REPORTED})
    website: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.WEBSITE})
    years_in_business: int | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.YEARS_IN_BUSINESS})
    year_founded: int | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.YEAR_FOUNDED})
    happy_hour: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAPPY_HOUR})
    has_balcony: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_BALCONY})
    has_been_placed_in_a_trust: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_BEEN_PLACED_IN_A_TRUST}
    )
    has_coverage_for_hired_and_non_owned_vehicles: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_COVERAGE_FOR_HIRED_AND_NON_OWNED_VEHICLES}
    )
    has_damages: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_DAMAGES})
    has_day_care: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_DAY_CARE})
    has_draw_plans: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_DRAW_PLANS})
    has_exposure_to_flammables: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_EXPOSURE_TO_FLAMMABLES}
    )
    has_gl_coverage_pollution_coverage_endorsement: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_GL_COVERAGE_POLLUTION_COVERAGE_ENDORSEMENT}
    )
    has_gl_coverage_with_standard_iso_pollution_exclusion: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_GL_COVERAGE_WITH_STANDARD_ISO_POLLUTION_EXCLUSION}
    )
    has_gl_coverage_with_standard_sudden_and_accidental_only: str | None = field(
        default=None,
        metadata={"fact_subtype_id": FactSubtypeID.HAS_GL_COVERAGE_WITH_STANDARD_SUDDEN_AND_ACCIDENTAL_ONLY},
    )
    has_past_losses_for_abuse_molestation_discrimination: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_PAST_LOSSES_FOR_ABUSE_MOLESTATION_DISCRIMINATION}
    )
    has_product_recalls: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_PRODUCT_RECALLS}
    )
    has_products_for_aircraft_or_space: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_PRODUCTS_FOR_AIRCRAFT_OR_SPACE}
    )
    has_products_under_label_from_others: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_PRODUCTS_UNDER_LABEL_FROM_OTHERS}
    )
    has_safety_code_violations: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_SAFETY_CODE_VIOLATIONS}
    )
    has_safety_manual: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_SAFETY_MANUAL})
    has_safety_policy: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_SAFETY_POLICY})
    has_safety_position: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_SAFETY_POSITION}
    )
    has_security_guards: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_SECURITY_GUARDS}
    )
    has_separate_pollution_coverage: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_SEPARATE_POLLUTION_COVERAGE}
    )
    has_subsidiaries: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_SUBSIDIARIES})
    has_sold_acquired_or_discontinued_operations_in_last_five_years: str | None = field(
        default=None,
        metadata={"fact_subtype_id": FactSubtypeID.HAS_SOLD_ACQUIRED_OR_DISCONTINUED_OPERATIONS_IN_LAST_FIVE_YEARS},
    )
    has_infestation: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_INFESTATION})
    has_lodging_operations: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_LODGING_OPERATIONS}
    )
    has_monthly_safety_meetings: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_MONTHLY_SAFETY_MEETINGS}
    )
    has_osha_safety_program: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_OSHA_SAFETY_PROGRAM}
    )
    has_other_businesses_not_requesting_coverage: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_OTHER_BUSINESSES_NOT_REQUESTING_COVERAGE}
    )
    has_research_and_development: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_RESEARCH_AND_DEVELOPMENT}
    )
    has_swimming_pool: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_SWIMMING_POOL})
    has_deep_fryers: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.COOKING_TYPES_DEEP_FRYERS}
    )
    has_foreign_operations_products_or_sales: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_FOREIGN_OPERATIONS_PRODUCTS_OR_SALES}
    )
    has_had_judgement_or_lien_past_five_years: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_HAD_JUDGEMENT_OR_LIEN_PAST_FIVE_YEARS}
    )
    has_hard_liquor: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_HARD_LIQUOR})
    has_vehicles_not_insured_by_underlying_policies: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_VEHICLES_NOT_INSURED_BY_UNDERLYING_POLICIES}
    )
    has_warranties_or_guarantees: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_WARRANTIES_OR_GUARANTEES}
    )
    has_watercraft_or_docks: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_WATERCRAFT_OR_DOCKS}
    )
    high_customer_concentration: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HIGH_CUSTOMER_CONCENTRATION}
    )
    hours_of_operation: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HOURS_OF_OPERATION})
    hotel_special_facilities: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.HOTEL_SPECIAL_FACILITIES}
    )
    installs_services_or_demonstrate_products: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.INSTALLS_SERVICES_OR_DEMONSTRATE_PRODUCTS}
    )
    is_subsidiary: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.IS_SUBSIDIARY})
    is_self_insured: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.IS_SELF_INSURED})
    seller_billing_disputes: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.SELLER_BILLING_DISPUTES}
    )
    sells_tobacco_or_cannabis: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.SELLS_TOBACCO_OR_CANNABIS}
    )
    has_delivery: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.HAS_DELIVERY})
    total_sales: float | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.TOTAL_SALES})
    total_sales_yoy_percentage: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TOTAL_SALES_YOY_PERCENTAGE}
    )
    latitude: float | None = None
    labor_interchange: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.LABOR_INTERCHANGE})
    leases_employees: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.LEASES_EMPLOYEES})
    leases_equipment: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.LEASES_EQUIPMENT})
    loans_or_rents_machinery: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.LOANS_OR_RENTS_MACHINERY}
    )
    lodging_operations_area: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.LODGING_OPERATIONS_AREA}
    )
    longitude: float | None = None
    manufacturing_process_types: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.MANUFACTURING_PROCESS_TYPES}
    )
    medical_facilities_or_professionals: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.MEDICAL_FACILITIES_OR_PROFESSIONALS}
    )
    naics_level_1_manufacturing_types: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.NAICS_LEVEL_1_MANUFACTURING_TYPES}
    )
    news_shooting: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.NEWS_SHOOTING})
    news_risks: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.NEWS_RISKS})
    new_venture: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.NEW_VENTURE})
    non_renewal_cancelation: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.NON_RENEWAL_CANCELATION}
    )
    on_premises_crime: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.ON_PREMISES_CRIME})
    open_status: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.OPEN_STATUS})
    payroll: float | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PAYROLL})
    past_due_accounts: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PAST_DUE_ACCOUNTS})
    parking_fees: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PARKING_FEES})
    parking_types: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PARKING_TYPES})
    policy_or_coverage_declined_canceled_past_three_years: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.POLICY_OR_COVERAGE_DECLINED_CANCELED_PAST_THREE_YEARS}
    )
    project_asbestos_other_hazardous_removal: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL}
    )
    project_below_grade: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_BELOW_GRADE}
    )
    project_blasting_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_BLASTING_WORK}
    )
    project_crane_work: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_CRANE_WORK})
    project_demolition_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_DEMOLITION_WORK}
    )
    project_depth_of_work: int | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_DEPTH_OF_WORK}
    )
    project_drilling_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_DRILLING_WORK}
    )
    project_electrical_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_ELECTRICAL_WORK}
    )
    project_excavation_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_EXCAVATION_WORK}
    )
    project_gas_line_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_GAS_LINE_WORK}
    )
    project_hvac_work: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_HVAC_WORK})
    project_mold_removal: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_MOLD_REMOVAL}
    )
    project_new_construction: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_NEW_CONSTRUCTION}
    )
    project_paving_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PAVING_WORK}
    )
    project_percentage_of_exterior_work: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PERCENTAGE_OF_EXTERIOR_WORK}
    )
    project_percentage_of_interior_work: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PERCENTAGE_OF_INTERIOR_WORK}
    )
    project_percentage_of_work_as_gc: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_GC}
    )
    project_percentage_of_work_as_subcontractor: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR}
    )
    project_percentage_of_work_commercial: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_COMMERCIAL}
    )
    project_percentage_of_work_industrial: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_INDUSTRIAL}
    )
    project_percentage_of_work_public: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_PUBLIC}
    )
    project_percentage_of_work_residential: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_RESIDENTIAL}
    )
    project_percentage_of_work_subcontracted_to_others: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS}
    )
    project_pile_driving_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PILE_DRIVING_WORK}
    )
    project_plumbing_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_PLUMBING_WORK}
    )
    project_roof_work: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_ROOF_WORK})
    project_remodeling_or_repair: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_REMODELING_OR_REPAIR}
    )
    project_safety_program: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_SAFETY_PROGRAM}
    )
    project_scaffolding: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_SCAFFOLDING}
    )
    project_sewer_work: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_SEWER_WORK})
    project_siding_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_SIDING_WORK}
    )
    project_solar_work: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_SOLAR_WORK})
    project_subcontractors_cost: float | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_SUBCONTRACTORS_COST}
    )
    project_subcontractors_used: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_SUBCONTRACTORS_USED}
    )
    project_traffic_lighting_signals_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_TRAFFIC_LIGHTING_SIGNALS_WORK}
    )
    project_use_of_eifs: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_USE_OF_EIFS}
    )
    project_welding_work: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROJECT_WELDING_WORK}
    )
    promotes_premises_safety: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.PROMOTES_PREMISES_SAFETY}
    )
    repackages_products_from_others: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.REPACKAGES_PRODUCTS_FROM_OTHERS}
    )
    rents_vehicles_to_others: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.RENTS_VEHICLES_TO_OTHERS}
    )
    sponsors_athletic_teams: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.SPONSORS_ATHLETIC_TEAMS}
    )
    sponsors_social_events: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.SPONSORS_SOCIAL_EVENTS}
    )
    subcontractors_without_insurance: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.SUBCONTRACTORS_WITHOUT_INSURANCE}
    )
    subcontractor_coverages_or_limits_less_than_named_insured: str | None = field(
        default=None,
        metadata={"fact_subtype_id": FactSubtypeID.SUBCONTRACTOR_COVERAGES_OR_LIMITS_LESS_THAN_NAMED_INSURED},
    )
    transportation_driver_fitness_violations: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TRANSPORTATION_DRIVER_FITNESS_VIOLATIONS}
    )
    transportation_drugs_and_alcohol_violations: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TRANSPORTATION_DRUGS_AND_ALCOHOL_VIOLATIONS}
    )
    transportation_hazardous_materials_violations: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TRANSPORTATION_HAZARDOUS_MATERIALS_VIOLATIONS}
    )
    transportation_legal_violations: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TRANSPORTATION_LEGAL_VIOLATIONS}
    )
    transportation_performance_benchmark_issues: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TRANSPORTATION_PERFORMANCE_BENCHMARK_ISSUES}
    )
    transportation_reported_operations_and_permits: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TRANSPORTATION_REPORTED_OPERATIONS_AND_PERMITS}
    )
    transportation_risk_of_transport_without_permission: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TRANSPORTATION_RISK_OF_TRANSPORT_WITHOUT_PERMISSION}
    )
    transportation_serious_maintenance_violations: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TRANSPORTATION_SERIOUS_MAINTENANCE_VIOLATIONS}
    )
    transportation_serious_unsafe_driving: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.TRANSPORTATION_SERIOUS_UNSAFE_DRIVING}
    )
    us_gaap_revenue: str | None = field(default=None, metadata={"fact_subtype_id": FactSubtypeID.US_GAAP_REVENUE})
    uses_advertising_agency: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.USES_ADVERTISING_AGENCY}
    )
    vendors_coverage_required: str | None = field(
        default=None, metadata={"fact_subtype_id": FactSubtypeID.VENDORS_COVERAGE_REQUIRED}
    )
    premises: list[Premise] = field(default_factory=list)
    licenses: list[License] | None = None
    news: list[News] | None = None
    requested_name: str | None = None
    requested_address: str | None = None

    naics: str | None = field(
        default=None,
        metadata={
            "fact_subtype_id": FactSubtypeID.MULTI_LABEL_NAICS_CODES,
            "conversion_config": FactMappableFieldValueConversionConfig(
                multi_label_conversion_type=MultiLabelConversionType.NAICS
            ),
        },
    )
    performance_types: str | None = field(
        default=None,
        metadata={
            "fact_subtype_id": FactSubtypeID.PERFORMANCE_TYPES,
            "conversion_config": FactMappableFieldValueConversionConfig(
                multi_label_conversion_type=MultiLabelConversionType.DEFAULT
            ),
        },
    )

    def get_or_create_premise_by_id(self, premise_id: str | UUID) -> Premise:
        for premise in self.premises:
            if premise.id == premise_id:
                return premise
        premise = Premise(id=premise_id, structures=[], insured_link=self)
        self.premises.append(premise)
        return premise

    def get_premise_by_id(self, premise_id: str | UUID) -> Premise | None:
        for premise in self.premises:
            if premise.id == premise_id:
                return premise
        return None

    def get_or_create_license_by_id(self, license_id: str | UUID) -> License:
        if not self.licenses:
            self.licenses = []

        for license in self.licenses:
            if license.id == license_id:
                return license

        license = License(id=str(license_id))
        self.licenses.append(license)
        return license

    def get_or_create_product_recall_by_id(self, product_recall_id: str | UUID) -> ProductRecall:
        if not self.product_recalls:
            self.product_recalls = []

        for product_recall in self.product_recalls:
            if product_recall.id == product_recall_id:
                return product_recall
        product_recall = ProductRecall(id=str(product_recall_id))
        self.product_recalls.append(product_recall)
        return product_recall

    def get_or_create_osha_inspection_by_id(self, osha_inspection_id: str | UUID) -> OshaInspection:
        if not self.osha_inspections:
            self.osha_inspections = []

        for osha_inspection in self.osha_inspections:
            if osha_inspection.id == osha_inspection_id:
                return osha_inspection
        osha_inspection = OshaInspection(id=str(osha_inspection_id))
        self.osha_inspections.append(osha_inspection)
        return osha_inspection

    def get_or_create_news_by_id(self, news_id: str | UUID) -> News:
        if not self.news:
            self.news = []

        for news in self.news:
            if news.id == news_id:
                return news
        news = News(id=str(news_id), insured_link=self)
        self.news.append(news)
        return news

    @property
    def premises_dict(self) -> dict[str | UUID, Premise]:
        if not self.premises:
            return {}
        return {premise.id: premise for premise in self.premises}

    @property
    def parent_type(self) -> ParentType:
        return ParentType.BUSINESS
