from collections import defaultdict
from collections.abc import Callable
from datetime import datetime
from typing import Any
from uuid import UUID
import re

from common.logic.identification.legal_entity import (
    get_business_type_from_legal_entity_name,
)
from dateutil.parser import parse
from entity_resolution_service_client_v3 import Entity
from facts_client.model.fact import Fact
from facts_client.model.time_series_observation import TimeSeriesObservation
from facts_client.model.time_series_value_observation import TimeSeriesValueObservation
from static_common.enums.business import BusinessType
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID

from report_serializer.results.models.fact_mappable import (
    FactMappableFieldValueConversionConfig,
    LetterGradeConversionType,
    MultiLabelConversionType,
)

DAY_NUM_TO_NAME = {
    1: "Sun",
    2: "Mon",
    3: "Tue",
    4: "Wed",
    5: "Thu",
    6: "Fri",
    7: "Sat",
}


def format_hours_of_operation_obs(fact: Fact, conversion_config: FactMappableFieldValueConversionConfig) -> str:
    formatted_intervals = []
    obs = fact.observation

    for interval in obs.open_intervals:
        day_idx = interval.day_of_week_index
        closes_time = interval.closes_time
        opens_time = interval.opens_time
        formatted_intervals.append(f"{DAY_NUM_TO_NAME[day_idx]}: {opens_time} - {closes_time}")

    return "\n".join(formatted_intervals)


def _get_description_for_score(score: int) -> str:
    if score < 3:
        return "Very Low"
    elif score == 3:
        return "Low"
    elif score < 7:
        return "Moderate"
    elif score < 9:
        return "High"
    else:
        return "Very High"


def extract_letter_grade_value(fact: Fact, conversion_config: FactMappableFieldValueConversionConfig) -> Any:
    grade = fact.observation.crime_grade
    score = fact.observation.crime_score
    description = _get_description_for_score(score)

    if conversion_config.letter_grade_conversion_type == LetterGradeConversionType.GRADE:
        return f"{description} (Grade {grade})"
    elif conversion_config.letter_grade_conversion_type == LetterGradeConversionType.SCORE:
        return f"{description} - {score}"


def _extract_class_description(
    fact_subtype_id: str, conversion_config: FactMappableFieldValueConversionConfig
) -> str | None:
    if not conversion_config.fact_subtype_id_to_data:
        return None

    fact_subtype = conversion_config.fact_subtype_id_to_data.get(fact_subtype_id)
    if not fact_subtype:
        return None

    if conversion_config.multi_label_conversion_type == MultiLabelConversionType.DEFAULT:
        return fact_subtype.display_name
    elif conversion_config.multi_label_conversion_type == MultiLabelConversionType.NAICS:
        naics_code = fact_subtype.id.removeprefix("NAICS_")
        return f"{naics_code} {fact_subtype.display_name}"


def extract_multi_label_value(fact: Fact, conversion_config: FactMappableFieldValueConversionConfig) -> Any:
    class_descriptions = []
    detected_classes = fact.observation.detected_classes or []
    if not detected_classes:
        return "None"

    for detected_class in detected_classes:
        class_description = _extract_class_description(detected_class.fact_subtype_id, conversion_config)
        probability_interpretation = detected_class.probability_interpretation
        if class_description:
            if probability_interpretation.lower().strip() != "yes":
                class_descriptions.append(f"{probability_interpretation} {class_description}")
            else:
                class_descriptions.append(class_description)

    return "\n".join(class_descriptions)


def extract_time_series_value(fact: Fact, conversion_config: FactMappableFieldValueConversionConfig) -> Any:
    time_series_obs: TimeSeriesObservation = fact.observation
    time_series_values: list[TimeSeriesValueObservation] = time_series_obs.time_series_values or []

    interval_order = ["CUSTOM", "MONTHLY", "QUARTERLY", "ANNUAL"]

    values_with_date: list[tuple[TimeSeriesValueObservation, datetime]] = [
        (tsv, parse(tsv.end_date)) for tsv in time_series_values if tsv.end_date and tsv.ts_value is not None
    ]

    values_with_date.sort(key=lambda x: (interval_order.index(x[0].interval), x[1]), reverse=True)

    return values_with_date[0][0].ts_value if values_with_date else None


def extract_business_categories(fact: Fact, conversion_config: FactMappableFieldValueConversionConfig) -> str | None:
    extracted_categories = []
    for business_category in fact.observation.business_categories or []:
        if business_category.is_relevant:
            extracted_categories.append(business_category.display_name)

    return "\n".join(extracted_categories) if extracted_categories else None


def extract_multiclass_classification(
    fact: Fact, conversion_config: FactMappableFieldValueConversionConfig
) -> str | None:
    predicted_class = fact.observation.predicted_class_id
    if not predicted_class:
        return None

    if not conversion_config.facts_client:
        raise ValueError("Facts client is not set in conversion config")

    classification_task = conversion_config.facts_client.get_classification_task(
        fact_subtype=FactSubtypeID[fact.fact_subtype.id]
    )
    if not classification_task:
        return None

    return next((cls.display_name for cls in classification_task.classes if cls.id == predicted_class), predicted_class)


VALUE_EXTRACTOR_MAPPINGS: dict[FactTypeID, Callable[[Fact, FactMappableFieldValueConversionConfig], Any]] = {
    FactTypeID.BINARY_CLASSIFICATION: lambda x, _: (
        x.observation.probability_interpretation if x.observation.predicted_class_id else "Unknown"
    ),
    FactTypeID.INTEGER: lambda x, _: x.observation.value,
    FactTypeID.DECIMAL: lambda x, _: x.observation.value,
    FactTypeID.FLOAT_VALUE: lambda x, _: x.observation.value,
    FactTypeID.DATETIME: lambda x, _: x.observation.year,
    FactTypeID.MEASUREMENT: lambda x, _: x.observation.number,
    FactTypeID.TAGS: lambda x, _: ", ".join([tag.display_name for tag in x.observation.tags or []]),
    FactTypeID.CONSTRUCTION_CLASS: lambda x, _: x.observation.construction_class.rsplit("-", 1)[0].strip(),
    FactTypeID.WEBSITE: lambda x, _: x.observation.url,
    FactTypeID.NUMBER_OF_STORIES: lambda x, _: x.observation.n_stories,
    FactTypeID.SHORT_TEXT: lambda x, _: x.observation.value,
    FactTypeID.ELEVATION: lambda x, _: x.observation.elevation,
    FactTypeID.DISTANCE_TO_POI: lambda x, _: x.observation.value,
    FactTypeID.FLOOD_ZONE: lambda x, _: f"Zone {x.observation.flood_zone}" if x.observation.flood_zone else None,
    FactTypeID.HOURS_OF_OPERATION: format_hours_of_operation_obs,
    FactTypeID.LETTER_GRADE: extract_letter_grade_value,
    FactTypeID.MULTI_LABEL_CLASSIFICATION: extract_multi_label_value,
    FactTypeID.FIRE_PROTECTION_CLASS: lambda x, _: x.observation.aais_code,
    FactTypeID.TIME_SERIES: extract_time_series_value,
    FactTypeID.BUSINESS_CATEGORIES: extract_business_categories,
    FactTypeID.MULTICLASS_CLASSIFICATION: extract_multiclass_classification,
}


def format_number(val: Any, symbol: str = "") -> str:
    if val is None:
        return ""
    sign = ""
    if val < 0:
        sign = "-"
        val = abs(val)
    if int(val) == float(val):
        return f"{sign}{symbol}{int(val):,}"
    else:
        return f"{sign}{symbol}{val:,.2f}"


def capitalize_first_letter_preserve_rest(input: str) -> str:
    words = input.split()
    result = []
    for word in words:
        if not word:
            # Empty string or something unexpected; just skip
            result.append(word)
            continue

        # Uppercase the first character, keep the rest unchanged
        capitalized = word[0].upper() + word[1:]
        result.append(capitalized)
    return " ".join(result)


def build_premise_id_to_entity_id(entities: dict[UUID, Entity]) -> dict[str, list[str]]:
    result = defaultdict(list)
    for entity in entities.values():
        for premise in entity.premises:
            result[premise.premises_id].append(entity.id)
    return result


def normalize_insured_name(insured_name: str | None) -> str | None:
    """
    Tries to strip some common prefixes from insured name, like remove leading 'owner'
    or trailing 'project:' and 'dba'/'dba:' and everything after them, and strip leading/trailing
    spaces and specific punctuation.
    """
    if not insured_name:
        return insured_name

    # Remove leading "owner" or "owner:"
    owner_pattern = r"^(OWNER\s*:?\s*)"
    modified_insured_name = re.sub(owner_pattern, "", insured_name, flags=re.IGNORECASE)

    # Remove "project:" and everything after it (colon must be present)
    project_pattern = r"\bproject:.*$"
    modified_insured_name = re.sub(project_pattern, "", modified_insured_name, flags=re.IGNORECASE)

    # Remove "dba" or "dba:" and everything after it
    dba_pattern = r"\bdba:?\b.*$"
    modified_insured_name = re.sub(dba_pattern, "", modified_insured_name, flags=re.IGNORECASE)

    # Remove extra spaces (happens sometimes)
    consecutive_spaces_pattern = r"\s{2,}"
    modified_insured_name = re.sub(consecutive_spaces_pattern, " ", modified_insured_name)

    # Strip trailing and leading whitespaces and selected punctuation chars (,;:)
    return modified_insured_name.rstrip().lstrip().rstrip(",;:")


def try_to_determine_business_type(
    insured_requested_name: str | None, ers_business_type: str | None, normalize_insured_name_before_check: bool = True
) -> BusinessType | None:
    """
    Tries to determine business type from insured requested name and ERS business type.
    :param insured_requested_name: Requested name of the insured.
    :param ers_business_type: Business type as detected by ERS.
    :param normalize_insured_name_before_check: If True, normalize the insured name before processing.
    :return: BusinessType enum value. If no match is found, returns BusinessType.OTHER.
    """
    # First, try to get business type from insured_requested_name. If present, it has higher priority.
    if insured_requested_name:
        insured_name = insured_requested_name
        if normalize_insured_name_before_check:
            insured_name = normalize_insured_name(insured_requested_name) or insured_name
        possible_business_type = get_business_type_from_legal_entity_name(insured_name)
        if possible_business_type and possible_business_type != BusinessType.OTHER:
            return possible_business_type

    # If the above fails or returns OTHER, fallback to the original ERS business_type.
    return BusinessType.try_parse_str(ers_business_type) if ers_business_type else BusinessType.OTHER
