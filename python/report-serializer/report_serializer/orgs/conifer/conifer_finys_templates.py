from collections import defaultdict
from collections.abc import Mapping
from datetime import datetime
from typing import Any

from static_common.enums.business import BusinessType
from static_common.enums.construction import ConstructionClass
from static_common.enums.fire_protection_class import FireProtectionClass

from report_serializer.results.models.insured import Insured
from report_serializer.results.models.report_repr import ReportRepr
from report_serializer.results.models.structure import Structure
from report_serializer.results.models.underwriter import Underwriter
from report_serializer.serializers.manual_dict_serializer import C, F, derive, when
from report_serializer.serializers.serializable_config.manual_dict_serializable_config import (
    ManualDictContext,
)

CONIFER_DATE_FORMAT = "%Y-%m-%dT%H:%M:%S"

# ------------------------------------------------
# -- Setting up context for whole serialization --
# ------------------------------------------------


def fill_context_for_conifer(context: ManualDictContext, report_repr: ReportRepr) -> None:
    _fill_coverages(context, report_repr)
    _fill_active_licenses_for_insureds(context, report_repr)


def _fill_coverages(context: ManualDictContext, report_repr: ReportRepr) -> None:
    context.has_gl = False
    context.has_ml = False  # Const: "VALUE_No"
    context.has_mp = False  # Const: "VALUE_No"
    context.has_pl = False
    context.has_pr = False
    for coverage in report_repr.coverages or []:
        if coverage.type != "PRIMARY":
            continue
        if coverage.name == "Liability":
            context.has_gl = True
        if coverage.name == "Product Liability":
            context.has_pl = True
        if coverage.name in {"Commercial Property", "Cannabis Liability"}:
            context.has_pr = True


def _fill_active_licenses_for_insureds(context: ManualDictContext, report_repr: ReportRepr) -> None:
    context.count_of_active_recreational_thc_licenses_for_insured = defaultdict(int)
    context.count_of_active_medical_thc_licenses_for_insured = defaultdict(int)
    context.active_thc_licenses_for_insured = defaultdict(list)
    # License type is "THC_LICENSE" + is active + number starts with:
    # AU -> recreational
    # not AU -> medical
    for insured in report_repr.insureds or []:
        for license in insured.licenses or []:
            if license.license_type == "THC_LICENSE" and license.is_active:
                context.active_thc_licenses_for_insured[insured.business_id].append(license)
                if license.number is not None and license.number.lower().startswith("au"):
                    context.count_of_active_recreational_thc_licenses_for_insured[insured.business_id] += 1
                else:
                    context.count_of_active_medical_thc_licenses_for_insured[insured.business_id] += 1


# -------------------------------------------
# -- Copilot to Conifer Finys enum mappers --
# -------------------------------------------


def yes_no_enum(value: bool | str | None) -> str:
    if isinstance(value, bool):
        # None or False → “VALUE_No”, True → “VALUE_Yes”
        return "VALUE_Yes" if value else "VALUE_No"

    if isinstance(value, str):
        if not value:
            return "VALUE_NULL"
        if value.lower() == "yes":
            return "VALUE_Yes"
        if value.lower() == "no":
            return "VALUE_No"

    return "VALUE_NULL"


def state_mapping(state: str | None) -> str:
    if not state:
        return "VALUE_NULL"
    kalepa_to_conifer = {
        "AL": "VALUE_Alabama",
        "AK": "VALUE_Alaska",
        "AZ": "VALUE_Arizona",
        "AR": "VALUE_Arkansas",
        "CA": "VALUE_California",
        "CO": "VALUE_Colorado",
        "CT": "VALUE_Connecticut",
        "DE": "VALUE_Delaware",
        "DC": "VALUE_District_Of_Columbia",
        "FL": "VALUE_Florida",
        "GA": "VALUE_Georgia",
        "HI": "VALUE_Hawaii",
        "ID": "VALUE_Idaho",
        "IL": "VALUE_Illinois",
        "IN": "VALUE_Indiana",
        "IA": "VALUE_Iowa",
        "KS": "VALUE_Kansas",
        "KY": "VALUE_Kentucky",
        "LA": "VALUE_Louisiana",
        "ME": "VALUE_Maine",
        "MD": "VALUE_Maryland",
        "MA": "VALUE_Massachusetts",
        "MI": "VALUE_Michigan",
        "MN": "VALUE_Minnesota",
        "MS": "VALUE_Mississippi",
        "MO": "VALUE_Missouri",
        "MT": "VALUE_Montana",
        "NE": "VALUE_Nebraska",
        "NV": "VALUE_Nevada",
        "NH": "VALUE_New_Hampshire",
        "NJ": "VALUE_New_Jersey",
        "NM": "VALUE_New_Mexico",
        "NY": "VALUE_New_York",
        "NC": "VALUE_North_Carolina",
        "ND": "VALUE_North_Dakota",
        "OH": "VALUE_Ohio",
        "OK": "VALUE_Oklahoma",
        "OR": "VALUE_Oregon",
        "PA": "VALUE_Pennsylvania",
        "PR": "VALUE_Puerto_Rico",
        "RI": "VALUE_Rhode_Island",
        "SC": "VALUE_South_Carolina",
        "SD": "VALUE_South_Dakota",
        "TN": "VALUE_Tennessee",
        "TX": "VALUE_Texas",
        "UT": "VALUE_Utah",
        "VT": "VALUE_Vermont",
        "VA": "VALUE_Virginia",
        "WA": "VALUE_Washington",
        "WV": "VALUE_West_Virginia",
        "WI": "VALUE_Wisconsin",
        "WY": "VALUE_Wyoming",
    }
    return kalepa_to_conifer.get(state.upper(), "VALUE_NULL")


def underwriter_enum(underwriter: Underwriter | None) -> str:
    if not underwriter:
        return "VALUE_NULL"
    email = underwriter.sanitized_email()
    if not email:
        return "VALUE_NULL"
    kalepa_to_conifer = {
        "<EMAIL>": "VALUE_Joe_Lyons",
        "<EMAIL>": "VALUE_Stephanie_Brant",
        "<EMAIL>": "VALUE_Lindsay_Wilson",
        "<EMAIL>": "VALUE_Toni_LaDue",
        "<EMAIL>": "VALUE_Lindsay_Wilcox",
        "<EMAIL>": "VALUE_Sarah_Gilbert",
        "<EMAIL>": "VALUE_Kristie_Shuller",
        "<EMAIL>": "VALUE_Brad_Lyons",
        "<EMAIL>": "VALUE_Rosann_Shunk",
    }
    return kalepa_to_conifer.get(email, "VALUE_NULL")


def business_type_enum(business_type: BusinessType | None) -> str:
    if not business_type:
        return "VALUE_OTHER"
    kalepa_to_conifer = {
        "INDIVIDUAL": "VALUE_INDIVIDUAL",
        "PARTNERSHIP": "VALUE_PARTNERSHIP",
        "LIMITED_PARTNERSHIP": "VALUE_PARTNERSHIP",
        "JOINT_VENTURE": "VALUE_JOINT_VENTURE",
        "LLC": "VALUE_LLC",
        "CORPORATION": "VALUE_CORPORATION",
        "TRUST": "VALUE_TRUST",
    }
    return kalepa_to_conifer.get(business_type, "VALUE_OTHER")


def open_status_enum(open_status: str | None) -> str:
    if not open_status:
        return "VALUE_NULL"
    kalepa_to_conifer = {
        "Open": "VALUE_Open_for_Business",
    }
    return kalepa_to_conifer.get(open_status, "VALUE_NULL")


def construction_class_enum(construction_class: str | None) -> str:
    if not construction_class:
        return "VALUE_NULL"

    kalepa_to_conifer = {
        ConstructionClass.FRAME.value: "VALUE_FRAME",
        ConstructionClass.JOISTED_MASONRY.value: "VALUE_JOISTED_MASONRY",
        ConstructionClass.NON_COMBUSTIBLE.value: "VALUE_NONCOMBUSTIBLE",
        ConstructionClass.MASONRY_NON_COMBUSTIBLE.value: "VALUE_MASONRY",
        ConstructionClass.MODIFIED_FIRE_RESISTIVE.value: "VALUE_MODIFIED_FIRE_RESISTIVE",
        ConstructionClass.FIRE_RESISTIVE.value: "VALUE_FIRERESISTIVE",
    }
    return kalepa_to_conifer.get(construction_class, "VALUE_NULL")


def fire_protection_class_enum(fire_protection_class: str | None) -> str:
    if not fire_protection_class:
        return "VALUE_NULL"

    kalepa_to_conifer = {
        FireProtectionClass.P1.value: "VALUE_1",
        FireProtectionClass.P2.value: "VALUE_2",
        FireProtectionClass.P3.value: "VALUE_3",
        FireProtectionClass.P4.value: "VALUE_4",
        FireProtectionClass.P5.value: "VALUE_5",
        FireProtectionClass.PP1.value: "VALUE_1",
        FireProtectionClass.PP2.value: "VALUE_2",
        FireProtectionClass.PP3.value: "VALUE_3",
        FireProtectionClass.PP4.value: "VALUE_4",
        FireProtectionClass.PP5.value: "VALUE_5",
    }
    return kalepa_to_conifer.get(fire_protection_class, "VALUE_NULL")


# ----------------------------------------------
# -- Custom rendering functions and templates --
# ----------------------------------------------


def _str_or_null(s: str | int | float | None) -> str:
    if not s:
        return "VALUE_NULL"
    return str(s)


def _int_or_null(s: str | int | float | None) -> str:
    if not s:
        return "VALUE_NULL"
    return str(int(s))


def as_conifer_date(conifer_datetime: datetime | None) -> str | None:
    return conifer_datetime.date().strftime(CONIFER_DATE_FORMAT) if conifer_datetime else None


def carrier(rr: ReportRepr, _context: ManualDictContext) -> str:
    state = (rr.fni and rr.fni.primary_state or "").lower()
    return (
        "VALUE_Palomar_Specialty_Insurance_Company"
        if state == "mi"
        else "VALUE_Palomar_Excess_and_Surplus_Insurance_Company"
    )


def thcpr_locations(structure: Structure) -> dict[str, Any]:
    return {
        "THCPRLocations": [
            {
                "THCPRLocationItems": [
                    {
                        "BIAmount": _int_or_null(structure.business_income),
                        "BPPLimit": _int_or_null(structure.bpp),
                        "IAndBAmount": _int_or_null(structure.tenants_improvements_and_betterments),
                        "BuildingLimit": _int_or_null(structure.building_value),
                        "IntruderAlarm": yes_no_enum(structure.has_burglar_alarm),
                        "BuildingYearBuilt": _str_or_null(structure.year_built),
                        "BPPProtectionClass": fire_protection_class_enum(structure.fire_protection_class),
                        "BPPConstructionType": construction_class_enum(structure.construction_class),
                        "FinishedStockAmount": _int_or_null(structure.inventory_value),
                        "IAndBProtectionClass": fire_protection_class_enum(structure.fire_protection_class),
                        "IAndBConstructionType": construction_class_enum(structure.construction_class),
                        "BuildingProtectionClass": fire_protection_class_enum(structure.fire_protection_class),
                        "BuildingConstructionType": construction_class_enum(structure.construction_class),
                        "AutomaticCentralFireAlarm": yes_no_enum(structure.has_fire_alarm),
                        "BPPTotalSQFootageBuilding": _int_or_null(structure.building_size),
                        "BuildingSQFootageBuilding": _int_or_null(structure.building_size),
                        "IAndBTotalSQFootageBuilding": _int_or_null(structure.building_size),
                    }
                ]
            }
        ]
    }


def thcpl_locations(insured: Insured, context: ManualDictContext) -> dict[str, Any]:
    location_item = {
        "ApprovedRecreational": context.count_of_active_recreational_thc_licenses_for_insured[insured.business_id],
        "ApprovedMedical": context.count_of_active_medical_thc_licenses_for_insured[insured.business_id],
    }

    # Add licenses if available
    licenses = context.active_thc_licenses_for_insured.get(insured.business_id, [])
    if licenses:
        license_items = []
        for license in licenses:
            license_type = (
                "VALUE_Recreational" if license.number and license.number.lower().startswith("au") else "VALUE_Medical"
            )
            license_items.append({"Type": license_type, "LicenseNumber": license.number})

        if license_items:
            location_item["THCPLLocationItemLicenses"] = license_items

    return {"THCPLLocations": [{"THCPLLocationItems": [location_item]}]}


def locations(rr: ReportRepr, context: ManualDictContext) -> list[Mapping[str, Any]]:
    # "Locations" element is every Structure (of every Premise of every Insured): Insured[x].Premise[y].Structure[z]
    # But some properties are coming from Structure parent (Premise) or even higher up the chain. Such values
    # are duplicated in every Structure object.
    # For multiple structures in a premise, we should only populate all fields in the first structure.

    out: list[Mapping[str, Any]] = []
    for insured in rr.insureds or []:
        if insured.hide_property_facts is True:
            continue
        for premise in insured.premises or []:
            prem_addr = derive(PREMISE_ADDRESS_TPL, "", premise, context)
            for idx, structure in enumerate(premise.structures or []):
                structure_element: dict[str, Any] = {"Address": prem_addr}
                if idx == 0:
                    structure_element.update(
                        {
                            "AgencyNameCode": None,
                            "ConstructionType": construction_class_enum(structure.construction_class),
                            "DBA": insured.dba_name,
                            "OperationState": open_status_enum(insured.open_status),
                            "HasPR": yes_no_enum(context.has_pr),
                            "HasPL": yes_no_enum(context.has_pl),
                            "HasGL": yes_no_enum(context.has_gl),
                            "HasML": yes_no_enum(context.has_ml),
                            "HasMP": yes_no_enum(context.has_mp),
                        }
                    )
                if context.has_pr:
                    structure_element.update(
                        {
                            "HasPR": yes_no_enum(True),
                            **thcpr_locations(structure),
                        }
                    )
                if context.has_pl:
                    structure_element.update(
                        {
                            "HasPL": yes_no_enum(True),
                            **thcpl_locations(insured, context),
                        }
                    )
                if structure_element:
                    out.append(structure_element)
    return out


PRIMARY_CONTACT_TPL: Mapping[str, Any] = {
    "DBA": F("dba_name"),
    "NameLast": F("name"),
    "PhoneBusiness": F("phone_number"),
    "EmailAddress": F("email"),
}

MAILING_ADDRESS_TPL: Mapping[str, Any] = {
    "City": F("primary_city"),
    "State": F("primary_state", state_mapping),
    "County": F("primary_county"),
    "LineOne": F("primary_address_line_1"),
    "LineTwo": F("primary_address_line_2"),
    "Zipcode": F("primary_zip"),
}

PREMISE_ADDRESS_TPL: Mapping[str, Any] = {
    "City": F("city"),
    "State": F("state", state_mapping),
    "County": F("county"),
    "LineOne": F("address_line_1"),
    "LineTwo": F("address_line_2"),
    "Zipcode": F("zip"),
}

THC_XY_POLICY_TPL: Mapping[str, Any] = {"AreLimitsSamePerLocation": "VALUE_NULL"}


CONIFER_FINYS_ROOT_TEMPLATE: Mapping[str, Any] = {
    "AgencyCode": F("agency_code", default="MKTCIC"),
    "AgentCode": F("agent_code", default="MKTCIC1"),
    "BusinessType": F("fni.business_type", business_type_enum),
    "Carrier": carrier,
    "CorprateWebAddress": F("fni.website"),
    "EffectiveDate": F("policy_effective_date", as_conifer_date),
    "HasGL": C("has_gl", yes_no_enum),
    "HasML": C("has_ml", yes_no_enum),
    "HasMP": C("has_mp", yes_no_enum),
    "HasPL": C("has_pl", yes_no_enum),
    "HasPR": C("has_pr", yes_no_enum),
    "InternalContact": F("first_highest_prio_underwriter", underwriter_enum),
    "Locations": locations,
    "MailingAddress": derive(MAILING_ADDRESS_TPL, "fni"),
    "PrimaryContact": derive(PRIMARY_CONTACT_TPL, "fni"),
    "THCGLPolicy": when("has_gl", THC_XY_POLICY_TPL),
    "THCMLPolicy": when("has_ml", THC_XY_POLICY_TPL),
    "THCMPPolicy": when("has_mp", THC_XY_POLICY_TPL),
    "THCPLPolicy": when("has_pl", THC_XY_POLICY_TPL),
    "THCPRPolicy": when("has_pr", THC_XY_POLICY_TPL),
    "UnderwritingStatus": "VALUE_READY_TO_QUOTE",
}
