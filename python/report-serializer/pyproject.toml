[project]
name = "report-serializer"
version = "25.5.26.23607.dev0"
description = "Generalized report serializer"
authors = [{ name = "Kalepa Tech" }]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "measurement==4.0a8",
    "common",
    "entity-resolution-service-client-v3",
    "copilot-client-v3",
    "infrastructure-common",
    "thefuzz>=0.22.1",
    "types-python-dateutil>=2.9.0.20241206",
]

[dependency-groups]
dev = ["pytest~=7.2.0", "pytest-mock~=3.14.0", "mypy~=1.4.1"]
kalepa = [
    "common",
    "entity-resolution-service-client-v3",
    "copilot-client-v3",
    "infrastructure-common",
    "static-common",
]

[tool.uv.sources]
common = { index = "kalepi" }
entity-resolution-service-client-v3 = { index = "kalepi" }
infrastructure-common = { index = "kalepi" }
static-common = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[project.optional-dependencies]
excel = ["xlsxwriter>=3.2.0"]

[tool.uv]
package = false
default-groups = ["dev", "kalepa"]

[tool.black]
line-length = 120
preview = true
target-version = ['py311']

[tool.isort]
profile = "black"
skip = ["__init__.py"]
from_first = true

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
line-length = 120
target-version = "py311"
extend-exclude = ["tests/", "scripts/", "test_utils/", "**/__init__.py"]

[tool.mypy]
python_version = "3.11"
strict = true
show_error_codes = true
disallow_untyped_calls = false
disallow_any_generics = false
warn_return_any = false
ignore_missing_imports = true
disable_error_code = "abstract"
exclude = [
    '^test_utils/',
    '^test/',
    '^tests/',
    '^scripts/',
    '^bin/',
    '.*__init__.py',
]
