from test.utils import u_uuid_1, u_uuid_2, u_uuid_8, u_uuid_9
from typing import Dict, List, Optional
from uuid import UUID

import shapely
from geoalchemy2.shape import from_shape
from infrastructure_common.logging import get_logger
from static_common.enums.entity import (
    EntitiesRelationOrigin,
    EntitiesRelationType,
    EntityCategory,
    EntityNameType,
    EntityPremisesType,
)
from static_common.enums.external import ExternalIdentifierType
from static_common.enums.file_type import FileType
from static_common.enums.industry import IndustryEnum
from static_common.enums.source_types import SourceTypeID, WebIngestedSourceTypeID

from src.api.v3.entities import get_or_create_entity
from src.api.v3.models.entities_relation import EntitiesRelationRequest
from src.api.v3.models.entity import (
    EntityIndustryRequest,
    EntityNameRequest,
    EntityPremisesRequest,
    EntityRequest,
    ExternalIdentifierRequest,
)
from src.api.v3.models.entity_with_relations import EntityWithRelationsRequest
from src.api.v3.models.source import SourceRequest
from src.api.v3.schemas.entity import (
    ComposedNodeResponseSchema,
    EntityIndustryRequestSchema,
    EntityNameRequestSchema,
    EntityPremisesRequestSchema,
    EntityRequestSchema,
    EntityResponseSchema,
    ExternalIdentifierRequestSchema,
)
from src.geo.utils import normalize_address_db
from src.logic.entity import create_entity, enrich_request_with_premises
from src.logic.entity_snapshot import custom_serializer
from src.logic.source_dao import SourceManager
from src.models import Premises, db
from src.models.composed_node import ComposedNode
from src.models.entities_relation import EntitiesRelation
from src.models.entity import (
    Entity,
    EntityIndustry,
    EntityName,
    EntityPremises,
    EntitySource,
    ExternalIdentifier,
)
from src.models.snapshots import ComposedNodeSnapshot, EntitySnapshot
from src.utils.constants import EMPTY_NAME

logger = get_logger()
PO_BOX_UUID = UUID("a8cd914c-5260-43ce-aca9-26338d87e13c")
TENANT_PREMISES_1_UUID = UUID("b8cd914c-5260-43ce-aca9-26338d87e13c")
TENANT_PREMISES_2_UUID = UUID("c8cd914c-5260-43ce-aca9-26338d87e13c")
TENANT_PREMISES_3_UUID = UUID("d8cd914c-5260-43ce-aca9-26338d87e13c")

PREMISES_ID_TO_CLEAN = [PO_BOX_UUID, TENANT_PREMISES_1_UUID, TENANT_PREMISES_2_UUID, TENANT_PREMISES_3_UUID]

ADDRESS_TO_PREMISES_ID: Dict[str, UUID] = {
    "176 BEVERLY RD, NEW YORK, NY 10305, US": UUID("a35c5c79-f1d7-41ec-b70b-d3ced7cfd900"),
    "187 MAPLEVIEW DR, TONAWANDA, NY 14150, US": UUID("2ff7d8d1-8c21-4146-ac57-01370daa847b"),
    "42.97867 - 78.87741": UUID("2ff7d8d1-8c21-4146-ac57-01370daa847b"),
    "505 EDWIN DR, VIRGINIA BEACH, VA 23462, US": UUID("d0f1ad4c-5380-4b84-905a-aa0ecc067f96"),
    "36.82471 -76.13276": UUID("d0f1ad4c-5380-4b84-905a-aa0ecc067f96"),
    "EDWIN DR, VIRGINIA BEACH, VA 23462, US": UUID("a35c5c79-f1d7-41ec-b70b-d3ced7cfbbb1"),
    "VIRGINIA BEACH, VA 23462, US": UUID("a35c5c79-f1d7-41ec-b70b-d3ced7cfbbb2"),
    "7696 SE BAY CEDAR CIR, HOBE SOUND, FL 33455, US": UUID("497553d5-741d-4791-a94e-7a3e0ae321c5"),
    "tulare ca 93274": PO_BOX_UUID,
    "NY, US": UUID("fb56477c-e71c-4914-b0e6-3be6370301c7"),
    "NEW YORK, NY, US": UUID("a8cd914c-5260-43ce-aca9-26338d87e13c"),
    "TONAWANDA, NY, US": UUID("2ff7d8d1-8c21-4146-ac57-01370daa847b"),
    "FL, US": UUID("497553d5-741d-4791-a94e-7a3e0ae321c5"),
    "DAYTON, OH, US": UUID("d036bdca-d5df-433e-901a-07ff294fdf33"),
    "US": UUID("a35c5c79-f1d7-41ec-b70b-d3ced7cfd900"),
    "POST OFFICE BOX 100 - ZIP CODE 00681": UUID("a35c5c79-f1d7-41ec-b70b-d3ced7cfd900"),
    "3595 Tyla & 3599 Tyla, Memphis, TN 38127, US": UUID("affc5c79-f1d7-41dc-ba0b-d3ced7cfdbb1"),
    "162-25 99TH ST, NY 11414, US": UUID("d6540186-e50d-4c99-b12c-cc07302e2297"),
}
PREMISES_TO_CREATE = {
    "a35c5c79-f1d7-41ec-b70b-d3ced7cfbbb1": Premises(
        id="a35c5c79-f1d7-41ec-b70b-d3ced7cfbbb1",
        formatted_address="EDWIN DR, VIRGINIA BEACH, VA 23462, US",
        normalized_address="EDWIN DR, VIRGINIA BEACH, VA 23462, US",
        address_line_1="EDWIN DR",
        state="VA",
        city="VIRGINIA BEACH",
        postal_code="23462",
        position=from_shape(shapely.wkt.loads("POINT(-79.07897 42.60051)")),
    ),
    "a35c5c79-f1d7-41ec-b70b-d3ced7cfbbb2": Premises(
        id="a35c5c79-f1d7-41ec-b70b-d3ced7cfbbb2",
        formatted_address="VIRGINIA BEACH, VA 23462, US",
        normalized_address="VIRGINIA BEACH, VA 23462, US",
        address_line_1="",
        state="VA",
        city="VIRGINIA BEACH",
        postal_code="23462",
        position=from_shape(shapely.wkt.loads("POINT(-71.07897 49.60051)")),
    ),
    "affc5c79-f1d7-41dc-ba0b-d3ced7cfdbb1": Premises(
        id="affc5c79-f1d7-41dc-ba0b-d3ced7cfdbb1",
        formatted_address="3595 TYLA, MEMPHIS, TN 38127, US",
        normalized_address="3595 TYLA, MEMPHIS, TN 38127, US",
        address_line_1="3595 TYLA",
        state="TN",
        city="MEMPHIS",
        postal_code="38127",
        position=from_shape(shapely.wkt.loads("POINT(-78.07897 40.60051)")),
    ),
    "afac5c79-f1d7-41dc-ba0b-d3ced7cfdbb1": Premises(
        id="afac5c79-f1d7-41dc-ba0b-d3ced7cfdbb1",
        formatted_address="3599 TYLA, MEMPHIS, TN 38127, US",
        normalized_address="3599 TYLA, MEMPHIS, TN 38127, US",
        address_line_1="3599 TYLA",
        state="TN",
        city="MEMPHIS",
        postal_code="38127",
        position=from_shape(shapely.wkt.loads("POINT(-38.07897 39.60051)")),
    ),
    "d6540186-e50d-4c99-b12c-cc07302e2297": Premises(
        id="d6540186-e50d-4c99-b12c-cc07302e2297",
        formatted_address="162-25 99TH ST, NY 11414, US",
        normalized_address="162-25 99TH ST, NY 11414, US",
        address_line_1="62-25 99TH ST",
        state="NY",
        postal_code="11414",
        position=from_shape(shapely.wkt.loads("POINT(-44.07897 39.60051)")),
    ),
}

entity_legal_name_1 = EntityName(type=EntityNameType.LEGAL_NAME, value="Foo Entity Inc")
entity_legal_name_1_copy = EntityName(type=EntityNameType.LEGAL_NAME, value="Foo Entity Inc")
entity_legal_name_2 = EntityName(type=EntityNameType.LEGAL_NAME, value="Baz Bar Company Inc")
entity_dba_name_1 = EntityName(type=EntityNameType.DBA_NAME, value="Fake Entity Name")
entity_dba_name_2 = EntityName(type=EntityNameType.DBA_NAME, value="Fictional Name Company")

entity_physical_address = EntityPremises(
    type=EntityPremisesType.PHYSICAL_ADDRESS, premises_id=UUID("a35c5c79-f1d7-41ec-b70b-d3ced7cfd900")
)
entity_physical_address_copy = EntityPremises(
    type=EntityPremisesType.PHYSICAL_ADDRESS, premises_id=UUID("a35c5c79-f1d7-41ec-b70b-d3ced7cfd900")
)
entity_mailing_address = EntityPremises(
    type=EntityPremisesType.MAILING_ADDRESS, premises_id=UUID("a35c5c79-f1d7-41ec-b70b-d3ced7cfd900")
)
entity_physical_address_2 = EntityPremises(
    type=EntityPremisesType.PHYSICAL_ADDRESS, premises_id=UUID("d036bdca-d5df-433e-901a-07ff294fdf33")
)
entity_mailing_address_2 = EntityPremises(
    type=EntityPremisesType.MAILING_ADDRESS, premises_id=UUID("0e132098-b692-443e-8b97-0ea6eeebe2b2")
)

email_identifier_1 = ExternalIdentifier(type=ExternalIdentifierType.EMAIL, value="<EMAIL>")
email_identifier_2 = ExternalIdentifier(type=ExternalIdentifierType.EMAIL, value="<EMAIL>")
website_identifier = ExternalIdentifier(type=ExternalIdentifierType.WEBSITE, value="fooentity.com")
phone_identifier = ExternalIdentifier(type=ExternalIdentifierType.PHONE, value="+48 111 222 333")
usdot_identifier_1 = ExternalIdentifier(type=ExternalIdentifierType.USDOT, value="123456")
usdot_identifier_2 = ExternalIdentifier(type=ExternalIdentifierType.USDOT, value="9876543")
fein_identifier_1 = ExternalIdentifier(type=ExternalIdentifierType.FEIN, value="12-3456789")
fein_identifier_2 = ExternalIdentifier(type=ExternalIdentifierType.FEIN, value="09-8765432")

entity_name_request_real_estate = EntityNameRequest(
    type=EntityNameType.UNDEFINED, value=EMPTY_NAME, is_public=True, is_verified=True
)
entity_name_request_not_verified = EntityNameRequest(
    type=EntityNameType.DBA_NAME, value="Not Verified Name", is_public=True, is_verified=False
)
entity_name_request_not_verified_data = EntityNameRequestSchema().dump(entity_name_request_not_verified)
entity_name_request_not_public = EntityNameRequest(
    type=EntityNameType.DBA_NAME, value="Not Public Name", is_public=False, is_verified=True
)
entity_name_request_not_public_data = EntityNameRequestSchema().dump(entity_name_request_not_verified)
entity_name_request = EntityNameRequest(
    type=EntityNameType.LEGAL_NAME, value="Foo Entity INC", is_public=True, is_verified=True
)
entity_name_request_data = EntityNameRequestSchema().dump(entity_name_request)
entity_name_request_comma = EntityNameRequest(
    type=EntityNameType.DBA_NAME, value="Foo, Entity", is_public=True, is_verified=True
)
entity_name_request_data_comma = EntityNameRequestSchema().dump(entity_name_request_comma)
entity_name_2_request = EntityNameRequest(
    type=EntityNameType.LEGAL_NAME, value="Baz Bar Company Inc", is_public=True, is_verified=True
)
entity_name_2_request_data = EntityNameRequestSchema().dump(entity_name_2_request)
entity_name_3_request = EntityNameRequest(
    type=EntityNameType.DBA_NAME, value="Foo cards at the Resout", is_public=True, is_verified=True
)
entity_name_3_request_data = EntityNameRequestSchema().dump(entity_name_3_request)
entity_name_dba_request = EntityNameRequest(
    type=EntityNameType.DBA_NAME, value="A.C.M.E", is_public=True, is_verified=True
)
entity_name_dba_request_data = EntityNameRequestSchema().dump(entity_name_dba_request)
entity_name_dba_2_request = EntityNameRequest(
    type=EntityNameType.DBA_NAME, value="Fictional Name Company", is_public=True, is_verified=True
)
entity_name_dba_2_request_data = EntityNameRequestSchema().dump(entity_name_dba_2_request)
entity_name_dba_3_request = EntityNameRequest(
    type=EntityNameType.DBA_NAME, value="Foo cards at the Resout", is_public=True, is_verified=True
)
entity_name_dba_3_request_data = EntityNameRequestSchema().dump(entity_name_dba_3_request)

entity_name_request_with_address = EntityNameRequest(
    type=EntityNameType.DBA_NAME, value="NEW YORK hospital", is_public=True, is_verified=True
)
entity_name_request_with_address_data = EntityNameRequestSchema().dump(entity_name_request_with_address)
entity_name_2_request_with_address = EntityNameRequest(
    type=EntityNameType.DBA_NAME, value="NEW YORK area", is_public=True, is_verified=True
)
entity_name_2_request_with_address_data = EntityNameRequestSchema().dump(entity_name_2_request_with_address)

entity_premises_request = EntityPremisesRequest(
    type=EntityPremisesType.PHYSICAL_ADDRESS, address="176 BEVERLY RD, NEW YORK, NY 10305, US"
)
unconfirmed_entity_premises_request = EntityPremisesRequest(
    type=EntityPremisesType.PHYSICAL_ADDRESS, address="176 BEVERLY RD, NEW YORK, NY 10305, US", confidence=0.0
)

entity_premises_request_data = EntityPremisesRequestSchema().dump(entity_premises_request)

unconfirmed_entity_premises_request_data = EntityPremisesRequestSchema().dump(unconfirmed_entity_premises_request)
entity_premises_2_request = EntityPremisesRequest(
    type=EntityPremisesType.PHYSICAL_ADDRESS, address="7696 SE BAY CEDAR CIR, HOBE SOUND, FL 33455, US"
)
entity_premises_2_request_data = EntityPremisesRequestSchema().dump(entity_premises_2_request)
entity_premises_mailing_request = EntityPremisesRequest(
    type=EntityPremisesType.MAILING_ADDRESS, address="187 MAPLEVIEW DR, TONAWANDA, NY 14150, US"
)
entity_premises_mailing_request_data = EntityPremisesRequestSchema().dump(entity_premises_mailing_request)
entity_premises_mailing_2_request = EntityPremisesRequest(
    type=EntityPremisesType.MAILING_ADDRESS, address="7696 SE BAY CEDAR CIR, HOBE SOUND, FL 33455, US"
)
entity_premises_mailing_2_request_data = EntityPremisesRequestSchema().dump(entity_premises_mailing_2_request)
entity_premises_mailing_3_request = EntityPremisesRequest(
    type=EntityPremisesType.MAILING_ADDRESS, address="176 BEVERLY RD, NEW YORK, NY 10305, US"
)
entity_premises_mailing_3_request_data = EntityPremisesRequestSchema().dump(entity_premises_mailing_3_request)
entity_premises_undefined_request = EntityPremisesRequest(
    type=EntityPremisesType.UNDEFINED, address="505 EDWIN DR, VIRGINIA BEACH, VA 23462, US"
)
entity_premises_undefined_request_without_address_line_1 = EntityPremisesRequest(
    type=EntityPremisesType.PHYSICAL_ADDRESS, address="VIRGINIA BEACH, VA 23462, US"
)
entity_premises_undefined_request_without_building_number_request = EntityPremisesRequest(
    type=EntityPremisesType.PHYSICAL_ADDRESS, address="EDWIN DR, VIRGINIA BEACH, VA 23462, US"
)
entity_premises_undefined_request_data = EntityPremisesRequestSchema().dump(entity_premises_undefined_request)
entity_premises_undefined_without_address_line_1_data = EntityPremisesRequestSchema().dump(
    entity_premises_undefined_request_without_address_line_1
)
entity_premises_undefined_without_building_number_request_data = EntityPremisesRequestSchema().dump(
    entity_premises_undefined_request_without_building_number_request
)
entity_premises_3_request = EntityPremisesRequest(
    type=EntityPremisesType.PHYSICAL_ADDRESS, address="1764 S ORTONVILLE RD, BRANDON TWP, MI 48462, US"
)
entity_premises_3_request_data = EntityPremisesRequestSchema().dump(entity_premises_3_request)
entity_premises_4_request = EntityPremisesRequest(
    type=EntityPremisesType.PHYSICAL_ADDRESS, address="187 MAPLEVIEW DR, TONAWANDA, NY 14150, US"
)
entity_premises_4_request_data = EntityPremisesRequestSchema().dump(entity_premises_4_request)
unconfirmed_entity_premises_4_request = EntityPremisesRequest(
    type=EntityPremisesType.PHYSICAL_ADDRESS, address="187 MAPLEVIEW DR, TONAWANDA, NY 14150, US", confidence=0.0
)
unconfirmed_entity_premises_4_request_data = EntityPremisesRequestSchema().dump(unconfirmed_entity_premises_4_request)

entity_undefined_new_york_request = EntityPremisesRequest(type=EntityPremisesType.UNDEFINED, address="NY, US")
entity_undefined_new_work_request_data = EntityPremisesRequestSchema().dump(entity_undefined_new_york_request)
entity_area_new_york_request = EntityPremisesRequest(type=EntityPremisesType.AREA, address="NY, US")
entity_area_new_york_request_data = EntityPremisesRequestSchema().dump(entity_area_new_york_request)
entity_area_florida_request = EntityPremisesRequest(type=EntityPremisesType.AREA, address="FL, US")
entity_area_florida_request_data = EntityPremisesRequestSchema().dump(entity_area_florida_request)
entity_area_texas_request = EntityPremisesRequest(type=EntityPremisesType.AREA, address="TX, US")
entity_area_texas_request_data = EntityPremisesRequestSchema().dump(entity_area_texas_request)
entity_area_us = EntityPremisesRequest(type=EntityPremisesType.AREA, address="US")
entity_area_us_data = EntityPremisesRequestSchema().dump(entity_area_us)
source_fda = SourceRequest(
    url="https://datadashboard.fda.gov/ora/api/index.htm",
    web_ingestion_type=str(WebIngestedSourceTypeID.FDA.value),
    source_type=str(SourceTypeID.WEB_INGESTED.value),
)

source_osha = SourceRequest(
    url="https://osha/api/index.htm",
    web_ingestion_type=str(WebIngestedSourceTypeID.OSHA.value),
    source_type=str(SourceTypeID.WEB_INGESTED.value),
)
source_file_9_row_1 = SourceRequest(
    source_type=str(SourceTypeID.FIRST_PARTY.value),
    organization_id=99,
    submission_id=u_uuid_9,
    file_id=u_uuid_9,
    row="1",
)
source_file_9_row_2 = SourceRequest(
    source_type=str(SourceTypeID.FIRST_PARTY.value),
    organization_id=99,
    submission_id=u_uuid_9,
    file_id=u_uuid_9,
    row="2",
)
source_file_8_row_1 = SourceRequest(
    source_type=str(SourceTypeID.FIRST_PARTY.value),
    organization_id=99,
    submission_id=u_uuid_9,
    file_id=u_uuid_8,
    row="1",
)

source_file_8_row_1_org_98 = SourceRequest(
    source_type=str(SourceTypeID.FIRST_PARTY.value),
    organization_id=98,
    submission_id=u_uuid_9,
    file_id=u_uuid_8,
    row="1",
)

source_file_9_row_1_sub_8 = SourceRequest(
    source_type=str(SourceTypeID.FIRST_PARTY.value),
    organization_id=99,
    submission_id=u_uuid_8,
    file_id=u_uuid_9,
    row="1",
)

source_with_file_type = SourceRequest(
    source_type=str(SourceTypeID.FIRST_PARTY.value),
    organization_id=98,
    submission_id=u_uuid_9,
    file_id=u_uuid_8,
    row="1",
    file_type=FileType.OTHER.value,
)

source_submission_8 = SourceRequest(
    source_type=str(SourceTypeID.FIRST_PARTY.value), organization_id=99, submission_id=u_uuid_8
)

source_submission_9 = SourceRequest(
    source_type=str(SourceTypeID.FIRST_PARTY.value), organization_id=99, submission_id=u_uuid_9
)
source_user_feedback = SourceRequest(source_type=str(SourceTypeID.USER_FEEDBACK.value), user_id=42)
source_website_1 = SourceRequest(
    url="https://arthur-e.github.io/Wicket/sandbox-gmaps3.html",
    web_ingestion_type=str(WebIngestedSourceTypeID.EBSA.value),
    source_type=str(SourceTypeID.WEB_INGESTED.value),
)
source_website_2 = SourceRequest(
    url="https://arthur-e.github.io/Wicket/1234.html",
    web_ingestion_type=str(WebIngestedSourceTypeID.EBSA.value),
    source_type=str(SourceTypeID.WEB_INGESTED.value),
)

usdot_request = ExternalIdentifierRequest(type=ExternalIdentifierType.USDOT, value="123456")
usdot_request_data = ExternalIdentifierRequestSchema().dump(usdot_request)
usdot_2_request = ExternalIdentifierRequest(type=ExternalIdentifierType.USDOT, value="654321")
usdot_2_request_data = ExternalIdentifierRequestSchema().dump(usdot_2_request)
fein_request = ExternalIdentifierRequest(type=ExternalIdentifierType.FEIN, value="09-8765432")
fein_request_data = ExternalIdentifierRequestSchema().dump(fein_request)
fein_2_request = ExternalIdentifierRequest(type=ExternalIdentifierType.FEIN, value="12-3456789")
fein_2_request_data = ExternalIdentifierRequestSchema().dump(fein_2_request)
fein_request_with_source = ExternalIdentifierRequest(
    type=ExternalIdentifierType.FEIN, value="09-8765432", source=source_user_feedback
)
fein_request_with_source_data = ExternalIdentifierRequestSchema().dump(fein_request_with_source)
epa_request = ExternalIdentifierRequest(type=ExternalIdentifierType.EPA_REGISTRY_ID, value="9988776622111")
epa_request_data = ExternalIdentifierRequestSchema().dump(epa_request)
phone_request = ExternalIdentifierRequest(type=ExternalIdentifierType.PHONE, value="+48 111222333")
phone_request_data = ExternalIdentifierRequestSchema().dump(phone_request)

cage_request = ExternalIdentifierRequest(type=ExternalIdentifierType.CAGE, value="H0095")
cage_request_data = ExternalIdentifierRequestSchema().dump(cage_request)
cage_2_request = ExternalIdentifierRequest(type=ExternalIdentifierType.CAGE, value="H0195")
cage_2_request_data = ExternalIdentifierRequestSchema().dump(cage_2_request)
uei_request = ExternalIdentifierRequest(type=ExternalIdentifierType.UEI, value="1222333")
uei_request_data = ExternalIdentifierRequestSchema().dump(uei_request)
fda_fei_request = ExternalIdentifierRequest(type=ExternalIdentifierType.FDA_FEI, value="2333")
fda_fei_request_data = ExternalIdentifierRequestSchema().dump(fda_fei_request)
duns_request = ExternalIdentifierRequest(type=ExternalIdentifierType.DUNS, value="2333")
duns_request_data = ExternalIdentifierRequestSchema().dump(duns_request)
sam_request = ExternalIdentifierRequest(type=ExternalIdentifierType.SAM, value="2333")
sam_request_data = ExternalIdentifierRequestSchema().dump(sam_request)
open_corporates_id_request = ExternalIdentifierRequest(
    type=ExternalIdentifierType.OPEN_CORPORATES_ID, value="E17908332021-6"
)
open_corporates_id_request_data = ExternalIdentifierRequestSchema().dump(open_corporates_id_request)
open_corporates_id_2_request = ExternalIdentifierRequest(
    type=ExternalIdentifierType.OPEN_CORPORATES_ID, value="E17908332021-7"
)
open_corporates_id_2_request_data = ExternalIdentifierRequestSchema().dump(open_corporates_id_request)

po_box_request = ExternalIdentifierRequest(
    type=ExternalIdentifierType.PO_BOX, value="POST OFFICE BOX 8312 - ZIP CODE 91409"
)
po_box_request_data = ExternalIdentifierRequestSchema().dump(po_box_request)
email_request = ExternalIdentifierRequest(type=ExternalIdentifierType.EMAIL, value="<EMAIL>")
email_request_data = ExternalIdentifierRequestSchema().dump(email_request)
website_request = ExternalIdentifierRequest(type=ExternalIdentifierType.WEBSITE, value="google.com")
website_request_data = ExternalIdentifierRequestSchema().dump(website_request)
owner_request = ExternalIdentifierRequest(type=ExternalIdentifierType.OWNER, value="owner")
owner_request_data = ExternalIdentifierRequestSchema().dump(owner_request)
canada_country_code = ExternalIdentifierRequest(type=ExternalIdentifierType.COUNTRY_CODE, value="CA")
canada_country_code_data = ExternalIdentifierRequestSchema().dump(owner_request)
entities_relation_active_request_directional = EntitiesRelationRequest(
    from_entity_id=u_uuid_1,
    to_entity_id=u_uuid_2,
    type=EntitiesRelationType.REINCORPORATED,
    confidence=0.95,
    explanation="Fake",
    origin=EntitiesRelationOrigin.AUTOGENERATED,
)
entities_relation_active_request_bidirectional = EntitiesRelationRequest(
    from_entity_id=u_uuid_1,
    to_entity_id=u_uuid_2,
    type=EntitiesRelationType.ALIAS,
    confidence=0.95,
    explanation="Fake",
    origin=EntitiesRelationOrigin.AUTOGENERATED,
)

entities_relation_inactive_request_directional = EntitiesRelationRequest(
    from_entity_id=u_uuid_1,
    to_entity_id=u_uuid_2,
    type=EntitiesRelationType.REINCORPORATED,
    confidence=0.05,
    explanation="Fake",
    origin=EntitiesRelationOrigin.AUTOGENERATED,
)
entities_relation_inactive_request_bidirectional = EntitiesRelationRequest(
    from_entity_id=u_uuid_1,
    to_entity_id=u_uuid_2,
    type=EntitiesRelationType.ALIAS,
    confidence=0.05,
    explanation="Fake",
    origin=EntitiesRelationOrigin.AUTOGENERATED,
)

entity_with_relations_request = EntityWithRelationsRequest(
    from_entity=EntityRequest(names=[entity_name_request]),
    to_entity_ids=[u_uuid_1, u_uuid_2],
    confidence=0.95,
    explanation="Fake",
    origin=EntitiesRelationOrigin.INGESTED,
    source=source_user_feedback,
)

entity_with_relations_and_sources_request = EntityWithRelationsRequest(
    from_entity=EntityRequest(names=[entity_name_request], source=source_fda),
    to_entity_ids=[u_uuid_1, u_uuid_2],
    confidence=0.95,
    explanation="Fake",
    origin=EntitiesRelationOrigin.INGESTED,
    source=source_user_feedback,
)

other_industry_request = EntityIndustryRequest(name=IndustryEnum.other)
other_industry_request_data = EntityIndustryRequestSchema().dump(other_industry_request)
retail_industry_request = EntityIndustryRequest(name=IndustryEnum.retail)
retail_industry_request_data = EntityIndustryRequestSchema().dump(retail_industry_request)
finance_industry_request = EntityIndustryRequest(name=IndustryEnum.finance)
finance_industry_request_data = EntityIndustryRequestSchema().dump(finance_industry_request)


def enrich_request_and_create_entity_fixture(request: EntityRequest) -> Entity:
    enriched = enrich_request_with_premises(request.premises)
    request.premises = enriched
    return create_entity(request)


def create_entity_from_request_fixture(
    *,
    names: Optional[List[EntityNameRequest]] = None,
    premises: Optional[List[EntityPremisesRequest]] = None,
    identifiers: Optional[List[ExternalIdentifierRequest]] = None,
    industries: Optional[List[EntityIndustryRequest]] = None,
    category: Optional[EntityCategory] = None,
    source: Optional[SourceRequest] = None,
) -> Entity:
    names = names or []
    premises = premises or []
    identifiers = identifiers or []
    industries = industries or []
    request = EntityRequest(
        names=names,
        premises=premises,
        identifiers=identifiers,
        industries=industries,
        categories=[category] if category else [],
        source=source,
    )

    result = get_or_create_entity(EntityRequestSchema().dump(request))

    # sanity check
    entity_id = result[0].get("id")
    entity = Entity.query.get(entity_id)
    assert entity is not None
    assert EntityName.query.count() >= len(names)
    assert EntityPremises.query.count() >= len(premises)
    assert ExternalIdentifier.query.count() >= len(identifiers)

    return entity


def create_entity_fixture(
    uuid: UUID,
    *,
    names: Optional[List[EntityName]] = None,
    premises: Optional[List[EntityPremises]] = None,
    identifiers: Optional[List[ExternalIdentifier]] = None,
    industries: Optional[List[EntityIndustry]] = None,
    category: Optional[EntityCategory] = None,
    source: Optional[SourceRequest] = None,
) -> Entity:
    names = names or []
    premises = premises or []
    identifiers = identifiers or []
    industries = industries or []

    if not (entity := Entity.query.get(uuid)):
        stored_source = SourceManager.get_one_or_create_if_unique(source)
        entity = Entity(
            id=uuid,
            names=names,
            premises=premises,
            identifiers=identifiers,
            industries=industries,
            category=category if category else EntityCategory.LOCATION,
            sources=[
                EntitySource(
                    _source=stored_source, source_id=stored_source.id, source_type=stored_source.get_detailed_type()
                )
            ]
            if stored_source
            else [],
        )
        db.session.add(entity)
        db.session.commit()

    # sanity check
    assert Entity.query.filter(Entity.id == uuid).count() == 1
    assert EntityName.query.count() >= len(names)
    assert EntityPremises.query.count() >= len(premises)
    assert ExternalIdentifier.query.count() >= len(identifiers)

    return entity


def create_artifical_premises():
    for id in PREMISES_TO_CREATE.keys():
        if not Premises.query.get(id):
            normalized_address = normalize_address_db(
                address_line_1=PREMISES_TO_CREATE[id].address_line_1,
                address_line_2=PREMISES_TO_CREATE[id].address_line_2,
                state=PREMISES_TO_CREATE[id].state,
                postal_code=PREMISES_TO_CREATE[id].postal_code,
            )
            PREMISES_TO_CREATE[id].normalized_address = normalized_address
            db.session.add(PREMISES_TO_CREATE[id])
    db.session.commit()


def create_snapshot_fixture(
    uuid: UUID, *, entity_id: Optional[UUID] = None, entity: Optional[Entity] = None
) -> EntitySnapshot:
    entity = entity or create_entity_fixture(entity_id or u_uuid_9)
    db.session.add(entity)

    entity_dump = EntityResponseSchema().dumps(entity, default=custom_serializer)
    snapshot = EntitySnapshot(id=uuid, entity_id=entity.id, entity=entity_dump)
    db.session.add(snapshot)
    db.session.commit()

    # sanity check
    assert Entity.query.filter(Entity.id == entity.id).count() == 1
    assert EntitySnapshot.query.filter(EntitySnapshot.id == uuid).count() == 1

    return snapshot


def create_composed_node_snapshot_fixture(
    uuid: UUID, *, composed_node_id: Optional[UUID] = None, composed_node: Optional[Entity] = None
) -> ComposedNodeSnapshot:
    composed_node = composed_node or create_composed_node_fixture(composed_node_id or u_uuid_8)
    db.session.add(composed_node)

    composed_node_dump = ComposedNodeResponseSchema().dumps(composed_node, default=custom_serializer)
    snapshot = ComposedNodeSnapshot(id=uuid, composed_node_id=composed_node.id, composed_node=composed_node_dump)
    db.session.add(snapshot)
    db.session.commit()

    # sanity check
    assert ComposedNode.query.filter(ComposedNode.id == ComposedNode.id).count() == 1
    assert ComposedNodeSnapshot.query.filter(ComposedNodeSnapshot.id == uuid).count() == 1

    return snapshot


def create_entities_relation_fixture(
    entity_from_id: UUID,
    entity_to_id: UUID,
    *,
    rel_type: Optional[EntitiesRelationType] = None,
    confidence: Optional[float] = None,
    origin: Optional[EntitiesRelationOrigin] = None,
) -> EntitiesRelation:
    entity_from = create_entity_fixture(entity_from_id)
    entity_to = create_entity_fixture(entity_to_id)
    db.session.add(entity_from)
    db.session.add(entity_to)
    db.session.commit()

    relation = EntitiesRelation(
        from_entity_id=entity_from.id,
        to_entity_id=entity_to.id,
        type=rel_type or EntitiesRelationType.ALIAS,
        confidence=confidence or 0.0,
        origin=origin or EntitiesRelationOrigin.MANUAL,
    )
    db.session.add(relation)
    db.session.commit()

    # sanity check
    assert Entity.query.filter(Entity.id == entity_from.id).count() == 1
    assert Entity.query.filter(Entity.id == entity_to.id).count() == 1
    assert EntitiesRelation.query.filter(EntitiesRelation.id == relation.id).count() == 1

    return relation


def create_composed_node_fixture(
    uuid: UUID, *, is_active: bool = True, entities_ids: Optional[List[UUID]] = None
) -> ComposedNode:
    entities_ids = entities_ids or [u_uuid_9]
    for entity_id in entities_ids:
        create_entity_fixture(entity_id)

    composed_node = ComposedNode(id=uuid, is_active=is_active)
    composed_node.entities_ids = entities_ids
    db.session.add(composed_node)
    db.session.commit()

    # sanity check
    assert (node := ComposedNode.query.get(composed_node.id))
    assert node.entities_ids == entities_ids

    return composed_node


# explicitly list items that can be imported using `from <this_module> import *`
__all__ = (
    "ADDRESS_TO_PREMISES_ID",
    "PO_BOX_UUID",
    "PREMISES_ID_TO_CLEAN",
    "TENANT_PREMISES_3_UUID",
    "TENANT_PREMISES_2_UUID",
    "TENANT_PREMISES_1_UUID",
    "create_entity_fixture",
    "create_snapshot_fixture",
    "create_entities_relation_fixture",
    "create_composed_node_fixture",
    "create_composed_node_snapshot_fixture",
    "entity_legal_name_1",
    "entity_legal_name_2",
    "entity_dba_name_1",
    "entity_dba_name_2",
    "entity_physical_address",
    "entity_mailing_address",
    "email_identifier_1",
    "email_identifier_2",
    "website_identifier",
    "phone_identifier",
    "usdot_identifier_1",
    "usdot_identifier_2",
    "fein_identifier_1",
    "fein_identifier_2",
    "create_entity_from_request_fixture",
    "entity_name_request_data_comma",
    "entity_mailing_address_2",
    "entity_name_request",
    "entity_name_request_data",
    "entity_name_2_request",
    "entity_name_2_request_data",
    "entity_name_dba_request",
    "entity_name_dba_request_data",
    "entity_name_dba_2_request",
    "entity_name_dba_2_request_data",
    "entity_name_request_not_verified",
    "entity_name_request_not_verified_data",
    "entity_premises_request",
    "entity_premises_request_data",
    "entity_premises_2_request",
    "entity_premises_2_request_data",
    "entity_premises_mailing_request",
    "entity_premises_mailing_2_request",
    "entity_premises_mailing_request_data",
    "entity_premises_undefined_request",
    "entity_premises_undefined_request_data",
    "entity_physical_address_2",
    "usdot_request",
    "usdot_request_data",
    "usdot_2_request_data",
    "fein_request",
    "fein_request_data",
    "fein_2_request",
    "fein_2_request_data",
    "epa_request",
    "epa_request_data",
    "phone_request",
    "phone_request_data",
    "entities_relation_active_request_bidirectional",
    "entities_relation_active_request_directional",
    "entities_relation_inactive_request_bidirectional",
    "entities_relation_inactive_request_directional",
    "entity_with_relations_request",
    "other_industry_request_data",
    "retail_industry_request_data",
    "finance_industry_request_data",
    "retail_industry_request",
    "enrich_request_and_create_entity_fixture",
    "cage_request",
    "cage_2_request",
    "cage_request_data",
    "cage_2_request_data",
    "uei_request",
    "uei_request_data",
    "fda_fei_request",
    "fda_fei_request_data",
    "duns_request",
    "duns_request_data",
    "sam_request",
    "sam_request_data",
    "open_corporates_id_request",
    "open_corporates_id_request_data",
    "open_corporates_id_2_request",
    "open_corporates_id_2_request_data",
    "po_box_request",
    "po_box_request_data",
    "email_request",
    "email_request_data",
    "website_request",
    "website_request_data",
    "owner_request",
    "owner_request_data",
    "entity_name_request_with_address_data",
    "entity_name_2_request_with_address_data",
    "entity_premises_3_request",
    "entity_premises_3_request_data",
    "entity_area_florida_request_data",
    "entity_area_new_york_request_data",
    "entity_area_texas_request_data",
    "entity_area_us_data",
    "entity_undefined_new_york_request",
    "entity_name_3_request",
    "entity_name_3_request_data",
    "entity_name_dba_3_request",
    "entity_name_dba_3_request_data",
)
