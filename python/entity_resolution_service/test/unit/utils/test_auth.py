import unittest
from unittest.mock import patch

import flask

from src.utils.auth import (
    _extract_entity_ids_from_data,
    _extract_organization_id_from_data,
    _extract_source_ids_from_data,
    check_organization,
)


class TestAuthUtils(unittest.TestCase):
    def setUp(self):
        self.app = flask.Flask(__name__)
        self.app.testing = True
        self.app.config["DEBUG"] = True

    def test_extract_organization_id_from_data(self):
        data = {"organization_id": 123}
        self.assertEqual(_extract_organization_id_from_data(data), 123)

        data = {"source": {"organization_id": 456}}
        self.assertEqual(_extract_organization_id_from_data(data), 456)

        data = {"sources": [{"organization_id": 789}]}
        self.assertEqual(_extract_organization_id_from_data(data), 789)

        data = {"level1": {"level2": {"level3": {"organization_id": 999}}}}
        self.assertEqual(_extract_organization_id_from_data(data), 999)

        data = {"items": [{"user": {"organization_id": 111}}]}
        self.assertEqual(_extract_organization_id_from_data(data), 111)

        data = {"other_field": "value"}
        self.assertIsNone(_extract_organization_id_from_data(data))

    def test_extract_entity_ids_from_data(self):
        data = {"entity_id": "123e4567-e89b-12d3-a456-426614174000"}
        self.assertEqual(_extract_entity_ids_from_data(data), {"123e4567-e89b-12d3-a456-426614174000"})

        data = {
            "entity_id_1": "123e4567-e89b-12d3-a456-426614174001",
            "entity_id_2": "123e4567-e89b-12d3-a456-426614174002",
            "primary_entity_id": "123e4567-e89b-12d3-a456-426614174003",
        }
        self.assertEqual(
            _extract_entity_ids_from_data(data),
            {
                "123e4567-e89b-12d3-a456-426614174001",
                "123e4567-e89b-12d3-a456-426614174002",
                "123e4567-e89b-12d3-a456-426614174003",
            },
        )

        data = {"entity_ids": ["123e4567-e89b-12d3-a456-426614174004", "123e4567-e89b-12d3-a456-426614174005"]}
        self.assertEqual(
            _extract_entity_ids_from_data(data),
            {"123e4567-e89b-12d3-a456-426614174004", "123e4567-e89b-12d3-a456-426614174005"},
        )

        data = {"data": {"entity": {"entity_id": "123e4567-e89b-12d3-a456-426614174006"}}}
        self.assertEqual(_extract_entity_ids_from_data(data), {"123e4567-e89b-12d3-a456-426614174006"})

        data = {"items": [{"entity_id": "123e4567-e89b-12d3-a456-426614174007"}]}
        self.assertEqual(_extract_entity_ids_from_data(data), {"123e4567-e89b-12d3-a456-426614174007"})

    def test_extract_source_ids_from_data(self):
        data = {"source_id": "123e4567-e89b-12d3-a456-426614174000"}
        self.assertEqual(_extract_source_ids_from_data(data), {"123e4567-e89b-12d3-a456-426614174000"})

        data = {"submission_id": "123e4567-e89b-12d3-a456-426614174001"}
        self.assertEqual(_extract_source_ids_from_data(data), {"123e4567-e89b-12d3-a456-426614174001"})

        data = {"data": {"source": {"source_id": "123e4567-e89b-12d3-a456-426614174002"}}}
        self.assertEqual(_extract_source_ids_from_data(data), {"123e4567-e89b-12d3-a456-426614174002"})

        data = {"items": [{"source_id": "123e4567-e89b-12d3-a456-426614174003"}]}
        self.assertEqual(_extract_source_ids_from_data(data), {"123e4567-e89b-12d3-a456-426614174003"})

    def test_check_organization_with_matching_request(self):
        with self.app.test_request_context(
            "/test", headers={"Userorganizationid": "123"}, json={"data": {"organization_id": 123}}
        ):

            @check_organization()
            def test_func():
                return {"success": True}, 200

            result = test_func()
            self.assertEqual(result, ({"success": True}, 200))

    @patch("src.utils.auth.logger")
    def test_check_organization_with_mismatched_request(self, mock_logger):
        with self.app.test_request_context(
            "/test", headers={"Userorganizationid": "123"}, json={"data": {"organization_id": 456}}
        ):

            @check_organization()
            def test_func():
                return {"success": True}, 200

            result = test_func()
            self.assertEqual(result, ({"success": True}, 200))
            mock_logger.error.assert_called_with(
                "Organization id mismatch between header and request data",
                header_org_id=123,
                request_org_id=456,
            )

    def test_check_organization_with_matching_response(self):
        with self.app.test_request_context("/test", headers={"Userorganizationid": "123"}):

            @check_organization()
            def test_func():
                return {"organization_id": 123}, 200

            result = test_func()
            self.assertEqual(result, ({"organization_id": 123}, 200))

    @patch("src.utils.auth.logger")
    def test_check_organization_with_mismatched_response(self, mock_logger):
        with self.app.test_request_context("/test", headers={"Userorganizationid": "123"}):

            @check_organization()
            def test_func():
                return {"organization_id": 456}, 200

            result = test_func()
            self.assertEqual(result, ({"organization_id": 456}, 200))
            mock_logger.error.assert_called_with(
                "Organization id mismatch between header and response data",
                header_org_id=123,
                response_org_id=456,
            )

    def test_check_organization_with_parameter_name(self):
        with self.app.test_request_context("/test", headers={"Userorganizationid": "123"}):

            @check_organization(parameter_name="org_id")
            def test_func(org_id=123):
                return {"success": True}, 200

            result = test_func(org_id=123)
            self.assertEqual(result, ({"success": True}, 200))

    def test_check_organization_with_cross_org_access(self):
        with self.app.test_request_context(
            "/test",
            headers={"Userorganizationid": "123", "Usercrossorganizationaccess": "true"},
            json={"data": {"organization_id": 456}},
        ):

            @check_organization()
            def test_func():
                return {"organization_id": 456}, 200

            result = test_func()
            self.assertEqual(result, ({"organization_id": 456}, 200))

    @patch("src.utils.auth._validate_entity_organization_id")
    def test_check_organization_with_entity_id_validation(self, mock_validate):
        mock_validate.return_value = (False, {789})

        with self.app.test_request_context(
            "/test",
            method="POST",
            headers={"Userorganizationid": "123"},
            json={"entity_id": "123e4567-e89b-12d3-a456-426614174000"},
        ), patch("src.utils.auth.logger") as mock_logger:

            @check_organization()
            def test_func():
                return {"success": True}, 200

            result = test_func()
            self.assertEqual(result, ({"success": True}, 200))
            mock_logger.error.assert_called_with(
                "Organization id mismatch between header and entities referenced in request",
                header_org_id=123,
                entity_ids=["123e4567-e89b-12d3-a456-426614174000"],
                mismatched_org_ids=[789],
            )

    @patch("src.utils.auth._validate_source_organization_id")
    def test_check_organization_with_source_id_validation(self, mock_validate):
        mock_validate.return_value = (False, {789})

        with self.app.test_request_context(
            "/test",
            method="POST",
            headers={"Userorganizationid": "123"},
            json={"source_id": "123e4567-e89b-12d3-a456-426614174000"},
        ), patch("src.utils.auth.logger") as mock_logger:

            @check_organization()
            def test_func():
                return {"success": True}, 200

            result = test_func()
            self.assertEqual(result, ({"success": True}, 200))
            mock_logger.error.assert_called_with(
                "Organization id mismatch between header and sources referenced in request",
                header_org_id=123,
                source_ids=["123e4567-e89b-12d3-a456-426614174000"],
                mismatched_org_ids=[789],
            )


if __name__ == "__main__":
    unittest.main()
