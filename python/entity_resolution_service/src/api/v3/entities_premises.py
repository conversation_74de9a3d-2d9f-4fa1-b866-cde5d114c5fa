from http import HTTPStatus
from typing import Dict, Optional
from uuid import UUID

import flask
from common.utils.logging import log_function_result
from infrastructure_common.logging import get_logger
from marshmallow import ValidationError
from werkzeug.exceptions import Conflict, HTTPException

from src.api.v3.models.entity import (
    CoordinatesAssigmentRequest,
    CoordinatesAssigmentResponse,
    EntitiesPremisesDeduplicateRequest,
)
from src.api.v3.schemas.entity import (
    ContextKeyEnum,
    CoordinatesAssigmentRequestSchema,
    CoordinatesAssigmentResponseSchema,
    EntitiesPremisesDeduplicateRequestSchema,
    EntitiesPremisesDeduplicateResponseSchema,
    EntityResponseSchema,
    SourceRequestSchema,
)
from src.logic.entity_premises_profile_deduplicator import PremisesProfileDeduplicator
from src.logic.entity_premises_service import EntityPremisesService
from src.logic.exceptions import CouldNotResolvePremisesFromAddress
from src.schemas.submission_premises_coordinates import (
    SubmissionPremisesCoordinatesSchema,
)
from src.utils.constants import HTTPResponse
from src.utils.error_handling import handle_http_exception

logger = get_logger()
exclude = [
    "entity.premises.premises.entity_tenants_ids",
    "entity.sources",
    "entity.relations_to",
    "entity.relations_from",
]


def get_premises_profile_deduplicator() -> PremisesProfileDeduplicator:
    return flask.current_app.premises_profile_deduplicator  # type: ignore


def get_entity_premises_service() -> EntityPremisesService:
    return flask.current_app.entity_premises_service  # type: ignore


SOURCE_SCHEMA = SourceRequestSchema()
ENTITY_RESPONSE_SCHEMA = EntityResponseSchema()


# POST /entities/premises/deduplicate
@log_function_result
def deduplicate_entity_premises(
    body: Dict,
    call_origin: Optional[str] = None,
) -> HTTPResponse:
    try:
        request: EntitiesPremisesDeduplicateRequest = EntitiesPremisesDeduplicateRequestSchema().load(body)
    except Exception as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    try:
        result = get_premises_profile_deduplicator().deduplicate_entity_premises(request)
        return EntitiesPremisesDeduplicateResponseSchema().dump(result), HTTPStatus.OK
    except Conflict as e:
        logger.warning("Conflict on deduplicate_entity_premises()", call_origin=call_origin, exc_info=e)
        flask.abort(HTTPStatus.CONFLICT, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on deduplicate_entity_premises()", call_origin=call_origin)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /entities/premises/coordinates
@log_function_result
def assign_coordinates_to_entity_premises(
    body: Dict,
    call_origin: Optional[str] = None,
    skip_cache: Optional[bool] = False,  # - not used directly but impacts cache usage down the stream
) -> HTTPResponse:
    log = logger.bind(call_origin=call_origin, body=body)
    try:
        request: CoordinatesAssigmentRequest = CoordinatesAssigmentRequestSchema().load(body)
    except Exception as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    try:
        response = get_entity_premises_service().assign_coordinates_to_entity_premises(request)
        created = response.coordinates_assignment.premises_id != request.premises_id
        context = _create_context_for_coordinate_assigment(response)
        data = CoordinatesAssigmentResponseSchema(exclude=exclude, context=context).dump(response)
        code = HTTPStatus.CREATED if created else HTTPStatus.OK
        return data, code
    except Conflict as e:
        log.warning(
            "Conflict on assign_coordinates_to_entity_premises()",
            exc_info=e,
        )
        flask.abort(HTTPStatus.CONFLICT, str(e))
    except CouldNotResolvePremisesFromAddress as e:
        log.warning(
            "CouldNotResolvePremisesFromAddress on assign_coordinates_to_entity_premises()",
            exc_info=e,
        )
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except ValidationError as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        log.exception("Unexpected exception on assign_coordinates_to_entity_premises()")
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# GET /entities/premises/coordinates
@log_function_result
def get_coordinates_for_entity_premises(
    submission_id: str,
    call_origin: Optional[str] = None,
) -> HTTPResponse:
    log = logger.bind(call_origin=call_origin, submission_id=submission_id)
    try:
        response = get_entity_premises_service().get_coordinates_for_entity_premises(UUID(submission_id))
        data = SubmissionPremisesCoordinatesSchema().dump(response, many=True)
        return data, HTTPStatus.OK
    except ValueError as e:
        log.warning(
            "ValueError on for `submission_id`",
            exc_info=e,
        )
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except HTTPException as e:
        log.warning(
            "HTTPException on get_coordinates_for_entity_premises()",
            exc_info=e,
        )


def _create_context_for_coordinate_assigment(data: CoordinatesAssigmentResponse) -> Dict:
    return {
        ContextKeyEnum.PREMISES_ID_TO_COORDINATES.value: {
            str(data.coordinates_assignment.premises_id): data.coordinates_assignment.coordinates
        },
    }
