from http import HTT<PERSON><PERSON>us
from typing import Dict, Iterable, Optional
from uuid import UUID

import flask
from infrastructure_common.logging import get_logger
from werkzeug.exceptions import HTTPException

from src.api.v3.schemas.snapshots import CreateSnapshotsRequestSchema
from src.logic.entity_enrichment import CreateDatabaseObjectException
from src.logic.entity_snapshot import (
    create_entity_snapshot,
    create_entity_snapshots,
    get_entity_snapshot_by_id,
    get_entity_snapshots_by_ids,
)
from src.logic.entity_snapshot import (
    get_snapshots_for_entity as get_snapshots_for_entity_logic,
)
from src.schemas.entity_snapshot import EntitySnapshotSchema
from src.utils.constants import HTTPResponse
from src.utils.error_handling import handle_http_exception

logger = get_logger()


# GET /snapshots/{id}
def get_snapshot(snapshot_id: UUID) -> HTTPResponse:
    """Retrieve Entity Snapshot by its ID."""

    try:
        snapshot = get_entity_snapshot_by_id(snapshot_id)
        return EntitySnapshotSchema().dump(snapshot), HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on get_snapshot()", snapshot_id=snapshot_id)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# GET /snapshots
def get_snapshots(snapshot_ids: Iterable[UUID]) -> HTTPResponse:
    """Retrieve multiple Entity Snapshots by their IDs."""

    try:
        snapshots = get_entity_snapshots_by_ids(snapshot_ids)
        return EntitySnapshotSchema().dump(snapshots, many=True), HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on get_snapshots()", snapshot_ids=snapshot_ids)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /entities/{id}/snapshots
def create_snapshot_for_entity(entity_id: UUID, body: Optional[Dict]) -> HTTPResponse:
    """Create a snapshot for the Entity with the given ID."""

    try:
        snapshot = create_entity_snapshot(entity_id)
        return EntitySnapshotSchema().dump(snapshot), HTTPStatus.CREATED
    except CreateDatabaseObjectException as e:
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on create_snapshot_for_entity()", entity_id=entity_id, body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /entities/snapshots
def bulk_create_snapshots_for_entities(body: Dict) -> HTTPResponse:
    """Create a snapshots for the Entities with the given IDs."""
    try:
        request = CreateSnapshotsRequestSchema().load(body)
    except Exception as e:
        logger.exception("Bad request on create_snapshots_for_entities", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        snapshots = create_entity_snapshots(request.entity_ids)
        return EntitySnapshotSchema().dump(snapshots, many=True), HTTPStatus.CREATED
    except CreateDatabaseObjectException as e:
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception(
            "Unexpected exception on create_snapshots_for_entities()", entity_ids=request.entity_ids, body=body
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# GET /snapshots/entity/{entity_id}
def get_snapshots_for_entity(
    entity_id: UUID, lightweight: bool = False, allow_composed_nodes: bool = True
) -> HTTPResponse:
    """Retrieve Entity Snapshots for given entity ID."""
    try:
        snapshots = get_snapshots_for_entity_logic(entity_id, allow_composed_nodes)

        if lightweight:
            only = ["id", "entity_id", "created_at"]
        else:
            only = ["id", "entity_id", "created_at", "entity"]

        return EntitySnapshotSchema(only=only).dump(snapshots, many=True), HTTPStatus.OK
    except HTTPException as e:
        if e.code == HTTPStatus.INTERNAL_SERVER_ERROR:
            import inspect

            logger.exception("Unexpected exception on", frame=inspect.currentframe().f_code.co_name)
        flask.abort(e.code, str(e))
    except Exception as e:
        logger.exception("Unexpected exception on get_snapshots_for_entity()", entity_id=entity_id)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
