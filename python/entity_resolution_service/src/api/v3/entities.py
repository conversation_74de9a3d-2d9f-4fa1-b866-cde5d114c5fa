from http import HTTPStatus
from typing import Dict, Iterable, List, Optional, Tuple
from uuid import UUID

import flask
from common.utils.logging import log_function_result
from flask import current_app
from infrastructure_common.logging import get_logger
from static_common.enums.entity import EntityCategory
from werkzeug.exceptions import Conflict, HTTPException

from src.api.v3.models.entity import (
    EntitiesEnvelope,
    EntitiesLinkingRequest,
    EntityNameRequest,
    EntityRequest,
)
from src.api.v3.models.search import EntitySearchRequest, SearchEntityResults
from src.api.v3.schemas.entity import (
    ContextKeyEnum,
    EntitiesEnvelopeSchema,
    EntitiesLinkingRequestSchema,
    EntityIndustryRequestSchema,
    EntityIndustrySchema,
    EntityNamePatchSchema,
    EntityNameRequestSchema,
    EntityNameSchema,
    EntityPremisesPatchSchema,
    EntityPremisesRequestSchema,
    EntityPremisesSchema,
    EntityRedirectsRequestSchema,
    EntityRequestSchema,
    EntityResponseSchema,
    ExternalIdentifierRequestSchema,
    ExternalIdentifierSchema,
)
from src.api.v3.schemas.search import (
    EntitySearchRequestSchema,
    SearchEntityResultsSchema,
)
from src.api.v3.schemas.source import SourceRequestSchema
from src.events import on_entities_linking
from src.logic.entity import (
    assign_premises_to_entity,
    create_entity,
    create_entity_names,
    create_external_identifiers,
    delete_entities_created_by_sub,
    delete_entity_name_by_id,
    delete_external_identifier_by_id,
    get_entities_redirects_ids,
    hard_delete_entity,
    replace_entity_premises,
    replace_industries,
    search_and_enrich_entity,
)
from src.logic.entity import search_entities as search_entities_logic
from src.logic.entity import (
    soft_delete_entity,
    unassign_entity_premises_by_id,
    update_entity_name,
    update_entity_premises,
)
from src.logic.entity_cache import maybe_add_to_cache, maybe_get_from_cache
from src.logic.entity_dao import EntityDAO
from src.logic.entity_retriever import (
    get_entities_by_ids_with_contextual_data,
    get_entity_by_id,
    list_entities_by_premises_ids,
)
from src.logic.exceptions import (
    CircularRedirectionsBetweenEntities,
    CouldNotResolvePremisesFromAddress,
    CreateDatabaseObjectException,
    DeleteEntityReferencedByReport,
    EntityRequestWithConflictingPremises,
    InconsistentEntity,
    MissingVESRequiredData,
    RedirectToConflictingEntity,
    RedirectToEntityAlreadyExists,
    RedirectToNotExistingEntityException,
    RedirectToPremisesProfileException,
    RedirectToSelfEntity,
    RedirectToTooFarEntity,
)
from src.logic.source_dao import SourceManager
from src.models import SubmissionPremisesCoordinates
from src.models.source import Source
from src.utils.constants import (
    CREATABLE_ENTITY_CATEGORIES,
    EMPTY_NAME,
    SOURCE_RELATED_ENTITY_CATEGORIES,
    HTTPResponse,
    RelationSetEnum,
)
from src.utils.db import no_expire_on_commit
from src.utils.error_handling import handle_http_exception
from src.utils.local_storage_values import LocalStorageKeys
from src.utils.locks import MultiLock, SourceLock

logger = get_logger()
search_result_schema = SearchEntityResultsSchema(
    exclude=[
        "data.entity.premises.premises.entity_tenants_ids",
        "data.entity.sources",
        "data.entity.relations_to",
        "data.entity.relations_from",
    ],
    context={
        ContextKeyEnum.WITH_RELATIONS.value: RelationSetEnum.NONE,
        ContextKeyEnum.WITH_ADDRESSES_RELEVANT_FIELDS_ONLY.value: True,
        ContextKeyEnum.PREMISES_ID_TO_COORDINATES.value: {},
    },
)


def _should_lock_by_source(entity: Optional, request: EntityRequest) -> Tuple[bool, Optional[Source]]:
    if not entity and request.source and set(request.categories).issubset(set(SOURCE_RELATED_ENTITY_CATEGORIES)):
        if source := SourceManager.get_one_or_create_if_unique(request.source):
            return True, source
    return False, None


# POST /entities
@log_function_result
def get_or_create_entity(
    body: Dict,
    with_relations: Optional[RelationSetEnum] = RelationSetEnum.NONE,
    skip_cache: Optional[bool] = False,
    call_origin: Optional[str] = None,
    expand: Optional[Iterable[str]] = None,
    high_geocoding_confidence: Optional[bool] = False,
    # not used directly but used by CallOriginAndSkipCacheEnrichmentInterceptor and later as cross-cutting concern:
    ignore_al2_mode: Optional[bool] = False,
) -> HTTPResponse:
    """Create a new entity or return the existing one if it already exists"""
    expand = set(expand) if expand else set()

    try:
        skip_cache = body.pop("skip_cache", skip_cache)
        # update if skip_cache from body
        LocalStorageKeys.SkipCache.set(LocalStorageKeys.SkipCache.get() or skip_cache)
        request = EntityRequestSchema().load(body)
        request_names: List[EntityNameRequest] = request.names or []
        names = [name.value for name in request_names]
        if EMPTY_NAME in names and not set(request.categories).intersection(
            {EntityCategory.SHELL, EntityCategory.REAL_ESTATE}
        ):
            flask.abort(HTTPStatus.BAD_REQUEST, f"{EMPTY_NAME} in get or create entity for non shell entity")
        request.categories = request.categories or [EntityCategory.LOCATION]  # default to location
    except Exception as e:
        logger.exception("Bad request on get_or_create_entity()", body=body, call_origin=call_origin)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    cache_key = (str(request) + str(with_relations)).upper()  # get it as case-insensitive
    if cached_value := maybe_get_from_cache(cache_key, skip_cache):
        return cached_value, HTTPStatus.OK

    locks: List[MultiLock] = []
    try:
        with no_expire_on_commit():
            load_relations = with_relations != RelationSetEnum.NONE
            entity = search_and_enrich_entity(
                request, high_geocoding_confidence=high_geocoding_confidence, load_relations=load_relations
            )
            # for some of the entities we would like ensure single exists per source
            should_lock, source = _should_lock_by_source(entity, request)
            if should_lock:
                locks.append(MultiLock(source.get_locking_ids(), SourceLock, raise_exception=True).acquire())
                # try again after acquiring lock in case another request created the entity
                entity = search_and_enrich_entity(
                    request, high_geocoding_confidence=high_geocoding_confidence, load_relations=load_relations
                )

            status = HTTPStatus.OK
            if not entity:
                _ensure_single_category(request)
                entity = create_entity(request)
                status = HTTPStatus.CREATED
            entity_response_schema = _get_entity_response_schema(with_relations, with_tenants=False, expand=expand)
            raw_response = entity_response_schema.dump(entity)
            maybe_add_to_cache(cache_key, entity, raw_response)
            return raw_response, status
    except EntityRequestWithConflictingPremises as e:
        flask.abort(HTTPStatus.CONFLICT, str(e))
    except MissingVESRequiredData as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except CouldNotResolvePremisesFromAddress as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except InconsistentEntity as e:
        flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, str(e))
    except CreateDatabaseObjectException as e:
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on get_or_create_entity()", body=body, call_origin=call_origin)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
    finally:
        for lock in locks:
            lock.release_acquired()


def _ensure_single_category(request: EntityRequest):
    """Creation should be done only for single category"""
    for category in CREATABLE_ENTITY_CATEGORIES:
        if category in request.categories:
            request.categories = [category]
            return
    request.categories = [EntityCategory.LOCATION]  # default to location


# GET /entities
def bulk_get_entities(
    entity_ids: Iterable[UUID],
    with_tenants: bool = False,
    with_relations: Optional[RelationSetEnum] = RelationSetEnum.NONE,
    call_origin: Optional[str] = None,
    expand: Optional[Iterable[str]] = None,
    organization_id: Optional[int] = None,
) -> HTTPResponse:
    if not entity_ids:
        logger.error("Bad request on bulk_get_entities(): missing entity_ids")
        flask.abort(HTTPStatus.BAD_REQUEST, "Missing `entities_ids` filter value")

    expand = set(expand) if expand else set()

    try:
        load_relations = with_relations != RelationSetEnum.NONE or organization_id is not None
        entities, coordinates = get_entities_by_ids_with_contextual_data(
            entity_ids, with_tenants=with_tenants, with_relations=load_relations, organization_id=organization_id
        )
        envelope = EntitiesEnvelope(count=len(entities), entities=entities)
        schema = _get_entities_envelope_schema(with_relations, with_tenants, expand, coordinates, organization_id)
        return schema.dump(envelope), HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on bulk_get_entities()", entity_ids=entity_ids, call_origin=call_origin)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# GET /entities/by_premises
def bulk_get_entities_with_premises(premises_ids: Iterable[UUID], types: Optional[List[str]] = None) -> HTTPResponse:
    if not premises_ids:
        logger.error("Bad request on bulk_get_entities_with_premises(): missing premises_ids")
        flask.abort(HTTPStatus.BAD_REQUEST, "Missing `premises_ids` filter value")

    try:
        entities = list_entities_by_premises_ids(premises_ids, types)
        envelope = EntitiesEnvelope(count=len(entities), entities=entities)
        return (
            _get_entities_envelope_schema(RelationSetEnum.NONE, with_tenants=False, expand=set()).dump(envelope),
            HTTPStatus.OK,
        )
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on bulk_get_entities_with_premises()", premises_ids=premises_ids)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /search
@log_function_result
def search_entities(
    body: Dict,
    skip_cache: Optional[bool] = False,
    conflict_on_range: Optional[bool] = False,
    call_origin: Optional[str] = None,
    # not used directly but used by CallOriginAndSkipCacheEnrichmentInterceptor and later as cross-cutting concern:
    ignore_al2_mode: Optional[bool] = False,
) -> HTTPResponse:
    """Search from existing entities"""
    request: Optional[EntitySearchRequest] = None

    try:
        skip_cache = body.pop("skip_cache", skip_cache)
        # update if skip_cache from body
        LocalStorageKeys.SkipCache.set(LocalStorageKeys.SkipCache.get() or skip_cache)
        conflict_on_range = body.pop("conflict_on_range", conflict_on_range)
        request: EntitySearchRequest = EntitySearchRequestSchema().load(body)
    except Exception as e:
        logger.exception("Bad request on search_entities()", body=body, call_origin=call_origin)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    cache_key = str(request).upper()  # get it as case-insensitive
    if cached_value := maybe_get_from_cache(cache_key, skip_cache):
        return cached_value, HTTPStatus.OK

    try:
        response = search_entities_logic(request, conflict_on_range, include_resolution_info=True)
        _preload_all_relations(response)
        raw_response = search_result_schema.dump(response, many=True)
        maybe_add_to_cache(cache_key, response, raw_response)
        return raw_response, HTTPStatus.OK
    except CreateDatabaseObjectException as e:
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on search_entities()", body=body, call_origin=call_origin)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


def _preload_all_relations(response: List[SearchEntityResults]) -> None:
    entity_ids = []
    for search_result in sum([r.data for r in response], []):
        if search_result and search_result.entity:
            entity_ids.append(search_result.entity.id)
    if entity_ids:
        current_app.entity_dao.load_relations_in_addition_to_scoring(entity_ids)


# GET /entities/{id}
def get_entity(
    entity_id: str,
    with_tenants: bool = False,
    with_relations: Optional[RelationSetEnum] = RelationSetEnum.NONE,
    expand: Iterable[str] | None = None,
) -> HTTPResponse:
    """Retrieve a single entity by its ID."""
    expand = set(expand) if expand else set()

    if with_relations is None:
        with_relations = RelationSetEnum.NONE
    try:
        load_relations = with_relations != RelationSetEnum.NONE
        entity = get_entity_by_id(UUID(entity_id), with_tenants=with_tenants, with_relations=load_relations)
        schema = _get_entity_response_schema(with_relations, with_tenants, expand=expand)
        return schema.dump(entity), HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on get_entity()", entity_id=entity_id)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /entities/{id}/redirect_to/{id}
@log_function_result
def redirect_entity(entity_id: str, redirect_to_id: str, body: Optional[Dict] = None) -> HTTPResponse:
    """Soft delete scenario. Set redirect to entity and update any entity that was pointing to deleted business."""

    try:
        soft_delete_entity(UUID(entity_id), UUID(redirect_to_id))
    except RedirectToNotExistingEntityException as e:
        flask.abort(HTTPStatus.NOT_FOUND, str(e))
    except (
        RedirectToSelfEntity,
        RedirectToEntityAlreadyExists,
        CircularRedirectionsBetweenEntities,
        RedirectToConflictingEntity,
        RedirectToTooFarEntity,
        MissingVESRequiredData,
        RedirectToPremisesProfileException,
    ) as e:
        flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on redirect_entity()", entity_id=entity_id)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"message": f"Redirected: {entity_id}"}, HTTPStatus.OK


# DELETE /entities/{id}
@log_function_result
def delete_entity(entity_id: str) -> HTTPResponse:
    """Hard delete scenario. Delete the entity and all entities pointing on it."""

    try:
        deleted_entity_ids = hard_delete_entity(UUID(entity_id))
    except DeleteEntityReferencedByReport as e:
        return {
            "message": str(e),
            "entity_id": str(e.entity_id),
            "report_ids": [str(report_id) for report_id in e.report_ids],
        }, HTTPStatus.CONFLICT
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on delete_entity()", entity_id=entity_id)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"deleted_entity_ids": deleted_entity_ids}, HTTPStatus.OK


# POST /entities/{id}/names
@log_function_result
def bulk_create_entity_names(entity_id: str, body: List) -> HTTPResponse:
    """Create multiple EntityNames for the Entity at once."""

    request_schema = None

    try:
        request_schema = EntityNameRequestSchema().load(body, many=True)
    except Exception as e:
        logger.exception("Bad request on bulk_create_entity_names()", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        entity_names = create_entity_names(UUID(entity_id), request_schema)
        return EntityNameSchema().dump(entity_names, many=True), HTTPStatus.CREATED
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on bulk_create_entity_names()", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# DELETE /entities/{id}/names/{id}
@log_function_result
def delete_entity_name(entity_id: str, entity_name_id: str) -> HTTPResponse:
    """Delete EntityName with given id under given entity."""

    try:
        delete_entity_name_by_id(UUID(entity_id), UUID(entity_name_id))
    except MissingVESRequiredData as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception(
            "Unexpected exception on delete_entity_name()", entity_id=entity_id, entity_name_id=entity_name_id
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"message": f"Deleted: {entity_name_id}"}, HTTPStatus.NO_CONTENT


# PATCH /entities/{id}/names/{id}
@log_function_result
def patch_entity_name(entity_id: str, entity_name_id: str, body: Dict) -> HTTPResponse:
    request_schema = None

    try:
        request_schema = EntityNamePatchSchema().load(body)
    except Exception as e:
        logger.exception("Bad request on patch_entity_name()", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        entity_name = update_entity_name(UUID(entity_id), UUID(entity_name_id), request_schema)
        return EntityNameSchema().dump(entity_name), HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on patch_entity_name()", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /entities/{id}/premises
@log_function_result
def bulk_assign_premises_to_entity(entity_id: str, body: List) -> HTTPResponse:
    """Create multiple EntityPremises for the Entity at once."""

    request_schema = None

    try:
        request_schema = EntityPremisesRequestSchema().load(body, many=True)
    except Exception as e:
        logger.exception("Bad request on bulk_assign_premises_to_entity()", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        entity_premises = assign_premises_to_entity(UUID(entity_id), request_schema)
        return EntityPremisesSchema().dump(entity_premises, many=True), HTTPStatus.CREATED
    except (MissingVESRequiredData, CouldNotResolvePremisesFromAddress, InconsistentEntity) as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except EntityRequestWithConflictingPremises as e:
        flask.abort(HTTPStatus.CONFLICT, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on bulk_assign_premises_to_entity()", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# DELETE /entities/{id}/premises/{id}
@log_function_result
def unassign_premises_from_entity(entity_id: str, entity_premises_id: str) -> HTTPResponse:
    """Delete EntityPremises with given id under given entity."""

    try:
        unassign_entity_premises_by_id(UUID(entity_id), UUID(entity_premises_id))
    except MissingVESRequiredData as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on unassign_premises_from_entity()", entity_id=entity_id)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"message": f"Deleted: {entity_premises_id}"}, HTTPStatus.NO_CONTENT


# PATCH /entities/{id}/premises/{id}
@log_function_result
def update_premises_of_entity(entity_id: str, entity_premises_id: str, body: Dict) -> HTTPResponse:
    request_schema = None

    try:
        request_schema = EntityPremisesPatchSchema().load(body)
    except Exception as e:
        logger.exception("Bad request on update_premises_of_entity()", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        entity_premises = update_entity_premises(UUID(entity_id), UUID(entity_premises_id), request_schema)
        return EntityPremisesSchema().dump(entity_premises), HTTPStatus.OK
    except MissingVESRequiredData as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on update_premises_of_entity()", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /entities/{id}/identifiers
def bulk_create_external_identifiers(entity_id: str, body: List) -> HTTPResponse:
    """Create multiple ExternalIdentifiers for the Entity at once."""

    request_schema = None

    try:
        request_schema = ExternalIdentifierRequestSchema().load(body, many=True)
    except Exception as e:
        logger.exception("Bad request on bulk_create_external_identifiers()", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        identifiers = create_external_identifiers(UUID(entity_id), request_schema)
        return ExternalIdentifierSchema().dump(identifiers, many=True), HTTPStatus.CREATED
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on bulk_create_external_identifiers()", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /entities/{id}/identifiers/upsert
def bulk_upsert_external_identifiers(entity_id: str, body: List) -> HTTPResponse:
    """
    Create multiple ExternalIdentifiers for the Entity at once.
    If ExternalIdentifier with given type and value already exists, update it with a new value.

    In case of many incoming values, we update using the last.

    Example:
    | current value | new values | result |
    |---------------|------------|--------|
    | 123           | [123, 456] | 456    |
    | 123           | [789, 456] | 456    |
    | 123           | [9, 8, 7]  | 7      |
    | 123           | [123]      | 123    |
    """

    request_schema = None
    try:
        request_schema = ExternalIdentifierRequestSchema().load(body, many=True)
    except Exception as e:
        logger.exception("Bad request on bulk_create_external_identifiers()", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        identifiers = create_external_identifiers(entity_id=UUID(entity_id), items=request_schema, upsert=True)
        return ExternalIdentifierSchema().dump(identifiers, many=True), HTTPStatus.CREATED
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on bulk_create_external_identifiers()", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# DELETE /entities/{id}/identifiers/{id}
@log_function_result
def delete_external_identifier(entity_id: str, external_identifier_id: str) -> HTTPResponse:
    """Delete ExternalIdentifier with given id under given entity."""

    try:
        delete_external_identifier_by_id(UUID(entity_id), UUID(external_identifier_id))
    except MissingVESRequiredData as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on delete_external_identifier()", entity_id=entity_id)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"message": f"Deleted: {external_identifier_id}"}, HTTPStatus.NO_CONTENT


# DELETE /entities/submission/{submission_id}
@log_function_result
def delete_entities_created_by_submission(
    submission_id: str,
    file_id: Optional[str] = None,  # query parameter
    call_origin: Optional[str] = None,  # query parameter
) -> HTTPResponse:
    if submission_id is None:
        flask.abort(HTTPStatus.BAD_REQUEST, "Missing `submission_id` filter value")
    try:
        deleted = delete_entities_created_by_sub(submission_id, file_id)
    except Conflict as e:
        flask.abort(409, str(e))
    except Exception as e:
        logger.exception(
            "Unexpected exception on delete_entities_created_by_submission()",
            submission_id=str(submission_id),
            file_id=file_id,
            call_origin=call_origin,
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
    if len(deleted) != 0:
        return {
            "message": "Deleted shell entities",
            "submission_id": str(submission_id),
            "count": len(deleted),
        }, HTTPStatus.OK
    else:
        return {
            "message": "There was no shell entities to delete",
            "submission_id": str(submission_id),
            "count": 0,
        }, HTTPStatus.OK


# PUT /entities/{id}/entity-industries
def update_entity_industries(entity_id: str, body: List) -> HTTPResponse:
    """Update the list of industries for given entity."""

    request_schema = None

    try:
        request_schema = EntityIndustryRequestSchema().load(body, many=True)
    except Exception as e:
        logger.exception("Bad request on update_entity_industries()", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        entity_industries = replace_industries(UUID(entity_id), request_schema)
        return EntityIndustrySchema().dump(entity_industries, many=True), HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on update_entity_industries()", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# GET /entities/redirects
def get_entities_redirects(body: Dict) -> HTTPResponse:
    """Retrieve a redirects for list of entities"""
    schema = EntityRedirectsRequestSchema()
    try:
        request = schema.load(body)
        redirect_entity_ids = get_entities_redirects_ids(request.entity_ids)
        return redirect_entity_ids, HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on get_entities_redirects", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /entities/entities-linking
def entities_linking(body: Dict) -> HTTPResponse:
    from src.events.events_sender import EventsSender

    request: Optional[EntitiesLinkingRequest] = None
    try:
        request: EntitiesLinkingRequest = EntitiesLinkingRequestSchema().load(body)
    except Exception as e:
        logger.exception("Bad request on entities_linking", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    if request.async_request:
        logger.info("Running entities_linking in async mode", request=request)

        try:
            EventsSender.emit_async_linking_for_ids(request.entity_id, request.entities_ids)
            return {"message": f"Task created for {request=}"}, HTTPStatus.CREATED
        except HTTPException as e:
            handle_http_exception(e)
        except Exception as e:
            flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    else:
        logger.info("Running entities_linking in synchronous mode mode", request=request)

        try:
            on_entities_linking(str(request.entity_id), [str(e_id) for e_id in request.entities_ids])
            return {"message": f"Linked: {request.entity_id} and {request.entities_ids}"}, HTTPStatus.NO_CONTENT
        except HTTPException as e:
            handle_http_exception(e)
        except Exception as e:
            flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


def _create_context(
    with_relations: Optional[RelationSetEnum] = None,
    with_addresses_relevant_fields_only: Optional[bool] = None,
    premises_id_to_coordinates: Optional[Dict[str, Tuple[float, float]]] = None,
    organization_id: Optional[int] = None,
) -> Dict:
    """Create context for marshmallow schemas."""
    if with_relations is None:
        with_relations = RelationSetEnum.BASE
    if with_addresses_relevant_fields_only is None:
        with_addresses_relevant_fields_only = True
    if premises_id_to_coordinates is None:
        premises_id_to_coordinates = {}
    context = {
        ContextKeyEnum.WITH_RELATIONS.value: with_relations,
        ContextKeyEnum.WITH_ADDRESSES_RELEVANT_FIELDS_ONLY.value: with_addresses_relevant_fields_only,
        ContextKeyEnum.PREMISES_ID_TO_COORDINATES.value: premises_id_to_coordinates,
        ContextKeyEnum.ORGANIZATION_ID.value: organization_id,
    }
    return context


ENTITY_RESPONSE_EXCLUDED_BY_DEFAULT = ["relations_to.explanation_summary", "relations_from.explanation_summary"]


def _get_entity_response_schema(with_relations: RelationSetEnum, with_tenants: bool, expand: set[str]):
    exclude = ["sources"]
    if with_relations == RelationSetEnum.NONE:
        exclude.append("relations_to")
        exclude.append("relations_from")
    if not with_tenants:
        exclude.append("premises.premises.entity_tenants_ids")

    exclude.extend(
        excluded_field for excluded_field in ENTITY_RESPONSE_EXCLUDED_BY_DEFAULT if excluded_field not in expand
    )
    return EntityResponseSchema(exclude=exclude, context=_create_context(with_relations))


ENTITIES_ENVELOPE_EXCLUDED_BY_DEFAULT = [
    "entities.relations_to.explanation_summary",
    "entities.relations_from.explanation_summary",
]


def _get_entities_envelope_schema(
    with_relations: RelationSetEnum,
    with_tenants: bool,
    expand: set[str],
    premises_coordinates: List[SubmissionPremisesCoordinates] = None,
    organization_id: Optional[int] = None,
):
    if premises_coordinates is None:
        premises_coordinates = []
    exclude = ["entities.sources"]
    if with_relations == RelationSetEnum.NONE:
        exclude.append("entities.relations_to")
        exclude.append("entities.relations_from")
    if not with_tenants:
        exclude.append("entities.premises.premises.entity_tenants_ids")

    exclude.extend(
        excluded_field for excluded_field in ENTITIES_ENVELOPE_EXCLUDED_BY_DEFAULT if excluded_field not in expand
    )
    premises_id_to_coordinates = {str(pc.premises_id): pc.coordinates for pc in premises_coordinates}
    return EntitiesEnvelopeSchema(
        exclude=exclude,
        context=_create_context(
            with_relations, premises_id_to_coordinates=premises_id_to_coordinates, organization_id=organization_id
        ),
    )


def get_entities_premises(entities_ids: list[str | UUID]) -> HTTPResponse:
    entities_ids = [eid if isinstance(eid, UUID) else UUID(eid) for eid in entities_ids]
    entities_to_premises = EntityDAO.get_entities_premises(entities_ids)

    for missing_entity_id in [str(id) for id in entities_ids if str(id) not in entities_to_premises]:
        entities_to_premises[missing_entity_id] = None

    return {"entities_to_premises": entities_to_premises}, HTTPStatus.OK


# POST /entities/{entity_id}/premises/{entity_premises_id}/{premises_id}
@log_function_result
def replace_premisses(
    entity_id: str,
    entity_premises_id: str,
    premises_id: str,
    body: Dict,
    call_origin: Optional[str] = None,
) -> HTTPResponse:
    try:
        source = SourceRequestSchema().load(body, many=False)
    except Exception as e:
        logger.warning(
            "Bad request on replace_premisses()", body=body, call_origin=call_origin, exc_info=e, source=body
        )
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        response = replace_entity_premises(
            entity_id=UUID(entity_id) if isinstance(entity_id, str) else entity_id,
            entity_premises_id=UUID(entity_premises_id) if isinstance(entity_premises_id, str) else entity_premises_id,
            premises_id=UUID(premises_id) if isinstance(premises_id, str) else premises_id,
            source=source,
        )
        data = _get_entity_response_schema(with_relations=RelationSetEnum.NONE, with_tenants=False, expand=set()).dump(
            response, many=False
        )
        return data, HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on replace_premisses()", body=body, call_origin=call_origin, source=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
