# POST /events
from http import HTTPStatus
from typing import Any
from uuid import UUID

import flask
from flask import current_app
from infrastructure_common.logging import get_logger
from werkzeug.exceptions import HTTPException

from src.api.v3.models.geo import (
    BulkCreateGeoEventsRequest,
    EventsBBoxRequest,
    EventsPointsRequest,
    ZoneBBoxRequest,
    ZonePointsRequest,
)
from src.api.v3.schemas.geo import (
    BulkCreateEventsRequestSchema,
    BulkCreateZonesRequestSchema,
)
from src.logic.exceptions import InconsistentEntity
from src.models import Event, Zone
from src.schemas.event import EventSchema
from src.schemas.zone import ZoneSchema
from src.utils.constants import HTTPResponse
from src.utils.error_handling import handle_http_exception
from src.utils.local_storage_values import LocalStorageKeys

logger = get_logger()

bulk_event_schema = BulkCreateEventsRequestSchema()
bulk_zone_schema = BulkCreateZonesRequestSchema()
events_default_exclude = ["source", "zones"]
event_schema = EventSchema(exclude=events_default_exclude)
zone_default_exclude = ["source"]
zone_schema = ZoneSchema(exclude=zone_default_exclude)


# POST /geo/events
def bulk_upsert_events(body: dict[str, Any], call_origin: str | None = None) -> HTTPResponse:
    try:
        events: BulkCreateGeoEventsRequest = bulk_event_schema.load(body)
    except Exception as e:
        logger.warning("Bad request on bulk_upsert_events()", body=body, call_origin=call_origin)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    try:
        ids = current_app.geo_events_service.bulk_upsert_events(events)  # type: ignore
        # just for easier debugging and testing: return the number of created and failed events
        return {
            "processed": len(list(it for it in ids if it is not None)),
            "failed": len(list(it for it in ids if it is None)),
            "ids": ids,
        }, HTTPStatus.CREATED
    except HTTPException as e:
        handle_http_exception(e)
    except InconsistentEntity as e:
        flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, str(e))
    except Exception as e:
        logger.exception("Unexpected exception on bulk_upsert_events()", body=body, call_origin=call_origin)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /geo/zones
def bulk_upsert_zones(body: dict, call_origin: str | None = None) -> HTTPResponse:
    try:
        events = bulk_zone_schema.load(body)
    except Exception as e:
        logger.warning("Bad request on bulk_upsert_zones()", body=body, call_origin=call_origin)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    try:
        ids = current_app.geo_events_service.bulk_upsert_zones(events)
        return {
            "processed": len(list(it for it in ids if it is not None)),
            "failed": len(list(it for it in ids if it is None)),
            "ids": ids,  # same order as in request
        }, HTTPStatus.CREATED
    except HTTPException as e:
        handle_http_exception(e)
    except InconsistentEntity as e:
        flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, str(e))
    except Exception as e:
        logger.exception("Unexpected exception on bulk_upsert_zones()", body=body, call_origin=call_origin)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# GET /geo/events
def get_events_by_ids(ids: list[UUID], include: list[str], call_origin: str | None = None) -> HTTPResponse:
    try:
        if include:
            fields = set(event_schema.declared_fields.keys())
            if unknown_fields := set(include) - fields:
                _bad_request_for_unknown_fields(call_origin, unknown_fields)

        items: list[Event] = current_app.geo_events_service.get_events_by_ids(ids, include=include)
        if not include:
            result = event_schema.dump(items, many=True)
        else:
            result = EventSchema(exclude=[it for it in events_default_exclude if it not in include]).dump(
                items, many=True
            )
        return result, HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception:
        logger.exception("Unexpected exception on get_events_by_ids()", event_ids=ids, call_origin=call_origin)


# GET /geo/zones
def get_zones_by_ids(ids: list[UUID], include: list[str], call_origin: str | None = None) -> HTTPResponse:
    try:
        _validate_zone_include(call_origin, include)

        items = current_app.geo_events_service.get_zones_by_ids(ids, include=include)
        result = _dump_zone(items, include)
        return result, HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception:
        logger.exception("Unexpected exception on get_zones_by_ids()", event_ids=ids, call_origin=call_origin)


# GET /geo/zones/{zone_type}
def get_zone_by_external_ids(
    zone_type: str, external_ids: list[str], include: list[str] | None, call_origin: str | None = None
) -> HTTPResponse:
    if include is None:
        include = []

    try:
        _validate_zone_include(call_origin, include)
        items = current_app.geo_events_service.get_zone_by_external_ids(zone_type, external_ids, include=include)
        result = _dump_zone(items, include)
        return result, HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception:
        logger.exception(
            "Unexpected exception on get_zone_by_external_ids()",
            zone_type=zone_type,
            event_ids=external_ids,
            call_origin=call_origin,
        )


# GET /geo/events/bbox
def get_events_by_bbox(
    bbox: str | None = None,
    centers_lat: list[str] | None = None,
    centers_lon: list[str] | None = None,
    max_distance_in_miles: float | None = None,
    coordinates_lat: float | None = None,
    coordinates_lon: float | None = None,
    at_datetime: str | None = None,
    start_datetime: str | None = None,
    end_datetime: str | None = None,
    events_types: list[str] | None = None,
    events_subtypes: list[str] | None = None,
    included_fields: list[str] | None = None,
    excluded_fields: list[str] | None = None,
    limit: int | None = None,
    page: int | None = None,
    call_origin: str | None = None,
    skip_cache: bool | None = False,
) -> HTTPResponse | None:
    try:
        LocalStorageKeys.SkipCache.set(LocalStorageKeys.SkipCache.get() or skip_cache)
        try:
            if bbox is not None:
                request = EventsBBoxRequest.from_request(
                    bbox=bbox,
                    coordinates_lat=coordinates_lat,
                    coordinates_lon=coordinates_lon,
                    at_datetime=at_datetime,
                    start_datetime=start_datetime,
                    end_datetime=end_datetime,
                    included_fields=included_fields or [],
                    excluded_fields=excluded_fields or [],
                    events_types=events_types,
                    events_subtypes=events_subtypes,
                    page=page,
                    limit=limit,
                )
            elif centers_lat is not None and centers_lon is not None:
                request = EventsPointsRequest.from_request(
                    centers_lat=centers_lat,
                    centers_lon=centers_lon,
                    max_distance_in_miles=max_distance_in_miles,
                    at_datetime=at_datetime,
                    start_datetime=start_datetime,
                    end_datetime=end_datetime,
                    included_fields=included_fields or [],
                    excluded_fields=excluded_fields or [],
                    events_types=events_types,
                    events_subtypes=events_subtypes,
                    page=page,
                    limit=limit,
                )
            else:
                raise ValueError("Either bbox or centers must be provided")

        except Exception as e:
            logger.warning("Bad request on get_events_by_bbox", call_origin=call_origin)
            raise flask.abort(HTTPStatus.BAD_REQUEST, str(e))

        geojson = current_app.geo_events_service.get_geojson(request)  # type: ignore

        return geojson, HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception(
            "Unexpected exception on get_geojson_events_in_bbox()",
            call_origin=call_origin,
            skip_cache=skip_cache,
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# GET /geo/zones/bbox
def get_zones_by_bbox(
    bbox: str | None = None,
    centers_lat: list[str] | None = None,
    centers_lon: list[str] | None = None,
    max_distance_in_miles: float | None = None,
    coordinates_lat: float | None = None,
    coordinates_lon: float | None = None,
    zone_types: list[str] | None = None,
    included_fields: list[str] | None = None,
    excluded_fields: list[str] | None = None,
    limit: int | None = None,
    page: int | None = None,
    call_origin: str | None = None,
    skip_cache: bool | None = False,
) -> HTTPResponse:
    try:
        LocalStorageKeys.SkipCache.set(LocalStorageKeys.SkipCache.get() or skip_cache)

        try:
            if bbox is not None:
                request = ZoneBBoxRequest.from_request(
                    bbox=bbox,
                    coordinates_lat=coordinates_lat,
                    coordinates_lon=coordinates_lon,
                    included_fields=included_fields or [],
                    excluded_fields=excluded_fields or [],
                    zone_types=zone_types,
                    page=page,
                    limit=limit,
                )
            elif centers_lat is not None and centers_lon is not None:
                request = ZonePointsRequest.from_request(
                    centers_lat=centers_lat,
                    centers_lon=centers_lon,
                    max_distance_in_miles=max_distance_in_miles,
                    included_fields=included_fields or [],
                    excluded_fields=excluded_fields or [],
                    zone_types=zone_types,
                    page=page,
                    limit=limit,
                )

            else:
                logger.warning("Bad request on get_zones_by_bbox", call_origin=call_origin)
                raise ValueError("Either bbox or centers and max_distance must be provided")
        except Exception as e:
            logger.warning("Bad request on get_zones_by_bbox", call_origin=call_origin)
            raise flask.abort(HTTPStatus.BAD_REQUEST, str(e))

        geojson = current_app.geo_events_service.get_geojson(request)  # type: ignore

        return geojson, HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception(
            "Unexpected exception on get_geojson_events_in_bbox()",
            call_origin=call_origin,
            skip_cache=skip_cache,
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


def _dump_zone(items: list[Zone], include: list[str]) -> list[dict[str, Any]]:
    if not items:
        return []
    if not include:
        result = zone_schema.dump(items, many=True)
    else:
        result = ZoneSchema(exclude=[it for it in zone_default_exclude if it not in include]).dump(items, many=True)
    return result


def _validate_zone_include(call_origin: str, include: list[str]):
    if include:
        fields = set(zone_schema.declared_fields.keys())
        if unknown_fields := set(include) - fields:
            _bad_request_for_unknown_fields(call_origin, unknown_fields)


def _bad_request_for_unknown_fields(call_origin: str, unknown_fields: set[str]):
    logger.warning("Unknown fields in include", unknown_fields=unknown_fields, call_origin=call_origin)
    flask.abort(HTTPStatus.BAD_REQUEST, f"Unknown fields in include: {unknown_fields}")
