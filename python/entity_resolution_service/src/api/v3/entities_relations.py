from http import HTTPStatus
from typing import Dict, List, Optional
from uuid import UUID

import flask
from common.utils.logging import log_function_result
from infrastructure_common.logging import get_logger
from werkzeug.exceptions import HTTPException

from src.api.v3.models.entities_relation import EntitiesRelationsAutomateCreation
from src.api.v3.models.relations_graph import (
    GRAPH_EXCLUDE_OPTIONS,
    GraphOptions,
    RelationsGraph,
)
from src.api.v3.schemas.entities_relation import (
    EntitiesRelationRequestSchema,
    EntitiesRelationsAutomateCreationSchema,
    EntityWithRelationsResponseSchema,
)
from src.api.v3.schemas.entity_with_relations import EntityWithRelationsRequestSchema
from src.api.v3.schemas.relations_graph import RelationsGraphSchema
from src.api.v3.schemas.source import SourceRequestSchema
from src.logic import entities_relation
from src.logic.entities_relation import (
    create_many_entities_relations,
    delete_entities_relation_by_id,
    delete_entity_relations_by_source,
    delete_relations_by_source_field_value,
)
from src.logic.entities_relation_graph import RelationsGraphProvider
from src.logic.exceptions import (
    CouldNotResolvePremisesFromAddress,
    EntityRequestWithConflictingPremises,
    InconsistentEntity,
    InvalidRelationshipCreationRequested,
    RelationshipToSelfEntity,
)
from src.utils.constants import HTTPResponse
from src.utils.db import no_expire_on_commit
from src.utils.error_handling import handle_http_exception

logger = get_logger()


def get_relationship_graph_provider() -> RelationsGraphProvider:
    return flask.current_app.relationship_graph_provider  # type: ignore


# POST /entities-relations/
@log_function_result
def bulk_create_relations_between_entities(body: List[Dict]) -> HTTPResponse:
    """Create a relationship between two Entities."""

    request = None

    try:
        request = EntitiesRelationRequestSchema().load(body, many=True)
    except Exception as e:
        logger.warning("Bad request on bulk_create_relations_between_entities()", body=body, exc_info=e)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        relations = create_many_entities_relations(request)
        return {"message": f"Created {len(relations)} relations between entities."}, HTTPStatus.CREATED
    except RelationshipToSelfEntity as e:
        flask.abort(HTTPStatus.CONFLICT, str(e))
    except InvalidRelationshipCreationRequested as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on bulk_create_relations_between_entities()", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# DELETE /entities-relations/{id}
@log_function_result
def delete_entities_relation(entities_relation_id: UUID) -> HTTPResponse:
    """Delete a relationship with the given ID."""

    try:
        delete_entities_relation_by_id(entities_relation_id)
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception(
            "Unexpected exception on delete_entities_relation()", entities_relation_id=entities_relation_id
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"message": f"Deleted: {entities_relation_id}"}, HTTPStatus.NO_CONTENT


# POST /entities/{entity_id}/relations
@log_function_result
def delete_entity_relations(entity_id: UUID, body: Dict, call_origin: Optional[str] = None) -> HTTPResponse:
    """Delete relations of an entity with the given ID. based on source"""
    deleted_count = 0
    try:
        source = SourceRequestSchema().load(body)
    except Exception as e:
        logger.error("Bad request on delete_entity_relation()", body=body, call_origin=call_origin, exc_info=e)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    try:
        deleted_count = delete_entity_relations_by_source(entity_id, source)
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception(
            "Unexpected exception on delete_entity_relation()", entity_id=entity_id, call_origin=call_origin
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"message": f"Deleted: {deleted_count}"}, HTTPStatus.OK


# DELETE /relations/submission/{submission_id}
@log_function_result
def delete_relations_by_submission(submission_id: UUID, call_origin: Optional[str] = None) -> HTTPResponse:
    deleted_count = 0
    try:
        deleted_count = delete_relations_by_source_field_value(value=submission_id, field="submission_id")
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception(
            "Unexpected exception on delete_relations_by_submission()",
            submission_id=submission_id,
            call_origin=call_origin,
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"message": f"Deleted: {deleted_count}"}, HTTPStatus.OK


# DELETE /relations/file/{file_id}
@log_function_result
def delete_relations_by_file(file_id: UUID, call_origin: Optional[str] = None) -> HTTPResponse:
    deleted_count = 0
    try:
        deleted_count = delete_relations_by_source_field_value(value=file_id, field="file_id")
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception(
            "Unexpected exception on delete_relations_by_submission()", file_id=file_id, call_origin=call_origin
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"message": f"Deleted: {deleted_count}"}, HTTPStatus.OK


# POST /entity-with-owner-relations
@log_function_result
def create_entity_with_owner_relations(body: Dict) -> HTTPResponse:
    """Create a relationship between Entity and other Entities."""

    request = None

    try:
        request = EntityWithRelationsRequestSchema().load(body)
    except Exception as e:
        logger.exception("Bad request on create_entity_with_owner_relations()", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        with no_expire_on_commit():
            response = entities_relation.get_or_create_entity_with_relations(request)
            return EntityWithRelationsResponseSchema().dump(response), HTTPStatus.CREATED
    except RelationshipToSelfEntity as e:
        flask.abort(HTTPStatus.CONFLICT, str(e))
    except EntityRequestWithConflictingPremises as e:
        flask.abort(HTTPStatus.CONFLICT, str(e))
    except InvalidRelationshipCreationRequested as e:
        logger.warning("InvalidRelationshipCreationRequested", body=body, exc_info=e)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except CouldNotResolvePremisesFromAddress as e:
        logger.warning("CouldNotResolvePremisesFromAddress", body=body, exc_info=e)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on create_entity_with_owner_relations()", body=body)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))


# POST /entities-relations/automated-create-async
def entities_relations_automated_create_async(body: Dict) -> HTTPResponse:
    from src.events.events_sender import EventsSender

    request: Optional[EntitiesRelationsAutomateCreation] = None
    try:
        request: EntitiesRelationsAutomateCreation = EntitiesRelationsAutomateCreationSchema().load(body)
    except Exception as e:
        logger.exception("Bad request on entities_relations_automated_create_async()", body=body)
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        EventsSender.emit_async_entities_relations_automate_creation(request)
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return {"message": f"Task created for {request=}"}, HTTPStatus.CREATED


# GET /entities-relations/graph
@log_function_result
def get_entities_relations_graph(
    entity_id: str,
    only_with_issues: Optional[bool] = False,
    max_depth: Optional[int] = None,
    max_nodes: Optional[int] = None,
    call_origin: Optional[str] = None,
    organization_id: Optional[int] = None,
    submission_id: Optional[UUID] = None,
    first_party_only: Optional[bool] = False,
    third_party_only: Optional[bool] = False,
    include_sources: Optional[bool] = False,
    exclude: Optional[List[str]] = None,
) -> HTTPResponse:
    try:
        if first_party_only and third_party_only:
            flask.abort(HTTPStatus.BAD_REQUEST, "first_party_only and third_party_only cannot be both True")
        if first_party_only and not organization_id:
            flask.abort(HTTPStatus.BAD_REQUEST, "organization_id is required when first_party_only is True")
        if submission_id and not organization_id:
            flask.abort(HTTPStatus.BAD_REQUEST, "organization_id is required when submission_id is provided")
        if exclude is None:
            exclude = []
        if exclude and (unknown := set(exclude) - GRAPH_EXCLUDE_OPTIONS):
            flask.abort(HTTPStatus.BAD_REQUEST, f"Unknown exclude options: {unknown}")
        if only_with_issues and "nodes.entity_id_to_doc_count" in exclude:
            flask.abort(
                HTTPStatus.BAD_REQUEST,
                "Cannot exclude 'nodes.entity_id_to_doc_count' while filtering by only_with_issues",
            )
        graph_options = GraphOptions(
            organization_id=int(organization_id) if organization_id else None,
            submission_id=UUID(submission_id) if isinstance(submission_id, str) else submission_id,
            first_party_only=first_party_only,
            third_party_only=third_party_only,
            only_with_issues=only_with_issues,
            max_depth=int(max_depth) if max_depth else None,
            max_nodes=int(max_nodes) if max_nodes else None,
            include_sources=include_sources,
            exclude=exclude,
        )
        graph: RelationsGraph = get_relationship_graph_provider().create_graph(
            UUID(entity_id), graph_options=graph_options
        )
        result = RelationsGraphSchema().dump(graph)
        return result, HTTPStatus.OK
    except InconsistentEntity as e:
        flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, str(e))
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception(
            "Unexpected exception on get_entities_relations_graph()",
            entity_id=entity_id,
            only_with_issues=only_with_issues,
            max_depth=max_depth,
            max_nodes=max_nodes,
            call_origin=call_origin,
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
