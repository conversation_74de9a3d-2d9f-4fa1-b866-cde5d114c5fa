from http import HTTPStatus
from typing import Dict

import flask
from infrastructure_common.logging import get_logger
from werkzeug.exceptions import HTTPException

from src.api.v3.models.duplications import DeduplicateRequest
from src.api.v3.schemas.duplications import (
    DeduplicateRequestSchema,
    DeduplicateResponseSchema,
)
from src.logic.duplications import deduplicate as deduplicate_logic
from src.utils.constants import HTTPResponse
from src.utils.error_handling import handle_http_exception

logger = get_logger()


def deduplicate(body: Dict) -> HTTPResponse:
    request = None
    try:
        request: DeduplicateRequest = DeduplicateRequestSchema().load(body)
    except Exception as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))

    try:
        response = deduplicate_logic(request.entity_id_1, request.entity_id_2, request.primary_entity_id)
        return DeduplicateResponseSchema().dump(response), HTTPStatus.OK
    except HTTPException as e:
        handle_http_exception(e)
    except Exception as e:
        logger.exception("Unexpected exception on deduplicate()", body=body, request=request)
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))
