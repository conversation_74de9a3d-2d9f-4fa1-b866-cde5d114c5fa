from http import HTT<PERSON>tatus
from typing import Dict, Optional, Sequence

import flask
from infrastructure_common.logging import get_logger
from marshmallow import ValidationError
from werkzeug.exceptions import Conflict, HTTPException

from src.geo.geocode import AddressGeocodingException
from src.geo.utils import enrich_many_premises_missing_fields
from src.logic.exceptions import InvalidGeocoderResponse
from src.logic.premises import (
    enrich_premises_with_block_group_geoid,
    get_or_create_premises_with_entity,
)
from src.logic.premises_resolver import PremisesResolver
from src.models import Premises, db
from src.schemas.premises import PremisesSchema
from src.utils.constants import (
    GEOCODING_HIGH_CONFIDENCE_THRESHOLD,
    LOW_GEOCODING_CONFIDENCE_THRESHOLD,
)
from src.utils.db import no_expire_on_commit
from src.utils.error_handling import handle_http_exception

premises_schema = PremisesSchema()

logger = get_logger()


def post_premises(
    address: str,
    # not used directly but used by CallOriginAndSkipCacheEnrichmentInterceptor and later as cross-cutting concern:
    call_origin: str | None = None,
    ignore_al2_mode: bool | None = False,
) -> Dict:
    """
    Create a premises and a real estate entity from an address.
    """
    try:
        with no_expire_on_commit():
            result = PremisesResolver.get_by_geocoding(address, require_postal_code=True, full_address_query=True)
            if result and (premises := result.premises):
                if result.resolved_address and result.resolved_address.confidence is not None:
                    if result.resolved_address.confidence <= LOW_GEOCODING_CONFIDENCE_THRESHOLD:
                        logger.warning(
                            "Post Premises with low confidence",
                            address=address,
                            result=result.resolved_address,
                            confidence=result.resolved_address.confidence,
                        )
                    elif result.resolved_address.confidence < GEOCODING_HIGH_CONFIDENCE_THRESHOLD:
                        logger.info(
                            "Post Premises with not high confidence",
                            address=address,
                            result=result.resolved_address,
                            confidence=result.resolved_address.confidence,
                        )
                get_or_create_premises_with_entity([premises], skip_retrieving_premises=True)
                return premises_schema.dump(premises)
            flask.abort(404, f"Premises not found {result}")
    except (AddressGeocodingException, InvalidGeocoderResponse) as e:
        flask.abort(400, str(e))
    except Conflict as e:
        flask.abort(409, str(e))


def get_premises(
    address_line_1: Optional[str] = None,
    postal_code: Optional[str] = None,
    state: Optional[str] = None,
    address_line_2: Optional[str] = None,
    city: Optional[str] = None,
    ids: Optional[Sequence[str]] = None,
    enrich_with_block_group_geoid: bool = False,
) -> Dict:
    has_address = address_line_1 and postal_code and state

    if not ids and not has_address:
        raise ValidationError({"_schema": ["the list of ids or address components is required"]})
    if ids and has_address:
        raise ValidationError({"_schema": ["providing both the list of ids and address components is not supported"]})

    if ids:
        query = db.session.query(Premises).filter(Premises.id.in_(ids))

    if has_address:
        query = (
            db.session.query(Premises)
            .filter(Premises.address_line_1 == address_line_1.upper())
            .filter(Premises.state == state.upper())
            .filter(Premises.postal_code == postal_code)
        )
        if address_line_2:
            query = query.filter(Premises.address_line_2 == address_line_2.upper())
        if city:
            query = query.filter(Premises.city == city.upper())

    premises = query.all()
    if enrich_with_block_group_geoid:
        enrich_premises_with_block_group_geoid(premises)
    enrich_many_premises_missing_fields(premises)
    return premises_schema.dump(premises, many=True)


def get_premises_by_id(premises_id: str, enrich_with_block_group_geoid: bool = False) -> Dict:
    premises = Premises.query.get_or_404(premises_id, description="Premises with specified ID was not found")
    if enrich_with_block_group_geoid:
        enrich_premises_with_block_group_geoid([premises])
    enrich_many_premises_missing_fields([premises])
    return premises_schema.dump(premises)


def delete_premises_by_address(address: str) -> Dict:
    """
    Use to delete invalid premises and cache by address.
    Premises can not be deleted if it is used by an entity.
    """
    try:
        return PremisesResolver.delete_premises_by_address(address)
    except (AddressGeocodingException, InvalidGeocoderResponse) as e:
        flask.abort(HTTPStatus.BAD_REQUEST, str(e))
    except HTTPException as e:
        handle_http_exception(e)
