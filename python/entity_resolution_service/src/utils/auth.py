import inspect
import json
from dataclasses import dataclass
from functools import wraps
from typing import Any, Dict, Optional, Set, Tuple
from uuid import UUID

from flask import g, has_request_context, request
from infrastructure_common.logging import get_logger
from sqlalchemy.orm import joinedload

USER_ORGANIZATION_ID_HEADER = "Userorganizationid"
USER_EMAIL_HEADER = "Useremail"
USER_CROSS_ORG_ACCESS_HEADER = "Usercrossorganizationaccess"

logger = get_logger()


@dataclass
class User:
    organization_id: int | None
    email: str | None
    cross_organization_access: bool


def _extract_organization_id_from_data(data: Dict[str, Any]) -> Optional[int]:
    """
    Extract organization_id from a dictionary, recursively checking nested structures.

    This function searches for the first organization_id found in the data structure.
    It checks in the following order:
    1. Direct "organization_id" field in the root data
    2. "organization_id" field within a "source" object
    3. "organization_id" field within the first item of a "sources" list
    4. Recursively searches nested dictionaries and lists

    ERS response objects typically contain a single organization_id that applies to all
    elements in the response. This function returns the first valid organization_id
    found, which should be consistent across all elements in a properly formed response.

    Args:
        data: Dictionary to search for organization_id

    Returns:
        First valid organization_id found as integer, or None if not found
    """
    if not data or not isinstance(data, dict):
        return None

    # Check direct organization_id field
    if "organization_id" in data and data["organization_id"] is not None:
        try:
            return int(data["organization_id"])
        except (ValueError, TypeError):
            pass

    # Check organization_id in source object
    if "source" in data and isinstance(data["source"], dict) and "organization_id" in data["source"]:
        try:
            return int(data["source"]["organization_id"])
        except (ValueError, TypeError):
            pass

    # Check organization_id in sources list (first item)
    if "sources" in data and isinstance(data["sources"], list):
        for source in data["sources"]:
            if isinstance(source, dict) and "organization_id" in source:
                try:
                    return int(source["organization_id"])
                except (ValueError, TypeError):
                    continue

    # Recursively search nested structures
    for _, value in data.items():
        if isinstance(value, dict):
            org_id = _extract_organization_id_from_data(value)
            if org_id is not None:
                return org_id
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, dict):
                    org_id = _extract_organization_id_from_data(item)
                    if org_id is not None:
                        return org_id

    return None


def _extract_entity_ids_from_data(data: Dict[str, Any]) -> Set[str]:
    """
    Extract entity IDs from a dictionary, recursively checking nested structures.

    Returns: set of entity IDs as strings.
    """
    result = set()

    if not data or not isinstance(data, dict):
        return result

    if "entity_id" in data and data["entity_id"] is not None:
        result.add(str(data["entity_id"]))

    for key in ["entity_id", "entity_id_1", "entity_id_2", "from_entity_id", "to_entity_id", "primary_entity_id"]:
        if key in data and data[key] is not None:
            result.add(str(data[key]))

    for key in ["entity_ids", "entities_ids"]:
        if key in data and isinstance(data[key], list):
            for item in data[key]:
                result.add(str(item))

    for key, value in data.items():
        if isinstance(value, dict):
            result.update(_extract_entity_ids_from_data(value))
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, dict):
                    result.update(_extract_entity_ids_from_data(item))

    return {id_str for id_str in result if id_str}


def _extract_source_ids_from_data(data: Dict[str, Any]) -> Set[str]:
    """
    Extract source IDs from a dictionary, recursively checking nested structures.

    Returns: set of source IDs as strings.
    """
    result = set()

    if not data or not isinstance(data, dict):
        return result

    if "source_id" in data and data["source_id"] is not None:
        result.add(str(data["source_id"]))

    for key in ["source_id", "submission_id"]:
        if key in data and data[key] is not None:
            result.add(str(data[key]))

    for key, value in data.items():
        if isinstance(value, dict):
            result.update(_extract_source_ids_from_data(value))
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, dict):
                    result.update(_extract_source_ids_from_data(item))

    return {id_str for id_str in result if id_str}


def _validate_entity_organization_id(entity_ids: Set[str], expected_org_id: int) -> Tuple[bool, Set[int]]:
    """
    Validate that the entities belong to the expected organization.

    Returns: tuple of (is_valid, mismatched_org_ids).
    """
    from src.models import Entity

    if not entity_ids or expected_org_id is None:
        return True, set()

    mismatched_org_ids = set()

    from src.models import db

    try:
        entities = (
            db.session.query(Entity)
            .options(joinedload(Entity.sources).joinedload(Entity._source))
            .filter(Entity.id.in_([UUID(id_str) for id_str in entity_ids if id_str]))
            .all()
        )

        for entity in entities:
            for entity_source in entity.sources:
                if hasattr(entity_source, "_source") and entity_source._source:
                    org_id = entity_source._source.organization_id
                    if org_id is not None and org_id != expected_org_id:
                        mismatched_org_ids.add(org_id)

        return len(mismatched_org_ids) == 0, mismatched_org_ids
    except Exception as e:
        logger.warning(f"Failed to validate entity organization IDs: {e!s}")
        # On error validating, treat as mismatched to surface warning
        return False, set()


def _validate_source_organization_id(source_ids: Set[str], expected_org_id: int) -> Tuple[bool, Set[int]]:
    """
    Validate that the sources belong to the expected organization.

    Returns: tuple of (is_valid, mismatched_org_ids).
    """
    if not source_ids or expected_org_id is None:
        return True, set()

    mismatched_org_ids = set()

    from src.models import db
    from src.models.source import Source

    try:
        sources = (
            db.session.query(Source).filter(Source.id.in_([UUID(id_str) for id_str in source_ids if id_str])).all()
        )

        for source in sources:
            if source.organization_id is not None and source.organization_id != expected_org_id:
                mismatched_org_ids.add(source.organization_id)

        return len(mismatched_org_ids) == 0, mismatched_org_ids
    except Exception as e:
        logger.warning(f"Failed to validate source organization IDs: {e!s}")
        # On error validating, treat as mismatched to surface warning
        return False, set()


def _extract_organization_id_from_request() -> Tuple[Optional[int], str, bool]:
    """
    Extract organization ID and user information from request headers.

    Returns:
        Tuple of (org_id_int, email, cross_org_access)
    """
    org_id = request.headers.get(USER_ORGANIZATION_ID_HEADER)
    existing_g_org = getattr(g, "organization_id", None)
    email = request.headers.get(USER_EMAIL_HEADER)
    cross_org_access = bool(request.headers.get(USER_CROSS_ORG_ACCESS_HEADER, False))

    try:
        org_id_int = int(org_id) if org_id is not None else existing_g_org
    except Exception:
        logger.error("Invalid organization id in header", org_id=org_id)
        org_id_int = existing_g_org

    return org_id_int, email, cross_org_access


def _should_skip_organization_check(org_id_int: Optional[int], email: str, cross_org_access: bool) -> bool:
    """
    Determine if organization validation should be skipped.

    Checks if the user has cross-organization access or is a Kalepa organization user (but not synthetic test user).

    Args:
        org_id_int: Organization ID from request
        email: User email from request
        cross_org_access: Cross-organization access flag

    Returns:
        True if organization checks should be skipped
    """
    if cross_org_access:
        return True

    # Check if user is from Kalepa organization and not a synthetic test user
    kalepa_orgs = {3, 9}
    synth_test_email = "<EMAIL>"

    if email and org_id_int is not None:
        try:
            is_kalepa = any(str(org_id_int) == str(k) for k in kalepa_orgs)
            if is_kalepa and email.lower() != synth_test_email:
                return True
        except Exception:
            pass

    return False


def _is_state_changing_method() -> bool:
    """
    Check if the current HTTP method is state-changing (requires database validation).

    Returns:
        True for POST, PUT, PATCH methods; False for GET and other read-only methods
    """
    return request.method in {"POST", "PUT", "PATCH"}


def _extract_entity_id_from_function_args(func, args, kwargs) -> Optional[str]:
    """
    Extract entity_id from function arguments using function introspection.
    Uses function signature to find the correct parameter by name.

    Args:
        func: The decorated function
        args: Positional arguments passed to the function
        kwargs: Keyword arguments passed to the function

    Returns:
        Entity ID as string if found, None otherwise
    """
    if "entity_id" in kwargs:
        return str(kwargs["entity_id"]) if kwargs["entity_id"] is not None else None

    try:
        sig = inspect.signature(func)
        param_names = list(sig.parameters.keys())

        entity_id_params = [
            "entity_id",
            "entity_id_1",
            "entity_id_2",
            "from_entity_id",
            "to_entity_id",
            "primary_entity_id",
            "id",
        ]

        for param_name in entity_id_params:
            if param_name in param_names:
                param_index = param_names.index(param_name)
                if param_index < len(args) and args[param_index] is not None:
                    return str(args[param_index])
                break

    except Exception as e:
        logger.warning(f"Failed to extract entity_id using function introspection: {e}")

    # Fallback: if we have args and no entity_id found, try first argument
    if args and args[0] is not None:
        return str(args[0])

    return None


def _validate_request_data_organization(org_id_int: Optional[int]) -> None:
    """
    Validate organization ID in request JSON data.

    Args:
        org_id_int: Expected organization ID from request headers
    """
    if not (org_id_int is not None and request.is_json and request.data):
        return

    try:
        request_data = request.get_json()
        if not request_data:
            return

        request_org_id = _extract_organization_id_from_data(request_data)
        if request_org_id is not None and int(request_org_id) != int(org_id_int):
            logger.error(
                "Organization id mismatch between header and request data",
                header_org_id=org_id_int,
                request_org_id=request_org_id,
            )

        entity_ids = _extract_entity_ids_from_data(request_data)
        if entity_ids and _is_state_changing_method():
            is_valid, mismatched_org_ids = _validate_entity_organization_id(entity_ids, org_id_int)
            if not is_valid:
                logger.error(
                    "Organization id mismatch between header and entities referenced in request",
                    header_org_id=org_id_int,
                    entity_ids=list(entity_ids),
                    mismatched_org_ids=list(mismatched_org_ids),
                )

        source_ids = _extract_source_ids_from_data(request_data)
        if source_ids and _is_state_changing_method():
            is_valid, mismatched_org_ids = _validate_source_organization_id(source_ids, org_id_int)
            if not is_valid:
                logger.error(
                    "Organization id mismatch between header and sources referenced in request",
                    header_org_id=org_id_int,
                    source_ids=list(source_ids),
                    mismatched_org_ids=list(mismatched_org_ids),
                )

    except json.JSONDecodeError as e:
        logger.warning("Could not parse request JSON for organization id validation", exc_info=e)
    except Exception:
        logger.exception("Error during organization id validation")


def _validate_path_parameter_organization(func, args, kwargs, org_id_int: Optional[int]) -> None:
    """
    Validate organization ID for path parameters.

    Path parameter validation is always performed regardless of HTTP method,
    as it's a security concern to prevent access to entities from other organizations.

    Args:
        func: The decorated function
        args: Positional arguments passed to the function
        kwargs: Keyword arguments passed to the function
        org_id_int: Expected organization ID from request headers
    """
    if org_id_int is None:
        return

    entity_id_val = _extract_entity_id_from_function_args(func, args, kwargs)
    if entity_id_val is not None:
        is_valid, mismatched_org_ids = _validate_entity_organization_id({entity_id_val}, org_id_int)
        if not is_valid:
            logger.error(
                "Organization id mismatch between header and path parameter",
                header_org_id=org_id_int,
                entity_id=entity_id_val,
                mismatched_org_ids=list(mismatched_org_ids),
            )


def _validate_response_organization(result, org_id_int: Optional[int]) -> None:
    """
    Validate organization ID in response data.

    Args:
        result: Function result to validate
        org_id_int: Expected organization ID from request headers
    """
    if org_id_int is None:
        return

    if not isinstance(result, tuple) or len(result) < 1:
        return

    response_data = result[0]
    if not isinstance(response_data, dict):
        return

    response_org_id = _extract_organization_id_from_data(response_data)
    if response_org_id is not None and int(response_org_id) != int(org_id_int):
        logger.error(
            "Organization id mismatch between header and response data",
            header_org_id=org_id_int,
            response_org_id=response_org_id,
        )


def check_organization(parameter_name: str | None = None):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not has_request_context():
                return func(*args, **kwargs)

            # Extract organization ID and user information from request
            org_id_int, email, cross_org_access = _extract_organization_id_from_request()

            # Early return if org_id cannot be extracted
            if org_id_int is None:
                logger.warning("No organization ID found in request headers or context, proceeding without validation")
                return func(*args, **kwargs)

            skip_check = _should_skip_organization_check(org_id_int, email, cross_org_access)

            g.user = User(
                organization_id=org_id_int,
                email=email,
                cross_organization_access=cross_org_access,
            )
            g.organization_id = org_id_int

            from structlog.contextvars import bound_contextvars

            with bound_contextvars(
                request_auth=dict(
                    organization_id=org_id_int,
                    email=email,
                    cross_organization_access=cross_org_access,
                    skip_check=skip_check,
                )
            ):
                if skip_check:
                    return func(*args, **kwargs)

                # Validate parameter-based organization ID
                if parameter_name:
                    param_value = kwargs.get(parameter_name)
                    if param_value is not None and org_id_int is not None and int(param_value) != int(org_id_int):
                        logger.error(
                            "Organization id mismatch between header and argument",
                            header_org_id=org_id_int,
                            param_value=param_value,
                        )
                else:
                    # Validate request data organization IDs
                    _validate_request_data_organization(org_id_int)

                    # Validate path parameter organization ID (when no JSON body)
                    _validate_path_parameter_organization(func, args, kwargs, org_id_int)

                result = func(*args, **kwargs)

                # Validate response organization ID
                if not parameter_name and not skip_check:
                    _validate_response_organization(result, org_id_int)

                return result

        return wrapper

    return decorator
