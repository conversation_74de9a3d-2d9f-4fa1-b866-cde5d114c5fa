locals {
  batch_jobs = {
    "add-lowered-to-entity-names" = {
      handler = "src.tasks.add_lowered_to_entity_names.handler.execute"
      memory  = "4096"
    }
    "admiral-backfill-cancel-status" = {
      handler                  = "src.tasks.sudijovski.admiral_backfill_cancel_status.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "admiral-casualty-groups-backfix" = {
      handler                  = "src.tasks.sudijovski.admiral_casualty_groups_backfix.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "admiral-resync-splits" = {
      handler                  = "src.tasks.sudijovski.admiral_resync_splits.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "append-observation-hud-observation" = {
      handler = "src.tasks.append_observation.main.main"
      memory  = "4096"
      triggers = [
        {
          schedule = "cron(0 0 1 * ? *)"
        },
      ]
    }
    "arch-bulk-update-client-ids" = {
      handler                  = "src.tasks.sudijovski.arch_bulk_update_client_ids.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-canada-subs-delete" = {
      handler                  = "src.tasks.sudijovski.arch_canada_subs_delete.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-client-ids-fix" = {
      handler                  = "src.tasks.sudijovski.arch_client_ids_fix.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-coverage-splits-fix" = {
      handler                  = "src.tasks.sudijovski.arch_coverage_splits_fix.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-coverage-splits-shells" = {
      handler                  = "src.tasks.sudijovski.arch_coverage_splits_shells.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-duplicates-fix" = {
      handler                  = "src.tasks.sudijovski.arch_duplicates_fix.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-duplicates-update-created-at" = {
      handler                  = "src.tasks.sudijovski.arch_duplicates_update_created_at.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-historical-backfill" = {
      handler                  = "src.tasks.sudijovski.arch_historical_backfill.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-split-submissions" = {
      handler                  = "src.tasks.sudijovski.arch_split_submissions.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-submission-sync-without-client-ids" = {
      handler                  = "src.tasks.sudijovski.arch_submission_sync_without_client_ids.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-sync-coverages" = {
      handler                  = "src.tasks.sudijovski.arch_sync_coverages.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-sync-status-uw" = {
      handler                  = "src.tasks.sudijovski.arch_sync_status_uw.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "arch-uws-eff-date-update" = {
      handler                  = "src.tasks.sudijovski.arch_uws_eff_date_update.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "ba-single_run_job" = {
      handler                  = "src.tasks.bartek.ers_remove_unused_submissions_entities.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "ba_update_normalized_addresses_run_job" = {
      handler                  = "src.tasks.bartek.ers_update_premises_normalized_address.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "backfill-account-id-sync" = {
      handler = "src.tasks.sudijovski.backfill_account_id_sync.execute"
      memory  = "4096"
    }
    "backfill-block-group-geoid" = {
      handler = "src.tasks.ivan.backfill_block_group_geoid.run"
      memory  = "4096"
    }
    "backfill-file-processing" = {
      handler                  = "src.tasks.zachary.backfill_file_processing.main"
      attempt_duration_seconds = 172800
      memory                   = "4096"
    }
    "backfill-historical-munich-shells" = {
      handler = "src.tasks.maciej.backfill_historical_munich_shells.execute"
      memory  = "4096"
    }
    "backfill-is-renewal" = {
      handler                  = "src.tasks.maciej.backfill_is_renewal.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        SLEEP_TIME_SECONDS = "1"
        DRY_RUN            = "False"
      }
    }
    "backfill-lob-carrier-mappings" = {
      handler = "src.tasks.maciej.backfill_lob_carrier_mappings.execute"
      memory  = "4096"
    }
    "backfill-loss-run-extraction-documents" = {
      handler                  = "src.tasks.sudijovski.backfill_loss_run_extraction_documents.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "backfill-missing-correspondence-ids" = {
      handler = "src.tasks.ivan.backfill_missing_correspondence_ids.run"
      memory  = "4096"
    }
    "backfill-missing-losses" = {
      handler = "src.tasks.maciej.backfill_missing_loss_data.execute"
      memory  = "4096"
    }
    "backfill-munich-re-sync-premiums" = {
      handler                  = "src.tasks.maciej.backfill_munich_re_sync_premiums.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "backfill-munich-shell-data" = {
      handler = "src.tasks.maciej.backfill_munich_shell_data.execute"
      memory  = "4096"
    }
    "backfill-munich-shells" = {
      handler = "src.tasks.maciej.backfill_munich_shells.execute"
      memory  = "4096"
    }
    "backfill-primary-naics" = {
      handler = "src.tasks.ada.backfill_primary_naics.main"
      memory  = "4096"
    }
    "backfill-null-reco-score-in-capi" = {
      handler = "src.tasks.wojtek.backfill_null_reco_score_in_capi.main"
      memory  = "4096"
    }
    "backfill-summarization" = {
      handler = "src.tasks.maciej.backfill_summarization.execute"
      memory  = "4096"
    }
    "backfill-v2-metrics" = {
      handler                  = "src.tasks.zachary.backfill_v2_metrics.execute"
      attempt_duration_seconds = 172800
      memory                   = "4096"
    }
    "backfill_files_checksums" = {
      handler = "src.tasks.maciej.backfill_files_checksums.execute"
      memory  = "4096"
      environment = {
        FILE_AGE_LIMIT_DAYS = "90"
        BATCH_SIZE          = "100"
        MAX_WORKERS         = "16"
      }
    }
    "backfill_loss_lob" = {
      handler = "src.tasks.maciej.backfill_loss_lob.execute"
      memory  = "4096"
      environment = {
        BATCH_SIZE = "10000"
      }
    }
    "backfill_manual_verification" = {
      handler = "src.tasks.marcin.backfill_manual_verified.main"
      memory  = "4096"
    }
    "backfill-reco-data-in-capi" = {
      handler = "src.tasks.donahue.backfill_reco_data_in_capi.main"
      memory  = "4096"
    }
    "backfill_redirects-job" = {
      handler                  = "src.tasks.backfill_redirects.backfill_redirects.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        MERGE_BUSINESSES = "copilot-workers-merge-businesses"
        FILENAME         = "redirects.csv"
        START            = "0"
        END              = "0"
      }
    }
    "backfill_report_tiers" = {
      handler                  = "src.tasks.konrad.backfill_tags_tiers.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        SLEEP_TIME_SECONDS = "1"
        DRY_RUN            = "False"
      }
    }
    "backfill_time_series_values" = {
      handler                  = "src.tasks.konrad.backfill_time_series_values.backfill_time_series_values"
      attempt_duration_seconds = 7200
      memory                   = "4096"
      environment = {
        SLEEP_TIME_SECONDS = "1"
        DRY_RUN            = "False"
      }
    }
    "broker-emails-with-underscore-fix" = {
      handler                  = "src.tasks.sudijovski.broker_emails_with_underscore_fix.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "brokers-cleanup" = {
      handler                  = "src.tasks.sudijovski.brokers_cleanup.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "calculate-erisa-category-stats" = {
      handler = "src.tasks.calculate_erisa_category_stats.handler.execute"
      memory  = "4096"
      environment = {
        YEARS_BACK = "3"
        YEARS_TO   = "1"
      }
      triggers = [
        {
          schedule = local.schedules.calculate_erisa_category_stats
        },
      ]
    }
    "clean-invalid-identifiers-job" = {
      handler                  = "src.tasks.bartek.clean_invalid_identifiers.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "clean-up-business-premises" = {
      handler = "src.tasks.maciej.clean_up_business_premises.execute"
      memory  = "4096"
      environment = {
        ENTITY_RESOLUTION_SERVICE_URL = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
      }
    }
    "clean-up-munich-shells-duplicates" = {
      handler = "src.tasks.maciej.clean_up_munich_shells_duplicates.execute"
      memory  = "4096"
    }
    "clean-useless-sources-job" = {
      handler                  = "src.tasks.bartek.clean_useless_sources.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "cleanup-ers-entity-names" = {
      handler                  = "src.tasks.ivan.cleanup_ers_entity_names.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "cleanup-ers-loops" = {
      handler                  = "src.tasks.sudijovski.ers_loops_cleanup.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        LIMIT  = "1000"
        OFFSET = "0"
      }
    }
    "cleanup-observation-relationship" = {
      handler                  = "src.tasks.ivan.cleanup_observation_relationship.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "cleanup-premises-first" = {
      handler                  = "src.tasks.premises_cleanup.main.cleanup_duplicates"
      attempt_duration_seconds = 2592000
      memory                   = "4096"
      environment = {
        LIMIT  = "1000"
        OFFSET = "0"
      }
    }
    "cleanup-report-correspondence" = {
      handler = "src.tasks.ivan.cleanup_report_correspondence.run"
      memory  = "4096"
    }
    "cloudsearch-backfill-reports" = {
      handler                  = "src.tasks.cloudsearch.backfill_cloudsearch_reports.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        SLEEP_TIME_SECONDS       = "1"
        DRY_RUN                  = "False"
        CLOUDSEARCH_ENDPOINT_URL = "https://${local.workflow_parameters.cloudsearch_reports_endpoints.search_service_endpoint}"
        SYNC_PAST_DAYS           = "0"     # 0 means sync all days, overwrite with a custom value to only sync reports from the past X days
        SYNC_ORGANIZATION        = "0"     # 0 means sync all organizations, overwrite with a custom value to only sync reports from a specific org ID
        SYNC_IDS                 = "all"   # `all` means sync all report IDs, overwrite with a custom value to only sync reports with given IDs — accepts a comma-separated list of report IDs
        PURGE_EXISTING_REPORTS   = "True"  # Set to True to delete all existing reports before syncing new ones
        SKIP_SYNC                = "False" # Set to True to skip syncing the reports (does not skip purging existing reports, if enabled)
      }
      triggers = [
        {
          schedule = local.schedules.cloudsearch_backfill_reports
        },
      ]
    }
    "create_boss_mappings" = {
      handler = "src.tasks.maciej.create_boss_mappings.execute"
      memory  = "4096"
      environment = {
        SHEET_NAME = "MasterUWAssignmentStatic"
        BATCH_SIZE = "1000"
        S3_KEY     = "boss_uw_assignment.xlsx"
      }
    }
    "create_paragon_wc_mappings" = {
      handler = "src.tasks.maciej.create_paragon_wc_mappings.create_mappings"
      memory  = "4096"
      environment = {
        SHEET_NAME    = "Producer Information"
        BATCH_SIZE    = "1000"
        S3_KEY        = "paragon_wc_assignment.xlsx"
        UPDATE_ONLY   = "True"
        HEADER_OFFSET = "0"
      }
    }
    "delete-and-add-ers-entity-names" = {
      handler                  = "src.tasks.delete.delete_and_add_ers_entity_names.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        ERS_V3_URL       = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
        ERS_CALL_TIMEOUT = "0.1"
      }
    }
    "delete-data-from-pinecone-and-ers" = {
      handler                  = "src.tasks.delete.delete_data_from_pinecone_and_ers.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        PICONE_INDEX = "write index name here"
      }
    }
    "delete-entities-from-ers" = {
      handler                  = "src.tasks.delete.delete_entities_from_ers.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        ERS_V3_URL = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
        QUERY      = "QUERY_DELETE_ENTITIES_WITHOUT_PREMISES"
      }
    }
    "delete-invalid-actions-and-outcomes" = {
      handler = "src.tasks.donahue.delete_invalid_actions_and_outcomes.main"
      memory  = "4096"
    }
    "delete-licenses-from_facts" = {
      handler                  = "src.tasks.delete.delete_licenses_from_facts.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "ensure-identifier-sync-job" = {
      handler                  = "src.tasks.bartek.ensure_identifier_sync.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        FACT_SUBTYPE_ID     = "FEIN"
        ERS_IDENTIFIER_TYPE = "FEIN"
      }
    }
    "ers-add-areas-for-entities-without-job" = {
      handler                  = "src.tasks.bartek.ers_add_area_premises_to_entities_with_no_premises.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "ers-clean-geocoding-from-partial-matches" = {
      handler                  = "src.tasks.bartek.ers_clean_geocoding_from_partial_matches.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "ers-clean-geocoding-job" = {
      handler                  = "src.tasks.bartek.ers_clean_geocoding.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "ers-deduplicate-by-name-and-address-job" = {
      handler                  = "src.tasks.bartek.ers_deduplicate_by_name_and_address.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "ers-deduplicate-entities-by-search-job" = {
      handler                  = "src.tasks.bartek.ers_deduplicate_entities_by_search.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "ers-generate-density-job" = {
      handler                  = "src.tasks.bartek.generate_density.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "ers-google-local-cleanup" = {
      handler                  = "src.tasks.sudijovski.ers_google_local_cleanup.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        SLEEP_TIME_SECONDS = "1"
        DRY_RUN            = "False"
      }
    }
    "ers-remove-invalid-relations-job" = {
      handler                  = "src.tasks.bartek.ers_remove_invalid_relations.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "ers-remove-premises-embeddings-job" = {
      handler                  = "src.tasks.bartek.remove_premises_embeddings.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        PICONE_INDEX     = "write index name here"
        PICONE_NAMESPACE = "premises"
      }
    }
    "ers-websites-back-fill-job" = {
      handler                  = "src.tasks.bartek.backfill_ers_with_websites.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "ers_fixing_corrupted_data" = {
      handler                  = "src.tasks.bartek.find_corrupted_names.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "exclude-facts" = {
      handler = "src.tasks.ivan.exclude_facts.exclude_facts"
      memory  = "4096"
      environment = {
        EXCLUDE_FACTS_ARN  = "arn:aws:lambda:us-east-1:${local.workflow_parameters.account_id}:function:copilot-workers-exclude-facts"
        SLEEP_TIME_SECONDS = "10"
      }
    }
    "facts-change-parent-type-job" = {
      handler                  = "src.tasks.facts.facts_change_parent_type.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        SOURCE_PARENT_TYPE = "ORGANIZATION"
        TARGET_PARENT_TYPE = "BUSINESS"
      }
    }
    "facts-clean-by-fact-subtype-job" = {
      handler                  = "src.tasks.facts.facts_clean_by_fact_subtype.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        FACT_SUBTYPE_ID = "PERMIT_TYPE"
      }
    }
    "facts-clean-duplicated-permit-type-job" = {
      handler                  = "src.tasks.bartek.facts_clean_duplicated_permit_type.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "facts-clean-too-long-permit-name-job" = {
      handler                  = "src.tasks.bartek.facts_clean_too_long_permit_name.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "facts-delete-orphans-job" = {
      handler                  = "src.tasks.facts.facts_delete_orphans.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "facts-generate-year-found" = {
      handler                  = "src.tasks.bartek.facts_generate_year_found.run"
      attempt_duration_seconds = 259200
    }
    "fill-normalized_address" = {
      handler                  = "src.tasks.premises_cleanup.fill_normalized_address.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "reassign-fact-subtype-id" = {
      handler                  = "src.tasks.facts.reassign_fact_subtype_id.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "fill-up-missing-entities-data" = {
      handler                  = "src.tasks.bartek.fill_up_missing_entities_data.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        SAMPLE_JOB_SPECIFIC_VARIABLE = "3"
      }
    }
    "fix-dossier-snapshot-child-businesses" = {
      handler = "src.tasks.migrate_snapshots.fix_child_businesses.run"
      memory  = "4096"
      environment = {
        ENTITY_RESOLUTION_SERVICE_URL = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
      }
    }
    "fix-expired-quoted-subs" = {
      handler = "src.tasks.maciej.fix_expired_quoted_subs.execute"
      memory  = "4096"
    }
    "fix-hyper-products-inc-relations" = {
      handler = "src.tasks.ivan.fix_hyper_products_inc_relations.run"
      memory  = "4096"
    }
    "fix-munich-coverage-conflicts" = {
      handler = "src.tasks.maciej.fix_munich_coverage_conflicts.execute"
      memory  = "4096"
    }
    "fix-nw-not-quoted-subs" = {
      handler = "src.tasks.maciej.fix_nw_not_quoted_subs.execute"
      memory  = "4096"
    }
    "fix-osha-naics-job" = {
      handler                  = "src.tasks.bartek.fix_osha_invalid_nacis_ingested_facts.run"
      attempt_duration_seconds = 2678400
      memory                   = "5120"
    }
    "fix-paragon-permissions" = {
      handler = "src.tasks.maciej.fix_paragon_permissions.fix_permissions"
      memory  = "4096"
    }
    "gen-doc-hashes-job" = {
      handler                  = "src.tasks.konrad.generate_document_hashes.gen_document_hashes"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        SAMPLE_JOB_SPECIFIC_VARIABLE = "3"
      }
    }
    "iso-vehicle-rating-mapping" = {
      handler                  = "src.tasks.iso_vehicle_rating.create_iso_vehicle_rating_mapping.execute"
      retry_attempts           = 2
      attempt_duration_seconds = 15000
      memory                   = "4096"
      triggers = [
        {
          schedule = "cron(0 5 1,15 * ? *)"
        },
      ]
    }
    "kalepi-cleanup" = {
      handler                  = "src.tasks.kalepi.periodic_cleanup.execute"
      attempt_duration_seconds = 259200
      memory                   = "8192"
      vcpu                     = "2"
      environment = {
        SAFE_INIT_RESOLVE_SECRETS  = "True"
        KALEPI_URL                 = local.env_config.kalepi_url
        KALEPI_INDEX               = "kalepa/packages"
        KALEPI_USERNAME            = "kalepa"
        GITHUB_TOKEN_SECRET_ARN    = local.regional_secret_arns["github-api-key"]
        KALEPI_PASSWORD_SECRET_ARN = local.regional_secret_arns["kalepi-kalepa-password"]
        CLEANUP_PERIOD_DAYS        = "7" # keep all packages released within the last 7 days
        KEEP_LATEST_RELEASES       = "5" # keep the latest 5 releases
        DRY_RUN                    = "False"
      }
      triggers = [
        {
          schedule = local.schedules.kalepi_cleanup
        },
      ]
    }
    "migrate-bbb-contact-info" = {
      handler                  = "src.tasks.meteviasam.migrate_bbb_contact_info.execute"
      attempt_duration_seconds = 43200
      memory                   = "4096"
    }
    "migrate-brokers-to-v2" = {
      handler = "src.tasks.migrate_brokers.migrate_brokers_to_v2.run"
      memory  = "8192"
    }
    "migrate-emails-to-copilot" = {
      handler = "src.tasks.ivan.migrate_emails_to_copilot.run"
      memory  = "4096"
    }
    "migrate-entity-snapshot-to-dossier-snapshot" = {
      handler = "src.tasks.migrate_snapshots.entity_snapshot_to_dossier_snapshot.run"
      memory  = "4096"
      environment = {
        ENTITY_RESOLUTION_SERVICE_URL = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
      }
    }
    "migrate-hazard-hub-cache" = {
      handler = "src.tasks.maciej.migrate_hazard_hub_cache.execute"
      memory  = "4096"
      environment = {
        BATCH_SIZE = "1000"
      }
    }
    "migrate-industries" = {
      handler = "src.tasks.migrate_industries.handler.run"
      memory  = "4096"
    }
    "migrate-organizations" = {
      handler = "src.tasks.migrate_organizations.handler.run"
      memory  = "4096"
    }
    "migrate-recommendation-rules" = {
      handler = "src.tasks.migrate_recommendation_rules.migrate_recommendation_rules.main"
      memory  = "4096"
    }
    "migrate-relations" = {
      handler = "src.tasks.migrate_relations.handler.run"
      memory  = "4096"
    }
    "migrate-snapshots" = {
      handler = "src.tasks.migrate_snapshots.handler.run"
      memory  = "4096"
    }
    "migrate-threads-to-copilot" = {
      handler = "src.tasks.ivan.migrate_threads_to_copilot.run"
      memory  = "4096"
    }
    "move-file-comments" = {
      handler                  = "src.tasks.ivan.move_file_comments.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-boss-contractor-fields" = {
      handler                  = "src.tasks.sudijovski.nw_boss_contractor_fields.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-coverages-align" = {
      handler                  = "src.tasks.sudijovski.nw_coverages_align.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-duplicates-created_at-sync" = {
      handler                  = "src.tasks.sudijovski.nw_duplicates_created_at_sync.sync_created_at"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-match-missing-boss-ids" = {
      handler                  = "src.tasks.sudijovski.nw_match_missing_boss_ids.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-ml-delete-sync-shells" = {
      handler                  = "src.tasks.fuchs.nw_ml_delete_sync_shells.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "clean-insights-obs" = {
      handler                  = "src.tasks.fuchs.clean_insights_obs.clean_insights_obs.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        DELETE_OBSERVATIONS = "false"
        RERUN_INSIGHTS      = "false"
        PROCESS_ISC_ONLY    = "true"
      }
    }
    "create-missing-multi-label-facts" = {
      handler                  = "src.tasks.fuchs.create_missing_multi_label_facts.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "organize-hh-obs" = {
      handler                  = "src.tasks.fuchs.organize_hh_obs.main"
      attempt_duration_seconds = 2592000 # 30 days
      memory                   = "8192"
      environment = {
        APPEND_ORGANIZATIONAL_FACTS = "false"
        DELETE_HH_OBS               = "false"
        ORGANIZATION_IDS            = ""
      }
    }
    "cleanup_spa_obs" = {
      handler                  = "src.tasks.kacper.ENG-26356.clean_up_spa.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        DELETE_OBSERVATIONS = "false"
        RERUN_INSIGHTS      = "false"
      }
    }
    "unify-custom-classifiers-to-4-digit-naics" = {
      handler                  = "src.tasks.fuchs.unify_customizable_classifiers.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-shell-submissions" = {
      handler                  = "src.tasks.sudijovski.nw_shell_submissions.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-shells-delete-wrong-permissions" = {
      handler                  = "src.tasks.sudijovski.nw_shells_delete_wrong_permissions.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-submission-fix-underwriters" = {
      handler                  = "src.tasks.maciej.backfill_nw_underwriters.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        SLEEP_TIME_SECONDS = "1"
        DRY_RUN            = "False"
      }
    }
    "nw-submissions-copy-snapshot-id" = {
      handler                  = "src.tasks.sudijovski.nw_submissions_copy_snapshot_id.patch_snapshots"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-submissions-split" = {
      handler                  = "src.tasks.sudijovski.nw_submissions_split.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        LIMIT     = "10000000"
        WAIT_TIME = "60"
      }
    }
    "nw-submissions-stage-sync" = {
      handler                  = "src.tasks.sudijovski.nw_submission_stages_sync.update_stages"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "nw-user_aliases" = {
      handler                  = "src.tasks.konrad.process_nw_user_aliases.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        SLEEP_TIME_SECONDS = "1"
        DRY_RUN            = "False"
      }
    }
    "paragon-missing-xs-backfill" = {
      handler = "src.tasks.maciej.paragon_missing_xs_backfill.main"
      memory  = "4096"
    }
    "paragon-uw-sync" = {
      handler = "src.tasks.paragon.underwriters_sync.execute"
      memory  = "4096"
    }
    "patryks-special-job" = {
      handler = "src.tasks.patryk.special_job.main"
      memory  = "4096"
      environment = {
        SAMPLE_JOB_SPECIFIC_VARIABLE = "3"
      }
      triggers = [
        {
          schedule = local.schedules.patryks_test_special_job
        },
      ]
    }
    "pinecone-backup" = {
      handler                  = "src.tasks.pinecone.backup_index.main"
      attempt_duration_seconds = 3600
      memory                   = "4096"
      environment = {
        PINECONE_API_KEY            = local.regional_secret_arns["pinecone-api-key"]
        PINECONE_API_ENV            = local.regional_secret_arns["pinecone-api-env"]
        PINECONE_INDEX_TO_BACKUP    = "ers-entity-prod-v5"
        PINECONE_REMOVE_OLD_BACKUPS = "True"
      }
      triggers = [
        {
          schedule = local.schedules.pinecone_ersprod_index_backup
        },
      ]
    }
    "recalculate-legal-filings-ids-capi" = {
      handler                  = "src.tasks.recalculate_document_ids.recalculate_legal_filings_ids_capi.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "recalculate-recommendations" = {
      handler = "src.tasks.recalculate_recommendations.recalculate_recommendations.main"
      memory  = "4096"
    }
    "recalculate-risk-scores" = {
      handler = "src.tasks.recalculate_recommendations.recalculate_risk_scores.main"
      memory  = "4096"
    }
    "redshift-data-consistency-check" = {
      handler                  = "src.tasks.redshift.redshift_data_consistency.execute"
      attempt_duration_seconds = 3600
      memory                   = "4096"
      triggers = [
        {
          schedule = local.schedules.redshift_consistency_check_job
        },
      ]
    }
    "redshift-warehouse-backup" = {
      handler                  = "src.tasks.redshift.redshift_warehouse_backup.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
      environment = {
        BACKUPS_BUCKET_NAME      = try(local.workflow_parameters.backup_bucket_names_by_region["us-east-1"], "")
        REDSHIFT_BACKUP_ROLE_ARN = try(local.workflow_parameters.backup_redshift_role_arn, "")
      }
      triggers = [
        {
          schedule = local.schedules.backup_redshift_warehouse_handler
        },
      ]
    }
    "regenerate-documents-ids" = {
      handler                  = "src.tasks.recalculate_document_ids.recalculate_documents_ids.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        CLEAR_TABLE = "0"
        CHUNK_SIZE  = "1000"
        LAST_ROW_NO = "0"
      }
    }
    "regenerate-ids-fmcsa-crashes" = {
      handler                  = "src.tasks.sudijovski.regenerate_ids_fmcsa_crashes.do_update"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "remove-cn-and-alias-job" = {
      handler                  = "src.tasks.remove_cn_and_alias.remove_cn_and_alias.main"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        CUT_OFF_DATE = "2023-06-20"
      }
    }
    "remove-in-process-scrapping-metadata" = {
      handler = "src.tasks.ivan.remove_in_process_scraping_metadata.run"
      memory  = "4096"
    }
    "remove-single-entity-composed-nodes" = {
      handler = "src.tasks.maciej.remove_single_entity_composed_nodes.execute"
      memory  = "4096"
      environment = {
        BATCH_SIZE = "100"
      }
    }
    "resynchronize-submissions" = {
      handler                  = "src.tasks.sudijovski.resynchronize_submissions.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "sf-rerun-failed-executions" = {
      handler                  = "src.tasks.step_functions.rerun_failed_executions.main"
      attempt_duration_seconds = 259200
      memory                   = "8192"
      vcpu                     = "2"
      environment = {
        DRY_RUN        = "False"
        SF_NAME_PREFIX = "None"  # example: "ingestion-"
        SF_NAMES       = "None"  # example: "ingestion-sf-1,ingestion-sf-2"
        SF_RERUN_ALL   = "False" # "True" or "False"; disables filters above
        SF_TIME_FROM   = "None"  # example: "2023-04-20 13:37:42" — in UTC
        SF_TIME_TO     = "None"  # example: "2023-04-20 13:37:42" — in UTC
        SF_RETRY_RATE  = "10"
      }
    }
    "shrink-databases-job" = {
      handler = "src.tasks.databases_shrinkers.shrink_databases_job.main"
      memory  = "4096"
    }
    "shrink-prod-facts-api-business-documents-job" = {
      handler                  = "src.tasks.databases_shrinkers.shrink_prod_facts_api_business_documents.main"
      attempt_duration_seconds = 604800
      memory                   = "4096"
      vcpu                     = "2"
      environment = {
        DELETE_BATCH_SIZE = "1000"
        DELETE_WORKERS    = "2"
        RUN_MODE          = "dry_run"
      }
    }
    "shrink-prod-facts-api-business-observations-job" = {
      handler                  = "src.tasks.databases_shrinkers.shrink_prod_facts_api_business_observations.main"
      attempt_duration_seconds = 604800
      memory                   = "4096"
      environment = {
        DELETE_BATCH_SIZE = "10000"
        DELETE_WORKERS    = "10"
        RUN_MODE          = "dry_run"
      }
    }
    "shrink-prod-facts-api-premises-observations-job" = {
      handler                  = "src.tasks.databases_shrinkers.shrink_prod_facts_api_premises_observations.main"
      attempt_duration_seconds = 604800
      memory                   = "4096"
      environment = {
        DELETE_BATCH_SIZE = "10000"
        DELETE_WORKERS    = "10"
        RUN_MODE          = "dry_run"
      }
    }
    "soft-delete-boss-duplicates" = {
      handler                  = "src.tasks.sudijovski.soft_delete_boss_duplicates.soft_delete_duplicates"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "split-historical-paragon-subs" = {
      handler = "src.tasks.paragon.historical_subs_split.execute"
      memory  = "4096"
      environment = {
        CUTOFF_DATE = "2023-12-01"
      }
    }
    "submissions-bulk-policy-assignment" = {
      handler                  = "src.tasks.sudijovski.submissions_bulk_policy_assignment.update_all"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "sync-ers-and-pinecone" = {
      handler = "src.tasks.sync_ers_and_pinecone.sync_ers_and_pinecone.execute"
      memory  = "32768"
      vcpu    = "16"
      environment = {
        MODEL_PATH             = "ers_optimized_for_cpu.onnx"
        NUM_WORKERS            = "10"
        FILE_NAME              = "entity_ids_prod.csv"
        GENERATE_EMBEDDING_IDS = "0"
        SAVE_TO_DB             = "0"
        INDEX_NAME             = "TODO"
      }
    }
    "sync-nw-boss-rows" = {
      handler                  = "src.tasks.sudijovski.sync_nw_boss_rows.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "unicourt-statuses-backfill" = {
      handler                  = "src.tasks.sudijovski.unicourt_statuses_backfill.execute"
      attempt_duration_seconds = 259200
      memory                   = "4096"
    }
    "migrate-nw-ml-notes" = {
      handler = "src.tasks.sudijovski.migrate_nw_ml_notes.execute"
      memory  = "4096"
    }
    "update_counties_in_k2_rules" = {
      handler = "src.tasks.donahue.ENG-26315.update_counties_in_k2_rules.main"
      memory  = "4096"
    }
    "populate-secura-sic-rules" = {
      handler = "src.tasks.donahue.ENG-28417.populate_secura_rules_sic_source_conditions.main"
      memory  = "4096"
    }
    "populate-secura-iso-rules" = {
      handler = "src.tasks.donahue.ENG-29088.populate_secura_rules_gl_codes_source_condition.main"
      memory  = "4096"
    }
    "update-legal-filings-capi" = {
      handler                  = "src.tasks.recalculate_document_ids.update_legal_filings_capi.run"
      attempt_duration_seconds = 259200
      memory                   = "4096"
      environment = {
        CONCURRENCY       = "5"
        UPDATE_CHUNK_SIZE = "1000"
        DELETE_CHUNK_SIZE = "25"
        LOGGING_FREQUENCY = "2500"
        PAUSE_IN_SECONDS  = "1"
      }
    }
    "update-relationships-with-sources" = {
      handler                  = "src.tasks.przemko.update_relationships_with_sources.execute"
      attempt_duration_seconds = 172800
      memory                   = "4096"
    }
    "calculate_used_fields_in_em" = {
      handler                  = "src.tasks.przemko.calculate_used_fileds_in_em.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
    }
    "trigger_summarization_for_some_subs" = {
      handler = "src.tasks.przemko.trigger_summarization_for_some_subs.execute"
      memory  = "4096"
    }
    "fix_bad_summarization_events" = {
      handler = "src.tasks.przemko.fix_bad_summarization_events.execute"
      memory  = "4096"
    }
    "update_resolved_businesses-special-job" = {
      handler = "src.tasks.capi.update_resolved_businesses.main"
      memory  = "4096"
      environment = {
        STARTING_DATE = "2024-01-01"
      }
    }
    "backfill-submission-businesses-id" = {
      handler                  = "src.tasks.patryk.ENG-23965.backfill_shareholders_and_wc_rating_sub_business_id.main"
      attempt_duration_seconds = 7200
      memory                   = "4096"
    }
    "clear-is-relevant-false" = {
      handler                  = "src.tasks.janek.clear_is_relevant_false.process_large_deletion"
      attempt_duration_seconds = 7200
      memory                   = "4096"
      environment = {
        BATCH_SIZE      = "10000"
        TEMP_INDEX_NAME = "temp_is_relevant_index"
      }
    }
    "rds-cron-jobs-check" = {
      handler                  = "src.tasks.patryk.rds_pg_cron_jobs_monitor.execute"
      attempt_duration_seconds = 3600
      memory                   = "4096"
      triggers = [
        {
          schedule = local.schedules.rds_cron_job_check
        },
      ]
    }
    "make-email-body-pdf-internal" = {
      handler = "src.tasks.sudijovski.make_email_body_pdf_internal.execute"
      memory  = "4096"
    }
    "conifer-report-permissions-backfill" = {
      handler                  = "src.tasks.sudijovski.conifer_report_permissions_backfill.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
    }
    "submission-sync-first-seen-backfill" = {
      handler                  = "src.tasks.sudijovski.submission_sync_first_seen_backfill.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
    }
    "backfill-ingestions" = {
      handler                  = "src.tasks.maciej.backfill_ingestion.execute"
      attempt_duration_seconds = 18000 # 5h
      memory                   = "4096"
    }
    "paragon-org-group-sync-backfill" = {
      handler                  = "src.tasks.sudijovski.paragon_org_group_sync_backfill.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
    }
    "normalize-geocoding-cache-addresses" = {
      handler                  = "src.tasks.marco.normalize_geocoding_cache_addresses.main"
      attempt_duration_seconds = 86400
      memory                   = "4096"
      environment = {
        BATCH_SIZE         = "10000"
        BEFORE_DATETIME    = "2026-01-01T00:00:00"
        SLEEP_TIME_SECONDS = "0"
      }
    }
    "submission-ingestion-nationwide-poc" = {
      handler                  = "src.tasks.submission_ingestion.nationwide_poc.run"
      attempt_duration_seconds = 7200
      memory                   = "4096"
      environment = {
        STORE_LOCAL  = "False"
        AUTO_RESOLVE = "False"
        DELETE_OLD   = "False"
        MAX_WORKERS  = "10"
        LIMIT        = "1000"
      }
    }
    "not-matched-sync-brokerages" = {
      handler                  = "src.tasks.submission_sync.not_matched_sync_brokerages.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
      triggers = [
        {
          schedule = local.schedules.not_matched_sync_brokerages
        },
      ]
    }
    "backfill-ingestion-for-organization" = {
      handler                  = "src.tasks.marco.backfill_ingestion.main"
      attempt_duration_seconds = 18000 # 5h
      memory                   = "4096"
      environment = {
        BACKFILL_IDENTIFIER = "e.g.: 'backfill_apartments_com'. Unique id for the backfill, used for resuming"
        SLEEP_TIME_SECONDS  = "12"
        ORGANIZATION_ID     = "0"
      }
    }
    "ingestion-ss-queue-is-new" = {
      handler                  = "src.tasks.sudijovski.ingestion_ss_queue_is_new.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
    }
    "secura-migrate-sync" = {
      handler                  = "src.tasks.sudijovski.secura_migrate_sync.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
    }
    "arch-fix-coverage-splits" = {
      handler                  = "src.tasks.sudijovski.arch_fix_coverage_splits.execute"
      attempt_duration_seconds = 2592000 # 30 days
      memory                   = "4096"
    }
    "sync-submissions-align-dates" = {
      handler                  = "src.tasks.sudijovski.sync_submissions_align_dates.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
    }
    "submission-premises-backfill" = {
      handler                  = "src.tasks.sudijovski.submission_premises_backfill.execute"
      attempt_duration_seconds = 7200
      memory                   = "4096"
    }
  }
}
