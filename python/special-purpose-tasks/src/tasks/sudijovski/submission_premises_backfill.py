import json
from dataclasses import dataclass
from functools import cached_property
from uuid import uuid4

from common.logic.addresses import try_parse_address_string
from entity_resolution_service_client import ApiClient as ApiClientV1
from entity_resolution_service_client import Configuration as ConfigurationV1
from entity_resolution_service_client import <PERSON><PERSON><PERSON><PERSON><PERSON> as DefaultApiV1
from entity_resolution_service_client import Premises as PremisesV1
from entity_resolution_service_client_v3 import ApiClient as ApiClientV3
from entity_resolution_service_client_v3 import Configuration as ConfigurationV3
from entity_resolution_service_client_v3 import Default<PERSON><PERSON> as DefaultApiV3
from entity_resolution_service_client_v3 import (
    Entity,
    EntityNameRequest,
    EntityPremisesRequest,
    EntityRequest,
    EntitySearchRequest,
)
from entity_resolution_service_client_v3 import Premises as PremisesV3
from entity_resolution_service_client_v3 import SearchEntityResults
from infrastructure_common.logging import get_logger
from more_itertools import chunked
from sqlalchemy.orm import Session

from src import ERS_URL, ERS_V3_URL
from src.db.db_session_provider import DBSessionProvider

logger = get_logger()

ers_client = DefaultApiV1(ApiClientV1(configuration=ConfigurationV1(ERS_URL)))
ers_client_v3 = DefaultApiV3(ApiClientV3(configuration=ConfigurationV3(ERS_V3_URL)))

QUERY = """
select s.id, ssmd.id, ssmd.named_insured, ssmd.named_insured_address, ss.organization_id
from submission_sync_matcher_data ssmd
         join submission_sync ss on ssmd.submission_sync_id = ss.id
         join submissions_client_ids sci on ss.policy_number = sci.client_submission_id
         join submissions s on sci.submission_id = s.id
         join reports_v2 r on s.report_id = r.id and r.organization_id = ss.organization_id;
"""


@dataclass
class PremisesRow:
    submission_id: str
    submission_sync_matcher_data_id: str
    named_insured: str | None
    address: str
    organization_id: int

    @property
    def additional_data_str(self) -> str | None:
        address_obj = try_parse_address_string(self.address)
        if not address_obj:
            return None
        additional_data = {
            "address_line_1": address_obj.address_line_1,
            "address_line_2": address_obj.address_line_2,
            "city": address_obj.city,
            "state": address_obj.state,
            "postal_code": address_obj.zip_code,
            "country": address_obj.country,
        }
        additional_data = {k: v for k, v in additional_data.items() if v is not None}
        return json.dumps(additional_data)

    @cached_property
    def premises(self) -> PremisesV3 | PremisesV1 | None:
        entity = self.entity
        if entity and entity.premises and len(entity.premises) > 0:
            return entity.premises[0].premises

        if not self.address:
            return None

        try:
            premises = ers_client.post_premises(address=self.address)
            return premises
        except Exception as e:
            logger.warning("Cannot post premises in ERS", name=self.named_insured, address=self.address, exc_info=e)
            return None

    @cached_property
    def entity(self) -> Entity | None:
        if not self.named_insured or not self.address:
            return None
        try:
            search_results: list[SearchEntityResults] = ers_client_v3.search_entities(
                entity_search_request=EntitySearchRequest(
                    entities=[
                        EntityRequest(
                            names=[EntityNameRequest(value=self.named_insured)],
                            premises=[EntityPremisesRequest(address=self.address)],
                        )
                    ],
                    high_confidence_only=True,
                ),
                skip_cache=False,
            )
            if not search_results or not search_results[0].data:
                return None
            entity = search_results[0].data[0].entity
            return entity
        except Exception as e:
            logger.warning("Failed searching entity in ERS", name=self.named_insured, address=self.address, exc_info=e)
            return None

    @cached_property
    def premises_id(self) -> str | None:
        premises = self.premises
        if not premises:
            return None
        return premises.id

    @property
    def insert_into_submission_premises(self) -> tuple[str, dict]:
        statement = (
            "insert into submission_premises "
            "(id, named_insured, address, submission_id, premises_id, "
            "submission_premises_type, additional_data, organization_id) "
            "values (:id, :named_insured, :address, :submission_id, :premises_id, 'SYNC', "
            ":additional_data, :organization_id)"
        )
        return statement, {
            "id": str(uuid4()),
            "named_insured": self.named_insured,
            "address": self.address,
            "submission_id": str(self.submission_id),
            "premises_id": self.premises_id,
            "additional_data": self.additional_data_str,
            "organization_id": self.organization_id,
        }

    @property
    def update_sync_matcher_data(self) -> tuple[str, dict]:
        statement = r"update submission_sync_matcher_data set additional_data = :additional_data where id = :id"
        return statement, {
            "additional_data": self.additional_data_str,
            "id": str(self.submission_sync_matcher_data_id),
        }


def get_matcher_data() -> list[PremisesRow]:
    """
    Get matcher data from the database.
    """
    # This function should be replaced with the actual implementation to fetch data from the database.
    # For now, we will return an empty list.
    with DBSessionProvider.get_copilot_reader_session() as session:
        session: Session
        rows = session.execute(QUERY).fetchall()
        return [
            PremisesRow(
                submission_id=row[0],
                submission_sync_matcher_data_id=row[1],
                named_insured=row[2],
                address=row[3],
                organization_id=row[4],
            )
            for row in rows
        ]


def update_sync_matcher_data(rows: list[PremisesRow]) -> None:
    """
    Update the submission_sync_matcher_data table with the additional data.
    """
    with DBSessionProvider.get_copilot_writer_session() as session:
        session: Session
        for row in rows:
            statement, params = row.update_sync_matcher_data
            session.execute(statement, params)


def insert_into_submission_premises(rows: list[PremisesRow]) -> None:
    """
    Insert the premises into the submission_premises table.
    """
    statements_params = [row.insert_into_submission_premises for row in rows]
    with DBSessionProvider.get_copilot_writer_session() as session:
        session: Session
        for row in statements_params:
            statement, params = row[0], row[1]
            session.execute(statement, params)


def execute():
    rows = get_matcher_data()
    chunks = chunked(rows, 1000)
    idx = 0
    for chunk in chunks:
        update_sync_matcher_data(chunk)
        insert_into_submission_premises(chunk)
        idx += 1


if __name__ == "__main__":
    execute()
