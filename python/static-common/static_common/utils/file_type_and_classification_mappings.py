from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_type import FileType

CLASSIFICATION_TO_FILE_TYPE = {
    ClassificationDocumentType.LOSS_RUN: FileType.LOSS_RUN,
    ClassificationDocumentType.LOSS_RUN_PDF: FileType.LOSS_RUN,
    ClassificationDocumentType.LOSS_RUN_EDITABLE_DOC: FileType.LOSS_RUN,
    ClassificationDocumentType.LOSS_RUN_SPREADSHEET: FileType.LOSS_RUN,
    ClassificationDocumentType.SUPPLEMENTAL_APPLICATION: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_SPREADSHEET: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_EDITABLE_DOC: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_PDF: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_SPREADSHEET: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_PDF: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_SPREADSHEET: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_PDF: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_SPREADSHEET: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC: (
        FileType.SUPPLEMENTAL_FORM
    ),
    ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_PDF: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_SPREADSHEET: (
        FileType.SUPPLEMENTAL_FORM
    ),
    ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC: (
        FileType.SUPPLEMENTAL_FORM
    ),
    ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_PDF: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_SPREADSHEET: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_PDF: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC: (
        FileType.SUPPLEMENTAL_FORM
    ),
    ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_SPREADSHEET: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.ACORD_FORM: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_101: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_125: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_126: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_127: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_128: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_129: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_130: FileType.ACORD_FORM,
    ClassificationDocumentType.APPLIED_130: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_131: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_139: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_140: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_160: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_211: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_35: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_175: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_823: FileType.ACORD_FORM,
    ClassificationDocumentType.ACORD_829: FileType.ACORD_FORM,
    ClassificationDocumentType.APPLIED_98: FileType.ACORD_FORM,
    ClassificationDocumentType.APPLIED_126: FileType.ACORD_FORM,
    ClassificationDocumentType.APPLIED_125: FileType.ACORD_FORM,
    ClassificationDocumentType.OFAPPINFCNI: FileType.ACORD_FORM,
    ClassificationDocumentType.OFSCHHAZ: FileType.ACORD_FORM,
    ClassificationDocumentType.DRIVERS: FileType.DRIVERS,
    ClassificationDocumentType.DRIVERS_PDF: FileType.DRIVERS,
    ClassificationDocumentType.DRIVERS_EDITABLE_DOC: FileType.DRIVERS,
    ClassificationDocumentType.DRIVERS_SPREADSHEET: FileType.DRIVERS,
    ClassificationDocumentType.VEHICLES: FileType.VEHICLES,
    ClassificationDocumentType.VEHICLES_PDF: FileType.VEHICLES,
    ClassificationDocumentType.VEHICLES_EDITABLE_DOC: FileType.VEHICLES,
    ClassificationDocumentType.VEHICLES_SPREADSHEET: FileType.VEHICLES,
    ClassificationDocumentType.SOV: FileType.SOV,
    ClassificationDocumentType.SOV_PDF: FileType.SOV,
    ClassificationDocumentType.SOV_EDITABLE_DOC: FileType.SOV,
    ClassificationDocumentType.SOV_SPREADSHEET: FileType.SOV,
    ClassificationDocumentType.WORK_COMP_PAYROLL: FileType.WORK_COMP_PAYROLL,
    ClassificationDocumentType.WORK_COMP_PAYROLL_PDF: FileType.WORK_COMP_PAYROLL,
    ClassificationDocumentType.WORK_COMP_PAYROLL_EDITABLE_DOC: FileType.WORK_COMP_PAYROLL,
    ClassificationDocumentType.WORK_COMP_PAYROLL_SPREADSHEET: FileType.WORK_COMP_PAYROLL,
    ClassificationDocumentType.WELL_SCHEDULE: FileType.WELL_SCHEDULE,
    ClassificationDocumentType.WELL_SCHEDULE_PDF: FileType.WELL_SCHEDULE,
    ClassificationDocumentType.WELL_SCHEDULE_EDITABLE_DOC: FileType.WELL_SCHEDULE,
    ClassificationDocumentType.WELL_SCHEDULE_SPREADSHEET: FileType.WELL_SCHEDULE,
    ClassificationDocumentType.NAMED_INSURED_SCHEDULE: FileType.NAMED_INSURED_SCHEDULE,
    ClassificationDocumentType.NAMED_INSURED_SCHEDULE_PDF: FileType.NAMED_INSURED_SCHEDULE,
    ClassificationDocumentType.NAMED_INSURED_SCHEDULE_EDITABLE_DOC: FileType.NAMED_INSURED_SCHEDULE,
    ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET: FileType.NAMED_INSURED_SCHEDULE,
    ClassificationDocumentType.GEOTECH_REPORT: FileType.GEOTECH_REPORT,
    ClassificationDocumentType.GEOTECH_REPORT_PDF: FileType.GEOTECH_REPORT,
    ClassificationDocumentType.GEOTECH_REPORT_EDITABLE_DOC: FileType.GEOTECH_REPORT,
    ClassificationDocumentType.GEOTECH_REPORT_SPREADSHEET: FileType.GEOTECH_REPORT,
    ClassificationDocumentType.SAFETY_MANUAL: FileType.SAFETY_MANUAL,
    ClassificationDocumentType.SAFETY_MANUAL_PDF: FileType.SAFETY_MANUAL,
    ClassificationDocumentType.SAFETY_MANUAL_EDITABLE_DOC: FileType.SAFETY_MANUAL,
    ClassificationDocumentType.SAFETY_MANUAL_SPREADSHEET: FileType.SAFETY_MANUAL,
    ClassificationDocumentType.SITE_REPORT: FileType.SITE_REPORT,
    ClassificationDocumentType.EMAIL: FileType.EMAIL,
    ClassificationDocumentType.RAW_EMAIL: FileType.RAW_EMAIL,
    ClassificationDocumentType.LOSS_SUMMARY: FileType.LOSS_RUN_SUMMARY,
    ClassificationDocumentType.LOSS_SUMMARY_PDF: FileType.LOSS_RUN_SUMMARY,
    ClassificationDocumentType.LOSS_SUMMARY_SPREADSHEET: FileType.LOSS_RUN_SUMMARY,
    ClassificationDocumentType.LOSS_SUMMARY_EDITABLE_DOC: FileType.LOSS_RUN_SUMMARY,
    ClassificationDocumentType.OTHER: FileType.OTHER,
    ClassificationDocumentType.OTHER_PDF: FileType.OTHER,
    ClassificationDocumentType.OTHER_SPREADSHEET: FileType.OTHER,
    ClassificationDocumentType.OTHER_EDITABLE_DOC: FileType.OTHER,
    ClassificationDocumentType.UNKNOWN: FileType.UNKNOWN,
    ClassificationDocumentType.MERGED: FileType.MERGED,
    ClassificationDocumentType.MERGED_PDF: FileType.MERGED,
    ClassificationDocumentType.MERGED_SPREADSHEET: FileType.MERGED,
    ClassificationDocumentType.MERGED_EDITABLE_DOC: FileType.MERGED,
    ClassificationDocumentType.ARCHIVE: FileType.ARCHIVE,
    ClassificationDocumentType.LOSS_RUN_NO_CLAIM: FileType.LOSS_RUN,
    ClassificationDocumentType.LOSS_RUN_NO_CLAIM_PDF: FileType.LOSS_RUN,
    ClassificationDocumentType.LOSS_RUN_NO_CLAIM_EDITABLE_DOC: FileType.LOSS_RUN,
    ClassificationDocumentType.LOSS_RUN_NO_CLAIM_SPREADSHEET: FileType.LOSS_RUN,
    ClassificationDocumentType.BUDGET: FileType.BUDGET,
    ClassificationDocumentType.BUDGET_PDF: FileType.BUDGET,
    ClassificationDocumentType.BUDGET_SPREADSHEET: FileType.BUDGET,
    ClassificationDocumentType.BUDGET_EDITABLE_DOC: FileType.BUDGET,
    ClassificationDocumentType.ORG_CHART: FileType.COMPANY_STRUCTURE,
    ClassificationDocumentType.ORG_CHART_PDF: FileType.COMPANY_STRUCTURE,
    ClassificationDocumentType.ORG_CHART_EDITABLE_DOC: FileType.COMPANY_STRUCTURE,
    ClassificationDocumentType.ORG_CHART_SPREADSHEET: FileType.COMPANY_STRUCTURE,
    ClassificationDocumentType.COMPANY_BYLAWS: FileType.COMPANY_BYLAWS,
    ClassificationDocumentType.COMPANY_BYLAWS_PDF: FileType.COMPANY_BYLAWS,
    ClassificationDocumentType.COMPANY_BYLAWS_EDITABLE_DOC: FileType.COMPANY_BYLAWS,
    ClassificationDocumentType.COMPANY_BYLAWS_SPREADSHEET: FileType.COMPANY_BYLAWS,
    ClassificationDocumentType.HIRING_GUIDELINES: FileType.HIRING_GUIDELINES,
    ClassificationDocumentType.HIRING_GUIDELINES_PDF: FileType.HIRING_GUIDELINES,
    ClassificationDocumentType.HIRING_GUIDELINES_EDITABLE_DOC: FileType.HIRING_GUIDELINES,
    ClassificationDocumentType.HIRING_GUIDELINES_SPREADSHEET: FileType.HIRING_GUIDELINES,
    ClassificationDocumentType.EMPLOYEE_HANDBOOK: FileType.EMPLOYEE_HANDBOOK,
    ClassificationDocumentType.EMPLOYEE_HANDBOOK_PDF: FileType.EMPLOYEE_HANDBOOK,
    ClassificationDocumentType.EMPLOYEE_HANDBOOK_EDITABLE_DOC: FileType.EMPLOYEE_HANDBOOK,
    ClassificationDocumentType.EMPLOYEE_HANDBOOK_SPREADSHEET: FileType.EMPLOYEE_HANDBOOK,
    ClassificationDocumentType.RESUME: FileType.RESUME,
    ClassificationDocumentType.RESUME_PDF: FileType.RESUME,
    ClassificationDocumentType.RESUME_EDITABLE_DOC: FileType.RESUME,
    ClassificationDocumentType.RESUME_SPREADSHEET: FileType.RESUME,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_PDF: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_EDITABLE_DOC: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_SPREADSHEET: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_CA: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_CA_PDF: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_CA_EDITABLE_DOC: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_CA_SPREADSHEET: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_IN: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_IN_PDF: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_IN_EDITABLE_DOC: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_IN_SPREADSHEET: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_NJ: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_NJ_PDF: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_NJ_EDITABLE_DOC: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_NJ_SPREADSHEET: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_NY: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_NY_PDF: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_NY_EDITABLE_DOC: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_NY_SPREADSHEET: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_PA: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_PA_PDF: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_PA_SPREADSHEET: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_PA_EDITABLE_DOC: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_MN: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_MN_PDF: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_MN_SPREADSHEET: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_MN_EDITABLE_DOC: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_WI: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_WI_PDF: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_WI_SPREADSHEET: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.WORK_COMP_EXPERIENCE_WI_EDITABLE_DOC: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.FINANCIAL_STATEMENT: FileType.FINANCIAL_STATEMENT,
    ClassificationDocumentType.FINANCIAL_STATEMENT_PDF: FileType.FINANCIAL_STATEMENT,
    ClassificationDocumentType.FINANCIAL_STATEMENT_SPREADSHEET: FileType.FINANCIAL_STATEMENT,
    ClassificationDocumentType.FINANCIAL_STATEMENT_EDITABLE_DOC: FileType.FINANCIAL_STATEMENT,
    ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT: FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
    ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF: FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
    ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_SPREADSHEET: FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
    ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_EDITABLE_DOC: FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
    ClassificationDocumentType.AUTO_DEALER_FINANCIAL_STATEMENT: FileType.FINANCIAL_STATEMENT,
    ClassificationDocumentType.AUTO_DEALER_FINANCIAL_STATEMENT_PDF: FileType.FINANCIAL_STATEMENT,
    ClassificationDocumentType.AUTO_DEALER_FINANCIAL_STATEMENT_SPREADSHEET: FileType.FINANCIAL_STATEMENT,
    ClassificationDocumentType.AUTO_DEALER_FINANCIAL_STATEMENT_EDITABLE_DOC: FileType.FINANCIAL_STATEMENT,
    ClassificationDocumentType.IFTA: FileType.IFTA,
    ClassificationDocumentType.IFTA_PDF: FileType.IFTA,
    ClassificationDocumentType.IFTA_SPREADSHEET: FileType.IFTA,
    ClassificationDocumentType.IFTA_EDITABLE_DOC: FileType.IFTA,
    ClassificationDocumentType.MVR: FileType.MVR,
    ClassificationDocumentType.MVR_PDF: FileType.MVR,
    ClassificationDocumentType.MVR_SPREADSHEET: FileType.MVR,
    ClassificationDocumentType.MVR_EDITABLE_DOC: FileType.MVR,
    ClassificationDocumentType.ALLY_AUTO_PROPERTY_SOV: FileType.ALLY_AUTO_SOV,
    ClassificationDocumentType.ALLY_AUTO_SUPPLEMENTAL: FileType.ALLY_AUTO_SOV,
    ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_PDF: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_SPREADSHEET: FileType.SUPPLEMENTAL_FORM,
    ClassificationDocumentType.EMPTY: FileType.EMPTY,
    ClassificationDocumentType.CAB_REPORT: FileType.CAB_REPORT,
    ClassificationDocumentType.COVER_SHEET: FileType.COVER_SHEET,
    ClassificationDocumentType.COVER_SHEET_PDF: FileType.COVER_SHEET,
    ClassificationDocumentType.COVER_SHEET_SPREADSHEET: FileType.COVER_SHEET,
    ClassificationDocumentType.COVER_SHEET_EDITABLE_DOC: FileType.COVER_SHEET,
    ClassificationDocumentType.ERISA_FORM_5500: FileType.ERISA_FORM_5500,
    ClassificationDocumentType.ERISA_FORM_5500_PDF: FileType.ERISA_FORM_5500,
    ClassificationDocumentType.ERISA_FORM_5500_SPREADSHEET: FileType.ERISA_FORM_5500,
    ClassificationDocumentType.ERISA_FORM_5500_EDITABLE_DOC: FileType.ERISA_FORM_5500,
    ClassificationDocumentType.ERISA_FORM_5500_SF: FileType.ERISA_FORM_5500_SF,
    ClassificationDocumentType.ERISA_FORM_5500_SF_PDF: FileType.ERISA_FORM_5500_SF,
    ClassificationDocumentType.ERISA_FORM_5500_SF_SPREADSHEET: FileType.ERISA_FORM_5500_SF,
    ClassificationDocumentType.ERISA_FORM_5500_SF_EDITABLE_DOC: FileType.ERISA_FORM_5500_SF,
    ClassificationDocumentType.DIRECTORS_AND_OFFICERS: FileType.DIRECTORS_AND_OFFICERS,
    ClassificationDocumentType.DIRECTORS_AND_OFFICERS_PDF: FileType.DIRECTORS_AND_OFFICERS,
    ClassificationDocumentType.DIRECTORS_AND_OFFICERS_SPREADSHEET: FileType.DIRECTORS_AND_OFFICERS,
    ClassificationDocumentType.DIRECTORS_AND_OFFICERS_EDITABLE_DOC: FileType.DIRECTORS_AND_OFFICERS,
    ClassificationDocumentType.EEOC: FileType.EEOC,
    ClassificationDocumentType.EEOC_PDF: FileType.EEOC,
    ClassificationDocumentType.EEOC_SPREADSHEET: FileType.EEOC,
    ClassificationDocumentType.EEOC_EDITABLE_DOC: FileType.EEOC,
    ClassificationDocumentType.HTML_DOCUMENT: FileType.HTML_DOCUMENT,
    ClassificationDocumentType.EQUIPMENT: FileType.EQUIPMENT,
    ClassificationDocumentType.EQUIPMENT_PDF: FileType.EQUIPMENT,
    ClassificationDocumentType.EQUIPMENT_SPREADSHEET: FileType.EQUIPMENT,
    ClassificationDocumentType.EQUIPMENT_EDITABLE_DOC: FileType.EQUIPMENT,
    ClassificationDocumentType.BROKER_OF_RECORD_LETTER: FileType.BROKER_OF_RECORD_LETTER,
    ClassificationDocumentType.BROKER_OF_RECORD_LETTER_PDF: FileType.BROKER_OF_RECORD_LETTER,
    ClassificationDocumentType.BROKER_OF_RECORD_LETTER_SPREADSHEET: FileType.BROKER_OF_RECORD_LETTER,
    ClassificationDocumentType.BROKER_OF_RECORD_LETTER_EDITABLE_DOC: FileType.BROKER_OF_RECORD_LETTER,
    ClassificationDocumentType.EMOD_SUMMARY: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.EMOD_SUMMARY_PDF: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.EMOD_SUMMARY_EDITABLE_DOC: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.EMOD_SUMMARY_SPREADSHEET: FileType.WORK_COMP_EXPERIENCE,
    ClassificationDocumentType.QUOTE: FileType.QUOTE,
    ClassificationDocumentType.QUOTE_PDF: FileType.QUOTE,
    ClassificationDocumentType.QUOTE_EDITABLE_DOC: FileType.QUOTE,
    ClassificationDocumentType.QUOTE_SPREADSHEET: FileType.QUOTE,
    ClassificationDocumentType.GENERAL_LIABILITY_QUOTE: FileType.GENERAL_LIABILITY_QUOTE,
    ClassificationDocumentType.PROJECT_SCHEDULE: FileType.PROJECT_SCHEDULE,
    ClassificationDocumentType.PROJECT_SCHEDULE_PDF: FileType.PROJECT_SCHEDULE,
    ClassificationDocumentType.PROJECT_SCHEDULE_EDITABLE_DOC: FileType.PROJECT_SCHEDULE,
    ClassificationDocumentType.PROJECT_SCHEDULE_SPREADSHEET: FileType.PROJECT_SCHEDULE,
    ClassificationDocumentType.PROJECT_LIST: FileType.UNKNOWN,
    ClassificationDocumentType.PROJECT_LIST_PDF: FileType.UNKNOWN,
    ClassificationDocumentType.PROJECT_LIST_EDITABLE_DOC: FileType.UNKNOWN,
    ClassificationDocumentType.PROJECT_LIST_SPREADSHEET: FileType.UNKNOWN,
    ClassificationDocumentType.ADDITIONAL_INSURED_SCHEDULE: FileType.UNKNOWN,
    ClassificationDocumentType.ADDITIONAL_INSURED_SCHEDULE_PDF: FileType.UNKNOWN,
    ClassificationDocumentType.ADDITIONAL_INSURED_SCHEDULE_EDITABLE_DOC: FileType.UNKNOWN,
    ClassificationDocumentType.ADDITIONAL_INSURED_SCHEDULE_SPREADSHEET: FileType.UNKNOWN,
    ClassificationDocumentType.SHAREHOLDERS: FileType.UNKNOWN,
    ClassificationDocumentType.SHAREHOLDERS_PDF: FileType.UNKNOWN,
    ClassificationDocumentType.SHAREHOLDERS_EDITABLE_DOC: FileType.UNKNOWN,
    ClassificationDocumentType.SHAREHOLDERS_SPREADSHEET: FileType.UNKNOWN,
    ClassificationDocumentType.EXPOSURE_SHEET: FileType.UNKNOWN,
    ClassificationDocumentType.EXPOSURE_SHEET_PDF: FileType.UNKNOWN,
    ClassificationDocumentType.EXPOSURE_SHEET_EDITABLE_DOC: FileType.UNKNOWN,
    ClassificationDocumentType.EXPOSURE_SHEET_SPREADSHEET: FileType.UNKNOWN,
    ClassificationDocumentType.NDA: FileType.NDA,
    ClassificationDocumentType.NDA_PDF: FileType.NDA,
    ClassificationDocumentType.NDA_EDITABLE_DOC: FileType.NDA,
    ClassificationDocumentType.NDA_SPREADSHEET: FileType.NDA,
    ClassificationDocumentType.LETTER_OF_INTENT: FileType.LETTER_OF_INTENT,
    ClassificationDocumentType.LETTER_OF_INTENT_PDF: FileType.LETTER_OF_INTENT,
    ClassificationDocumentType.LETTER_OF_INTENT_EDITABLE_DOC: FileType.LETTER_OF_INTENT,
    ClassificationDocumentType.INVESTMENT_MEMORANDUM: FileType.INVESTMENT_MEMORANDUM,
    ClassificationDocumentType.INVESTMENT_MEMORANDUM_PDF: FileType.INVESTMENT_MEMORANDUM,
    ClassificationDocumentType.INVESTMENT_MEMORANDUM_EDITABLE_DOC: FileType.INVESTMENT_MEMORANDUM,
    ClassificationDocumentType.INVESTMENT_JOINDER: FileType.INVESTMENT_JOINDER,
    ClassificationDocumentType.INVESTMENT_JOINDER_PDF: FileType.INVESTMENT_JOINDER,
    ClassificationDocumentType.INVESTMENT_JOINDER_EDITABLE_DOC: FileType.INVESTMENT_JOINDER,
    ClassificationDocumentType.PURCHASE_AGREEMENT: FileType.PURCHASE_AGREEMENT,
    ClassificationDocumentType.PURCHASE_AGREEMENT_PDF: FileType.PURCHASE_AGREEMENT,
    ClassificationDocumentType.PURCHASE_AGREEMENT_EDITABLE_DOC: FileType.PURCHASE_AGREEMENT,
    ClassificationDocumentType.PURCHASE_AGREEMENT_SPREADSHEET: FileType.PURCHASE_AGREEMENT,
    ClassificationDocumentType.POLICY: FileType.POLICY,
    ClassificationDocumentType.POLICY_PDF: FileType.POLICY,
    ClassificationDocumentType.POLICY_EDITABLE_DOC: FileType.POLICY,
    ClassificationDocumentType.POLICY_SPREADSHEET: FileType.POLICY,
    ClassificationDocumentType.CUSTOM: FileType.CUSTOM,
    ClassificationDocumentType.ENDORSEMENT: FileType.ENDORSEMENT,
    ClassificationDocumentType.ENDORSEMENT_PDF: FileType.ENDORSEMENT,
    ClassificationDocumentType.ENDORSEMENT_EDITABLE_DOC: FileType.ENDORSEMENT,
    ClassificationDocumentType.ENDORSEMENT_SPREADSHEET: FileType.ENDORSEMENT,
}

FILE_TYPE_TO_CLASSIFICATION = {
    FileType.LOSS_RUN: ClassificationDocumentType.LOSS_RUN,
    FileType.LOSS_RUN_SUMMARY: ClassificationDocumentType.LOSS_SUMMARY,
    FileType.SUPPLEMENTAL_FORM: ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
    FileType.EMAIL: ClassificationDocumentType.EMAIL,
    FileType.RAW_EMAIL: ClassificationDocumentType.RAW_EMAIL,
    FileType.SITE_REPORT: ClassificationDocumentType.SITE_REPORT,
    FileType.SAFETY_MANUAL: ClassificationDocumentType.SAFETY_MANUAL,
    FileType.SOV: ClassificationDocumentType.SOV,
    FileType.NAMED_INSURED_SCHEDULE: ClassificationDocumentType.NAMED_INSURED_SCHEDULE,
    FileType.DRIVERS: ClassificationDocumentType.DRIVERS,
    FileType.VEHICLES: ClassificationDocumentType.VEHICLES,
    FileType.GEOTECH_REPORT: ClassificationDocumentType.GEOTECH_REPORT,
    FileType.ACORD_FORM: ClassificationDocumentType.ACORD_FORM,
    FileType.OTHER: ClassificationDocumentType.OTHER,
    FileType.ARCHIVE: ClassificationDocumentType.ARCHIVE,
    FileType.MERGED: ClassificationDocumentType.MERGED,
    FileType.BUDGET: ClassificationDocumentType.BUDGET,
    FileType.UNKNOWN: ClassificationDocumentType.UNKNOWN,
    FileType.COMPANY_BYLAWS: ClassificationDocumentType.COMPANY_BYLAWS,
    FileType.RESUME: ClassificationDocumentType.RESUME,
    FileType.EMPLOYEE_HANDBOOK: ClassificationDocumentType.EMPLOYEE_HANDBOOK,
    FileType.HIRING_GUIDELINES: ClassificationDocumentType.HIRING_GUIDELINES,
    FileType.COMPANY_STRUCTURE: ClassificationDocumentType.ORG_CHART,
    FileType.FINANCIAL_STATEMENT: ClassificationDocumentType.FINANCIAL_STATEMENT,
    FileType.CONSOLIDATED_FINANCIAL_STATEMENT: ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT,
    FileType.IFTA: ClassificationDocumentType.IFTA,
    FileType.MVR: ClassificationDocumentType.MVR,
    FileType.WORK_COMP_EXPERIENCE: ClassificationDocumentType.WORK_COMP_EXPERIENCE,
    FileType.CAB_REPORT: ClassificationDocumentType.CAB_REPORT,
    FileType.EMPTY: ClassificationDocumentType.EMPTY,
    FileType.COVER_SHEET: ClassificationDocumentType.COVER_SHEET,
    FileType.DIRECTORS_AND_OFFICERS: ClassificationDocumentType.DIRECTORS_AND_OFFICERS,
    FileType.HTML_DOCUMENT: ClassificationDocumentType.HTML_DOCUMENT,
    FileType.ERISA_FORM_5500: ClassificationDocumentType.ERISA_FORM_5500,
    FileType.ERISA_FORM_5500_SF: ClassificationDocumentType.ERISA_FORM_5500_SF,
    FileType.EEOC: ClassificationDocumentType.EEOC,
    FileType.EQUIPMENT: ClassificationDocumentType.EQUIPMENT,
    FileType.BROKER_OF_RECORD_LETTER: ClassificationDocumentType.BROKER_OF_RECORD_LETTER,
    FileType.PROJECT_SCHEDULE: ClassificationDocumentType.PROJECT_SCHEDULE,
    FileType.QUOTE: ClassificationDocumentType.QUOTE,
    FileType.GENERAL_LIABILITY_QUOTE: ClassificationDocumentType.GENERAL_LIABILITY_QUOTE,
    FileType.NDA: ClassificationDocumentType.NDA,
    FileType.LETTER_OF_INTENT: ClassificationDocumentType.LETTER_OF_INTENT,
    FileType.INVESTMENT_MEMORANDUM: ClassificationDocumentType.INVESTMENT_MEMORANDUM,
    FileType.INVESTMENT_JOINDER: ClassificationDocumentType.INVESTMENT_JOINDER,
    FileType.PURCHASE_AGREEMENT: ClassificationDocumentType.PURCHASE_AGREEMENT,
    FileType.POLICY: ClassificationDocumentType.POLICY,
    FileType.CUSTOM: ClassificationDocumentType.CUSTOM,
    FileType.ENDORSEMENT: ClassificationDocumentType.ENDORSEMENT,
}

FILE_TYPE_TO_TEXT_EXTRACTABLE_CLASSIFICATIONS: dict[FileType, list[ClassificationDocumentType]] = {
    FileType.CAT_RESULT: [],
    FileType.LOSS_RUN: [ClassificationDocumentType.LOSS_RUN_PDF, ClassificationDocumentType.LOSS_RUN_NO_CLAIM_PDF],
    FileType.LOSS_RUN_SUMMARY: [ClassificationDocumentType.LOSS_SUMMARY_PDF],
    FileType.OTHER: [ClassificationDocumentType.OTHER_PDF],
    FileType.CAT_REQUEST_FORM: [],
    FileType.SOV: [ClassificationDocumentType.SOV_PDF],
    FileType.NAMED_INSURED_SCHEDULE: [ClassificationDocumentType.NAMED_INSURED_SCHEDULE_PDF],
    FileType.SAFETY_MANUAL: [ClassificationDocumentType.SAFETY_MANUAL_PDF],
    FileType.SITE_REPORT: [ClassificationDocumentType.SITE_REPORT],
    FileType.DRIVERS: [ClassificationDocumentType.DRIVERS_PDF],
    FileType.VEHICLES: [ClassificationDocumentType.VEHICLES_PDF],
    FileType.EQUIPMENT: [ClassificationDocumentType.EQUIPMENT_PDF],
    FileType.GEOTECH_REPORT: [ClassificationDocumentType.GEOTECH_REPORT_PDF],
    FileType.SUPPLEMENTAL_FORM: [
        ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
        ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_PDF,
        ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_PDF,
        ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_PDF,
        ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_PDF,
        ClassificationDocumentType.TRANSPORTATION_SUPPLEMENTAL_APPLICATION_PDF,
    ],
    FileType.EMAIL: [ClassificationDocumentType.EMAIL],
    FileType.RAW_EMAIL: [],
    FileType.CORRESPONDENCE_EMAIL: [],
    FileType.VEHICLES_UNDERLYING_POLICY: [],
    FileType.ACORD_FORM: [],
    FileType.COMPANY_STRUCTURE: [ClassificationDocumentType.ORG_CHART_PDF],
    FileType.EMPLOYEE_HANDBOOK: [ClassificationDocumentType.EMPLOYEE_HANDBOOK_PDF],
    FileType.HIRING_GUIDELINES: [ClassificationDocumentType.HIRING_GUIDELINES_PDF],
    FileType.COMPANY_BYLAWS: [ClassificationDocumentType.COMPANY_BYLAWS_PDF],
    FileType.RESUME: [ClassificationDocumentType.RESUME_PDF],
    FileType.BUDGET: [ClassificationDocumentType.BUDGET_PDF],
    FileType.FINANCIAL_STATEMENT: [ClassificationDocumentType.FINANCIAL_STATEMENT_PDF],
    FileType.CONSOLIDATED_FINANCIAL_STATEMENT: [ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF],
    FileType.WORK_COMP_EXPERIENCE: [ClassificationDocumentType.WORK_COMP_EXPERIENCE_PDF],
    FileType.WORK_COMP_PAYROLL: [ClassificationDocumentType.WORK_COMP_PAYROLL_PDF],
    FileType.WELL_SCHEDULE: [ClassificationDocumentType.WELL_SCHEDULE_PDF],
    FileType.MVR: [ClassificationDocumentType.MVR_PDF],
    FileType.IFTA: [ClassificationDocumentType.IFTA_PDF],
    FileType.MERGED: [ClassificationDocumentType.MERGED_PDF],
    FileType.ARCHIVE: [],
    FileType.COPILOT_TERMS_AND_CONDITIONS: [],
    FileType.ALLY_AUTO_SOV: [],
    FileType.UNKNOWN: [ClassificationDocumentType.UNKNOWN],
    FileType.EMPTY: [],
    FileType.CAB_REPORT: [ClassificationDocumentType.CAB_REPORT],
    FileType.HTML_DOCUMENT: [],
    FileType.COVER_SHEET: [ClassificationDocumentType.COVER_SHEET_PDF],
    FileType.DIRECTORS_AND_OFFICERS: [ClassificationDocumentType.DIRECTORS_AND_OFFICERS_PDF],
    FileType.ERISA_FORM_5500: [ClassificationDocumentType.ERISA_FORM_5500_PDF],
    FileType.ERISA_FORM_5500_SF: [ClassificationDocumentType.ERISA_FORM_5500_SF_PDF],
    FileType.EEOC: [ClassificationDocumentType.EEOC_PDF],
    FileType.CLIENT_DATA: [],
    FileType.BROKER_OF_RECORD_LETTER: [ClassificationDocumentType.BROKER_OF_RECORD_LETTER_PDF],
    FileType.PROJECT_SCHEDULE: [ClassificationDocumentType.PROJECT_SCHEDULE_PDF],
    FileType.QUOTE: [ClassificationDocumentType.QUOTE_PDF],
    FileType.GENERAL_LIABILITY_QUOTE: [ClassificationDocumentType.GENERAL_LIABILITY_QUOTE],
    FileType.NDA: [ClassificationDocumentType.NDA_PDF],
    FileType.LETTER_OF_INTENT: [ClassificationDocumentType.LETTER_OF_INTENT_PDF],
    FileType.INVESTMENT_MEMORANDUM: [ClassificationDocumentType.INVESTMENT_MEMORANDUM_PDF],
    FileType.INVESTMENT_JOINDER: [ClassificationDocumentType.INVESTMENT_JOINDER_PDF],
    FileType.PURCHASE_AGREEMENT: [ClassificationDocumentType.PURCHASE_AGREEMENT_PDF],
    FileType.CUSTOM: [],
    FileType.POLICY: [ClassificationDocumentType.POLICY_PDF],
    FileType.ENDORSEMENT: [ClassificationDocumentType.ENDORSEMENT_PDF],
}
