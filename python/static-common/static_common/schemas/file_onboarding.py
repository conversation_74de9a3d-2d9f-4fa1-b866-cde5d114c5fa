from marshmallow import <PERSON>X<PERSON><PERSON><PERSON>, <PERSON>hema, ValidationError
from marshmallow import fields as f
from marshmallow import post_dump, post_load, pre_dump, pre_load, validates_schema
from marshmallow_enum import EnumField
from marshmallow_oneofschema import OneOfSchema

from static_common.constants import DEFAULT
from static_common.enums.aggregation import AggregationType
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.shareholder_type import ShareholderType
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.enums.units import Units
from static_common.models.file_onboarding import (
    Acord126TransientData,
    Acord131TransientData,
    Acord139TransientData,
    Acord140TransientData,
    Acord160TransientData,
    AcordLocationInformation,
    AdditionalData,
    BoundingBox,
    CustomizableClassifierEvidence,
    Evidence,
    ExtractedEntityInfo,
    FactSubtypeSuggestion,
    FileEntitiesExtractionInfo,
    HazardInformationWithEntity,
    OnboardedFile,
    PDFEvidence,
    PolicyInformation,
    PremisesInformationTableRow,
    PremisesInformationTableWithEntity,
    ResolvedDataField,
    ResolvedDataValue,
    ShareholderInformation,
    SpreadsheetEvidence,
    SubmissionEntity,
    SuggestedField,
    TransientData,
    ValueSuggestionResult,
    ValueValidationResult,
)
from static_common.schemas.matching_metadata import MatchingMetadataSchema
from static_common.taxonomies.industry_classification import NaicsCode


class AcordLocationInformationSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    location_number = f.Int(allow_none=True, required=False)
    building_number = f.Int(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> AcordLocationInformation:
        return AcordLocationInformation(**data)


class TransientDataSchema(Schema):
    file_classification = EnumField(ClassificationDocumentType, by_value=True, required=True, allow_none=False)
    named_insured = f.String(allow_none=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> TransientData:
        return TransientData(**data)


class PremisesInformationTableRowSchema(Schema):
    subject_of_insurance = f.String(allow_none=True)
    amount = f.Float(allow_none=True)
    coins_percentage = f.Float(allow_none=True)
    valuation = f.String(allow_none=True)
    causes_of_loss = f.String(allow_none=True)
    inflation_guard_percentage = f.Float(allow_none=True)
    deductible = f.Float(allow_none=True)
    deductible_type = f.String(allow_none=True)
    blanket_number = f.Float(allow_none=True)
    forms_and_conditions_to_apply = f.String(allow_none=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> PremisesInformationTableRow:
        return PremisesInformationTableRow(**data)


class PremisesInformationTableWithEntitySchema(Schema):
    requested_name = f.String(allow_none=True)
    requested_address = f.String(allow_none=True)
    location_number = f.Integer(allow_none=True)
    building_number = f.Integer(allow_none=True)
    premises_information_table = f.Nested(PremisesInformationTableRowSchema, allow_none=False, missing=[], many=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> PremisesInformationTableWithEntity:
        return PremisesInformationTableWithEntity(**data)


class HazardInformationWithEntitySchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    requested_name = f.String(allow_none=True)
    requested_address = f.String(allow_none=True)
    location_number = f.Integer(allow_none=True)
    building_number = f.Integer(allow_none=True)
    hazard_number = f.Integer(allow_none=True)
    class_code = f.String(allow_none=True)
    premium_basis = f.String(allow_none=True)
    exposure = f.Integer(allow_none=True)
    territorial_rating = f.String(allow_none=True)
    rate_prem_ops = f.Float(allow_none=True)
    rate_products = f.Float(allow_none=True)
    premium_prem_ops = f.Float(allow_none=True)
    premium_products = f.Float(allow_none=True)
    classification = f.String(allow_none=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> HazardInformationWithEntity:
        return HazardInformationWithEntity(**data)


class PolicyInformationSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    new = f.Boolean(allow_none=True)
    renew = f.Boolean(allow_none=True)
    umbrella = f.Boolean(allow_none=True)
    excess = f.Boolean(allow_none=True)
    occurrence = f.Boolean(allow_none=True)
    claims_made = f.Boolean(allow_none=True)
    voluntary = f.Boolean(allow_none=True)
    limits_occurrence = f.Float(allow_none=True)
    limits_general_aggregate = f.Float(allow_none=True)
    proposed_retroactive_date = f.String(allow_none=True)
    current_retroactive_date = f.String(allow_none=True)
    retained_limit = f.Float(allow_none=True)
    first_dollar_defense = f.Boolean(allow_none=True)

    @pre_load
    def normalize_date(self, data, **kwargs):
        if (
            "proposed_retroactive_date" in data
            and data["proposed_retroactive_date"]
            and hasattr(data["proposed_retroactive_date"], "strftime")
        ):
            data["proposed_retroactive_date"] = data["proposed_retroactive_date"].strftime("%Y-%m-%d")
        if (
            "current_retroactive_date" in data
            and data["current_retroactive_date"]
            and hasattr(data["current_retroactive_date"], "strftime")
        ):
            data["current_retroactive_date"] = data["current_retroactive_date"].strftime("%Y-%m-%d")
        return data

    @post_load
    def make_obj(self, data, *args, **kwargs) -> PolicyInformation:
        return PolicyInformation(**data)


class Acord140TransientDataSchema(TransientDataSchema):
    premises_info = f.Nested(PremisesInformationTableWithEntitySchema, allow_none=True, many=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Acord140TransientData:
        return Acord140TransientData(**data)


class Acord126TransientDataSchema(TransientDataSchema):
    schedule_of_hazards = f.Nested(HazardInformationWithEntitySchema, allow_none=True, many=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Acord126TransientData:
        return Acord126TransientData(**data)


class Acord131TransientDataSchema(TransientDataSchema):
    policy_information = f.Nested(PolicyInformationSchema, allow_none=True, many=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Acord131TransientData:
        return Acord131TransientData(**data)


class Acord160TransientDataSchema(TransientDataSchema):
    @post_load
    def make_obj(self, data, *args, **kwargs) -> Acord160TransientData:
        return Acord160TransientData(**data)


class Acord139TransientDataSchema(TransientDataSchema):
    premises_info = f.Nested(PremisesInformationTableWithEntitySchema, allow_none=True, many=True)

    @pre_load
    def transform_preload_data_to_match_acord_140(self, data, *args, **kwargs) -> dict:
        """
        Transforms input data to match Acord140PremisesInformationTableSchema.
        - 'value_100_percent' -> 'amount',
        - 'subject' -> 'subject_of_insurance'

        Groups rows by location_number + building_number into a nested structure under 'premises_information_table'
        """
        grouped_data = {}

        for row in data.get("premises_info", []):
            location_number = row.get("location_number")
            building_number = row.get("building_number")
            key = f"{location_number}-{building_number}"

            if key not in grouped_data:
                grouped_data[key] = {
                    "location_number": location_number,
                    "building_number": building_number,
                    "premises_information_table": [],
                }

            grouped_data[key]["premises_information_table"].append(
                {
                    "subject_of_insurance": row.get("subject"),
                    "amount": row.get("value_100_percent"),
                    "valuation": row.get("valuation"),
                }
            )

        return {"premises_info": list(grouped_data.values()), "file_classification": data.get("file_classification")}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Acord139TransientData:
        return Acord139TransientData(**data)


class EvidenceSchema(Schema):
    SKIP_VALUES = {
        DEFAULT: [None, ""],
        "file_idx": [None, 0],
    }

    class Meta(Schema.Meta):
        unknown = EXCLUDE

    file_idx = f.Int(allow_none=False, required=False, load_default=0)
    file_id = f.UUID(allow_none=True, required=False)
    observed_name = f.String(allow_none=True, required=False)
    observed_value = f.String(allow_none=True, required=False)
    page = f.Int(allow_none=True, required=False)
    confidence = f.Float(allow_none=True, required=False)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {
            key: value
            for key, value in data.items()
            if value not in self.SKIP_VALUES.get(key, self.SKIP_VALUES[DEFAULT])
        }

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Evidence:
        return Evidence(**data)


class BoundingBoxSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    xmin = f.Float(allow_none=True, required=False)
    ymin = f.Float(allow_none=True, required=False)
    xmax = f.Float(allow_none=True, required=False)
    ymax = f.Float(allow_none=True, required=False)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> BoundingBox:
        return BoundingBox(**data)


class PDFEvidenceSchema(EvidenceSchema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    observed_value_bbox = f.Nested(BoundingBoxSchema, allow_none=True, required=False)
    observed_name_bbox = f.Nested(BoundingBoxSchema, allow_none=True, required=False)
    document_snippet = f.String(allow_none=True, required=False)

    @pre_dump
    def drop_empty_bounding_box(self, obj, **kwargs):
        obj.observed_value_bbox = (
            obj.observed_value_bbox if obj.observed_value_bbox and obj.observed_value_bbox.is_valid else None
        )
        obj.observed_name_bbox = (
            obj.observed_name_bbox if obj.observed_name_bbox and obj.observed_name_bbox.is_valid else None
        )
        return obj

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Evidence:
        return PDFEvidence(**data)


class CustomizableClassifierEvidenceSchema(EvidenceSchema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    snippet_bbox = f.Nested(BoundingBoxSchema, allow_none=True, required=False)
    document_snippet = f.String(allow_none=True, required=False)
    classifier_id = f.UUID(allow_none=True, required=False)
    label = f.String(allow_none=True, required=False)

    @pre_dump
    def drop_empty_bounding_box(self, obj, **kwargs):
        obj.snippet_bbox = obj.snippet_bbox if obj.snippet_bbox and obj.snippet_bbox.is_valid else None
        return obj

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Evidence:
        return CustomizableClassifierEvidence(**data)


class SpreadsheetEvidenceSchema(EvidenceSchema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    row_number = f.Integer(allow_none=True, required=False)
    column_number = f.Integer(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Evidence:
        return SpreadsheetEvidence(**data)


class PolymorphicEvidenceSchema(OneOfSchema):
    type_schemas: dict[ClassificationDocumentType, TransientDataSchema] = {
        SpreadsheetEvidence.__name__: SpreadsheetEvidenceSchema,
        PDFEvidence.__name__: PDFEvidenceSchema,
        Evidence.__name__: EvidenceSchema,
        CustomizableClassifierEvidence.__name__: CustomizableClassifierEvidenceSchema,
    }

    def get_data_type(self, data):
        """Overrides the default method, so that we are backwards compatible. If there is no type_field,
        we fallback to the default Evidence schema"""
        data_type = data.get(self.type_field)
        if data_type is None:
            return Evidence.__name__
        if self.type_field in data and self.type_field_remove:
            data.pop(self.type_field)
        return data_type


class ShareholderInformationSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    shareholder_name = f.String(allow_none=True, required=False)
    shareholder_type = EnumField(ShareholderType, required=False, allow_none=True, by_value=True)
    ownership_percentage = f.Float(allow_none=True, required=False)
    is_director_or_board_member = f.Boolean(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> ShareholderInformation:
        return ShareholderInformation(**data)


class SuggestedFieldSchema(Schema):
    skip_values = [None, False, [], {}, 0]

    class Meta(Schema.Meta):
        unknown = EXCLUDE

    name = f.String(allow_none=False, required=True)
    fact_subtype_id = EnumField(FactSubtypeID, required=False, allow_none=True, by_value=True)
    evidences = f.Nested(PolymorphicEvidenceSchema, allow_none=True, required=False, many=True)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value not in self.skip_values}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> SuggestedField:
        return SuggestedField(**data)


class ExtractedEntityInfoSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    entity_type = EnumField(SubmissionEntityType, allow_none=False, required=True, by_value=True)
    count = f.Integer(allow_none=False, required=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> ExtractedEntityInfo:
        return ExtractedEntityInfo(**data)


class FileEntitiesExtractionInfoSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    file_idx = f.Integer(allow_none=False, required=True)
    number_of_rows = f.Integer(allow_none=False, required=True, dump_only=True)
    extracted_entities = f.Nested(ExtractedEntityInfoSchema, allow_none=False, required=False, many=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> FileEntitiesExtractionInfo:
        return FileEntitiesExtractionInfo(**data)


class AdditionalDataSchema(Schema):
    SKIP_VALUES = [[]]

    class Meta(Schema.Meta):
        unknown = EXCLUDE

    suggested_fields = f.Nested(SuggestedFieldSchema, allow_none=False, required=False, many=True)
    finished_do_sub_step = f.String(allow_none=True, required=False)
    extracted_entities_info = f.Nested(FileEntitiesExtractionInfoSchema, allow_none=False, required=False, many=True)
    fe_properties = f.Dict(allow_none=True, required=False)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        if data.get("extracted_entities_info"):
            data["extracted_entities_info"] = [
                item for item in data["extracted_entities_info"] if item.get("extracted_entities") != []
            ]
        return {key: value for key, value in data.items() if value not in self.SKIP_VALUES}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> AdditionalData:
        return AdditionalData(**data)


class LeanValueValidationResultSchema(Schema):
    SKIP_VALUES = [None, ""]

    class Meta(Schema.Meta):
        unknown = EXCLUDE

    is_valid = f.Boolean(allow_none=False, required=True)
    error_message = f.String(allow_none=True, required=False)
    warning_message = f.String(allow_none=True, required=False)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value not in self.SKIP_VALUES}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> ValueValidationResult:
        return ValueValidationResult(**data)


class ValueValidationResultSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    is_valid = f.Boolean(allow_none=False, required=True)
    error_message = f.String(allow_none=True, required=False)
    warning_message = f.String(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> ValueValidationResult:
        return ValueValidationResult(**data)


class ValueSuggestionResultSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    value = f.String(allow_none=True, required=False)
    confidence = f.Float(allow_none=True, required=False)
    auto_apply = f.Boolean(allow_none=True, required=False)
    field_type = EnumField(FieldType, allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> ValueSuggestionResult:
        return ValueSuggestionResult(**data)


class ResolvedDataValueSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    value = f.Raw(allow_none=True, required=True)
    manually_added = f.Boolean(required=False, allow_none=False, default=False, missing=False)
    explanations = f.List(f.String, required=False)
    observed_name = f.String(allow_none=True, required=False)
    observed_value = f.String(allow_none=True, required=False)
    suggested_values = f.Nested(ValueSuggestionResultSchema, allow_none=True, required=False, many=True)
    entity_idx = f.Int(allow_none=True, required=False)
    file_idx = f.Int(allow_none=True, required=False)
    external_file_id = f.UUID(allow_none=True, required=False)
    evidences = f.Nested(PolymorphicEvidenceSchema, allow_none=True, required=False, many=True)
    validation_result = f.Nested(ValueValidationResultSchema, allow_none=True, required=False)
    related_entity_idx = f.Int(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> ResolvedDataValue:
        return ResolvedDataValue(**data)


class FactSubtypeSuggestionSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    fact_subtype_id = EnumField(FactSubtypeID, required=False, allow_none=True, by_value=True)
    field_type = EnumField(FieldType, required=False, allow_none=True, by_value=True)
    name_score = f.Float(required=False, allow_none=True)
    values_score = f.Float(required=False, allow_none=True)
    confidence = f.Float(required=False, allow_none=True)
    explanations = f.List(f.String, required=False, allow_none=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> FactSubtypeSuggestion:
        return FactSubtypeSuggestion(**data)


class ResolvedDataFieldSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    name = f.String(allow_none=False, required=True)
    values = f.Nested(ResolvedDataValueSchema, allow_none=False, required=False, many=True)
    value_type = EnumField(FieldType, required=True, allow_none=False, by_value=True)
    display_as_fact = f.Boolean(required=False, allow_none=False, default=False, missing=False)
    fact_subtype_id = EnumField(FactSubtypeID, required=False, allow_none=True, by_value=True)
    fact_subtype_suggestions = f.Nested(FactSubtypeSuggestionSchema, allow_none=True, required=False, many=True)
    naics_code = EnumField(NaicsCode, required=False, allow_none=True, by_value=True)
    unit = EnumField(Units, required=False, allow_none=True, by_value=True)
    group_ids = f.List(f.String, required=False, allow_none=True)
    aggregation_type = EnumField(AggregationType, required=False, allow_none=True, by_value=True)
    position = f.Int(allow_none=True, required=False)
    observed_name = f.String(allow_none=True, required=False)
    matching_metadata = f.Nested(MatchingMetadataSchema, allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> ResolvedDataField:
        return ResolvedDataField(**data)

    @validates_schema
    def validate_naics_and_fact_subtype(self, data, **kwargs):
        if data.get("fact_subtype_id", None) and data.get("naics_code", None):
            raise ValidationError("Only one of ['fact_subtype_id', 'naics_code'] can have value")


class SubmissionEntitySchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    type = EnumField(SubmissionEntityType, required=False, allow_none=True, by_value=True)
    entity_named_insured = EnumField(
        SubmissionBusinessEntityNamedInsured, required=False, allow_none=True, by_value=True
    )
    entity_role = EnumField(SubmissionBusinessEntityRole, required=False, allow_none=True, by_value=True)
    id = f.String(required=False, allow_none=True)
    remote_id = f.String(required=False, allow_none=True)
    resolved = f.Boolean(required=False, allow_none=False, default=False, missing=False)
    parent_idx = f.Int(allow_none=True, required=False)
    acord_location_information = f.Nested(AcordLocationInformationSchema, allow_none=True, required=False)
    resolution_id = f.UUID(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> SubmissionEntity:
        return SubmissionEntity(**data)


class PolymorphicTransientDataSchema(OneOfSchema):
    type_field = "file_classification"
    type_field_remove = False
    type_schemas: dict[ClassificationDocumentType, TransientDataSchema] = {
        ClassificationDocumentType.ACORD_FORM: TransientDataSchema,
        ClassificationDocumentType.ACORD_140: Acord140TransientDataSchema,
        ClassificationDocumentType.ACORD_126: Acord126TransientDataSchema,
        ClassificationDocumentType.ACORD_211: Acord126TransientDataSchema,
        ClassificationDocumentType.ACORD_131: Acord131TransientDataSchema,
        ClassificationDocumentType.ACORD_139: Acord139TransientDataSchema,
        ClassificationDocumentType.APPLIED_126: Acord126TransientDataSchema,
        ClassificationDocumentType.OFSCHHAZ: Acord126TransientDataSchema,
        ClassificationDocumentType.ACORD_160: Acord160TransientDataSchema,
    }

    def get_obj_type(self, obj):
        return getattr(obj, "file_classification", None)


class OnboardedFileSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    fields = f.Nested(ResolvedDataFieldSchema, allow_none=False, many=True)
    entity_information = f.Nested(ResolvedDataFieldSchema, allow_none=False, many=True)
    entities = f.Nested(SubmissionEntitySchema, allow_none=False, many=True)
    files = f.List(f.UUID, allow_none=False)
    transient_data = f.Nested(PolymorphicTransientDataSchema, allow_none=True)
    additional_data = f.Nested(AdditionalDataSchema, allow_none=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> OnboardedFile:
        return OnboardedFile(**data)


class LeanResolvedDataValueSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    value = f.Raw(allow_none=True, required=True)
    manually_added = f.Boolean(required=False, allow_none=False, default=False, missing=False)
    explanations = f.List(f.String, required=False)
    observed_name = f.String(allow_none=True, required=False)
    observed_value = f.String(allow_none=True, required=False)
    suggested_values = f.Nested(ValueSuggestionResultSchema, allow_none=True, required=False, many=True)
    entity_idx = f.Int(allow_none=True, required=False)
    file_idx = f.Int(allow_none=True, required=False)
    external_file_id = f.UUID(allow_none=True, required=False)
    evidences = f.Nested(PolymorphicEvidenceSchema, allow_none=True, required=False, many=True)
    validation_result = f.Nested(LeanValueValidationResultSchema, allow_none=True, required=False)
    related_entity_idx = f.Int(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> ResolvedDataValue:
        data["file_idx"] = data.get("file_idx", 0)
        return ResolvedDataValue(**data)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {
            key: value
            for key, value in data.items()
            if value
            or key in ["value", "entity_idx"]
            or (key in ["related_entity_idx"] and value is not None)  # allow for related_entity_idx = 0
        }


class LeanResolvedDataFieldSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    name = f.String(allow_none=False, required=True)
    values = f.Nested(LeanResolvedDataValueSchema, allow_none=False, required=False, many=True)
    value_type = EnumField(FieldType, required=True, allow_none=False, by_value=True)
    display_as_fact = f.Boolean(required=False, allow_none=False, default=False, missing=False)
    fact_subtype_id = EnumField(FactSubtypeID, required=False, allow_none=True, by_value=True)
    fact_subtype_suggestions = f.Nested(FactSubtypeSuggestionSchema, allow_none=True, required=False, many=True)
    naics_code = EnumField(NaicsCode, required=False, allow_none=True, by_value=True)
    unit = EnumField(Units, required=False, allow_none=True, by_value=True)
    group_ids = f.List(f.String, required=False, allow_none=True)
    aggregation_type = EnumField(AggregationType, required=False, allow_none=True, by_value=True)
    position = f.Int(allow_none=True, required=False)
    observed_name = f.String(allow_none=True, required=False)
    matching_metadata = f.Nested(MatchingMetadataSchema, allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> ResolvedDataField:
        return ResolvedDataField(**data)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}

    @validates_schema
    def validate_naics_and_fact_subtype(self, data, **kwargs):
        if data.get("fact_subtype_id", None) and data.get("naics_code", None):
            raise ValidationError("Only one of ['fact_subtype_id', 'naics_code'] can have value")


class LeanSubmissionEntitySchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    type = EnumField(SubmissionEntityType, required=False, allow_none=True, by_value=True)
    entity_named_insured = EnumField(
        SubmissionBusinessEntityNamedInsured, required=False, allow_none=True, by_value=True
    )
    entity_role = EnumField(SubmissionBusinessEntityRole, required=False, allow_none=True, by_value=True)
    id = f.String(required=False, allow_none=True)
    remote_id = f.String(required=False, allow_none=True)
    resolved = f.Boolean(required=False, allow_none=False, default=False, missing=False)
    parent_idx = f.Int(allow_none=True, required=False)
    acord_location_information = f.Nested(AcordLocationInformationSchema, allow_none=True, required=False)
    resolution_id = f.UUID(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> SubmissionEntity:
        return SubmissionEntity(**data)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}


class LeanTransientDataSchema(Schema):
    file_classification = EnumField(ClassificationDocumentType, by_value=True, required=True, allow_none=False)
    named_insured = f.String(allow_none=True)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> TransientData:
        return TransientData(**data)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}


class LeanPremisesInformationTableRowSchema(Schema):
    subject_of_insurance = f.String(allow_none=True)
    amount = f.Float(allow_none=True)
    coins_percentage = f.Float(allow_none=True)
    valuation = f.String(allow_none=True)
    causes_of_loss = f.String(allow_none=True)
    inflation_guard_percentage = f.Float(allow_none=True)
    deductible = f.Float(allow_none=True)
    deductible_type = f.String(allow_none=True)
    blanket_number = f.Float(allow_none=True)
    forms_and_conditions_to_apply = f.String(allow_none=True)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> PremisesInformationTableRow:
        return PremisesInformationTableRow(**data)


class LeanPremisesInformationTableWithEntitySchema(Schema):
    requested_name = f.String(allow_none=True)
    requested_address = f.String(allow_none=True)
    location_number = f.Integer(allow_none=True)
    building_number = f.Integer(allow_none=True)
    premises_information_table = f.Nested(
        LeanPremisesInformationTableRowSchema, allow_none=False, missing=[], many=True
    )

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> PremisesInformationTableWithEntity:
        return PremisesInformationTableWithEntity(**data)


class LeanHazardInformationWithEntitySchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    requested_name = f.String(allow_none=True)
    requested_address = f.String(allow_none=True)
    location_number = f.Integer(allow_none=True)
    building_number = f.Integer(allow_none=True)
    hazard_number = f.Integer(allow_none=True)
    class_code = f.String(allow_none=True)
    premium_basis = f.String(allow_none=True)
    exposure = f.Integer(allow_none=True)
    territorial_rating = f.String(allow_none=True)
    rate_prem_ops = f.Float(allow_none=True)
    rate_products = f.Float(allow_none=True)
    premium_prem_ops = f.Float(allow_none=True)
    premium_products = f.Float(allow_none=True)
    classification = f.String(allow_none=True)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> HazardInformationWithEntity:
        return HazardInformationWithEntity(**data)


class LeanPolicyInformationSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    new = f.Boolean(allow_none=True)
    renew = f.Boolean(allow_none=True)
    umbrella = f.Boolean(allow_none=True)
    excess = f.Boolean(allow_none=True)
    occurrence = f.Boolean(allow_none=True)
    claims_made = f.Boolean(allow_none=True)
    voluntary = f.Boolean(allow_none=True)
    limits_occurrence = f.Float(allow_none=True)
    limits_general_aggregate = f.Float(allow_none=True)
    proposed_retroactive_date = f.String(allow_none=True)
    current_retroactive_date = f.String(allow_none=True)
    retained_limit = f.Float(allow_none=True)
    first_dollar_defense = f.Boolean(allow_none=True)

    @pre_load
    def normalize_date(self, data, **kwargs):
        if (
            "proposed_retroactive_date" in data
            and data["proposed_retroactive_date"]
            and hasattr(data["proposed_retroactive_date"], "strftime")
        ):
            data["proposed_retroactive_date"] = data["proposed_retroactive_date"].strftime("%Y-%m-%d")
        if (
            "current_retroactive_date" in data
            and data["current_retroactive_date"]
            and hasattr(data["current_retroactive_date"], "strftime")
        ):
            data["current_retroactive_date"] = data["current_retroactive_date"].strftime("%Y-%m-%d")
        return data

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> PolicyInformation:
        return PolicyInformation(**data)


class LeanAcord140TransientDataSchema(LeanTransientDataSchema):
    premises_info = f.Nested(LeanPremisesInformationTableWithEntitySchema, allow_none=True, many=True)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Acord140TransientData:
        return Acord140TransientData(**data)


class LeanAcord160TransientDataSchema(LeanTransientDataSchema):
    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Acord160TransientData:
        return Acord160TransientData(**data)


class LeanAcord126TransientDataSchema(LeanTransientDataSchema):
    schedule_of_hazards = f.Nested(LeanHazardInformationWithEntitySchema, allow_none=True, many=True)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Acord126TransientData:
        return Acord126TransientData(**data)


class LeanAcord131TransientDataSchema(TransientDataSchema):
    policy_information = f.Nested(LeanPolicyInformationSchema, allow_none=True, many=True)

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value is not None and value is not False}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> Acord131TransientData:
        return Acord131TransientData(**data)


class LeanPolymorphicTransientDataSchema(OneOfSchema):
    type_field = "file_classification"
    type_field_remove = False
    type_schemas: dict[ClassificationDocumentType, LeanTransientDataSchema] = {
        ClassificationDocumentType.ACORD_FORM: LeanTransientDataSchema,
        ClassificationDocumentType.ACORD_139: LeanAcord140TransientDataSchema,
        ClassificationDocumentType.ACORD_140: LeanAcord140TransientDataSchema,
        ClassificationDocumentType.ACORD_126: LeanAcord126TransientDataSchema,
        ClassificationDocumentType.ACORD_211: LeanAcord126TransientDataSchema,
        ClassificationDocumentType.ACORD_131: LeanAcord131TransientDataSchema,
        ClassificationDocumentType.APPLIED_126: LeanAcord126TransientDataSchema,
        ClassificationDocumentType.OFSCHHAZ: LeanAcord126TransientDataSchema,
        ClassificationDocumentType.ACORD_160: LeanAcord160TransientDataSchema,
    }

    def get_obj_type(self, obj):
        return getattr(obj, "file_classification", None)


class LeanOnboardedFileSchema(Schema):
    SKIP_VALUES = [None, False, {}]

    class Meta(Schema.Meta):
        unknown = EXCLUDE

    fields = f.Nested(LeanResolvedDataFieldSchema, allow_none=False, many=True)
    entity_information = f.Nested(LeanResolvedDataFieldSchema, allow_none=False, many=True)
    entities = f.Nested(LeanSubmissionEntitySchema, allow_none=False, many=True)
    files = f.List(f.UUID, allow_none=False)
    transient_data = f.Nested(LeanPolymorphicTransientDataSchema, allow_none=True)
    additional_data = f.Nested(AdditionalDataSchema, allow_none=True, load_default=lambda: AdditionalData())

    @post_dump
    def remove_default_values(self, data, **kwargs):
        return {key: value for key, value in data.items() if value not in self.SKIP_VALUES}

    @post_load
    def make_obj(self, data, *args, **kwargs) -> OnboardedFile:
        return OnboardedFile(**data)
