from marshmallow import EXCLU<PERSON>, Schema
from marshmallow import fields as f
from marshmallow import post_dump, post_load
from marshmallow_enum import Enum<PERSON>ield

from static_common.enums.supplemental_category import SupplementalCategory
from static_common.models.file_additional_info import (
    AcordClassificationInfo,
    FileAdditionalInfo,
    SupplementalClassificationInfo,
)


class AcordClassificationInfoSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    version_id = f.String(allow_none=False, required=True)
    form_name = f.String(allow_none=False, required=True)
    form_delimiter = f.String(allow_none=True, required=False)
    form_year = f.Int(allow_none=True, required=False)
    form_version = f.String(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> AcordClassificationInfo:
        return AcordClassificationInfo(**data)


class SupplementalClassificationInfoSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    template_name = f.String(allow_none=True, required=False)
    category = EnumField(SupplementalCategory, allow_none=True, required=False)
    category_description = f.String(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> SupplementalClassificationInfo:
        return SupplementalClassificationInfo(**data)


class FileAdditionalInfoSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    acord = f.Nested(AcordClassificationInfoSchema, allow_none=True, required=False)
    supplemental = f.Nested(SupplementalClassificationInfoSchema, allow_none=True, required=False)
    email_id = f.String(allow_none=True, required=False)
    is_pre_renewal = f.Boolean(allow_none=True, required=False)
    policy_carrier = f.String(allow_none=True, required=False)
    number_of_pages = f.Int(allow_none=True, required=False)
    number_of_sheets = f.Int(allow_none=True, required=False)
    number_of_tables = f.Int(allow_none=True, required=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> FileAdditionalInfo:
        return FileAdditionalInfo(**data)

    @post_dump
    def remove_none_values(self, data, *args, **kwargs):
        return {k: v for k, v in data.items() if v is not None}
