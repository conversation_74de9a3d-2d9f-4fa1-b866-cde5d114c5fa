from marshmallow import EXCLUDE, Schema
from marshmallow import fields as f
from marshmallow import post_load
from marshmallow_enum import Enum<PERSON>ield

from static_common.models.matching_metadata import (
    MatcherType,
    MatchingMetadata,
    ParserType,
)


class MatchingMetadataSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    matcher_type = EnumField(MatcherType, by_value=True, required=False, allow_none=True)
    parser_type = EnumField(ParserType, by_value=True, required=False, allow_none=True)
    matching_explanation = f.Dict(required=False, allow_none=True)
    dropped_explanation = f.Dict(required=False, allow_none=True)
    is_dropped = f.<PERSON>(required=False, default=False)

    @post_load
    def make_obj(self, data, *args, **kwargs) -> MatchingMetadata:
        return MatchingMetadata(**data)
