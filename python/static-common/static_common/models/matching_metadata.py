from dataclasses import dataclass

from static_common.enums.enum import StrEnum


class MatcherType(StrEnum):
    LLM = "LLM"
    REGEX = "REGEX"


class ParserType(StrEnum):
    SUPPLEMENTAL_TEMPLATE = "SUPPLEMENTAL_TEMPLATE"
    SUPPLEMENTAL_GENERIC = "SUPPLEMENTAL_GENERIC"
    SUPPLEMENTAL_TABLES = "SUPPLEMENTAL_TABLES"
    FINANCIAL_TABLES = "FINANCIAL_TABLES"


@dataclass
class MatchingMetadata:
    """
    :param matching_explanation: Explanation of the matching process.
        In case of LLM matcher can contain the closest vector that was the proposition.
        If not matched can provide the reason, e.g. conflict with some other field.
    """

    matcher_type: MatcherType | None = None
    parser_type: ParserType | None = None
    matching_explanation: dict | None = None
    dropped_explanation: dict | None = None
    is_dropped: bool = False
