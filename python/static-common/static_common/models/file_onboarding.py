from __future__ import annotations

import dataclasses
import itertools
import uuid
from collections import defaultdict
from collections.abc import Collection, Iterator, Sequence
from dataclasses import dataclass, field
from typing import Any
from uuid import UUID

from infrastructure_common.logging import get_logger

from static_common.enums.aggregation import AggregationType
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityFieldID
from static_common.enums.external import ExternalIdentifierType
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.parent import ParentType
from static_common.enums.shareholder_type import ShareholderType
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.enums.units import Units
from static_common.models.business_resolution_data import ExternalIdentifier
from static_common.models.matching_metadata import MatchingMetadata
from static_common.taxonomies.industry_classification import NaicsCode

logger = get_logger()


@dataclass
class AcordLocationInformation:
    location_number: int | None = None
    building_number: int | None = None


@dataclass
class PremisesInformationTableRow:
    subject_of_insurance: str | None = None
    amount: float | None = None
    coins_percentage: float | None = None
    valuation: str | None = None
    causes_of_loss: str | None = None
    inflation_guard_percentage: float | None = None
    deductible: float | None = None
    deductible_type: str | None = None
    blanket_number: float | None = None
    forms_and_conditions_to_apply: str | None = None


@dataclass
class PremisesInformationTableWithEntity:
    requested_name: str | None = None
    requested_address: str | None = None
    location_number: int | None = None
    building_number: int | None = None
    premises_information_table: list[PremisesInformationTableRow] = field(default_factory=list)


@dataclass
class HazardInformationWithEntity:
    requested_name: str | None = None
    requested_address: str | None = None
    location_number: int | None = None
    building_number: int | None = None
    hazard_number: int | None = None
    class_code: str | None = None
    premium_basis: str | None = None
    exposure: int | None = None
    territorial_rating: str | None = None
    rate_prem_ops: float | None = None
    rate_products: float | None = None
    premium_prem_ops: float | None = None
    premium_products: float | None = None
    classification: str | None = None


@dataclass
class PolicyInformation:
    new: bool | None = None
    renewal: bool | None = None
    umbrella: bool | None = None
    excess: bool | None = None
    occurrence: bool | None = None
    claims_made: bool | None = None
    voluntary: bool | None = None
    limits_occurrence: float | None = None
    limits_general_aggregate: float | None = None
    proposed_retroactive_date: str | None = None
    current_retroactive_date: str | None = None
    retained_limit: float | None = None
    first_dollar_defense: bool | None = None


@dataclass
class Evidence:
    file_idx: int | None = None
    file_id: UUID | None = None
    observed_name: str | None = None
    observed_value: str | None = None
    page: int | None = None
    confidence: float | None = None

    @property
    def no_evidence(self) -> bool:
        return self.page is None and self.confidence is None


@dataclass
class BoundingBox:
    xmin: float
    ymin: float
    xmax: float
    ymax: float

    def __eq__(self, other):
        if not isinstance(other, BoundingBox):
            return False
        return (
            self.xmin == other.xmin and self.ymin == other.ymin and self.xmax == other.xmax and self.ymax == other.ymax
        )

    def __hash__(self):
        return hash((self.xmin, self.ymin, self.xmax, self.ymax))

    @property
    def width(self) -> float:
        return self.xmax - self.xmin

    @property
    def height(self) -> float:
        return self.ymax - self.ymin

    @property
    def center(self) -> tuple[float, float]:
        return (self.xmin + self.xmax) / 2, (self.ymin + self.ymax) / 2

    @property
    def is_valid(self) -> bool:
        if not all(v is not None for v in [self.xmin, self.ymin, self.xmax, self.ymax]):
            return False
        return self.width > 0 and self.height > 0


@dataclass
class PDFEvidence(Evidence):
    observed_name_bbox: BoundingBox | None = None
    observed_value_bbox: BoundingBox | None = None
    document_snippet: str | None = None


@dataclass
class CustomizableClassifierEvidence(Evidence):
    snippet_bbox: BoundingBox | None = None
    document_snippet: str | None = None
    classifier_id: UUID | None = None
    label: str | None = None


@dataclass
class SpreadsheetEvidence(Evidence):
    row_number: int | None = None
    column_number: int | None = None

    @property
    def is_valid(self) -> bool:
        return self.row_number is not None and self.column_number is not None


@dataclass
class ShareholderInformation:
    shareholder_name: str | None = None
    shareholder_type: ShareholderType | None = None
    ownership_percentage: float | None = None
    is_director_or_board_member: bool | None = None


@dataclass
class ValueValidationResult:
    is_valid: bool
    error_message: str | None = None
    warning_message: str | None = None


@dataclass(frozen=True)
class ValueSuggestionResult:
    value: str | None
    confidence: float | None
    auto_apply: bool | None
    field_type: FieldType | None = None

    def assign_field_type(self, field_type: FieldType) -> ValueSuggestionResult:
        return dataclasses.replace(self, field_type=field_type)


@dataclass
class ResolvedDataValue:
    value: Any
    manually_added: bool = False
    observed_name: str | None = None
    observed_value: str | None = None
    suggested_values: list[ValueSuggestionResult] = field(default_factory=list)
    explanations: list[str] = field(default_factory=list)
    entity_idx: int | None = None
    file_idx: int | None = None
    external_file_id: UUID | None = None
    evidences: list[Evidence] = field(default_factory=list)
    validation_result: ValueValidationResult | None = None
    related_entity_idx: int | None = None  # if saving A -> B relation, B entity_idx should be saved here


@dataclass
class FactSubtypeSuggestion:
    """
    :param name_score: Score of the field name matching.
    :param values_score: Score of the field value matching.
    :param confidence: Final confidence of the suggestion.
    """

    fact_subtype_id: FactSubtypeID
    field_type: FieldType
    name_score: float
    values_score: float
    confidence: float
    explanations: list[str] = field(default_factory=list)


@dataclass
class ResolvedDataField:
    """
    :param fact_subtype_id: Best matching fact subtype id from fact_subtype_suggestions.
    :param fact_subtype_suggestions: List of all fact subtype ids that were suggested for this field.
    """

    name: str
    values: list[ResolvedDataValue]
    value_type: FieldType
    display_as_fact: bool = False
    fact_subtype_id: FactSubtypeID | None = None
    fact_subtype_suggestions: list[FactSubtypeSuggestion] = field(default_factory=list)
    naics_code: NaicsCode | None = None
    unit: Units | None = None
    group_ids: list[str] | None = None
    aggregation_type: AggregationType | None = None
    position: int | None = None
    observed_name: str | None = None
    matching_metadata: MatchingMetadata | None = None


@dataclass
class SubmissionEntity:
    PARENT_TYPE_MAP = {  # noqa: RUF008
        SubmissionEntityType.GENERAL_CONTRACTOR: ParentType.BUSINESS,
        SubmissionEntityType.PROJECT: ParentType.BUSINESS,
        SubmissionEntityType.OTHER_INSURED: ParentType.BUSINESS,
        SubmissionEntityType.PRIMARY_INSURED: ParentType.BUSINESS,
        SubmissionEntityType.BUSINESS: ParentType.BUSINESS,
        SubmissionEntityType.DRIVER: ParentType.DRIVER,
        SubmissionEntityType.VEHICLE: ParentType.VEHICLE,
        SubmissionEntityType.STRUCTURE: ParentType.STRUCTURE,
        SubmissionEntityType.PRODUCT: ParentType.PRODUCT,
        SubmissionEntityType.GARAGE: ParentType.GARAGE,
        SubmissionEntityType.EMPLOYEE: ParentType.PERSON,
        SubmissionEntityType.BENEFITS_PLAN: ParentType.ERISA_PLAN,
        SubmissionEntityType.EQUIPMENT: ParentType.EQUIPMENT,
        SubmissionEntityType.TRANSACTION: ParentType.TRANSACTION,
    }

    RESOLVABLE_ENTITY_TYPES = {  # noqa: RUF008
        SubmissionEntityType.BUSINESS,
        SubmissionEntityType.PRIMARY_INSURED,
        SubmissionEntityType.OTHER_INSURED,
        SubmissionEntityType.GENERAL_CONTRACTOR,
        SubmissionEntityType.PROJECT,
        SubmissionEntityType.GARAGE,
    }

    type: SubmissionEntityType | None = None
    entity_named_insured: SubmissionBusinessEntityNamedInsured | None = None
    entity_role: SubmissionBusinessEntityRole | None = None
    id: str | None = None
    remote_id: str | None = None
    resolved: bool | None = False
    parent_idx: int | None = None
    parent_id: str | None = None
    acord_location_information: AcordLocationInformation | None = None
    resolution_id: UUID | None = None

    @property
    def unique_identifier(self) -> str:
        # The id is randomly generated for structures. The real unique identifier is id of parent and the remote id.
        if self.type == SubmissionEntityType.STRUCTURE and self.parent_id and self.remote_id:
            return f"{self.parent_id}-{self.remote_id}"
        return self.id

    @property
    def is_fni(self) -> bool:
        return self.is_business and (
            self.type == SubmissionEntityType.PRIMARY_INSURED
            or self.entity_named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
        )

    @property
    def is_oni(self) -> bool:
        return self.is_business and (
            self.type == SubmissionEntityType.OTHER_INSURED
            or self.entity_named_insured == SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED
        )

    @property
    def parent_type(self) -> ParentType | None:
        return self.PARENT_TYPE_MAP.get(self.type)

    @property
    def is_business(self) -> bool:
        return self.parent_type == ParentType.BUSINESS

    @property
    def is_gc(self) -> bool:
        return self.is_business and (
            self.type == SubmissionEntityType.GENERAL_CONTRACTOR
            or self.entity_role == SubmissionBusinessEntityRole.GENERAL_CONTRACTOR
        )

    @property
    def is_project(self) -> bool:
        return self.is_business and (
            self.type == SubmissionEntityType.PROJECT or self.entity_role == SubmissionBusinessEntityRole.PROJECT
        )

    @property
    def acord_location_number(self) -> int | None:
        return self.acord_location_information.location_number if self.acord_location_information else None

    @property
    def acord_building_number(self) -> int | None:
        return self.acord_location_information.building_number if self.acord_location_information else None

    @property
    def is_resolvable_entity(self) -> bool:
        return self.type in self.RESOLVABLE_ENTITY_TYPES

    @staticmethod
    def calculate_entity_id(name: str | None, address: str | None) -> str:
        name = (name or "").lower().strip()
        address = (address or "").lower().strip()
        return f"{name} {address}".strip()


@dataclass
class SuggestedField:
    name: str
    fact_subtype_id: FactSubtypeID | None = None
    evidences: list[Evidence] = field(default_factory=list)


@dataclass
class ExtractedEntityInfo:
    entity_type: SubmissionEntityType
    count: int


@dataclass
class FileEntitiesExtractionInfo:
    file_idx: int
    extracted_entities: list[ExtractedEntityInfo] = field(default_factory=list)

    @property
    def number_of_rows(self) -> int:
        if any(entity.entity_type == SubmissionEntityType.STRUCTURE for entity in self.extracted_entities):
            return sum(
                entity.count
                for entity in self.extracted_entities
                if entity.entity_type != SubmissionEntityType.BUSINESS
            )
        return sum(entity.count for entity in self.extracted_entities)


@dataclass
class AdditionalData:
    suggested_fields: list[SuggestedField] = field(default_factory=list)
    finished_do_sub_step: str | None = None
    extracted_entities_info: list[FileEntitiesExtractionInfo] = field(default_factory=list)
    fe_properties: dict | None = None


@dataclass
class TransientData:
    file_classification: ClassificationDocumentType
    named_insured: str | None = None


@dataclass
class Acord140TransientData(TransientData):
    premises_info: list[PremisesInformationTableWithEntity] = field(default_factory=list)


@dataclass
class Acord126TransientData(TransientData):
    schedule_of_hazards: list[HazardInformationWithEntity] = field(default_factory=list)


@dataclass
class Acord131TransientData(TransientData):
    policy_information: list[PolicyInformation] = field(default_factory=list)


@dataclass
class Acord160TransientData(TransientData):
    pass


@dataclass
class Acord139TransientData(Acord140TransientData):
    pass


@dataclass
class OnboardedFile:
    fields: list[ResolvedDataField] = field(default_factory=list)
    entity_information: list[ResolvedDataField] = field(default_factory=list)
    entities: list[SubmissionEntity] = field(default_factory=list)
    files: list[UUID] = field(default_factory=list)
    transient_data: type[TransientData] | None = None
    additional_data: AdditionalData | None = field(default_factory=AdditionalData)

    @property
    def is_empty(self) -> bool:
        """
        Returns True if the file has no fields and no entity information.
        Files field should always be populated and entities field is meaningless if there is no entity information."""
        return not self.fields and not self.entity_information

    @property
    def no_entities(self) -> bool:
        return not self.entities

    @property
    def only_submission_info(self) -> bool:
        return len(self.entities) == 1 and self.entities[0].type == SubmissionEntityType.SUBMISSION

    @property
    def all_entities_have_name_and_address(self) -> bool:
        entity_data = defaultdict(lambda: {EntityFieldID.NAME.value: False, EntityFieldID.ADDRESS.value: False})
        for field_ in self.entity_information:
            if field_.name in [EntityFieldID.NAME.value, EntityFieldID.ADDRESS.value]:
                for value in field_.values:
                    if value.entity_idx in self.business_entity_idxs and value.value:
                        entity_data[value.entity_idx][field_.name] = True
        return all(v[EntityFieldID.NAME.value] and v[EntityFieldID.ADDRESS.value] for v in entity_data.values())

    @property
    def has_fni(self) -> bool:
        return any(e.is_fni for e in self.entities)

    @property
    def submission_entity_idx(self) -> int | None:
        submission_entity_idx = next(
            (idx for idx, entity in enumerate(self.entities) if entity.type == SubmissionEntityType.SUBMISSION),
            None,
        )
        return submission_entity_idx

    @property
    def business_entity_idxs(self) -> list[int]:
        return self.get_entity_idxs_by_type(SubmissionEntityType.business_entities())

    @property
    def fni_idx(self) -> int | None:
        return next((idx for idx, e in enumerate(self.entities) if e.is_fni), None)

    @property
    def all_fields(self) -> Iterator[ResolvedDataField]:
        return itertools.chain(self.fields, self.entity_information)

    def get_entity_idxs_by_type(self, entity_types: Collection[SubmissionEntityType]) -> list[int]:
        return [idx for idx, e in enumerate(self.entities) if e.type in entity_types]

    def get_entity_idxs_by_role(
        self, entity_types: Collection[SubmissionEntityType], entity_roles: Collection[SubmissionBusinessEntityRole]
    ) -> list[int]:
        return [idx for idx, e in enumerate(self.entities) if e.type in entity_types and e.entity_role in entity_roles]

    def get_or_create_submission_entity_idx(self, submission_id: UUID | str | None = None) -> int:
        submission_entity_idx = self.submission_entity_idx

        if submission_entity_idx is None:
            submission_entity_idx = len(self.entities)
            if submission_id:
                entity = SubmissionEntity(type=SubmissionEntityType.SUBMISSION, id=str(submission_id), resolved=True)
            else:
                entity = SubmissionEntity(type=SubmissionEntityType.SUBMISSION, id=str(uuid.uuid4()))
            self.entities.append(entity)

        return submission_entity_idx

    def get_entity_info_value_for_entity(self, field_names: list[str], entity_idxs: list[int]) -> str | None:
        fields = [ei for ei in self.entity_information if ei.name in field_names]
        for f in fields:
            return next((v.value for v in f.values if v.entity_idx in entity_idxs), None)

    def add_or_create_entity_field(self, rdf: ResolvedDataField, entity_information: bool = True) -> None:
        """
        Adds a field to the entity information or fields list.
        If the field already exists, it will append new values, if they don't already exist
        for the requested entity index.
        """
        check = self.entity_information if entity_information else self.fields

        existing_candidates = [f for f in check if f.name == rdf.name]
        if rdf.fact_subtype_id:
            existing_candidates = [c for c in existing_candidates if c.fact_subtype_id == rdf.fact_subtype_id]
        if len(existing_candidates) > 1:
            logger.warning(
                "Found more than one field candidate for merger", merging_field=rdf, candidates=existing_candidates
            )
        existing_field = next(iter(existing_candidates), None)
        if existing_field:
            new_values = []
            for value in rdf.values:
                for idx, ex_value in enumerate(existing_field.values):
                    if (
                        value.entity_idx == ex_value.entity_idx
                        and value.related_entity_idx == ex_value.related_entity_idx
                    ):
                        conf = max((e.confidence for e in value.evidences if e.confidence is not None), default=0)
                        ex_conf = max((e.confidence for e in ex_value.evidences if e.confidence is not None), default=0)
                        if conf > ex_conf:
                            existing_field.values[idx] = value
                        break
                else:
                    new_values.append(value)
            existing_field.values.extend(new_values)
        else:
            check.append(rdf)

    def add_or_create_entity(
        self,
        entity: SubmissionEntity,
        entity_information: list[ResolvedDataField] | None = None,
        fields: list[ResolvedDataField] | None = None,
        entity_idx: int | None = None,
    ) -> int:
        """
        Adds an entity to the onboarded file or updates an existing entity if it's a duplicate.
        If entity_idx is provided, it will migrate the values for that entity to the new entity index.
        None values are transferred in any case, but not assigned to an entity.

        This will not deduplicate SubmissionEntityType.SUBMISSION entities with different IDs, because merging
        those values is not supported. Caller should handle this case.

        Returns the entity index to which data was saved.
        """
        has_fni = self.has_fni
        new_idx = len(self.entities)
        for c_idx, c_entity in enumerate(self.entities):
            if c_entity.type == entity.type and c_entity.id.lower() == entity.id.lower():
                new_idx = c_idx
                if not has_fni and entity.is_fni:
                    # If we have an incoming FNI, we should replace the existing duplicate entity
                    self.entities[c_idx] = entity
                break
        else:
            self.entities.append(entity)

        def _filter_vals(vals: list[ResolvedDataValue]) -> list[ResolvedDataValue]:
            new_values = [dataclasses.replace(v) for v in vals if v.entity_idx is None]
            if entity_idx is not None:
                # If we have an existing entity, remap entity information to the new entity index
                new_values += [dataclasses.replace(v, entity_idx=new_idx) for v in vals if v.entity_idx == entity_idx]
            return new_values

        if entity_information:
            for ei in entity_information:
                n_vals = _filter_vals(ei.values)
                if n_vals:
                    self.add_or_create_entity_field(dataclasses.replace(ei, values=n_vals))

        if fields:
            for fi in fields:
                n_vals = _filter_vals(fi.values)
                if n_vals:
                    self.add_or_create_entity_field(
                        dataclasses.replace(fi, values=n_vals),
                        entity_information=False,
                    )

        return new_idx

    def assign_to_fni(self, entity_idxs: list[int | None] | None = None):
        """
        Assigns all values from entities at supplied indexes to the First Named Insured entity.
        Defaults to assigning unassigned values.
        """
        if entity_idxs is None:
            entity_idxs = [None]

        if self.has_fni:
            for f in self.all_fields:
                for value in f.values:
                    if value.entity_idx in entity_idxs:
                        value.entity_idx = self.fni_idx

    def get_external_identifiers(self) -> dict[int, list[ExternalIdentifier]]:
        """
        Extracts all entity identifiers from entity information and fields and returns a dictionary with key entity_idx
        and value list of corresponding ExternalIdentifiers
        """
        external_identifiers = defaultdict(dict)
        for f in self.fields + self.entity_information:
            for val in f.values:
                if val.value is None:
                    continue
                identifier = ExternalIdentifierType.try_parse_str(
                    f.fact_subtype_id
                ) or ExternalIdentifierType.try_parse_str(f.name)
                if not identifier and f.fact_subtype_id == FactSubtypeID.USDOTS:
                    identifier = ExternalIdentifierType.USDOT
                if identifier:
                    if identifier in external_identifiers[val.entity_idx]:
                        if external_identifiers[val.entity_idx][identifier][0].value == val.value:
                            continue
                        if identifier in ExternalIdentifierType.unique_external_identifiers():
                            if val.entity_idx is None:
                                logger.error(
                                    "Multiple values for unique external identifier",
                                    identifier=identifier.value,
                                    entity_idx=val.entity_idx,
                                    existing_value=external_identifiers[val.entity_idx][identifier],
                                    new_value=val.value,
                                )
                            else:
                                logger.warning(
                                    "Multiple values for unique external identifier",
                                    identifier=identifier.value,
                                    entity_idx=val.entity_idx,
                                    existing_value=external_identifiers[val.entity_idx][identifier],
                                    new_value=val.value,
                                )
                        else:
                            logger.warning(
                                "Multiple values for external identifier",
                                identifier=identifier.value,
                                entity_idx=val.entity_idx,
                                existing_value=external_identifiers[val.entity_idx][identifier],
                                new_value=val.value,
                            )
                            external_identifiers[val.entity_idx][identifier].append(
                                ExternalIdentifier(value=val.value, type=identifier)
                            )
                    else:
                        external_identifiers[val.entity_idx][identifier] = [
                            ExternalIdentifier(value=val.value, type=identifier)
                        ]
        return {k: sum(v.values(), []) for k, v in external_identifiers.items()}

    def get_in_llm_friendly_format(self) -> dict:
        """
        Returns a dictionary representation of the onboarded file that is friendly for LLM processing.
        This includes all fields and entities in a flat structure.
        """

        llm_entities_fields = [{} for e in self.entities]
        llm_entities_information = [{} for e in self.entities]

        for f in self.fields:
            values = {v.entity_idx: v.value for v in f.values}
            for entity_idx, llm_fields in enumerate(llm_entities_fields):
                if entity_idx in values:
                    llm_fields.update({f.name: values.get(entity_idx)})

        for ei in self.entity_information:
            values = {v.entity_idx: v.observed_value or v.value for v in ei.values}
            for entity_idx, llm_fields in enumerate(llm_entities_information):
                if entity_idx in values:
                    llm_fields.update({ei.name: values.get(entity_idx)})

        entity_to_structures = defaultdict(list)
        for entity_idx, entity in enumerate(self.entities):
            if entity.type != SubmissionEntityType.STRUCTURE:
                continue
            structure = llm_entities_information[entity_idx]
            structure["fields"] = llm_entities_fields[entity_idx]
            entity_to_structures[entity.parent_idx].append(structure)

        parent_entities = []
        for entity_idx, entity in enumerate(self.entities):
            if entity.type != SubmissionEntityType.BUSINESS:
                continue
            business_entity = llm_entities_information[entity_idx]
            if entity.entity_named_insured:
                business_entity["entity_named_insured"] = entity.entity_named_insured.value
            business_entity["fields"] = llm_entities_fields[entity_idx]
            if entity_idx in entity_to_structures:
                business_entity["structures"] = entity_to_structures[entity_idx]
            parent_entities.append(business_entity)

        return {"entities": parent_entities}


def _get_entity_type(entity: SubmissionEntity | dict) -> SubmissionEntityType:
    if isinstance(entity, dict):
        return SubmissionEntityType.try_parse_str(entity["type"])
    return entity.type


def _is_non_empty_value(v: Any) -> bool:
    if isinstance(v, str):
        v = v.strip()
    return v is not None and v != "" and v != {}  # noqa: PLC1901


def get_field_key(
    field_: ResolvedDataField | dict, entities: Sequence[SubmissionEntity | dict], override_with_fsid: bool = True
) -> str:
    """
    During processing we have tried to resolve the field name to a fact subtype.
    If we haven't there is a good reason for that, hence we don't want to merge here fact subtype with name.

    For example if we have field "Driver Name" in VEHICLE and DRIVER file
    we do not want to merge them despite the same name.
    """
    fact_subtype_id = field_.fact_subtype_id if hasattr(field_, "fact_subtype_id") else field_.get("fact_subtype_id")
    if override_with_fsid and fact_subtype_id:
        return f"fact-{fact_subtype_id}"

    entity_types: list[SubmissionEntityType]
    if isinstance(field_, dict):
        entity_types = [
            _get_entity_type(entities[x["entity_idx"]])
            for x in field_["values"]
            if x["entity_idx"] is not None and _is_non_empty_value(x["value"])
        ]
    else:
        entity_types = [
            _get_entity_type(entities[x.entity_idx])
            for x in field_.values
            if x.entity_idx is not None and _is_non_empty_value(x.value)
        ]
    distinct_type: str = "Unknown"
    if len(entity_types) == 0:
        distinct_type = "Non-Fleet"
    elif all(x == SubmissionEntityType.EQUIPMENT for x in entity_types):
        distinct_type = "Equipment"
    elif all(x == SubmissionEntityType.DRIVER for x in entity_types):
        distinct_type = "Driver"
    elif all(x == SubmissionEntityType.VEHICLE for x in entity_types):
        distinct_type = "Vehicle"
    elif all(
        x not in {SubmissionEntityType.DRIVER, SubmissionEntityType.VEHICLE, SubmissionEntityType.EQUIPMENT}
        for x in entity_types
    ):
        distinct_type = "Non-Fleet"
    elif fact_subtype_id and fact_subtype_id in FactSubtypeID.subtypes_with_multiple_parents():
        distinct_type = "Multiple-Parents"
    else:
        logger.error("Unexpected entity_types", entity_types=entity_types, field=field_, entities=entities)

    if isinstance(field_, dict):
        return f"name-{field_['name']}-{distinct_type}"
    return f"name-{field_.name}-{distinct_type}"
