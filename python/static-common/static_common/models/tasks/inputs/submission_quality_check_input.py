from dataclasses import dataclass

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class PdfFile:
    file_name: str
    s3_key: str


@dataclass_json
@dataclass
class TsvSheet:
    sheet_name: str
    tsv_data: str


@dataclass_json
@dataclass
class TsvFile:
    file_name: str
    file_sheets: list[TsvSheet]


@dataclass_json
@dataclass
class SubmissionQualityCheckInput:
    extracted_naics_code: str
    pdf_files: list[PdfFile]
    tsv_files: list[TsvFile]
