from dataclasses import dataclass

from static_common.enums.supplemental_category import SupplementalCategory


@dataclass
class AcordClassificationInfo:
    version_id: str
    form_name: str
    form_delimiter: str | None = None
    form_year: int | None = None
    form_version: str | None = None


@dataclass
class SupplementalClassificationInfo:
    template_name: str | None = None
    category: SupplementalCategory | None = None
    category_description: str | None = None


@dataclass
class FileAdditionalInfo:
    acord: AcordClassificationInfo | None = None
    supplemental: SupplementalClassificationInfo | None = None
    email_id: str | None = None
    policy_carrier: str | None = None
    is_pre_renewal: bool | None = None
    number_of_pages: int | None = None
    number_of_sheets: int | None = None
    number_of_tables: int | None = None
