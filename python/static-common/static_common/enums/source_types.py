from static_common.enums.enum import StrEnum


class SourceTypeID(StrEnum):
    """Sources of facts and documents"""

    INFERENCE = "INFERENCE"
    USER_FEEDBACK = "USER_FEEDBACK"
    FIRST_PARTY = "FIRST_PARTY"
    OBSERVATION_AGGREGATION = "OBSERVATION_AGGREGATION"
    HEALTH_INSPECTION = "HEALTH_INSPECTION"
    WEB_INGESTED = "WEB_INGESTED"
    """
    Data received form specific url and of type: WebIngestedSourceTypeID
    """
    SCALE_SERP = "SCALE_SERP"
    """
    Data discovered through ScaleSERP, only used for news
    https://www.scaleserp.com/
    """

    BETTER_BUSINESS_BUREAU = "BETTER_BUSINESS_BUREAU"
    """Deprecated. Replaced by WEB_INGESTED"""
    BUILDZOOM = "BUILDZOOM"
    """Deprecated. Replaced by WEB_INGESTED"""
    FACEBOOK = "FACEBOOK"
    """Deprecated. Replaced by WEB_INGESTED"""
    GOOGLE_LOCAL = "GOOGLE_LOCAL"
    """Deprecated. Replaced by WEB_INGESTED"""
    TRIP_ADVISOR = "TRIP_ADVISOR"
    """Deprecated. Replaced by WEB_INGESTED"""
    YELP = "YELP"
    """Deprecated. Replaced by WEB_INGESTED"""
    HAZARD_HUB = "HAZARD_HUB"
    """Deprecated. Replaced by WEB_INGESTED"""
    ENHANCED_HAZARD_HUB = "ENHANCED_HAZARD_HUB"
    """Deprecated. Replaced by WEB_INGESTED"""
    FEMA = "FEMA"
    """Deprecated. Replaced by WEB_INGESTED"""
    HUD_DATASET = "HUD_DATASET"
    """Deprecated. Replaced by WEB_INGESTED"""
    OSHA = "OSHA"
    """Deprecated. Replaced by WEB_INGESTED"""
    HOUZZ = "HOUZZ"
    """Deprecated. Replaced by WEB_INGESTED"""
    INDEED = "INDEED"
    """Deprecated. Replaced by WEB_INGESTED"""
    FDA = "FDA"
    """Deprecated. Replaced by WEB_INGESTED"""
    CPSC = "CPSC"
    """Deprecated. Replaced by WEB_INGESTED"""
    FSIS = "FSIS"
    """Deprecated. Replaced by WEB_INGESTED"""
    THOMASNET = "THOMASNET"
    """Deprecated. Replaced by WEB_INGESTED"""
    RELATIVITY6 = "RELATIVITY6"
    """Deprecated. Replaced by WEB_INGESTED"""
    IQS = "IQS"
    """Deprecated. Replaced by WEB_INGESTED"""
    FMCSA = "FMCSA"
    """Deprecated. Replaced by WEB_INGESTED"""
    ATF = "ATF"
    """Deprecated. Replaced by WEB_INGESTED"""
    NHTSA = "NHTSA"
    """Deprecated. Replaced by WEB_INGESTED"""
    DOT_REPORT = "DOT_REPORT"
    """Deprecated. Replaced by WEB_INGESTED"""
    GOOD_JOBS_FIRST = "GOOD_JOBS_FIRST"
    """Deprecated. Replaced by WEB_INGESTED"""


ERSSupportedSourceTypes = [SourceTypeID.USER_FEEDBACK, SourceTypeID.WEB_INGESTED, SourceTypeID.FIRST_PARTY]


class WebIngestedSourceTypeID(StrEnum):
    """Type of source for WEB_INGESTED"""

    BETTER_BUSINESS_BUREAU = "BETTER_BUSINESS_BUREAU"
    YELP = "YELP"
    BUILDZOOM = "BUILDZOOM"
    HAZARD_HUB = "HAZARD_HUB"
    ENHANCED_HAZARD_HUB = "ENHANCED_HAZARD_HUB"
    FEMA = "FEMA"
    FACEBOOK = "FACEBOOK"
    GOOGLE_LOCAL = "GOOGLE_LOCAL"
    TRIP_ADVISOR = "TRIP_ADVISOR"
    APARTMENTS = "APARTMENTS"
    """ https://www.apartments.com/ """
    HUD_DATASET = "HUD_DATASET"
    OSHA = "OSHA"
    INDEED = "INDEED"
    HOUZZ = "HOUZZ"
    FDA = "FDA"
    PROMETRIX = "PROMETRIX"
    RISKMETER = "RISKMETER"
    USPTO = "USPTO"
    """
     U.S. Food & Drug Administration
     https://datadashboard.fda.gov/ora/api/index.htm
    """
    FSIS = "FSIS"
    CPSC = "CPSC"
    RELATIVITY6 = "RELATIVITY6"
    """
    Deprecated. Won't be used anymore due to ENG-9045
    """
    THOMASNET = "THOMASNET"
    IQS = "IQS"
    """
    Industrial Quick Search - Manufacturer Directory
    https://news.iqsdirectory.com/
    """
    FMCSA = "FMCSA"
    """
    Federal Motor Carrier Safety Administration
    https://www.fmcsa.dot.gov/
    """
    SAFER_FMCSA = "SAFER_FMCSA"
    DOT_REPORT = "DOT_REPORT"
    """
    DOT.report is the most comprehensive up-to-date database of all known USDOT numbers. On this site you will find all
     publicly avcailable details on any requested DOT number as well as contact information if provided.
    https://dot.report/
    We use it as a complement source for data missing in FMCSA
    """
    ATF = "ATF"
    """
    Bureau of Alcohol, Tobacco, Firearms and Explosives
    From https://www.atf.gov/
    """
    TTB = "TTB"
    """
    Alcohol and Tobacco Tax and Trade Bureau
    From https://www.ttb.gov/
    """
    NHTSA = "NHTSA"
    """
    National Highway Traffic Safety Administration
    From https://www.nhtsa.gov/ and https://vpic.nhtsa.dot.gov/api/
    """
    GOOD_JOBS_FIRST = "GOOD_JOBS_FIRST"
    """
    https://violationtracker.goodjobsfirst.org/violation-tracker
    """
    EFAST = "EFAST"
    """
    EFAST2, an all-electronic system, receives and displays Forms 5500 Series Annual Returns/Reports and
    Form PR Pooled Plan Provider Registrations on behalf of DOL, IRS, and PBGC. An official website of
    the United States government.
    https://www.efast.dol.gov/welcome.html
    """
    EPA = "EPA"
    """
    United States Environmental Protection Agency
    From https://www.epa.gov/
    """
    WEBSITE_CONTENT = "WEBSITE_CONTENT"
    """
    Usually represents the homepage of the business from where we have crawled the information.
    """
    UNICOURT = "UNICOURT"
    """
    UniCourt - Access Court Records and Legal data
    From https://unicourt.com/
    """
    SAM = "SAM"
    """
    The System for Award Management
    from https://sam.gov/content/home
    """
    OPEN_CORPORATES = "OPEN_CORPORATES"
    """
    The largest open database of companies in the world
    From https://opencorporates.com/
    """
    ABC_CA = "ABC_CA"
    """
    California department of Alcoholic Beverage Control
    From https://www.abc.ca.gov/
    """
    NY_GOV = "NY_GOV"
    """
    State of New York open database
    From https://data.ny.gov/
    """
    TX_GOV = "TX_GOV"
    """
    State of Texas open database
    https://www.tabc.texas.gov/
    """
    IL_GOV = "IL_GOV"
    """ https://ilcc.illinois.gov/ """
    AZ_GOV = "AZ_GOV"
    """ https://www.azliquor.gov/ """
    PA_GOV = "PA_GOV"
    """ https://plcbplus.pa.gov/ """
    FL_GOV = "FL_GOV"
    """ http://www.myfloridalicense.com/"""
    NJ_GOV = "NJ_GOV"
    """ https://www.nj.gov/ """
    MA_GOV = "MA_GOV"
    """ https://elicensing.mass.gov/ """
    MN_GOV = "MN_GOV"
    """ https://app.dps.mn.gov/ """
    MI_GOV = "MI_GOV"
    """ https://www.michigan.gov/ """
    MI_CRA_THC = "MI_CRA_THC"
    """ https://aca-prod.accela.com/MIMM """
    MI_MLCC = "MI_MLCC"
    """ https://customers.mlcc.michigan.gov/ """
    VA_GOV = "VA_GOV"
    """ https://www.abc.virginia.gov/ """
    MO_GOV = "MO_GOV"
    """ https://data.mo.gov/"""
    CO_GOV = "CO_GOV"
    """ https://codor.mylicense.com/ """
    SC_GOV = "SC_GOV"
    """ https://mydorway.dor.sc.gov/ """
    EBSA = "EBSA"
    NOAA = "NOAA"
    """
    National Oceanic and Atmospheric Administration
    https://www.noaa.gov/ used for weather events
    example https://www.ncei.noaa.gov/pub/data/swdi/stormevents/csvfiles/
    Used with GeoEventType.WEATHER and WeatherEventSubType
    """
    NOAA_WEATHER_ALERTS = "NOAA_WEATHER_ALERTS"
    """
    https://www.weather.gov/ provided by NOAA used for weather alerts
    https://www.weather.gov/documentation/services-web-api#/default/alerts_active
    Used with GeoEventType.WEATHER_ALERT and GeoZoneType.WEATHER_AFFECTED_ZONE
    """

    NASA_FIRMS = "NASA_FIRMS"
    """ https://firms.modaps.eosdis.nasa.gov/ and https://firms.modaps.eosdis.nasa.gov/active_fire/ """
    USGS_EARTHQUAKE = "USGS_EARTHQUAKE"
    """ https://earthquake.usgs.gov/ """
    FEMA_NFHL = "FEMA_NFHL"
    """ https://msc.fema.gov/portal/advanceSearch """
    EXPERIAN = "EXPERIAN"
    IRS_FORM_990 = "IRS_FORM_990"
    """ IRS Form 990 data (https://www.irs.gov/charities-non-profits/form-990-series-downloads) """

    CO_DEC = "CO_DEC"
    """ Colorado Department of Early Childhood """
    WI_DCF = "WI_DCF"
    """ Wisconsin Department of Children and Families """
    MN_DHS = "MN_DHS"
    """ Minnesota Department of Human Services """
    IL_DCFS = "IL_DCFS"
    """ Illinois Department of Children and Family Services """
    MO_DESE = "MO_DESE"
    """ Missouri Department of Elementary and Secondary Education """
    MI_CCLB = "MI_CCLB"
    """ Michigan Child Care Licensing Bureau """
    CA_CSLB = "CA_CSLB"
    """California Contractors State License Board"""
