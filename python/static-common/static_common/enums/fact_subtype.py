from __future__ import annotations

from static_common.enums.enum import StrEnum


class FactSubtypeID(StrEnum):
    TEST_MULTI_LABEL = "TEST_MULTI_LABEL"
    TEST_BINARY_FACT = "TEST_BINARY_FACT"
    TEST_SINGLE_BINARY = "TEST_SINGLE_BINARY"
    TEST_SPRAY_PAINTED_BY_SAM = "TEST_SPRAY_PAINTED_BY_SAM"
    TEST_SPRAY_PAINTED_BY_SAM_MULTILABEL = "TEST_SPRAY_PAINTED_BY_SAM_MULTILABEL"
    SAM_CAN_PAINT_X = "SAM_CAN_PAINT_X"
    FACT_SUBTYPE_ID_TEST_ONE_NO_GROUP = "FACT_SUBTYPE_ID_TEST_ONE_NO_GROUP"
    FACT_SUBTYPE_ID_TEST_ONE_WITH_MULTILABEL_NEWLY_CREATED = "FACT_SUBTYPE_ID_TEST_ONE_WITH_MULTILABEL_NEWLY_CREATED"
    FACT_SUBTYPE_ID_TEST_ONE_WITH_MULTILABEL_GROUP_EXISTING = "FACT_SUBTYPE_ID_TEST_ONE_WITH_MULTILABEL_GROUP_EXISTING"
    NEWS_SHOOTING = "NEWS_SHOOTING"
    HOMEONWERS_ASSOCIATION = "HOMEONWERS_ASSOCIATION"
    NEWS_RISKS = "NEWS_RISKS"
    BROKER_NAICS_GROUP = "BROKER_NAICS_GROUP"
    ELECTRONIC_CIGARETTE_MANUFACTURING = "ELECTRONIC_CIGARETTE_MANUFACTURING"
    ATV_MANUFACTURING = "ATV_MANUFACTURING"
    NEW_VENTURE_CANCELLATION = "NEW_VENTURE_CANCELLATION"
    NEW_VENTURE = "NEW_VENTURE"
    NON_RENEWAL_CANCELATION = "NON_RENEWAL_CANCELATION"
    HAPPY_HOUR = "HAPPY_HOUR"
    TRAMPOLINE_PARK = "TRAMPOLINE_PARK"
    SELLS_MARIJUANA = "SELLS_MARIJUANA"
    DISCONTINUED_PRODUCTS = "DISCONTINUED_PRODUCTS"
    E_SCOOTER_MANUFACTURING = "E_SCOOTER_MANUFACTURING"
    CONCRETE_PRINTING = "CONCRETE_PRINTING"
    E_CIG_PRODUCT = "E_CIG_PRODUCT"
    HORIZONTAL_CONCRETE = "HORIZONTAL_CONCRETE"
    DISCONTINUED_OPERATIONS = "DISCONTINUED_OPERATIONS"
    MARIJUANA_TESTING_LAB = "MARIJUANA_TESTING_LAB"
    HELMETS_CONTACT_SPORTS = "HELMETS_CONTACT_SPORTS"
    SMASH_ROOM = "SMASH_ROOM"
    ZIPLINE_FACILITY = "ZIPLINE_FACILITY"
    HORIZONTAL_DRILLING = "HORIZONTAL_DRILLING"
    MOLD_PRESENCE = "MOLD_PRESENCE"
    TREE_STAND_PRODUCT = "TREE_STAND_PRODUCT"
    COMMERCIAL_AREA_SIZE = "COMMERCIAL_AREA_SIZE"
    DRIVER_DEPARTMENT = "DRIVER_DEPARTMENT"
    DRIVER_ENTITY = "DRIVER_ENTITY"
    DRIVER_LICENSE_CLASS = "DRIVER_LICENSE_CLASS"
    DRIVER_LICENSE_EXPIRATION_DATE = "DRIVER_LICENSE_EXPIRATION_DATE"
    DRIVER_LOCATION = "DRIVER_LOCATION"
    DRIVER_MARTIAL_STATUS = "DRIVER_MARTIAL_STATUS"
    DRIVER_ROLE = "DRIVER_ROLE"
    DRIVER_STATUS = "DRIVER_STATUS"
    DRIVERS_NOTES = "DRIVERS_NOTES"
    DRIVER_CDL_STATUS = "DRIVER_CDL_STATUS"
    DRIVER_NUMBER_OF_ACCIDENTS = "DRIVER_NUMBER_OF_ACCIDENTS"
    DRIVER_NUMBER_OF_VIOLATIONS = "DRIVER_NUMBER_OF_VIOLATIONS"
    DRIVER_MOST_RECENT_ACCIDENT = "DRIVER_MOST_RECENT_ACCIDENT"
    DRIVER_MOST_RECENT_VIOLATION = "DRIVER_MOST_RECENT_VIOLATION"
    DRIVER_PAST_DUI = "DRIVER_PAST_DUI"
    DRIVER_CDL_ISSUE_DATE = "DRIVER_CDL_ISSUE_DATE"
    DRIVER_ENDORSEMENTS = "DRIVER_ENDORSEMENTS"
    HAS_CCTV = "HAS_CCTV"
    HAS_ELEVATORS = "HAS_ELEVATORS"
    LOSS_PAYEE = "LOSS_PAYEE"
    NUMBER_OF_BATHROOMS = "NUMBER_OF_BATHROOMS"
    NUMBER_OF_BEDROOM = "NUMBER_OF_BEDROOM"
    NUMBER_OF_BUILDINGS = "NUMBER_OF_BUILDINGS"
    NUMBER_OF_ELEVATORS = "NUMBER_OF_ELEVATORS"
    PREMISES_DATE_ADDED = "PREMISES_DATE_ADDED"
    PREMISES_DIVISION = "PREMISES_DIVISION"
    PREMISES_NOTES = "PREMISES_NOTES"
    PREMISES_STATUS = "PREMISES_STATUS"
    REPLACEMENT_COST = "REPLACEMENT_COST"
    ROOF_ANCHOR = "ROOF_ANCHOR"
    VEHICLE_ADDITIONAL_INTEREST = "VEHICLE_ADDITIONAL_INTEREST"
    VEHICLE_AMOUNT_OF_INSURANCE = "VEHICLE_AMOUNT_OF_INSURANCE"
    VEHICLE_CLASS_CODE = "VEHICLE_CLASS_CODE"
    VEHICLE_COLLISION_COMPREHENSIVE_DEDUCTIBLE_COVERAGE = "VEHICLE_COLLISION_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"
    VEHICLE_COLOR = "VEHICLE_COLOR"
    VEHICLE_COMPREHENSIVE_DEDUCTIBLE_COVERAGE = "VEHICLE_COMPREHENSIVE_DEDUCTIBLE_COVERAGE"
    VEHICLE_DATE_ADDED = "VEHICLE_DATE_ADDED"
    VEHICLE_DESCRIPTION = "VEHICLE_DESCRIPTION"
    VEHICLE_DIVISION = "VEHICLE_DIVISION"
    VEHICLE_ENTITY_OWNER = "VEHICLE_ENTITY_OWNER"
    VEHICLE_INSURANCE_PREMIUM = "VEHICLE_INSURANCE_PREMIUM"
    VEHICLE_LEASED = "VEHICLE_LEASED"
    VEHICLE_LESSOR = "VEHICLE_LESSOR"
    VEHICLE_OWNED = "VEHICLE_OWNED"
    VEHICLE_PIP_DEDUCTIBLE_COVERAGE = "VEHICLE_PIP_DEDUCTIBLE_COVERAGE"
    VEHICLE_RADIUS = "VEHICLE_RADIUS"
    VEHICLE_REPLACEMENT_COST = "VEHICLE_REPLACEMENT_COST"
    VEHICLE_STATUS = "VEHICLE_STATUS"
    VEHICLE_USE = "VEHICLE_USE"
    VEHICLES_NOTES = "VEHICLES_NOTES"
    HAS_PREMISES_FENCE = "HAS_PREMISES_FENCE"
    HAS_PULL_CORDS_IN_EACH_UNIT = "HAS_PULL_CORDS_IN_EACH_UNIT"
    HAS_VEHICLE_APD_COVERAGE = "HAS_VEHICLE_APD_COVERAGE"
    HAS_VEHICLE_AUTO_LIABILITY_COVERAGE = "HAS_VEHICLE_AUTO_LIABILITY_COVERAGE"
    HAS_VEHICLE_COLLISION_COVERAGE = "HAS_VEHICLE_COLLISION_COVERAGE"
    HAS_VEHICLE_COLLISION_WAIVER_COVERAGE = "HAS_VEHICLE_COLLISION_WAIVER_COVERAGE"
    HAS_VEHICLE_COMPREHENSIVE_COVERAGE = "HAS_VEHICLE_COMPREHENSIVE_COVERAGE"
    HAS_VEHICLE_LIABILITY_COVERAGE = "HAS_VEHICLE_LIABILITY_COVERAGE"
    HAS_VEHICLE_MEDICAL_PAYMENTS_COVERAGE = "HAS_VEHICLE_MEDICAL_PAYMENTS_COVERAGE"
    HAS_VEHICLE_PIP_COVERAGE = "HAS_VEHICLE_PIP_COVERAGE"
    HAS_VEHICLE_PROPERTY_DAMAGE_COVERAGE = "HAS_VEHICLE_PROPERTY_DAMAGE_COVERAGE"
    HAS_VEHICLE_UNDERINSURED_MOTORIST_COVERAGE = "HAS_VEHICLE_UNDERINSURED_MOTORIST_COVERAGE"
    HAS_POOL_LIFEGUARD = "HAS_POOL_LIFEGUARD"
    HAS_POOL_SELF_LOCKING_GATES = "HAS_POOL_SELF_LOCKING_GATES"
    HAS_POOL_FENCE = "HAS_POOL_FENCE"
    HAS_POOL_RULES_POSTED = "HAS_POOL_RULES_POSTED"
    HAS_POOL_DIVING_BOARD = "HAS_POOL_DIVING_BOARD"
    HAS_POOL_DEPTH_MARKERS = "HAS_POOL_DEPTH_MARKERS"
    POOL_FENCE_HEIGHT = "POOL_FENCE_HEIGHT"
    OBTAIN_PRODUCT_LIABILITY_CERTIFICATES_FROM_PARTNERS = "OBTAIN_PRODUCT_LIABILITY_CERTIFICATES_FROM_PARTNERS"
    PARTNER_PRODUCT_LIABILITY_MINIMUM = "PARTNER_PRODUCT_LIABILITY_MINIMUM"
    LISTED_AS_ADDITIONAL_INSURED_VENDOR = "LISTED_AS_ADDITIONAL_INSURED_VENDOR"
    YEARS_QUALITY_CONTROL_RECORDS_KEPT = "YEARS_QUALITY_CONTROL_RECORDS_KEPT"
    IDENTIFIABLE_FROM_COMPETITORS = "IDENTIFIABLE_FROM_COMPETITORS"
    PRODUCT_MANUFACTURING_RECORDS_KEPT = "PRODUCT_MANUFACTURING_RECORDS_KEPT"
    PRODUCT_SALES_RECORDS_KEPT = "PRODUCT_SALES_RECORDS_KEPT"
    PRODUCT_SUPPLIER_RECORDS_KEPT = "PRODUCT_SUPPLIER_RECORDS_KEPT"
    PRODUCT_DESIGN_RECORDS_KEPT = "PRODUCT_DESIGN_RECORDS_KEPT"
    YEARS_PRODUCT_RECORDS_KEPT = "YEARS_PRODUCT_RECORDS_KEPT"
    PRODUCT_DESIGNERS = "PRODUCT_DESIGNERS"
    PRODUCT_DESIGNS_VERIFIED = "PRODUCT_DESIGNS_VERIFIED"
    PRODUCT_WARNING_AND_INSTRUCTIONS_LEGAL_REVIEW = "PRODUCT_WARNING_AND_INSTRUCTIONS_LEGAL_REVIEW"
    PRODUCT_COMPLIES_WITH_STANDARDS = "PRODUCT_COMPLIES_WITH_STANDARDS"
    HAS_PRODUCT_CERTIFICATIONS = "HAS_PRODUCT_CERTIFICATIONS"
    OFFERS_USER_TRAINING = "OFFERS_USER_TRAINING"
    RECENT_ACQUISITIONS = "RECENT_ACQUISITIONS"
    PRODUCT_SUBJECT_TO_STANDARDS = "PRODUCT_SUBJECT_TO_STANDARDS"
    HAS_PRODUCT_RECALL_PROCEDURE = "HAS_PRODUCT_RECALL_PROCEDURE"
    PRODUCT_HAS_SPECIAL_MATERIALS = "PRODUCT_HAS_SPECIAL_MATERIALS"
    HAS_OTHER_PRODUCT_LIABILITY_ISSUES = "HAS_OTHER_PRODUCT_LIABILITY_ISSUES"
    HAS_PRODUCT_COMPLAINTS = "HAS_PRODUCT_COMPLAINTS"
    PRODUCT_UNDER_INVESTIGATION = "PRODUCT_UNDER_INVESTIGATION"
    PULL_CORD = "PULL_CORD"
    HAS_DAYCARE = "HAS_DAYCARE"
    PUBLICLY_HELD_COMPANY = "PUBLICLY_HELD_COMPANY"
    MERGER_ACQUISITION_ASSET_SALE_PAST_YEAR = "MERGER_ACQUISITION_ASSET_SALE_PAST_YEAR"
    OWNED_BY_OTHER_ENTITY = "OWNED_BY_OTHER_ENTITY"
    ENTITIES_CLOSING_LAYOFFS_PAST_YEAR = "ENTITIES_CLOSING_LAYOFFS_PAST_YEAR"
    HAS_ANTIDISCRIMINATION_POLICY = "HAS_ANTIDISCRIMINATION_POLICY"
    HAS_DATA_SECURITY_POLICY = "HAS_DATA_SECURITY_POLICY"
    YEARLY_EMPLOYEES_COUNT_10000_PLUS = "YEARLY_EMPLOYEES_COUNT_10000_PLUS"
    YEARLY_EMPLOYEES_PERCENTAGE_100000_PLUS = "YEARLY_EMPLOYEES_PERCENTAGE_100000_PLUS"
    REVENUE_FROM_PUBLIC_10_PERCENT_PLUS = "REVENUE_FROM_PUBLIC_10_PERCENT_PLUS"
    REVENUE_FROM_PUBLIC_PERCENTAGE = "REVENUE_FROM_PUBLIC_PERCENTAGE"
    HIPAA_COMPLIANCE = "HIPAA_COMPLIANCE"
    DIRECTORS_AND_OFFICERS = "DIRECTORS_AND_OFFICERS"
    SALARY_RANGES = "SALARY_RANGES"
    SEASONAL_EMPLOYEES_COUNT = "SEASONAL_EMPLOYEES_COUNT"
    LIABILITIES_AND_EQUITY = "LIABILITIES_AND_EQUITY"
    NON_CURRENT_LIABILITIES = "NON_CURRENT_LIABILITIES"
    NON_CURRENT_ASSETS = "NON_CURRENT_ASSETS"
    PROPERTY_AND_EQUIPMENT = "PROPERTY_AND_EQUIPMENT"
    OPERATING_EXPENSES = "OPERATING_EXPENSES"
    ADMINISTRATIVE_EXPENSES = "ADMINISTRATIVE_EXPENSES"
    INVESTMENT_EXPENSES = "INVESTMENT_EXPENSES"
    OTHER_EXPENSES = "OTHER_EXPENSES"
    TOTAL_EXPENSES = "TOTAL_EXPENSES"
    COST_OF_SALES = "COST_OF_SALES"
    CASHFLOW_OPERATING = "CASHFLOW_OPERATING"
    NET_LOSS = "NET_LOSS"
    CASHFLOW_INVESTING = "CASHFLOW_INVESTING"
    CASHFLOW_FINANCING = "CASHFLOW_FINANCING"
    CASH_END_PERIOD = "CASH_END_PERIOD"
    NON_CASH_ACTIVITY = "NON_CASH_ACTIVITY"
    INTEREST_EXPENSE = "INTEREST_EXPENSE"
    CURRENT_LONG_TERM_DEBT_MATURITY = "CURRENT_LONG_TERM_DEBT_MATURITY"
    CURRENT_LEASE_OBLIGATIONS = "CURRENT_LEASE_OBLIGATIONS"
    CURRENT_LIABILITY_OPERATING = "CURRENT_LIABILITY_OPERATING"
    CURRENT_LIABILITY_FINANCING = "CURRENT_LIABILITY_FINANCING"
    DEBT_TO_SERVICE_RATIO = "DEBT_TO_SERVICE_RATIO"
    DEBT_TO_ASSETS_RATIO = "DEBT_TO_ASSETS_RATIO"
    WORKING_CAPITAL_RATIO = "WORKING_CAPITAL_RATIO"
    VOLUNTEERS_EMPLOYEES_COUNT = "VOLUNTEERS_EMPLOYEES_COUNT"
    LEASED_EMPLOYEES_COUNT = "LEASED_EMPLOYEES_COUNT"
    INTERNS_EMPLOYEES_COUNT = "INTERNS_EMPLOYEES_COUNT"
    FOREIGN_EMPLOYEES_COUNT = "FOREIGN_EMPLOYEES_COUNT"
    US_EMPLOYEES_COUNT = "US_EMPLOYEES_COUNT"
    FT_US_EMPLOYEES_COUNT = "FT_US_EMPLOYEES_COUNT"
    PT_US_EMPLOYEES_COUNT = "PT_US_EMPLOYEES_COUNT"
    FT_NON_US_EMPLOYEES_COUNT = "FT_NON_US_EMPLOYEES_COUNT"
    PT_NON_US_EMPLOYEES_COUNT = "PT_NON_US_EMPLOYEES_COUNT"
    INDEPENDENT_CONTRACTOR_US_COUNT = "INDEPENDENT_CONTRACTOR_US_COUNT"
    INDEPENDENT_CONTRACTOR_NON_US_COUNT = "INDEPENDENT_CONTRACTOR_NON_US_COUNT"
    INHOUSE_COUNSEL_EMPLOYEES_COUNT = "INHOUSE_COUNSEL_EMPLOYEES_COUNT"
    FT_CA_EMPLOYEES_COUNT = "FT_CA_EMPLOYEES_COUNT"
    PT_CA_EMPLOYEES_COUNT = "PT_CA_EMPLOYEES_COUNT"
    CA_EMPLOYEES_COUNT = "CA_EMPLOYEES_COUNT"
    UNION_FT_US_EMPLOYEES_COUNT = "UNION_FT_US_EMPLOYEES_COUNT"
    UNION_PT_US_EMPLOYEES_COUNT = "UNION_PT_US_EMPLOYEES_COUNT"
    UNION_US_EMPLOYEES_COUNT = "UNION_US_EMPLOYEES_COUNT"
    UNION_FT_NON_US_EMPLOYEES_COUNT = "UNION_FT_NON_US_EMPLOYEES_COUNT"
    UNION_PT_NON_US_EMPLOYEES_COUNT = "UNION_PT_NON_US_EMPLOYEES_COUNT"
    UNION_NON_US_EMPLOYEES_COUNT = "UNION_NON_US_EMPLOYEES_COUNT"
    UNION_EMPLOYEES_COUNT = "UNION_EMPLOYEES_COUNT"
    NON_UNION_FT_US_EMPLOYEES_COUNT = "NON_UNION_FT_US_EMPLOYEES_COUNT"
    NON_UNION_PT_US_EMPLOYEES_COUNT = "NON_UNION_PT_US_EMPLOYEES_COUNT"
    NON_UNION_US_EMPLOYEES_COUNT = "NON_UNION_US_EMPLOYEES_COUNT"
    NON_UNION_FT_NON_US_EMPLOYEES_COUNT = "NON_UNION_FT_NON_US_EMPLOYEES_COUNT"
    NON_UNION_PT_NON_US_EMPLOYEES_COUNT = "NON_UNION_PT_NON_US_EMPLOYEES_COUNT"
    NON_UNION_NON_US_EMPLOYEES_COUNT = "NON_UNION_NON_US_EMPLOYEES_COUNT"
    NON_UNION_EMPLOYEES_COUNT = "NON_UNION_EMPLOYEES_COUNT"
    NON_UNION_FT_EMPLOYEES_COUNT = "NON_UNION_FT_EMPLOYEES_COUNT"
    NON_UNION_PT_EMPLOYEES_COUNT = "NON_UNION_PT_EMPLOYEES_COUNT"
    UNION_FT_EMPLOYEES_COUNT = "UNION_FT_EMPLOYEES_COUNT"
    UNION_PT_EMPLOYEES_COUNT = "UNION_PT_EMPLOYEES_COUNT"
    VOL_TERMINATED_EMPLOYEES_COUNT = "VOL_TERMINATED_EMPLOYEES_COUNT"
    INVOL_TERMINATED_EMPLOYEES_COUNT = "INVOL_TERMINATED_EMPLOYEES_COUNT"
    FT_FL_EMPLOYEES_COUNT = "FT_FL_EMPLOYEES_COUNT"
    PT_FL_EMPLOYEES_COUNT = "PT_FL_EMPLOYEES_COUNT"
    FL_EMPLOYEES_COUNT = "FL_EMPLOYEES_COUNT"
    FT_NY_EMPLOYEES_COUNT = "FT_NY_EMPLOYEES_COUNT"
    PT_NY_EMPLOYEES_COUNT = "PT_NY_EMPLOYEES_COUNT"
    NY_EMPLOYEES_COUNT = "NY_EMPLOYEES_COUNT"
    TX_EMPLOYEES_COUNT = "TX_EMPLOYEES_COUNT"
    FT_TX_EMPLOYEES_COUNT = "FT_TX_EMPLOYEES_COUNT"
    PT_TX_EMPLOYEES_COUNT = "PT_TX_EMPLOYEES_COUNT"
    LAYOFFS_EMPLOYEES_COUNT = "LAYOFFS_EMPLOYEES_COUNT"
    HAS_UNIFORM_APPLICATION = "HAS_UNIFORM_APPLICATION"
    HAS_ANNUAL_PERFORMANCE_REVIEW = "HAS_ANNUAL_PERFORMANCE_REVIEW"
    HAS_PERFORMANCE_CRITERIA = "HAS_PERFORMANCE_CRITERIA"
    HAS_COUNSEL_REVIEW_PRIOR_DISCIPLINE = "HAS_COUNSEL_REVIEW_PRIOR_DISCIPLINE"
    HAS_BACKGROUND_CHECK_AFTER_EMPLOYMENT = "HAS_BACKGROUND_CHECK_AFTER_EMPLOYMENT"
    HAS_PUBLISHED_HR_PROCEDURES = "HAS_PUBLISHED_HR_PROCEDURES"
    HAS_PROGRESSIVE_DISCIPLINE_POLICY = "HAS_PROGRESSIVE_DISCIPLINE_POLICY"
    HAS_DEDICATED_HR_MANAGER = "HAS_DEDICATED_HR_MANAGER"
    HAS_UNIFORM_HR_PRATICES_IN_ALL_LOCATIONS = "HAS_UNIFORM_HR_PRATICES_IN_ALL_LOCATIONS"
    HAS_IMPACT_ANALYSIS_FOR_WORKFORCE_REDUCTION = "HAS_IMPACT_ANALYSIS_FOR_WORKFORCE_REDUCTION"
    HAS_HANDBOOK_ACKNOWLEDGED_BY_EMPLOYEES = "HAS_HANDBOOK_ACKNOWLEDGED_BY_EMPLOYEES"
    HAS_HANDBOOK_PERIODIC_UPDATES = "HAS_HANDBOOK_PERIODIC_UPDATES"
    HAS_HANDBOOK_REVIEWED_BY_HR_COUNSEL = "HAS_HANDBOOK_REVIEWED_BY_HR_COUNSEL"
    HAS_COMPLAINT_PROCEDURES_ESTABILISHED = "HAS_COMPLAINT_PROCEDURES_ESTABILISHED"
    HAS_NO_RETALIATION_POLICY = "HAS_NO_RETALIATION_POLICY"
    HAS_EMPLOYEE_HOTLINE = "HAS_EMPLOYEE_HOTLINE"
    HAS_INTERNAL_INVESTIGATIONS_BY_HR = "HAS_INTERNAL_INVESTIGATIONS_BY_HR"
    HAS_CONFIDENTIAL_AGREEMENTS_OR_NDA = "HAS_CONFIDENTIAL_AGREEMENTS_OR_NDA"
    HAS_ANTIDISCRIMINATION_TRAINING_FOR_ALL_ANNUAL = "HAS_ANTIDISCRIMINATION_TRAINING_FOR_ALL_ANNUAL"
    HAS_ANTIDISCRIMINATION_TRAINING_FOR_ALL_EVERY_2_YEARS = "HAS_ANTIDISCRIMINATION_TRAINING_FOR_ALL_EVERY_2_YEARS"
    HAS_ANTIDISCRIMINATION_TRAINING_FOR_MANAGERS = "HAS_ANTIDISCRIMINATION_TRAINING_FOR_MANAGERS"
    HAS_ANTIDISCRIMINATION_TRAINING_UNREGULAR = "HAS_ANTIDISCRIMINATION_TRAINING_UNREGULAR"
    HAS_EMPLOYEE_TRAINING__FOR_HR_POLICY_WELL_DOCUMENTED = "HAS_EMPLOYEE_TRAINING__FOR_HR_POLICY_WELL_DOCUMENTED"
    HAS_ONBOARDING_TRAINING = "HAS_ONBOARDING_TRAINING"
    HAS_CYBERSECURITY_TRAINING = "HAS_CYBERSECURITY_TRAINING"
    HAS_SAFETY_TRAINING = "HAS_SAFETY_TRAINING"
    HAS_DEVELOPMENT_PLANS = "HAS_DEVELOPMENT_PLANS"
    FIRE_INCIDENT = "FIRE_INCIDENT"
    FATALITY_INCIDENT = "FATALITY_INCIDENT"
    PLAN_CHANGES_TERMINATIONS_PAST_24_MONTHS = "PLAN_CHANGES_TERMINATIONS_PAST_24_MONTHS"
    PLAN_FEES_EXPENSES_REVIEWS_PAST_24_MONTHS = "PLAN_FEES_EXPENSES_REVIEWS_PAST_24_MONTHS"
    PLAN_ERISA_NON_COMPLIANT = "PLAN_ERISA_NON_COMPLIANT"
    PLAN_BENEFIT_CHANGES_PAST_24_MONTHS = "PLAN_BENEFIT_CHANGES_PAST_24_MONTHS"
    PLAN_FIDUCIARY_LEGAL_ISSUES_PAST_24_MONTHS = "PLAN_FIDUCIARY_LEGAL_ISSUES_PAST_24_MONTHS"
    HAS_ESOP = "HAS_ESOP"
    HAS_LEVERAGED_ESOP = "HAS_LEVERAGED_ESOP"
    BANK_RECONCILIATION_FREQUENCY = "BANK_RECONCILIATION_FREQUENCY"
    BANK_RECONCILIATION_AUTHORIZATION = "BANK_RECONCILIATION_AUTHORIZATION"
    BANK_RECONCILIATION_SIGN_PERMISSION = "BANK_RECONCILIATION_SIGN_PERMISSION"
    DUAL_APPROVAL_REQUIRED = "DUAL_APPROVAL_REQUIRED"
    WIRE_TRANSFER_APPROVAL_PROCEDURES = "WIRE_TRANSFER_APPROVAL_PROCEDURES"
    AUDIT_FREQUENCY_DOMESTIC = "AUDIT_FREQUENCY_DOMESTIC"
    AUDIT_FREQUENCY_FOREIGN = "AUDIT_FREQUENCY_FOREIGN"
    INVENTORY_COUNT_FREQUENCY = "INVENTORY_COUNT_FREQUENCY"
    IT_AUDIT_THIRD_PARTY = "IT_AUDIT_THIRD_PARTY"
    INTERNAL_CONTROLS_ISSUES = "INTERNAL_CONTROLS_ISSUES"
    FRAUD_TRAINING_ALL_EMOPLOYEES = "FRAUD_TRAINING_ALL_EMOPLOYEES"
    FRAUD_TRAINING_TRANSFER_HANDLERS = "FRAUD_TRAINING_TRANSFER_HANDLERS"
    VENDOR_BACKGROUND_CHECKS = "VENDOR_BACKGROUND_CHECKS"
    CLIENT_ACCESS_CONTROLS = "CLIENT_ACCESS_CONTROLS"
    CLIENT_FUNDS_ACCESS = "CLIENT_FUNDS_ACCESS"
    EXPECTED_ENTITIES_CLOSING_LAYOFFS = "EXPECTED_ENTITIES_CLOSING_LAYOFFS"
    ENTITIES_CLOSING_LAYOFFS_PAST_24_MONTHS_OR_EXPECTED = "ENTITIES_CLOSING_LAYOFFS_PAST_24_MONTHS_OR_EXPECTED"
    BANKRUPTCY_OR_LIQUIDATION_PAST_24_MONTHS_OR_EXPECTED = "BANKRUPTCY_OR_LIQUIDATION_PAST_24_MONTHS_OR_EXPECTED"
    OFFICERS_CHANGES_EXPECTED = "OFFICERS_CHANGES_EXPECTED"
    OFFICERS_CHANGES_PAST_24_MONTHS_OR_EXPECTED = "OFFICERS_CHANGES_PAST_24_MONTHS_OR_EXPECTED"
    MERGER_OR_ACQUISITION_ASSET_SALE_PAST_24_MONTHS_OR_EXPECTED = (
        "MERGER_OR_ACQUISITION_ASSET_SALE_PAST_24_MONTHS_OR_EXPECTED"
    )
    PLAN_CHANGES_TERMINATIONS_EXPECTED = "PLAN_CHANGES_TERMINATIONS_EXPECTED"
    PLAN_CHANGES_TERMINATIONS_PAST_24_MONTHS_OR_EXPECTED = "PLAN_CHANGES_TERMINATIONS_PAST_24_MONTHS_OR_EXPECTED"
    PLAN_FEES_EXPENSES_REVIEWS_EXPECTED = "PLAN_FEES_EXPENSES_REVIEWS_EXPECTED"
    PLAN_FEES_EXPENSES_REVIEWS_PAST_24_MONTHS_OR_EXPECTED = "PLAN_FEES_EXPENSES_REVIEWS_PAST_24_MONTHS_OR_EXPECTED"
    PLAN_BENEFIT_CHANGES_EXPECTED = "PLAN_BENEFIT_CHANGES_EXPECTED"
    PLAN_BENEFIT_CHANGES_PAST_24_MONTHS_OR_EXPECTED = "PLAN_BENEFIT_CHANGES_PAST_24_MONTHS_OR_EXPECTED"
    ANY_MULTIEMPLOYER_PLAN = "ANY_MULTIEMPLOYER_PLAN"
    PLAN_REVIEWS_ANNUALLY = "PLAN_REVIEWS_ANNUALLY"
    PLAN_HIPAA_COMPLIANT = "PLAN_HIPAA_COMPLIANT"
    PLAN_MANAGED_BY_3RD_PARTY = "PLAN_MANAGED_BY_3RD_PARTY"
    SHAREHOLDERS_CHANGES_PAST_24_MONTHS = "SHAREHOLDERS_CHANGES_PAST_24_MONTHS"
    SHAREHOLDERS_CHANGES_EXPECTED = "SHAREHOLDERS_CHANGES_EXPECTED"
    SHAREHOLDERS_CHANGES_PAST_24_MONTHS_OR_EXPECTED = "SHAREHOLDERS_CHANGES_PAST_24_MONTHS_OR_EXPECTED"
    PUBLIC_OR_PRIVATE_OFFERING_PAST_24_MONTHS = "PUBLIC_OR_PRIVATE_OFFERING_PAST_24_MONTHS"
    PUBLIC_OR_PRIVATE_OFFERING_EXPECTED = "PUBLIC_OR_PRIVATE_OFFERING_EXPECTED"
    PUBLIC_OR_PRIVATE_OFFERING_PAST_24_MONTHS_OR_EXPECTED = "PUBLIC_OR_PRIVATE_OFFERING_PAST_24_MONTHS_OR_EXPECTED"
    BIG_4_AUDITOR = "BIG_4_AUDITOR"
    DEAL_DATE = "DEAL_DATE"
    DEAL_DESCRIPTION = "DEAL_DESCRIPTION"
    TRANSACTION_TYPE = "TRANSACTION_TYPE"
    TRANSACTION_JURISDICTION = "TRANSACTION_JURISDICTION"
    ENTERPRISE_VALUE = "ENTERPRISE_VALUE"
    US_GAAP_REVENUE = "US_GAAP_REVENUE"
    TOP_CUSTOMERS = "TOP_CUSTOMERS"
    HIGH_CUSTOMER_CONCENTRATION = "HIGH_CUSTOMER_CONCENTRATION"
    CA_EBITDA_USED = "CA_EBITDA_USED"
    AUDIT_CONCERNS = "AUDIT_CONCERNS"
    FRAUD_REPORTED = "FRAUD_REPORTED"
    BANKRUPTCY = "BANKRUPTCY"
    LOAN_AGREEMENTS_DESCRIPTION = "LOAN_AGREEMENTS_DESCRIPTION"
    SELLER_LEASES_DESCRIPTION = "SELLER_LEASES_DESCRIPTION"
    PAST_DUE_ACCOUNTS = "PAST_DUE_ACCOUNTS"
    SELLER_BILLING_DISPUTES = "SELLER_BILLING_DISPUTES"
    ASSETS_IN_GOOD_REPAIR = "ASSETS_IN_GOOD_REPAIR"
    TAXES_PAID_AT_CLOSING = "TAXES_PAID_AT_CLOSING"
    SELLER_LEGAL_CAPACITY = "SELLER_LEGAL_CAPACITY"
    SELLER_INFO_ACCURACY = "SELLER_INFO_ACCURACY"
    SELLER_GOOD_STANDING_CORP = "SELLER_GOOD_STANDING_CORP"
    SELLER_TAX_DELINQUENT = "SELLER_TAX_DELINQUENT"
    SELLER_JUDGMENTS_CLAIMS_LIENS_PROCEEDINGS = "SELLER_JUDGMENTS_CLAIMS_LIENS_PROCEEDINGS"
    COMPREHENSIVE_SALE = "COMPREHENSIVE_SALE"
    SELLER_COMPLIANCE = "SELLER_COMPLIANCE"
    SELLER_INVENTORY_SALABLE = "SELLER_INVENTORY_SALABLE"
    SELLER_DEFAULT = "SELLER_DEFAULT"
    SELLER_HAZARDOUS_SUBSTANCES = "SELLER_HAZARDOUS_SUBSTANCES"
    SELLER_UNDISCLOSED_LIABILITIES = "SELLER_UNDISCLOSED_LIABILITIES"
    SELLER_UNDISCLOSED_AGREEMENTS = "SELLER_UNDISCLOSED_AGREEMENTS"
    SELLER_DISCLOSURE = "SELLER_DISCLOSURE"
    SOLAR_PANELS_LIMIT = "SOLAR_PANELS_LIMIT"
    ED_SOFTWARE_LIMIT = "ED_SOFTWARE_LIMIT"
    LOSS_RUN_REQUEST = "LOSS_RUN_REQUEST"
    RESTAURANTOPERATIONTYPES = "RESTAURANTOPERATIONTYPES"
    DRINKINGESTABLISHMENTTYPES = "DRINKINGESTABLISHMENTTYPES"
    COMPETITOR_POLICY_ERP = "COMPETITOR_POLICY_ERP"
    EXPANDED_LIMITS_DEDUCTILES_COVERAGE_ERP = "EXPANDED_LIMITS_DEDUCTILES_COVERAGE_ERP"
    OVER_30_DAY_ERP = "OVER_30_DAY_ERP"
    EMPLOYED_PHYSICIANS_COVERAGE = "EMPLOYED_PHYSICIANS_COVERAGE"
    EMPLOYED_PHYSICIANS_SEPARATE_LIMIT = "EMPLOYED_PHYSICIANS_SEPARATE_LIMIT"
    UNLIMITED_ERP = "UNLIMITED_ERP"
    DEDUCTIBLE = "DEDUCTIBLE"
    MID_TERM_CANCEL_REWRITE = "MID_TERM_CANCEL_REWRITE"
    NON_STANDARD_POLICY_PERIOD = "NON_STANDARD_POLICY_PERIOD"
    BACKDATING_POLICY_EXTENSION = "BACKDATING_POLICY_EXTENSION"
    PL_OCCURRENCE_COVERAGE = "PL_OCCURRENCE_COVERAGE"
    MONOLINE_GL_COVERAGE = "MONOLINE_GL_COVERAGE"
    PRIOR_ACTS_GAP_COVERAGE = "PRIOR_ACTS_GAP_COVERAGE"
    PL_GL_LIMIT_DIFF = "PL_GL_LIMIT_DIFF"
    NO_AGGREGATE_LIMIT = "NO_AGGREGATE_LIMIT"
    MID_TERM_LIMIT_INCREASE = "MID_TERM_LIMIT_INCREASE"
    EBL_STANDALONE_COVERAGE = "EBL_STANDALONE_COVERAGE"
    PL_GL_DEDUCTIBLE_DIFF = "PL_GL_DEDUCTIBLE_DIFF"
    AGGREGATED_DEDUCTIBLE = "AGGREGATED_DEDUCTIBLE"
    AMEND_POLICY_LANGUAGE = "AMEND_POLICY_LANGUAGE"
    EXCLUSION_REMOVAL_ENDORSEMENT = "EXCLUSION_REMOVAL_ENDORSEMENT"
    CONSENT_TO_SETTLE_ENDORSEMENT = "CONSENT_TO_SETTLE_ENDORSEMENT"
    WAIVER_OF_SUBROGRATION_ENDORSEMENT = "WAIVER_OF_SUBROGRATION_ENDORSEMENT"
    UNDERLYING_AM_BEST_RATING_BELOW_A_MINUS = "UNDERLYING_AM_BEST_RATING_BELOW_A_MINUS"
    COVERAGE_ABOVE_SIR = "COVERAGE_ABOVE_SIR"
    COVERAGE_ABOVE_OTHER_PRIMARY_EXCESS = "COVERAGE_ABOVE_OTHER_PRIMARY_EXCESS"
    FACULTATIVE_REINSURANCE = "FACULTATIVE_REINSURANCE"
    UMBRELLA_LIMIT_LESS_THAN_500K = "UMBRELLA_LIMIT_LESS_THAN_500K"
    UNDERLYING_AUTO_POLICY = "UNDERLYING_AUTO_POLICY"
    UNDERLYING_MANAGED_CARE = "UNDERLYING_MANAGED_CARE"
    LIMIT_WITH_FACULTATIVE_REINSURANCE = "LIMIT_WITH_FACULTATIVE_REINSURANCE"
    LIMIT_REINSTATEMENT = "LIMIT_REINSTATEMENT"
    LIMIT_INCREASE = "LIMIT_INCREASE"
    DUAL_TOWER_LIMITS = "DUAL_TOWER_LIMITS"
    DEFENSE_OUTSIDE_LIMITS = "DEFENSE_OUTSIDE_LIMITS"
    EXCLUSION_REMOVAL = "EXCLUSION_REMOVAL"
    ABUSE_COVERAGE = "ABUSE_COVERAGE"
    ABUSE_LIMIT = "ABUSE_LIMIT"
    TOTAL_NUMBER_OF_PROCEDURES = "TOTAL_NUMBER_OF_PROCEDURES"
    NIGHTCLUB = "NIGHTCLUB"
    LOUNGE = "LOUNGE"
    IS_RUNDOWN_OR_UNKEMPT = "IS_RUNDOWN_OR_UNKEMPT"
    BAR_OR_PUB = "BAR_OR_PUB"
    SPECIALTY_BEER = "SPECIALTY_BEER"
    DIVE_BAR = "DIVE_BAR"
    SPECIALTY_WINE = "SPECIALTY_WINE"
    COLLEGE_BAR = "COLLEGE_BAR"
    IZAKAYA = "IZAKAYA"
    SPECIALTY_COCKTAIL = "SPECIALTY_COCKTAIL"
    QUICK_SERVICE = "QUICK_SERVICE"
    MOBILE_FOOD_BUSINESS = "MOBILE_FOOD_BUSINESS"
    BUFFET_OR_SELF_SERVICE = "BUFFET_OR_SELF_SERVICE"
    VIRTUAL = "VIRTUAL"
    CAFE_OR_COFFEE_SHOP = "CAFE_OR_COFFEE_SHOP"
    FULL_SERVICE_CASUAL = "FULL_SERVICE_CASUAL"
    FULL_SERVICE_UPSCALE = "FULL_SERVICE_UPSCALE"
    AFTERMARKET_AUTO_PARTS = "AFTERMARKET_AUTO_PARTS"
    SELF_POUR_EXPOSURE = "SELF_POUR_EXPOSURE"
    BOAT_MANUFACTURING = "BOAT_MANUFACTURING"
    LADDER_MANUFACTURING = "LADDER_MANUFACTURING"
    GAS_CANS_OR_FUEL_CONTAINERS_MANUFACTURING = "GAS_CANS_OR_FUEL_CONTAINERS_MANUFACTURING"
    ABOVE_GROUND_POOL_MANUFACTURING = "ABOVE_GROUND_POOL_MANUFACTURING"
    SCUBA_DIVING_GEAR_MANUFACTURING = "SCUBA_DIVING_GEAR_MANUFACTURING"
    FIRE_SUPPRESSION_EQUIPMENT_MANUFACTURING = "FIRE_SUPPRESSION_EQUIPMENT_MANUFACTURING"
    BOUNCE_HOUSES_AND_WEARABLE_INFLATABLE_TOYS = "BOUNCE_HOUSES_AND_WEARABLE_INFLATABLE_TOYS"
    AUTONOMOUS_VEHICLES_MANUFACTURING = "AUTONOMOUS_VEHICLES_MANUFACTURING"
    BABY_LOUNGERS_OR_HAMMOCKS_MANUFACTURING = "BABY_LOUNGERS_OR_HAMMOCKS_MANUFACTURING"
    BACKYARD_ZIPLINES_MANUFACTURING = "BACKYARD_ZIPLINES_MANUFACTURING"
    INFANT_CAR_SEATS_MANUFACTURING = "INFANT_CAR_SEATS_MANUFACTURING"
    BOTOX_OR_INJECTABLE_COSMETICS_MANUFACTURING = "BOTOX_OR_INJECTABLE_COSMETICS_MANUFACTURING"
    CONTINUOUS_IMPACT_HELMETS_MANUFACTURING = "CONTINUOUS_IMPACT_HELMETS_MANUFACTURING"
    CONSTRUCTION_CRANE_MANUFACTURING = "CONSTRUCTION_CRANE_MANUFACTURING"
    CRIB_BUMPER_MANUFACTURING = "CRIB_BUMPER_MANUFACTURING"
    CRITICAL_AUTO_PARTS_MANUFACTURING = "CRITICAL_AUTO_PARTS_MANUFACTURING"
    DOWN_THE_HOLE_OIL_GAS_PRODUCTS_MANUFACTURING = "DOWN_THE_HOLE_OIL_GAS_PRODUCTS_MANUFACTURING"
    FERTILITY_TREATMENT = "FERTILITY_TREATMENT"
    ELECTRIC_VEHICLE_BATTERY_MANUFACTURING = "ELECTRIC_VEHICLE_BATTERY_MANUFACTURING"
    E_CIGARETTES_OR_VAPE_MANUFACTURING = "E_CIGARETTES_OR_VAPE_MANUFACTURING"
    DROP_SIDE_CRIBS_MANUFACTURING = "DROP_SIDE_CRIBS_MANUFACTURING"
    FIREWORKS_OR_EXPLOSIVE_DEVICES_MANUFACTURING = "FIREWORKS_OR_EXPLOSIVE_DEVICES_MANUFACTURING"
    HOVERBOARD_OR_SELF_BALANCING_SCOOTER_MANUFACTURING = "HOVERBOARD_OR_SELF_BALANCING_SCOOTER_MANUFACTURING"
    INFANT_SKULL_SHAPING_HELMETS_MANUFACTURING = "INFANT_SKULL_SHAPING_HELMETS_MANUFACTURING"
    IMPLANTED_MEDICAL_PRODUCTS_MANUFACTURING = "IMPLANTED_MEDICAL_PRODUCTS_MANUFACTURING"
    INCLINED_INFANT_SLEEPERS_MANUFACTURING = "INCLINED_INFANT_SLEEPERS_MANUFACTURING"
    INVERSION_TABLES_MANUFACTURING = "INVERSION_TABLES_MANUFACTURING"
    PERMANENT_MAKEUP_OR_TATTOO_INK_MANUFACTURING = "PERMANENT_MAKEUP_OR_TATTOO_INK_MANUFACTURING"
    PETROCHEMICAL_VALVES_MANUFACTURING = "PETROCHEMICAL_VALVES_MANUFACTURING"
    POURABLE_ETHANOL_FUEL_MANUFACTURING = "POURABLE_ETHANOL_FUEL_MANUFACTURING"
    SCAFFOLDING_MANUFACTURING = "SCAFFOLDING_MANUFACTURING"
    SKYDIVING_GEAR_MANUFACTURING = "SKYDIVING_GEAR_MANUFACTURING"
    SLINGS_OR_FABRIC_WRAP_INFANT_CARRIERS_MANUFACTURING = "SLINGS_OR_FABRIC_WRAP_INFANT_CARRIERS_MANUFACTURING"
    TALCUM_POWDER_MANUFACTURING = "TALCUM_POWDER_MANUFACTURING"
    TRAMPOLINE_MANUFACTURING = "TRAMPOLINE_MANUFACTURING"
    TURKEY_FRYER_MANUFACTURING = "TURKEY_FRYER_MANUFACTURING"
    UNDERGROUND_STORAGE_TANKS_MANUFACTURING = "UNDERGROUND_STORAGE_TANKS_MANUFACTURING"
    OUTDOOR_SKYDIVING = "OUTDOOR_SKYDIVING"
    ELECTRIC_SCOOTER_SHARING_OR_RENTAL = "ELECTRIC_SCOOTER_SHARING_OR_RENTAL"
    MUSIC_FESTIVALS = "MUSIC_FESTIVALS"
    TRAVELING_CARNIVAL_RIDES = "TRAVELING_CARNIVAL_RIDES"
    INPATIENT_TREATMENT = "INPATIENT_TREATMENT"
    BUS_OR_DUCK_TOURS = "BUS_OR_DUCK_TOURS"
    PEDIATRIC_EXPOSURE = "PEDIATRIC_EXPOSURE"
    MEDICAL_MARIJUANA_SERVICES = "MEDICAL_MARIJUANA_SERVICES"
    ADOPTION_SERVICES = "ADOPTION_SERVICES"
    COSMETIC_ONLY_MEDSPA = "COSMETIC_ONLY_MEDSPA"
    GRILLING_ON_BALCONY = "GRILLING_ON_BALCONY"
    ANIMAL_FEED_MANUFACTURING = "ANIMAL_FEED_MANUFACTURING"
    COMPLETE_VEHICLE_MANUFACTURING = "COMPLETE_VEHICLE_MANUFACTURING"
    MAINTAINS_VENDOR_CONTRACT_RECORDS = "MAINTAINS_VENDOR_CONTRACT_RECORDS"
    SECURITY_GUARDS_CONTRACTED = "SECURITY_GUARDS_CONTRACTED"
    EQUIPMENT_SERVICED_BY_TECHNICIAN = "EQUIPMENT_SERVICED_BY_TECHNICIAN"
    VOLATILE_SUBSTANCES_STORED_IN_EXTERNAL_CAGES = "VOLATILE_SUBSTANCES_STORED_IN_EXTERNAL_CAGES"
    PRODUCT_LABELS_COMPLY_WITH_REGULATIONS = "PRODUCT_LABELS_COMPLY_WITH_REGULATIONS"
    HAS_TRACK_AND_TRACE = "HAS_TRACK_AND_TRACE"
    CANNABIS_DISTRIBUTION_RIDESHARE = "CANNABIS_DISTRIBUTION_RIDESHARE"
    CANNABIS_DISTRIBUTION_UNMARKED_VEHICLE = "CANNABIS_DISTRIBUTION_UNMARKED_VEHICLE"
    CANNABIS_DISTRIBUTION_CAMERAS_ON_VEHICLE = "CANNABIS_DISTRIBUTION_CAMERAS_ON_VEHICLE"
    CANNABIS_DISTRIBUTION_TAKE_HOME_MONEY_INVENTORY = "CANNABIS_DISTRIBUTION_TAKE_HOME_MONEY_INVENTORY"
    CANNABIS_DISTRIBUTION_PERSONAL_STOPS = "CANNABIS_DISTRIBUTION_PERSONAL_STOPS"
    BURGLAR_ALARM_ARMED_NON_BUSINESS_HOURS = "BURGLAR_ALARM_ARMED_NON_BUSINESS_HOURS"
    HAS_CRA_APPROVED_SAFE = "HAS_CRA_APPROVED_SAFE"
    BURGLAR_ALARM_ALL_DOORS_AND_WINDOWS_CONNECTED = "BURGLAR_ALARM_ALL_DOORS_AND_WINDOWS_CONNECTED"
    HAS_VAULT_OR_DEA_CAGE = "HAS_VAULT_OR_DEA_CAGE"
    CARRY_WORKERS_COMP = "CARRY_WORKERS_COMP"
    GC_ADDITIONAL_INSURED_ENDORSEMENT_COMPLETED_OPERATIONS = "GC_ADDITIONAL_INSURED_ENDORSEMENT_COMPLETED_OPERATIONS"
    GC_NAMED_AS_ADDITIONAL_INSURED = "GC_NAMED_AS_ADDITIONAL_INSURED"
    GC_SUBCONTRACTOR_INDEMNIFY_AGREEMENT = "GC_SUBCONTRACTOR_INDEMNIFY_AGREEMENT"
    GC_SAME_SUBCONTRACTORS = "GC_SAME_SUBCONTRACTORS"
    GC_SELL_CASUAL_LABOR = "GC_SELL_CASUAL_LABOR"
    GC_BENEFITS_LEASED_EMPLOYEES = "GC_BENEFITS_LEASED_EMPLOYEES"
    GC_OBTAIN_CERTIFICATES_OF_INSURANCE = "GC_OBTAIN_CERTIFICATES_OF_INSURANCE"
    GC_WRITTEN_CONTACTS = "GC_WRITTEN_CONTACTS"
    LICENSED_TECHNICIANS = "LICENSED_TECHNICIANS"
    HAS_HEALTH_CODE_VIOLATIONS = "HAS_HEALTH_CODE_VIOLATIONS"
    HAS_MOBILE_FOOD_LICENSE = "HAS_MOBILE_FOOD_LICENSE"
    MOBILE_FOOD_FIRE_PROTECTION = "MOBILE_FOOD_FIRE_PROTECTION"
    ANNUAL_BACKGROUND_CHECKS = "ANNUAL_BACKGROUND_CHECKS"
    DAYCARE_LOCATION_HOME_APARTMENT = "DAYCARE_LOCATION_HOME_APARTMENT"
    PROVIDE_MSDS = "PROVIDE_MSDS"
    INFLATABLE_DEVICES_SECURED = "INFLATABLE_DEVICES_SECURED"
    MANUFACTURERS_CHECKLIST_USED = "MANUFACTURERS_CHECKLIST_USED"
    RENTAL_AGREEMENT_REQUIRED = "RENTAL_AGREEMENT_REQUIRED"
    HAS_CHILDCARE_LICENSE = "HAS_CHILDCARE_LICENSE"
    RAILROAD_WORK = "RAILROAD_WORK"
    POWERLINE_WORK = "POWERLINE_WORK"
    REQUIRED_WAIVER_FROM_MEMBERS = "REQUIRED_WAIVER_FROM_MEMBERS"
    CUSTOMERS_WEAR_EYE_PROTECTION = "CUSTOMERS_WEAR_EYE_PROTECTION"
    COSMETICS_MANUFACTURING = "COSMETICS_MANUFACTURING"
    RACING_SPONSOR_OR_SERVICE = "RACING_SPONSOR_OR_SERVICE"
    FLAMMABLE_MATERIALS_STORED_SECURELY = "FLAMMABLE_MATERIALS_STORED_SECURELY"
    PAINTING_OPERATIONS_NFPA_COMPLIANT_BOOTH = "PAINTING_OPERATIONS_NFPA_COMPLIANT_BOOTH"
    DESIGNS_REVIEWED_BY_PROFESSIONAL = "DESIGNS_REVIEWED_BY_PROFESSIONAL"
    PRODUCTS_CHANGED_WITHOUT_CUSTOMER_APPROVAL = "PRODUCTS_CHANGED_WITHOUT_CUSTOMER_APPROVAL"
    PRODUCTS_MADE_TO_CUSTOMER_SPECIFICATIONS = "PRODUCTS_MADE_TO_CUSTOMER_SPECIFICATIONS"
    WORK_NFPA_COMPLIANT = "WORK_NFPA_COMPLIANT"
    DESIGN_WITHOUT_INSTALLATION = "DESIGN_WITHOUT_INSTALLATION"
    MOBILE_EQUIPMENT_WORK = "MOBILE_EQUIPMENT_WORK"
    HAZARDOUS_FACILITY_WORK = "HAZARDOUS_FACILITY_WORK"
    GRAIN_BIN_CONSTRUCTION = "GRAIN_BIN_CONSTRUCTION"
    NOT_CHAIN_SUPERMARKET = "NOT_CHAIN_SUPERMARKET"
    HUD_HOUSING = "HUD_HOUSING"
    HAS_HUD_HOUSING = "HAS_HUD_HOUSING"
    HAS_STUDENT_HOUSING = "HAS_STUDENT_HOUSING"
    STUDENT_HOUSING = "STUDENT_HOUSING"
    MODULAR_STRUCTURES = "MODULAR_STRUCTURES"
    HAS_MODULAR_STRUCTURES = "HAS_MODULAR_STRUCTURES"
    ON_PREMISES_CRIME = "ON_PREMISES_CRIME"
    HAS_ADULT_ENTERTAINMENT = "HAS_ADULT_ENTERTAINMENT"
    HAS_BALCONY = "HAS_BALCONY"
    HAS_BOTTLE_SERVICE = "HAS_BOTTLE_SERVICE"
    HAS_BYOB = "HAS_BYOB"
    HAS_CATERING = "HAS_CATERING"
    HAS_DAMAGES = "HAS_DAMAGES"
    HAS_DANCING = "HAS_DANCING"
    HAS_DELIVERY = "HAS_DELIVERY"
    HAS_INFESTATION = "HAS_INFESTATION"
    HAS_OUTDOOR_SEATING = "HAS_OUTDOOR_SEATING"
    HAS_PROSTITUTION = "HAS_PROSTITUTION"
    PROSTITUTION_RISK = "PROSTITUTION_RISK"
    HAS_ROOF_ACCESS = "HAS_ROOF_ACCESS"
    HAS_SECURITY_GUARDS = "HAS_SECURITY_GUARDS"
    HAS_SWIMMING_POOL = "HAS_SWIMMING_POOL"
    HAS_WAITER_SERVICE = "HAS_WAITER_SERVICE"
    HAS_BASEMENT = "HAS_BASEMENT"
    HAS_FORMAL_HR_DEPARTMENT = "HAS_FORMAL_HR_DEPARTMENT"
    HAS_HIRING_GUIDELINES = "HAS_HIRING_GUIDELINES"
    HAS_EMPLOYEE_HANDBOOK = "HAS_EMPLOYEE_HANDBOOK"
    HAS_COMPANY_BYLAWS = "HAS_COMPANY_BYLAWS"
    COOKING_TYPES_GRILLING = "COOKING_TYPES_GRILLING"
    COOKING_TYPES_SMOKEHOUSE = "COOKING_TYPES_SMOKEHOUSE"
    COOKING_TYPES_SOLID_FUEL_COOKING = "COOKING_TYPES_SOLID_FUEL_COOKING"
    COOKING_TYPES_TABLE_SIDE_COOKING = "COOKING_TYPES_TABLE_SIDE_COOKING"
    COOKING_TYPES_BARBECUE = "COOKING_TYPES_BARBECUE"
    COOKING_TYPES_ROASTING = "COOKING_TYPES_ROASTING"
    COOKING_TYPES_DEEP_FRYERS = "COOKING_TYPES_DEEP_FRYERS"
    COOKING_TYPES_OPEN_BROILING = "COOKING_TYPES_OPEN_BROILING"
    COOKING_TYPES_BROILING = "COOKING_TYPES_BROILING"
    COOKING_TYPES_OVEN = "COOKING_TYPES_OVEN"
    COOKING_TYPES_OTHER = "COOKING_TYPES_OTHER"
    COOKING_EXTENT = "COOKING_EXTENT"
    PRICE_LEVEL = "PRICE_LEVEL"
    """ Price levels for the establishment eg $ / $$ / $$$ / $$$$"""
    IS_UL300_COMPLIANT = "IS_UL300_COMPLIANT"
    HAS_ANSUL_EQUIPMENT = "HAS_ANSUL_EQUIPMENT"
    ENTERTAINMENT_POOL_TABLE = "ENTERTAINMENT_POOL_TABLE"
    ENTERTAINMENT_DARTS = "ENTERTAINMENT_DARTS"
    ENTERTAINMENT_KARAOKE = "ENTERTAINMENT_KARAOKE"
    ENTERTAINMENT_JUKEBOX = "ENTERTAINMENT_JUKEBOX"
    ENTERTAINMENT_ARCADES = "ENTERTAINMENT_ARCADES"
    ENTERTAINMENT_ARCHERY = "ENTERTAINMENT_ARCHERY"
    ENTERTAINMENT_BOWLING = "ENTERTAINMENT_BOWLING"
    ENTERTAINMENT_AXE_THROWING = "ENTERTAINMENT_AXE_THROWING"
    ENTERTAINMENT_TV = "ENTERTAINMENT_TV"
    ENTERTAINMENT_FOOSBALL = "ENTERTAINMENT_FOOSBALL"
    ENTERTAINMENT_HOOKAH = "ENTERTAINMENT_HOOKAH"
    ENTERTAINMENT_MECHANICAL_BULL = "ENTERTAINMENT_MECHANICAL_BULL"
    ENTERTAINMENT_KENO = "ENTERTAINMENT_KENO"
    ENTERTAINMENT_OTHER = "ENTERTAINMENT_OTHER"
    PERFORMANCES_COMEDY = "PERFORMANCES_COMEDY"
    PERFORMANCES_DJ = "PERFORMANCES_DJ"
    PERFORMANCES_LIVE_MUSIC = "PERFORMANCES_LIVE_MUSIC"
    PARKING_VALET = "PARKING_VALET"
    PARKING_PRIVATE_LOT = "PARKING_PRIVATE_LOT"
    PARKING_STREET = "PARKING_STREET"
    HAS_SPA = "HAS_SPA"
    HAS_DAY_CARE = "HAS_DAY_CARE"
    HAS_FITNESS_CENTER = "HAS_FITNESS_CENTER"
    HAS_LAUNDRY = "HAS_LAUNDRY"
    HAS_PLAYGROUND = "HAS_PLAYGROUND"
    HAS_SAUNA = "HAS_SAUNA"
    HAS_SLIDE_OR_DIVING_BOARD = "HAS_SLIDE_OR_DIVING_BOARD"
    RESIDENTIAL_OWNERSHIP_UNITS_FOR_RENT = "RESIDENTIAL_OWNERSHIP_UNITS_FOR_RENT"
    RESIDENTIAL_OWNERSHIP_UNITS_FOR_SALE = "RESIDENTIAL_OWNERSHIP_UNITS_FOR_SALE"
    ARCHITECTURAL_SERVICES_CONTRACTOR = "ARCHITECTURAL_SERVICES_CONTRACTOR"
    COMMERCIAL_AND_INSTITUTIONAL_BUILDING_CONSTRUCTION_CONTRACTOR = (
        "COMMERCIAL_AND_INSTITUTIONAL_BUILDING_CONSTRUCTION_CONTRACTOR"
    )
    DRYWALL_AND_INSULATION_CONTRACTOR = "DRYWALL_AND_INSULATION_CONTRACTOR"
    ELECTRICAL_AND_OTHER_WIRING_INSTALLATION_CONTRACTOR = "ELECTRICAL_AND_OTHER_WIRING_INSTALLATION_CONTRACTOR"
    FINISH_CARPENTRY_CONTRACTOR = "FINISH_CARPENTRY_CONTRACTOR"
    FLOORING_CONTRACTOR = "FLOORING_CONTRACTOR"
    FRAMING_CONTRACTOR = "FRAMING_CONTRACTOR"
    GLASS_AND_GLAZING_CONTRACTOR = "GLASS_AND_GLAZING_CONTRACTOR"
    INDUSTRIAL_BUILDING_CONSTRUCTION_CONTRACTOR = "INDUSTRIAL_BUILDING_CONSTRUCTION_CONTRACTOR"
    ISO_FIRE_PROTECTION_CLASS = "ISO_FIRE_PROTECTION_CLASS"
    LANDSCAPE_ARCHITECTURAL_SERVICES_CONTRACTOR = "LANDSCAPE_ARCHITECTURAL_SERVICES_CONTRACTOR"
    LANDSCAPING_SERVICES_CONTRACTOR = "LANDSCAPING_SERVICES_CONTRACTOR"
    MASONRY_CONTRACTOR = "MASONRY_CONTRACTOR"
    OTHER_FOUNDATION_STRUCTURE_AND_BUILDING_EXTERIOR_CONTRACTOR = (
        "OTHER_FOUNDATION_STRUCTURE_AND_BUILDING_EXTERIOR_CONTRACTOR"
    )
    PAINTING_AND_WALL_COVERING_CONTRACTOR = "PAINTING_AND_WALL_COVERING_CONTRACTOR"
    PLUMBING_HEATING_AND_AIR_CONDITIONING_CONTRACTOR = "PLUMBING_HEATING_AND_AIR_CONDITIONING_CONTRACTOR"
    POURED_CONCRETE_FOUNDATION_AND_STRUCTURE_CONTRACTOR = "POURED_CONCRETE_FOUNDATION_AND_STRUCTURE_CONTRACTOR"
    RESIDENTIAL_BUILDING_CONSTRUCTION_CONTRACTOR = "RESIDENTIAL_BUILDING_CONSTRUCTION_CONTRACTOR"
    ROOFING_CONTRACTOR = "ROOFING_CONTRACTOR"
    SIDING_CONTRACTOR = "SIDING_CONTRACTOR"
    SITE_PREPARATION_CONTRACTOR = "SITE_PREPARATION_CONTRACTOR"
    STRUCTURAL_STEEL_AND_PRECAST_CONCRETE_CONTRACTOR = "STRUCTURAL_STEEL_AND_PRECAST_CONCRETE_CONTRACTOR"
    TILE_AND_TERRAZZO_CONTRACTOR = "TILE_AND_TERRAZZO_CONTRACTOR"
    ACTIVE_CONTRACTOR_LICENSE = "ACTIVE_CONTRACTOR_LICENSE"
    ALCOHOL_SERVED = "ALCOHOL_SERVED"
    ASBESTOS_REMOVAL = "ASBESTOS_REMOVAL"
    AVERAGE_SIZE_OF_JOBS = "AVERAGE_SIZE_OF_JOBS"
    BEARING_WALLS_REMOVAL = "BEARING_WALLS_REMOVAL"
    BOILER_WORK = "BOILER_WORK"
    BOOMS = "BOOMS"
    BURGLAR_ALARM = "BURGLAR_ALARM"
    HAS_BURGLAR_ALARM = "HAS_BURGLAR_ALARM"
    BURGLAR_ALARM_TYPE = "BURGLAR_ALARM_TYPE"
    CARPENTER = "CARPENTER"
    CRANES = "CRANES"
    DANGEROUS_OBJECTS_IN_FOOD = "DANGEROUS_OBJECTS_IN_FOOD"
    DAMAGE_RESTORATION = "DAMAGE_RESTORATION"
    DEMOLITION = "DEMOLITION"
    DIGGING = "DIGGING"
    DIRECTIONAL_BORING = "DIRECTIONAL_BORING"
    DRILLING = "DRILLING"
    DRIVING_EXPOSURE = "DRIVING_EXPOSURE"
    DRONE = "DRONE"
    DRYWALL = "DRYWALL"
    DRYWALL_SERVICE = "DRYWALL_SERVICE"
    DUST_COLLECTION_SYSTEM = "DUST_COLLECTION_SYSTEM"
    ELECTRIC = "ELECTRIC"
    ELEVATORS_ESCALATORS = "ELEVATORS_ESCALATORS"
    EXCAVATION = "EXCAVATION"
    EXTERIOR_INSULATION_FINISHING_SYSTEMS = "EXTERIOR_INSULATION_FINISHING_SYSTEMS"
    EXTERIOR_PLUMBING = "EXTERIOR_PLUMBING"
    EXTERNAL_FRAMING = "EXTERNAL_FRAMING"
    FIRE_ALARM = "FIRE_ALARM"
    HAS_FIRE_ALARM = "HAS_FIRE_ALARM"
    FIRE_ALARM_TYPE = "FIRE_ALARM_TYPE"
    FIREPROOFING = "FIREPROOFING"
    FORKLIFTS = "FORKLIFTS"
    GAS_MAINS = "GAS_MAINS"
    GAS_OIL_WORK = "GAS_OIL_WORK"
    HAZARDOUS_MATERIALS = "HAZARDOUS_MATERIALS"
    DANGEROUS_MATERIALS = "DANGEROUS_MATERIALS"
    HOISTS = "HOISTS"
    INDUSTRIAL_WORK = "INDUSTRIAL_WORK"
    LADDERS = "LADDERS"
    LEAD_ABATEMENT = "LEAD_ABATEMENT"
    MOLD_ABATEMENT = "MOLD_ABATEMENT"
    PAINTING = "PAINTING"
    PILING = "PILING"
    RADON_ABATEMENT = "RADON_ABATEMENT"
    VEHICLE_INFORMATION_NUMBER = "VEHICLE_INFORMATION_NUMBER"
    """VIN"""
    REMODELING_WORK = "REMODELING_WORK"
    RENOVATION_RESTORATION = "RENOVATION_RESTORATION"
    ROAD_OR_STREET_WORK = "ROAD_OR_STREET_WORK"
    ROOFING = "ROOFING"
    ROOFING_SERVICE = "ROOFING_SERVICE"
    SANDING = "SANDING"
    SCAFFOLDING = "SCAFFOLDING"
    SCISSOR_LIFTS = "SCISSOR_LIFTS"
    SOLAR = "SOLAR"
    SOLID_STATE_PLASTIC_ORGANIC_INSULATION = "SOLID_STATE_PLASTIC_ORGANIC_INSULATION"
    SPRAY_PAINTING = "SPRAY_PAINTING"
    SPRAYABLE_FOAM_INSULATION = "SPRAYABLE_FOAM_INSULATION"
    SUBCONTRACTOR = "SUBCONTRACTOR"
    TRAFFIC_SIGNAL_WORK = "TRAFFIC_SIGNAL_WORK"
    UNDERGROUND_CABLE_INSTALLATION = "UNDERGROUND_CABLE_INSTALLATION"
    UNDERGROUND_WORK = "UNDERGROUND_WORK"
    USED_AS_HOMELESS_SHELTER = "USED_AS_HOMELESS_SHELTER"
    WAXING_FLOORS = "WAXING_FLOORS"
    WELDING_WORK = "WELDING_WORK"
    WORK_IN_ALTITUDE = "WORK_IN_ALTITUDE"
    WRECKING = "WRECKING"
    CABINETRY = "CABINETRY"
    TILE_AND_STONE_WORK = "TILE_AND_STONE_WORK"
    CONCRETE_CONSTRUCTION = "CONCRETE_CONSTRUCTION"
    DECORATING = "DECORATING"
    DOOR_AND_WINDOW_INSTALLATION = "DOOR_AND_WINDOW_INSTALLATION"
    FENCE_CONSTRUCTION = "FENCE_CONSTRUCTION"
    HVAC_INSTALLATION_AND_SERVICE = "HVAC_INSTALLATION_AND_SERVICE"
    MASONRY = "MASONRY"
    PLUMBING = "PLUMBING"
    SHEET_METAL_WORK = "SHEET_METAL_WORK"
    RESIDENTIAL_SIDING_WORK = "RESIDENTIAL_SIDING_WORK"
    SIGN_INSTALLATION_AND_REPAIR = "SIGN_INSTALLATION_AND_REPAIR"
    SWIMMING_POOL_CONSTRUCTION = "SWIMMING_POOL_CONSTRUCTION"
    NEW_TOWNHOUSE_CONSTRUCTION = "NEW_TOWNHOUSE_CONSTRUCTION"
    NEW_CONDOMINIUM_CONSTRUCTION = "NEW_CONDOMINIUM_CONSTRUCTION"
    NEW_TRACT_CONSTRUCTION = "NEW_TRACT_CONSTRUCTION"
    FLOOD_RISK = "FLOOD_RISK"
    CATASTROPHIC_FLOOD_RISK = "CATASTROPHIC_FLOOD_RISK"
    FLOOD_ZONE = "FLOOD_ZONE"
    ELEVATION = "ELEVATION"
    LIGHTNING_RISK = "LIGHTNING_RISK"
    TORNADO_RISK = "TORNADO_RISK"
    COASTAL_STORM_SURGE_RISK = "COASTAL_STORM_SURGE_RISK"
    HAIL_RISK = "HAIL_RISK"
    EARTHQUAKE_RISK = "EARTHQUAKE_RISK"
    SINKHOLE_RISK = "SINKHOLE_RISK"
    SNOW_LOAD_RISK = "SNOW_LOAD_RISK"
    WILDFIRE_RISK = "WILDFIRE_RISK"
    HURRICANE_RISK = "HURRICANE_RISK"
    WIND_RISK = "WIND_RISK"
    FIRE_PROTECTION_GRADE = "FIRE_PROTECTION_GRADE"
    FIRE_PROTECTION_CLASS = "FIRE_PROTECTION_CLASS"
    OVERALL_CRIME_GRADE = "OVERALL_CRIME_GRADE"
    ASSAULT_GRADE = "ASSAULT_GRADE"
    LARCENY_GRADE = "LARCENY_GRADE"
    MURDER_GRADE = "MURDER_GRADE"
    ROBBERY_GRADE = "ROBBERY_GRADE"
    BURGLARY_GRADE = "BURGLARY_GRADE"
    VEHICLE_THEFT_GRADE = "VEHICLE_THEFT_GRADE"
    RAPE_GRADE = "RAPE_GRADE"
    DRUG_ALCOHOL_RELATED_DEATHS_GRADE = "DRUG_ALCOHOL_RELATED_DEATHS_GRADE"
    DISTANCE_TO_URGENT_CARE = "DISTANCE_TO_URGENT_CARE"
    DISTANCE_TO_COAST = "DISTANCE_TO_COAST"
    DISTANCE_TO_BODY_OF_WATER = "DISTANCE_TO_BODY_OF_WATER"
    DISTANCE_TO_FIRE_HYDRANT = "DISTANCE_TO_FIRE_HYDRANT"
    DISTANCE_TO_FIRE_STATION = "DISTANCE_TO_FIRE_STATION"
    DISTANCE_TO_POLICE_STATION = "DISTANCE_TO_POLICE_STATION"
    DISTANCE_TO_HOSPITAL = "DISTANCE_TO_HOSPITAL"
    DISTANCE_TO_TOXIC_FACILITY = "DISTANCE_TO_TOXIC_FACILITY"
    DISTANCE_TO_SINKHOLE = "DISTANCE_TO_SINKHOLE"
    DISTANCE_TO_CLOSEST_PREMISES_IN_SUBMISSION = "DISTANCE_TO_CLOSEST_PREMISES_IN_SUBMISSION"
    LAND_USE = "LAND_USE"
    ROOF_TYPE = "ROOF_TYPE"
    ROOF_COVER = "ROOF_COVER"
    EXTERIOR_WALL_FEATURES = "EXTERIOR_WALL_FEATURES"
    OWNER = "OWNER"
    YEAR_BUILT = "YEAR_BUILT"
    BUILDING_CONDITION = "BUILDING_CONDITION"
    BUILDING_AGE = "BUILDING_AGE"
    BUILDING_SIZE = "BUILDING_SIZE"
    SUBPREMISES_IDENTIFIER = "SUBPREMISES_IDENTIFIER"
    """
    Identifier of parts of the premises.
    In most cases is Address Line 2.
        In few cases it parts may be included in address line 1
        eg AL1: 124B Main St can be seen as AL1: 123 Man St and AL2/Subpremises Id: Building B
    In most cases SUBPREMISES means Structures:
        - eg Building (latter) - `BLDG C`
        - Permanent equipment name - `Water Pomp`
        - Specific constructs - `HANGER 12`, `PIER 219`, `LOT 22B`
    But in some cases it may refer to parts of single building premises eg:
        - `North Wing`
        - `UNIT 12`
        - `APT 525`
    https://developers.google.com/maps/documentation/geocoding/requests-geocoding#Types see subpremise
    """

    PROPERTY_TYPE = "PROPERTY_TYPE"
    PROPERTY_DESCRIPTION = "PROPERTY_DESCRIPTION"
    FOUNDATION_TYPE = "FOUNDATION_TYPE"
    WEBSITE = "WEBSITE"
    HOURS_OF_OPERATION = "HOURS_OF_OPERATION"
    BUSINESS_CATEGORIES = "BUSINESS_CATEGORIES"
    TAGS = "TAGS"
    OSHA_VIOLATION_COUNT = "OSHA_VIOLATION_COUNT"
    PRODUCT_RECALL_COUNT = "PRODUCT_RECALL_COUNT"
    FDA_WARNING_LETTER_COUNT = "FDA_WARNING_LETTER_COUNT"
    BUSINESS_REPUTATION = "BUSINESS_REPUTATION"
    OCCUPATION_PERCENT = "OCCUPATION_PERCENT"
    SUBSIDIZED_PERCENT = "SUBSIDIZED_PERCENT"
    ANNUAL_TURNOVER_PERCENT = "ANNUAL_TURNOVER_PERCENT"
    US_BASED_OPERATIONS_PERCENT = "US_BASED_OPERATIONS_PERCENT"
    WIRING = "WIRING"
    HAS_FIFTY_PERCENT_LICENSEE_PARTICIPATION = "HAS_FIFTY_PERCENT_LICENSEE_PARTICIPATION"
    HAS_FIFTEEN_PERCENT_PROFESSIONAL_LICENSEES = "HAS_FIFTEEN_PERCENT_PROFESSIONAL_LICENSEES"
    HAS_FORMALIZED_STAFF_TRAINING = "HAS_FORMALIZED_STAFF_TRAINING"
    CLIENT_EXCEEDS_APPLICANT_ANNUAL_INCOME_THRESHOLD = "CLIENT_EXCEEDS_APPLICANT_ANNUAL_INCOME_THRESHOLD"
    HAS_HOME_WARRANTY_PROGRAM = "HAS_HOME_WARRANTY_PROGRAM"
    APPLICANT_DISCLOSES_LEGAL_RELATIONSHIPS = "APPLICANT_DISCLOSES_LEGAL_RELATIONSHIPS"
    MANUAL_INCLUDES_COMPLIANCE_PROCEDURES = "MANUAL_INCLUDES_COMPLIANCE_PROCEDURES"
    HAS_ADR_PRESENT = "HAS_ADR_PRESENT"
    AVERAGE_PROPERTY_SALE_VALUE_PREVIOUS_YEAR = "AVERAGE_PROPERTY_SALE_VALUE_PREVIOUS_YEAR"
    DUAL_AGENCY_PERCENTAGE_PREVIOUS_YEAR = "DUAL_AGENCY_PERCENTAGE_PREVIOUS_YEAR"
    OVERALL_REO_TRANSACTION_PERCENT = "OVERALL_REO_TRANSACTION_PERCENT"
    OVERALL_SHORT_SALES_TRANSACTION_PERCENT = "OVERALL_SHORT_SALES_TRANSACTION_PERCENT"
    ISO_LIQUOR_LIABILITY_HAZARD_GRADE = "ISO_LIQUOR_LIABILITY_HAZARD_GRADE"
    HAS_SPRINKLERS = "HAS_SPRINKLERS"
    FENCE_BPP_LIMITS = "FENCE_BPP_LIMITS"
    SPECIAL_EQUIPMENT_LIMIT = "SPECIAL_EQUIPMENT_LIMIT"
    TOOL_VALUE = "TOOL_VALUE"
    TECHNICIANS = "TECHNICIANS"
    AR_LIMIT = "AR_LIMIT"
    VALUABLES_LIMIT = "VALUABLES_LIMIT"
    ED_HARDWARE_LIMIT = "ED_HARDWARE_LIMIT"
    ED_EXTRA_LIMIT = "ED_EXTRA_LIMIT"
    HAS_CHARGING_STATION_LIMIT = "HAS_CHARGING_STATION_LIMIT"
    SPECIAL_LIMIT = "SPECIAL_LIMIT"
    VACANT_LAND_SIZE = "VACANT_LAND_SIZE"
    IS_GARDEN_HABITATIONAL = "IS_GARDEN_HABITATIONAL"
    DUCTS_FIRE_RETARDANT = "DUCTS_FIRE_RETARDANT"
    GAS_SAFETY_SHUTOFF_MARKED = "GAS_SAFETY_SHUTOFF_MARKED"
    FROZEN_PIPE_RISK = "FROZEN_PIPE_RISK"
    LANDSLIDE_RISK = "LANDSLIDE_RISK"
    HEATING_TYPE = "HEATING_TYPE"
    EXTERIOR_WALL_SURFACE_TYPE = "EXTERIOR_WALL_SURFACE_TYPE"

    OPEN_STATUS_IS_OPEN = "OPEN_STATUS_IS_OPEN"
    "One of label option of OPEN_STATUS"
    OPEN_STATUS_IS_CLOSED = "OPEN_STATUS_IS_CLOSED"
    "One of label options of OPEN_STATUS"
    OPEN_STATUS_IS_TEMPORARILY_CLOSED = "OPEN_STATUS_IS_TEMPORARILY_CLOSED"
    "One of label options of OPEN_STATUS"

    ORDERS_MVRS = "ORDERS_MVRS"
    ANNUAL_HARVESTS = "ANNUAL_HARVESTS"
    BACKUP_POWER_SUPPLY = "BACKUP_POWER_SUPPLY"
    CANNABIS_TESTING = "CANNABIS_TESTING"
    FELONY_CONVICTION = "FELONY_CONVICTION"
    INVENTORY_VALUE = "INVENTORY_VALUE"
    GROW_FACILITY_LICENSED_ELECTRICIAN = "GROW_FACILITY_LICENSED_ELECTRICIAN"
    GROW_FACILITY_LICENSED_INSPECTION = "GROW_FACILITY_LICENSED_INSPECTION"
    MANUFACTURE_COMPLETED_PRODUCT = "MANUFACTURE_COMPLETED_PRODUCT"
    THC_MEDICINAL = "THC_MEDICINAL"
    THC_RECREATIONAL = "THC_RECREATIONAL"
    TRANSPORTS_CANNABIS = "TRANSPORTS_CANNABIS"
    VENDOR_MODIFIES_PRODUCT = "VENDOR_MODIFIES_PRODUCT"
    WEAPONS_IN_VEHICLES = "WEAPONS_IN_VEHICLES"

    # URL FactSubtypes
    GOOGLE_PROFILE_URL = "GOOGLE_PROFILE_URL"
    FACEBOOK_PROFILE_URL = "FACEBOOK_PROFILE_URL"
    APARTMENTS_COM_PROFILE_URL = "APARTMENTS_COM_PROFILE_URL"
    # end of URL FactSubtypes

    # Acord FactSubtypes
    GL_CODE = "GL_CODE"
    SIC = "SIC"
    IN_CITY_LIMITS = "IN_CITY_LIMITS"
    PREMISES_INTEREST_TYPE = "PREMISES_INTEREST_TYPE"
    OCCUPIED_AREA = "OCCUPIED_AREA"
    OPEN_TO_PUBLIC_AREA = "OPEN_TO_PUBLIC_AREA"
    LEASED_AREA = "LEASED_AREA"
    ON_PREMISE_INSTALLATION_SALES_PERCENT = "ON_PREMISE_INSTALLATION_SALES_PERCENT"
    OFF_PREMISE_INSTALLATION_SALES_PERCENT = "OFF_PREMISE_INSTALLATION_SALES_PERCENT"
    IS_SUBSIDIARY = "IS_SUBSIDIARY"
    OWNED_BY_PARENT_PERCENT = "OWNED_BY_PARENT_PERCENT"
    HAS_SUBSIDIARIES = "HAS_SUBSIDIARIES"
    OWNER_OF_SUBSIDIARY_PERCENT = "OWNER_OF_SUBSIDIARY_PERCENT"
    HAS_SAFETY_MANUAL = "HAS_SAFETY_MANUAL"
    HAS_SAFETY_POSITION = "HAS_SAFETY_POSITION"
    HAS_MONTHLY_SAFETY_MEETINGS = "HAS_MONTHLY_SAFETY_MEETINGS"
    HAS_OSHA_SAFETY_PROGRAM = "HAS_OSHA_SAFETY_PROGRAM"
    POLICY_OR_COVERAGE_DECLINED_CANCELED_PAST_THREE_YEARS = "POLICY_OR_COVERAGE_DECLINED_CANCELED_PAST_THREE_YEARS"
    REASON_FOR_DECLINE_CANCELLATION_NON_RENEWAL = "REASON_FOR_DECLINE_CANCELLATION_NON_RENEWAL"
    HAS_PAST_LOSSES_FOR_ABUSE_MOLESTATION_DISCRIMINATION = "HAS_PAST_LOSSES_FOR_ABUSE_MOLESTATION_DISCRIMINATION"
    HAS_SAFETY_CODE_VIOLATIONS = "HAS_SAFETY_CODE_VIOLATIONS"
    HAS_HAD_FORECLOSURE_OR_BANKRUPTCY_PAST_FIVE_YEARS = "HAS_HAD_FORECLOSURE_OR_BANKRUPTCY_PAST_FIVE_YEARS"
    HAS_HAD_JUDGEMENT_OR_LIEN_PAST_FIVE_YEARS = "HAS_HAD_JUDGEMENT_OR_LIEN_PAST_FIVE_YEARS"
    HAS_BEEN_PLACED_IN_A_TRUST = "HAS_BEEN_PLACED_IN_A_TRUST"
    BUSINESS_TRUST = "BUSINESS_TRUST"
    HAS_FOREIGN_OPERATIONS_PRODUCTS_OR_SALES = "HAS_FOREIGN_OPERATIONS_PRODUCTS_OR_SALES"
    HAS_OTHER_BUSINESSES_NOT_REQUESTING_COVERAGE = "HAS_OTHER_BUSINESSES_NOT_REQUESTING_COVERAGE"
    OTHER_BUSINESSES_NOT_REQUESTING_COVERAGE = "OTHER_BUSINESSES_NOT_REQUESTING_COVERAGE"
    BUSINESS_HIRES_DRONES = "BUSINESS_HIRES_DRONES"
    HAS_DRAW_PLANS = "HAS_DRAW_PLANS"
    SUBCONTRACTOR_COVERAGES_OR_LIMITS_LESS_THAN_NAMED_INSURED = (
        "SUBCONTRACTOR_COVERAGES_OR_LIMITS_LESS_THAN_NAMED_INSURED"
    )
    SUBCONTRACTORS_WITHOUT_INSURANCE = "SUBCONTRACTORS_WITHOUT_INSURANCE"
    LEASES_EQUIPMENT = "LEASES_EQUIPMENT"
    PRODUCTS_OR_COMPLETED_OPERATIONS = "PRODUCTS_OR_COMPLETED_OPERATIONS"
    # Prometrix fact subtypes
    PROMETRIX_RISK_ID = "PROMETRIX_RISK_ID"
    SPRINKLER_SCORE = "SPRINKLER_SCORE"
    FIRE_PPC_CODE = "FIRE_PPC_CODE"
    SPRINKLER_CREDIT_LEVEL = "SPRINKLER_CREDIT_LEVEL"
    SPRINKLER_COVERED_AREA = "SPRINKLER_COVERED_AREA"
    # End Prometrix fact subtypes

    # Riskmeter fact subtypes
    WILDFIRE_RISK_SCORE = "WILDFIRE_RISK_SCORE"
    DISTANCE_TO_HIGH_WILDFIRE_RISK_ZONE = "DISTANCE_TO_HIGH_WILDFIRE_RISK_ZONE"
    CRIME_RISK_SCORE = "CRIME_RISK_SCORE"
    PROPERTY_CRIME_RISK_SCORE = "PROPERTY_CRIME_RISK_SCORE"
    PERSON_CRIME_RISK_SCORE = "PERSON_CRIME_RISK_SCORE"
    # End Riskmeter fact subtypes

    # Kalepa Risk subtypes
    RISK_OF_EARTHQUAKE = "RISK_OF_EARTHQUAKE"
    RISK_OF_FIRE = "RISK_OF_FIRE"
    RISK_OF_HAIL = "RISK_OF_HAIL"
    RISK_OF_HURRICANE_TYPHOON = "RISK_OF_HURRICANE_TYPHOON"
    RISK_OF_HIGH_WIND = "RISK_OF_HIGH_WIND"
    RISK_OF_TORNADO = "RISK_OF_TORNADO"
    RISK_OF_STRONG_WINDS = "RISK_OF_STRONG_WINDS"
    RISK_OF_FLOOD = "RISK_OF_FLOOD"
    # End Kalepa Risk subtypes

    # Product Parent Type
    PRODUCT_NAME = "PRODUCT_NAME"
    PRODUCT_ANNUAL_GROSS_SALES = "PRODUCT_ANNUAL_GROSS_SALES"
    PRODUCT_NUMBER_OF_UNITS = "PRODUCT_NUMBER_OF_UNITS"
    PRODUCT_TIME_IN_MARKET = "PRODUCT_TIME_IN_MARKET"
    PRODUCT_EXPECTED_LIFE = "PRODUCT_EXPECTED_LIFE"
    PRODUCT_INTENDED_USE = "PRODUCT_INTENDED_USE"
    PRODUCT_PRINCIPAL_COMPONENTS = "PRODUCT_PRINCIPAL_COMPONENTS"
    # End Product Parent Type
    INSTALLS_SERVICES_OR_DEMONSTRATE_PRODUCTS = "INSTALLS_SERVICES_OR_DEMONSTRATE_PRODUCTS"
    HAS_FOREIGN_PRODUCTS_AS_COMPONENTS = "HAS_FOREIGN_PRODUCTS_AS_COMPONENTS"
    HAS_RESEARCH_AND_DEVELOPMENT = "HAS_RESEARCH_AND_DEVELOPMENT"
    HAS_WARRANTIES_OR_GUARANTEES = "HAS_WARRANTIES_OR_GUARANTEES"
    HAS_PRODUCTS_FOR_AIRCRAFT_OR_SPACE = "HAS_PRODUCTS_FOR_AIRCRAFT_OR_SPACE"
    HAS_PRODUCT_RECALLS = "HAS_PRODUCT_RECALLS"
    REPACKAGES_PRODUCTS_FROM_OTHERS = "REPACKAGES_PRODUCTS_FROM_OTHERS"
    HAS_PRODUCTS_UNDER_LABEL_FROM_OTHERS = "HAS_PRODUCTS_UNDER_LABEL_FROM_OTHERS"
    VENDORS_COVERAGE_REQUIRED = "VENDORS_COVERAGE_REQUIRED"
    ANY_NAMED_INSURED_SELLS_TO_OTHER_NAMED_INSURED = "ANY_NAMED_INSURED_SELLS_TO_OTHER_NAMED_INSURED"
    MEDICAL_FACILITIES_OR_PROFESSIONALS = "MEDICAL_FACILITIES_OR_PROFESSIONALS"
    HAS_SOLD_ACQUIRED_OR_DISCONTINUED_OPERATIONS_IN_LAST_FIVE_YEARS = (
        "HAS_SOLD_ACQUIRED_OR_DISCONTINUED_OPERATIONS_IN_LAST_FIVE_YEARS"
    )
    LOANS_OR_RENTS_MACHINERY = "LOANS_OR_RENTS_MACHINERY"
    HAS_WATERCRAFT_OR_DOCKS = "HAS_WATERCRAFT_OR_DOCKS"
    PARKING_FEES = "PARKING_FEES"
    HAS_LODGING_OPERATIONS = "HAS_LODGING_OPERATIONS"
    LODGING_OPERATIONS_AREA = "LODGING_OPERATIONS_AREA"
    SPONSORS_SOCIAL_EVENTS = "SPONSORS_SOCIAL_EVENTS"
    SPONSORS_ATHLETIC_TEAMS = "SPONSORS_ATHLETIC_TEAMS"
    CONTEMPLATING_STRUCTURAL_ALTERATIONS = "CONTEMPLATING_STRUCTURAL_ALTERATIONS"
    CONTEMPLATING_DEMOLITION = "CONTEMPLATING_DEMOLITION"
    ACTIVE_IN_JOINT_VENTURES = "ACTIVE_IN_JOINT_VENTURES"
    LEASES_EMPLOYEES = "LEASES_EMPLOYEES"
    LABOR_INTERCHANGE = "LABOR_INTERCHANGE"
    HAS_SAFETY_POLICY = "HAS_SAFETY_POLICY"
    PROMOTES_PREMISES_SAFETY = "PROMOTES_PREMISES_SAFETY"
    FIRE_DISTRICT = "FIRE_DISTRICT"
    FIRE_DISTRICT_CODE_NUMBER = "FIRE_DISTRICT_CODE_NUMBER"
    NUMBER_OF_BASEMENTS = "NUMBER_OF_BASEMENTS"
    HAS_EMERGENCY_OR_EVACUATION_PLAN = "HAS_EMERGENCY_OR_EVACUATION_PLAN"
    BUILDING_IMPROVEMENTS_WIRING = "BUILDING_IMPROVEMENTS_WIRING"
    BUILDING_IMPROVEMENTS_WIRING_YEAR = "BUILDING_IMPROVEMENTS_WIRING_YEAR"
    BUILDING_IMPROVEMENTS_ROOFING = "BUILDING_IMPROVEMENTS_ROOFING"
    BUILDING_IMPROVEMENTS_ROOFING_YEAR = "BUILDING_IMPROVEMENTS_ROOFING_YEAR"
    BUILDING_IMPROVEMENTS_PLUMBING = "BUILDING_IMPROVEMENTS_PLUMBING"
    BUILDING_IMPROVEMENTS_PLUMBING_YEAR = "BUILDING_IMPROVEMENTS_PLUMBING_YEAR"
    BUILDING_IMPROVEMENTS_HEATING = "BUILDING_IMPROVEMENTS_HEATING"
    BUILDING_IMPROVEMENTS_HEATING_YEAR = "BUILDING_IMPROVEMENTS_HEATING_YEAR"
    MOST_RECENT_RENOVATION_WORK_YEAR = "MOST_RECENT_RENOVATION_WORK_YEAR"
    BURGLAR_ALARM_CERTIFICATE_NUMBER = "BURGLAR_ALARM_CERTIFICATE_NUMBER"
    BURGLAR_ALARM_EXPIRATION_DATE = "BURGLAR_ALARM_EXPIRATION_DATE"
    BURGLAR_ALARM_SERVICED_BY = "BURGLAR_ALARM_SERVICED_BY"
    BURGLAR_ALARM_EXTENT = "BURGLAR_ALARM_EXTENT"
    BURGLAR_ALARM_GRADE = "BURGLAR_ALARM_GRADE"
    NUMBER_OF_GUARDS = "NUMBER_OF_GUARDS"
    FIRE_PROTECTION_TYPE = "FIRE_PROTECTION_TYPE"
    SPRINKLER_AREA_COVERAGE_PERCENT = "SPRINKLER_AREA_COVERAGE_PERCENT"
    FIRE_ALARM_MANUFACTURER = "FIRE_ALARM_MANUFACTURER"
    FOREIGN_TOTAL_SALES = "FOREIGN_TOTAL_SALES"
    DOMESTIC_TOTAL_SALES = "DOMESTIC_TOTAL_SALES"
    MEDIA_USED = "MEDIA_USED"
    ANNUAL_MEDIA_COST = "ANNUAL_MEDIA_COST"
    USES_ADVERTISING_AGENCY = "USES_ADVERTISING_AGENCY"
    COVERAGE_FROM_ADVERTISING_AGENCY = "COVERAGE_FROM_ADVERTISING_AGENCY"
    AIRCRAFT_EXPOSURE = "AIRCRAFT_EXPOSURE"
    HAS_VEHICLES_NOT_INSURED_BY_UNDERLYING_POLICIES = "HAS_VEHICLES_NOT_INSURED_BY_UNDERLYING_POLICIES"
    RENTS_VEHICLES_TO_OTHERS = "RENTS_VEHICLES_TO_OTHERS"
    HAS_COVERAGE_FOR_HIRED_AND_NON_OWNED_VEHICLES = "HAS_COVERAGE_FOR_HIRED_AND_NON_OWNED_VEHICLES"
    CONTRACTOR_AGREEMENT_DESCRIPTION = "CONTRACTOR_AGREEMENT_DESCRIPTION"
    IS_SELF_INSURED = "IS_SELF_INSURED"
    HAS_HOSPITAL = "HAS_HOSPITAL"
    HAS_COVERAGE_FOR_DOCTORS_AND_NURSES = "HAS_COVERAGE_FOR_DOCTORS_AND_NURSES"
    NUMBER_OF_DOCTORS = "NUMBER_OF_DOCTORS"
    NUMBER_OF_NURSES = "NUMBER_OF_NURSES"
    NUMBER_OF_HOSPITAL_BEDS = "NUMBER_OF_HOSPITAL_BEDS"
    HAS_GL_COVERAGE_WITH_STANDARD_ISO_POLLUTION_EXCLUSION = "HAS_GL_COVERAGE_WITH_STANDARD_ISO_POLLUTION_EXCLUSION"
    HAS_GL_COVERAGE_WITH_STANDARD_SUDDEN_AND_ACCIDENTAL_ONLY = (
        "HAS_GL_COVERAGE_WITH_STANDARD_SUDDEN_AND_ACCIDENTAL_ONLY"
    )
    HAS_GL_COVERAGE_POLLUTION_COVERAGE_ENDORSEMENT = "HAS_GL_COVERAGE_POLLUTION_COVERAGE_ENDORSEMENT"
    HAS_SEPARATE_POLLUTION_COVERAGE = "HAS_SEPARATE_POLLUTION_COVERAGE"
    HAS_FOREIGN_PRODUCTS_OR_OPERATIONS = "HAS_FOREIGN_PRODUCTS_OR_OPERATIONS"
    HAS_RECENT_PRODUCT_LIABILITY_LOSS = "HAS_RECENT_PRODUCT_LIABILITY_LOSS"
    INDEPENDENT_CONTRACTORS_DESCRIPTION = "INDEPENDENT_CONTRACTORS_DESCRIPTION"
    NUMBER_OF_WATERCRAFT = "NUMBER_OF_WATERCRAFT"
    LOCATION_NUMBER = "LOCATION_NUMBER"
    NUMBER_OF_LOCATIONS = "NUMBER_OF_LOCATIONS"
    NUMBER_OF_FOREIGN_LOCATIONS = "NUMBER_OF_FOREIGN_LOCATIONS"
    OWN_OR_OPERATE_AIRCRAFT_OR_WATERCRAFT = "OWN_OR_OPERATE_AIRCRAFT_OR_WATERCRAFT"
    WORK_PERFORMED_UNDERGROUND_OR_ABOVE_15_FEET = "WORK_PERFORMED_UNDERGROUND_OR_ABOVE_15_FEET"
    WORK_ON_BARGES_VESSELS_DOCKS_BRIDGES_OVER_WATER = "WORK_ON_BARGES_VESSELS_DOCKS_BRIDGES_OVER_WATER"
    ENGAGED_IN_ANY_OTHER_TYPE_OF_BUSINESS = "ENGAGED_IN_ANY_OTHER_TYPE_OF_BUSINESS"
    ANY_WORK_SUBLET_WITHOUT_CERTIFICATES_OF_INSURANCE = "ANY_WORK_SUBLET_WITHOUT_CERTIFICATES_OF_INSURANCE"
    GROUP_TRANSPORTATION_PROVIDED = "GROUP_TRANSPORTATION_PROVIDED"
    EMPLOYEES_UNDER_16_OR_OVER_60 = "EMPLOYEES_UNDER_16_OR_OVER_60"
    ANY_SEASONAL_EMPLOYEES = "ANY_SEASONAL_EMPLOYEES"
    VOLUNTEER_OR_DONATED_LABOR = "VOLUNTEER_OR_DONATED_LABOR"
    ANY_EMPLOYEES_WITH_PHYSICAL_HANDICAPS = "ANY_EMPLOYEES_WITH_PHYSICAL_HANDICAPS"
    DO_EMPLOYEES_TRAVEL_OUT_OF_STATE = "DO_EMPLOYEES_TRAVEL_OUT_OF_STATE"
    PHYSICALS_REQUIRED_AFTER_EMPLOYMENT_OFFERS = "PHYSICALS_REQUIRED_AFTER_EMPLOYMENT_OFFERS"
    ANY_OTHER_INSURANCE_WITH_THIS_INSURER = "ANY_OTHER_INSURANCE_WITH_THIS_INSURER"
    EMPLOYEE_HEALTH_PLANS_PROVIDED = "EMPLOYEE_HEALTH_PLANS_PROVIDED"
    EMPLOYEES_PERFORM_WORK_FOR_OTHER_BUSINESSES_OR_SUBSIDIARIES = (
        "EMPLOYEES_PERFORM_WORK_FOR_OTHER_BUSINESSES_OR_SUBSIDIARIES"
    )
    ANY_EMPLOYEES_PREDOMINANTLY_WORK_AT_HOME = "ANY_EMPLOYEES_PREDOMINANTLY_WORK_AT_HOME"
    ANY_UNDISPUTED_AND_UNPAID_WORKERS_COMPENSATION_DUE = "ANY_UNDISPUTED_AND_UNPAID_WORKERS_COMPENSATION_DUE"
    # End Acord FactSubtypes

    # First Party FactSubtypes
    BUSINESS_INCOME = "BUSINESS_INCOME"
    EMPLOYEES_COUNT = "EMPLOYEES_COUNT"
    FT_EMPLOYEES_COUNT = "FT_EMPLOYEES_COUNT"
    PT_EMPLOYEES_COUNT = "PT_EMPLOYEES_COUNT"
    PAYROLL = "PAYROLL"
    ZIP_CODE = "ZIP_CODE"
    BPP = "BPP"
    TENANTS_IMPROVEMENTS_AND_BETTERMENTS = "TENANTS_IMPROVEMENTS_AND_BETTERMENTS"
    EDP = "EDP"
    BI_EE = "BI_EE"
    TIV = "TIV"
    TIV_PER_SQ_FT = "TIV_PER_SQ_FT"
    SIGNS_TIV = "SIGNS_TIV"
    ADJUSTED_TIV = "ADJUSTED_TIV"
    ITV = "ITV"
    TOTAL_VALUE = "TOTAL_VALUE"
    RCP = "RCP"
    RPV = "RPV"
    FRS = "FRS"
    SPRINKLER = "SPRINKLER"
    SPRINKLER_COUNT = "SPRINKLER_COUNT"
    SPRINKLER_TYPE = "SPRINKLER_TYPE"
    SPRINKLER_COVERAGE = "SPRINKLER_COVERAGE"
    STRUCTURES_COUNT = "STRUCTURES_COUNT"
    ROOF_SHAPE = "ROOF_SHAPE"
    WIND_ZONE = "WIND_ZONE"
    CONSTRUCTION_CODE = "CONSTRUCTION_CODE"
    PARKING_SPACES_COUNT = "PARKING_SPACES_COUNT"
    PROPERTY_RIGHT_OF_USE = "PROPERTY_RIGHT_OF_USE"
    OPERATIONS = "OPERATIONS"
    BUILDING_TYPE = "BUILDING_TYPE"
    NUMBER_OF_STORIES = "NUMBER_OF_STORIES"
    NUMBER_OF_ROOMS = "NUMBER_OF_ROOMS"
    NUMBER_OF_SWIMMING_POOLS = "NUMBER_OF_SWIMMING_POOLS"
    POLICY_NUMBER = "POLICY_NUMBER"
    EMPLOYEE_COUNT = "EMPLOYEE_COUNT"
    SECURITY_CAMERAS = "SECURITY_CAMERAS"
    BUILDING_IMPROVEMENTS_VALUE = "BUILDING_IMPROVEMENTS_VALUE"
    CONTENT_VALUE = "CONTENT_VALUE"
    OCCUPANCY = "OCCUPANCY"
    OCCUPANCY_TYPE = "OCCUPANCY_TYPE"
    OCCUPANCY_CLASS = "OCCUPANCY_CLASS"
    OPERATION_TYPE = "OPERATION_TYPE"
    BUILDING_VALUE = "BUILDING_VALUE"
    BUILDING_VALUATION_METHOD = "BUILDING_VALUATION_METHOD"
    PROPERTY_VALUATION = "PROPERTY_VALUATION"
    PROPERTY_VALUATION_METHOD = "PROPERTY_VALUATION_METHOD"
    COST_PER_SQ_FT = "COST_PER_SQ_FT"
    HAS_SMOKE_DETECTOR = "HAS_SMOKE_DETECTOR"
    SMOKE_DETECTOR_TYPE = "SMOKE_DETECTOR_TYPE"
    FIRE_PROTECTION = "FIRE_PROTECTION"
    LENDER_NAME = "LENDER_NAME"
    LENDER_STATE = "LENDER_STATE"
    LENDER_CITY = "LENDER_CITY"
    LENDER_STREET = "LENDER_STREET"
    LENDER_ZIP_CODE = "LENDER_ZIP_CODE"
    LOSS_OF_RENT_INCOME = "LOSS_OF_RENT_INCOME"
    LOSS_OF_INCOME = "LOSS_OF_INCOME"
    GROSS_AREA = "GROSS_AREA"
    SIGN = "SIGN"
    SALES_ESTIMATE = "SALES_ESTIMATE"
    TOTAL_PROPERTY_VALUE = "TOTAL_PROPERTY_VALUE"
    LIQUOR_SALES = "LIQUOR_SALES"
    HOTEL_SALES = "HOTEL_SALES"
    DELIVERY_SALES = "DELIVERY_SALES"
    FOOD_SALES = "FOOD_SALES"
    TOTAL_SALES = "TOTAL_SALES"
    RENT_INCOME = "RENT_INCOME"
    MAX_PRICE = "MAX_PRICE"
    MIN_PRICE = "MIN_PRICE"
    HOTEL_CLASS = "HOTEL_CLASS"
    AVERAGE_RATING = "AVERAGE_RATING"
    HOTEL_AMENITIES = "HOTEL_AMENITIES"
    OPENED_AT = "OPENED_AT"
    CURRENTLY_OPEN = "CURRENTLY_OPEN"
    HAS_EXPOSURE_TO_FLAMMABLES = "HAS_EXPOSURE_TO_FLAMMABLES"
    BUILDZOOM_SCORE = "BUILDZOOM_SCORE"
    AVERAGE_PROJECT_PRICE = "AVERAGE_PROJECT_PRICE"
    PROJECT_TYPES = "PROJECT_TYPES"
    INSURANCE_INFORMATION = "INSURANCE_INFORMATION"
    LOT_SIZE = "LOT_SIZE"
    PROJECTS_PER_TYPE = "PROJECTS_PER_TYPE"
    PROJECTS_PER_YEAR = "PROJECTS_PER_YEAR"
    EMPLOYEE_ROLES = "EMPLOYEE_ROLES"
    HUD_UNITS_COUNT = "HUD_UNITS_COUNT"
    OTHER_VALUE_TIV = "OTHER_VALUE_TIV"
    WRITTEN_SAFETY_PROGRAM = "WRITTEN_SAFETY_PROGRAM"
    MANAGEMENT_EXPERIENCE = "MANAGEMENT_EXPERIENCE"
    HAS_TOWING_EQUIPMENT = "HAS_TOWING_EQUIPMENT"
    HAS_SOLAR = "HAS_SOLAR"
    FURNISHED_AUTO = "FURNISHED_AUTO"
    FURNISHED_AUTO_NON_EMPLOYEES = "FURNISHED_AUTO_NON_EMPLOYEES"
    OFAC_SANCTION_LIST = "OFAC_SANCTION_LIST"
    LATEST_UPDATE_DATE = "LATEST_UPDATE_DATE"
    RELATIVE_HAZARD_PERCENTILE = "RELATIVE_HAZARD_PERCENTILE"
    ON_SITE_SURVEY_DATE = "ON_SITE_SURVEY_DATE"
    CAP_INDEX = "CAP_INDEX"

    EMPLOYEES = "EMPLOYEES"
    """
    The FactSubtype: EMPLOYEES of FactTypeID: RELATIONSHIPS. Models relation between
    ParentType: BUSINESS and ParentType: PERSON, where the person is employee and the business is employer.
    BUSINESS id is observation parentId id and PERSONs' ids are remoteId of relationships records
    """
    # Facts describing the ParentType.PERSON in EMPLOYEES context
    PERSON_NAME = "PERSON_NAME"
    EMPLOYEE_POSITION = "EMPLOYEE_POSITION"
    EMPLOYEE_IS_DIRECTOR_OFFICER_REPRESENTATIVE = "EMPLOYEE_IS_DIRECTOR_OFFICER_REPRESENTATIVE"
    """If the person is a director, officer or board representative of the business"""
    EMPLOYEE_OWNERSHIP_PERCENT = "EMPLOYEE_OWNERSHIP_PERCENT"
    """The percentage of ownership of the person in the business (0-100). Directly or beneficially"""
    # Facts describing the ParentType.PERSON END

    # Equipment FactSubtypes
    EQUIPMENT = "EQUIPMENT"
    """
    The FactSubtype: EQUIPMENT of FactTypeID: RELATIONSHIPS.
    Models relation between ParentType: SUBMISSION and ParentType: EQUIPMENT, as Submission includes Equipment.
    """
    EQUIPMENT_SERIAL_NUMBER = "EQUIPMENT_SERIAL_NUMBER"
    EQUIPMENT_GARAGE = "EQUIPMENT_GARAGE"
    EQUIPMENT_MODEL = "EQUIPMENT_MODEL"
    EQUIPMENT_DESCRIPTION = "EQUIPMENT_DESCRIPTION"
    EQUIPMENT_MANUFACTURER = "EQUIPMENT_MANUFACTURER"
    EQUIPMENT_YEAR = "EQUIPMENT_YEAR"
    EQUIPMENT_LIMIT = "EQUIPMENT_LIMIT"
    EQUIPMENT_CATEGORY = "EQUIPMENT_CATEGORY"
    # End Equipment FactSubtypes

    """
    HOUSING UNITS: a house, apartment, group of rooms, or single room occupied or intended for occupancy as separate
    living quarters.
    http://www.hud.gov/
     """
    HUD_LOW_INCOME_HUD_UNITS_COUNT = "HUD_LOW_INCOME_HUD_UNITS_COUNT"
    """
    Low income housing units. See HUD_UNITS_COUNT
    """
    CLOSED_AT = "CLOSED_AT"
    HEALTH_INSPECTION_SCORE = "HEALTH_INSPECTION_SCORE"
    HEALTH_INSPECTION = "HEALTH_INSPECTION"
    NUMBER_OF_UNITS = "NUMBER_OF_UNITS"
    NUMBER_OF_COMMERCIAL_UNITS = "NUMBER_OF_COMMERCIAL_UNITS"
    ESTIMATED_SALE_VALUE = "ESTIMATED_SALE_VALUE"
    BUSINESS_SERVICES = "BUSINESS_SERVICES"
    LATEST_LICENSE_NUMBER = "LATEST_LICENSE_NUMBER"
    TYPICAL_JOB_COST = "TYPICAL_JOB_COST"
    HOUZZ_REVIEW_SCORE = "HOUZZ_REVIEW_SCORE"
    NUMBER_OF_PROJECTS = "NUMBER_OF_PROJECTS"
    YEAR_FOUNDED = "YEAR_FOUNDED"
    BUSINESS_PRODUCTS = "BUSINESS_PRODUCTS"
    CONSTRUCTION_CLASS = "CONSTRUCTION_CLASS"
    STRUCTURES = "STRUCTURES"
    GARAGE = "GARAGE"
    FNI_STATE = "FNI_STATE"
    MAILING_ADDRESSES = "MAILING_ADDRESSES"
    FEIN = "FEIN"
    """
    There are 2 FEI Numbers: Federal Employer Identification Number (FEIN) and FDA Establishment Identifier (FDA_FEI_NUMBER).
     The FEIN is Federal Employer Identification Number. A federal tax identification number, also known as a FEIN or
     an employer identification number (EIN), is issued to entities that do business in the United States. The FEIN is
     a unique nine-digit corporate ID number that works the same way a Social Security number does for individuals.
     https://www.businessnewsdaily.com/17-federal-employer-identification-number-criteria.html
    """
    EPA_REGISTRY_ID = "EPA_REGISTRY_ID"
    """
    Identifier of facilities in United States Environmental Protection Agency
    Eg https://echodata.epa.gov/echo/echo_rest_services.get_facility_info?p_frs=110064021053
    """
    DUNS = "DUNS"
    """
    The Dun & Bradstreet D-U-N-S Number is a unique nine-digit identifier for businesses.
    https://www.dnb.com/duns-number.html
    """
    # Manufacturing process
    MANUFACTURING_PROCESS_CHEMICAL_FINISHING = "MANUFACTURING_PROCESS_CHEMICAL_FINISHING"
    MANUFACTURING_PROCESS_METALLURGY = "MANUFACTURING_PROCESS_METALLURGY"
    MANUFACTURING_PROCESS_METALWORKING = "MANUFACTURING_PROCESS_METALWORKING"
    MANUFACTURING_PROCESS_METAL_FINISHING = "MANUFACTURING_PROCESS_METAL_FINISHING"
    MANUFACTURING_PROCESS_WOODWORKING = "MANUFACTURING_PROCESS_WOODWORKING"
    MANUFACTURING_PROCESS_TRADITIONAL_FOOD_PROCESSING = "MANUFACTURING_PROCESS_TRADITIONAL_FOOD_PROCESSING"
    MANUFACTURING_PROCESS_MODERN_FOOD_PROCESSING = "MANUFACTURING_PROCESS_MODERN_FOOD_PROCESSING"
    MANUFACTURING_PROCESS_COATING = "MANUFACTURING_PROCESS_COATING"
    MANUFACTURING_PROCESS_DIPPING = "MANUFACTURING_PROCESS_DIPPING"
    MANUFACTURING_PROCESS_ELECTROPLATING = "MANUFACTURING_PROCESS_ELECTROPLATING"
    MANUFACTURING_PROCESS_FOUNDRY_OPERATIONS = "MANUFACTURING_PROCESS_FOUNDRY_OPERATIONS"
    MANUFACTURING_PROCESS_OPEN_FLAMES = "MANUFACTURING_PROCESS_OPEN_FLAMES"
    MANUFACTURING_PROCESS_PAINT_APPLICATION = "MANUFACTURING_PROCESS_PAINT_APPLICATION"
    MANUFACTURING_PROCESS_SAWING = "MANUFACTURING_PROCESS_SAWING"
    MANUFACTURING_PROCESS_SPRAY_PAINT_APPLICATION = "MANUFACTURING_PROCESS_SPRAY_PAINT_APPLICATION"
    MANUFACTURING_PROCESS_USE_OF_CAUSTICTS_OR_CORROSIVES = "MANUFACTURING_PROCESS_USE_OF_CAUSTICTS_OR_CORROSIVES"
    MANUFACTURING_PROCESS_USE_OF_COMBUSTIBLE_MATERIALS = "MANUFACTURING_PROCESS_USE_OF_COMBUSTIBLE_MATERIALS"
    MANUFACTURING_PROCESS_USE_OF_FLAMMABLE_GLUE = "MANUFACTURING_PROCESS_USE_OF_FLAMMABLE_GLUE"
    MANUFACTURING_PROCESS_VARNISH = "MANUFACTURING_PROCESS_VARNISH"
    MANUFACTURING_PROCESS_WELDING = "MANUFACTURING_PROCESS_WELDING"
    # NAICS Level 1 Manufacturing Types
    FOOD_MANUFACTURING = "FOOD_MANUFACTURING"
    BEVERAGE_AND_TOBACCO_PRODUCT_MANUFACTURING = "BEVERAGE_AND_TOBACCO_PRODUCT_MANUFACTURING"
    TEXTILE_MILLS = "TEXTILE_MILLS"
    TEXTILE_PRODUCT_MILLS = "TEXTILE_PRODUCT_MILLS"
    APPAREL_MANUFACTURING = "APPAREL_MANUFACTURING"
    LEATHER_AND_ALLIED_PRODUCT_MANUFACTURING = "LEATHER_AND_ALLIED_PRODUCT_MANUFACTURING"
    WOOD_PRODUCT_MANIFACTURING = "WOOD_PRODUCT_MANIFACTURING"
    PAPER_MANUFACTURING = "PAPER_MANUFACTURING"
    PRINTING_AND_RELATED_SUPPORT_ACTIVITIES = "PRINTING_AND_RELATED_SUPPORT_ACTIVITIES"
    PETROLEU_AND_COAL_PRODUCTS_MANUFACTURING = "PETROLEU_AND_COAL_PRODUCTS_MANUFACTURING"
    CHEMICAL_MANUFACTURING = "CHEMICAL_MANUFACTURING"
    PLASTICS_AND_RUBBER_PRODUCTS_MANUFACTURING = "PLASTICS_AND_RUBBER_PRODUCTS_MANUFACTURING"
    NONMETALLIC_MINERAL_PRODUCT_MANUFACTURING = "NONMETALLIC_MINERAL_PRODUCT_MANUFACTURING"
    PRIMARY_METAL_MANUFACTURING = "PRIMARY_METAL_MANUFACTURING"
    FABRICATED_METAL_PRODUCT_MANUFACTURING = "FABRICATED_METAL_PRODUCT_MANUFACTURING"
    MACHINERY_MANUFACTURING = "MACHINERY_MANUFACTURING"
    COMPUTER_AND_ELECTRONIC_PRODUCT_MANUFACTURING = "COMPUTER_AND_ELECTRONIC_PRODUCT_MANUFACTURING"
    FURNITURE_AND_RELATED_PRODUCT_MANUFACTURING = "FURNITURE_AND_RELATED_PRODUCT_MANUFACTURING"
    ELECTRICAL_EQUIPMENT_APPLIANCE_AND_COMPONENT_MANUFACTURING = (
        "ELECTRICAL_EQUIPMENT_APPLIANCE_AND_COMPONENT_MANUFACTURING"
    )
    TRANSPORTATION_EQUIPMENT_MANUFACTURING = "TRANSPORTATION_EQUIPMENT_MANUFACTURING"
    MISCELLANEOUS_MANUFACTURING = "MISCELLANEOUS_MANUFACTURING"
    NAICS_CODES = "NAICS_CODES"
    USES_BIOHAZARD_MATERIALS = "USES_BIOHAZARD_MATERIALS"
    USES_RADIOACTIVE_MATERIALS = "USES_RADIOACTIVE_MATERIALS"
    USES_DANGEROUS_CHEMICALS = "USES_DANGEROUS_CHEMICALS"
    # Multi label subtypes
    COOKING_TYPES = "COOKING_TYPES"
    PARKING_TYPES = "PARKING_TYPES"
    ENTERTAINMENT_TYPES = "ENTERTAINMENT_TYPES"
    PERFORMANCE_TYPES = "PERFORMANCE_TYPES"
    CONTRACTOR_SERVICES = "CONTRACTOR_SERVICES"
    CONTRACTOR_PROJECT_TYPES = "CONTRACTOR_PROJECT_TYPES"
    MANUFACTURING_PROCESS_TYPES = "MANUFACTURING_PROCESS_TYPES"
    CONTRACTOR_TYPES = "CONTRACTOR_TYPES"
    HOTEL_SPECIAL_FACILITIES = "HOTEL_SPECIAL_FACILITIES"
    NAICS_LEVEL_1_MANUFACTURING_TYPES = "NAICS_LEVEL_1_MANUFACTURING_TYPES"
    MULTI_LABEL_ISO_GL_CODES = "MULTI_LABEL_ISO_GL_CODES"
    MULTI_LABEL_NAICS_CODES = "MULTI_LABEL_NAICS_CODES"
    RESIDENTIAL_OWNERSHIP_TYPES = "RESIDENTIAL_OWNERSHIP_TYPES"
    RELATED_URLS = "RELATED_URLS"
    TRANSPORTATION_DRIVER_FITNESS_VIOLATIONS = "TRANSPORTATION_DRIVER_FITNESS_VIOLATIONS"
    TRANSPORTATION_DRUGS_AND_ALCOHOL_VIOLATIONS = "TRANSPORTATION_DRUGS_AND_ALCOHOL_VIOLATIONS"
    TRANSPORTATION_HAZARDOUS_MATERIALS_VIOLATIONS = "TRANSPORTATION_HAZARDOUS_MATERIALS_VIOLATIONS"
    TRANSPORTATION_LEGAL_VIOLATIONS = "TRANSPORTATION_LEGAL_VIOLATIONS"
    TRANSPORTATION_SERIOUS_MAINTENANCE_VIOLATIONS = "TRANSPORTATION_SERIOUS_MAINTENANCE_VIOLATIONS"
    TRANSPORTATION_REPORTED_OPERATIONS_AND_PERMITS = "TRANSPORTATION_REPORTED_OPERATIONS_AND_PERMITS"
    TRANSPORTATION_PERFORMANCE_BENCHMARK_ISSUES = "TRANSPORTATION_PERFORMANCE_BENCHMARK_ISSUES"
    TRANSPORTATION_ACUTE_VIOLATIONS = "TRANSPORTATION_ACUTE_VIOLATIONS"
    TRANSPORTATION_SERIOUS_UNSAFE_DRIVING_VIOLATIONS = "TRANSPORTATION_SERIOUS_UNSAFE_DRIVING_VIOLATIONS"
    TRANSPORTATION_RISK_OF_TRANSPORT_WITHOUT_PERMISSION = "TRANSPORTATION_RISK_OF_TRANSPORT_WITHOUT_PERMISSION"

    OPEN_STATUS = "OPEN_STATUS"
    """
    Status of the business, whether it is open or closed.
    Multilabel type with labels of:
    OPEN_STATUs_IS_OPEN
    OPEN_STATUS_IS_CLOSED
    OPEN_STATUS_IS_TEMPORARILY_CLOSED
    """

    # Range subtypes
    TYPICAL_JOB_COST_RANGE = "TYPICAL_JOB_COST_RANGE"
    ANNUAL_SALES_RANGE = "ANNUAL_SALES_RANGE"
    NUMBER_OF_EMPLOYEES_RANGE = "NUMBER_OF_EMPLOYEES_RANGE"
    PREMISES_STRUCTURES = "PREMISES_STRUCTURES"
    # Transportation
    US_DOT = "US_DOT"
    USDOTS = "USDOTS"
    """
    List of US DOT numbers, which is a unique identifier when collecting and monitoring a company's safety information
    acquired during audits, compliance reviews, crash investigations, and inspections. However, there are cases when
    companies re-register and obtain multiple US DOT numbers or for big organisations different branches may have
    different US DOT numbers.
    """
    STATE_CARRIER_ID = "STATE_CARRIER_ID"
    VEHICLE_MILES_TRAVELED = "VEHICLE_MILES_TRAVELED"
    NUMBER_OF_VEHICLES = "NUMBER_OF_VEHICLES"
    NUMBER_OF_REGISTERED_DRIVERS = "NUMBER_OF_REGISTERED_DRIVERS"
    TRANSPORTATION_SAFETY_RATING = "TRANSPORTATION_SAFETY_RATING"
    OPERATION_CLASSIFICATION = "OPERATION_CLASSIFICATION"
    CARGO_CARRIED = "CARGO_CARRIED"
    CARRIER_OPERATION = "CARRIER_OPERATION"
    SAFER_CARRIER_OPERATION_STATUS = "SAFER_CARRIER_OPERATION_STATUS"
    CARRIER_OPERATION_STATUS = "CARRIER_OPERATION_STATUS"
    """
    examples:
    - active: https://safer.fmcsa.dot.gov/query.asp?searchtype=ANY&query_type=queryCarrierSnapshot&query_param=USDOT&query_string=104641
    - inactive: https://safer.fmcsa.dot.gov/query.asp?searchtype=ANY&query_type=queryCarrierSnapshot&query_param=USDOT&query_string=3288535
    - out of service: https://safer.fmcsa.dot.gov/query.asp?searchtype=ANY&query_type=queryCarrierSnapshot&query_param=USDOT&query_string=3028725#Inspections
    more may come in future
    """
    COMPLAINTS_COUNT = "COMPLAINTS_COUNT"
    IS_NEW_ENTRANT = "IS_NEW_ENTRANT"
    FMCSA_VIOLATION_COUNT = "FMCSA_VIOLATION_COUNT"
    FMCSA_ACTIVATION_DATE = "FMCSA_ACTIVATION_DATE"
    TRANSPORTATION_MILES_PER_DRIVER = "TRANSPORTATION_MILES_PER_DRIVER"
    DRIVERS = "DRIVERS"
    VEHICLES = "VEHICLES"
    """
    VEHICLES is a list of vehicles that are associated with the business or submission, since ENG-18039 should not
    include TRAILERS, which have its won subtype.
    """
    TRAILERS = "TRAILERS"
    """
    TRAILERS is a list of trailers that are associated with the business or submission.
    """
    MISSING_VEHICLES = "MISSING_VEHICLES"
    """
    Contains invalid or missing VINs from (usually form the submission's vehicles file) (default)
    """
    MISSING_TRAILERS = "MISSING_TRAILERS"
    """
    Contains invalid or missing VINs from (usually form the submission's vehicles file) that can be recognized as trailers
    """
    PERCENT_DRIVERS_UNDER_21 = "PERCENT_DRIVERS_UNDER_21"
    PERCENT_DRIVERS_OVER_70 = "PERCENT_DRIVERS_OVER_70"
    ## DRIVERS START
    DRIVER_NAME = "DRIVER_NAME"
    DRIVER_FIRST_NAME = "DRIVER_FIRST_NAME"
    DRIVER_MIDDLE_NAME = "DRIVER_MIDDLE_NAME"
    DRIVER_LAST_NAME = "DRIVER_LAST_NAME"
    DRIVER_LICENSE_NUMBER = "DRIVER_LICENSE_NUMBER"
    DRIVER_LICENSE_STATE = "DRIVER_LICENSE_STATE"
    DRIVER_DATE_OF_BIRTH = "DRIVER_DATE_OF_BIRTH"
    DRIVER_AGE = "DRIVER_AGE"
    DRIVER_YEARS_OF_EXPERIENCE = "DRIVER_YEARS_OF_EXPERIENCE"
    DRIVER_YEAR_STARTED_DRIVING = "DRIVER_YEAR_STARTED_DRIVING"
    DRIVER_DATE_OF_HIRE = "DRIVER_DATE_OF_HIRE"
    DRIVER_GENDER = "DRIVER_GENDER"
    ## DRIVERS END
    ## VEHICLES START
    VEHICLE_PLATE_NUMBER = "VEHICLE_PLATE_NUMBER"
    VEHICLE_PLATE_STATE = "VEHICLE_PLATE_STATE"
    VEHICLE_VALUE_CURRENT = "VEHICLE_VALUE_CURRENT"
    VEHICLE_VALUE_NEW = "VEHICLE_VALUE_NEW"
    VEHICLE_YEAR = "VEHICLE_YEAR"
    """
    Indicates the number of vehicles that have missing and/or invalid VINs from the submission's vehicles file.
    """

    ### DECODED_VIN https://www.govinfo.gov/content/pkg/CFR-2012-title49-vol6/xml/CFR-2012-title49-vol6-part565.xml
    UNKNOWN_VIN = "UNKNOWN_VIN"
    """Indicates VINs that may be unknown, can not be decoded or is invalid"""

    VEHICLE_MODEL = "VEHICLE_MODEL"
    VEHICLE_SERIES = "VEHICLE_SERIES"
    VEHICLE_TYPE = "VEHICLE_TYPE"
    """Also see NhtsaVehicleType for values from NHTSA"""
    VEHICLE_MODEL_YEAR = "VEHICLE_MODEL_YEAR"
    GVWR = "GVWR"
    """Also see NhtsaGvwrClass for values from NHTSA"""
    ENGINE_HP = "ENGINE_HP"
    NUMBER_OF_SEATS = "NUMBER_OF_SEATS"
    VEHICLE_BODY_CLASS = "VEHICLE_BODY_CLASS"
    """Also see NhtsaVehicleBodyClass for values from NHTSA"""

    FUEL_TYPE_PRIMARY = "FUEL_TYPE_PRIMARY"
    VEHICLE_NOTE = "VEHICLE_NOTE"
    VEHICLE_BASE_PRICE = "VEHICLE_BASE_PRICE"
    ELECTRIFICATION_LEVEL = "ELECTRIFICATION_LEVEL"

    HAS_ABS = "HAS_ABS"
    BLIND_SPOT_MONITORING = "BLIND_SPOT_MONITORING"
    LANE_DEPARTURE_WARNING = "LANE_DEPARTURE_WARNING"
    FORWARD_COLLISION_WARNING = "FORWARD_COLLISION_WARNING"
    LANE_KEEP_SYSTEM = "LANE_KEEP_SYSTEM"
    PEDESTRIAN_AUTOMATIC_EMERGENCY_BRAKING = "PEDESTRIAN_AUTOMATIC_EMERGENCY_BRAKING"
    CASH_FOR_CLUNKERS = "CASH_FOR_CLUNKERS"

    TRAILER_BODY_TYPE = "TRAILER_BODY_TYPE"
    TRAILER_TYPE = "TRAILER_TYPE"
    TRAILER_LENGTH = "TRAILER_LENGTH"
    OTHER_TRAILER_INFO = "OTHER_TRAILER_INFO"

    VEHICLE_MAKE = "VEHICLE_MAKE"
    VEHICLE_MANUFACTURER = "VEHICLE_MANUFACTURER"
    ENGINE_MANUFACTURER = "ENGINE_MANUFACTURER"
    ### END OF DECODED_VIN

    ISO_VEHICLE_RATING = "ISO_VEHICLE_RATING"
    """
    See https://corbit.informationproviders.com/Training/ISO_AutoClassifications.pdf?v=636462621119636535"
    Calculated based on GVWR fact subtype.
    also see IsoVehicleRating for values
    """

    ## VEHICLES END

    ## Derived from FMCSA data
    # TRANSPORTATION_DRIVER_FITNESS_VIOLATIONS
    DRIVER_OPERATING_WHILE_ILL_OR_FATIGUED = "DRIVER_OPERATING_WHILE_ILL_OR_FATIGUED"
    DRIVER_VIOLATED_HOURS_SERVICE_RULES = "DRIVER_VIOLATED_HOURS_SERVICE_RULES"
    DRIVER_FITNESS_VIOLATIONS = "DRIVER_FITNESS_VIOLATIONS"
    # TRANSPORTATION_LEGAL_VIOLATIONS
    DRIVER_OPERATING_WITHOUT_VALID_CDL = "DRIVER_OPERATING_WITHOUT_VALID_CDL"
    DRIVER_OPERATING_WITHOUT_VALID_ENDORSEMENTS = "DRIVER_OPERATING_WITHOUT_VALID_ENDORSEMENTS"
    DRIVER_OPERATING_WHILE_DISQUALIFIED = "DRIVER_OPERATING_WHILE_DISQUALIFIED"
    DRIVER_OPERATING_ON_FALSE_LOG = "DRIVER_OPERATING_ON_FALSE_LOG"
    DRIVER_OPERATING_ON_LEARNER_WITHOUT_VALID_LICENSE = "DRIVER_OPERATING_ON_LEARNER_WITHOUT_VALID_LICENSE"
    DRIVER_FRAUD_MEDICAL_CERTIFICATE = "DRIVER_FRAUD_MEDICAL_CERTIFICATE"
    DRIVER_OPERATING_WITH_MORE_THAN_ONE_LICENSE = "DRIVER_OPERATING_WITH_MORE_THAN_ONE_LICENSE"
    DRIVER_OPERATING_VEHICLE_OOS = "DRIVER_OPERATING_VEHICLE_OOS"
    DRIVER_INVALID_MEDICAL_CERTIFICATE = "DRIVER_INVALID_MEDICAL_CERTIFICATE"
    TRANSPORTATION_UNAUTHORIZED_PASSENGER_ON_BOARD = "TRANSPORTATION_UNAUTHORIZED_PASSENGER_ON_BOARD"
    TRANSPORTATION_EOBR_RELATED = "TRANSPORTATION_EOBR_RELATED"
    TRANSPORTATION_HOS_VIOLATIONS = "TRANSPORTATION_HOS_VIOLATIONS"
    DRIVER_LOW_QUALIFICATION = "DRIVER_LOW_QUALIFICATION"
    FLEET_OPERATION_STATES = "FLEET_OPERATION_STATES"
    # TRANSPORTATION_DRUGS_AND_ALCOHOL_VIOLATIONS
    DRIVER_ALCOHOL_POSSESSION = "DRIVER_ALCOHOL_POSSESSION"
    DRIVER_DRUGS_USAGE_OR_POSSESSION = "DRIVER_DRUGS_USAGE_OR_POSSESSION"
    DRIVER_ALCOHOL_REPEATED_USAGE = "DRIVER_ALCOHOL_REPEATED_USAGE"
    DRIVER_ALCOHOL_USAGE = "DRIVER_ALCOHOL_USAGE"
    TRANSPORTATION_ALCOHOL_DRUGS_VIOLATIONS = "TRANSPORTATION_ALCOHOL_DRUGS_VIOLATIONS"
    # TRANSPORTATION_HAZARDOUS_MATERIALS_VIOLATIONS
    TRANSPORTATION_HM_FIRE_HAZARD = "TRANSPORTATION_HM_FIRE_HAZARD"
    TRANSPORTATION_HM_SERIOUS_LOAD_SECUREMENT = "TRANSPORTATION_HM_SERIOUS_LOAD_SECUREMENT"
    TRANSPORTATION_HM_SERIOUS_PACKAGE_INTEGRITY_ISSUES = "TRANSPORTATION_HM_SERIOUS_PACKAGE_INTEGRITY_ISSUES"
    TRANSPORTATION_HM_SERIOUS_PACKAGE_TESTING_ISSUES = "TRANSPORTATION_HM_SERIOUS_PACKAGE_TESTING_ISSUES"
    TRANSPORTATION_HM_VIOLATIONS = "TRANSPORTATION_HM_VIOLATIONS"
    # TRANSPORTATION_REPORTED_OPERATIONS_AND_PERMITS
    TRANSPORTATION_TRANSPORTS_RADIOACTIVE = "TRANSPORTATION_TRANSPORTS_RADIOACTIVE"
    TRANSPORTATION_TRANSPORTS_HAZARDOUS_MATERIALS = "TRANSPORTATION_TRANSPORTS_HAZARDOUS_MATERIALS"
    TRANSPORTATION_TRANSPORTS_GOODS_FOR_OTHERS = "TRANSPORTATION_TRANSPORTS_GOODS_FOR_OTHERS"
    TRANSPORTATION_TRANSPORTS_PASSENGERS = "TRANSPORTATION_TRANSPORTS_PASSENGERS"
    TRANSPORTATION_TRANSPORTS_HOUSEHOLD_GOODS = "TRANSPORTATION_TRANSPORTS_HOUSEHOLD_GOODS"
    TRANSPORTATION_HAS_BROKER_LICENSE = "TRANSPORTATION_HAS_BROKER_LICENSE"
    # Separate Binary
    TRANSPORTATION_RISKY_NEW_ENTRANT = "TRANSPORTATION_RISKY_NEW_ENTRANT"
    TRANSPORTATION_MIGRANT_OPERATION = "TRANSPORTATION_MIGRANT_OPERATION"
    TRANSPORTATION_HAS_ENFORCEMENT_CASES = "TRANSPORTATION_HAS_ENFORCEMENT_CASES"
    TRANSPORTATION_HAS_CONSUMER_COMPLAINTS = "TRANSPORTATION_HAS_CONSUMER_COMPLAINTS"
    # TRANSPORTATION_SERIOUS_MAINTENANCE_VIOLATIONS
    TRANSPORTATION_TIRES_MAINTENANCE = "TRANSPORTATION_TIRES_MAINTENANCE"
    TRANSPORTATION_LIGHTING_MAINTENANCE = "TRANSPORTATION_LIGHTING_MAINTENANCE"
    TRANSPORTATION_STEERING_MECHANISM_MAINTENANCE = "TRANSPORTATION_STEERING_MECHANISM_MAINTENANCE"
    TRANSPORTATION_SUSPENSION_MAINTENANCE = "TRANSPORTATION_SUSPENSION_MAINTENANCE"
    TRANSPORTATION_IMPROPER_LOAD_SECUREMENT = "TRANSPORTATION_IMPROPER_LOAD_SECUREMENT"
    TRANSPORTATION_BRAKES_MAINTENANCE = "TRANSPORTATION_BRAKES_MAINTENANCE"
    TRANSPORTATION_MAINTENANCE_VIOLATIONS = "TRANSPORTATION_MAINTENANCE_VIOLATIONS"
    # TRANSPORTATION_SERIOUS_UNSAFE_DRIVING_VIOLATIONS
    DRIVER_OPERATING_WHILE_TEXTING_OR_USING_PHONE = "DRIVER_OPERATING_WHILE_TEXTING_OR_USING_PHONE"
    TRANSPORTATION_SERIOUS_UNSAFE_DRIVING = "TRANSPORTATION_SERIOUS_UNSAFE_DRIVING"
    DRIVER_TOWING_LOADED_BUS = "DRIVER_TOWING_LOADED_BUS"
    TRANSPORTATION_UNSAFE_DRIVING_VIOLATIONS = "TRANSPORTATION_UNSAFE_DRIVING_VIOLATIONS"
    TRANSPORTATION_CRASHES_IN_CANADA = "TRANSPORTATION_CRASHES_IN_CANADA"
    # TRANSPORTATION_PERFORMANCE_BENCHMARK_ISSUES
    TRANSPORTATION_VEHICLE_OOS_SERVICE_RATE_EXCEEDS_NATIONAL_AVERAGE = (
        "TRANSPORTATION_VEHICLE_OOS_SERVICE_RATE_EXCEEDS_NATIONAL_AVERAGE"
    )
    TRANSPORTATION_DRIVER_OOS_RATE_EXCEEDS_NATIONAL_AVERAGE = "TRANSPORTATION_DRIVER_OOS_RATE_EXCEEDS_NATIONAL_AVERAGE"
    TRANSPORTATION_HAZMAT_OOS_RATE_EXCEEDS_NATIONAL_AVERAGE = "TRANSPORTATION_HAZMAT_OOS_RATE_EXCEEDS_NATIONAL_AVERAGE"
    TRANSPORTATION_UNSAFE_DRIVING_MEASURE_CLOSE_OR_ABOVE_LIMIT = (
        "TRANSPORTATION_UNSAFE_DRIVING_MEASURE_CLOSE_OR_ABOVE_LIMIT"
    )
    TRANSPORTATION_HOS_COMPLIANCE_MEASURE_CLOSE_OR_ABOVE_LIMIT = (
        "TRANSPORTATION_HOS_COMPLIANCE_MEASURE_CLOSE_OR_ABOVE_LIMIT"
    )
    TRANSPORTATION_ALCOHOL_AND_DRUGS_MEASURE_CLOSE_OR_ABOVE_LIMIT = (
        "TRANSPORTATION_ALCOHOL_AND_DRUGS_MEASURE_CLOSE_OR_ABOVE_LIMIT"
    )
    TRANSPORTATION_DRIVER_FITNESS_MEASURE_CLOSE_OR_ABOVE_LIMIT = (
        "TRANSPORTATION_DRIVER_FITNESS_MEASURE_CLOSE_OR_ABOVE_LIMIT"
    )
    TRANSPORTATION_VEHICLE_MAINTENANCE_MEASURE_CLOSE_OR_ABOVE_LIMIT = (
        "TRANSPORTATION_VEHICLE_MAINTENANCE_MEASURE_CLOSE_OR_ABOVE_LIMIT"
    )
    TRANSPORTATION_UNSATISFACTORY_OR_CONDITIONAL_RATING = "TRANSPORTATION_UNSATISFACTORY_OR_CONDITIONAL_RATING"
    # TRANSPORTATION_ACUTE_VIOLATIONS
    TRANSPORTATION_HOURS_OF_SERVICE_ACUTE_VIOLATIONS = "TRANSPORTATION_HOURS_OF_SERVICE_ACUTE_VIOLATIONS"
    TRANSPORTATION_UNSAFE_DRIVING_ACUTE_VIOLATIONS = "TRANSPORTATION_UNSAFE_DRIVING_ACUTE_VIOLATIONS"
    TRANSPORTATION_ALCOHOL_AND_DRUGS_ACUTE_VIOLATIONS = "TRANSPORTATION_ALCOHOL_AND_DRUGS_ACUTE_VIOLATIONS"
    TRANSPORTATION_DRIVER_FITNESS_ACUTE_VIOLATIONS = "TRANSPORTATION_DRIVER_FITNESS_ACUTE_VIOLATIONS"
    TRANSPORTATION_VEHICLE_MAINTENANCE_ACUTE_VIOLATIONS = "TRANSPORTATION_VEHICLE_MAINTENANCE_ACUTE_VIOLATIONS"
    TRANSPORTATION_HAZARDOUS_MATERIALS_ACUTE_VIOLATIONS = "TRANSPORTATION_HAZARDOUS_MATERIALS_ACUTE_VIOLATIONS"
    # TRANSPORTATION_RISK_OF_TRANSPORT_WITHOUT_PERMISSION
    TRANSPORTATION_TRANSPORT_PASSENGERS_WITHOUT_PERMISSION = "TRANSPORTATION_TRANSPORT_PASSENGERS_WITHOUT_PERMISSION"
    TRANSPORTATION_TRANSPORT_HAZARDOUS_MATERIALS_WITHOUT_PERMISSION = (
        "TRANSPORTATION_TRANSPORT_HAZARDOUS_MATERIALS_WITHOUT_PERMISSION"
    )

    # Fallowing observations are used for explanations for TRANSPORTATION_PERFORMANCE_BENCHMARK_ISSUES
    TRANSPORTATION_OOS_BENCHMARK_VEHICLE = "TRANSPORTATION_OOS_BENCHMARK_VEHICLE"
    TRANSPORTATION_OOS_BENCHMARK_DRIVER = "TRANSPORTATION_OOS_BENCHMARK_DRIVER"
    TRANSPORTATION_OOS_BENCHMARK_HAZMAT = "TRANSPORTATION_OOS_BENCHMARK_HAZMAT"
    TRANSPORTATION_BASIC_BENCHMARK_UNSAFE_DRIVING = "TRANSPORTATION_BASIC_BENCHMARK_UNSAFE_DRIVING"
    TRANSPORTATION_BASIC_BENCHMARK_HOS_COMPLIANCE = "TRANSPORTATION_BASIC_BENCHMARK_HOS_COMPLIANCE"
    TRANSPORTATION_BASIC_BENCHMARK_VEHICLE_MAINTENANCE = "TRANSPORTATION_BASIC_BENCHMARK_VEHICLE_MAINTENANCE"
    TRANSPORTATION_BASIC_BENCHMARK_DRUGS_ALCOHOL = "TRANSPORTATION_BASIC_BENCHMARK_DRUGS_ALCOHOL"
    TRANSPORTATION_BASIC_BENCHMARK_DRIVER_FITNESS = "TRANSPORTATION_BASIC_BENCHMARK_DRIVER_FITNESS"
    ## End of Derived from FMCSA data
    # Transportation End

    # Financial health
    YEARS_IN_EXPERIAN_FILE = "YEARS_IN_EXPERIAN_FILE"
    LIEN_COUNT = "LIEN_COUNT"
    EXPERIAN_INTELLISCORE = "EXPERIAN_INTELLISCORE"
    # End financial health

    # Retail
    SELLS_TOBACCO_OR_CANNABIS = "SELLS_TOBACCO_OR_CANNABIS"
    # BBB
    BBB_RATING = "BBB_RATING"
    BBB_REVIEW_RATING = "BBB_REVIEW_RATING"
    BBB_ACCREDITATION_DATE = "BBB_ACCREDITATION_DATE"
    BBB_ACCREDITATION_REVOKED_DATE = "BBB_ACCREDITATION_REVOKED_DATE"
    BBB_CONTACT_INFORMATION = "BBB_CONTACT_INFORMATION"
    ENTITY_TYPE = "ENTITY_TYPE"
    # Manufacturing
    FDA_FEI_NUMBER = "FDA_FEI_NUMBER"
    """
    There are 2 FEI Numbers: Federal Employer Identification Number (FEIN) and FDA Establishment Identifier (FDA_FEI_NUMBER).
     The FDA_FEI_NUMBER is an acronym which stands for FDA Establishment Identifier. It is also known as the Firm
     or Facility Establishment Identifier. The FEI number is a unique identifier assigned by the FDA to
     identify firms associated with FDA regulated products.
     https://datadashboard.fda.gov/ora/glossary.htm
    """
    IS_HACCP_CERTIFIED = "IS_HACCP_CERTIFIED"
    LOCATION_TYPE = "LOCATION_TYPE"
    "Thomasnet location type from crawling other locations"
    YEARS_IN_BUSINESS = "YEARS_IN_BUSINESS"
    KNOWN_PERSONNEL = "KNOWN_PERSONNEL"
    # SUPPLEMENTAL FORMS
    APPLICANT_NAME = "APPLICANT_NAME"
    # CONTRACTOR_PROJECTS
    PROJECT_USE_OF_EIFS = "PROJECT_USE_OF_EIFS"
    PROJECT_SCAFFOLDING = "PROJECT_SCAFFOLDING"
    PROJECT_ROOF_WORK = "PROJECT_ROOF_WORK"
    PROJECT_CRANE_WORK = "PROJECT_CRANE_WORK"
    PROJECT_DEMOLITION_WORK = "PROJECT_DEMOLITION_WORK"
    PROJECT_BELOW_GRADE = "PROJECT_BELOW_GRADE"
    PROJECT_DEPTH_OF_WORK = "PROJECT_DEPTH_OF_WORK"
    PROJECT_WORK_ABOVE_SEVEN_STORIES = "PROJECT_WORK_ABOVE_SEVEN_STORIES"
    PROJECT_MOLD_REMOVAL = "PROJECT_MOLD_REMOVAL"
    PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL = "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL"

    PROJECT_PERCENTAGE_OF_WORK_RESIDENTIAL = "PROJECT_PERCENTAGE_OF_WORK_RESIDENTIAL"
    PROJECT_PERCENTAGE_OF_WORK_COMMERCIAL = "PROJECT_PERCENTAGE_OF_WORK_COMMERCIAL"
    PROJECT_PERCENTAGE_OF_WORK_INDUSTRIAL = "PROJECT_PERCENTAGE_OF_WORK_INDUSTRIAL"
    PROJECT_PERCENTAGE_OF_WORK_PUBLIC = "PROJECT_PERCENTAGE_OF_WORK_PUBLIC"

    PROJECT_SUBCONTRACTORS_USED = "PROJECT_SUBCONTRACTORS_USED"
    PROJECT_SUBCONTRACTORS_COST = "PROJECT_SUBCONTRACTORS_COST"
    SUBCONTRACTOR_COSTS_AS_PERCENT_OF_TOTAL_RECEIPTS = "SUBCONTRACTOR_COSTS_AS_PERCENT_OF_TOTAL_RECEIPTS"
    PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS = "PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS"
    PROJECT_PERCENTAGE_OF_WORK_AS_GC = "PROJECT_PERCENTAGE_OF_WORK_AS_GC"
    PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR = "PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR"

    PROJECT_ESTIMATED_CONSTRUCTION_COST = "PROJECT_ESTIMATED_CONSTRUCTION_COST"
    PROJECT_SAFETY_PROGRAM = "PROJECT_SAFETY_PROGRAM"
    PROJECT_QUALITY_CONTROL_PROGRAM = "PROJECT_QUALITY_CONTROL_PROGRAM"
    PROJECT_SITE_INSPECTION_PROGRAM = "PROJECT_SITE_INSPECTION_PROGRAM"
    PROJECT_BLASTING_WORK = "PROJECT_BLASTING_WORK"
    PROJECT_EXCAVATION_WORK = "PROJECT_EXCAVATION_WORK"
    PROJECT_HEIGHT_IN_STORIES = "PROJECT_HEIGHT_IN_STORIES"
    PROJECT_HEIGHT_IN_FT = "PROJECT_HEIGHT_IN_FT"
    PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES = "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
    PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT = "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT"
    PROJECT_NEW_CONSTRUCTION = "PROJECT_NEW_CONSTRUCTION"
    PROJECT_REMODELING_OR_REPAIR = "PROJECT_REMODELING_OR_REPAIR"
    PROJECT_HVAC_WORK = "PROJECT_HVAC_WORK"
    PROJECT_PLUMBING_WORK = "PROJECT_PLUMBING_WORK"
    PROJECT_BOILER_WORK = "PROJECT_BOILER_WORK"
    PROJECT_ELECTRICAL_WORK = "PROJECT_ELECTRICAL_WORK"
    PROJECT_GAS_LINE_WORK = "PROJECT_GAS_LINE_WORK"
    PROJECT_SPRINKLER_SYSTEM_WORK = "PROJECT_SPRINKLER_SYSTEM_WORK"
    PROJECT_SEWER_WORK = "PROJECT_SEWER_WORK"
    PROJECT_SHEET_METAL_WORK = "PROJECT_SHEET_METAL_WORK"
    PROJECT_WELDING_WORK = "PROJECT_WELDING_WORK"
    PROJECT_GRADING_WORK = "PROJECT_GRADING_WORK"
    PROJECT_PILE_DRIVING_WORK = "PROJECT_PILE_DRIVING_WORK"
    PROJECT_PAVING_WORK = "PROJECT_PAVING_WORK"
    PROJECT_DRILLING_WORK = "PROJECT_DRILLING_WORK"
    PROJECT_CONCRETE = "PROJECT_CONCRETE"
    PROJECT_WINDOW = "PROJECT_WINDOW"
    PROJECT_DRYWALL = "PROJECT_DRYWALL"
    PROJECT_PAINTING = "PROJECT_PAINTING"
    PROJECT_PERCENTAGE_OF_HAND_DEMOLITION = "PROJECT_PERCENTAGE_OF_HAND_DEMOLITION"
    PROJECT_PERCENTAGE_OF_PULL_PUSH_DOWN = "PROJECT_PERCENTAGE_OF_PULL_PUSH_DOWN"
    PROJECT_PERCENTAGE_OF_MECHANICAL_DEMOLITION = "PROJECT_PERCENTAGE_OF_MECHANICAL_DEMOLITION"
    PROJECT_PERCENTAGE_OF_IMPLOSION_EXPLOSIVES = "PROJECT_PERCENTAGE_OF_IMPLOSION_EXPLOSIVES"
    PROJECT_PERCENTAGE_OF_HYDRODEMOLITION = "PROJECT_PERCENTAGE_OF_HYDRODEMOLITION"
    PROJECT_PERCENTAGE_OF_WRECKING_BALL = "PROJECT_PERCENTAGE_OF_WRECKING_BALL"
    PROJECT_PERCENTAGE_OF_OTHER_ROOFING_METHODS = "PROJECT_PERCENTAGE_OF_OTHER_ROOFING_METHODS"
    PROJECT_PERCENTAGE_OF_WORK_URBAN = "PROJECT_PERCENTAGE_OF_WORK_URBAN"
    PROJECT_PERCENTAGE_OF_WORK_SUBURBAN = "PROJECT_PERCENTAGE_OF_WORK_SUBURBAN"
    PROJECT_PERCENTAGE_OF_WORK_RURAL = "PROJECT_PERCENTAGE_OF_WORK_RURAL"
    PROJECT_SIDING_WORK = "PROJECT_SIDING_WORK"
    PROJECT_INSULATION_WORK = "PROJECT_INSULATION_WORK"
    PROJECT_WATER_PROOFING_WORK = "PROJECT_WATER_PROOFING_WORK"
    PROJECT_RAIN_GUTTER_WORK = "PROJECT_RAIN_GUTTER_WORK"
    PROJECT_CARPENTRY_WORK = "PROJECT_CARPENTRY_WORK"
    PROJECT_ASPHALT_SHINGLE = "PROJECT_ASPHALT_SHINGLE"
    PROJECT_WOOD_SHAKE_SHINGLE = "PROJECT_WOOD_SHAKE_SHINGLE"
    PROJECT_SLATE = "PROJECT_SLATE"
    PROJECT_TILE = "PROJECT_TILE"
    PROJECT_METAL = "PROJECT_METAL"
    PROJECT_POLYURETHANE_FOAM = "PROJECT_POLYURETHANE_FOAM"
    PROJECT_HOT_TAR = "PROJECT_HOT_TAR"
    PROJECT_TORCH_DOWN = "PROJECT_TORCH_DOWN"
    PROJECT_HOT_AIR_WELDING = "PROJECT_HOT_AIR_WELDING"
    PROJECT_MODIFIED_BITUMEN_HOT_OR_COLD = "PROJECT_MODIFIED_BITUMEN_HOT_OR_COLD"
    PROJECT_EPDM_HOT_OR_COLD = "PROJECT_EPDM_HOT_OR_COLD"
    PROJECT_OTHER_ROOFING_METHODS = "PROJECT_OTHER_ROOFING_METHODS"
    PROJECT_PERCENTAGE_OF_WORK_ON_PITCHED_ROOFS = "PROJECT_PERCENTAGE_OF_WORK_ON_PITCHED_ROOFS"
    PROJECT_PERCENTAGE_OF_WORK_ON_FLAT_ROOFS = "PROJECT_PERCENTAGE_OF_WORK_ON_FLAT_ROOFS"
    PROJECT_PERCENTAGE_OF_WORK_ON_OTHER_ROOFS = "PROJECT_PERCENTAGE_OF_WORK_ON_OTHER_ROOFS"
    PROJECT_PERCENTAGE_OF_EXTERIOR_WORK = "PROJECT_PERCENTAGE_OF_EXTERIOR_WORK"
    PROJECT_PERCENTAGE_OF_INTERIOR_WORK = "PROJECT_PERCENTAGE_OF_INTERIOR_WORK"
    PROJECT_PERCENTAGE_OF_OTHER_DEMOLITION_METHODS = "PROJECT_PERCENTAGE_OF_OTHER_DEMOLITION_METHODS"
    PROJECT_TRAFFIC_LIGHTING_SIGNALS_WORK = "PROJECT_TRAFFIC_LIGHTING_SIGNALS_WORK"
    PROJECT_AIRPORT_WORK = "PROJECT_AIRPORT_WORK"
    PROJECT_ALARM_SYSTEMS_WORK = "PROJECT_ALARM_SYSTEMS_WORK"
    PROJECT_SOLAR_WORK = "PROJECT_SOLAR_WORK"
    PROJECT_TOWER_ANTENNAS_WORK = "PROJECT_TOWER_ANTENNAS_WORK"
    PROJECT_FIBER_OPTICS_WORK = "PROJECT_FIBER_OPTICS_WORK"
    PROJECT_START_DATE = "PROJECT_START_DATE"
    PROJECT_END_DATE = "PROJECT_END_DATE"
    PROJECT_DURATION = "PROJECT_DURATION"
    # End of Contractor Project

    # CONTRACTOR SERVICES
    ARCHITECTURAL_SERVICES = "ARCHITECTURAL_SERVICES"
    BLASTING_OPERATIONS = "BLASTING_OPERATIONS"
    DREDGING = "DREDGING"
    ENVIRONMENTAL_SERVICES = "ENVIRONMENTAL_SERVICES"
    FINISH_CARPENTRY = "FINISH_CARPENTRY"
    FLOORING = "FLOORING"
    FRAMING = "FRAMING"
    GLASS_AND_GLAZING = "GLASS_AND_GLAZING"
    LANDFILL_SERVICES = "LANDFILL_SERVICES"
    LANDSCAPING_SERVICES = "LANDSCAPING_SERVICES"
    PAVING = "PAVING"
    PILE_REPAIR_JACKETING_SYSTEMS = "PILE_REPAIR_JACKETING_SYSTEMS"
    POURED_CONCRETE_FOUNDATION = "POURED_CONCRETE_FOUNDATION"
    SNOW_REMOVAL = "SNOW_REMOVAL"
    STRUCTURAL_STEEL = "STRUCTURAL_STEEL"
    TOWER_CRANE_SERVICES = "TOWER_CRANE_SERVICES"
    TRAFFIC_MANAGEMENT = "TRAFFIC_MANAGEMENT"
    # End of CONTRACTOR SERVICES

    # CONTRACTOR PROJECT TYPES
    AIRPORT_WORK = "AIRPORT_WORK"
    AMUSEMENT_PARKS = "AMUSEMENT_PARKS"
    APARTMENTS = "APARTMENTS"
    ARENAS = "ARENAS"
    BRIDGE_WORK = "BRIDGE_WORK"
    COMMERCIAL_WORK = "COMMERCIAL_WORK"
    DAMS_OR_DIKES = "DAMS_OR_DIKES"
    ENTERTAINMENT_CENTERS = "ENTERTAINMENT_CENTERS"
    FERTILIZER_DISTRIBUTION_CENTERS = "FERTILIZER_DISTRIBUTION_CENTERS"
    FERTILIZER_PLANTS = "FERTILIZER_PLANTS"
    GAS_ELECTRICAL_UTILITY_PLANTS = "GAS_ELECTRICAL_UTILITY_PLANTS"
    HEALTHCARE_FACILITIES = "HEALTHCARE_FACILITIES"
    HOSPITALS = "HOSPITALS"
    HOTELS = "HOTELS"
    INDUSTRIAL_BUILDING = "INDUSTRIAL_BUILDING"
    LANDFILL = "LANDFILL"
    LARGE_STADIUMS = "LARGE_STADIUMS"
    MARINE_WORK = "MARINE_WORK"
    MERCANTILE_SHOPPING_CENTERS = "MERCANTILE_SHOPPING_CENTERS"
    MIXED_USE_DEVELOPMENT = "MIXED_USE_DEVELOPMENT"
    MODULAR_APARTMENTS = "MODULAR_APARTMENTS"
    NUCLEAR_POWER_PLANTS = "NUCLEAR_POWER_PLANTS"
    NURSING_HOMES = "NURSING_HOMES"
    OFFICE_BUILDINGS = "OFFICE_BUILDINGS"
    RAIL = "RAIL"
    RECREATIONAL_FACILITIES = "RECREATIONAL_FACILITIES"
    RESIDENTIAL_WORK = "RESIDENTIAL_WORK"
    RESTAURANTS = "RESTAURANTS"
    RETAIL_STORES = "RETAIL_STORES"
    SCHOOLS_AND_COLLEGES = "SCHOOLS_AND_COLLEGES"
    SEWER = "SEWER"
    SMALL_TO_MIDSIZE_STADIUMS = "SMALL_TO_MIDSIZE_STADIUMS"
    STREET_AND_ROADS = "STREET_AND_ROADS"
    SUBWAYS = "SUBWAYS"
    TUNNEL_WORK = "TUNNEL_WORK"
    WAREHOUSES = "WAREHOUSES"
    # End of CONTRACTOR PROJECT TYPES

    # WC Supplemental facts
    NUMBER_OF_SHIFTS = "NUMBER_OF_SHIFTS"
    EMPLOYEE_RETIREMENT_OR_PENSION_PLANS_PROVIDED = "EMPLOYEE_RETIREMENT_OR_PENSION_PLANS_PROVIDED"
    PAID_SICK_LEAVE_PROVIDED = "PAID_SICK_LEAVE_PROVIDED"
    PAID_VACATIONS_PROVIDED = "PAID_VACATIONS_PROVIDED"
    PRE_HIRE_DRUG_TESTING = "PRE_HIRE_DRUG_TESTING"
    POST_ACCIDENT_DRUG_TESTING = "POST_ACCIDENT_DRUG_TESTING"
    HAS_LIFTING_EXPOSURES = "HAS_LIFTING_EXPOSURES"
    PERSONAL_PROTECTION_EQUIPMENT_PROVIDED = "PERSONAL_PROTECTION_EQUIPMENT_PROVIDED"
    HAS_RESPIRATORY_PROTECTION_PROGRAM = "HAS_RESPIRATORY_PROTECTION_PROGRAM"
    # End of WC Supplemental facts

    # K2 Aegis Supplemental facts
    GARAGE_OTHER_OPERATIONS = "GARAGE_OTHER_OPERATIONS"
    GARAGE_FUEL_CONVERSION = "GARAGE_FUEL_CONVERSION"
    GARAGE_PERFORMANCE_ENHANCEMENTS = "GARAGE_PERFORMANCE_ENHANCEMENTS"
    GARAGE_LOAN_OR_RENT = "GARAGE_LOAN_OR_RENT"
    GARAGE_PAWNING_TITLE_LOANS = "GARAGE_PAWNING_TITLE_LOANS"
    GARAGE_DISMANTLE_OR_SALVAGE = "GARAGE_DISMANTLE_OR_SALVAGE"
    GARAGE_CAR_CRUSHER_OR_SALVAGE = "GARAGE_CAR_CRUSHER_OR_SALVAGE"
    GARAGE_BUY_HERE_PAY_HERE = "GARAGE_BUY_HERE_PAY_HERE"
    GARAGE_AIRPORT_SEAPORT_RAILROAD = "GARAGE_AIRPORT_SEAPORT_RAILROAD"
    GARAGE_BREATHALYZER_IGNITION_INTERLOCK = "GARAGE_BREATHALYZER_IGNITION_INTERLOCK"
    GARAGE_MANUFACTURE_AUTO_PARTS = "GARAGE_MANUFACTURE_AUTO_PARTS"
    GARAGE_STRUCTURALLY_ALTER_VEHICLES = "GARAGE_STRUCTURALLY_ALTER_VEHICLES"
    GARAGE_TWO_OR_MORE_VEHICLE_TRANSPORT = "GARAGE_TWO_OR_MORE_VEHICLE_TRANSPORT"
    GARAGE_DRIVERS_UNDER_20_OVER_70 = "GARAGE_DRIVERS_UNDER_20_OVER_70"
    # K2 Aegis Supplemental facts

    # Financials
    REVENUE = "REVENUE"
    REVENUE_YOY_INCREASE = "REVENUE_YOY_INCREASE"
    REVENUE_YOY_INCREASE_RATIO = "REVENUE_YOY_INCREASE_RATIO"
    CURRENT_ASSETS = "CURRENT_ASSETS"
    OTHER_ASSETS = "OTHER_ASSETS"
    TOTAL_ASSETS = "TOTAL_ASSETS"
    TOTAL_ASSETS_YOY_INCREASE = "TOTAL_ASSETS_YOY_INCREASE"
    TOTAL_ASSETS_YOY_INCREASE_RATIO = "TOTAL_ASSETS_YOY_INCREASE_RATIO"
    CURRENT_LIABILITIES = "CURRENT_LIABILITIES"
    OTHER_LIABILITIES = "OTHER_LIABILITIES"
    TOTAL_LIABILITIES = "TOTAL_LIABILITIES"
    OPERATING_INCOME = "OPERATING_INCOME"
    NET_INCOME = "NET_INCOME"
    OTHER_INCOME = "OTHER_INCOME"
    NET_SALES = "NET_SALES"
    GROSS_PROFIT = "GROSS_PROFIT"
    CASHFLOW = "CASHFLOW"
    RETAINED_EARNINGS = "RETAINED_EARNINGS"
    EQUITY = "EQUITY"
    CURRENT_RATIO = "CURRENT_RATIO"
    CASH_RATIO = "CASH_RATIO"
    NET_WORKING_CAPITAL_RATIO = "NET_WORKING_CAPITAL_RATIO"
    QUICK_RATIO = "QUICK_RATIO"
    RECEIVABLES = "RECEIVABLES"
    MARKETABLE_SECURITIES = "MARKETABLE_SECURITIES"
    RETURN_ON_EQUITY = "RETURN_ON_EQUITY"
    DEBT_TO_EQUITY = "DEBT_TO_EQUITY"
    OPERATING_CASH_FLOW_RATIO = "OPERATING_CASH_FLOW_RATIO"
    INVENTORY = "INVENTORY"
    STOCK_TICKER = "STOCK_TICKER"
    # end of Financials

    # Financials ratio benchmarks
    CURRENT_RATIO_BENCHMARK = "CURRENT_RATIO_BENCHMARK"
    CASH_RATIO_BENCHMARK = "CASH_RATIO_BENCHMARK"
    QUICK_RATIO_BENCHMARK = "QUICK_RATIO_BENCHMARK"
    DEBT_TO_ASSETS_RATIO_BENCHMARK = "DEBT_TO_ASSETS_RATIO_BENCHMARK"
    DEBT_TO_EQUITY_BENCHMARK = "DEBT_TO_EQUITY_BENCHMARK"
    # end of Financials ratio benchmarks

    # Financials Time Series YoY
    REVENUE_YOY = "REVENUE_YOY"
    REVENUE_YOY_PERCENTAGE = "REVENUE_YOY_PERCENTAGE"
    CURRENT_ASSETS_YOY = "CURRENT_ASSETS_YOY"
    CURRENT_ASSETS_YOY_PERCENTAGE = "CURRENT_ASSETS_YOY_PERCENTAGE"
    OTHER_ASSETS_YOY = "OTHER_ASSETS_YOY"
    OTHER_ASSETS_YOY_PERCENTAGE = "OTHER_ASSETS_YOY_PERCENTAGE"
    TOTAL_ASSETS_YOY = "TOTAL_ASSETS_YOY"
    TOTAL_ASSETS_YOY_PERCENTAGE = "TOTAL_ASSETS_YOY_PERCENTAGE"
    CURRENT_LIABILITIES_YOY = "CURRENT_LIABILITIES_YOY"
    CURRENT_LIABILITIES_YOY_PERCENTAGE = "CURRENT_LIABILITIES_YOY_PERCENTAGE"
    OTHER_LIABILITIES_YOY = "OTHER_LIABILITIES_YOY"
    OTHER_LIABILITIES_YOY_PERCENTAGE = "OTHER_LIABILITIES_YOY_PERCENTAGE"
    TOTAL_LIABILITIES_YOY = "TOTAL_LIABILITIES_YOY"
    TOTAL_LIABILITIES_YOY_PERCENTAGE = "TOTAL_LIABILITIES_YOY_PERCENTAGE"
    OPERATING_INCOME_YOY = "OPERATING_INCOME_YOY"
    OPERATING_INCOME_YOY_PERCENTAGE = "OPERATING_INCOME_YOY_PERCENTAGE"
    NET_INCOME_YOY = "NET_INCOME_YOY"
    NET_INCOME_YOY_PERCENTAGE = "NET_INCOME_YOY_PERCENTAGE"
    OTHER_INCOME_YOY = "OTHER_INCOME_YOY"
    OTHER_INCOME_YOY_PERCENTAGE = "OTHER_INCOME_YOY_PERCENTAGE"
    NET_SALES_YOY = "NET_SALES_YOY"
    NET_SALES_YOY_PERCENTAGE = "NET_SALES_YOY_PERCENTAGE"
    GROSS_PROFIT_YOY = "GROSS_PROFIT_YOY"
    GROSS_PROFIT_YOY_PERCENTAGE = "GROSS_PROFIT_YOY_PERCENTAGE"
    CASHFLOW_YOY = "CASHFLOW_YOY"
    CASHFLOW_YOY_PERCENTAGE = "CASHFLOW_YOY_PERCENTAGE"
    RETAINED_EARNINGS_YOY = "RETAINED_EARNINGS_YOY"
    RETAINED_EARNINGS_YOY_PERCENTAGE = "RETAINED_EARNINGS_YOY_PERCENTAGE"
    EQUITY_YOY = "EQUITY_YOY"
    EQUITY_YOY_PERCENTAGE = "EQUITY_YOY_PERCENTAGE"
    CURRENT_RATIO_YOY = "CURRENT_RATIO_YOY"
    CURRENT_RATIO_YOY_PERCENTAGE = "CURRENT_RATIO_YOY_PERCENTAGE"
    RETURN_ON_EQUITY_YOY = "RETURN_ON_EQUITY_YOY"
    RETURN_ON_EQUITY_YOY_PERCENTAGE = "RETURN_ON_EQUITY_YOY_PERCENTAGE"
    DEBT_TO_EQUITY_YOY = "DEBT_TO_EQUITY_YOY"
    DEBT_TO_EQUITY_YOY_PERCENTAGE = "DEBT_TO_EQUITY_YOY_PERCENTAGE"
    TOTAL_SALES_YOY = "TOTAL_SALES_YOY"
    TOTAL_SALES_YOY_PERCENTAGE = "TOTAL_SALES_YOY_PERCENTAGE"
    FOREIGN_TOTAL_SALES_YOY = "FOREIGN_TOTAL_SALES_YOY"
    FOREIGN_TOTAL_SALES_YOY_PERCENTAGE = "FOREIGN_TOTAL_SALES_YOY_PERCENTAGE"
    DELIVERY_SALES_YOY = "DELIVERY_SALES_YOY"
    DELIVERY_SALES_YOY_PERCENTAGE = "DELIVERY_SALES_YOY_PERCENTAGE"
    FOOD_SALES_YOY = "FOOD_SALES_YOY"
    FOOD_SALES_YOY_PERCENTAGE = "FOOD_SALES_YOY_PERCENTAGE"
    HOTEL_SALES_YOY = "HOTEL_SALES_YOY"
    HOTEL_SALES_YOY_PERCENTAGE = "HOTEL_SALES_YOY_PERCENTAGE"
    LIQUOR_SALES_YOY = "LIQUOR_SALES_YOY"
    LIQUOR_SALES_YOY_PERCENTAGE = "LIQUOR_SALES_YOY_PERCENTAGE"
    CASH_RATIO_YOY = "CASH_RATIO_YOY"
    CASH_RATIO_YOY_PERCENTAGE = "CASH_RATIO_YOY_PERCENTAGE"
    NET_WORKING_CAPITAL_RATIO_YOY = "NET_WORKING_CAPITAL_RATIO_YOY"
    NET_WORKING_CAPITAL_RATIO_YOY_PERCENTAGE = "NET_WORKING_CAPITAL_RATIO_YOY_PERCENTAGE"
    QUICK_RATIO_YOY = "QUICK_RATIO_YOY"
    QUICK_RATIO_YOY_PERCENTAGE = "QUICK_RATIO_YOY_PERCENTAGE"
    RECEIVABLES_YOY = "RECEIVABLES_YOY"
    RECEIVABLES_YOY_PERCENTAGE = "RECEIVABLES_YOY_PERCENTAGE"
    MARKETABLE_SECURITIES_YOY = "MARKETABLE_SECURITIES_YOY"
    MARKETABLE_SECURITIES_YOY_PERCENTAGE = "MARKETABLE_SECURITIES_YOY_PERCENTAGE"
    DOMESTIC_TOTAL_SALES_YOY = "DOMESTIC_TOTAL_SALES_YOY"
    DOMESTIC_TOTAL_SALES_YOY_PERCENTAGE = "DOMESTIC_TOTAL_SALES_YOY_PERCENTAGE"
    PAYROLL_YOY = "PAYROLL_YOY"
    PAYROLL_YOY_PERCENTAGE = "PAYROLL_YOY_PERCENTAGE"
    NON_CURRENT_LIABILITIES_YOY = "NON_CURRENT_LIABILITIES_YOY"
    NON_CURRENT_LIABILITIES_YOY_PERCENTAGE = "NON_CURRENT_LIABILITIES_YOY_PERCENTAGE"
    NON_CURRENT_ASSETS_YOY = "NON_CURRENT_ASSETS_YOY"
    NON_CURRENT_ASSETS_YOY_PERCENTAGE = "NON_CURRENT_ASSETS_YOY_PERCENTAGE"
    PROPERTY_AND_EQUIPMENT_YOY = "PROPERTY_AND_EQUIPMENT_YOY"
    PROPERTY_AND_EQUIPMENT_YOY_PERCENTAGE = "PROPERTY_AND_EQUIPMENT_YOY_PERCENTAGE"
    OPERATING_EXPENSES_YOY = "OPERATING_EXPENSES_YOY"
    OPERATING_EXPENSES_YOY_PERCENTAGE = "OPERATING_EXPENSES_YOY_PERCENTAGE"
    ADMINISTRATIVE_EXPENSES_YOY = "ADMINISTRATIVE_EXPENSES_YOY"
    ADMINISTRATIVE_EXPENSES_YOY_PERCENTAGE = "ADMINISTRATIVE_EXPENSES_YOY_PERCENTAGE"
    INVESTMENT_EXPENSES_YOY = "INVESTMENT_EXPENSES_YOY"
    INVESTMENT_EXPENSES_YOY_PERCENTAGE = "INVESTMENT_EXPENSES_YOY_PERCENTAGE"
    OTHER_EXPENSES_YOY = "OTHER_EXPENSES_YOY"
    OTHER_EXPENSES_YOY_PERCENTAGE = "OTHER_EXPENSES_YOY_PERCENTAGE"
    TOTAL_EXPENSES_YOY = "TOTAL_EXPENSES_YOY"
    TOTAL_EXPENSES_YOY_PERCENTAGE = "TOTAL_EXPENSES_YOY_PERCENTAGE"
    COST_OF_SALES_YOY = "COST_OF_SALES_YOY"
    COST_OF_SALES_YOY_PERCENTAGE = "COST_OF_SALES_YOY_PERCENTAGE"
    CASHFLOW_OPERATING_YOY = "CASHFLOW_OPERATING_YOY"
    CASHFLOW_OPERATING_YOY_PERCENTAGE = "CASHFLOW_OPERATING_YOY_PERCENTAGE"
    NET_LOSS_YOY = "NET_LOSS_YOY"
    NET_LOSS_YOY_PERCENTAGE = "NET_LOSS_YOY_PERCENTAGE"
    CASHFLOW_INVESTING_YOY = "CASHFLOW_INVESTING_YOY"
    CASHFLOW_INVESTING_YOY_PERCENTAGE = "CASHFLOW_INVESTING_YOY_PERCENTAGE"
    CASHFLOW_FINANCING_YOY = "CASHFLOW_FINANCING_YOY"
    CASHFLOW_FINANCING_YOY_PERCENTAGE = "CASHFLOW_FINANCING_YOY_PERCENTAGE"
    CASH_END_PERIOD_YOY = "CASH_END_PERIOD_YOY"
    CASH_END_PERIOD_YOY_PERCENTAGE = "CASH_END_PERIOD_YOY_PERCENTAGE"
    NON_CASH_ACTIVITY_YOY = "NON_CASH_ACTIVITY_YOY"
    NON_CASH_ACTIVITY_YOY_PERCENTAGE = "NON_CASH_ACTIVITY_YOY_PERCENTAGE"
    LIABILITIES_AND_EQUITY_YOY = "LIABILITIES_AND_EQUITY_YOY"
    LIABILITIES_AND_EQUITY_YOY_PERCENTAGE = "LIABILITIES_AND_EQUITY_YOY_PERCENTAGE"
    DEBT_TO_SERVICE_RATIO_YOY = "DEBT_TO_SERVICE_RATIO_YOY"
    DEBT_TO_SERVICE_RATIO_YOY_PERCENTAGE = "DEBT_TO_SERVICE_RATIO_YOY_PERCENTAGE"
    DEBT_TO_ASSETS_RATIO_YOY = "DEBT_TO_ASSETS_RATIO_YOY"
    DEBT_TO_ASSETS_RATIO_YOY_PERCENTAGE = "DEBT_TO_ASSETS_RATIO_YOY_PERCENTAGE"
    WORKING_CAPITAL_RATIO_YOY = "WORKING_CAPITAL_RATIO_YOY"
    WORKING_CAPITAL_RATIO_YOY_PERCENTAGE = "WORKING_CAPITAL_RATIO_YOY_PERCENTAGE"
    CURRENT_LEASE_OBLIGATIONS_YOY = "CURRENT_LEASE_OBLIGATIONS_YOY"
    CURRENT_LEASE_OBLIGATIONS_YOY_PERCENTAGE = "CURRENT_LEASE_OBLIGATIONS_YOY_PERCENTAGE"
    INTEREST_EXPENSE_YOY = "INTEREST_EXPENSE_YOY"
    INTEREST_EXPENSE_YOY_PERCENTAGE = "INTEREST_EXPENSE_YOY_PERCENTAGE"
    CURRENT_LONG_TERM_DEBT_MATURITY_YOY = "CURRENT_LONG_TERM_DEBT_MATURITY_YOY"
    CURRENT_LONG_TERM_DEBT_MATURITY_YOY_PERCENTAGE = "CURRENT_LONG_TERM_DEBT_MATURITY_YOY_PERCENTAGE"
    OPERATING_CASH_FLOW_RATIO_YOY = "OPERATING_CASH_FLOW_RATIO_YOY"
    OPERATING_CASH_FLOW_RATIO_YOY_PERCENTAGE = "OPERATING_CASH_FLOW_RATIO_YOY_PERCENTAGE"
    # End Financials Time Series YoY

    # MANAGEMENT LIABILITY TYPES
    DOMESTIC_EMPLOYMENT_PERCENTAGE = "DOMESTIC_EMPLOYMENT_PERCENTAGE"
    EXPECTED_BANKRUPTCY_OR_LIQUIDATION = "EXPECTED_BANKRUPTCY_OR_LIQUIDATION"
    EXPECTED_MERGER_OR_ACQUISITION = "EXPECTED_MERGER_OR_ACQUISITION"
    HAS_OFFICERS_RECENTLY_DEPARTED = "HAS_OFFICERS_RECENTLY_DEPARTED"
    HAS_OWNER_WITH_MORE_THAN_5_PERCENT_SHARES = "HAS_OWNER_WITH_MORE_THAN_5_PERCENT_SHARES"
    INDEPENDENT_CONTRACTORS_PERCENTAGE = "INDEPENDENT_CONTRACTORS_PERCENTAGE"
    INDEPENDENT_CONTRACTOR_COUNT = "INDEPENDENT_CONTRACTOR_COUNT"
    OVERALL_EMPLOYEE_RATING = "OVERALL_EMPLOYEE_RATING"
    SENIOR_MANAGEMENT_EMPLOYEE_RATING = "SENIOR_MANAGEMENT_EMPLOYEE_RATING"
    UNIONIZED_EMPLOYMENT_PERCENTAGE = "UNIONIZED_EMPLOYMENT_PERCENTAGE"
    BANKRUPTCY_RESTRUCTURING_LIQUIDATION_EXPECTED = "BANKRUPTCY_RESTRUCTURING_LIQUIDATION_EXPECTED"
    BANKRUPTCY_RESTRUCTURING_LIQUIDATION_PAST_24_MONTHS = "BANKRUPTCY_RESTRUCTURING_LIQUIDATION_PAST_24_MONTHS"
    CLOSING_LAYOFFS_PAST_24_MONTHS = "CLOSING_LAYOFFS_PAST_24_MONTHS"
    MERGER_OR_ACQUISITION_ASSET_SALES_PAST_24_MONTHS = "MERGER_OR_ACQUISITION_ASSET_SALES_PAST_24_MONTHS"
    OFFICERS_CHANGES_PAST_24_MONTHS = "OFFICERS_CHANGES_PAST_24_MONTHS"
    # End of MANAGEMENT LIABILITY TYPES

    # LIQUOR LIABILITY
    LIQUOR_LIMIT_AGGREGATE = "LIQUOR_LIMIT_AGGREGATE"
    LIQUOR_LIMIT_PER_OCCURRENCE = "LIQUOR_LIMIT_PER_OCCURRENCE"
    HAS_EMPLOYEE_ALCOHOL_TRAINING = "HAS_EMPLOYEE_ALCOHOL_TRAINING"
    ALCOHOL_TRAINING_PROGRAM = "ALCOHOL_TRAINING_PROGRAM"
    HAS_ARMED_SECURITY = "HAS_ARMED_SECURITY"
    HAS_LIQUOR_LICENSE = "HAS_LIQUOR_LICENSE"
    LIQUOR_SALES_PERCENTAGE = "LIQUOR_SALES_PERCENTAGE"
    HAS_LIQUOR_VIOLATIONS = "HAS_LIQUOR_VIOLATIONS"
    LIQUOR_LIABILITY_CANCELLED = "LIQUOR_LIABILITY_CANCELLED"
    LIQUOR_LIABILITY_ASSAULT_EXCLUSION = "LIQUOR_LIABILITY_ASSAULT_EXCLUSION"
    ASSAULT_EXCLUSION = "ASSAULT_EXCLUSION"
    NUMBER_OF_LIQUOR_SERVERS = "NUMBER_OF_LIQUOR_SERVERS"
    NUMBER_OF_CERTIFIED_SERVERS = "NUMBER_OF_CERTIFIED_SERVERS"
    # End of LIQUOR LIABILITY

    LOW_INCOME_HOUSING_UNITS_PERCENTAGE = "LOW_INCOME_HOUSING_UNITS_PERCENTAGE"

    # Auto Dealers
    SELLS_ATVS_SNOWMOBILES_MOTORHOMES_OR_AMPHIBIOUS_VEHICLES = (
        "SELLS_ATVS_SNOWMOBILES_MOTORHOMES_OR_AMPHIBIOUS_VEHICLES"
    )
    SELLS_RVS = "SELLS_RVS"
    SELLS_MOTORCYCLES = "SELLS_MOTORCYCLES"
    SELLS_WATERCRAFTS_OR_BOATS = "SELLS_WATERCRAFTS_OR_BOATS"
    SELLS_OR_SERVICES_TRUCKS = "SELLS_OR_SERVICES_TRUCKS"
    SELLS_VEHICLES_THAT_SEAT_15_OR_MORE_PASSENGERS = "SELLS_VEHICLES_THAT_SEAT_15_OR_MORE_PASSENGERS"
    SELLS_EQUIPMENT = "SELLS_EQUIPMENT"
    TOWING_FOR_HIRE = "TOWING_FOR_HIRE"
    SELLS_EXOTIC_CARS = "SELLS_EXOTIC_CARS"
    SELLS_PERFORMANCE_CARS = "SELLS_PERFORMANCE_CARS"
    # End of Auto Dealers

    LINE_OF_BUSINESS = "LINE_OF_BUSINESS"

    PRIMARY_NAICS_CODE = "PRIMARY_NAICS_CODE"

    BENEFIT_PLANS = "BENEFIT_PLANS"
    """
    The FactSubtype: BENEFIT_PLANS of FactTypeID: Relationship. Models relation between ParentType: SUBMISSION
    and ParentType: ERISA_PLAN, where the ERISA_PLAN is a benefit plan included in the SUBMISSION.
    SUBMISSION id is observation parent_id and ERISA_PLAN ids are remote_id of relationship records.
    """

    TRANSACTION = "TRANSACTION"
    """
    The FactSubtype: TRANSACTION of FactTypeID: Relationship. Models relation between ParentType: SUBMISSION
    and ParentType: TRANSACTION, where the TRANSACTION is the transaction that the submission is requesting insurance
    for. SUBMISSION id is observation parent_id and TRANSACTION ids are remote_id of relationship records.
    """

    # Some for Bishop
    HAS_BEER_GARDEN = "HAS_BEER_GARDEN"
    HAS_HARD_LIQUOR = "HAS_HARD_LIQUOR"
    HAS_BUFFET = "HAS_BUFFET"
    HAS_BANQUET_HALL = "HAS_BANQUET_HALL"
    IS_MOBILE_FOOD_BUSINESS = "IS_MOBILE_FOOD_BUSINESS"
    IS_BREW_PUB = "IS_BREW_PUB"
    HAS_DRIVE_THRU = "HAS_DRIVE_THRU"
    IS_PROPERTY_FOR_SALE = "IS_PROPERTY_FOR_SALE"

    # Begin ERISA
    ERISA_PLAN_NAME = "ERISA_PLAN_NAME"
    ERISA_PLAN_TYPE = "ERISA_PLAN_TYPE"
    ERISA_PLAN_NUMBER = "ERISA_PLAN_NUMBER"
    ERISA_PLAN_ASSETS = "ERISA_PLAN_ASSETS"
    ERISA_LATEST_FYE_ANNUAL_CONTRIBUTIONS = "ERISA_LATEST_FYE_ANNUAL_CONTRIBUTIONS"
    ERISA_CURRENT_PARTICIPANTS = "ERISA_CURRENT_PARTICIPANTS"
    ERISA_TOTAL_PARTICIPANTS = "ERISA_TOTAL_PARTICIPANTS"
    ERISA_YEAR_ESTABLISHED = "ERISA_YEAR_ESTABLISHED"
    ERISA_PLAN_STATUS = "ERISA_PLAN_STATUS"
    ERISA_PLAN_QUALIFIED = "ERISA_PLAN_QUALIFIED"
    # End ERISA

    # begin IFTA miles
    TOTAL_IFTA_MILES = "TOTAL_IFTA_MILES"
    TOTAL_US_IFTA_MILES = "TOTAL_US_IFTA_MILES"
    TOTAL_CANADA_IFTA_MILES = "TOTAL_CANADA_IFTA_MILES"
    IFTA_MILES_AL = "IFTA_MILES_AL"
    IFTA_MILES_AZ = "IFTA_MILES_AZ"
    IFTA_MILES_AR = "IFTA_MILES_AR"
    IFTA_MILES_CA = "IFTA_MILES_CA"
    IFTA_MILES_CZ = "IFTA_MILES_CZ"
    IFTA_MILES_CO = "IFTA_MILES_CO"
    IFTA_MILES_CT = "IFTA_MILES_CT"
    IFTA_MILES_DE = "IFTA_MILES_DE"
    IFTA_MILES_FL = "IFTA_MILES_FL"
    IFTA_MILES_GA = "IFTA_MILES_GA"
    IFTA_MILES_GU = "IFTA_MILES_GU"
    IFTA_MILES_ID = "IFTA_MILES_ID"
    IFTA_MILES_IL = "IFTA_MILES_IL"
    IFTA_MILES_IN = "IFTA_MILES_IN"
    IFTA_MILES_IA = "IFTA_MILES_IA"
    IFTA_MILES_KS = "IFTA_MILES_KS"
    IFTA_MILES_KY = "IFTA_MILES_KY"
    IFTA_MILES_LA = "IFTA_MILES_LA"
    IFTA_MILES_ME = "IFTA_MILES_ME"
    IFTA_MILES_MD = "IFTA_MILES_MD"
    IFTA_MILES_MA = "IFTA_MILES_MA"
    IFTA_MILES_MI = "IFTA_MILES_MI"
    IFTA_MILES_MN = "IFTA_MILES_MN"
    IFTA_MILES_MS = "IFTA_MILES_MS"
    IFTA_MILES_MO = "IFTA_MILES_MO"
    IFTA_MILES_MT = "IFTA_MILES_MT"
    IFTA_MILES_NE = "IFTA_MILES_NE"
    IFTA_MILES_NV = "IFTA_MILES_NV"
    IFTA_MILES_NH = "IFTA_MILES_NH"
    IFTA_MILES_NJ = "IFTA_MILES_NJ"
    IFTA_MILES_NM = "IFTA_MILES_NM"
    IFTA_MILES_NY = "IFTA_MILES_NY"
    IFTA_MILES_NC = "IFTA_MILES_NC"
    IFTA_MILES_ND = "IFTA_MILES_ND"
    IFTA_MILES_OH = "IFTA_MILES_OH"
    IFTA_MILES_OK = "IFTA_MILES_OK"
    IFTA_MILES_OR = "IFTA_MILES_OR"
    IFTA_MILES_PA = "IFTA_MILES_PA"
    IFTA_MILES_PR = "IFTA_MILES_PR"
    IFTA_MILES_RI = "IFTA_MILES_RI"
    IFTA_MILES_SC = "IFTA_MILES_SC"
    IFTA_MILES_SD = "IFTA_MILES_SD"
    IFTA_MILES_TN = "IFTA_MILES_TN"
    IFTA_MILES_TX = "IFTA_MILES_TX"
    IFTA_MILES_UT = "IFTA_MILES_UT"
    IFTA_MILES_VT = "IFTA_MILES_VT"
    IFTA_MILES_VA = "IFTA_MILES_VA"
    IFTA_MILES_VI = "IFTA_MILES_VI"
    IFTA_MILES_WA = "IFTA_MILES_WA"
    IFTA_MILES_WV = "IFTA_MILES_WV"
    IFTA_MILES_WI = "IFTA_MILES_WI"
    IFTA_MILES_WY = "IFTA_MILES_WY"
    IFTA_MILES_AB = "IFTA_MILES_AB"
    IFTA_MILES_BC = "IFTA_MILES_BC"
    IFTA_MILES_MB = "IFTA_MILES_MB"
    IFTA_MILES_NB = "IFTA_MILES_NB"
    IFTA_MILES_NL = "IFTA_MILES_NL"
    IFTA_MILES_NS = "IFTA_MILES_NS"
    IFTA_MILES_ON = "IFTA_MILES_ON"
    IFTA_MILES_PE = "IFTA_MILES_PE"
    IFTA_MILES_QC = "IFTA_MILES_QC"
    IFTA_MILES_SK = "IFTA_MILES_SK"
    # end IFTA miles

    # begin iso codes
    ISO_CODES = "ISO_CODES"
    ISO9000 = "ISO9000"
    ISO14001_2015 = "ISO14001_2015"
    ISO14644_4_2001 = "ISO14644_4_2001"
    ISO14644_15_2017 = "ISO14644_15_2017"
    ISO50001 = "ISO50001"
    ISO14644_7_2004 = "ISO14644_7_2004"
    ISO14644_8_2013 = "ISO14644_8_2013"
    ISO10328_2016 = "ISO10328_2016"
    ISO14644_2_2015 = "ISO14644_2_2015"
    ISO13485 = "ISO13485"
    ISOTS29001_2007 = "ISOTS29001_2007"
    ISO22000_2005 = "ISO22000_2005"
    ISOTS16949_2002 = "ISOTS16949_2002"
    ISO14644_10_2013 = "ISO14644_10_2013"
    ISO9001_2008 = "ISO9001_2008"
    ISO14644_14_2016 = "ISO14644_14_2016"
    ISO14644_13_2017 = "ISO14644_13_2017"
    ISO50001_2011 = "ISO50001_2011"
    ISO14001_2004 = "ISO14001_2004"
    ISO14644_5_2004 = "ISO14644_5_2004"
    ISOTS16949_2009 = "ISOTS16949_2009"
    ISO14644_9_2012 = "ISO14644_9_2012"
    ISO9002 = "ISO9002"
    ISO22716_2007 = "ISO22716_2007"
    ISO9001 = "ISO9001"
    ISO13485_2016 = "ISO13485_2016"
    ISO14644_3_2019 = "ISO14644_3_2019"
    ISO14001 = "ISO14001"
    ISO17034_2016 = "ISO17034_2016"
    ISOTS29001 = "ISOTS29001"
    ISOTS29001_2010 = "ISOTS29001_2010"
    ISO9003 = "ISO9003"
    ISO14644_12_2018 = "ISO14644_12_2018"
    ISO45001 = "ISO45001"
    ISO9000_2001 = "ISO9000_2001"
    ISO14644_16_2019 = "ISO14644_16_2019"
    ISO14644_1_2015 = "ISO14644_1_2015"
    ISO9000_2000 = "ISO9000_2000"
    ISO14644_6_2007 = "ISO14644_6_2007"
    ISO9001_2015 = "ISO9001_2015"
    ISO9004 = "ISO9004"
    ISO14000 = "ISO14000"
    ISO13485_2003 = "ISO13485_2003"
    ISOTS16949 = "ISOTS16949"
    ISO45001_2018 = "ISO45001_2018"
    # end of iso codes

    # begin asme codes
    ASME_CODES = "ASME_CODES"
    ASME = "ASME"
    """
    Generic representation for ASME certificates without details
    """
    ASME_BPE = "ASME_BPE"
    """
    ASME Bioprocessing Equipment (BPE) Certification is a company level certification for organizations manufacturing or
    supplying tubing and fittings under the scope of the ASME standard "Bioprocessing Equipment", which dictates specific
    conditions for design, materials, construction, surface finish and inspection. This certification is intended for
    companies that manufacture or supply tubing and fittings for the bioprocessing, pharmaceutical, and personal-care
    products industries, as well as other applications with relatively high levels of hygienic requirements. Certification
    entails a review/survey of a company's quality manual and implementation of their quality program. The review/survey
    is performed by ASME consultants experienced in the field of quality assurance and trained in understanding the
    technical and quality requirements of the BPE Standard.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/asme/asme-bpe/
    """
    ASME_BPVC = "ASME_BPVC"
    """
    ASME Boiler and Pressure Vessel Certification is a company level certification of a manufacturer's or assembler's quality
    control system in accordance with the American Society of Mechanical Engineers (ASME) Boiler and Pressure Vessel Code (BPVC)
    Sections I, IV, VIII, X, and/or XII. This certification is for companies who are involved in the design, fabrication,
    assembly, and inspection of boiler and pressure vessel components during construction. Pressure equipment includes products
    such as power boilers, heating boilers, pressure vessels, fiber-reinforced plastic vessels, transport tanks and valves.
    Certification entails a review/survey of a company's quality manual and implementation of their quality program. The
    review/survey is performed by inspection agencies authorized by ASME.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/asme/asme-bpvc/
    """
    ASME_MO = "ASME_MO"
    """
    ASME Nuclear Material Organization (ASME MO) is a company level certification of a manufacturer or supplier's quality system
    in accordance with Section III of ASME Boiler and Pressure Code. This quality system certification program provides assurance
    that the organization's operations, processes, and services related to the procurement, manufacture, and supply of material,
    source material, and unqualified source material are performed in accordance with the requirements of the
    ASME BPVC, Section III, NCA-3800 and NCA-3900.
    This certification is intended for companies who provide materials and services to the nuclear power industry.
    Certification entails a review/survey of a company's quality manual and implementation of their quality program.
    The review/survey is performed by two ASME consultants experienced in the field of quality assurance in nuclear systems, components, and materials.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/asme/asme-mo/
    """
    ASME_N_TYPE = "ASME_N_TYPE"
    """
    ASME Nuclear Component (N-type) Certification is a company level certification of an organization's quality assurance
    program in accordance with Section III of the ASME Boiler and Pressure Vessel Code (BPVC) for components installed in
    nuclear facilities. This certification is intended for companies who design, fabricate, and install components and
    supports used in nuclear power plants and facilities. ASME issues six different N-type certificates which authorizes
    the following scope of activities: N - Vessels, pumps, valves, piping system, and storage tanks, NA - Field installation
    and shop assembly of all items, NPT - Parts, appurtenances, welded tubular products, and piping subassemblies,
    NS ? Support, NV - Pressure relief valves, N3 - Transportation containments and storage containments, and
    OWN - Nuclear power plant owner. Certification entails a review/survey of a company's quality manual and implementation
    of their quality program. The review/survey is performed by ASME consultants, an accredited authorized inspection agency,
    and a representative of the Enforcement Authority in Jurisdictions, where applicable.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/asme/asme-n-type/
    """
    ASME_NQA_1 = "ASME_NQA_1"
    """
    ASME Nuclear Quality Assurance (NQA-1) is a company level certification based on a standard published by the
    American Society of Mechanical Engineers (ASME) titled "Quality Assurance Requirements for Nuclear Facility Applications".
    Companies wishing to become certified will be evaluated against this standard.
    This certification is for companies involved in the nuclear industry who are committed to understanding quality and
    providing high quality products and services to meet the needs of the industry.
    Certification entails a full audit of a company's Quality Assurance Program performed by trained ASME auditors with
    an extensive background in quality assurance.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/asme/asme-nqa-1/
    """
    ASME_RTP = "ASME_RTP"
    """
    ASME Reinforced Thermoset Plastic (RTP) Corrosion-Resistant Equipment Certification is a company level certification
    of a fabricator's quality control system in accordance with the ASME RTP-1 standard "Reinforced Thermoset Plastic Corrosion-Resistant Equipment".
    The standard applies to stationary vessels used for the storage, accumulation, or processing of corrosive or other
    substances at pressures not exceeding 15 psig external and/or 15 psig internal. This certification is intended for
    companies who manufacture reinforced thermosetting plastic storage tanks and process vessels for holding corrosive
    and otherwise hazardous materials used in a variety of industries, including chemical processing, water and wastewater,
    metal and mining, electronics, power generating, and oil and gas production. Certification entails a review/survey
    of a company's quality manual and implementation of their quality program. The review/survey is performed by two
    ASME consultants experience in the field of quality assurance.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/asme/asme-rtp/
    """
    # end of asme codes

    # start of ansi codes
    ANSI_CODES = "ANSI_CODES"
    ANSI = "ANSI"
    """
    Generic representation for ANSI certificates without details
    """
    ANSI_NCSL_Z540 = "ANSI_NCSL_Z540"
    """
    ANSI/NCSL Z540 may refer to either ANSI/NCSL Z540.1 or ANSI/NCSL Z540.3, both of which are standards facilitated by
    the American National Standards Institute (ANSI) and the National Conference of Standards Laboratories (NCSL).
    ANSI/NCSL Z540.1 has been replaced by two separate standards-ISO/IEC 17025 and ANSI/NCSL Z540.3.
    ANSI/NCSL Z540.1 was intended for calibration laboratories. ANSI/NCSL Z540.3 gives requirements for the calibration
    of an organization's equipment when used in the manufacturing, modification or testing of products.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/ansi_ncsl/ansi_ncsl-z540/
    """
    ANSI_ESD_S20_20_2007 = "ANSI_ESD_S20_20_2007"
    """
    ANSI/ESD S20.20-2007 is a company level certification based on a standard developed by the Electrostatic Discharge
    Association (ESDA) titled “Protection of Electrical and Electronic Parts, Assemblies and Equipment (Excluding Electrically
    Initiated Explosive Devices)”. This certification is for companies that design, manufacture, package, test and/or
    handle electronic devices or equipment sensitive to electrostatic discharge.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/ansi/
    """
    ANSI_ESD_S20_20_2014 = "ANSI_ESD_S20_20_2014"
    """
    ANSI/ESD S20.20-2014 is a company-level certification based on the Electrostatic Discharge Association's (ESDA) standard
    titled, "Protection of Electrical and Electronic Parts, Assemblies and Equipment (Excluding Electrically Initiated
    Explosive Devices)". This certification is intended for companies that design, manufacture, package, test, and/or
    handle electronic devices or equipment. Certifications are issued by third party certifying bodies authorized by ESDA
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/ansi/
    """
    ANSI_MSE_50021_2013 = "ANSI_MSE_50021_2013"
    """
    ANSI/MSE 50021:2013 is a company-level standard that outlines energy management requirements from the 2012 Superior
    Energy Performance (SEP) program that go beyond ISO 50001. Certifications are issued by third party certifying bodies.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/ansi/
    """
    # end of ansi codes

    # other types of business certificates
    OTHER_CERTIFICATES = "OTHER_CERTIFICATES"
    ACC = "ACC"
    """
    The American Chemistry Council (ACC) is a trade association representing companies engaged in the business of chemistry.
    The association's mission is to deliver business value through advocacy using members, political engagement, communications
    and scientific research.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    AISC = "AISC"
    """
    The American Institute of Steel Construction (AISC) is a nonprofit technical institute and trade association for the
    structural steel design and construction industry in the U.S. The AISC develops specifications, codes, research, education,
    quality certification, standardization, and market development for structural steel applications. AISC members consist
    of industry professionals in steel fabrication, milling, equipment manufacturing, contracting, engineering, architecture,
    educators, and students.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    API = "API"
    """
    The American Petroleum Institute (API) is a trade association that creates standards for the development of petroleum
    and petrochemical equipment and operation, and offers product, quality, company, and individual certifications to oil
    and gas industry standards.  API is comprised of 600 U.S. corporate members active in various areas of oil production
    and transportation, including producers, refiners, suppliers, pipeline operators, as well as service and supply companies.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    IATF = "IATF"
    """
    The International Automotive Task Force (IATF) is an industry organization made up of automotive manufacturers. The
    IATF works to improve automotive products by developing production requirements, policies, and training procedures.
    Quality system requirements are established by consensus for suppliers of materials, products, parts, and finishing services.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    QS = "QS"
    """
    The QS inspection system from QS Qualität und Sicherheit GmbH for foodstuff quality is one of the leading quality
    control systems for meat and meat products, fruit, vegetables, and animal feed. It monitors quality throughout all
    production stages - from production to processing and retail.
    https://www.tuv.com/world/en/qs-certification.html
    """
    CEN = "CEN"
    """
    The European Committee for Standardization (CEN), is a collaborative association of the National Standardization Bodies
    of 33 European countries. CEN is a European standardization organization responsible for developing and defining voluntary
    standards for the European Union. CEN supports development of European standards and standardization activities in
    relation to a wide range of fields and sectors including: air and space, construction, defense and security, energy,
    environmental health and safety, healthcare, transportation and packaging.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    CSA = "CSA"
    """
    The Canadian Standards Association (CSA) is a standards development and certification organization accredited in both
    Canada and the U.S. The CSA is comprised of committee members and consumer representatives from industry, nonprofit,
    academic, government, and general interest organizations. The CSA develops standards for the Standards Council of Canada
    and also provides product certification to standards written or administered by the American National Standards Institute (ANSI),
    Underwriters Laboratories (UL), NSF International, and other North American organizations
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    EASA = "EASA"
    """
    The European Aviation Safety Agency (EASA) is an organization of the European Union that develops standards for civilian
    aviation safety. The EASA creates aviation safety legislation, conducts inspections and training for implementation
    of European aviation safety legislation, approves aircraft design organizations and works to harmonize aviation safety
    standards internationally. The EASA consists of several branches, including a certification directorate, flight standards
    directorate, resource and support directorate, and strategy and safety directorate. All branches are governed by a
    team of executive directors.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    NADCAP = "NADCAP"
    """
    Nadcap is a worldwide cooperative accreditation program of major companies that manage specific processes in the aerospace
    and automotive industries. In particular, the cooperative works to improve quality in the aerospace and defense industries
    by developing cost effective approaches to these specific processes.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    NBBI = "NBBI"
    """
    NBBI is a membership organization that ensures adherence to laws, rules, and regulations relating to pressure equipment
    such as boilers and pressure vessels. The National Board members are the chief boiler inspectors for most cities,
    states, and provinces in the United States and Canada. The National Board's primary functions include developing standards
    for the installation, inspection, repair, and alteration of pressure equipment, accrediting qualified repair and
    alteration companies, and setting global industry standards for pressure relief devices.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    NELAC = "NELAC"
    """
    The NELAC Institute (TNI) is a non-profit organization of state and federal officials formed to establish and promote
    mutually acceptable performance standards for the operation of environmental laboratories. These performance standards
    apply to both analytical testing of environmental samples and the laboratory accreditation process. Accreditation
    standards are developed by Expert Committees using a consensus process. Standards are developed in conformance with
    TNI's Procedures Governing Standards Development. The Expert Committees develop a working draft standard that is presented
    to the membership and the public at the TNI Forum on Laboratory Accreditation. The Expert Committees modify their
    working draft as a result of any input received during and after the forum, then produce a Voting Draft Standard.
    All TNI members may vote electronically with the option to include comments. The Expert Committees then allow for
    public debate on every comment at the next forum and also hold meetings to rule on each comment and publish their
    response to those comments. All comments must be resolved and the draft will be revised if necessary as a result of
    these comments, then the draft will need to be approved by a majority vote of the Committee Members; at this point,
    the standard then becomes final as the TNI Standard. Finally, the standard undergoes an editorial review for consistency
    and then is published on the TNI website with a 30 day time frame to allow for any appeals. The standards are available
    for adoption once any appeals are resolved.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    NFFS = "NFFS"
    """
    The Non-Ferrous Founders? Society (NFFS) is an industry trade association representing aluminum, brass and bronze foundries
    and ingot manufacturers. The association is made up of member foundries, not individuals as the association focuses
    on services at the organizational level. NFFS aims to help foundries simplify regulatory compliance activities, keep
    abreast of new technology and government regulations, reduce operating expenses and improve plant profitability.
    NFFS developed NQS 9000, an industry specific ISO 9000 compliant quality program for foundries.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    PRI = "PRI"
    """
    The Performance Review Institute (PRI) is a not-for-profit affiliate of SAE International. PRI provides collaborative
    supply chain oversight programs, quality management systems approvals and professional development.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    SAE = "SAE"
    """
    SAE International is a global association of engineers and technical experts in the aerospace, automotive and
    commercial-vehicle industries. One of SAE's core competencies is in the development of voluntary consensus standards.
    SAE technical reports, which include SAE standards, SAE recommended practices, SAE Information Reports and SAE Aerospace
    Material Specifications are developed by SAE's technical committees, consisting of technical experts from government,
    industry, regulatory agencies and academia. SAE technical committees are responsible for the preparation, development
    and maintenance of all relevant technical reports within their scope. A designated sponsor for these reports serves
    as the focal point within the committee for activities associated with the development of the technical report,
    including preparing drafts and resolving all comments received during the approval process. For these technical reports
    to be approved, the sponsor submits a draft to SAE. Committee members vote and provide comments on the document; the
    sponsor will attempt to resolve all comments from committee members. The report then goes to the governing body of the
    initiating committee for a process level review. Once approved by this governing body, SAE will publish the technical report.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    SQFI = "SQFI"
    """
    SQF Institute is an organization responsible for writing and maintaining food safety and quality program standards.
    The company is a division of the Food Marketing Institute (FMI). SQFI also develops training materials and education
    resources for food safety.
    https://certifications.thomasnet.com/certifications/glossary/quality-certifications/
    """
    HAACP = "HAACP"
    """
    HACCP is a management system in which food safety is addressed through the analysis and control of biological, chemical,
    and physical hazards from raw material production, procurement and handling, to manufacturing, distribution and
    consumption of the finished product. For successful implementation of a HACCP plan, management must be strongly committed
    to the HACCP concept. A firm commitment to HACCP by top management provides company employees with a sense of the
    importance of producing safe food.
    https://www.fda.gov/food/hazard-analysis-critical-control-point-haccp/haccp-principles-application-guidelines
    """
    GMP = "GMP"
    """
    Good Manufacturing Practices (GMP) or Current Good Manufacturing Practices (CGMP) are regulations established by the
    U.S Food & Drug Administration targeting the Food, Dietary Supplement, Pharmaceutical, and Cosmetics industries under
    the authority of the Federal Food, Drug, and Cosmetic Act. GMP Certifications may be issued by private third-party
    organizations which help manufacturers adhere to standards and regulations.
    Current Good Manufacturing Practices (CGMP) regulations generally address matters including appropriate personal hygienic
    practices, design and construction of a food plant and maintenance of plant grounds, plant equipment, sanitary operations,
    facility sanitation, and production and process controls during the production of food. The FDA does not require or
    administer GMP Certification.
    https://certifications.thomasnet.com/certifications/glossary/other-certification_registration/fda/good-manufacturing-practices-gmp/
    """

    # end of other business certificates

    # begin of CNA fact subtypes
    ADULT_DAY_CARE = "ADULT_DAY_CARE"
    CONCIERGE_MEDICINE = "CONCIERGE_MEDICINE"
    DETOX_WITH_ANESTHESIA = "DETOX_WITH_ANESTHESIA"
    ERECTILE_DYSFUNCTION_CLINIC = "ERECTILE_DYSFUNCTION_CLINIC"
    INSTITUTIONAL_REVIEW_BOARD = "INSTITUTIONAL_REVIEW_BOARD"
    INTRAOPERATIVE_NEUROMONITORING = "INTRAOPERATIVE_NEUROMONITORING"
    OIL_RIG_MEDICAL = "OIL_RIG_MEDICAL"
    OFF_LABEL_DRUGS = "OFF_LABEL_DRUGS"
    LETHAL_INJECTION_COMPOUNDS = "LETHAL_INJECTION_COMPOUNDS"
    PRENATAL_ULTRASOUND = "PRENATAL_ULTRASOUND"
    INTERVENTIONAL_PAIN_MANAGEMENT = "INTERVENTIONAL_PAIN_MANAGEMENT"
    NEUROSURGERY_SERVICE = "NEUROSURGERY_SERVICE"
    ORTHOPEDIC_SURGERY_SERVICE = "ORTHOPEDIC_SURGERY_SERVICE"
    CORD_BLOOD_BANK = "CORD_BLOOD_BANK"
    CORONER_SERVICE = "CORONER_SERVICE"
    CORRECTIONAL_FACILITY_MEDICINE = "CORRECTIONAL_FACILITY_MEDICINE"
    PERCENT_CORRECTIONAL_FACILITY_MEDICINE = "PERCENT_CORRECTIONAL_FACILITY_MEDICINE"
    ALTERNATIVE_MEDICINE = "ALTERNATIVE_MEDICINE"
    DURABLE_MEDICAL_EQUIPMENT = "DURABLE_MEDICAL_EQUIPMENT"
    DURABLE_MEDICAL_EQUIPMENT_SALES = "DURABLE_MEDICAL_EQUIPMENT_SALES"
    DURABLE_MEDICAL_EQUIPMENT_PERCENTAGE = "DURABLE_MEDICAL_EQUIPMENT_PERCENTAGE"
    FRAUD_CONVICTION = "FRAUD_CONVICTION"
    ALTERNATIVE_MEDICINE_PERCENTAGE = "ALTERNATIVE_MEDICINE_PERCENTAGE"
    HAS_PATIENTS_ON_VENTILATORS = "HAS_PATIENTS_ON_VENTILATORS"
    PERCENT_PATIENTS_ON_VENTILATORS = "PERCENT_PATIENTS_ON_VENTILATORS"
    MEDICALLY_FRAGILE_INFANTS = "MEDICALLY_FRAGILE_INFANTS"
    OCULAR_LABORATORY = "OCULAR_LABORATORY"
    MEDICAL_MANAGEMENT_COMPANY = "MEDICAL_MANAGEMENT_COMPANY"
    TOTAL_NUMBER_OF_PATIENTS = "TOTAL_NUMBER_OF_PATIENTS"
    TOTAL_NUMBER_OF_VISITS = "TOTAL_NUMBER_OF_VISITS"
    OVERNIGHT_RESIDENTIAL_SERVICES = "OVERNIGHT_RESIDENTIAL_SERVICES"
    FOREIGN_CLINICAL_OPERATIONS = "FOREIGN_CLINICAL_OPERATIONS"
    # end of CNA fact subtypes

    # Demo Lawyer fact subtypes#
    NUMBER_OF_ATTORNEYS = "NUMBER_OF_ATTORNEYS"
    AREAS_OF_PRACTICE = "AREAS_OF_PRACTICE"
    DOCKET_SYSTEM = "DOCKET_SYSTEM"
    HAS_CLIENT_EQUITY = "HAS_CLIENT_EQUITY"
    CLASS_ACTION = "CLASS_ACTION"
    ENGAGEMENT_LETTERS = "ENGAGEMENT_LETTERS"
    CONFLICT_OF_INTEREST_SYSTEM = "CONFLICT_OF_INTEREST_SYSTEM"
    HAS_HIGH_CLIENT_CONCENTRATION = "HAS_HIGH_CLIENT_CONCENTRATION"
    SUES_CLIENTS = "SUES_CLIENTS"

    # begin of ACORD 160 fact subtypes
    MIXING_PRODUCTS_FROM_OTHERS = "MIXING_PRODUCTS_FROM_OTHERS"
    AFTER_HOURS_OR_24H_OPERATIONS = "AFTER_HOURS_OR_24H_OPERATIONS"
    DEVELOPER_OR_CONTRACTOR_ON_BOARD = "DEVELOPER_OR_CONTRACTOR_ON_BOARD"
    HAS_PROPERTY_MANAGER = "HAS_PROPERTY_MANAGER"
    # end of ACORD 160 fact subtypes

    # begin of 990 Form fact subtypes

    HAS_990_FORM = "HAS_990_FORM"
    """
    Related to SourceTypeID.IRS_FORM_990 https://www.irs.gov/charities-non-profits/form-990-series-downloads
    Most tax-exempt organizations are required to file an annual return (Form 990, 990-EZ, 990-PF, 990-N, ...)
    """
    TAX_EXEMPT_STATUS = "TAX_EXEMPT_STATUS"
    """
    See https://www.irs.gov/charities-non-profits/other-tax-exempt-organizations
    """
    GOVERNING_BODY_VOTING_MEMBER_COUNT = "GOVERNING_BODY_VOTING_MEMBER_COUNT"
    INDEPENDENT_VOTING_MEMBER_COUNT = "INDEPENDENT_VOTING_MEMBER_COUNT"
    CONSISTENT_DESCRIPTION_OF_OPERATIONS = "CONSISTENT_DESCRIPTION_OF_OPERATIONS"
    """
    Generated based on first party data facts and 990 Form data. Not provided directly.
    Indicates if the description of operations has been consistent across those data points
    """

    # end of 990 Form fact subtypes

    # ----- DEPRECATED FACT SUBTYPES --------
    # -----     ⚰️   R.I.P.   ⚰️     --------
    # ---------------------------------------

    BROKER_TRANSPORTATION_LICENSE = "BROKER_TRANSPORTATION_LICENSE"
    """Deprecated. Replaced by document and TRANSPORTATION_HAS_BROKER_LICENSE"""
    CAN_TRANSPORT_HAZARDOUS_MATERIALS = "CAN_TRANSPORT_HAZARDOUS_MATERIALS"
    """Deprecated. Replaced by TRANSPORTATION_TRANSPORTS_HAZARDOUS_MATERIALS"""
    CAN_TRANSPORT_HOUSEHOLD_GOODS = "CAN_TRANSPORT_HOUSEHOLD_GOODS"
    """Deprecated. Replaced by TRANSPORTATION_TRANSPORTS_HOUSEHOLD_GOODS"""
    CAN_TRANSPORT_PASSENGERS = "CAN_TRANSPORT_PASSENGERS"
    """Deprecated. Replaced by TRANSPORTATION_TRANSPORTS_PASSENGERS"""
    DRIVER_FITNESS_RATING = "DRIVER_FITNESS_RATING"
    """Deprecated. Replaced by TRANSPORTATION_DRIVER_FITNESS_MEASURE_CLOSE_OR_ABOVE_LIMIT"""
    DRIVER_OOS_RATE = "DRIVER_OOS_RATE"
    """Deprecated. Replaced by TRANSPORTATION_DRIVER_OOS_RATE_EXCEEDS_NATIONAL_AVERAGE"""
    HAZMAT_OOS_RATE = "HAZMAT_OOS_RATE"
    """Deprecated. Replaced by TRANSPORTATION_HAZMAT_OOS_RATE_EXCEEDS_NATIONAL_AVERAGE"""
    HOURS_OF_SERVICE_COMPLIANCE_RATING = "HOURS_OF_SERVICE_COMPLIANCE_RATING"
    """Deprecated. Replaced by TRANSPORTATION_HOURS_OF_SERVICE_ACUTE_VIOLATIONS"""
    HOUSEHOLD_GOODS_TRANSPORTATION_LICENSE = "HOUSEHOLD_GOODS_TRANSPORTATION_LICENSE"
    """Deprecated. Replaced by document and TRANSPORTATION_TRANSPORTS_HOUSEHOLD_GOODS"""
    NUMBER_OF_FLOORS = "NUMBER_OF_FLOORS"
    """Deprecated. Replaced by NUMBER_OF_STORIES"""
    PASSENGER_TRANSPORTATION_LICENSE = "PASSENGER_TRANSPORTATION_LICENSE"
    """Deprecated. Replaced by document and TRANSPORTATION_TRANSPORTS_PASSENGERS"""
    PROPERTY_TRANSPORTATION_LICENSE = "PROPERTY_TRANSPORTATION_LICENSE"
    """Deprecated. Replaced by document and TRANSPORTATION_TRANSPORTS_GOODS_FOR_OTHERS"""
    SQUARE_FEET = "SQUARE_FEET"
    """Deprecated. Replaced by BUILDING_SIZE"""
    TOTAL_TIV = "TOTAL_TIV"
    """Deprecated. Replaced by TIV"""
    TRANSPORTATION_ALCOHOL_RATING = "TRANSPORTATION_ALCOHOL_RATING"
    """Deprecated. Replaced by TRANSPORTATION_ALCOHOL_AND_DRUGS_ACUTE_VIOLATIONS"""
    UNSAFE_DRIVING_RATING = "UNSAFE_DRIVING_RATING"
    """Deprecated. Replaced by TRANSPORTATION_UNSAFE_DRIVING_MEASURE_CLOSE_OR_ABOVE_LIMIT"""
    VEHICLE_MAINTENANCE_RATING = "VEHICLE_MAINTENANCE_RATING"
    """Deprecated. Replaced by TRANSPORTATION_VEHICLE_MAINTENANCE_ACUTE_VIOLATIONS"""
    VEHICLE_OOS_RATE = "VEHICLE_OOS_RATE"
    """Deprecated. Replaced by TRANSPORTATION_VEHICLE_OOS_SERVICE_RATE_EXCEEDS_NATIONAL_AVERAGE"""
    VEHICLES_FROM_3RD_PARTY = "VEHICLES_FROM_3RD_PARTY"
    """Deprecated. Replaced by RelationshipDiscoveredInType"""
    PERMIT_TYPE = "PERMIT_TYPE"
    """Deprecated. Replaced by document"""
    MANUFACTURING_TYPES = "MANUFACTURING_TYPES"
    """Deprecated. Removed without replacement."""
    HAS_STANDARD_CONTRACT_FORM_IN_USE = "HAS_STANDARD_CONTRACT_FORM_IN_USE"
    """Deprecated. Removed without replacement."""
    BUILDING_CONSTRUCTION = "BUILDING_CONSTRUCTION"
    """ Deprecated in favour of CONSTRUCTION_CLASS """
    MISSING_VIN_COUNT = "MISSING_VIN_COUNT"
    """ Deprecated in favour of being calculated later in the flow """

    @staticmethod
    def subtypes_with_multiple_parents() -> set[FactSubtypeID]:
        return {
            FactSubtypeID.ZIP_CODE,  # Makes sense for vehicle, driver, premises
            FactSubtypeID.OWNER,  # Makes sense for vehicle, driver, premises
            FactSubtypeID.POLICY_NUMBER,  # Makes sense for vehicle, driver, business, premises
            FactSubtypeID.TIV,  # Makes sense for structrues, equipment, premises, vehicles
        }

    @staticmethod
    def computed_subtype_map() -> dict[FactSubtypeID, list[FactSubtypeID]]:
        # key = computed subtype, value = subtypes that can be used to compute the key
        return {
            # Facts from create_computed_observations
            FactSubtypeID.YEARS_IN_BUSINESS: [
                FactSubtypeID.YEAR_FOUNDED,
                FactSubtypeID.CLOSED_AT,
            ],
            FactSubtypeID.REVENUE_FROM_PUBLIC_10_PERCENT_PLUS: [
                FactSubtypeID.REVENUE_FROM_PUBLIC_PERCENTAGE,
            ],
            FactSubtypeID.NEW_VENTURE: [FactSubtypeID.YEARS_IN_BUSINESS, FactSubtypeID.YEAR_FOUNDED],
            FactSubtypeID.YEAR_FOUNDED: [FactSubtypeID.YEARS_IN_BUSINESS],
            FactSubtypeID.DRIVER_AGE: [FactSubtypeID.DRIVER_DATE_OF_BIRTH],
            FactSubtypeID.YEAR_BUILT: [FactSubtypeID.BUILDING_AGE],
            FactSubtypeID.BUILDING_AGE: [FactSubtypeID.YEAR_BUILT],
            FactSubtypeID.DRIVER_YEARS_OF_EXPERIENCE: [FactSubtypeID.DRIVER_YEAR_STARTED_DRIVING],
            FactSubtypeID.DRIVER_YEAR_STARTED_DRIVING: [FactSubtypeID.DRIVER_YEARS_OF_EXPERIENCE],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_GC: [FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR: [FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_GC],
            FactSubtypeID.VEHICLE_INFORMATION_NUMBER: [FactSubtypeID.EQUIPMENT_SERIAL_NUMBER],
            FactSubtypeID.PROJECT_SUBCONTRACTORS_COST: [
                FactSubtypeID.PROJECT_SUBCONTRACTORS_USED,
                FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS,
            ],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS: [
                FactSubtypeID.PROJECT_SUBCONTRACTORS_USED,
                FactSubtypeID.PROJECT_SUBCONTRACTORS_COST,
            ],
            FactSubtypeID.PROJECT_SUBCONTRACTORS_USED: [
                FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS,
                FactSubtypeID.PROJECT_SUBCONTRACTORS_COST,
            ],
            # Facts from compute_methods
            # _get_number_and_binary_classification_associated_observations
            # Number to binary classification
            FactSubtypeID.CLOSING_LAYOFFS_PAST_24_MONTHS: [FactSubtypeID.LAYOFFS_EMPLOYEES_COUNT],
            FactSubtypeID.HAS_ELEVATORS: [FactSubtypeID.NUMBER_OF_ELEVATORS],
            FactSubtypeID.PARKING_PRIVATE_LOT: [FactSubtypeID.PARKING_SPACES_COUNT],
            FactSubtypeID.HAS_SECURITY_GUARDS: [FactSubtypeID.NUMBER_OF_GUARDS, FactSubtypeID.HAS_ARMED_SECURITY],
            FactSubtypeID.HAS_BASEMENT: [FactSubtypeID.NUMBER_OF_BASEMENTS],
            FactSubtypeID.HAS_POOL_FENCE: [FactSubtypeID.POOL_FENCE_HEIGHT],
            FactSubtypeID.HAS_LODGING_OPERATIONS: [FactSubtypeID.LODGING_OPERATIONS_AREA],
            # Binary classification to number
            FactSubtypeID.LAYOFFS_EMPLOYEES_COUNT: [FactSubtypeID.CLOSING_LAYOFFS_PAST_24_MONTHS],
            FactSubtypeID.NUMBER_OF_ELEVATORS: [FactSubtypeID.HAS_ELEVATORS],
            FactSubtypeID.PARKING_SPACES_COUNT: [FactSubtypeID.PARKING_PRIVATE_LOT],
            FactSubtypeID.NUMBER_OF_GUARDS: [FactSubtypeID.HAS_SECURITY_GUARDS],
            FactSubtypeID.NUMBER_OF_SWIMMING_POOLS: [FactSubtypeID.HAS_SWIMMING_POOL],
            FactSubtypeID.NUMBER_OF_BASEMENTS: [FactSubtypeID.HAS_BASEMENT],
            FactSubtypeID.POOL_FENCE_HEIGHT: [FactSubtypeID.HAS_POOL_FENCE],
            FactSubtypeID.LODGING_OPERATIONS_AREA: [FactSubtypeID.HAS_LODGING_OPERATIONS],
            FactSubtypeID.NUMBER_OF_WATERCRAFT: [FactSubtypeID.OWN_OR_OPERATE_AIRCRAFT_OR_WATERCRAFT],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_HAND_DEMOLITION: [FactSubtypeID.PROJECT_DEMOLITION_WORK],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_MECHANICAL_DEMOLITION: [FactSubtypeID.PROJECT_DEMOLITION_WORK],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_HYDRODEMOLITION: [FactSubtypeID.PROJECT_DEMOLITION_WORK],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_OTHER_DEMOLITION_METHODS: [FactSubtypeID.PROJECT_DEMOLITION_WORK],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_OTHER_ROOFING_METHODS: [FactSubtypeID.PROJECT_ROOF_WORK],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_ON_PITCHED_ROOFS: [FactSubtypeID.PROJECT_ROOF_WORK],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_ON_FLAT_ROOFS: [FactSubtypeID.PROJECT_ROOF_WORK],
            FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_ON_OTHER_ROOFS: [FactSubtypeID.PROJECT_ROOF_WORK],
            # Positive integer to binary classification
            FactSubtypeID.MEDICAL_FACILITIES_OR_PROFESSIONALS: [
                FactSubtypeID.NUMBER_OF_DOCTORS,
                FactSubtypeID.NUMBER_OF_NURSES,
                FactSubtypeID.HAS_HOSPITAL,
                FactSubtypeID.HAS_COVERAGE_FOR_DOCTORS_AND_NURSES,
            ],
            FactSubtypeID.HAS_HOSPITAL: [FactSubtypeID.NUMBER_OF_HOSPITAL_BEDS],
            FactSubtypeID.OWN_OR_OPERATE_AIRCRAFT_OR_WATERCRAFT: [FactSubtypeID.NUMBER_OF_WATERCRAFT],
            FactSubtypeID.HAS_BURGLAR_ALARM: [
                FactSubtypeID.BURGLAR_ALARM_EXTENT,
                FactSubtypeID.BURGLAR_ALARM_TYPE,
                FactSubtypeID.BURGLAR_ALARM_CERTIFICATE_NUMBER,
                FactSubtypeID.BURGLAR_ALARM_SERVICED_BY,
            ],
            FactSubtypeID.PROJECT_DEMOLITION_WORK: [
                FactSubtypeID.PROJECT_PERCENTAGE_OF_HAND_DEMOLITION,
                FactSubtypeID.PROJECT_PERCENTAGE_OF_MECHANICAL_DEMOLITION,
                FactSubtypeID.PROJECT_PERCENTAGE_OF_HYDRODEMOLITION,
                FactSubtypeID.PROJECT_PERCENTAGE_OF_OTHER_DEMOLITION_METHODS,
            ],
            FactSubtypeID.PROJECT_METAL: [FactSubtypeID.PROJECT_WELDING_WORK],
            FactSubtypeID.PROJECT_ROOF_WORK: [
                FactSubtypeID.PROJECT_PERCENTAGE_OF_OTHER_ROOFING_METHODS,
                FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_ON_PITCHED_ROOFS,
                FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_ON_FLAT_ROOFS,
                FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_ON_OTHER_ROOFS,
                FactSubtypeID.PROJECT_OTHER_ROOFING_METHODS,
            ],
            FactSubtypeID.IS_SUBSIDIARY: [FactSubtypeID.OWNED_BY_PARENT_PERCENT],
            FactSubtypeID.HAS_SUBSIDIARIES: [FactSubtypeID.OWNER_OF_SUBSIDIARY_PERCENT],
            # _get_binary_classifications_associated_observations
            # Yes propagating facts
            FactSubtypeID.HAS_SWIMMING_POOL: [
                FactSubtypeID.NUMBER_OF_SWIMMING_POOLS,
                FactSubtypeID.HAS_POOL_LIFEGUARD,
                FactSubtypeID.HAS_POOL_SELF_LOCKING_GATES,
                FactSubtypeID.HAS_POOL_FENCE,
                FactSubtypeID.HAS_POOL_RULES_POSTED,
                FactSubtypeID.HAS_POOL_DIVING_BOARD,
                FactSubtypeID.HAS_POOL_DEPTH_MARKERS,
            ],
            FactSubtypeID.PLUMBING: [FactSubtypeID.EXTERIOR_PLUMBING],
            FactSubtypeID.USES_ADVERTISING_AGENCY: [FactSubtypeID.COVERAGE_FROM_ADVERTISING_AGENCY],
            FactSubtypeID.SECURITY_CAMERAS: [FactSubtypeID.HAS_CCTV],
            FactSubtypeID.HAS_FORMAL_HR_DEPARTMENT: [
                FactSubtypeID.HAS_INTERNAL_INVESTIGATIONS_BY_HR,
                FactSubtypeID.HAS_DEDICATED_HR_MANAGER,
                FactSubtypeID.HAS_HANDBOOK_REVIEWED_BY_HR_COUNSEL,
            ],
            FactSubtypeID.HAS_EMPLOYEE_HANDBOOK: [
                FactSubtypeID.HAS_HANDBOOK_ACKNOWLEDGED_BY_EMPLOYEES,
                FactSubtypeID.HAS_HANDBOOK_PERIODIC_UPDATES,
                FactSubtypeID.HAS_HANDBOOK_REVIEWED_BY_HR_COUNSEL,
            ],
            # Yes to no propagating facts
            FactSubtypeID.HAS_ANTIDISCRIMINATION_TRAINING_FOR_ALL_ANNUAL: [
                FactSubtypeID.HAS_ANTIDISCRIMINATION_TRAINING_UNREGULAR
            ],
            FactSubtypeID.HAS_ANTIDISCRIMINATION_TRAINING_FOR_ALL_EVERY_2_YEARS: [
                FactSubtypeID.HAS_ANTIDISCRIMINATION_TRAINING_UNREGULAR
            ],
            FactSubtypeID.HAS_ANTIDISCRIMINATION_TRAINING_UNREGULAR: [
                FactSubtypeID.HAS_ANTIDISCRIMINATION_TRAINING_FOR_ALL_ANNUAL,
                FactSubtypeID.HAS_ANTIDISCRIMINATION_TRAINING_FOR_ALL_EVERY_2_YEARS,
            ],
            # _get_text_binary_classification_associated_observations
            # Valid text value to yes
            FactSubtypeID.HAS_FIRE_ALARM: [FactSubtypeID.FIRE_ALARM_TYPE, FactSubtypeID.FIRE_ALARM_MANUFACTURER],
            FactSubtypeID.HAS_SMOKE_DETECTOR: [FactSubtypeID.SMOKE_DETECTOR_TYPE],
            # _get_valid_year_and_binary_classification_associated_observations
            # Valid year to yes
            FactSubtypeID.BUILDING_IMPROVEMENTS_WIRING: [FactSubtypeID.BUILDING_IMPROVEMENTS_WIRING_YEAR],
            FactSubtypeID.BUILDING_IMPROVEMENTS_PLUMBING: [FactSubtypeID.BUILDING_IMPROVEMENTS_PLUMBING_YEAR],
            FactSubtypeID.BUILDING_IMPROVEMENTS_HEATING: [FactSubtypeID.BUILDING_IMPROVEMENTS_HEATING_YEAR],
            # _get_percentage_to_opposite_fact
            FactSubtypeID.PROJECT_REMODELING_OR_REPAIR: [FactSubtypeID.PROJECT_NEW_CONSTRUCTION],
            FactSubtypeID.PROJECT_NEW_CONSTRUCTION: [FactSubtypeID.PROJECT_REMODELING_OR_REPAIR],
            FactSubtypeID.DURABLE_MEDICAL_EQUIPMENT: [FactSubtypeID.DURABLE_MEDICAL_EQUIPMENT_SALES],
            FactSubtypeID.CORRECTIONAL_FACILITY_MEDICINE: [FactSubtypeID.PERCENT_CORRECTIONAL_FACILITY_MEDICINE],
            FactSubtypeID.HAS_PATIENTS_ON_VENTILATORS: [FactSubtypeID.PERCENT_PATIENTS_ON_VENTILATORS],
            # Financials YOY
            FactSubtypeID.REVENUE_YOY: [
                FactSubtypeID.REVENUE,
            ],
            FactSubtypeID.CURRENT_ASSETS_YOY: [
                FactSubtypeID.CURRENT_ASSETS,
            ],
            FactSubtypeID.OTHER_ASSETS_YOY: [
                FactSubtypeID.OTHER_ASSETS,
            ],
            FactSubtypeID.TOTAL_ASSETS_YOY: [
                FactSubtypeID.TOTAL_ASSETS,
            ],
            FactSubtypeID.CURRENT_LIABILITIES_YOY: [
                FactSubtypeID.CURRENT_LIABILITIES,
            ],
            FactSubtypeID.OTHER_LIABILITIES_YOY: [
                FactSubtypeID.OTHER_LIABILITIES,
            ],
            FactSubtypeID.TOTAL_LIABILITIES_YOY: [
                FactSubtypeID.TOTAL_LIABILITIES,
            ],
            FactSubtypeID.OPERATING_INCOME_YOY: [
                FactSubtypeID.OPERATING_INCOME,
            ],
            FactSubtypeID.NET_INCOME_YOY: [
                FactSubtypeID.NET_INCOME,
            ],
            FactSubtypeID.OTHER_INCOME_YOY: [
                FactSubtypeID.OTHER_INCOME,
            ],
            FactSubtypeID.NET_SALES_YOY: [
                FactSubtypeID.NET_SALES,
            ],
            FactSubtypeID.GROSS_PROFIT_YOY: [
                FactSubtypeID.GROSS_PROFIT,
            ],
            FactSubtypeID.CASHFLOW_YOY: [
                FactSubtypeID.CASHFLOW,
            ],
            FactSubtypeID.RETAINED_EARNINGS_YOY: [
                FactSubtypeID.RETAINED_EARNINGS,
            ],
            FactSubtypeID.EQUITY_YOY: [
                FactSubtypeID.EQUITY,
            ],
            FactSubtypeID.CURRENT_RATIO_YOY: [
                FactSubtypeID.CURRENT_RATIO,
            ],
            FactSubtypeID.RETURN_ON_EQUITY_YOY: [
                FactSubtypeID.RETURN_ON_EQUITY,
            ],
            FactSubtypeID.DEBT_TO_EQUITY_YOY: [
                FactSubtypeID.DEBT_TO_EQUITY,
            ],
            FactSubtypeID.TOTAL_SALES_YOY: [
                FactSubtypeID.TOTAL_SALES,
            ],
            FactSubtypeID.FOREIGN_TOTAL_SALES_YOY: [
                FactSubtypeID.FOREIGN_TOTAL_SALES,
            ],
            FactSubtypeID.DELIVERY_SALES_YOY: [
                FactSubtypeID.DELIVERY_SALES,
            ],
            FactSubtypeID.FOOD_SALES_YOY: [
                FactSubtypeID.FOOD_SALES,
            ],
            FactSubtypeID.HOTEL_SALES_YOY: [
                FactSubtypeID.HOTEL_SALES,
            ],
            FactSubtypeID.LIQUOR_SALES_YOY: [
                FactSubtypeID.LIQUOR_SALES,
            ],
            FactSubtypeID.CASH_RATIO_YOY: [
                FactSubtypeID.CASH_RATIO,
            ],
            FactSubtypeID.NET_WORKING_CAPITAL_RATIO_YOY: [
                FactSubtypeID.NET_WORKING_CAPITAL_RATIO,
            ],
            FactSubtypeID.OPERATING_CASH_FLOW_RATIO_YOY: [
                FactSubtypeID.OPERATING_CASH_FLOW_RATIO,
            ],
            FactSubtypeID.QUICK_RATIO_YOY: [
                FactSubtypeID.QUICK_RATIO,
            ],
            FactSubtypeID.RECEIVABLES_YOY: [
                FactSubtypeID.RECEIVABLES,
            ],
            FactSubtypeID.MARKETABLE_SECURITIES_YOY: [
                FactSubtypeID.MARKETABLE_SECURITIES,
            ],
            FactSubtypeID.DOMESTIC_TOTAL_SALES_YOY: [
                FactSubtypeID.DOMESTIC_TOTAL_SALES,
            ],
            FactSubtypeID.PAYROLL_YOY: [
                FactSubtypeID.PAYROLL,
            ],
            FactSubtypeID.NON_CURRENT_LIABILITIES_YOY: [
                FactSubtypeID.NON_CURRENT_LIABILITIES,
            ],
            FactSubtypeID.NON_CURRENT_ASSETS_YOY: [
                FactSubtypeID.NON_CURRENT_ASSETS,
            ],
            FactSubtypeID.PROPERTY_AND_EQUIPMENT_YOY: [
                FactSubtypeID.PROPERTY_AND_EQUIPMENT,
            ],
            FactSubtypeID.OPERATING_EXPENSES_YOY: [
                FactSubtypeID.OPERATING_EXPENSES,
            ],
            FactSubtypeID.ADMINISTRATIVE_EXPENSES_YOY: [
                FactSubtypeID.ADMINISTRATIVE_EXPENSES,
            ],
            FactSubtypeID.INVESTMENT_EXPENSES_YOY: [
                FactSubtypeID.INVESTMENT_EXPENSES,
            ],
            FactSubtypeID.OTHER_EXPENSES_YOY: [
                FactSubtypeID.OTHER_EXPENSES,
            ],
            FactSubtypeID.TOTAL_EXPENSES_YOY: [
                FactSubtypeID.TOTAL_EXPENSES,
            ],
            FactSubtypeID.COST_OF_SALES_YOY: [
                FactSubtypeID.COST_OF_SALES,
            ],
            FactSubtypeID.CASHFLOW_OPERATING_YOY: [
                FactSubtypeID.CASHFLOW_OPERATING,
            ],
            FactSubtypeID.CASHFLOW_INVESTING_YOY: [
                FactSubtypeID.CASHFLOW_INVESTING,
            ],
            FactSubtypeID.CASHFLOW_FINANCING_YOY: [
                FactSubtypeID.CASHFLOW_FINANCING,
            ],
            FactSubtypeID.CASH_END_PERIOD_YOY: [
                FactSubtypeID.CASH_END_PERIOD,
            ],
            FactSubtypeID.NON_CASH_ACTIVITY_YOY: [
                FactSubtypeID.NON_CASH_ACTIVITY,
            ],
            FactSubtypeID.LIABILITIES_AND_EQUITY_YOY: [
                FactSubtypeID.LIABILITIES_AND_EQUITY,
            ],
            # Financials YOY percentage
            FactSubtypeID.REVENUE_YOY_PERCENTAGE: [
                FactSubtypeID.REVENUE,
            ],
            FactSubtypeID.CURRENT_ASSETS_YOY_PERCENTAGE: [
                FactSubtypeID.CURRENT_ASSETS,
            ],
            FactSubtypeID.OTHER_ASSETS_YOY_PERCENTAGE: [
                FactSubtypeID.OTHER_ASSETS,
            ],
            FactSubtypeID.TOTAL_ASSETS_YOY_PERCENTAGE: [
                FactSubtypeID.TOTAL_ASSETS,
            ],
            FactSubtypeID.CURRENT_LIABILITIES_YOY_PERCENTAGE: [
                FactSubtypeID.CURRENT_LIABILITIES,
            ],
            FactSubtypeID.OTHER_LIABILITIES_YOY_PERCENTAGE: [
                FactSubtypeID.OTHER_LIABILITIES,
            ],
            FactSubtypeID.TOTAL_LIABILITIES_YOY_PERCENTAGE: [
                FactSubtypeID.TOTAL_LIABILITIES,
            ],
            FactSubtypeID.OPERATING_INCOME_YOY_PERCENTAGE: [
                FactSubtypeID.OPERATING_INCOME,
            ],
            FactSubtypeID.NET_INCOME_YOY_PERCENTAGE: [
                FactSubtypeID.NET_INCOME,
            ],
            FactSubtypeID.OTHER_INCOME_YOY_PERCENTAGE: [
                FactSubtypeID.OTHER_INCOME,
            ],
            FactSubtypeID.NET_SALES_YOY_PERCENTAGE: [
                FactSubtypeID.NET_SALES,
            ],
            FactSubtypeID.GROSS_PROFIT_YOY_PERCENTAGE: [
                FactSubtypeID.GROSS_PROFIT,
            ],
            FactSubtypeID.CASHFLOW_YOY_PERCENTAGE: [
                FactSubtypeID.CASHFLOW,
            ],
            FactSubtypeID.RETAINED_EARNINGS_YOY_PERCENTAGE: [
                FactSubtypeID.RETAINED_EARNINGS,
            ],
            FactSubtypeID.EQUITY_YOY_PERCENTAGE: [
                FactSubtypeID.EQUITY,
            ],
            FactSubtypeID.CURRENT_RATIO_YOY_PERCENTAGE: [
                FactSubtypeID.CURRENT_RATIO,
            ],
            FactSubtypeID.RETURN_ON_EQUITY_YOY_PERCENTAGE: [
                FactSubtypeID.RETURN_ON_EQUITY,
            ],
            FactSubtypeID.DEBT_TO_EQUITY_YOY_PERCENTAGE: [
                FactSubtypeID.DEBT_TO_EQUITY,
            ],
            FactSubtypeID.TOTAL_SALES_YOY_PERCENTAGE: [
                FactSubtypeID.TOTAL_SALES,
            ],
            FactSubtypeID.FOREIGN_TOTAL_SALES_YOY_PERCENTAGE: [
                FactSubtypeID.FOREIGN_TOTAL_SALES,
            ],
            FactSubtypeID.DELIVERY_SALES_YOY_PERCENTAGE: [
                FactSubtypeID.DELIVERY_SALES,
            ],
            FactSubtypeID.FOOD_SALES_YOY_PERCENTAGE: [
                FactSubtypeID.FOOD_SALES,
            ],
            FactSubtypeID.HOTEL_SALES_YOY_PERCENTAGE: [
                FactSubtypeID.HOTEL_SALES,
            ],
            FactSubtypeID.LIQUOR_SALES_YOY_PERCENTAGE: [
                FactSubtypeID.LIQUOR_SALES,
            ],
            FactSubtypeID.CASH_RATIO_YOY_PERCENTAGE: [
                FactSubtypeID.CASH_RATIO,
            ],
            FactSubtypeID.NET_WORKING_CAPITAL_RATIO_YOY_PERCENTAGE: [
                FactSubtypeID.NET_WORKING_CAPITAL_RATIO,
            ],
            FactSubtypeID.OPERATING_CASH_FLOW_RATIO_YOY_PERCENTAGE: [
                FactSubtypeID.OPERATING_CASH_FLOW_RATIO,
            ],
            FactSubtypeID.QUICK_RATIO_YOY_PERCENTAGE: [
                FactSubtypeID.QUICK_RATIO,
            ],
            FactSubtypeID.RECEIVABLES_YOY_PERCENTAGE: [
                FactSubtypeID.RECEIVABLES,
            ],
            FactSubtypeID.MARKETABLE_SECURITIES_YOY_PERCENTAGE: [
                FactSubtypeID.MARKETABLE_SECURITIES,
            ],
            FactSubtypeID.DOMESTIC_TOTAL_SALES_YOY_PERCENTAGE: [
                FactSubtypeID.DOMESTIC_TOTAL_SALES,
            ],
            FactSubtypeID.PAYROLL_YOY_PERCENTAGE: [
                FactSubtypeID.PAYROLL,
            ],
            FactSubtypeID.NON_CURRENT_LIABILITIES_YOY_PERCENTAGE: [
                FactSubtypeID.NON_CURRENT_LIABILITIES,
            ],
            FactSubtypeID.NON_CURRENT_ASSETS_YOY_PERCENTAGE: [
                FactSubtypeID.NON_CURRENT_ASSETS,
            ],
            FactSubtypeID.PROPERTY_AND_EQUIPMENT_YOY_PERCENTAGE: [
                FactSubtypeID.PROPERTY_AND_EQUIPMENT,
            ],
            FactSubtypeID.OPERATING_EXPENSES_YOY_PERCENTAGE: [
                FactSubtypeID.OPERATING_EXPENSES,
            ],
            FactSubtypeID.ADMINISTRATIVE_EXPENSES_YOY_PERCENTAGE: [
                FactSubtypeID.ADMINISTRATIVE_EXPENSES,
            ],
            FactSubtypeID.INVESTMENT_EXPENSES_YOY_PERCENTAGE: [
                FactSubtypeID.INVESTMENT_EXPENSES,
            ],
            FactSubtypeID.OTHER_EXPENSES_YOY_PERCENTAGE: [
                FactSubtypeID.OTHER_EXPENSES,
            ],
            FactSubtypeID.TOTAL_EXPENSES_YOY_PERCENTAGE: [
                FactSubtypeID.TOTAL_EXPENSES,
            ],
            FactSubtypeID.COST_OF_SALES_YOY_PERCENTAGE: [
                FactSubtypeID.COST_OF_SALES,
            ],
            FactSubtypeID.CASHFLOW_OPERATING_YOY_PERCENTAGE: [
                FactSubtypeID.CASHFLOW_OPERATING,
            ],
            FactSubtypeID.CASHFLOW_INVESTING_YOY_PERCENTAGE: [
                FactSubtypeID.CASHFLOW_INVESTING,
            ],
            FactSubtypeID.CASHFLOW_FINANCING_YOY_PERCENTAGE: [
                FactSubtypeID.CASHFLOW_FINANCING,
            ],
            FactSubtypeID.CASH_END_PERIOD_YOY_PERCENTAGE: [
                FactSubtypeID.CASH_END_PERIOD,
            ],
            FactSubtypeID.NON_CASH_ACTIVITY_YOY_PERCENTAGE: [
                FactSubtypeID.NON_CASH_ACTIVITY,
            ],
            FactSubtypeID.LIABILITIES_AND_EQUITY_YOY_PERCENTAGE: [
                FactSubtypeID.LIABILITIES_AND_EQUITY,
            ],
        }
