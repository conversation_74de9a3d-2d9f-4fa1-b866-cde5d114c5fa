from static_common.enums.enum import StrEnum


class TaskDefinitionCodes(StrEnum):
    GENERATE_DESCRIPTION = "GENERATE_DESCRIPTION"
    EXTRACT_NAICS = "EXTRACT_NAICS"
    EXTRACT_GL_CODE = "EXTRACT_GL_CODE"
    EXTRACT_NAICS_MAPPING = "EXTRACT_NAICS_MAPPING"
    CONSOLIDATE_NAICS_AND_DESCRIPTION = "CONSOLIDATE_NAICS_AND_DESCRIPTION"
    EXTRACT_EMAIL_DATA = "EXTRACT_EMAIL_DATA"
    EXTRACT_ENTITIES = "EXTRACT_ENTITIES"
    EXTRACT_BROKER_DATA = "EXTRACT_BROKER_DATA"
    EXTRACT_POLICY_DATA = "EXTRACT_POLICY_DATA"
    EXTRACT_COVERAGES = "EXTRACT_COVERAGES"
    EXTRACT_PROJECT_DATA = "EXTRACT_PROJECT_DATA"
    EXTRACT_GC_DATA = "EXTRACT_GC_DATA"
    EXTRACT_UNDERWRITERS = "EXTRACT_UNDERWRITERS"
    EXTRACT_RENEWAL_DATA = "EXTRACT_RENEWAL_DATA"
    FACTS_MATCHING_AND_NORMALIZATION = "FACTS_MATCHING_AND_NORMALIZATION"
    SOV_QUALITY_CHECK = "SOV_QUALITY_CHECK"
    SUBMISSION_QUALITY_CHECK = "SUBMISSION_QUALITY_CHECK"
    TEST = "TEST"
