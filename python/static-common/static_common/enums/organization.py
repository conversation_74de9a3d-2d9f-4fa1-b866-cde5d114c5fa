from enum import Enum
from typing import Optional


class ExistingOrganizations(Enum):
    KalepaTest = 3
    Nationwide = 6
    KalepaMGA = 7
    KalepaAutomatedTests = 8
    KalepaDemo = 9
    Arch = 10
    ArchTest = 31
    GuideOne = 33
    MunichRe = 36
    Paragon = 37
    NorthStarMutual = 40
    NecSpecialty = 41
    GoldenBear = 42
    NSM = 43
    KalepaNewDemo = 44
    OmahaNational = 45
    WCF = 46
    BoltonStreet = 47
    ZurichNA = 48
    AdmiralInsuranceGroup = 49
    CrcGroup = 50
    MSIGNA = 51
    FCCIGroup = 52
    QualityAudit = 53
    BowheadSpecialty = 54
    Travelers = 55
    MerchantsGroup = 56
    NationwideML = 57
    BishopConifer = 58
    ARU = 59
    ISC = 60
    K2 = 61
    MarkelDemo = 62
    CNA = 63
    SECURA = 64
    Vivere = 65
    AIG = 66
    BowheadSpecialtyTest = 67
    AdmiralInsuranceGroupTest = 68


class OrganizationGroups(Enum):
    # Paragon
    PARAGON_PSP_E3 = "PARAGON_PSP_E3"
    PARAGON_ALLY_AUTO = "PARAGON_ALLY_AUTO"
    PARAGON_WC = "PARAGON_WC"
    PARAGON_XS = "PARAGON_XS"
    PARAGON_TRIDENT = "PARAGON_TRIDENT"

    # K2
    K2_AEGIS = "Aegis Specialty Dealers"
    K2_VIKCO = "Vikco"

    # Merchants
    MERCHANTS_CEP = "CEP"
    MERCHANTS_ADMITTED = "Admitted"

    # Bishop - Conifer
    CONIFER_THC = "THC"
    CONIFER_HOSPITALITY = "Hospitality"
    CONIFER_MAIN_ST = "Main St"
    CONIFER_CANNABIS_SELECT = "Cannabis Select"

    # Bowhead
    BOWHEAD_PRIMARY = "BOWHEAD_PRIMARY"
    BOWHEAD_XS = "BOWHEAD_XS"
    BOWHEAD_ENVIRONMENTAL = "BOWHEAD_ENVIRONMENTAL"

    @classmethod
    def get_from_forward_account(cls, forward_account: str) -> Optional["OrganizationGroups"]:
        return EMAIL_FORWARD_TO_ORG_GROUP.get(forward_account)

    @classmethod
    def is_organization_group_based(cls, organization_id: int) -> bool:
        return organization_id in ORG_GROUP_BASED_ORGANIZATION

    @classmethod
    def get_from_email_recipients(cls, organization_id: int, email_recipients: list[str]) -> list["OrganizationGroups"]:
        if organization_id not in ORG_TO_EMAIL_RECIPIENT_TO_ORG_GROUP:
            return []
        org_groups = set()
        for email, org_group in ORG_TO_EMAIL_RECIPIENT_TO_ORG_GROUP[organization_id].items():
            if any(email in recipient.lower() for recipient in email_recipients if recipient):
                org_groups.add(org_group)
        return list(org_groups)


ORG_TO_EMAIL_RECIPIENT_TO_ORG_GROUP = {
    ExistingOrganizations.BowheadSpecialty.value: {
        "<EMAIL>": OrganizationGroups.BOWHEAD_PRIMARY,
        "<EMAIL>": OrganizationGroups.BOWHEAD_PRIMARY,
        "<EMAIL>": OrganizationGroups.BOWHEAD_XS,
        "<EMAIL>": OrganizationGroups.BOWHEAD_XS,
        "<EMAIL>": OrganizationGroups.BOWHEAD_ENVIRONMENTAL,
    },
    ExistingOrganizations.Paragon.value: {
        "<EMAIL>": OrganizationGroups.PARAGON_XS,
        "<EMAIL>": OrganizationGroups.PARAGON_XS,
        "<EMAIL>": OrganizationGroups.PARAGON_ALLY_AUTO,
        "<EMAIL>": OrganizationGroups.PARAGON_WC,
        "<EMAIL>": OrganizationGroups.PARAGON_PSP_E3,
    },
}

EMAIL_FORWARD_TO_ORG_GROUP = {
    # Paragon
    "<EMAIL>": OrganizationGroups.PARAGON_ALLY_AUTO,
    "<EMAIL>": OrganizationGroups.PARAGON_XS,
    "<EMAIL>": OrganizationGroups.PARAGON_WC,
    "<EMAIL>": OrganizationGroups.PARAGON_PSP_E3,
    "<EMAIL>": OrganizationGroups.PARAGON_XS,
    "<EMAIL>": OrganizationGroups.PARAGON_TRIDENT,
    # K2
    "<EMAIL>": OrganizationGroups.K2_AEGIS,
    "<EMAIL>": OrganizationGroups.K2_VIKCO,
    # Merchants
    "<EMAIL>": OrganizationGroups.MERCHANTS_CEP,
    "<EMAIL>": OrganizationGroups.MERCHANTS_ADMITTED,
}

ORG_GROUP_BASED_ORGANIZATION = {
    ExistingOrganizations.K2.value,
    ExistingOrganizations.Paragon.value,
    ExistingOrganizations.MerchantsGroup.value,
}

EMAIL_ACCOUNT_PER_ORG = {
    ExistingOrganizations.Nationwide.value: "<EMAIL>",
    ExistingOrganizations.Arch.value: "<EMAIL>",
    ExistingOrganizations.GuideOne.value: "<EMAIL>",
    ExistingOrganizations.KalepaMGA.value: "<EMAIL>",
    ExistingOrganizations.KalepaDemo.value: "<EMAIL>",
    ExistingOrganizations.MunichRe.value: "<EMAIL>",
    ExistingOrganizations.Paragon.value: "<EMAIL>",
    ExistingOrganizations.GoldenBear.value: "<EMAIL>",
    ExistingOrganizations.NSM.value: "<EMAIL>",
    ExistingOrganizations.NorthStarMutual.value: "<EMAIL>",
    ExistingOrganizations.NecSpecialty.value: "<EMAIL>",
    ExistingOrganizations.KalepaNewDemo.value: "<EMAIL>",
    ExistingOrganizations.OmahaNational.value: "<EMAIL>",
    ExistingOrganizations.WCF.value: "<EMAIL>",
    ExistingOrganizations.BoltonStreet.value: "<EMAIL>",
    ExistingOrganizations.ZurichNA.value: "<EMAIL>",
    ExistingOrganizations.AdmiralInsuranceGroup.value: "<EMAIL>",
    ExistingOrganizations.AdmiralInsuranceGroupTest.value: "<EMAIL>",
    ExistingOrganizations.CrcGroup.value: "<EMAIL>",
    ExistingOrganizations.MSIGNA.value: "<EMAIL>",
    ExistingOrganizations.FCCIGroup.value: "<EMAIL>",
    ExistingOrganizations.QualityAudit.value: "<EMAIL>",
    ExistingOrganizations.BowheadSpecialty.value: "<EMAIL>",
    ExistingOrganizations.BowheadSpecialtyTest.value: "<EMAIL>",
    ExistingOrganizations.Travelers.value: "<EMAIL>",
    ExistingOrganizations.MerchantsGroup.value: "<EMAIL>",
    ExistingOrganizations.NationwideML.value: "<EMAIL>",
    ExistingOrganizations.BishopConifer.value: "<EMAIL>",
    ExistingOrganizations.ARU.value: "<EMAIL>",
    ExistingOrganizations.ISC.value: "<EMAIL>",
    ExistingOrganizations.K2.value: "<EMAIL>",
    ExistingOrganizations.MarkelDemo.value: "<EMAIL>",
    ExistingOrganizations.CNA.value: "<EMAIL>",
    ExistingOrganizations.SECURA.value: "<EMAIL>",
    ExistingOrganizations.Vivere.value: "<EMAIL>",
    ExistingOrganizations.AIG.value: "<EMAIL>",
}

PDS_INBOXES_PER_ORG = {
    ExistingOrganizations.Nationwide.value: "<EMAIL>",
    ExistingOrganizations.Arch.value: "<EMAIL>",
    ExistingOrganizations.GuideOne.value: "<EMAIL>",
    ExistingOrganizations.KalepaMGA.value: "<EMAIL>",
    ExistingOrganizations.KalepaDemo.value: "<EMAIL>",
    ExistingOrganizations.MunichRe.value: "<EMAIL>",
    ExistingOrganizations.Paragon.value: "<EMAIL>",
    ExistingOrganizations.GoldenBear.value: "<EMAIL>",
    ExistingOrganizations.NSM.value: "<EMAIL>",
    ExistingOrganizations.NorthStarMutual.value: "<EMAIL>",
    ExistingOrganizations.NecSpecialty.value: "<EMAIL>",
    ExistingOrganizations.KalepaNewDemo.value: "<EMAIL>",
    ExistingOrganizations.OmahaNational.value: "<EMAIL>",
    ExistingOrganizations.WCF.value: "<EMAIL>",
    ExistingOrganizations.BoltonStreet.value: "<EMAIL>",
    ExistingOrganizations.ZurichNA.value: "<EMAIL>",
    ExistingOrganizations.AdmiralInsuranceGroup.value: "<EMAIL>",
    ExistingOrganizations.AdmiralInsuranceGroupTest.value: "<EMAIL>",
    ExistingOrganizations.CrcGroup.value: "<EMAIL>",
    ExistingOrganizations.MSIGNA.value: "<EMAIL>",
    ExistingOrganizations.FCCIGroup.value: "<EMAIL>",
    ExistingOrganizations.QualityAudit.value: "<EMAIL>",
    ExistingOrganizations.BowheadSpecialty.value: "<EMAIL>",
    ExistingOrganizations.BowheadSpecialtyTest.value: "<EMAIL>",
    ExistingOrganizations.Travelers.value: "<EMAIL>",
    ExistingOrganizations.MerchantsGroup.value: "<EMAIL>",
    ExistingOrganizations.NationwideML.value: "<EMAIL>",
    ExistingOrganizations.BishopConifer.value: "<EMAIL>",
    ExistingOrganizations.ARU.value: "<EMAIL>",
    ExistingOrganizations.ISC.value: "<EMAIL>",
    ExistingOrganizations.K2.value: "<EMAIL>",
    ExistingOrganizations.MarkelDemo.value: "<EMAIL>",
    ExistingOrganizations.CNA.value: "<EMAIL>",
    ExistingOrganizations.SECURA.value: "<EMAIL>",
    ExistingOrganizations.Vivere.value: "<EMAIL>",
    ExistingOrganizations.AIG.value: "<EMAIL>",
}
