from __future__ import annotations

from static_common.enums.enum import StrEnum


class ClassificationDocumentType(StrEnum):
    LOSS_RUN = "LOSS_RUN"
    LOSS_RUN_PDF = "LOSS_RUN_PDF"
    LOSS_RUN_SPREADSHEET = "LOSS_RUN_SPREADSHEET"
    LOSS_RUN_EDITABLE_DOC = "LOSS_RUN_EDITABLE_DOC"

    LOSS_SUMMARY = "LOSS_SUMMARY"
    LOSS_SUMMARY_PDF = "LOSS_SUMMARY_PDF"
    LOSS_SUMMARY_SPREADSHEET = "LOSS_SUMMARY_SPREADSHEET"
    LOSS_SUMMARY_EDITABLE_DOC = "LOSS_SUMMARY_EDITABLE_DOC"

    LOSS_RUN_NO_CLAIM = "LOSS_RUN_NO_CLAIM"
    LOSS_RUN_NO_CLAIM_PDF = "LOSS_RUN_NO_CLAIM_PDF"
    LOSS_RUN_NO_CLAIM_SPREADSHEET = "LOSS_RUN_NO_CLAIM_SPREADSHEET"
    LOSS_RUN_NO_CLAIM_EDITABLE_DOC = "LOSS_RUN_NO_CLAIM_EDITABLE_DOC"

    BUDGET = "BUDGET"
    BUDGET_PDF = "BUDGET_PDF"
    BUDGET_SPREADSHEET = "BUDGET_SPREADSHEET"
    BUDGET_EDITABLE_DOC = "BUDGET_EDITABLE_DOC"

    FINANCIAL_STATEMENT = "FINANCIAL_STATEMENT"
    FINANCIAL_STATEMENT_PDF = "FINANCIAL_STATEMENT_PDF"
    FINANCIAL_STATEMENT_SPREADSHEET = "FINANCIAL_STATEMENT_SPREADSHEET"
    FINANCIAL_STATEMENT_EDITABLE_DOC = "FINANCIAL_STATEMENT_EDITABLE_DOC"

    CONSOLIDATED_FINANCIAL_STATEMENT = "CONSOLIDATED_FINANCIAL_STATEMENT"
    CONSOLIDATED_FINANCIAL_STATEMENT_PDF = "CONSOLIDATED_FINANCIAL_STATEMENT_PDF"
    CONSOLIDATED_FINANCIAL_STATEMENT_SPREADSHEET = "CONSOLIDATED_FINANCIAL_STATEMENT_SPREADSHEET"
    CONSOLIDATED_FINANCIAL_STATEMENT_EDITABLE_DOC = "CONSOLIDATED_FINANCIAL_STATEMENT_EDITABLE_DOC"

    AUTO_DEALER_FINANCIAL_STATEMENT = "AUTO_DEALER_FINANCIAL_STATEMENT"
    AUTO_DEALER_FINANCIAL_STATEMENT_PDF = "AUTO_DEALER_FINANCIAL_STATEMENT_PDF"
    AUTO_DEALER_FINANCIAL_STATEMENT_SPREADSHEET = "AUTO_DEALER_FINANCIAL_STATEMENT_SPREADSHEET"
    AUTO_DEALER_FINANCIAL_STATEMENT_EDITABLE_DOC = "AUTO_DEALER_FINANCIAL_STATEMENT_EDITABLE_DOC"

    SUPPLEMENTAL_APPLICATION = "SUPPLEMENTAL_APPLICATION"
    SUPPLEMENTAL_APPLICATION_PDF = "SUPPLEMENTAL_APPLICATION_PDF"
    SUPPLEMENTAL_APPLICATION_SPREADSHEET = ("SUPPLEMENTAL_APPLICATION_SPREADSHEET",)
    SUPPLEMENTAL_APPLICATION_EDITABLE_DOC = "SUPPLEMENTAL_APPLICATION_EDITABLE_DOC"

    TRANSPORTATION_SUPPLEMENTAL_APPLICATION = "TRANSPORTATION_SUPPLEMENTAL_APPLICATION"
    TRANSPORTATION_SUPPLEMENTAL_APPLICATION_PDF = "TRANSPORTATION_SUPPLEMENTAL_APPLICATION_PDF"
    TRANSPORTATION_SUPPLEMENTAL_APPLICATION_SPREADSHEET = "PTRANSPORTATION_SUPPLEMENTAL_APPLICATION_SPREADSHEET"
    TRANSPORTATION_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC = "TRANSPORTATION_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC"

    PRACTICE_SUPPLEMENTAL_APPLICATION = "PRACTICE_SUPPLEMENTAL_APPLICATION"
    PRACTICE_SUPPLEMENTAL_APPLICATION_PDF = "PRACTICE_SUPPLEMENTAL_APPLICATION_PDF"
    PRACTICE_SUPPLEMENTAL_APPLICATION_SPREADSHEET = "PRACTICE_SUPPLEMENTAL_APPLICATION_SPREADSHEET"
    PRACTICE_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC = "PRACTICE_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC"

    # For backward compatibility
    PROJECT_SUPPLEMENTAL_APPLICATION = "PROJECT_SUPPLEMENTAL_APPLICATION"
    PROJECT_SUPPLEMENTAL_APPLICATION_PDF = "PROJECT_SUPPLEMENTAL_APPLICATION_PDF"
    PROJECT_SUPPLEMENTAL_APPLICATION_SPREADSHEET = "PROJECT_SUPPLEMENTAL_APPLICATION_SPREADSHEET"
    PROJECT_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC = "PROJECT_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC"

    PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION = "PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION"
    PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_PDF = "PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_PDF"
    PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_SPREADSHEET = "PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_SPREADSHEET"
    PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC = "PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC"

    PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION = "PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION"
    PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_PDF = "PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_PDF"
    PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_SPREADSHEET = (
        "PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_SPREADSHEET"
    )
    PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC = (
        "PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC"
    )

    PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION = "PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION"
    PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_PDF = "PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_PDF"
    PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_SPREADSHEET = (
        "PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_SPREADSHEET"
    )
    PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC = (
        "PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC"
    )

    RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION = "RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION"
    RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_PDF = "RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_PDF"
    RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_SPREADSHEET = (
        "RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_SPREADSHEET"
    )
    RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC = (
        "RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC"
    )

    RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION = "RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION"
    RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_PDF = "RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_PDF"
    RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_SPREADSHEET = "RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_SPREADSHEET"
    RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC = "RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC"

    QUOTE = "QUOTE"
    QUOTE_PDF = "QUOTE_PDF"
    QUOTE_SPREADSHEET = "QUOTE_SPREADSHEET"
    QUOTE_EDITABLE_DOC = "QUOTE_EDITABLE_DOC"

    GENERAL_LIABILITY_QUOTE = "GENERAL_LIABILITY_QUOTE"  # likely temporary classification

    PROJECT_SCHEDULE = "PROJECT_SCHEDULE"
    PROJECT_SCHEDULE_PDF = "PROJECT_SCHEDULE_PDF"
    PROJECT_SCHEDULE_SPREADSHEET = "PROJECT_SCHEDULE_SPREADSHEET"
    PROJECT_SCHEDULE_EDITABLE_DOC = "PROJECT_SCHEDULE_EDITABLE_DOC"

    SUPPLEMENTAL_APPLICATION_BENEFITS_PLAN = "SUPPLEMENTAL_APPLICATION_BENEFITS_PLAN"
    SUPPLEMENTAL_APPLICATION_SHAREHOLDER_INFO = "SUPPLEMENTAL_APPLICATION_SHAREHOLDER_INFO"
    SUPPLEMENTAL_APPLICATION_EMPLOYEE_INFO = "SUPPLEMENTAL_APPLICATION_EMPLOYEE_INFO"

    ACORD_FORM = "ACORD_FORM"  # always PDF
    ACORD_101 = "ACORD_101"
    ACORD_125 = "ACORD_125"
    ACORD_126 = "ACORD_126"
    ACORD_127 = "ACORD_127"
    ACORD_128 = "ACORD_128"
    ACORD_129 = "ACORD_129"
    ACORD_130 = "ACORD_130"
    ACORD_131 = "ACORD_131"
    ACORD_139 = "ACORD_139"
    ACORD_140 = "ACORD_140"
    ACORD_160 = "ACORD_160"
    ACORD_823 = "ACORD_823"
    ACORD_829 = "ACORD_829"
    ACORD_211 = "ACORD_211"
    ACORD_35 = "ACORD_35"
    ACORD_175 = "ACORD_175"
    APPLIED_98 = "APPLIED_98"
    APPLIED_130 = "APPLIED_130"
    APPLIED_126 = "APPLIED_126"
    APPLIED_125 = "APPLIED_125"
    OFAPPINFCNI = "OFAPPINFCNI"
    OFSCHHAZ = "OFSCHHAZ"

    DRIVERS = "DRIVERS"
    DRIVERS_SPREADSHEET = "DRIVERS_SPREADSHEET"
    DRIVERS_PDF = "DRIVERS_PDF"
    DRIVERS_EDITABLE_DOC = "DRIVERS_EDITABLE_DOC"

    VEHICLES = "VEHICLES"
    VEHICLES_SPREADSHEET = "VEHICLES_SPREADSHEET"
    VEHICLES_PDF = "VEHICLES_PDF"
    VEHICLES_EDITABLE_DOC = "VEHICLES_EDITABLE_DOC"

    EQUIPMENT = "EQUIPMENT"
    EQUIPMENT_PDF = "EQUIPMENT_PDF"
    EQUIPMENT_SPREADSHEET = "EQUIPMENT_SPREADSHEET"
    EQUIPMENT_EDITABLE_DOC = "EQUIPMENT_EDITABLE_DOC"

    GEOTECH_REPORT = "GEOTECH_REPORT"
    GEOTECH_REPORT_PDF = "GEOTECH_REPORT_PDF"
    GEOTECH_REPORT_SPREADSHEET = "GEOTECH_REPORT_SPREADSHEET"
    GEOTECH_REPORT_EDITABLE_DOC = "GEOTECH_REPORT_EDITABLE_DOC"

    SOV = "SOV"
    SOV_SPREADSHEET = "SOV_SPREADSHEET"
    SOV_PDF = "SOV_PDF"
    SOV_EDITABLE_DOC = "SOV_EDITABLE_DOC"

    NAMED_INSURED_SCHEDULE = "NAMED_INSURED_SCHEDULE"
    NAMED_INSURED_SCHEDULE_SPREADSHEET = "NAMED_INSURED_SCHEDULE_SPREADSHEET"
    NAMED_INSURED_SCHEDULE_PDF = "NAMED_INSURED_SCHEDULE_PDF"
    NAMED_INSURED_SCHEDULE_EDITABLE_DOC = "NAMED_INSURED_SCHEDULE_EDITABLE_DOC"

    EMAIL = "EMAIL"
    RAW_EMAIL = "RAW_EMAIL"
    CORRESPONDENCE_EMAIL = "CORRESPONDENCE_EMAIL"

    SITE_REPORT = "SITE_REPORT"  # always PDF

    SAFETY_MANUAL = "SAFETY_MANUAL"
    SAFETY_MANUAL_PDF = "SAFETY_MANUAL_PDF"
    SAFETY_MANUAL_SPREADSHEET = "SAFETY_MANUAL_SPREADSHEET"
    SAFETY_MANUAL_EDITABLE_DOC = "SAFETY_MANUAL_EDITABLE_DOC"

    OTHER = "OTHER"  # other file processable by Path 6 in PDS (through Label Studio annotation)
    OTHER_PDF = "OTHER_PDF"
    OTHER_SPREADSHEET = "OTHER_SPREADSHEET"
    OTHER_EDITABLE_DOC = "OTHER_EDITABLE_DOC"

    MERGED = "MERGED"
    MERGED_PDF = "MERGED_PDF"
    MERGED_SPREADSHEET = "MERGED_SPREADSHEET"
    MERGED_EDITABLE_DOC = "MERGED_EDITABLE_DOC"

    COMPANY_BYLAWS = "COMPANY_BYLAWS"
    COMPANY_BYLAWS_PDF = "COMPANY_BYLAWS_PDF"
    COMPANY_BYLAWS_SPREADSHEET = "COMPANY_BYLAWS_SPREADSHEET"
    COMPANY_BYLAWS_EDITABLE_DOC = "COMPANY_BYLAWS_EDITABLE_DOC"

    RESUME = "RESUME"
    RESUME_PDF = "RESUME_PDF"
    RESUME_SPREADSHEET = "RESUME_SPREADSHEET"
    RESUME_EDITABLE_DOC = "RESUME_EDITABLE_DOC"

    HIRING_GUIDELINES = "HIRING_GUIDELINES"
    HIRING_GUIDELINES_PDF = "HIRING_GUIDELINES_PDF"
    HIRING_GUIDELINES_SPREADSHEET = "HIRING_GUIDELINES_SPREADSHEET"
    HIRING_GUIDELINES_EDITABLE_DOC = "HIRING_GUIDELINES_EDITABLE_DOC"

    ORG_CHART = "ORG_CHART"
    ORG_CHART_PDF = "ORG_CHART_PDF"
    ORG_CHART_SPREADSHEET = "ORG_CHART_SPREADSHEET"
    ORG_CHART_EDITABLE_DOC = "ORG_CHART_EDITABLE_DOC"

    EMPLOYEE_HANDBOOK = "EMPLOYEE_HANDBOOK"
    EMPLOYEE_HANDBOOK_PDF = "EMPLOYEE_HANDBOOK_PDF"
    EMPLOYEE_HANDBOOK_SPREADSHEET = "EMPLOYEE_HANDBOOK_SPREADSHEET"
    EMPLOYEE_HANDBOOK_EDITABLE_DOC = "EMPLOYEE_HANDBOOK_EDITABLE_DOC"

    WORK_COMP_EXPERIENCE = "WORK_COMP_EXPERIENCE"
    WORK_COMP_EXPERIENCE_PDF = "WORK_COMP_EXPERIENCE_PDF"
    WORK_COMP_EXPERIENCE_SPREADSHEET = "WORK_COMP_EXPERIENCE_SPREADSHEET"
    WORK_COMP_EXPERIENCE_EDITABLE_DOC = "WORK_COMP_EXPERIENCE_EDITABLE_DOC"

    WELL_SCHEDULE = "WELL_SCHEDULE"
    WELL_SCHEDULE_PDF = "WELL_SCHEDULE_PDF"
    WELL_SCHEDULE_SPREADSHEET = "WELL_SCHEDULE_SPREADSHEET"
    WELL_SCHEDULE_EDITABLE_DOC = "WELL_SCHEDULE_EDITABLE_DOC"

    MVR = "MVR"
    MVR_PDF = "MVR_PDF"
    MVR_SPREADSHEET = "MVR_SPREADSHEET"
    MVR_EDITABLE_DOC = "MVR_EDITABLE_DOC"

    WORK_COMP_EXPERIENCE_CA = "WORK_COMP_EXPERIENCE_CA"
    WORK_COMP_EXPERIENCE_CA_PDF = "WORK_COMP_EXPERIENCE_CA_PDF"
    WORK_COMP_EXPERIENCE_CA_SPREADSHEET = "WORK_COMP_EXPERIENCE_CA_SPREADSHEET"
    WORK_COMP_EXPERIENCE_CA_EDITABLE_DOC = "WORK_COMP_EXPERIENCE_CA_EDITABLE_DOC"

    WORK_COMP_EXPERIENCE_IN = "WORK_COMP_EXPERIENCE_IN"
    WORK_COMP_EXPERIENCE_IN_PDF = "WORK_COMP_EXPERIENCE_IN_PDF"
    WORK_COMP_EXPERIENCE_IN_SPREADSHEET = "WORK_COMP_EXPERIENCE_IN_SPREADSHEET"
    WORK_COMP_EXPERIENCE_IN_EDITABLE_DOC = "WORK_COMP_EXPERIENCE_IN_EDITABLE_DOC"

    WORK_COMP_EXPERIENCE_NJ = "WORK_COMP_EXPERIENCE_NJ"
    WORK_COMP_EXPERIENCE_NJ_PDF = "WORK_COMP_EXPERIENCE_NJ_PDF"
    WORK_COMP_EXPERIENCE_NJ_SPREADSHEET = "WORK_COMP_EXPERIENCE_NJ_SPREADSHEET"
    WORK_COMP_EXPERIENCE_NJ_EDITABLE_DOC = "WORK_COMP_EXPERIENCE_NJ_EDITABLE_DOC"

    WORK_COMP_EXPERIENCE_NY = "WORK_COMP_EXPERIENCE_NY"
    WORK_COMP_EXPERIENCE_NY_PDF = "WORK_COMP_EXPERIENCE_NY_PDF"
    WORK_COMP_EXPERIENCE_NY_SPREADSHEET = "WORK_COMP_EXPERIENCE_NY_SPREADSHEET"
    WORK_COMP_EXPERIENCE_NY_EDITABLE_DOC = "WORK_COMP_EXPERIENCE_NY_EDITABLE_DOC"

    WORK_COMP_EXPERIENCE_PA = "WORK_COMP_EXPERIENCE_PA"
    WORK_COMP_EXPERIENCE_PA_PDF = "WORK_COMP_EXPERIENCE_PA_PDF"
    WORK_COMP_EXPERIENCE_PA_SPREADSHEET = "WORK_COMP_EXPERIENCE_PA_SPREADSHEET"
    WORK_COMP_EXPERIENCE_PA_EDITABLE_DOC = "WORK_COMP_EXPERIENCE_PA_EDITABLE_DOC"

    WORK_COMP_EXPERIENCE_MN = "WORK_COMP_EXPERIENCE_MN"
    WORK_COMP_EXPERIENCE_MN_PDF = "WORK_COMP_EXPERIENCE_MN_PDF"
    WORK_COMP_EXPERIENCE_MN_SPREADSHEET = "WORK_COMP_EXPERIENCE_MN_SPREADSHEET"
    WORK_COMP_EXPERIENCE_MN_EDITABLE_DOC = "WORK_COMP_EXPERIENCE_MN_EDITABLE_DOC"

    WORK_COMP_EXPERIENCE_WI = "WORK_COMP_EXPERIENCE_WI"
    WORK_COMP_EXPERIENCE_WI_PDF = "WORK_COMP_EXPERIENCE_WI_PDF"
    WORK_COMP_EXPERIENCE_WI_SPREADSHEET = "WORK_COMP_EXPERIENCE_WI_SPREADSHEET"
    WORK_COMP_EXPERIENCE_WI_EDITABLE_DOC = "WORK_COMP_EXPERIENCE_WI_EDITABLE_DOC"

    WORK_COMP_PAYROLL = "WORK_COMP_PAYROLL"
    WORK_COMP_PAYROLL_PDF = "WORK_COMP_PAYROLL_PDF"
    WORK_COMP_PAYROLL_SPREADSHEET = "WORK_COMP_PAYROLL_SPREADSHEET"
    WORK_COMP_PAYROLL_EDITABLE_DOC = "WORK_COMP_PAYROLL_EDITABLE_DOC"

    EMOD_SUMMARY = "EMOD_SUMMARY"
    EMOD_SUMMARY_PDF = "EMOD_SUMMARY_PDF"
    EMOD_SUMMARY_SPREADSHEET = "EMOD_SUMMARY_SPREADSHEET"
    EMOD_SUMMARY_EDITABLE_DOC = "EMOD_SUMMARY_EDITABLE_DOC"

    IFTA = "IFTA"
    IFTA_PDF = "IFTA_PDF"
    IFTA_SPREADSHEET = "IFTA_SPREADSHEET"
    IFTA_EDITABLE_DOC = "IFTA_EDITABLE_DOC"

    COVER_SHEET = "COVER_SHEET"
    COVER_SHEET_PDF = "COVER_SHEET_PDF"
    COVER_SHEET_SPREADSHEET = "COVER_SHEET_SPREADSHEET"
    COVER_SHEET_EDITABLE_DOC = "COVER_SHEET_EDITABLE_DOC"

    DIRECTORS_AND_OFFICERS = "DIRECTORS_AND_OFFICERS"
    DIRECTORS_AND_OFFICERS_PDF = "DIRECTORS_AND_OFFICERS_PDF"
    DIRECTORS_AND_OFFICERS_SPREADSHEET = "DIRECTORS_AND_OFFICERS_SPREADSHEET"
    DIRECTORS_AND_OFFICERS_EDITABLE_DOC = "DIRECTORS_AND_OFFICERS_EDITABLE_DOC"

    ERISA_FORM_5500 = "ERISA_FORM_5500"
    ERISA_FORM_5500_PDF = "ERISA_FORM_5500_PDF"
    ERISA_FORM_5500_SPREADSHEET = "ERISA_FORM_5500_SPREADSHEET"
    ERISA_FORM_5500_EDITABLE_DOC = "ERISA_FORM_5500_EDITABLE_DOC"

    ERISA_FORM_5500_SF = "ERISA_FORM_5500_SF"
    ERISA_FORM_5500_SF_PDF = "ERISA_FORM_5500_SF_PDF"
    ERISA_FORM_5500_SF_SPREADSHEET = "ERISA_FORM_5500_SF_SPREADSHEET"
    ERISA_FORM_5500_SF_EDITABLE_DOC = "ERISA_FORM_5500_SF_EDITABLE_DOC"

    EEOC = "EEOC"
    EEOC_PDF = "EEOC_PDF"
    EEOC_SPREADSHEET = "EEOC_SPREADSHEET"
    EEOC_EDITABLE_DOC = "EEOC_EDITABLE_DOC"

    BROKER_OF_RECORD_LETTER = "BROKER_OF_RECORD_LETTER"
    BROKER_OF_RECORD_LETTER_PDF = "BROKER_OF_RECORD_LETTER_PDF"
    BROKER_OF_RECORD_LETTER_SPREADSHEET = "BROKER_OF_RECORD_LETTER_SPREADSHEET"
    BROKER_OF_RECORD_LETTER_EDITABLE_DOC = "BROKER_OF_RECORD_LETTER_EDITABLE_DOC"

    SHAREHOLDERS = "SHAREHOLDERS"
    SHAREHOLDERS_PDF = "SHAREHOLDERS_PDF"
    SHAREHOLDERS_SPREADSHEET = "SHAREHOLDERS_SPREADSHEET"
    SHAREHOLDERS_EDITABLE_DOC = "SHAREHOLDERS_EDITABLE_DOC"

    EXPOSURE_SHEET = "EXPOSURE_SHEET"
    EXPOSURE_SHEET_PDF = "EXPOSURE_SHEET_PDF"
    EXPOSURE_SHEET_SPREADSHEET = "EXPOSURE_SHEET_SPREADSHEET"
    EXPOSURE_SHEET_EDITABLE_DOC = "EXPOSURE_SHEET_EDITABLE_DOC"

    ADDITIONAL_INSURED_SCHEDULE = "ADDITIONAL_INSURED_SCHEDULE"
    ADDITIONAL_INSURED_SCHEDULE_PDF = "ADDITIONAL_INSURED_SCHEDULE_PDF"
    ADDITIONAL_INSURED_SCHEDULE_SPREADSHEET = "ADDITIONAL_INSURED_SCHEDULE_SPREADSHEET"
    ADDITIONAL_INSURED_SCHEDULE_EDITABLE_DOC = "ADDITIONAL_INSURED_SCHEDULE_EDITABLE_DOC"

    PROJECT_LIST = "PROJECT_LIST"
    PROJECT_LIST_PDF = "PROJECT_LIST_PDF"
    PROJECT_LIST_SPREADSHEET = "PROJECT_LIST_SPREADSHEET"
    PROJECT_LIST_EDITABLE_DOC = "PROJECT_LIST_EDITABLE_DOC"

    NDA = "NDA"
    NDA_PDF = "NDA_PDF"
    NDA_SPREADSHEET = "NDA_SPREADSHEET"
    NDA_EDITABLE_DOC = "NDA_EDITABLE_DOC"

    LETTER_OF_INTENT = "LETTER_OF_INTENT"
    LETTER_OF_INTENT_PDF = "LETTER_OF_INTENT_PDF"
    LETTER_OF_INTENT_SPREADSHEET = "LETTER_OF_INTENT_SPREADSHEET"
    LETTER_OF_INTENT_EDITABLE_DOC = "LETTER_OF_INTENT_EDITABLE_DOC"

    INVESTMENT_MEMORANDUM = "INVESTMENT_MEMORANDUM"
    INVESTMENT_MEMORANDUM_PDF = "INVESTMENT_MEMORANDUM_PDF"
    INVESTMENT_MEMORANDUM_SPREADSHEET = "INVESTMENT_MEMORANDUM_SPREADSHEET"
    INVESTMENT_MEMORANDUM_EDITABLE_DOC = "INVESTMENT_MEMORANDUM_EDITABLE_DOC"

    INVESTMENT_JOINDER = "INVESTMENT_JOINDER"
    INVESTMENT_JOINDER_PDF = "INVESTMENT_JOINDER_PDF"
    INVESTMENT_JOINDER_SPREADSHEET = "INVESTMENT_JOINDER_SPREADSHEET"
    INVESTMENT_JOINDER_EDITABLE_DOC = "INVESTMENT_JOINDER_EDITABLE_DOC"

    PURCHASE_AGREEMENT = "PURCHASE_AGREEMENT"
    PURCHASE_AGREEMENT_PDF = "PURCHASE_AGREEMENT_PDF"
    PURCHASE_AGREEMENT_SPREADSHEET = "PURCHASE_AGREEMENT_SPREADSHEET"
    PURCHASE_AGREEMENT_EDITABLE_DOC = "PURCHASE_AGREEMENT_EDITABLE_DOC"

    ARCHIVE = "ARCHIVE"  # only zip for now

    ALLY_AUTO_SOV = "ALLY_AUTO_SOV"
    ALLY_AUTO_SUPPLEMENTAL = "ALLY_AUTO_SUPPLEMENTAL"
    ALLY_AUTO_PROPERTY_SOV = "ALLY_AUTO_PROPERTY_SOV"

    CAB_REPORT = "CAB_REPORT"
    HTML_DOCUMENT = "HTML_DOCUMENT"

    UNKNOWN = "UNKNOWN"
    EMPTY = "EMPTY"

    CUSTOM = "CUSTOM"
    CUSTOM_PDF = "CUSTOM_PDF"
    CUSTOM_SPREADSHEET = "CUSTOM_SPREADSHEET"
    CUSTOM_EDITABLE_DOC = "CUSTOM_EDITABLE_DOC"

    POLICY = "POLICY"
    POLICY_PDF = "POLICY_PDF"
    POLICY_SPREADSHEET = "POLICY_SPREADSHEET"
    POLICY_EDITABLE_DOC = "POLICY_EDITABLE_DOC"

    ENDORSEMENT = "ENDORSEMENT"
    ENDORSEMENT_PDF = "ENDORSEMENT_PDF"
    ENDORSEMENT_SPREADSHEET = "ENDORSEMENT_SPREADSHEET"
    ENDORSEMENT_EDITABLE_DOC = "ENDORSEMENT_EDITABLE_DOC"

    @staticmethod
    def get_sov_classifications() -> set[ClassificationDocumentType]:
        return {
            ClassificationDocumentType.SOV,
            ClassificationDocumentType.SOV_PDF,
            ClassificationDocumentType.SOV_SPREADSHEET,
            ClassificationDocumentType.SOV_EDITABLE_DOC,
        }

    @staticmethod
    def _supported_acords_with_versions() -> dict[str, set[str]]:
        return {
            ClassificationDocumentType.ACORD_101.value: {
                "ACORD_101_2008_01",
                "ACORD_101_2016_03",
            },
            ClassificationDocumentType.ACORD_125.value: {
                "ACORD_125_2004_03",
                "ACORD_125_2005_06",
                "ACORD_125_2006_08",
                "ACORD_125_2007_10",
                "ACORD_125_2009_08",
                "ACORD_125_CA_2023_01",
                "ACORD_125_2024_11",
                "ACORD_125_2016_03",
                "ACORD_125_2013_09",
                "ACORD_125_2014_12",
                "ACORD_125_FL_2016_03",
                "ACORD_125_2013_01",
                "ACORD_125_2011_09",
                "ACORD_125_2007_05",
                "ACORD_125_3_93",
                "ACORD_125_FL_2015_02",
                "ACORD_125_2007_07",
                "ACORD_125_2001_04",
                "ACORD_125_2009_05",
                "ACORD_125_7_98",
                "ACORD_125_2_02",
                "ACORD_125_2003_01",
                "ACORD_125_7_96",
                "ACORD_125_2000_08",
                "ACORD_125_2025_03",
            },
            ClassificationDocumentType.ACORD_126.value: {
                "ACORD_126_2007_05",
                "ACORD_126_2016_09",
                "ACORD_126_2016_03",
                "ACORD_126_2014_04",
                "ACORD_126_2011_09",
                "ACORD_126_2004_03",
                "ACORD_126_2009_08",
                "ACORD_126_2005_08",
                "ACORD_126_2010_05",
                "ACORD_126_S_3_93",
                "ACORD_126_2025_03",
                "ACORD_126_S_1_97",
                "ACORD_126_2007_01",
                "ACORD_126_2003_07",
                "ACORD_126_2000_04",
            },
            ClassificationDocumentType.ACORD_131.value: {
                "ACORD_131_2009_10",
                "ACORD_131_2017_11",
                "ACORD_131_2013_12",
                "ACORD_131_2011_11",
                "ACORD_131_2016_04",
                "ACORD_131_2007_09",
                "ACORD_131_2003_08",
            },
            ClassificationDocumentType.ACORD_130.value: {
                "ACORD_130_2013_09",
                "ACORD_130_2017_05",
                "ACORD_130_FL_2019_07",
                "ACORD_130_CA_2019_01",
                "ACORD_130_2013_01",
                "ACORD_130_2000_08",
                "ACORD_130_CA_2023_01",
                "ACORD_130_2005_08",
                "ACORD_130_2007_11",
                "ACORD_130_2010_05",
                "ACORD_130_2009_09",
                "ACORD_130_2002_09",
                "ACORD_130_7_98",
                "ACORD_130_2017_01",
                "ACORD_130_FL_2002_07",
                "ACORD_130_2004_03",
            },
            ClassificationDocumentType.ACORD_139.value: {
                "ACORD_139_2004_03",
                "ACORD_139_2014_09",
                "ACORD_139_2015_12",
            },
            ClassificationDocumentType.ACORD_140.value: {
                "ACORD_140_2014_12",
                "ACORD_140_2016_03",
                "ACORD_140_2011_10",
                "ACORD_140_2010_12",
                "ACORD_140_2007_09",
                "ACORD_140_2007_05",
                "ACORD_140_1_98",
                "ACORD_140_2002_09",
                "ACORD_140_2005_01",
                "ACORD_140_2006_08",
                "ACORD_140_S_7_88",
                "ACORD_140_3_95",
                "ACORD_140_5_94",
                "ACORD_140_2001_08",
            },
            ClassificationDocumentType.ACORD_823.value: {
                "ACORD_823_2011_10",
                "ACORD_823_2015_12",
            },
            ClassificationDocumentType.ACORD_829.value: {
                "ACORD_829_2009_05",
                "ACORD_829_LOB_2009_05",
                "ACORD_829_CLAP_2009_05",
            },
            ClassificationDocumentType.APPLIED_130.value: {
                "APPLIED_130_API_2005_08",
                "APPLIED_130_API_2013_01",
            },
            ClassificationDocumentType.ACORD_211.value: {
                "ACORD_211_2016_09",
            },
            ClassificationDocumentType.OFSCHHAZ.value: {
                "OFSCHHAZ",
            },
            ClassificationDocumentType.APPLIED_126.value: {
                "APPLIED_126_HAZ_2007_05",
                "APPLIED_126_HS_2005_08",
            },
            ClassificationDocumentType.APPLIED_98.value: {"APPLIED_98_2001_01"},
            ClassificationDocumentType.APPLIED_125.value: {"APPLIED_125_ONI_2009_08"},
            ClassificationDocumentType.OFAPPINFCNI.value: {"OFAPPINFCNI"},
            ClassificationDocumentType.ACORD_160.value: {
                "ACORD_160_2011_10",
                "ACORD_160_2004_03",
            },
        }

    @staticmethod
    def supported_acords() -> set[str]:
        return set(ClassificationDocumentType._supported_acords_with_versions().keys())

    @property
    def supported_acord_versions(self) -> set[str]:
        return self._supported_acords_with_versions().get(self, set())

    @staticmethod
    def get_consolidation_priority(classification: str) -> int:
        priorities = {
            ClassificationDocumentType.ACORD_125.value: 0,
            ClassificationDocumentType.APPLIED_125.value: 50,
            ClassificationDocumentType.OFAPPINFCNI.value: 100,
            ClassificationDocumentType.ACORD_823.value: 200,
            ClassificationDocumentType.ACORD_131.value: 300,
            ClassificationDocumentType.ACORD_140.value: 400,
            ClassificationDocumentType.ACORD_139.value: 450,
            ClassificationDocumentType.ACORD_130.value: 500,
            ClassificationDocumentType.APPLIED_130.value: 550,
            ClassificationDocumentType.ACORD_126.value: 600,
            ClassificationDocumentType.APPLIED_126.value: 650,
            ClassificationDocumentType.ACORD_211.value: 700,
            ClassificationDocumentType.OFSCHHAZ.value: 800,
            ClassificationDocumentType.ACORD_160.value: 900,
        }
        return priorities.get(classification, 9999)

    @staticmethod
    def entity_mapping_classifications() -> set[str]:
        return {
            ClassificationDocumentType.TRANSPORTATION_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_PDF.value,
            # For backward compatibility
            ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_SPREADSHEET.value,
            ClassificationDocumentType.EMAIL.value,
            ClassificationDocumentType.BUDGET_PDF.value,
            ClassificationDocumentType.BUDGET_SPREADSHEET.value,
            ClassificationDocumentType.COVER_SHEET_PDF.value,
            ClassificationDocumentType.INVESTMENT_MEMORANDUM_PDF.value,
            ClassificationDocumentType.PURCHASE_AGREEMENT_PDF.value,
            ClassificationDocumentType.FINANCIAL_STATEMENT_PDF.value,
            ClassificationDocumentType.LETTER_OF_INTENT_PDF.value,
        }

    @staticmethod
    def data_consolidation_classifications() -> set[str]:
        """
        All ACORD + files due for EM without Budget.
        """
        return {
            ClassificationDocumentType.TRANSPORTATION_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF.value,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_SPREADSHEET.value,
            ClassificationDocumentType.DIRECTORS_AND_OFFICERS_PDF.value,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_SHAREHOLDER_INFO.value,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_EMPLOYEE_INFO.value,
            # For backward compatibility
            ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF.value,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_SPREADSHEET.value,
            ClassificationDocumentType.EMAIL.value,
            ClassificationDocumentType.COVER_SHEET_PDF.value,
        } | ClassificationDocumentType.supported_acords()

    @staticmethod
    def di_data_consolidation_classifications() -> set[str]:
        return {
            ClassificationDocumentType.ACORD_140.value,
            ClassificationDocumentType.ACORD_211.value,
            ClassificationDocumentType.OFSCHHAZ.value,
            ClassificationDocumentType.APPLIED_126.value,
            ClassificationDocumentType.ACORD_126.value,
            ClassificationDocumentType.ACORD_125.value,
            ClassificationDocumentType.ACORD_131.value,
            ClassificationDocumentType.ACORD_139.value,
            ClassificationDocumentType.ACORD_823.value,
            ClassificationDocumentType.ACORD_160.value,
        }

    @staticmethod
    def data_onboarding_classifications() -> set[str]:
        return {
            ClassificationDocumentType.SOV_SPREADSHEET.value,
            ClassificationDocumentType.DRIVERS_SPREADSHEET.value,
            ClassificationDocumentType.DRIVERS_PDF.value,
            ClassificationDocumentType.VEHICLES_SPREADSHEET.value,
            ClassificationDocumentType.VEHICLES_PDF.value,
            ClassificationDocumentType.EQUIPMENT_SPREADSHEET.value,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF.value,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_SPREADSHEET.value,
            ClassificationDocumentType.ACORD_127,
            ClassificationDocumentType.ACORD_128,
            ClassificationDocumentType.ACORD_129,
        }

    @staticmethod
    def skip_onboarding_classifications() -> set[str]:
        return ClassificationDocumentType.supported_acords().union(
            {
                ClassificationDocumentType.ALLY_AUTO_PROPERTY_SOV,
                ClassificationDocumentType.ALLY_AUTO_SUPPLEMENTAL,
                ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET,
                ClassificationDocumentType.DIRECTORS_AND_OFFICERS_PDF,
                ClassificationDocumentType.EMPLOYEE_HANDBOOK_PDF,
                ClassificationDocumentType.ERISA_FORM_5500_PDF,
                ClassificationDocumentType.ERISA_FORM_5500_SF_PDF,
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_BENEFITS_PLAN,
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_SHAREHOLDER_INFO,
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_EMPLOYEE_INFO,
                ClassificationDocumentType.ORG_CHART_PDF,
            }
        )

    @staticmethod
    def skip_bc_classifications() -> set[str]:
        return {
            ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET,
            ClassificationDocumentType.VEHICLES_SPREADSHEET,
            ClassificationDocumentType.VEHICLES_PDF,
            ClassificationDocumentType.ACORD_127,
            ClassificationDocumentType.APPLIED_125,
            ClassificationDocumentType.OFAPPINFCNI,
        }

    @staticmethod
    def pds_classifications() -> set[str]:
        return (
            ClassificationDocumentType.skip_onboarding_classifications()
            | ClassificationDocumentType.entity_mapping_classifications()
            | ClassificationDocumentType.data_onboarding_classifications()
        )

    @staticmethod
    def loss_run_classifications() -> set[ClassificationDocumentType]:
        return {
            ClassificationDocumentType.LOSS_RUN,
            ClassificationDocumentType.LOSS_RUN_PDF,
            ClassificationDocumentType.LOSS_RUN_SPREADSHEET,
            ClassificationDocumentType.LOSS_RUN_EDITABLE_DOC,
            ClassificationDocumentType.LOSS_SUMMARY,
            ClassificationDocumentType.LOSS_SUMMARY_PDF,
            ClassificationDocumentType.LOSS_SUMMARY_SPREADSHEET,
            ClassificationDocumentType.LOSS_SUMMARY_EDITABLE_DOC,
            ClassificationDocumentType.LOSS_RUN_NO_CLAIM,
            ClassificationDocumentType.LOSS_RUN_NO_CLAIM_PDF,
            ClassificationDocumentType.LOSS_RUN_NO_CLAIM_SPREADSHEET,
            ClassificationDocumentType.LOSS_RUN_NO_CLAIM_EDITABLE_DOC,
        }

    @staticmethod
    def drop_not_resolved_named_insureds_classifications() -> set[ClassificationDocumentType]:
        return {
            ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET,
            ClassificationDocumentType.ACORD_125,
            ClassificationDocumentType.APPLIED_125,
            ClassificationDocumentType.OFAPPINFCNI,
        }

    @staticmethod
    def files_with_named_insureds_classifications() -> set[ClassificationDocumentType]:
        return {
            ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET,
            ClassificationDocumentType.ACORD_125,
            ClassificationDocumentType.ACORD_130,
            ClassificationDocumentType.ACORD_140,
            ClassificationDocumentType.APPLIED_125,
            ClassificationDocumentType.OFAPPINFCNI,
        }

    @staticmethod
    def files_with_names_only_extracted() -> set[ClassificationDocumentType]:
        return {
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
            ClassificationDocumentType.DIRECTORS_AND_OFFICERS_PDF,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_SPREADSHEET,
        }

    @staticmethod
    def supplementals_not_due_to_em() -> set[ClassificationDocumentType]:
        return {
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_SHAREHOLDER_INFO,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_BENEFITS_PLAN,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_EMPLOYEE_INFO,
        }

    @staticmethod
    def no_preview_document_types() -> set[ClassificationDocumentType]:
        return {
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_SHAREHOLDER_INFO,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_BENEFITS_PLAN,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_EMPLOYEE_INFO,
        }

    @staticmethod
    def supplemental_pdf_classifications() -> set[ClassificationDocumentType]:
        return {
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.TRANSPORTATION_SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.PROJECT_OWNERS_INTEREST_SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.RESIDENTIAL_REAL_ESTATE_SUPPLEMENTAL_APPLICATION_PDF,
            ClassificationDocumentType.RESTAURANT_BAR_SUPPLEMENTAL_APPLICATION_PDF,
        }
