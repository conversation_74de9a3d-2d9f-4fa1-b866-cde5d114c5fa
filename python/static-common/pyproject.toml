[project]
name = "static-common"
version = "25.5.27.53854.dev0"
description = "Kalepa static-common package"
authors = [{ name = "Kalepa Tech" }]
readme = "README.md"
requires-python = ">=3.11,<3.13"
dependencies = [
    # Try to keep this list to a minimum, this project should be as lightweight as possible. If you need to add a dependency, consider moving your code to a different project.
    "dataclasses-json~=0.5",
    "dateparser~=1.1",
    "fuzzywuzzy>=0.18.0",
    "intervaltree~=3.1",
    "marshmallow>=3.3.0",
    "marshmallow-enum~=1.5",
    "marshmallow-oneofschema>=2.0.1",
    "pydantic>=2.10.6",
    # remove after all projects are migrated to uv — Poetry fails to resolve the dependency correctly when version is only specified in the optional-dependencies section
    "infrastructure-common",
]

[dependency-groups]
dev = ["pytest~=7.2", "pytest-mock~=3.14"]
kalepa = ["infrastructure-common"]

[tool.uv.sources]
infrastructure-common = { index = "kalepi" }
intervaltree = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[tool.uv]
package = true
default-groups = ["dev", "kalepa"]

[tool.black]
line-length = 120
preview = true
target-version = ['py311']

[tool.isort]
profile = "black"
skip = ["__init__.py"]

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
allowed-confusables = ["–"]
line-length = 120
target-version = "py311"
extend-exclude = [
    "test/",
    "static_common/enums/class_code.py",
    "static_common/mappings/categories_naics.py",
    "**/__init__.py",
]

[tool.ruff.per-file-ignores]
"static_common/enums/fact_subtype.py" = ["E501"]
"static_common/enums/document_type.py" = ["E501"]
