from static_common.enums.supplemental_category import SupplementalCategory
from static_common.models.file_additional_info import (
    AcordClassificationInfo,
    FileAdditionalInfo,
    SupplementalClassificationInfo,
)
from static_common.schemas.file_additional_info import FileAdditionalInfoSchema


def test_nonempty_input():
    file_additional_info_json = """{
        "acord": {
            "form_name": "acord_125",
            "version_id": "acord_125_oh_2009_09",
            "form_year": 2009
        }
    }"""
    expected = FileAdditionalInfo(
        AcordClassificationInfo(version_id="acord_125_oh_2009_09", form_name="acord_125", form_year=2009)
    )
    assert FileAdditionalInfoSchema().loads(file_additional_info_json) == expected


def test_empty_input():
    file_additional_info_json = """{"acord": null}"""
    expected = FileAdditionalInfo(acord=None)
    assert FileAdditionalInfoSchema().loads(file_additional_info_json) == expected


def test_supplemental_empty_input():
    file_additional_info_json = """{
        "acord": {
            "form_name": "acord_125",
            "version_id": "acord_125_oh_2009_09",
            "form_year": 2009
        },
        "supplemental": null
    }"""
    expected = FileAdditionalInfo(
        AcordClassificationInfo(version_id="acord_125_oh_2009_09", form_name="acord_125", form_year=2009),
        supplemental=None,
    )
    assert FileAdditionalInfoSchema().loads(file_additional_info_json) == expected


def test_supplemental_not_empty_input():
    file_additional_info_json = """{
        "supplemental": {
            "template_name": "ADMIRAL"
        }
    }"""
    expected = FileAdditionalInfo(supplemental=SupplementalClassificationInfo(template_name="ADMIRAL"))
    assert FileAdditionalInfoSchema().loads(file_additional_info_json) == expected


def test_dump_empty_input():
    file_additional_info = FileAdditionalInfo(email_id="my_email_id")
    expected = """{"email_id": "my_email_id"}"""
    assert FileAdditionalInfoSchema().dumps(file_additional_info) == expected


def test_num_of_pages():
    file_additional_info_json = """{
        "acord": {
            "form_name": "acord_125",
            "version_id": "acord_125_oh_2009_09",
            "form_year": 2009
        },
        "number_of_pages": 5
    }"""
    expected = FileAdditionalInfo(
        AcordClassificationInfo(version_id="acord_125_oh_2009_09", form_name="acord_125", form_year=2009),
        supplemental=None,
        number_of_pages=5,
    )
    assert FileAdditionalInfoSchema().loads(file_additional_info_json) == expected


def test_supp_category():
    file_additional_info_json = """{
        "supplemental": {
            "template_name": "ADMIRAL",
            "category": "GENERAL_CONSTRUCTION",
            "category_description": "General Construction"
        }
    }"""
    expected = FileAdditionalInfo(
        supplemental=SupplementalClassificationInfo(
            template_name="ADMIRAL",
            category=SupplementalCategory.GENERAL_CONSTRUCTION,
            category_description="General Construction",
        )
    )
    assert FileAdditionalInfoSchema().loads(file_additional_info_json) == expected


def test_policy_type():
    file_additional_info_json = """{
        "acord": {
            "form_name": "acord_125",
            "version_id": "acord_125_oh_2009_09",
            "form_year": 2009
        },
        "policy_carrier": "CLIENT_NAME"
    }"""
    expected = FileAdditionalInfo(
        AcordClassificationInfo(version_id="acord_125_oh_2009_09", form_name="acord_125", form_year=2009),
        supplemental=None,
        policy_carrier="CLIENT_NAME",
    )
    assert FileAdditionalInfoSchema().loads(file_additional_info_json) == expected
