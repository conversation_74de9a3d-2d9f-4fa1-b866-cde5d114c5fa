from uuid import UUID

from pytest import fixture

from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.submission_business import SubmissionBusinessEntityRole
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.file_onboarding import (
    AdditionalData,
    Evidence,
    ExtractedEntityInfo,
    FactSubtypeSuggestion,
    FileEntitiesExtractionInfo,
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
    SubmissionEntity,
    SuggestedField,
    ValueSuggestionResult,
)
from static_common.models.matching_metadata import MatcherType, MatchingMetadata
from static_common.schemas.file_onboarding import (
    LeanOnboardedFileSchema,
    OnboardedFileSchema,
)


@fixture
def onboarded_file():
    return OnboardedFile(
        files=[UUID("b277f51b-741e-42cf-9664-56f80937d769")],
        entities=[
            SubmissionEntity(
                type=SubmissionEntityType.BUSINESS,
                entity_role=SubmissionBusinessEntityRole.PROJECT,
                id="1",
            ),
            SubmissionEntity(
                type=SubmissionEntityType.STRUCTURE,
                id="2",
                resolved=True,
                parent_idx=0,
                remote_id="1",
                resolution_id=UUID("db4cfaa1-66f9-45b4-a5b5-8b77dc0dddbb"),
            ),
        ],
        entity_information=[
            ResolvedDataField(
                name="Name",
                values=[ResolvedDataValue(value="Entity1", entity_idx=0, file_idx=0)],
                value_type=FieldType.TEXT,
                display_as_fact=False,
            ),
            ResolvedDataField(
                name="Address",
                values=[
                    ResolvedDataValue(
                        value="Address1",
                        entity_idx=0,
                        file_idx=0,
                        external_file_id=UUID("db4cfaa1-66f9-45b4-a5b5-8b77dc0ddd4b"),
                        related_entity_idx=0,
                    )
                ],
                value_type=FieldType.TEXT,
                display_as_fact=False,
            ),
        ],
        fields=[
            ResolvedDataField(
                name="Bldg Values",
                observed_name="Building Values",
                matching_metadata=MatchingMetadata(
                    matcher_type=MatcherType.LLM,
                    matching_explanation={
                        "closest_embedding_value": ["Fetched Building Value"],
                    },
                    is_dropped=False,
                ),
                values=[
                    ResolvedDataValue(
                        value=0,
                        manually_added=True,
                        observed_value="0",
                        file_idx=0,
                        entity_idx=1,
                    ),
                    ResolvedDataValue(
                        value=1000,
                        suggested_values=[
                            ValueSuggestionResult(
                                value="2137", confidence=0.1, auto_apply=True, field_type=FieldType.TEXT
                            )
                        ],
                        manually_added=False,
                        explanations=[],
                        file_idx=1,
                        entity_idx=0,
                    ),
                ],
                value_type=FieldType.TEXT,
                display_as_fact=True,
                fact_subtype_id=FactSubtypeID.BUILDING_VALUE,
                fact_subtype_suggestions=[
                    FactSubtypeSuggestion(
                        fact_subtype_id=FactSubtypeID.FEIN,
                        field_type=FieldType.INTEGER,
                        name_score=0.1,
                        values_score=1.0,
                        confidence=0.1,
                        explanations=["this is the best!"],
                    )
                ],
            )
        ],
        additional_data=AdditionalData(fe_properties={"anything": "yes"}),
    )


@fixture
def serialized_onboarded_file():
    return {
        "fields": [
            {
                "name": "Bldg Values",
                "observed_name": "Building Values",
                "matching_metadata": {
                    "matcher_type": "LLM",
                    "parser_type": None,
                    "matching_explanation": {"closest_embedding_value": ["Fetched Building Value"]},
                    "is_dropped": False,
                    "dropped_explanation": None,
                },
                "values": [
                    {
                        "value": 0,
                        "manually_added": True,
                        "explanations": [],
                        "observed_name": None,
                        "observed_value": "0",
                        "suggested_values": [],
                        "entity_idx": 1,
                        "file_idx": 0,
                        "external_file_id": None,
                        "evidences": [],
                        "validation_result": None,
                        "related_entity_idx": None,
                    },
                    {
                        "value": 1000,
                        "manually_added": False,
                        "explanations": [],
                        "observed_name": None,
                        "observed_value": None,
                        "suggested_values": [
                            {
                                "value": "2137",
                                "confidence": 0.1,
                                "auto_apply": True,
                                "field_type": "TEXT",
                            }
                        ],
                        "entity_idx": 0,
                        "file_idx": 1,
                        "external_file_id": None,
                        "evidences": [],
                        "validation_result": None,
                        "related_entity_idx": None,
                    },
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "BUILDING_VALUE",
                "naics_code": None,
                "unit": None,
                "group_ids": None,
                "aggregation_type": None,
                "position": None,
                "fact_subtype_suggestions": [
                    {
                        "fact_subtype_id": "FEIN",
                        "field_type": "INTEGER",
                        "name_score": 0.1,
                        "values_score": 1.0,
                        "confidence": 0.1,
                        "explanations": ["this is the best!"],
                    }
                ],
            }
        ],
        "entity_information": [
            {
                "name": "Name",
                "observed_name": None,
                "matching_metadata": None,
                "values": [
                    {
                        "value": "Entity1",
                        "manually_added": False,
                        "explanations": [],
                        "observed_name": None,
                        "observed_value": None,
                        "suggested_values": [],
                        "entity_idx": 0,
                        "file_idx": 0,
                        "external_file_id": None,
                        "evidences": [],
                        "validation_result": None,
                        "related_entity_idx": None,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": False,
                "fact_subtype_id": None,
                "fact_subtype_suggestions": [],
                "naics_code": None,
                "unit": None,
                "group_ids": None,
                "aggregation_type": None,
                "position": None,
            },
            {
                "name": "Address",
                "observed_name": None,
                "matching_metadata": None,
                "values": [
                    {
                        "value": "Address1",
                        "manually_added": False,
                        "explanations": [],
                        "observed_name": None,
                        "observed_value": None,
                        "suggested_values": [],
                        "entity_idx": 0,
                        "file_idx": 0,
                        "external_file_id": "db4cfaa1-66f9-45b4-a5b5-8b77dc0ddd4b",
                        "evidences": [],
                        "validation_result": None,
                        "related_entity_idx": 0,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": False,
                "fact_subtype_id": None,
                "fact_subtype_suggestions": [],
                "naics_code": None,
                "unit": None,
                "group_ids": None,
                "aggregation_type": None,
                "position": None,
            },
        ],
        "entities": [
            {
                "type": "Business",
                "entity_named_insured": None,
                "entity_role": "PROJECT",
                "id": "1",
                "remote_id": None,
                "resolution_id": None,
                "resolved": False,
                "parent_idx": None,
                "acord_location_information": None,
            },
            {
                "type": "Structure",
                "entity_named_insured": None,
                "entity_role": None,
                "id": "2",
                "remote_id": "1",
                "resolution_id": "db4cfaa1-66f9-45b4-a5b5-8b77dc0dddbb",
                "resolved": True,
                "parent_idx": 0,
                "acord_location_information": None,
            },
        ],
        "files": ["b277f51b-741e-42cf-9664-56f80937d769"],
        "transient_data": None,
        "additional_data": {"finished_do_sub_step": None, "fe_properties": {"anything": "yes"}},
    }


@fixture
def serialized_lean_onboarded_file():
    return {
        "fields": [
            {
                "name": "Bldg Values",
                "observed_name": "Building Values",
                "matching_metadata": {
                    "matcher_type": "LLM",
                    "parser_type": None,
                    "matching_explanation": {"closest_embedding_value": ["Fetched Building Value"]},
                    "is_dropped": False,
                    "dropped_explanation": None,
                },
                "values": [
                    {
                        "value": 0,
                        "manually_added": True,
                        "observed_value": "0",
                        "entity_idx": 1,
                    },
                    {
                        "value": 1000,
                        "entity_idx": 0,
                        "file_idx": 1,
                        "suggested_values": [
                            {
                                "value": "2137",
                                "confidence": 0.1,
                                "auto_apply": True,
                                "field_type": "TEXT",
                            }
                        ],
                    },
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "BUILDING_VALUE",
                "fact_subtype_suggestions": [
                    {
                        "fact_subtype_id": "FEIN",
                        "field_type": "INTEGER",
                        "name_score": 0.1,
                        "values_score": 1.0,
                        "confidence": 0.1,
                        "explanations": ["this is the best!"],
                    }
                ],
            }
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [
                    {
                        "value": "Entity1",
                        "entity_idx": 0,
                    }
                ],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
            {
                "name": "Address",
                "values": [
                    {
                        "value": "Address1",
                        "entity_idx": 0,
                        "external_file_id": "db4cfaa1-66f9-45b4-a5b5-8b77dc0ddd4b",
                        "related_entity_idx": 0,
                    }
                ],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
        ],
        "entities": [
            {
                "type": "Business",
                "entity_role": "PROJECT",
                "id": "1",
            },
            {
                "type": "Structure",
                "id": "2",
                "remote_id": "1",
                "resolution_id": "db4cfaa1-66f9-45b4-a5b5-8b77dc0dddbb",
                "resolved": True,
                "parent_idx": 0,
            },
        ],
        "files": ["b277f51b-741e-42cf-9664-56f80937d769"],
        "additional_data": {"finished_do_sub_step": None, "fe_properties": {"anything": "yes"}},
    }


def test_serialize_onboarded_file_regular(onboarded_file, serialized_onboarded_file):
    assert serialized_onboarded_file == OnboardedFileSchema().dump(onboarded_file)


def test_deserialize_onboarded_file_regular(onboarded_file, serialized_onboarded_file):
    assert onboarded_file == OnboardedFileSchema().load(serialized_onboarded_file)


def test_serialize_onboarded_file_lean(onboarded_file, serialized_lean_onboarded_file):
    assert serialized_lean_onboarded_file == LeanOnboardedFileSchema().dump(onboarded_file)


def test_deserialize_onboarded_file_lean(onboarded_file, serialized_lean_onboarded_file):
    assert onboarded_file == LeanOnboardedFileSchema().load(serialized_lean_onboarded_file)


def test_deserialize_onboarded_file_regular_with_lean_schema(onboarded_file, serialized_onboarded_file):
    assert onboarded_file == LeanOnboardedFileSchema().load(serialized_onboarded_file)


def test_additional_data():
    additional_data = AdditionalData()
    onboarded_file = OnboardedFile()
    onboarded_file.additional_data = additional_data
    assert "additional_data" in LeanOnboardedFileSchema().dump(onboarded_file)
    assert LeanOnboardedFileSchema().load({}).additional_data.suggested_fields == []
    assert LeanOnboardedFileSchema().load({}).additional_data.finished_do_sub_step == None
    assert LeanOnboardedFileSchema().load({}).additional_data.fe_properties == None


def test_suggested_field():
    e1 = Evidence(file_idx=0, page=4)
    e2 = Evidence(file_idx=0)
    suggested_field = SuggestedField(name="some", evidences=[e1, e2])
    onboarded_file = OnboardedFile()
    onboarded_file.additional_data = AdditionalData(suggested_fields=[suggested_field])
    dump = LeanOnboardedFileSchema().dump(onboarded_file)
    assert dump["additional_data"]["suggested_fields"] == [
        {"evidences": [{"page": 4, "type": "Evidence"}, {"type": "Evidence"}], "name": "some"}
    ]
    loaded = LeanOnboardedFileSchema().load(dump)
    assert len(loaded.additional_data.suggested_fields) == 1
    suggested_field = loaded.additional_data.suggested_fields[0]
    assert suggested_field.name == "some"
    assert len(suggested_field.evidences) == 2
    assert suggested_field.evidences[0].file_idx == 0
    assert suggested_field.evidences[1].file_idx == 0


def test_extracted_entities_info():
    extracted_entities_info = [
        FileEntitiesExtractionInfo(
            file_idx=0, extracted_entities=[ExtractedEntityInfo(count=1, entity_type=SubmissionEntityType.BUSINESS)]
        ),
        FileEntitiesExtractionInfo(
            file_idx=1,
            extracted_entities=[ExtractedEntityInfo(count=2, entity_type=SubmissionEntityType.GENERAL_CONTRACTOR)],
        ),
        FileEntitiesExtractionInfo(
            file_idx=2,
            extracted_entities=[],
        ),
    ]
    onboarded_file = OnboardedFile()
    onboarded_file.additional_data = AdditionalData(extracted_entities_info=extracted_entities_info)
    dump = LeanOnboardedFileSchema().dump(onboarded_file)
    assert dump["additional_data"]["extracted_entities_info"] == [
        {"file_idx": 0, "number_of_rows": 1, "extracted_entities": [{"entity_type": "Business", "count": 1}]},
        {"file_idx": 1, "number_of_rows": 2, "extracted_entities": [{"entity_type": "General Contractor", "count": 2}]},
    ]
    loaded = LeanOnboardedFileSchema().load(dump)
    assert len(loaded.additional_data.extracted_entities_info) == 2
    extracted_entities_info = loaded.additional_data.extracted_entities_info
    assert extracted_entities_info[0].file_idx == 0
    assert extracted_entities_info[0].number_of_rows == 1
    assert extracted_entities_info[0].extracted_entities == [
        ExtractedEntityInfo(entity_type=SubmissionEntityType.BUSINESS, count=1)
    ]


def test_add_field_values(onboarded_file):
    onboarded_file.add_or_create_entity_field(
        ResolvedDataField(
            name="Name",
            values=[ResolvedDataValue(value="NewEntity1", entity_idx=0, file_idx=0)],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        )
    )

    assert len(onboarded_file.entities) == 2
    assert len(onboarded_file.entity_information) == 2
    assert onboarded_file.entity_information[0].name == "Name"
    assert onboarded_file.entity_information[0].values[0].value == "Entity1"

    onboarded_file.add_or_create_entity_field(
        ResolvedDataField(
            name="Name",
            values=[ResolvedDataValue(value="NewEntity1", entity_idx=1, file_idx=0)],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        )
    )

    assert len(onboarded_file.entities) == 2
    assert len(onboarded_file.entity_information) == 2
    assert onboarded_file.entity_information[0].name == "Name"
    assert onboarded_file.entity_information[0].values[0].value == "Entity1"
    assert onboarded_file.entity_information[0].values[1].value == "NewEntity1"

    onboarded_file.add_or_create_entity_field(
        ResolvedDataField(
            name="Bldg Values",
            values=[ResolvedDataValue(value="NewEntity1", entity_idx=0, file_idx=0)],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        ),
        entity_information=False,
    )

    onboarded_file.add_or_create_entity_field(
        ResolvedDataField(
            name="Bldg Values",
            values=[ResolvedDataValue(value="NewEntity2", entity_idx=0, file_idx=0)],
            value_type=FieldType.TEXT,
            display_as_fact=False,
            fact_subtype_id=FactSubtypeID.ENTERPRISE_VALUE,
        ),
        entity_information=False,
    )

    assert len(onboarded_file.entities) == 2
    assert len(onboarded_file.fields) == 2
    assert len(onboarded_file.fields[0].values) == 2
    assert onboarded_file.fields[0].name == "Bldg Values"
    assert onboarded_file.fields[0].values[0].value == 0
    assert len(onboarded_file.fields[1].values) == 1
    assert onboarded_file.fields[1].name == "Bldg Values"
    assert onboarded_file.fields[1].values[0].value == "NewEntity2"


def test_add_field_value_with_confidence(onboarded_file):
    onboarded_file.add_or_create_entity_field(
        ResolvedDataField(
            name="ConfField",
            values=[ResolvedDataValue(value="ConfEntity1", entity_idx=0, evidences=[Evidence(page=0, confidence=0.8)])],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        ),
        entity_information=False,
    )

    assert len(onboarded_file.fields) == 2
    assert onboarded_file.fields[1].name == "ConfField"
    assert onboarded_file.fields[1].values[0].value == "ConfEntity1"

    onboarded_file.add_or_create_entity_field(
        ResolvedDataField(
            name="ConfField",
            values=[
                ResolvedDataValue(value="ConfPoorEntity1", entity_idx=0, evidences=[Evidence(page=0, confidence=0.7)])
            ],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        ),
        entity_information=False,
    )

    assert len(onboarded_file.fields) == 2
    assert len(onboarded_file.fields[1].values) == 1
    assert onboarded_file.fields[1].name == "ConfField"
    assert onboarded_file.fields[1].values[0].value == "ConfEntity1"

    onboarded_file.add_or_create_entity_field(
        ResolvedDataField(
            name="ConfField",
            values=[
                ResolvedDataValue(value="ConfNewEntity1", entity_idx=0, evidences=[Evidence(page=0, confidence=0.9)])
            ],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        ),
        entity_information=False,
    )

    assert len(onboarded_file.fields) == 2
    assert len(onboarded_file.fields[1].values) == 1
    assert onboarded_file.fields[1].name == "ConfField"
    assert onboarded_file.fields[1].values[0].value == "ConfNewEntity1"


def test_deduplication(onboarded_file):
    entity = SubmissionEntity(type=SubmissionEntityType.BUSINESS, id="1")
    e_info = [
        ResolvedDataField(
            name="Name",
            values=[ResolvedDataValue(value="NewEntity1", entity_idx=0, file_idx=0)],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        )
    ]
    f_info = [
        ResolvedDataField(
            name="NewField",
            values=[ResolvedDataValue(value="NewEntity1", entity_idx=0, file_idx=0)],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        )
    ]
    onboarded_file.add_or_create_entity(entity, entity_information=e_info, fields=f_info, entity_idx=0)

    assert len(onboarded_file.entities) == 2
    assert len(onboarded_file.entity_information) == 2
    assert onboarded_file.entity_information[0].name == "Name"
    assert onboarded_file.entity_information[0].values[0].value == "Entity1"
    assert len(onboarded_file.fields) == 2
    assert onboarded_file.fields[1].name == "NewField"
    assert onboarded_file.fields[1].values[0].value == "NewEntity1"

    entity = SubmissionEntity(type=SubmissionEntityType.BUSINESS, id="3")
    e_info = [
        ResolvedDataField(
            name="Name",
            values=[ResolvedDataValue(value="NewEntity3", entity_idx=0, file_idx=0)],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        )
    ]
    f_info = [
        ResolvedDataField(
            name="NewField",
            values=[ResolvedDataValue(value="NewEntity3", entity_idx=0, file_idx=0)],
            value_type=FieldType.TEXT,
            display_as_fact=False,
        )
    ]
    onboarded_file.add_or_create_entity(entity, entity_information=e_info, fields=f_info, entity_idx=0)

    assert len(onboarded_file.entities) == 3
    assert len(onboarded_file.entity_information) == 2
    assert onboarded_file.entity_information[0].name == "Name"
    assert onboarded_file.entity_information[0].values[1].value == "NewEntity3"
    assert len(onboarded_file.fields) == 2
    assert onboarded_file.fields[1].name == "NewField"
    assert onboarded_file.fields[1].values[1].value == "NewEntity3"
