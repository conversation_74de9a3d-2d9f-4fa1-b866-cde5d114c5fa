import email
from email.message import EmailMessage
from email.parser import Bytes<PERSON>arser
from unittest.mock import MagicMock

import pytest
from file_processing.email_extractor import EmailExtractor
from pytest import fixture

from src.handlers.emails_processor import EmailsProcessor
from src.logic.emails_processor import is_iscmga_submission_email
from src.models.email_data import EmailAttachment, EmailData

credentials = MagicMock()
imap_mock = MagicMock()
EmailsProcessor._setup_folders = MagicMock(return_value=None)


def get_file(file_name: str) -> EmailMessage:
    with open(f"test/data/email_parser/{file_name}", "rb") as f:
        mail = BytesParser(policy=email.policy.default).parsebytes(f.read())
    return mail


def test_single_eml(mocker):
    mocker.patch("src.handlers.emails_processor.get_imap_server", return_value=imap_mock)
    ep = EmailsProcessor(credentials)
    file = get_file("single_eml.eml")
    data = ep._process_attachments(file)

    assert len(data) == 1
    assert data[0].name == "new marcin submission.eml"

    embedded_email = EmailExtractor.parse_from_eml_binary(data[0].content)

    assert len(embedded_email.attachments) == 1
    assert embedded_email.attachments[0].name == "small_SOV.xlsx"
    assert len(embedded_email.attachments[0].binary) > 7700


def test_eml_in_eml(mocker):
    mocker.patch("src.handlers.emails_processor.get_imap_server", return_value=imap_mock)
    ep = EmailsProcessor(credentials)
    file = get_file("eml_in_eml.eml")
    data = ep._process_attachments(file)

    assert len(data) == 1
    assert data[0].name == "Marcin Submission After body batc.eml"

    # Email Extractor flattens the email structure and extracts all attachments ignoring nested embedded email
    embedded_email = EmailExtractor.parse_from_eml_binary(data[0].content)

    assert len(embedded_email.attachments) == 1
    assert embedded_email.attachments[0].name == "small_SOV.xlsx"
    assert len(embedded_email.attachments[0].binary) > 7700


@fixture
def email_data():
    return EmailData(
        account="<EMAIL>",
        thread_id="test_thread_id",
        message_id="test_message_id",
        subject="test_subject",
        email_from="test_email_from",
        email_to="test_email_to",
        email_cc=["test_email_cc"],
        email_reply_to="test_email_reply_to",
        email_body="test_email_body",
        email_text_body=None,
        email_date=None,
        references="test_references",
        attachments=[],
        email_message_id="test_email_message_id",
        copilot_email_id=None,
        copilot_correspondence_id=None,
    )


@pytest.mark.parametrize(
    "email_body, email_subject, has_attachment, expected_result",
    [
        ("", "", False, True),
        ("", "", True, True),
        ("body without negative keywords", "subject without negative keywords", False, True),
        ("body without negative keywords", "subject without negative keywords", True, True),
        ("body with negative keywords: Bind Task", "subject without negative keywords", False, False),
        ("body without negative keywords", "subject with negative keywords: 45-Days Subjectivities", True, False),
    ],
)
def test_is_iscmga_submission_email_with_attachments(
    email_data, email_body, email_subject, has_attachment, expected_result
):
    email_data.email_body = email_body
    email_data.subject = email_subject
    if has_attachment:
        email_data.attachments = [EmailAttachment(name="test_name", size=1, content="test_content")]
    assert is_iscmga_submission_email(email_data) == expected_result
