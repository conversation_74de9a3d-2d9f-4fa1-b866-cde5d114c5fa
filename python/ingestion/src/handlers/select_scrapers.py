import datetime as dt
import os
from typing import Any, Dict, Sequence

import sentry_sdk
from events_common.model.types import StepFunctionStatus
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from more_itertools import flatten

from src.constants import RELATIONS_TYPES_FOR_EXTENDED_SCRAPE
from src.logic.select_scrapers import run_selected_scrapers, update_running_arns
from src.models.flow import RunScrapersState
from src.models.scraping_metadata import ScrapingMetadata
from src.models.types import Source
from src.schemas.flow import RunScrapersStateSchema
from src.session import get_queue_pool_session

logger = get_logger()

sentry_sdk.init(
    os.environ.get("SENTRY_DSN"),
    environment=os.environ.get("KALEPA_ENV", "dev"),
)

TWENTY_FOUR_HOURS = 24
ers_client_v3 = None
run_scrapers_state_schema = RunScrapersStateSchema()


def get_ers_client_v3():
    global ers_client_v3
    if not ers_client_v3:
        from src.clients.ers_v3 import ERSClientV3

        ers_client_v3 = ERSClientV3(os.environ["ENTITY_RESOLUTION_SERVICE_V3_URL"])
    return ers_client_v3


def log_function_inputs(fn):
    def func(*args, **kwargs):
        logger.info("Executing function", function_name=fn.__name__, args=args, kwargs=kwargs)
        result = fn(*args, **kwargs)
        logger.info("Execution of function finished", function_name=fn.__name__, args=args, kwargs=kwargs)
        return result

    return func


def extend_dict_list(d: Dict, key: Any, val: Any):
    if key in d:
        d[key].append(val)
    else:
        d[key] = [val]


@bind_lambda_logging_context
@log_function_inputs
def select_scrapers_bulk(event, context=None):
    # This is used by FE for verification. It has to be fast, so we skip ERS checks.
    return _select_scrapers_bulk_impl(event, context, scrape_related_entities=False)


def _select_scrapers_bulk_impl(event, context, scrape_related_entities=True) -> Dict[str, Dict]:
    execution_id = context.aws_request_id if context else None
    skip_recently_ingested = isinstance(event, list) or event.pop("skip_recently_ingested", True)
    result_template = {
        "kalepa_id": None,
        "scrape_yelp": False,
        "scrape_trip": False,
        "scrape_google_local": False,
        "scrape_facebook": False,
        "scrape_buildzoom": False,
        "scrape_better_business_bureau": False,
        "scrape_thomasnet": False,
        "scrape_houzz": False,
        "scrape_iqs": False,
        "scrape_fmcsa": False,
        "scrape_safer_fmcsa": False,
        "scrape_good_jobs_first": False,
        "scrape_epa": False,
        "scrape_osha": False,
        "scrape_fda": False,
        "scrape_unicourt": False,
        "scrape_sam": False,
        "scrape_opencorporates": False,
        "scrape_erisa_5500": False,
        "scrape_apartments": False,
        "scrape_irs_form_990": False,
        "scrape_cs_cslb": False,
        "execution_id": execution_id,
    }
    logger = get_logger().bind(
        execution_id=execution_id,
        skip_recently_ingested=skip_recently_ingested,
    )
    db_session = get_queue_pool_session()
    try:
        kalepa_ids = event if isinstance(event, list) else event.pop("kalepa_ids")
        logger = logger.bind(kalepa_ids=kalepa_ids, scrape_related_entities=scrape_related_entities)
        all_metadatas = db_session.query(ScrapingMetadata).filter(ScrapingMetadata.kalepa_id.in_(kalepa_ids)).all()
        if scrape_related_entities:
            entities = get_ers_client_v3().get_entities(kalepa_ids)
        else:
            entities = {}
        results: Dict[str, Dict] = {}
        for kalepa_id in kalepa_ids:
            logger = logger.bind(kalepa_id=kalepa_id)
            redirected_to = None
            if scrape_related_entities and kalepa_id not in entities:
                if len(kalepa_ids) == 1 and len(entities) == 1:
                    redirected_to = list(entities.keys())[0]  # redirected easy to detect
                    logger.bind(redirected_to=redirected_to)
                else:
                    logger.info("Skipping kalepa_id because it is not in ERS")
                    continue
            result = result_template.copy()
            result["kalepa_id"] = kalepa_id
            metadatas = [metadata for metadata in all_metadatas if str(metadata.kalepa_id) == str(kalepa_id)]
            logger.bind(metadatas_count=len(metadatas))
            now = dt.datetime.utcnow()
            # TODO: support multiple urls for one source_type
            for metadata in metadatas:
                logger.bind(metadata=metadata, source_type=metadata.source_type)
                if _should_skip(skip_recently_ingested, metadata, now):
                    logger.info(
                        "Skipping source_type for kalepa_id because it was recently ingested",
                    )
                    continue
                if metadata.source_type == "yelp-business":
                    result["scrape_yelp"] = True
                    result["yelp_url"] = metadata.url
                if metadata.source_type == "tripadvisor-business":
                    result["scrape_trip"] = True
                    result["trip_url"] = metadata.url
                if metadata.source_type == "google-business":
                    result["scrape_google_local"] = True
                    result["google_local_url"] = metadata.url
                if metadata.source_type == "facebook-business":
                    result["scrape_facebook"] = True
                    result["facebook_url"] = metadata.url
                if metadata.source_type == "buildzoom-business":
                    result["scrape_buildzoom"] = True
                    result["buildzoom_url"] = metadata.url
                if metadata.source_type == Source.BETTER_BUSINESS_BUREAU.value:
                    result["scrape_better_business_bureau"] = True
                    result["better_business_bureau_url"] = metadata.url
                if metadata.source_type == Source.THOMASNET_BUSINESS.value:
                    result["scrape_thomasnet"] = True
                    result["thomasnet_url"] = metadata.url
                if metadata.source_type == Source.IQS_BUSINESS.value:
                    result["scrape_iqs"] = True
                    result["iqs_url"] = metadata.url
                if metadata.source_type == Source.HOUZZ_BUSINESS.value:
                    result["scrape_houzz"] = True
                    result["houzz_url"] = metadata.url
                if metadata.source_type == Source.FMCSA_BUSINESS.value:
                    result["scrape_fmcsa"] = True
                    result["fmcsa_url"] = metadata.url
                if metadata.source_type == Source.SAFER_FMCSA_BUSINESS.value:
                    result["scrape_safer_fmcsa"] = True
                    result["safer_fmcsa_url"] = metadata.url
                if metadata.source_type == Source.GOOD_JOBS_FIRST_BUSINESS.value:
                    result["scrape_good_jobs_first"] = True
                    result["good_jobs_first_url"] = metadata.url
                if metadata.source_type == Source.EPA_BUSINESS.value:
                    result["scrape_epa"] = True
                    result["epa_url"] = metadata.url
                if metadata.source_type == Source.OSHA_BUSINESS.value:
                    result["scrape_osha"] = True
                    extend_dict_list(result, "scrape_osha_urls", metadata.url)
                if metadata.source_type == Source.FDA_BUSINESS.value:
                    result["scrape_fda"] = True
                    result["fda_url"] = metadata.url
                if metadata.source_type == Source.UNICOURT.value:
                    result["scrape_unicourt"] = True
                    extend_dict_list(result, "scrape_unicourt_urls", metadata.url)
                if metadata.source_type == Source.SAM.value:
                    result["scrape_sam"] = True
                    result["sam_url"] = metadata.url
                if metadata.source_type == Source.OPENCORPORATES.value:
                    result["scrape_opencorporates"] = True
                    result["opencorporates_url"] = metadata.url
                if metadata.source_type == Source.ERISA_5500_BUSINESS.value:
                    result["scrape_erisa_5500"] = True
                    extend_dict_list(result, "scrape_erisa_5500_record_ids", metadata.url)
                if metadata.source_type == Source.APARTMENTS.value:
                    result["scrape_apartments"] = True
                    result["apartments_url"] = metadata.url
                if metadata.source_type == "irs-form-990":
                    result["scrape_irs_form_990"] = True
                    result["irs_form_990_url"] = metadata.url
                if metadata.source_type == "ca-cslb":
                    result["scrape_cs_cslb"] = True
                    result["cs_cslb_url"] = metadata.url

            if scrape_related_entities:
                # please notice we may not have all relations yet (eg from metadata.url) so in fallowing code we are
                # ingesting related entities according ERS (no only meta data)
                entity = entities.get(kalepa_id) or entities.get(redirected_to)
                all_related_ids_pairs = [
                    [relation.from_entity_id, relation.to_entity_id]
                    for relation in entity.relations_to
                    if relation.type in RELATIONS_TYPES_FOR_EXTENDED_SCRAPE and relation.is_active
                ]
                related_ids = list(flatten(all_related_ids_pairs))
                unique_target_ids = list({*related_ids, kalepa_id})
                if oc := get_extended_opencorporates(db_session, unique_target_ids, skip_recently_ingested, now):
                    result.update(oc)
            results[kalepa_id] = result
        db_session.commit()
        return results
    finally:
        db_session.close()


def get_extended_opencorporates(
    db_session, target_ids: Sequence[str], skip_recently_ingested: bool, now: dt.datetime
) -> Dict[str, Any]:
    result = {}
    opencorporates_metadata = (
        db_session.query(ScrapingMetadata)
        .filter(ScrapingMetadata.kalepa_id.in_(list(target_ids)))
        .filter(ScrapingMetadata.source_type == Source.OPENCORPORATES.value)
        .all()
    )
    if opencorporates_metadata:
        result["scrape_opencorporates"] = True
        result["opencorporates_metadata"] = [
            (str(metadata.kalepa_id), metadata.url)
            for metadata in opencorporates_metadata
            if not _should_skip(skip_recently_ingested, metadata, now)  # important to skip
        ]
        return result


def _should_skip(skip_recently_ingested: bool, metadata, now: dt.datetime) -> bool:
    return (
        skip_recently_ingested
        and metadata.scraper_start_date
        and metadata.scraper_start_date + dt.timedelta(hours=TWENTY_FOUR_HOURS) > now
    )


def _ensure_integrity_of_selected_scrapers(selected_scrapers: dict) -> dict:
    if selected_scrapers.get("scrape_opencorporates") and not selected_scrapers.get("opencorporates_metadata"):
        logger.warning(
            "Selected to scrape opencorporates, but no metadata was found", selected_scrapers=selected_scrapers
        )
        selected_scrapers["scrape_opencorporates"] = False

    return selected_scrapers


def _run_scrapers(run_scrapers_state: RunScrapersState, selected_scrapers_raw: dict):
    if not (selected_scrapers := selected_scrapers_raw.get(run_scrapers_state.kalepa_id)):
        run_scrapers_state.sf_status = StepFunctionStatus.SUCCESS
        return

    selected_scrapers = _ensure_integrity_of_selected_scrapers(selected_scrapers)

    run_scrapers_state.scraping_sf_arns = run_selected_scrapers(
        run_scrapers_state.kalepa_id, run_scrapers_state.sf_execution_id, selected_scrapers
    )

    if not run_scrapers_state.scraping_sf_arns:
        run_scrapers_state.sf_status = StepFunctionStatus.SUCCESS


def _run_scraping_sfs(scraping_state: RunScrapersState, context):
    selected_scrapers_raw = _select_scrapers_bulk_impl([scraping_state.kalepa_id], context)
    _run_scrapers(scraping_state, selected_scrapers_raw)


def _poll_scraping_sfs(scraping_state: RunScrapersState):
    if not scraping_state.scraping_sf_arns:
        logger.error("Loaded scrapers state, but no ARNs were supplied")
        raise ValueError("No scraping ARNs were supplied")

    logger.info("Updating currently running ARNs", current_arns=scraping_state.scraping_sf_arns)
    update_running_arns(scraping_state)

    if scraping_state.still_running:
        if scraping_state.wait_exceeded():
            logger.error("Wait for scraping exceeded, but still running", scraping_state=scraping_state)
            scraping_state.sf_status = StepFunctionStatus.FAILURE
        else:
            scraping_state.wait_rounds += 1


@bind_lambda_logging_context
def select_and_run_or_poll_scrapers(event, context=None):
    scraping_state: RunScrapersState = run_scrapers_state_schema.load(event)

    if scraping_state.scraping_sf_arns is None:
        _run_scraping_sfs(scraping_state, context)
    else:
        _poll_scraping_sfs(scraping_state)

    return run_scrapers_state_schema.dump(scraping_state)
