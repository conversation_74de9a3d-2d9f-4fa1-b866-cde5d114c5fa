import os
from typing import Dict, Optional
from uuid import UUID

import sentry_sdk
from facts_client.model.web_ingested_source import WebIngestedSource
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from sqlalchemy.orm import Session
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.source_types import SourceTypeID, WebIngestedSourceTypeID

from src.clients.ca_cslb import CACSLBClient
from src.clients.facts import FactsClient
from src.handlers.base_handler import BaseHandler, run
from src.models.ca_cslb import ContractorData, LicensePage
from src.models.license import LicenseFactory
from src.models.observations.insurance_information import (
    InsuranceInformationObservationFactory,
)
from src.utils.lambda_metric import lambda_metric
from src.utils.logic import fetch_and_load_documents_for_businesses

logger = get_logger().bind(handler="CA_CSLB")

sentry_sdk.init(
    os.environ.get("SENTRY_DSN"),
    integrations=[AwsLambdaIntegration(timeout_warning=True)],
    environment=os.environ.get("KALEPA_ENV", "dev"),
)

facts_client = FactsClient(os.environ["FACTS_API_URL"], proxy_url=os.environ.get("PROXY_URL"))


class CACSLBHandler(BaseHandler):
    LICENSE_NUMBER_PARAM_NAME = "LicNum="

    def __init__(
        self,
        event: Dict,
        db_session: Session,
        context,
        facts_client: FactsClient,
    ):
        super().__init__(event, db_session, context)
        self.facts_client = facts_client
        self.source = WebIngestedSource(
            source_type_id=SourceTypeID.WEB_INGESTED,
            web_ingestion_type=WebIngestedSourceTypeID.CA_CSLB,
            url=self._url,
        )

    def is_kalepa_id_required(self):
        return False

    def source_type(self) -> str:
        return "ca-cslb"

    def create_observations(self, business_id: UUID, license_page: LicensePage):
        if license_page.bond_information.bond_agent or license_page.workers_comp_information.insurance_company:
            try:
                insurance_information_observation = InsuranceInformationObservationFactory.create_from_ca_cslb(
                    business_id, license_page, self.source
                )
                self.facts_client.append_observation(insurance_information_observation)
                self._items_found += 1
            except Exception:
                logger.exception("Exception while creating observation for insurance")
        self._expected_items += 1

    def create_license_document(self, business_id: UUID, license_data: LicensePage) -> str:
        license_document = LicenseFactory().create_from_ca_cslb(business_id, license_data, self.source)
        __, created_license_ids = fetch_and_load_documents_for_businesses(
            DocumentTypeID.LICENSE, {self.business_id: [license_document]}, WebIngestedSourceTypeID.CA_CSLB
        )
        lambda_metric("scrape.ca_cslb.licenses.found", 1)
        return created_license_ids[0]

    def handle(self) -> Optional[Dict]:
        self._expected_items = 0
        self._items_found = 0
        if self.LICENSE_NUMBER_PARAM_NAME not in self._url:
            logger.info("No license number found in URL", url=self._url)
            return None
        license_number = self._url.split("LicNum=")[-1]

        license_page_html = CACSLBClient.get_license_page(license_number)
        if not license_page_html:
            logger.info("License page not found", license_number=license_number)
            return None
        contractor = ContractorData.from_license_number(license_number)
        license_data = CACSLBClient.extract_license_details(license_page_html, contractor)
        if not license_data:
            logger.info("License data not found", license_number=license_number)
            return None

        license_document_id = None
        if self.is_full_scraping_mode:
            self.create_observations(UUID(self._kalepa_id), license_data)
            license_document_id = self.create_license_document(UUID(self._kalepa_id), license_data)
        return {
            "license": license_document_id,
        }


@bind_lambda_logging_context
def scrape_ca_cslb(event, context=None):
    return run(CACSLBHandler, event, context=context, facts_client=facts_client)
