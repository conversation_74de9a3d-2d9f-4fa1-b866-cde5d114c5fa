import os
from concurrent.futures import as_completed
from concurrent.futures.thread import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from operator import and_
from typing import TYPE_CHECKING, Any, Dict, Iterable, List, Optional, Sequence

import sentry_sdk
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from sqlalchemy.orm import load_only
from static_common.constants import REAL_ESTATE_NAME
from static_common.models.on_demand import OnDemandRequest
from static_common.schemas.on_demand import OnDemandSearchRequestSchema

from src.clients.ebsa.ebsa_db_client import EBSADbClient
from src.clients.efast.efast_db_client import EFASTDbClient
from src.clients.serper import SerperSearchResult
from src.models.types import Source
from src.scrapers.ca_cslb import CACSLBScraper
from src.utils.address import get_state_from_address
from src.utils.logging_utils import log_function_result

if TYPE_CHECKING:
    from entity_resolution_service_client_v3 import Entity

    from src.models.on_demand import OnDemandHandlerResponse
    from src.models.search.business_profile_source import BusinessProfileSource

sentry_sdk.init(
    os.environ.get("SENTRY_DSN"),
    integrations=[AwsLambdaIntegration(timeout_warning=True)],
    environment=os.environ.get("KALEPA_ENV", "dev"),
)


logger = get_logger()

MAX_ERS_THREADS = 1
REAL_ESTATE = "Real Estate/Property"
MAX_ENTITIES_TO_INGEST_FOR_THE_SAME_PREMISE = 5

_search_limit = None


def _get_search_limit() -> int:
    global _search_limit
    if _search_limit is None:
        from src.clients.search_client import SearchClient

        _search_limit = SearchClient.SEARCH_LIMIT
    return _search_limit


def has_source_metadata(business_id: str, source_type: str) -> bool:
    from src.models.scraping_metadata import ScrapingMetadata
    from src.session import get_queue_pool_session

    """Returns true iff there is url of specified source type assigned to specified kalepa_id"""
    if not business_id:
        return False
    db_session = get_queue_pool_session()
    try:
        metadata = db_session.query(ScrapingMetadata).filter_by(kalepa_id=business_id, source_type=source_type).first()
        if not metadata or not metadata.url:
            return False
        return True
    finally:
        db_session.close()


def extract_business_details(event: dict) -> "OnDemandRequest":
    from static_common.schemas.on_demand import OnDemandSearchRequestSchema

    return OnDemandSearchRequestSchema().load(event)


@bind_lambda_logging_context
def search_facebook(event, context=None):
    from src.clients.facebook import FacebookClient
    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.facebook.business import FacebookBusinessScraper

    execution_id = context.aws_request_id if context else None
    request = extract_business_details(event)
    if has_source_metadata(request.kalepa_id, "facebook-business"):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }
    if not request.address or not request.name:
        return {
            "results": [],
            "execution_id": execution_id,
        }

    search_order = [
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
            WebsiteSearchField.PHONE_NUMBER,
        ),
    ]

    limit = 5 if not request.fast_track else 2

    def filter_urls(urls):
        return [FacebookClient.normalize_facebook_url(url) for url in urls]

    serp_based_client = WebsiteSearchClient(
        source=Source.FACEBOOK_BUSINESS,
        search_order=search_order,
        website="https://www.facebook.com/",
        scraper=FacebookBusinessScraper,
        search_limit=limit,
        upper_limit=limit,
        fast_track=request.fast_track,
        filter_urls=filter_urls,
    )
    return _dump_result(serp_based_client.search(request, execution_id))


def yelp_url_filter(urls: Iterable[str]) -> List[str]:
    YELP_URL_SEGMENT_BLACKLIST = [
        "/c/",
        "/brands/",
        "/topic/",
        "/city/",
        "/nearme/",
        "/questions/",
        "/best/",
        "/reviews/",
        "/article/",
    ]
    YELP_URL_ENDING_BLACKLIST = ["/search", "/brands", "/ir", "/factsheet", "/careers"]

    urls_result = []
    for url in urls:
        prepared_url = url.strip().lower()
        validated = (
            not any(prepared_url.endswith(banned_suffix) for banned_suffix in YELP_URL_ENDING_BLACKLIST)
            and not any(banned_segment in prepared_url for banned_segment in YELP_URL_SEGMENT_BLACKLIST)
            and "yelp.com" in prepared_url
        )

        if validated:
            urls_result.append(prepared_url)

    return urls_result


@bind_lambda_logging_context
@log_function_result
def search_yelp(event, context=None):
    execution_id = context.aws_request_id if context else None

    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.yelp.business import YelpBusinessScraper

    request = extract_business_details(event)
    if has_source_metadata(request.kalepa_id, "yelp-business"):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }

    if not request.address:
        return {
            "results": [],
            "execution_id": execution_id,
        }

    results = {}
    search_order = [
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
            WebsiteSearchField.PHONE_NUMBER,
        ),
    ]

    serp_based_client = WebsiteSearchClient(
        source=Source.YELP_BUSINESS,
        search_order=search_order,
        website="https://www.yelp.com/",
        scraper=YelpBusinessScraper,
        search_limit=10,
        upper_limit=5,
        fast_track=request.fast_track,
        filter_urls=yelp_url_filter,
    )

    for result in serp_based_client.search(request, execution_id).results:
        results[result.url] = {
            "name": result.name,
            "address": result.address,
            "phone": result.phone,
            "url": result.url.split("?")[0],
            "source_type": "yelp-business",
        }

    return {
        "results": list(results.values()),
        "execution_id": execution_id,
    }


@bind_lambda_logging_context
def search_epa(event, context=None):
    from src.models.types import Source
    from src.schemas.search.business_profile_source import BusinessProfileSourceSchema
    from src.scrapers.epa.business import EPABusinessScraper

    execution_id = context.aws_request_id if context else None
    request = extract_business_details(event)

    if has_source_metadata(request.kalepa_id, Source.EPA_BUSINESS.value):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }
    if not request.address or not request.name:
        return {
            "results": [],
            "execution_id": execution_id,
        }
    search_results = EPABusinessScraper().search_epa_business(request.name, request.address)
    return {
        "results": BusinessProfileSourceSchema(many=True).dump(search_results),
        "execution_id": execution_id,
    }


def _get_request_and_execution_id(event: Dict, context=None) -> tuple[OnDemandRequest, str, int]:
    execution_id = context.aws_request_id if context else None
    request: OnDemandRequest = OnDemandSearchRequestSchema().load(event)
    search_limit = _get_search_limit() if request.search_limit is None else request.search_limit
    return request, execution_id, search_limit


def _maybe_trim_search_limit(event: Dict, results: List["BusinessProfileSource"]) -> List["BusinessProfileSource"]:
    search_limit = event.get("Input", {}).get("search_limit")
    if search_limit:
        return results[:search_limit]
    return results


def _dump_result(result: "OnDemandHandlerResponse") -> dict[str, Any]:
    from src.schemas.on_demand import OnDemandHandlerResponseSchema

    return OnDemandHandlerResponseSchema().dump(result)


@bind_lambda_logging_context
@log_function_result
def search_trip_advisor(event, context=None):
    from typing import List, Sequence

    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.tripadvisor.business import TripAdvisorBusinessScraper

    request, execution_id, _ = _get_request_and_execution_id(event, context)
    if not request.address:
        return {
            "results": [],
            "execution_id": execution_id,
        }
    search_order = [
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
        ),
    ]

    def filter_urls(urls: Sequence[str]) -> List[str]:
        return [url for url in urls if "-or" not in url and "Hotel_Review" in url]

    client = WebsiteSearchClient(
        source=Source.TRIPADVISOR_BUSINESS,
        search_order=search_order,
        website="https://www.tripadvisor.com/",
        scraper=TripAdvisorBusinessScraper,
        search_limit=100,
        upper_limit=5,
        filter_urls=filter_urls,
        fast_track=request.fast_track,
    )
    return _dump_result(client.search(request, execution_id))


@bind_lambda_logging_context
@log_function_result
def search_apartments(event: dict[str, Any], context: Any | None = None) -> dict[str, Any]:
    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.apartments.apartments_scrapper import ApartmentsScraper

    request, execution_id, _ = _get_request_and_execution_id(event, context)
    if not request.address:
        return {
            "results": [],
            "execution_id": execution_id,
        }

    # This bypasse name checking by indicating tha we are only looking for an Real Estate
    if not request.name:
        request.name = REAL_ESTATE_NAME

    search_order: list[tuple[WebsiteSearchField, ...]] = [
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
        ),
    ]

    if request.name != REAL_ESTATE_NAME:
        # if name is real estate it means that we are searching only by address
        # if we have a real name, we should also try to search only by address
        search_order.append((WebsiteSearchField.ADDRESS,))

    client = WebsiteSearchClient(
        source=Source.APARTMENTS,
        search_order=search_order,
        website=ApartmentsScraper.BASE_URL,
        scraper=ApartmentsScraper,
        search_limit=100,
        upper_limit=5,
        filter_urls=ApartmentsScraper.filter_urls,
        fast_track=request.fast_track or False,
        should_skip_real_estate=False,
        accumulate_searches=True,
    )

    result = client.search(request, execution_id)

    return _dump_result(result)  # type: ignore


@bind_lambda_logging_context
@log_function_result
def search_irs_form_990(event: dict[str, Any], context: Any | None = None) -> dict[str, Any]:
    from src.clients.ers_v3 import ERSClientV3
    from src.logic import irs_form_990
    from src.models.on_demand import OnDemandHandlerResponse

    ers_client_v3 = ERSClientV3(os.environ["ENTITY_RESOLUTION_SERVICE_V3_URL"])

    request, execution_id, _ = _get_request_and_execution_id(event, context)
    if not request.address or not request.name or request.name == REAL_ESTATE:
        return {
            "results": [],
            "execution_id": execution_id,
        }

    results = irs_form_990.search_business_profile(request.name, request.address, ers_client_v3=ers_client_v3)

    return _dump_result(
        OnDemandHandlerResponse(
            results=results,
            execution_id=execution_id,
        )
    )


@bind_lambda_logging_context
@log_function_result
def search_buildzoom(event, context=None):
    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.buildzoom.business import BuildZoomBusinessScraper

    request, execution_id, search_limit = _get_request_and_execution_id(event, context)

    search_order: list[Sequence[WebsiteSearchField]] = [
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
            WebsiteSearchField.PHONE_NUMBER,
        ),
        (WebsiteSearchField.NAME,),  # This comma is important as we need the value as Tuple.
    ]

    def filter_urls(urls: Iterable[str]) -> list[str]:
        return [url for url in urls if not url.endswith(".xml")]

    client = WebsiteSearchClient(
        source=Source.BUILDZOOM_BUSINESS,
        search_order=search_order,
        website="https://www.buildzoom.com/",
        scraper=BuildZoomBusinessScraper,
        filter_urls=filter_urls,
        should_skip_real_estate=True,
        upper_limit=search_limit,
    )
    return _dump_result(client.search(request, execution_id))


@bind_lambda_logging_context
@log_function_result
def search_thomasnet(event, context=None):
    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.thomasnet.business import ThomasnetBusinessScraper

    request, execution_id, search_limit = _get_request_and_execution_id(event, context)

    search_order = [
        (WebsiteSearchField.NAME,),  # This comma is important as we need the value as Tuple.
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
            WebsiteSearchField.PHONE_NUMBER,
        ),
    ]

    def filter_urls(urls):
        return [url for url in urls if "profile" in url]

    client = WebsiteSearchClient(
        source=Source.THOMASNET_BUSINESS,
        search_order=search_order,
        website="https://www.thomasnet.com/",
        scraper=ThomasnetBusinessScraper,
        should_skip_real_estate=True,
        filter_urls=filter_urls,
        upper_limit=search_limit,
    )

    return _dump_result(client.search(request, execution_id))


@bind_lambda_logging_context
def search_iqs(event, context=None):
    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.iqs.business import IQSBusinessScraper

    request, execution_id, search_limit = _get_request_and_execution_id(event, context)

    search_order: list[Sequence[WebsiteSearchField]] = [(WebsiteSearchField.NAME, WebsiteSearchField.ADDRESS)]
    if request.phone_number and not request.fast_track:
        search_order.append(
            (
                WebsiteSearchField.NAME,
                WebsiteSearchField.ADDRESS,
                WebsiteSearchField.PHONE_NUMBER,
            )
        )
    client = WebsiteSearchClient(
        source=Source.IQS_BUSINESS,
        search_order=search_order,
        website="https://www.iqsdirectory.com/",
        scraper=IQSBusinessScraper,
        should_skip_real_estate=True,
        search_limit=search_limit,
    )
    return _dump_result(client.search(request, execution_id))


@bind_lambda_logging_context
def search_houzz(event, context=None):
    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.houzz.business import HouzzBusinessScraper

    request, execution_id, search_limit = _get_request_and_execution_id(event, context)

    search_order = [
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
        ),
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
            WebsiteSearchField.PHONE_NUMBER,
        ),
    ]

    def filter_urls(urls):
        return [url for url in urls if "professional" in url]

    def serper_search_result_predicate(serp_result: SerperSearchResult):
        if not serp_result.title:
            return True

        lower_title = serp_result.title.lower()
        if lower_title.startswith("best 15"):
            return False

        return True

    client = WebsiteSearchClient(
        source=Source.HOUZZ_BUSINESS,
        search_order=search_order,
        website="https://www.houzz.com/",
        scraper=HouzzBusinessScraper,
        should_skip_real_estate=True,
        filter_urls=filter_urls,
        serper_search_result_predicate=serper_search_result_predicate,
        upper_limit=search_limit,
    )
    return _dump_result(client.search(request, execution_id))


@bind_lambda_logging_context
def search_good_jobs_first(event, context=None):
    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.good_jobs_first.business import GoodJobsFirstScraper

    request, execution_id, search_limit = _get_request_and_execution_id(event, context)

    search_order = [
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
            WebsiteSearchField.PHONE_NUMBER,
        ),
        (WebsiteSearchField.NAME,),  # This comma is important as we need the value as Tuple.
    ]

    def filter_urls(urls):
        return [url for url in urls if "violation-tracker" in url]

    client = WebsiteSearchClient(
        source=Source.GOOD_JOBS_FIRST_BUSINESS,
        search_order=search_order,
        website="https://goodjobsfirst.org/",
        scraper=GoodJobsFirstScraper,
        should_skip_real_estate=True,
        filter_urls=filter_urls,
        upper_limit=search_limit,
    )
    return _dump_result(client.search(request, execution_id))


@bind_lambda_logging_context
@log_function_result
def search_google_local(event, context=None):
    from static_common.constants import REAL_ESTATE_NAME

    from src.models.types import Source
    from src.schemas.search.business_profile_source import BusinessProfileSourceSchema
    from src.scrapers.google.business import GoogleBusinessScraper

    execution_id = context.aws_request_id if context else None
    refresh_scale_serp = event.pop("refresh_scale_serp", False)
    request = extract_business_details(event)
    if has_source_metadata(request.kalepa_id, Source.GOOGLE_BUSINESS.value):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }
    if not request.address:
        return {
            "results": [],
            "execution_id": execution_id,
        }
    results = []
    if request.name and request.name.lower() != REAL_ESTATE_NAME.lower():
        results = GoogleBusinessScraper().search_all(
            request.name,
            request.address,
            request.phone_number,
            search_serper=True,
            allow_cache=not refresh_scale_serp,
        )
    if not results and request.address:
        results = GoogleBusinessScraper().search_only_address(request.address)
    return {
        "results": BusinessProfileSourceSchema(many=True).dump(results),
        "execution_id": execution_id,
    }


@bind_lambda_logging_context
def search_better_business_bureau(event, context=None):
    import re
    from typing import List, Sequence

    from src.clients.search_client import WebsiteSearchClient, WebsiteSearchField
    from src.models.types import Source
    from src.scrapers.better_business_bureau.business import BetterBusinessBureauScraper

    request, execution_id, search_limit = _get_request_and_execution_id(event, context)

    search_order = [
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
            WebsiteSearchField.PHONE_NUMBER,
        ),
    ]

    def filter_urls(urls: Sequence[str]) -> List[str]:
        regex = r"(.*?\/profile\/.*?\/.*?)(\/|$)"
        result = []
        for url in urls:
            if not url.endswith(".pdf/") and (match := re.match(regex, url)):
                result.append(match.groups()[0])
        return result

    client = WebsiteSearchClient(
        source=Source.BETTER_BUSINESS_BUREAU,
        search_order=search_order,
        website="https://www.bbb.org/",
        scraper=BetterBusinessBureauScraper,
        filter_urls=filter_urls,
        cleanup_url_params=False,
        upper_limit=search_limit,
    )
    return _dump_result(client.search(request, execution_id))


@bind_lambda_logging_context
def search_osha(event, context=None):
    from static_common.constants import REAL_ESTATE_NAME

    from src.clients.osha.osha_client_provider import OshaClientProvider
    from src.schemas.search.business_profile_source import BusinessProfileSourceSchema

    execution_id = context.aws_request_id if context else None
    request = OnDemandSearchRequestSchema().load(event)
    if not request.name:
        return {
            "results": [],
            "execution_id": execution_id,
        }
    if request.name and request.name.lower() == REAL_ESTATE_NAME.lower():
        results = []
    else:
        with OshaClientProvider.get_client(page_size=100, batch_mode=False) as osha_client:
            osha_profile_source = osha_client.get_establishment(request.name, request.address)
            results = BusinessProfileSourceSchema(many=True).dump(osha_profile_source)
    return {
        "results": _maybe_trim_search_limit(event, results),
        "execution_id": execution_id,
    }


@bind_lambda_logging_context
def search_efast(event, context=None):
    from static_common.constants import REAL_ESTATE_NAME

    from src.clients.ers_v3 import ERSClientV3
    from src.schemas.search.business_profile_source import BusinessProfileSourceSchema

    execution_id = context.aws_request_id if context else None
    request = OnDemandSearchRequestSchema().load(event)
    if not request.name:
        return {
            "results": [],
            "execution_id": execution_id,
        }
    if request.name and request.name.lower() == REAL_ESTATE_NAME.lower():
        results = []
    else:
        ebsa_db_client = EBSADbClient()
        ers_client = ERSClientV3(os.environ["ENTITY_RESOLUTION_SERVICE_V3_URL"])
        efast_client = EFASTDbClient(ers_client, ebsa_db_client)
        profiles = efast_client.search(request.name, request.address, request.kalepa_id)
        results = BusinessProfileSourceSchema(many=True).dump(profiles)
    return {
        "results": _maybe_trim_search_limit(event, results),
        "execution_id": execution_id,
    }


@bind_lambda_logging_context
def search_fda(event, context=None):
    from src.clients.fda_search_client import FDASearchClient
    from src.models.types import Source
    from src.schemas.search.business_profile_source import BusinessProfileSourceSchema

    execution_id = context.aws_request_id if context else None
    request = extract_business_details(event)

    if has_source_metadata(request.kalepa_id, Source.FDA_BUSINESS.value):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }

    # TODO(ENG-28321): Check if we can enable FDA
    return {
        "results": [],
        "execution_id": execution_id,
    }

    search_results = FDASearchClient().search(request)
    results = BusinessProfileSourceSchema(many=True).dump(search_results)
    return {
        "results": _maybe_trim_search_limit(event, results),
        "execution_id": execution_id,
    }


@bind_lambda_logging_context
@log_function_result
def search_fmcsa(event, context=None):
    from src.clients.fmcsa import (
        FmcsaSearchClient,
        FmcsaSearchMergedClient,
        FmcsaSmsSearchClient,
        filter_urls,
    )
    from src.clients.search_client import (
        SearchClient,
        WebsiteSearchClient,
        WebsiteSearchField,
    )
    from src.models.types import Source
    from src.scrapers.fmcsa.business import FMCSABusinessScraper

    request, execution_id, search_limit = _get_request_and_execution_id(event, context)

    search_order = [
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
            WebsiteSearchField.PHONE_NUMBER,
        ),
    ]

    limit = SearchClient.get_search_limit(request)
    google_client = WebsiteSearchClient(
        source=Source.FMCSA_BUSINESS,
        search_order=search_order,
        website="https://fmcsa.dot.gov/",
        scraper=FMCSABusinessScraper,
        filter_urls=filter_urls,
        cleanup_url_params=False,
        enhance_results=FmcsaSearchClient.process_result,
        upper_limit=limit,
        fast_track=request.fast_track,
    )

    fmcsa_client = FmcsaSmsSearchClient(limit=limit, fast_track=request.fast_track)
    merged_client = FmcsaSearchMergedClient(google_client, fmcsa_client)
    return _dump_result(merged_client.search(request, execution_id))


@bind_lambda_logging_context
@log_function_result
def search_dot_report(event, context=None):
    from static_common.models.on_demand import OnDemandRequest

    from src.clients.dot_report import DotReportSearchClient
    from src.clients.search_client import SearchClient
    from src.models.types import Source
    from src.schemas.search.business_profile_source import BusinessProfileSourceSchema

    execution_id = context.aws_request_id if context else None
    request: OnDemandRequest = extract_business_details(event)

    if has_source_metadata(request.kalepa_id, Source.DOT_REPORT_BUSINESS.value):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }

    limit = SearchClient.get_search_limit(request)
    results = DotReportSearchClient().search(request, limit, fast_track=request.fast_track)
    # TODO(ENG-10023): Remove once ENG-10023 is implemented and added to the "ingestOnIdentifierCreated" SF
    fmcsa_results = [fmcsa_business for b in results if (fmcsa_business := _convert_to_fmcsa_result(b))]
    results += fmcsa_results
    results = _maybe_trim_search_limit(event, BusinessProfileSourceSchema(many=True).dump(results))
    return {
        "results": results,
        "execution_id": execution_id,
    }


@bind_lambda_logging_context
@log_function_result
def search_safer_fmcsa_report(event, context=None):
    from src.clients.safer_fmcsa import SaferFMCSAGoogleSearchClient
    from src.clients.search_client import (
        SearchClient,
        WebsiteSearchClient,
        WebsiteSearchField,
    )
    from src.models.types import Source
    from src.scrapers.safer_fmcsa.business import SaferFMCSABusinessScraper

    request, execution_id, search_limit = _get_request_and_execution_id(event, context)

    search_order = [
        (WebsiteSearchField.NAME,),
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.ADDRESS,
        ),
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.STATE,
        ),
        (
            WebsiteSearchField.NAME,
            WebsiteSearchField.PHONE_NUMBER,
        ),
    ]
    if request.fast_track:
        search_order = search_order[:2]

    def filter_urls(urls):
        return [url for url in urls if SaferFMCSAGoogleSearchClient.is_valid_link(url)]

    google_client = WebsiteSearchClient(
        source=Source.SAFER_FMCSA_BUSINESS,
        search_order=search_order,
        website="https://fmcsa.dot.gov/",
        scraper=SaferFMCSABusinessScraper,
        filter_urls=filter_urls,
        accumulate_searches=True,
        cleanup_url_params=False,
        enhance_results=SaferFMCSAGoogleSearchClient.process_result,
        upper_limit=SearchClient.get_search_limit(request),
        fast_track=request.fast_track,
    )

    return _dump_result(google_client.search(request, execution_id))


@bind_lambda_logging_context
@log_function_result
def search_unicourt(event, context=None):
    from static_common.models.on_demand import OnDemandRequest

    from src.clients.ers_v3 import ERSClientV3
    from src.clients.unicourt import (
        UnicourtGoogleSearchClient,
        UnicourtSearchClient,
        UnicourtSearchMergeClient,
    )
    from src.models.types import Source
    from src.schemas.search.business_profile_source import BusinessProfileSourceSchema
    from src.scrapers.unicourt.case_details import CaseDetailsScraper
    from src.scrapers.unicourt.courthouse import CourthouseScraper

    execution_id = context.aws_request_id if context else None
    request: OnDemandRequest = extract_business_details(event)
    # check if real estate
    if REAL_ESTATE == request.name:
        return {
            "results": [],
            "execution_id": execution_id,
        }
    # noinspection PyTypeChecker
    if has_source_metadata(request.kalepa_id, Source.UNICOURT.value):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }

    case_details_scraper = CaseDetailsScraper()
    court_scraper = CourthouseScraper()
    unicourt_client = UnicourtSearchClient(case_details_scraper, court_scraper)
    unicourt_google_client = UnicourtGoogleSearchClient(
        case_details_scraper=case_details_scraper,
        court_scraper=court_scraper,
    )
    unicourt_merge_client = UnicourtSearchMergeClient(
        unicourt_client, unicourt_google_client, ERSClientV3(os.environ["ENTITY_RESOLUTION_SERVICE_V3_URL"])
    )
    profiles = unicourt_merge_client.search(request)
    results = _maybe_trim_search_limit(event, BusinessProfileSourceSchema(many=True).dump(profiles))
    return {
        "results": results,
        "execution_id": execution_id,
    }


@bind_lambda_logging_context
def search_sam(event, context=None):
    from static_common.models.on_demand import OnDemandRequest

    from src.clients.sam_search_client import SAMSearchClient
    from src.models.types import Source
    from src.utils.lambda_metric import lambda_metric

    execution_id = context.aws_request_id if context else None
    request: OnDemandRequest = extract_business_details(event)
    if has_source_metadata(request.kalepa_id, Source.SAM.value):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }
    if kalepa_id := SAMSearchClient().search(request):
        lambda_metric("on_demand.search.sam.found.db", 1)
        return {
            "kalepa_id": kalepa_id,
            "execution_id": execution_id,
        }
    return {
        "results": [],
        "execution_id": execution_id,
    }


@bind_lambda_logging_context
@log_function_result
def search_opencorporates(event, context=None):
    from static_common.constants import REAL_ESTATE_NAME
    from static_common.models.on_demand import OnDemandRequest

    from src.clients.opencorporates import OpencorporatesClient
    from src.models.types import Source
    from src.schemas.search.business_profile_source import BusinessProfileSourceSchema

    execution_id = context.aws_request_id if context else None
    request: OnDemandRequest = extract_business_details(event)

    if request.name and request.name.lower() == REAL_ESTATE_NAME.lower():
        return {"results": [], "execution_id": execution_id}

    if has_source_metadata(request.kalepa_id, Source.OPENCORPORATES.value):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }
    businesses = OpencorporatesClient().search(request)
    results = BusinessProfileSourceSchema(many=True).dump([b.to_business_profile_source() for b in businesses])
    results = _maybe_trim_search_limit(event, results)
    return {
        "results": results,
        "execution_id": execution_id,
    }


@bind_lambda_logging_context
@log_function_result
def search_ca_cslb(event, context=None):
    from static_common.constants import REAL_ESTATE_NAME
    from static_common.models.on_demand import OnDemandRequest

    from src.clients.ers_v3 import ERSClientV3
    from src.models.types import Source
    from src.schemas.search.business_profile_source import BusinessProfileSourceSchema

    execution_id = context.aws_request_id if context else None
    request: OnDemandRequest = extract_business_details(event)

    if request.name and request.name.lower() == REAL_ESTATE_NAME.lower():
        return {"results": [], "execution_id": execution_id}

    if not request.address or get_state_from_address(request.address) != "CA":
        logger.info("Address is not in CA, skipping CA CSLB search", address=request.address)
        return {"results": [], "execution_id": execution_id}

    if has_source_metadata(request.kalepa_id, Source.CA_CSLB.value):
        return {
            "kalepa_id": request.kalepa_id,
            "execution_id": execution_id,
        }

    ers_client_v3 = ERSClientV3(os.environ["ENTITY_RESOLUTION_SERVICE_V3_URL"])
    license_pages = CACSLBScraper(ers_client=ers_client_v3).search(business_name=request.name) or []
    results = BusinessProfileSourceSchema(many=True).dump([lp.to_business_profile_source() for lp in license_pages])
    results = _maybe_trim_search_limit(event, results)
    return {
        "results": results,
        "execution_id": execution_id,
    }


# TODO(ENG-10023): Remove once ENG-10023 is implemented and added to the "ingestOnIdentifierCreated" SF
def _convert_to_fmcsa_result(business_profile: "BusinessProfileSource") -> Optional["BusinessProfileSource"]:
    from src.models.search.business_profile_source import BusinessProfileSource
    from src.models.types import Source

    if business_profile.source_type == Source.DOT_REPORT_BUSINESS:
        url_converter = _convert_dot_report_url_to_fmcsa_url
    else:
        # source type not intended to be converted into FMCSA Business
        return None

    if not (url := url_converter(business_profile.url)):
        # could not convert url to FMCSA format
        return None

    return BusinessProfileSource(
        source_type=Source.FMCSA_BUSINESS.value,
        url=url,
        name=business_profile.name,
        address=business_profile.address,
        phone=business_profile.phone,
        website=business_profile.website,
        email=business_profile.email,
        extra_parameters=business_profile.extra_parameters,
    )


# TODO(ENG-10023): Remove once ENG-10023 is implemented and added to the "ingestOnIdentifierCreated" SF
def _convert_dot_report_url_to_fmcsa_url(url: str) -> str:
    """
    For given DOT.report url extract US dot and build FMCSA url from it.

    Example:
     -> DOT.report url: https://dot.report/usdot/1028478
     -> US dot:         1028478
     -> FMCSA url:      https://ai.fmcsa.dot.gov/SMS/Carrier/1028478/CompleteProfile.aspx
    """
    us_dot = url.split("/")[-1]
    return f"https://ai.fmcsa.dot.gov/SMS/Carrier/{us_dot}/CompleteProfile.aspx"


def _is_empty_result(result: Dict) -> bool:
    non_empty_values = [value for key, value in result.items() if value and key not in ("url", "source_type")]
    if non_empty_values:
        return False
    return True


@bind_lambda_logging_context
@log_function_result
def identify_businesses(event, context=None) -> dict[str, Any]:
    from src.models.scraping_metadata import ScrapingMetadata
    from src.session import get_queue_pool_session
    from src.util import maybe_create_source_metadata

    execution_id = context.aws_request_id if context else None
    search_result_kalepa_ids: set[str] = set()
    db_session = get_queue_pool_session()
    process_only_new_urls = event.get("Input", {}).get("process_only_new_urls", None)

    try:
        search_results = event["search_results"]
        flat_results = []
        for search_result in search_results:
            if not search_result:
                continue
            if "kalepa_id" in search_result:
                search_result_kalepa_ids.add(search_result["kalepa_id"])
            else:
                results = []
                for result in search_result["results"]:
                    if _is_empty_result(result):
                        logger.error(
                            "Result is empty",
                            source_type=result.get("source_type"),
                            execution_id=search_result["execution_id"],
                        )
                    else:
                        results.append(result)
                flat_results.extend(results)
        if process_only_new_urls:
            fetched_urls = {r["url"] for r in flat_results if r["url"]}
            urls_in_db = {
                u.url
                for u in db_session.query(ScrapingMetadata)
                .options(load_only(ScrapingMetadata.url))
                .filter(and_(ScrapingMetadata.url.in_(fetched_urls), ScrapingMetadata.kalepa_id.isnot(None)))
                .all()
            }
            flat_results = [fr for fr in flat_results if fr["url"] and fr["url"] not in urls_in_db]

        provided_entity = _get_entity_by_id(event["Input"]["kalepa_id"])
        provided_premises_ids: set[str] = set()
        if provided_entity:
            provided_premises_ids = (
                {entity_premise.premises_id for entity_premise in provided_entity.premises}
                if provided_entity.premises
                else set()
            )

        # Search in ERS for each result
        entities_with_overlapping_premises: list[Entity] = []
        with ThreadPoolExecutor(max_workers=MAX_ERS_THREADS) as executor:
            future_to_search_result = {
                executor.submit(_identify_business, _input=result): result for result in flat_results
            }
            for future in as_completed(future_to_search_result):
                try:
                    entity = future.result()
                    search_result = future_to_search_result[future]
                    if not entity:
                        continue

                    entity_premise_ids = (
                        {entity_premise.premises_id for entity_premise in entity.premises} if entity.premises else set()
                    )
                    if (
                        provided_premises_ids.intersection(entity_premise_ids)
                        and search_result.get("source_type") == Source.APARTMENTS.value
                    ):
                        # Save entities which have at least one overlapping premise and was found from apartments
                        entities_with_overlapping_premises.append(entity)

                    search_result_kalepa_ids.add(entity.id)
                    maybe_create_source_metadata(
                        db_session, entity.id, search_result["source_type"], search_result["url"]
                    )
                except Exception as e:
                    sentry_sdk.capture_exception(e)
                    db_session.rollback()
                    logger.warning("Could not identify search_result", exc_info=e, search_result=search_result)

        provided_kalepa_id: str | None = event["Input"]["kalepa_id"]
        if provided_kalepa_id:
            kalepa_ids_to_return: set[str] = set()
            if provided_kalepa_id in search_result_kalepa_ids:
                # We found the required entity in the search results, we can add it to the return list
                kalepa_ids_to_return.add(provided_kalepa_id)

            # We run the ingestion for entities with overlapping premises
            entity_ids_with_overlapping_premises: list[str] = list(
                {entity.id for entity in entities_with_overlapping_premises}
            )
            if entities_with_overlapping_premises:
                logger.info(
                    "Found entities with overlapping premises",
                    entities=entities_with_overlapping_premises,
                    original_entity=provided_kalepa_id,
                    entities_count=len(entities_with_overlapping_premises),
                    ids_count=len(entity_ids_with_overlapping_premises),
                )

                if len(entity_ids_with_overlapping_premises) > MAX_ENTITIES_TO_INGEST_FOR_THE_SAME_PREMISE:
                    logger.warning(
                        "Found too many entities with overlapping premises - truncating the list",
                        found=len(entity_ids_with_overlapping_premises),
                        max_allowed=MAX_ENTITIES_TO_INGEST_FOR_THE_SAME_PREMISE,
                    )
                    entity_ids_with_overlapping_premises = entity_ids_with_overlapping_premises[
                        :MAX_ENTITIES_TO_INGEST_FOR_THE_SAME_PREMISE
                    ]

                kalepa_ids_to_return.update(entity_ids_with_overlapping_premises)

            return {
                "kalepa_ids": list(kalepa_ids_to_return),
                "execution_id": execution_id,
            }

        return {"kalepa_ids": [], "execution_id": execution_id}

    finally:
        db_session.close()


def _identify_business(_input: Dict) -> "Entity | None":
    from src.clients.ers_v3 import ERSClientV3
    from src.models.search.utils import profile_dict_to_entity_request

    entity_request = profile_dict_to_entity_request(_input)
    if not entity_request:
        return None
    ers_client = ERSClientV3(base_url=os.environ["ENTITY_RESOLUTION_SERVICE_V3_URL"])

    return ers_client.get_or_create_entity(entity_request=entity_request)


def _get_entity_by_id(kalepa_id: str | None) -> "Entity | None":
    from src.clients.ers_v3 import ERSClientV3

    ers_client = ERSClientV3(base_url=os.environ["ENTITY_RESOLUTION_SERVICE_V3_URL"])
    if not kalepa_id:
        return None

    return ers_client.get_entity(entity_id=kalepa_id)
