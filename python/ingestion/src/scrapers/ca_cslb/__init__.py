from src.clients.ca_cslb import CACSLBClient
from src.clients.ers_v3 import ERSClientV3
from entity_resolution_service_client_v3 import NameScoreResponse
from collections import defaultdict

from src.models.ca_cslb import LicensePage


class CACSLBScraper:
    ERS_NAME_SCORING_THRESHOLD = 0.9
    MAX_RESULTS = 10

    def __init__(
        self,
        ers_client: ERSClientV3,
    ):
        self.ers_client = ers_client

    def search(self, business_name: str) -> list[LicensePage] | None:
        search_html = CACSLBClient.search_ca_license_board(business_name)
        if not search_html:
            return None
        search_results = CACSLBClient.extract_contractor_data(search_html)
        if not search_results:
            return None
        contractors_map = defaultdict(list)
        for contractor in search_results:
            contractors_map[contractor.contractor_name].append(contractor)
        name_scoring_result = self.ers_client.score_names(business_name, list(contractors_map.keys()))
        matching_names: list[NameScoreResponse] = [
            result for result in name_scoring_result if result.score > self.ERS_NAME_SCORING_THRESHOLD
        ]
        top_name_responses: list[NameScoreResponse] = sorted(matching_names, key=lambda x: x.score, reverse=True)[
            : self.MAX_RESULTS
        ]
        top_names: set[str] = {it.name.value for it in top_name_responses if it.name and it.name.value}

        results = []
        for contractor_name in top_names:
            contractors = contractors_map[contractor_name]
            for contractor in contractors:
                license_page_html = CACSLBClient.get_license_page(contractor.license_number)
                if not license_page_html:
                    continue
                license_data = CACSLBClient.extract_license_details(license_page_html, contractor)
                results.append(license_data)
        return results
