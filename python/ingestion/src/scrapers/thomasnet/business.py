import json
from collections import defaultdict
from typing import Dict, List, Optional, Set

from infrastructure_common.logging import get_logger
from parsel import Selector

from src.clients.proxy import proxy_request
from src.models.thomasnet import (
    ThomasnetAdditionalCorpInfo,
    ThomasnetAdditionalCorpInfoDetail,
    ThomasnetBusiness,
    ThomasnetBusinessDescription,
    ThomasnetBusinessNews,
    ThomasnetBussinessProducts,
    ThomasnetProductGroup,
)
from src.scrapers.thomasnet.utils import (
    decorate_with_next_data_error_catching,
    prepare_base_thomasnet_headers,
)

logger = get_logger()

DEFAULT_PROXY = []


class ThomasnetBusinessScraper:
    NEWS_TYPE_TO_DESCRIPTION_MAP = {"FULL_STORY": "New Product & Services", "COMPANY_STORY": "Products in the News"}
    ADDITIONAL_INFO_SECTION_TO_DESCRIPTION_MAP = {
        "COMPANY": "Company Information",
        "PRODUCT": "Product Information",
        "CAPABILITIES_SERVICES": "Capabilities / Services",
    }
    LOCATIONS_GRAPHQL_QUERY = ""

    @staticmethod
    def _parse_next_data(selector: Selector, log_url: str) -> Optional[Dict]:
        log = logger.bind(url=log_url)
        next_data_content = selector.xpath("//script[@id='__NEXT_DATA__']/text()").get()
        if not next_data_content:
            log.warning("Website doesn't have __NEXT_DATA__ prop", url=log_url)
            return None

        try:
            return json.loads(next_data_content)
        except Exception as e:
            log.warning("Unable to parse __NEXT_DATA__ prop from website", exc_info=e)
            return None

    @staticmethod
    def _get_key_with_space(data: dict, key: str, append_space: bool = True) -> str:
        if value := data.get(key, None):
            if append_space:
                return " " + value
            else:
                return value

    @staticmethod
    @decorate_with_next_data_error_catching("address")
    def _parse_address(next_data: Dict) -> Optional[str]:
        try:
            address: Dict = next_data["props"]["pageProps"]["data"]["address"]
            address1 = ThomasnetBusinessScraper._get_key_with_space(address, "address1", False) or ""
            address2 = ThomasnetBusinessScraper._get_key_with_space(address, "address2") or ""
            address3 = ThomasnetBusinessScraper._get_key_with_space(address, "address3") or ""
            address_segment = f"{address1}{address2}{address3}".strip()

            city = address.get("city", None)
            state = address.get("state", None)
            zip_code = address.get("zip", None)

            if not city or not (state and zip_code) or not address_segment:
                return None

            city = city or ""
            state = state or ""
            zip_code = zip_code or ""

            address_str = address_segment
            if city:
                address_str = address_str + f" {city.strip()}"

            if state:
                address_str = address_str + ", "

            if zip_code and state:
                address_str = address_str + f"{state} {zip_code}"
            elif state:
                address_str = address_str + state

            if address_str:
                return address_str
        except Exception:
            return None

    @staticmethod
    @decorate_with_next_data_error_catching("descriptions")
    def _parse_descriptions(next_data: Dict, name: str) -> List[ThomasnetBusinessDescription]:
        descriptions = []
        exceptions = []

        try:
            thomasnet_description = next_data["props"]["pageProps"]["data"]["description"]
            if thomasnet_description:
                descriptions.append(
                    ThomasnetBusinessDescription(header="Company Description by Thomasnet", text=thomasnet_description)
                )
        except Exception as e:
            exceptions.append(e)

        try:
            company_description = next_data["props"]["pageProps"]["data"]["descriptionByCompany"]
            if company_description:
                descriptions.append(
                    ThomasnetBusinessDescription(header=f"Company Description by {name}", text=company_description)
                )
        except Exception as e:
            exceptions.append(e)

        if exceptions:
            logger.warning("Error occurred when scraping descriptions", exceptions=exceptions)

        return descriptions

    @staticmethod
    @decorate_with_next_data_error_catching("logo_url")
    def _parse_logo_url(next_data: Dict) -> Optional[str]:
        return next_data["props"]["pageProps"]["data"]["logoUrl"]

    @staticmethod
    @decorate_with_next_data_error_catching("website")
    def _parse_website(next_data: Dict) -> Optional[str]:
        return next_data["props"]["pageProps"]["data"]["website"]

    @staticmethod
    @decorate_with_next_data_error_catching("thomson_verified")
    def _parse_thomson_verified(next_data: Dict) -> Optional[bool]:
        tier = next_data["props"]["pageProps"]["data"]["tier"]
        return tier.strip() == "VERIFIED"

    @staticmethod
    @decorate_with_next_data_error_catching("phone_number")
    def _parse_phone_number(next_data: Dict) -> Optional[bool]:
        return next_data["props"]["pageProps"]["data"]["primaryPhone"]

    @staticmethod
    @decorate_with_next_data_error_catching("img_description_url")
    def _parse_img_description_url(next_data: Dict) -> Optional[str]:
        company_ad = next_data["props"]["pageProps"]["data"]["companyAd"]
        if not company_ad:
            return None

        return company_ad["adimg"]

    @staticmethod
    @decorate_with_next_data_error_catching("primary_type")
    def _parse_primary_type(next_data: Dict) -> Optional[str]:
        first_column_sections = next_data["props"]["pageProps"]["businesDetailsSections"]["firstColumnSections"]
        for data in first_column_sections:
            if data["label"] == "Primary Company Type":
                return data["items"][0]["text"]

    @staticmethod
    @decorate_with_next_data_error_catching("additional_activities")
    def _parse_additional_activities(next_data: Dict) -> Optional[List[str]]:
        first_column_sections = next_data["props"]["pageProps"]["businesDetailsSections"]["firstColumnSections"]

        additional_activities_str: Optional[str] = None
        for data in first_column_sections:
            if data["label"] == "Additional Activities":
                additional_activities_str = data["items"][0]["text"]
                break

        if additional_activities_str is None:
            raise RuntimeError("Cannot find Additional Activities label")

        if not additional_activities_str:
            return []

        return [activity.strip() for activity in additional_activities_str.split(",")]

    @staticmethod
    @decorate_with_next_data_error_catching("certifications_desc")
    def _parse_certifications_desc(next_data: Dict) -> Optional[List[str]]:
        certification_totals = next_data["props"]["pageProps"]["data"]["certificationTotals"]
        if not certification_totals:
            return []

        result = []
        for certification_total in certification_totals:
            cert_type: str = certification_total["type"].lower().capitalize()
            cert_count: int = certification_total["count"]

            result.append(f"{cert_count} {cert_type} certifications available")

        return result

    @staticmethod
    @decorate_with_next_data_error_catching("annual_sales")
    def _parse_annual_sales(next_data: Dict) -> Optional[str]:
        annual_sales = next_data["props"]["pageProps"]["data"]["annualSales"]
        if annual_sales is None:
            return None

        annual_sales = annual_sales.strip()

        if not annual_sales:
            return None

        return annual_sales

    @staticmethod
    @decorate_with_next_data_error_catching("employee_number_range")
    def _parse_employee_number_range(next_data: Dict) -> Optional[str]:
        number_employees = next_data["props"]["pageProps"]["data"]["numberEmployees"]
        if number_employees is None:
            return "Not Available"

        return number_employees

    @staticmethod
    @decorate_with_next_data_error_catching("year_founded")
    def _parse_year_founded(next_data: Dict) -> Optional[int]:
        year_founded = next_data["props"]["pageProps"]["data"]["yearFounded"]
        if year_founded is None:
            return None

        return int(year_founded)

    @staticmethod
    @decorate_with_next_data_error_catching("certifications_url")
    def _parse_certifications_url(next_data: Dict) -> Optional[str]:
        # Currently certifications can only be fetched after login, their scraping is disabled.
        company_id = next_data["query"]["companyId"]
        return f"https://www.thomasnet.com/company/{company_id}/certifications"

    @staticmethod
    @decorate_with_next_data_error_catching("product_catalog_url")
    def _parse_product_catalog_url(next_data: Dict, selector: Selector) -> Optional[str]:
        products_elements = selector.xpath("//button[contains(text(), 'Full Catalog')]")
        if not products_elements:
            return None

        company_id = next_data["query"]["companyId"]
        return f"https://www.thomasnet.com/company/{company_id}/catalog"

    @staticmethod
    @decorate_with_next_data_error_catching("key_personnel")
    def _parse_key_personnel(next_data: Dict) -> Optional[List[str]]:
        personnel = next_data["props"]["pageProps"]["data"]["personnel"]
        if personnel is None:
            return []

        result = []
        for person in personnel:
            name = " ".join(person["name"].strip().split())
            title = " ".join(person["title"].strip().split())
            result.append(f"{name}, {title}")

        return result

    @staticmethod
    @decorate_with_next_data_error_catching("news")
    def _parse_news(next_data: Dict) -> Optional[List[ThomasnetBusinessNews]]:
        company_id = next_data["query"]["companyId"]
        news_url = f"https://www.thomasnet.com/company/{company_id}/news"

        next_data_alternative = None
        try:
            response = proxy_request(
                news_url, proxies=DEFAULT_PROXY, headers=prepare_base_thomasnet_headers(), use_cloudscraper=True
            )
            selector = Selector(response.text)
            next_data_alternative = ThomasnetBusinessScraper._parse_next_data(selector, log_url=news_url)
        except Exception as e:
            logger.warning("Can't get news page for url, falling back to main page", news_url=news_url, exc_info=e)

        if next_data_alternative:
            next_data = next_data_alternative

        news_list = next_data["props"]["pageProps"]["data"]["news"]
        if news_list is None:
            return []

        result = []
        for news in news_list:
            headline = news["headline"]
            summary = news["summary"]
            news_type = news["type"]
            news_id = news["id"]
            news_url = f"https://news.thomasnet.com/fullstory/{news_id}"

            category = ThomasnetBusinessScraper.NEWS_TYPE_TO_DESCRIPTION_MAP.get(news_type, None)
            if not category:
                logger.warning("Can't decode news category", news_url=news_url, news_type=news_type)

            result.append(ThomasnetBusinessNews(headline=headline, url=news_url, summary=summary, category=category))

        return result

    @staticmethod
    @decorate_with_next_data_error_catching("additional_corp_info")
    def _parse_additional_corp_info(next_data: Dict) -> Optional[List[ThomasnetAdditionalCorpInfo]]:
        additional_information = next_data["props"]["pageProps"]["data"]["additionalInformation"]
        if additional_information is None:
            return []

        section_to_info_map: Dict[str, List[ThomasnetAdditionalCorpInfoDetail]] = defaultdict(list)
        for additional_info in additional_information:
            title = additional_info["title"]
            description = additional_info["description"]
            url = additional_info["url"]
            info_type = additional_info["type"]
            section = additional_info["section"]

            info_detail = ThomasnetAdditionalCorpInfoDetail(
                headline=title,
                summary=description,
                img_url=url if info_type == "IMAGE" else None,
                pdf_url=url if info_type == "DOCUMENT" else None,
                clip_url=url if info_type == "VIDEO" else None,
            )
            section_to_info_map[section].append(info_detail)

        result = []
        for section, detail_list in section_to_info_map.items():
            section_description = ThomasnetBusinessScraper.ADDITIONAL_INFO_SECTION_TO_DESCRIPTION_MAP.get(section, None)

            if not section_description:
                logger.warning("Can't decode additional corp info section", section=section)

            result.append(ThomasnetAdditionalCorpInfo(info_type=section_description, info_list=detail_list))

        return result

    @staticmethod
    @decorate_with_next_data_error_catching("products->summary_products")
    def _parse_summary_products(next_data: Dict) -> Optional[List[str]]:
        summary_set: Set[str] = set()
        families = next_data["props"]["pageProps"]["families"]
        if families is None:
            return None

        for family in families:
            family_name = family["name"]
            summary_set.add(family_name)

        return list(summary_set)

    @staticmethod
    @decorate_with_next_data_error_catching("products->detail_products")
    def _parse_detail_products(next_data: Dict) -> Optional[List[ThomasnetProductGroup]]:
        families = next_data["props"]["pageProps"]["families"]
        if families is None:
            return None

        groups_map = defaultdict(list)
        for family in families:
            family_name = family["name"]
            if family["headings"]:
                heading_names = [(x["name"] if "name" in x else x["headingName"]) for x in family["headings"]]
                groups_map[family_name].extend(heading_names)

        result = []
        for family_name, product_list in groups_map.items():
            result.append(ThomasnetProductGroup(group_name=family_name, product_list=product_list))

        return result

    @staticmethod
    @decorate_with_next_data_error_catching("thomasnet_cid")
    def _parse_other_locations_tgrams_id(next_data: Dict, selector: Selector) -> Optional[str]:
        locations_elements = selector.xpath("//button[contains(text(), 'Locations')]")
        if not locations_elements:
            return None

        tgrams_id = next_data["props"]["pageProps"]["data"]["tgramsId"]
        if not tgrams_id:
            raise RuntimeError("Can't read tgrams_id from __NEXT_DATA__")

        return tgrams_id

    @staticmethod
    @decorate_with_next_data_error_catching("products")
    def _parse_products(next_data: Dict) -> Optional[ThomasnetBussinessProducts]:
        company_id = next_data["query"]["companyId"]
        if not company_id:
            raise RuntimeError("Can't read company_id from __NEXT_DATA__")

        products_page_url = f"https://www.thomasnet.com/company/{company_id}/products-services"
        try:
            response = proxy_request(
                products_page_url,
                proxies=DEFAULT_PROXY,
                headers=prepare_base_thomasnet_headers(),
                use_cloudscraper=True,
            )
            selector = Selector(response.text)
            next_data = ThomasnetBusinessScraper._parse_next_data(selector, products_page_url)
        except Exception as e:
            logger.warning("Can't get products services page for url", url=products_page_url, exc_info=e)
            return None

        if not next_data:
            return None

        return ThomasnetBussinessProducts(
            summary_products=ThomasnetBusinessScraper._parse_summary_products(next_data),
            detail_products=ThomasnetBusinessScraper._parse_detail_products(next_data),
        )

    @staticmethod
    @decorate_with_next_data_error_catching("name")
    def _parse_name(next_data: Dict) -> Optional[str]:
        return next_data["props"]["pageProps"]["data"]["name"]

    def scrape(self, url: str, basic_info_only: bool = False) -> Optional[ThomasnetBusiness]:
        response = proxy_request(
            url, proxies=DEFAULT_PROXY, headers=prepare_base_thomasnet_headers(), use_cloudscraper=True
        )
        if not response:
            return None
        selector = Selector(response.text)
        next_data = self._parse_next_data(selector, url)
        if not next_data:
            return None
        name = self._parse_name(next_data)
        address = self._parse_address(next_data)

        if not name or not address:
            logger.warning("Failed to scrape Thomasnet - either name or address wasn't found", url=url)
            return None

        if basic_info_only:
            return ThomasnetBusiness(name=name, address=address, url=url)

        return ThomasnetBusiness(
            name=name,
            url=url,
            address=address,
            descriptions=self._parse_descriptions(next_data, name),
            logo_url=self._parse_logo_url(next_data),
            website=self._parse_website(next_data),
            thomson_verified=self._parse_thomson_verified(next_data),
            phone=self._parse_phone_number(next_data),
            img_description_url=self._parse_img_description_url(next_data),
            primary_type=self._parse_primary_type(next_data),
            additional_activities=self._parse_additional_activities(next_data),
            certifications_desc=self._parse_certifications_desc(next_data),
            annual_sales=self._parse_annual_sales(next_data),
            employee_number_range=self._parse_employee_number_range(next_data),
            year_founded=self._parse_year_founded(next_data),
            certifications_url=self._parse_certifications_url(next_data),
            product_catalog_url=self._parse_product_catalog_url(next_data, selector),
            key_personnel=self._parse_key_personnel(next_data),
            news=self._parse_news(next_data),
            additional_corp_info=self._parse_additional_corp_info(next_data),
            products=self._parse_products(next_data),
            other_locations_tgrams_id=self._parse_other_locations_tgrams_id(next_data, selector),
        )
