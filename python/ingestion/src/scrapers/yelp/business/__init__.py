import html
import json
import os
import re
from collections import namedtuple
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from urllib.parse import unquote

from boto3 import client
from static_common.models.yelp import YelpBusiness, YelpDeliveryServices, YelpHealthInspection, YelpLicense
from infrastructure_common.logging import get_logger
from parsel import Selector
from requests import Response

from src.clients.proxy import proxy_request, DEFAULT_PROXIES
from src.clients.splash import SplashClient
from src.constants import USER_AGENT
from src.scrapers.yelp.business.qa_section import YelpQASectionScraper
from src.utils.parsing import maybe_clean_and_validate_url
from src.utils.user_agent import get_user_agent
from src.scrapers.yelp.utils import to_splash_proxies
from src.utils.lambda_metric import lambda_metric

logger = get_logger()

ServiceOffered = namedtuple("ServiceOffered", "services verified found")

AMENITY_LABELS = {
    "Offers Delivery",
    "Offers Takeout",
    "Alcohol",
    "Accepts Credit Cards",
    "Good for Kids",
    "Health Score",
    "Noise Level",
    "Ambience",
}

s3_client = client("s3")


def html_uncomment(s: str) -> str:
    if s.startswith("<!--"):
        return s[4:-3]


class YelpBusinessScraper:
    LABEL_BUSINESS_MENU_LINK = "View full menu"
    FAKE_USER_AGENT = get_user_agent(fallback=USER_AGENT)
    USER_AGENT_RANDOM = FAKE_USER_AGENT.random
    YELP_PROXIES = [None, *DEFAULT_PROXIES]
    SPLASH_CLIENT = SplashClient()

    @staticmethod
    def _extract_category_names(categories_data: List[Dict]) -> List[str]:
        return [category.get("title") for category in categories_data]

    @staticmethod
    def _extract_business_type(local_business_data: List[Dict]) -> List[str]:
        categories = []
        if local_business_data:
            for data in local_business_data:
                data = data.get("data") or {}  # noqa: PLW2901
                business = data.get("business") or {}
                categories_ = business.get("categories") or []
                for category in categories_:
                    if isinstance(category, dict):
                        ancestries = category.get("ancestry") or []
                        for ancestry in ancestries:
                            categories.append(ancestry.get("title"))
                    else:
                        categories.append(category)
        return list(set(filter(None, categories)))

    @staticmethod
    def _extract_license(license_data: Dict[str, Any]) -> Optional[YelpLicense]:
        if not (license_number := license_data.get("licenseNumber").strip()):
            return None

        verification_date = None
        if date_str := license_data.get("verificationDate"):
            try:
                verification_date = datetime.strptime(date_str.strip(), "%Y-%m-%d").date()
            except Exception:
                pass

        issuing_authority = license_data.get("issuingAuthority")
        trade = license_data.get("trade")

        return YelpLicense(
            license_number=license_number,
            issuing_authority=issuing_authority.strip() if issuing_authority else None,
            trade=trade.strip() if trade else None,
            verification_date=verification_date,
        )

    @staticmethod
    def _extract_menu_link(page: Selector, link_name: str):
        if link_name:
            menu_href = f"/menu/{link_name}"
            menu_link = page.xpath(f"//a[contains(@href, '{menu_href}')]/@href").get()
            if menu_link:
                return menu_link
        return ""

    @staticmethod
    def _extract_website(base: Dict, page: Selector) -> Optional[str]:
        infoProps = base.get("bizContactInfoProps") or {}
        businessWebsite = infoProps.get("businessWebsite") or {}

        if not (website := businessWebsite.get("linkText")):
            link_location = "//a[contains(@href, '/biz_redir')]"
            for link in page.xpath(f"{link_location}/text()").getall():
                if link and link != YelpBusinessScraper.LABEL_BUSINESS_MENU_LINK:
                    website = link
                    break

            # Catching a corner case when yelp hides part of url
            if website is None or "…" in website:
                try:
                    website = page.xpath(f"{link_location}/@href").get()
                    website = website.split("url=")[1].split("&")[0]
                    website = unquote(website)
                except Exception:
                    pass
        if not website:
            logger.info("Scraped no website from Yelp")
            return None
        cleaned_website = maybe_clean_and_validate_url(website)
        if not cleaned_website:
            logger.warning("Website is not a valid url", website=website)
        return cleaned_website

    @staticmethod
    def _extract_business_closed(selector: Selector):
        title = selector.xpath("//meta[@property='og:title']/@content").get() or ""
        return "- CLOSED -" in title.strip()

    @staticmethod
    def _extract_from_the_business(details: Dict):
        if from_business := details.get("fromTheBusinessProps"):
            return from_business.get("fromTheBusinessContentProps")

    @staticmethod
    def _extract_business_owner_details(from_the_business: Dict):
        BusinessOwner = namedtuple("BusinessOwner", "name role bio")
        if business_owner := from_the_business.get("businessOwner"):
            name = business_owner.get("markupDisplayName")
            role = business_owner.get("localizedRole")
            bio = from_the_business.get("businessOwnerBio")
            return BusinessOwner(
                html.unescape(name) if name else None,
                html.unescape(role) if role else None,
                html.unescape(bio) if bio else None,
            )
        else:
            return BusinessOwner(None, None, None)

    @staticmethod
    def _extract_year_established(from_the_business: Dict):
        year_established = from_the_business.get("yearEstablished")
        return html.unescape(year_established) if year_established else None

    @staticmethod
    def _extract_specialities(from_the_business: Dict):
        business_specialities = from_the_business.get("specialtiesText")
        return html.unescape(business_specialities) if business_specialities else None

    @staticmethod
    def _extract_history(from_the_business: Dict):
        history = from_the_business.get("historyText")
        return html.unescape(history) if history else None

    @staticmethod
    def _extract_price_range_from_dict(from_the_business: Dict) -> Optional[str]:
        if "priceRange" in from_the_business:
            try:
                price_range = from_the_business.get("priceRange")
                if not price_range:
                    return
                if isinstance(price_range, str):
                    value = html.unescape(price_range)
                elif isinstance(price_range, dict) and "display" in price_range:
                    value = html.unescape(price_range.get("display"))
                else:
                    value = None
                if value and isinstance(value, int):
                    value = "$" * value
                return value
            except Exception as e:
                logger.warning("Could not extract price range", exc_info=e)

    @staticmethod
    def _extract_price_range_from_list_of_dicts(local_business_data: List[Dict]) -> Optional[str]:
        for details in local_business_data:
            if detailed_data := details.get("data", {}).get("business", {}):
                if range := YelpBusinessScraper._extract_price_range_from_dict(detailed_data):
                    return range
        return None

    @staticmethod
    def _extract_service_offered(details: Dict):
        if service_offerings_prop := details.get("serviceOfferingsProps"):
            if services := service_offerings_prop.get("services"):
                all_services = [
                    html.unescape(service["serviceName"]) for service in services if "serviceName" in service
                ]
                if not all_services:
                    all_services = None
                verified = service_offerings_prop.get("verified", False)
                return ServiceOffered(all_services, verified, bool(all_services))
        return ServiceOffered(None, None, False)

    @staticmethod
    def _extract_business_info_label_by_title(business_infos: List[Dict], title: str) -> Optional[str]:
        for business_info in business_infos:
            if business_info["alias"] == title:
                return business_info["displayText"]

    @staticmethod
    def _extract_delivery_services(page: Selector) -> Optional[YelpDeliveryServices]:
        has_takeout_div = bool(len(page.css("div:contains('Takeout')").extract()))
        has_delivery_address_input = bool(len(page.css("#address-autocomplete")))

        if has_delivery_address_input or has_takeout_div:
            return YelpDeliveryServices(
                offers_delivery=has_delivery_address_input,
                offers_takeout=has_takeout_div,
                delivery_fee=None,  # todo
                takeout_fee=None,  # todo
            )

    def _has_inspections_link(self, selector: Selector) -> bool:
        inspections_link = selector.xpath("//a[contains(@href, '/inspections/')]/text()").get()
        return inspections_link is not None

    def get_external_biz_id(self, selector: Selector) -> Optional[str]:
        return selector.xpath('//meta[@name="yelp-biz-id"]/@content').get()

    def get_link_name(self, yelp_url: str) -> Optional[str]:
        return yelp_url.split("/biz/", 1)[1]

    def enrich_with_inspection_links(self, business: YelpBusiness, selector: Selector):
        has_inspections_link = self._has_inspections_link(selector)
        logger.info(
            "Business has_inspections_link dump", business_url=business.url, has_inspections_link=has_inspections_link
        )
        if has_inspections_link:
            logger.info("YelpBusiness Scrape Result: Inspection Links")
            try:
                yelp_inspections_url = business.url.replace("biz", "inspections")
                inspection_response = self.SPLASH_CLIENT.get_page_with_proxy(
                    yelp_inspections_url, to_splash_proxies(self.YELP_PROXIES)
                )
            except Exception as e:
                logger.warning("Could not scrape health violations", exc_info=e)

    def enrich_delivery_services(self, business: YelpBusiness, selector: Selector):
        try:
            business.delivery_services = self._extract_delivery_services(selector)
            logger.info("YelpBusiness Scrape Result: Delivery services")
        except Exception as e:
            logger.warning("Could not scrape delivery services", exc_info=e)

    def _get_json(self, external_business_id: str) -> Response:
        return self.SPLASH_CLIENT.get_page_with_proxy(
            f"https://www.yelp.com/biz/{external_business_id}/props", to_splash_proxies(self.YELP_PROXIES)
        )

    def scrape_from_application_json(
        self, yelp_url: str, business: YelpBusiness, external_business_id: str, selector: Selector
    ) -> Optional[YelpBusiness]:
        try:
            logger.info("Scraping from json")
            response = self._get_json(external_business_id)
            base = json.loads(Selector(text=response.text).xpath("//pre/text()").get()).get("bizDetailsPageProps")
            if not base:
                logger.warning("Failed to extract base JSON payload")
                return None

            business.website = self._extract_website(base, selector)
            if all_details := base:
                services_offered = self._extract_service_offered(all_details)
                business.services = services_offered.services
                business.services_verified = services_offered.verified
                business.services_found = services_offered.found
                if from_the_business := self._extract_from_the_business(all_details):
                    business_owner = self._extract_business_owner_details(from_the_business)
                    business.business_owner_name = business_owner.name
                    business.business_owner_role = business_owner.role
                    business.business_owner_bio = business_owner.bio
                    business.year_established = self._extract_year_established(from_the_business)
                    business.specialities = self._extract_specialities(from_the_business)
                    business.history = self._extract_history(from_the_business)
        except Exception as e:
            logger.warning("Failed to scrape business from json", exc_info=e, url=yelp_url)

        return business

    def _get_address(self, selector: Selector):
        try:
            return (
                selector.xpath('//a[contains(text(), "Get Directions")]')
                .xpath("../following-sibling::p/text()")
                .get()
                .strip()
            )
        except Exception as e:
            pass

        try:
            return ",".join(selector.xpath('//meta[@name="description"]/@content').get().split(",")[1:4]).strip()
        except Exception as e:
            pass

        return selector.xpath('//title[@data-rh="true"]/text()').get().split("-")[3].strip()

    def _get_main_page(self, url):
        return self.SPLASH_CLIENT.get_page_with_proxy(url, to_splash_proxies(self.YELP_PROXIES))

    def _get_phone(self, selector: Selector):
        try:
            opts = selector.xpath('//p[@class=" y-css-19xonnr"]/text()').getall()
            phone_regex = r"\(\d{3}\) \d{3}-\d{4}"
            for option in opts:
                if re.search(phone_regex, option):
                    return option
        except Exception as e:
            logger.warning("Failed to extract phone number", exc_info=e)
            return None

    def _get_website(self, selector: Selector):
        try:
            website = selector.xpath('//a[@class="y-css-14ckas3"][@target="_blank"]/text()').get()
            if "www" not in website:
                website = "www." + website
            if "https" not in website:
                website = "https://" + website
            return website
        except Exception as e:
            logger.warning("Failed to extract website", exc_info=e)
            return None

    def _get_price_range(self, selector: Selector):
        try:
            return selector.xpath('//span[@class=" y-css-1tsir1e"]/text()').get().strip()
        except Exception as e:
            logger.warning("Failed to extract price range", exc_info=e)
            return None

    def _get_hours(self, selector: Selector):
        try:
            hours = {}
            rows = selector.xpath('//tr[@class=" y-css-29kerx"]').getall()
            for row in rows:
                day, hrs = Selector(text=row).xpath("//p/text()").getall()
                hours[day] = hrs
            return hours
        except Exception as e:
            logger.warning("Failed to extract hours", exc_info=e)
            return None

    def _get_categories(self, selector: Selector):
        try:
            categories = []
            opts = selector.xpath('//a[@class="y-css-1x1e1r2"]').getall()
            for option in opts:
                subselector = Selector(text=option)
                href = subselector.xpath("//a/@href").get()
                text = subselector.xpath("//a/text()").get()
                if text.replace(" ", "+") in href:
                    categories.append(text)
            return categories
        except Exception as e:
            logger.warning("Failed to extract categories", exc_info=e)
            return None

    def _get_business_types(self, selector: Selector):
        try:
            opts = selector.xpath('//script[@type="application/ld+json"][contains(text(), "BreadcrumbList")]').getall()
            for option in opts:
                try:
                    business_types = []
                    subselector = Selector(text=option)
                    data = json.loads(subselector.xpath("//script/text()").get())
                    for item in data.get("itemListElement"):
                        business_types.append(
                            item.get("name").replace("&quot;", '"').replace("&#x2F;", "/").replace("&amp;", "&")
                        )
                    return business_types
                except Exception as e:
                    continue
        except Exception as e:
            logger.warning("Failed to extract business types", exc_info=e)
            return None

    def _get_business_name(self, selector: Selector):
        name = selector.xpath('//h1[@class="y-css-olzveb"]/text()').get()
        if name:
            return name

        title = selector.xpath('//title[@data-rh="true"]/text()').get()
        if title:
            return title.split("-")[0].strip()

        return None

    def _get_base_business(self, url: str, tries: int, event_id: Optional[str] = None, basic_info_only: bool = False):
        logger.info("Scraping from source")
        try:
            try:
                response = self._get_main_page(url)
            except Exception as e:
                if not basic_info_only:
                    lambda_metric("scrape.yelp.business.failed_request", 1)
                raise e

            if "yelp captcha" in response.text.lower():
                logger.warning("Yelp Captcha detected", url=url)
                return None, None, None

            selector = Selector(text=response.text)
            external_business_id = self.get_external_biz_id(selector)
            business = None
            if event_id is not None:
                s3_client.put_object(
                    Body=response.text,
                    Bucket=os.environ.get("INGESTION_DATA_BUCKET", "kalepa-dev-ingestion-data-us-east-1"),
                    Key=f'tmp/yelp/scrape_business_{event_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html',
                )
            try:
                name = self._get_business_name(selector)
                address = self._get_address(selector)

                if name and address:
                    business = YelpBusiness(
                        id=external_business_id,
                        url=url,
                        link_name=self.get_link_name(url),
                        name=name,
                        address=address,
                    )
            except Exception as e:
                logger.warning("Failed to extract business name or address from source", exc_info=e)

            if business and external_business_id:
                logger.info("Returning base business information", business=business)
                return business, selector, external_business_id
            if tries == 0:
                logger.warning("Did not find business or biz_id after retries for url", retry_count=tries, url=url)
                return business, selector, external_business_id
            else:
                return self._get_base_business(url, tries - 1)
        except Exception as e:
            logger.warning("Failed to get biz_id", exc_info=e, url=url)
            return None, None, None

    def _scrape_health_info(self, business, business_json):
        try:
            inspections = []
            for inspection in business_json.get("healthInspections", []):
                inspection_score = inspection.get("formattedScore")
                inspection_date = inspection.get("dateTime")
                violations = []
                for violation in inspection.get("violations", []):
                    violations.append(violation.get("description"))
                inspection_violations = violations if violations else None
                inspections.append(
                    YelpHealthInspection(
                        score=inspection_score,
                        date=inspection_date,
                        critical_violations=None,
                        noncritical_violations=inspection_violations,
                    )
                )
            business.health_inspection_history = inspections if inspections else None
        except AttributeError as e:
            logger.info("Cannot scrape health info from business json", exc_info=e)
            business.health_inspection_history = None
        business.health_inspection = (
            business.health_inspection_history[0] if business.health_inspection_history else None
        )

    def _scrape_organized_properties(self, business, business_json):
        try:
            properties = []
            for json in business_json.get('organizedProperties({"clientPlatform":"WWW"})', []):
                if json.get("__typename", "") == "OrganizedBusinessPropertiesSection":
                    properties = json.get("properties", [])
                    break
            if not properties:
                return

            paired_properties = {
                "RestaurantsReservations": "has_reservations",
                "RestaurantsDelivery": "offers_delivery",
                "RestaurantsTakeOut": "offers_takeout",
                "liked_by_vegans": "vegan_options",
                "num_vegetarian_options": "vegetarian_options",
                "is_wheelchair_accessible": "wheelchair_accessible",
                "accepts_credit_cards": "accepts_credit_cards",
                "accepts_android_pay": "accepts_google_pay",
                "accepts_apple_pay": "accepts_apple_pay",
                "Ambience": "ambience",
                "NoiseLevel": "noise_level",
                "good_for_groups": "good_for_groups",
                "BusinessParking": "parking",
                "wifi_options": "wifi",
                "alcohol_options": "alcohol",
                "is_open_to_all": "open_to_all",
                "has_outdoor_seating": "outdoor_seating",
                "Caters": "caters",
                "good_for_kids": "good_for_kids",
                "happy_hour_specials": "happy_hour_specials",
                "dogs_allowed": "dogs_allowed",
                "has_tv": "has_tv",
                "has_bike_parking": "bike_parking",
                "GoodForMeal": "good_for",
                "has_table_service": "waiter_service",
            }

            for property in properties:
                if property.get("alias") in paired_properties:
                    setattr(
                        business,
                        paired_properties[property.get("alias")],
                        property.get("displayText") if property.get("isActive") else "No",
                    )
        except AttributeError as e:
            logger.info("Cannot scrape organized properties from business json", exc_info=e)

    def _scrape_review_count(self, business, business_json):
        try:
            business.review_count = business_json.get("reviewCount", business.review_count)
        except AttributeError as e:
            logger.info("Cannot scrape review count from source", exc_info=e)

    def _scrape_media_count(self, business, business_json):
        try:
            carousels = business_json.get("ambiance", {}).get("carousels", [])
            for carousel in carousels:
                if carousel.get("__typename", "") == "BusinessPhotoTab" and carousel.get("alias", "") == "all":
                    business.media_count = carousel.get("count", business.media_count)
                    break
        except AttributeError as e:
            logger.info("Cannot scrape media count from business json", exc_info=e)

    def _scrape_phone(self, business, business_json):
        try:
            business.phone = business_json.get("phoneNumber", {}).get("formatted", business.phone)
        except AttributeError as e:
            logger.info("Cannot scrape phone from business json", exc_info=e)

    def _scrape_price_range(self, business, business_json):
        try:
            business.price_range = business_json.get("priceRange", {}).get("display", business.price_range)
        except AttributeError as e:
            logger.info("Cannot scrape price range from business json", exc_info=e)

    def _scrape_hours(self, business, business_json):
        try:
            hours = {}
            hours_info = business_json.get("operationHours", {}).get(
                "regularHoursMergedWithSpecialHoursForCurrentWeek", []
            )
            for info in hours_info:
                day = info.get("dayOfWeekShort")
                times = info.get("regularHours")[0].replace("(Next day)", "").strip()
                hours[day] = times

            business.hours = hours if hours else business.hours
        except AttributeError as e:
            logger.info("Cannot scrape hours from business json", exc_info=e)

    def _scrape_year(self, business, business_json):
        try:
            business.year_established = business_json.get("history", {}).get(
                "yearEstablished", business.year_established
            )
        except AttributeError as e:
            logger.info("Cannot scrape year from business json", exc_info=e)

    def _scrape_specialities(self, business, business_json):
        try:
            business.specialities = business_json.get("specialties", None)
        except AttributeError as e:
            logger.info("Cannot scrape specialities from business json", exc_info=e)

    def scrape_from_main_page_json(self, business, selector, business_id):
        logger.info("Scraping main page json")
        main_page_json = None
        try:
            main_page_json = (
                selector.xpath('//script[@type="application/json"][contains(text(), "ROOT_QUERY")]/text()')
                .get()[4:-3]
                .replace("&quot;", '"')
                .replace("&#x2F;", "/")
                .replace("&amp;", "&")
            )
        except Exception as e:
            logger.warning("Failed to extract main page json", exc_info=e)

        if main_page_json is None:
            logger.info("No main page json found")
            return None
        whole_json = json.loads(main_page_json)
        business_json = whole_json[f"Business:{business_id}"]

        self._scrape_media_count(business, business_json)
        self._scrape_review_count(business, business_json)
        self._scrape_phone(business, business_json)
        self._scrape_price_range(business, business_json)
        self._scrape_hours(business, business_json)
        self._scrape_year(business, business_json)
        self._scrape_health_info(business, business_json)
        self._scrape_organized_properties(business, business_json)
        self._scrape_specialities(business, business_json)
        business.delivery_services = YelpDeliveryServices(
            offers_delivery=business.offers_delivery,
            offers_takeout=business.offers_takeout,
            delivery_fee=None,
            takeout_fee=None,
        )

    def scrape_extra_info(self, business, selector):
        business.phone = self._get_phone(selector)
        business.website = self._get_website(selector)
        business.price_range = self._get_price_range(selector)
        business.hours = self._get_hours(selector)
        business.categories = self._get_categories(selector)
        business.business_types = self._get_business_types(selector)

    def scrape(
        self, yelp_url: str, basic_info_only: bool = False, event_id: Optional[str] = None
    ) -> Optional[YelpBusiness]:
        self._logger = logger.bind(url=yelp_url)
        logger.info("Scraping Yelp business", url=yelp_url)
        business, selector, external_business_id = self._get_base_business(yelp_url, 3, event_id, basic_info_only)

        if business:
            logger.info(
                "Scraped base business",
                id=business.id,
                url=business.url,
                link_name=business.link_name,
                name=business.name,
                address=business.address,
            )

        if basic_info_only or not business:
            logger.info("Returning basic business information", business=business)
            return business

        if business is None or (selector is None and external_business_id is None):
            return business

        self.scrape_extra_info(business, selector)

        if external_business_id:
            business = self.scrape_from_application_json(yelp_url, business, external_business_id, selector)
        if selector and external_business_id:
            self.scrape_from_main_page_json(business, selector, external_business_id)
        if business and selector:
            business.menu_link = self._extract_menu_link(selector, business.link_name)
            business.is_closed = self._extract_business_closed(selector)
            self.enrich_with_inspection_links(business, selector)
            try:
                business.qa_section = YelpQASectionScraper().scrape(external_business_id)
            except Exception as e:
                logger.warning("Failed to scrape QA section", exc_info=e)
        else:
            logger.warning("YelpBusiness Scrape Result: Issue - No business loaded")
        logger.info("Returning full business information", business=business)
        return business
