import re

import requests
from bs4 import BeautifulSoup
from infrastructure_common.logging import get_logger

from src.clients.proxy import proxy_request
from src.models.ca_cslb import (
    BondInformation,
    BusinessInformation,
    ContractorData,
    LicensePage,
    WorkersCompInformation,
)

logger = get_logger()


class CACSLBClient:
    VIEWSTATE = "xHhG9+aPvsve+07SNDKZrCjVCcZ45oKh3qw4dJJI9WtZvBYMs2vilS4UBvG1b4M05jPRxNxAa5vwJ89bM+EynatIPns="
    VIEWSTATEGENERATOR = "3A7CC068"
    EVENTVALIDATION = (
        "WgAc4WPkvA8zcLrbgDb7jVuAo5M+SGSJicQAbIj8BpxsDs6KYmQhPEwBA04XrPFav2F46wlzkjYWD+QvxxyD1byxt"
        "3SGWUY0B6OuyF12iegXr7XH4LkNC3qtj2dx74ScWKQc/nwx4VCHQ3qzym81YrXVEV69IiWrWYcgcjy5OlBLgObmHL"
        "LeOhL33ozuubWPcYPkiDRf10QkpIvD/Gj4BgVbSmY+17oecLfyBdLTt3DIfFRCR91nq1Ogkx0Cki6pwuvUlEGd/FxYXN"
        "f116wT0ZxoX3cvZtWxxKw1RGyYdw1PqDZMW2jrxzcyefvH2oW/30l3VOojvFskohDdXQ2KCyipEwwRxxXSn0OU19zOgP"
        "OasYCy"
    )

    @classmethod
    def search_ca_license_board(cls, company_name: str) -> str | None:
        log = logger.bind(company_name=company_name)
        data = {
            "__VIEWSTATE": cls.VIEWSTATE,
            "__VIEWSTATEGENERATOR": cls.VIEWSTATEGENERATOR,
            "__EVENTVALIDATION": cls.EVENTVALIDATION,
            "ctl00$MainContent$NextName": company_name,
            "ctl00$MainContent$Contractor_Business_Name_Button": "",
        }

        try:
            response = proxy_request(
                url="https://cslb.ca.gov/OnlineServices/CheckLicenseII/CheckLicense.aspx",
                method="POST",
                data=data,
                timeout=20,
                if_retry=False,
            )
            response.raise_for_status()  # Raise an error for bad responses
        except requests.exceptions.RequestException as e:
            log_payload = {"error": str(e)}
            if hasattr(e, "response") and e.response is not None:
                log_payload["response"] = e.response.text
                log_payload["status_code"] = e.response.status_code
                log_payload["reason"] = e.request.reason
            log.warning("Search Request to CA CSLB failed", **log_payload)
            return None
        except Exception as e:
            log.exception("Unexpected error occurred during CA CSLB search", error=str(e))
            return None
        return response.text

    @staticmethod
    def extract_contractor_data(html_content: str) -> list[ContractorData]:
        """
        Extracts contractor information from the HTML content.

        Args:
            html_content: The HTML content as a string.

        Returns:
            A list of ContractorData objects containing the extracted information.
        """
        soup = BeautifulSoup(html_content, "html.parser")
        contractors_data: list[ContractorData] = []

        # Find the main table containing all contractor entries
        main_table = soup.find("table", id="MainContent_dlMain")

        if not main_table:
            logger.warning("Could not find the main contractor table.")
            return contractors_data

        # Each contractor's details are within a <tr>, and then a <td>, which contains another <table>
        contractor_entries = main_table.find_all("tr", recursive=False)

        for entry in contractor_entries:
            # The actual data table for each contractor is nested inside a <td>
            data_table = entry.find("td").find("table")
            if not data_table:
                continue

            rows = data_table.find_all("tr")

            try:
                contractor_name = rows[0].find_all("td")[1].get_text(strip=True)
                name_type = rows[1].find_all("td")[1].get_text(strip=True)
                license_number = rows[2].find_all("td")[1].find("a").get_text(strip=True)
                city = rows[3].find_all("td")[1].get_text(strip=True)
                status = rows[4].find_all("td")[1].get_text(strip=True)
                contractor_data = ContractorData(
                    contractor_name=contractor_name,
                    name_type=name_type,
                    license_number=license_number,
                    city=city,
                    status=status,
                )
                contractors_data.append(contractor_data)
            except IndexError:
                # Handle cases where a row or cell might be missing, though unlikely with this structure
                logger.warning("Could not parse all details for an entry.")
            except AttributeError:
                # Handle cases where an expected tag (like 'a' for license) is missing
                logger.warning("Could not find expected tag for an entry.")

        return contractors_data

    @staticmethod
    def get_license_page(license_number: str) -> str | None:
        headers = {
            "Referer": "https://cslb.ca.gov/OnlineServices/CheckLicenseII/CheckLicense.aspx",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
        }

        params = (("LicNum", license_number),)
        log = logger.bind(license_number=license_number)
        try:
            response = proxy_request(
                url="https://cslb.ca.gov/OnlineServices/CheckLicenseII/LicenseDetail.aspx",
                method="GET",
                params=params,
                headers=headers,
                timeout=20,
                if_retry=False,
            )
            response.raise_for_status()  # Raise an error for bad responses
        except requests.exceptions.RequestException as e:
            log_payload = {"error": str(e)}
            if hasattr(e, "response") and e.response is not None:
                log_payload["response"] = e.response.text
                log_payload["status_code"] = e.response.status_code
                log_payload["reason"] = e.request.reason
            log.warning("Retrieving Licence Page from CA CSLB failed", **log_payload)
            return None
        except Exception as e:
            log.exception("Unexpected error occurred when retrieving CA CSLB license page", error=str(e))
            return None
        return response.text

    @staticmethod
    def _extract_business_information_from_license_page(soup: BeautifulSoup) -> BusinessInformation:
        # Helper function to get text from an element if it exists
        def get_text_by_id(element_id):
            element = soup.find(id=element_id)
            return element.get_text(strip=True) if element else None

        # --- Business Information ---
        bus_info_td = soup.find("td", id="MainContent_BusInfo")
        business_name = None
        address = None
        phone_number = None
        license_status = None
        if bus_info_td:
            bus_info_lines = [line.strip() for line in bus_info_td.get_text(separator="\n").split("\n") if line.strip()]

            # Attempt to parse Business Name, Address, and Phone
            # This is heuristic and might need adjustment if the format varies
            if bus_info_lines:
                business_name = bus_info_lines[0]  # Assuming first line is business name

                address_lines = []
                phone_number = None

                # Heuristic to find address and phone
                # Address lines are typically consecutive and don't start with "Business Phone Number:"
                # Phone number usually starts with "Business Phone Number:"

                potential_address_end_index = len(bus_info_lines)
                for i, line in enumerate(bus_info_lines):
                    if "Business Phone Number:" in line:
                        phone_number = line.split("Business Phone Number:")[1].strip()
                        potential_address_end_index = i  # Address lines should be before phone
                        break

                # Collect address lines (usually after name/DBA and before phone)
                start_address_index = 1  # Start after business name

                for i in range(start_address_index, potential_address_end_index):
                    if bus_info_lines[i]:  # Add non-empty lines
                        address_lines.append(bus_info_lines[i])

                address = ", ".join(address_lines) if address_lines else None
                phone_number = phone_number

        # --- Other Direct Fields ---
        entity_type = get_text_by_id("MainContent_Entity")
        license_issue_date = get_text_by_id("MainContent_IssDt")
        license_expiration_date = get_text_by_id("MainContent_ExpDt")

        status_element = soup.find(id="MainContent_Status")
        if status_element:
            status_strong = status_element.find("strong")
            license_status = (
                status_strong.get_text(strip=True) if status_strong else status_element.get_text(strip=True)
            )

        class_element = soup.find(id="MainContent_ClassCellTable")
        classifications = []
        for item in class_element.find_all("a"):
            classifications.append(item.get_text(strip=True))
        license_classifications = classifications
        return BusinessInformation(
            business_name=business_name,
            address=address,
            phone_number=phone_number,
            entity_type=entity_type,
            license_issue_date=license_issue_date,
            license_expiration_date=license_expiration_date,
            license_status=license_status,
            license_classifications=license_classifications,
        )

    @staticmethod
    def _extract_bond_information_from_license_page(soup: BeautifulSoup) -> BondInformation:
        # --- Bonding Information ---
        bonding_td = soup.find("td", id="MainContent_BondingCellTable")
        bond_information = BondInformation(
            bond_agent=None,
            bond_number=None,
            bond_amount=None,
            bond_effective_date=None,
            bond_cancellation_date=None,
        )
        if bonding_td:
            bond_agent_a_tag = bonding_td.find("a", href=lambda href: href and "INSDetail.aspx" in href)
            bond_agent = None
            if bond_agent_a_tag:
                bond_agent = bond_agent_a_tag.get_text(strip=True)
            bonding_text = bonding_td.get_text(separator="\n", strip=True)

            bond_number_match = re.search(r"Bond Number:\s*([^\n]+)", bonding_text)
            bond_number = bond_number_match.group(1).strip() if bond_number_match else None

            bond_amount_match = re.search(r"Bond Amount:\s*([^\n]+)", bonding_text)
            bond_amount = bond_amount_match.group(1).strip() if bond_amount_match else None

            # Dates in bonding info can be tricky if there are multiple date sets.
            # This tries to find dates associated with the primary contractor's bond.
            # Regex looks for "Effective Date: <date>" not preceded by "Bond of Qualifying Individual"
            # and similar for Cancellation Date.

            # Find Contractor's Bond section specifically
            contractor_bond_section_text = ""
            all_p_tags = bonding_td.find_all("p")
            for i, p_tag in enumerate(all_p_tags):
                if "This license filed a Contractor's Bond" in p_tag.get_text():
                    # Collect this and subsequent <p> tags until BQI or history link
                    for j in range(i, len(all_p_tags)):
                        current_p_text = all_p_tags[j].get_text(separator="\n", strip=True)
                        if "Bond of Qualifying Individual" in current_p_text or (
                            all_p_tags[j].find("a") and "Bond History" in all_p_tags[j].find("a").get_text()
                        ):
                            break
                        contractor_bond_section_text += current_p_text + "\n"
                    break

            if not contractor_bond_section_text and all_p_tags:  # Fallback if specific intro not found
                contractor_bond_section_text = bonding_text

            effective_date_match = re.search(r"Effective Date:\s*(\d{2}/\d{2}/\d{4})", contractor_bond_section_text)
            bond_effective_date = effective_date_match.group(1).strip() if effective_date_match else None

            cancellation_date_match = re.search(
                r"Cancellation Date:\s*(\d{2}/\d{2}/\d{4})", contractor_bond_section_text
            )
            bond_cancellation_date = cancellation_date_match.group(1).strip() if cancellation_date_match else None
            bond_information = BondInformation(
                bond_agent=bond_agent,
                bond_number=bond_number,
                bond_amount=bond_amount,
                bond_effective_date=bond_effective_date,
                bond_cancellation_date=bond_cancellation_date,
            )
        return bond_information

    @staticmethod
    def _extract_workers_comp_details_from_license_page(soup: BeautifulSoup) -> WorkersCompInformation | None:
        # --- Workers' Compensation ---
        wc_td = soup.find("td", id="MainContent_WCStatus")
        if wc_td:
            wc_company_a_tag = wc_td.find("a", href=lambda href: href and "INSDetail.aspx" in href)
            insurance_company = None
            if wc_company_a_tag:
                insurance_company = wc_company_a_tag.get_text(strip=True)
            insurance_company = insurance_company
            wc_text = wc_td.get_text(separator="\n", strip=True)
            policy_number_match = re.search(r"Policy Number:\s*([^\n]+)", wc_text)
            policy_number = policy_number_match.group(1).strip() if policy_number_match else None

            wc_effective_date_match = re.search(r"Effective Date:\s*(\d{2}/\d{2}/\d{4})", wc_text)
            effective_date = wc_effective_date_match.group(1).strip() if wc_effective_date_match else None

            wc_cancellation_date_match = re.search(
                r"Cancellation Date:\s*(\d{2}/\d{2}/\d{4})", wc_text
            )  # Changed from 'Expiration'
            cancellation_date = wc_cancellation_date_match.group(1).strip() if wc_cancellation_date_match else None
            return WorkersCompInformation(
                insurance_company=insurance_company,
                policy_number=policy_number,
                effective_date=effective_date,
                cancellation_date=cancellation_date,
            )
        return None

    @classmethod
    def extract_license_details(cls, html_content: str, contractor: ContractorData) -> LicensePage:
        soup = BeautifulSoup(html_content, "html.parser")
        business_information = cls._extract_business_information_from_license_page(soup)
        bond_information = cls._extract_bond_information_from_license_page(soup)
        workers_comp_information = cls._extract_workers_comp_details_from_license_page(soup)

        return LicensePage(
            business_information=business_information,
            bond_information=bond_information,
            workers_comp_information=workers_comp_information,
            contractor_data=contractor,
        )
