import dataclasses

from infrastructure_common.logging import bind_lambda_logging_context, get_logger

from src.clients.ca_cslb import CACSLBClient
from src.models.ca_cslb import ContractorData, LicensePage
from src.utils.logging_utils import log_function_inputs

logger = get_logger().bind(handler_test="CA_CSLB")


def _validate_license_data(license_number: str, license_data: LicensePage) -> dict[str, bool] | None:
    if license_number == "440770":
        return {
            "name": license_data.business_information.business_name == "SOWELL LANDSCAPE",
            "address": license_data.business_information.address == "P O BOX 398, SOLANA BEACH, CA 92075",
            "phone_number": license_data.business_information.phone_number == "(*************",
            "entity_type": license_data.business_information.entity_type == "Sole Ownership",
            "license_issue_date": license_data.business_information.license_issue_date == "05/27/1983",
            "license_expiration_date": license_data.business_information.license_expiration_date == "05/31/2017",
            "license_status": license_data.business_information.license_status
            == "This license is expired and not able to contract at this time.",
            "license_classification": license_data.business_information.license_classifications
            == ["C27 - LANDSCAPING"],
            "bond_agent": license_data.bond_information.bond_agent == "AMERICAN CONTRACTORS INDEMNITY COMPANY",
            "bond_amount": license_data.bond_information.bond_amount == "$15,000",
            "bond_effective_date": license_data.bond_information.bond_effective_date == "01/01/2016",
            "bond_cancellation_date": license_data.bond_information.bond_cancellation_date == "03/19/2016",
            "bond_number": license_data.bond_information.bond_number == "221477",
            "workers_comp_insurance_company": license_data.workers_comp_information.insurance_company
            == "STATE COMPENSATION INSURANCE FUND",
            "workers_comp_policy_number": license_data.workers_comp_information.policy_number == "9099187",
            "workers_comp_effective_date": license_data.workers_comp_information.effective_date == "05/03/2014",
            "workers_comp_cancellation_date": license_data.workers_comp_information.cancellation_date == "04/03/2015",
        }
    else:
        logger.info("Test CA CSLB license number not found", license_number=license_number)
        return None


@bind_lambda_logging_context
@log_function_inputs
def test_search_ca_cslb_license(event, context=None):
    logger.info(
        "Running CA CSLB scraper test", business_name=event["contractor_name"], license_number=event["license_number"]
    )

    scraped = False
    error = False

    try:
        search_html = CACSLBClient.search_ca_license_board(event["contractor_name"])
    except Exception:
        search_html = None

    if not search_html:
        logger.error("CA CSLB search failed", _event=event)
        error = True
        return {
            "type": "CA_CSLB",
            "contractor_name": event["contractor_name"],
            "license_number": event["license_number"],
            "scraped": scraped,
            "error": error,
        }

    contractors = CACSLBClient.extract_contractor_data(search_html)
    if len(contractors) < 50:
        logger.warning("CA CSLB search returned less than 50 contractors")
    fields = dataclasses.fields(ContractorData)
    for field in fields:
        scraped_field = any(bool(getattr(contractor, field.name)) for contractor in contractors)
        if not scraped_field:
            logger.error("CA CSLB search did not scrape a field", field=field.name, _event=event)
            error = True

    try:
        license_page_html = CACSLBClient.get_license_page(event["license_number"])
    except Exception:
        license_page_html = None

    if not license_page_html:
        logger.error("CA CSLB license page failed", _event=event)
        error = True
        return {
            "type": "CA_CSLB",
            "contractor_name": event["contractor_name"],
            "license_number": event["license_number"],
            "scraped": scraped,
            "error": error,
        }
    contractor = ContractorData(
        contractor_name=event["contractor_name"],
        name_type=event["name_type"],
        license_number=event["license_number"],
        city=event["city"],
        status=event["status"],
    )
    license_data = CACSLBClient.extract_license_details(license_page_html, contractor)
    if not license_data:
        logger.error("CA CSLB license page failed to extract data", _event=event)
        error = True
        return {
            "type": "CA_CSLB",
            "contractor_name": event["contractor_name"],
            "license_number": event["license_number"],
            "scraped": scraped,
            "error": error,
        }
    scraped = True
    checks = _validate_license_data(event["license_number"], license_data)
    if not checks:
        logger.error("CA CSLB license page failed to validate data", _event=event)
        error = True
        return {
            "type": "CA_CSLB",
            "contractor_name": event["contractor_name"],
            "license_number": event["license_number"],
            "scraped": scraped,
            "error": error,
        }

    failed_checks = [key for key, value in checks.items() if not value]
    if failed_checks:
        logger.error("CA CSLB license failed some checks", failed_checks=failed_checks, _event=event)
        error = True
    return {
        "type": "CA_CSLB",
        "contractor_name": event["contractor_name"],
        "license_number": event["license_number"],
        "scraped": scraped,
        "error": error,
    }
