import hashlib
from dataclasses import dataclass
from datetime import datetime, timedelta

from common.logic.addresses import try_parse_address_string
from common.utils.type_conversion import camel_to_normal
from copilot_client_v3 import (
    SubmissionSyncCoverage,
    SubmissionSyncMatcherData,
    SubmissionSyncReport,
)
from frozendict import frozendict
from infrastructure_common.logging import get_logger
from sqlalchemy import Column, String
from static_common.enums.enum import StrEnum
from static_common.enums.submission import SubmissionStage

from src.models._private import BaseModel
from src.models.types import CoverageType

logger = get_logger()


@dataclass
class ConiferCoverageMapping:
    coverage_name: str
    coverage_type: CoverageType
    keep_other_coverage: bool | None = None


class ConiferIssuanceStatus(StrEnum):
    PENDING_REFERRAL = "PENDING-REFERRAL"
    SUBMITTED = "SUBMITTED"
    APPROVED_REFERRAL = "APPROVED-REFERRAL"
    PENDING = "PENDING"
    CANCEL = "CANCEL"
    RESCIND = "RESCIND"
    ACCEPTED_REFERRAL = "ACCEPTED-REFERRAL"
    ALL = "ALL"
    ALL_REFERRAL = "ALL-REFERRAL"
    DENIED_REFERRAL = "DENIED-REFERRAL"


class ConiferUnderwritingStatus(StrEnum):
    PENDING = "Pending"
    UW_DECLINED = "UWDeclined"
    APPROVED = "Approved"
    AGENCY_REQUEST = "Agency Request"
    UNDERWRITING_REASONS = "UnderwritingReasons"
    PRODUCT_CANCELLED_BY_UNDERWRITER = "Product cancelled by underwriter"
    HOLD = "Hold"
    LOST = "Lost"
    MGMT_APPROVED = "MgmtApproved"
    MGMT_DECLINED = "MgmtDeclined"
    NOT_RELEASED = "Not Released"
    QUOTED = "Quoted"
    DECLINED = "Declined"
    QUOTE_RELEASED = "Quote Released"
    READY_TO_QUOTE = "Ready to quote"
    AGENT_CLOSE = "Agent Close"
    ALTERNATE = "Alternate"
    DUPLICATE_QUOTE = "Duplicate Quote"
    QUOTE_HOLD = "Quote Hold"
    QUOTE_IN_PROCESS = "Quote In Process"
    QUOTE_NOT_RELEASED = "Quote Not Released"
    SETUP_IN_PROCESS = "Setup In Process"


class ConiferPolicyType(StrEnum):
    CA = "CA"
    NFAD = "NFAD"
    GPP = "GPP"
    GP = "GP"
    BOP = "BOP"
    CPP = "CPP"
    LL = "LL"
    WC = "WC"
    GL = "GL"
    CUP = "CUP"
    THC = "THC"


class ConiferPolicyPackageMemberType(StrEnum):
    CA = "CA"
    NFAD = "NFAD"
    GPP = "GPP"
    GP = "GP"
    BOP = "BOP"
    CPP = "CPP"
    LL = "LL"
    WC = "WC"
    GL = "GL"
    CUP = "CUP"
    THC = "THC"
    CR = "CR"
    DO = "DO"
    DW = "DW"
    GS = "GS"
    IM = "IM"
    PR = "PR"
    MP = "MP"
    ML = "ML"
    PL = "PL"
    OT = "OT"


class ConiferNewOrRenewal(StrEnum):
    AUDIT = "AUDIT"
    OOS_AMD = "OOS AMD"
    NEW = "NEW"
    BATCH_RENEW = "BATCHRENEW"
    INQ = "INQ"
    REN = "REN"
    AMD = "AMD"
    BATCH_PRE_RENEW = "BATCHPRERENEW"
    CAN = "CAN"

    @staticmethod
    def valid_values() -> set["ConiferNewOrRenewal"]:
        return {
            ConiferNewOrRenewal.NEW,
            ConiferNewOrRenewal.BATCH_RENEW,
            ConiferNewOrRenewal.REN,
            ConiferNewOrRenewal.BATCH_PRE_RENEW,
        }

    @property
    def is_renewal(self) -> bool:
        return self in {
            ConiferNewOrRenewal.REN,
            ConiferNewOrRenewal.BATCH_RENEW,
            ConiferNewOrRenewal.BATCH_PRE_RENEW,
        }


class ConiferBoundPolicyStatus(StrEnum):
    ACTIVE = "ACTIVE"
    RESCINDED = "RESCINDED"
    CANCELLED = "CANCELLED"
    EXPIRED = "EXPIRED"


COPILOT_STATUS_MAPPING = {
    ConiferIssuanceStatus.SUBMITTED: SubmissionStage.QUOTED_BOUND,
    ConiferIssuanceStatus.PENDING: {
        None: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.NOT_RELEASED: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.APPROVED: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.HOLD: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.LOST: SubmissionStage.QUOTED_LOST,
        ConiferUnderwritingStatus.MGMT_APPROVED: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.MGMT_DECLINED: SubmissionStage.DECLINED,
        ConiferUnderwritingStatus.PENDING: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.UW_DECLINED: SubmissionStage.DECLINED,
        ConiferUnderwritingStatus.QUOTED: SubmissionStage.QUOTED,
        # new statuses
        ConiferUnderwritingStatus.AGENT_CLOSE: SubmissionStage.CANCELED,
        ConiferUnderwritingStatus.QUOTE_HOLD: SubmissionStage.WAITING_FOR_OTHERS,
        ConiferUnderwritingStatus.QUOTE_IN_PROCESS: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.READY_TO_QUOTE: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.QUOTE_NOT_RELEASED: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.QUOTE_RELEASED: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.DECLINED: SubmissionStage.DECLINED,
    },
    ConiferIssuanceStatus.APPROVED_REFERRAL: {
        None: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.APPROVED: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.HOLD: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.LOST: SubmissionStage.QUOTED_LOST,
        ConiferUnderwritingStatus.MGMT_APPROVED: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.MGMT_DECLINED: SubmissionStage.DECLINED,
        ConiferUnderwritingStatus.PENDING: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.UW_DECLINED: SubmissionStage.DECLINED,
    },
    ConiferIssuanceStatus.PENDING_REFERRAL: {
        None: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.APPROVED: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.HOLD: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.LOST: SubmissionStage.QUOTED_LOST,
        ConiferUnderwritingStatus.MGMT_APPROVED: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.MGMT_DECLINED: SubmissionStage.DECLINED,
        ConiferUnderwritingStatus.PENDING: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.UW_DECLINED: SubmissionStage.DECLINED,
        # new statuses
        ConiferUnderwritingStatus.AGENT_CLOSE: SubmissionStage.CANCELED,
        ConiferUnderwritingStatus.QUOTE_HOLD: SubmissionStage.WAITING_FOR_OTHERS,
        ConiferUnderwritingStatus.QUOTE_IN_PROCESS: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.READY_TO_QUOTE: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.QUOTE_NOT_RELEASED: SubmissionStage.ON_MY_PLATE,
        ConiferUnderwritingStatus.QUOTE_RELEASED: SubmissionStage.QUOTED,
        ConiferUnderwritingStatus.DECLINED: SubmissionStage.DECLINED,
    },
    ConiferIssuanceStatus.CANCEL: SubmissionStage.DECLINED,
}

COPILOT_ORG_GROUP_MAPPING = {
    ConiferPolicyType.CPP: {
        None: None,
        "AFPD": "Main St",
        "AlarmContractor": "Main St",
        "ArtisanContractors": "Main St",
        "Apartments": "Main St",
        "Bakeries": "Hospitality",
        "Bars and Taverns": "Hospitality",
        "Bowling": "Hospitality",
        "Bowling Centers": "Hospitality",
        "Brewery": "Hospitality",
        "CBD": "THC",
        "Cigar Stores": "Main St",
        "Convenience Stores": "Main St",
        "Craft Beverage": "Hospitality",
        "CraftBeverage": "Hospitality",
        "Distillery": "Hospitality",
        "Dwellings": "Main St",
        "FireSuppression": "Main St",
        "Fraternal": "Hospitality",
        "Fraternal Organizations": "Hospitality",
        "Gas Stations": "Main St",
        "HEMP": "THC",
        "Hospitality_Bakeries": "Hospitality",
        "Hospitality_Bars and Taverns": "Hospitality",
        "Hospitality_Bowling Centers": "Hospitality",
        "Hospitality_Brewery": "Hospitality",
        "Hospitality_Craft Beverage": "Hospitality",
        "Hospitality_Distillery": "Hospitality",
        "Hospitality_Fraternal Organizations": "Hospitality",
        "Hospitality_Lounges": "Hospitality",
        "Hospitality_Restaurants": "Hospitality",
        "Hospitality_Winery": "Hospitality",
        "Janitorial": "Main St",
        "Lounges": "Hospitality",
        "Mainstreet": "Main St",
        "Mainstreet_Bars and Taverns": "Main St",
        "Mainstreet_Cigar Stores": "Main St",
        "Mainstreet_Convenience Stores": "Main St",
        "Mainstreet_Distillery": "Main St",
        "Mainstreet_Gas Stations": "Main St",
        "Mainstreet_Lounges": "Main St",
        "Mainstreet_Restaurants": "Main St",
        "Mainstreet_Student Housing": "Main St",
        "Mainstreet_Tobacco Stores": "Main St",
        "Mainstreet_Vape Stores": "Main St",
        "MLBA": "Main St",
        "PvtInvestigators": "Main St",
        "RBT": "Hospitality",
        "Regular": None,  # No longer active
        "Repo": "Main St",
        "Restaurants": "Hospitality",
        "SecurityGuard": "Main St",
        "SpecialEventTHC": "THC",
        "Student Housing": "Main St",
        "StudentHousing": "Main St",
        "THC": "THC",
        "Tobacco Stores": "Main St",
        "Vape Stores": "Main St",
        "Winery": "Hospitality",
    },
    ConiferPolicyType.CUP: {
        "AlarmContractor": "Main St",
        "FireSuppression": "Main St",
        "Fraternal": "Hospitality",
        "Janitorial": "Main St",
        "PvtInvestigators": "Main St",
        "RBT": "Hospitality",
        "SecurityGuard": "Main St",
        "THC": "THC",
    },
    ConiferPolicyType.GL: {
        "REO": "Main St",
    },
    ConiferPolicyType.GPP: {
        "PhysDamMotorCarrier": "Main St",
    },
    ConiferPolicyType.LL: {
        "AFPD": "Hospitality",
        "Bars and Taverns": "Hospitality",
        "BarsandTaverns": "Hospitality",
        "Bowling": "Hospitality",
        "BowlingCenters": "Hospitality",
        "Brewery": "Hospitality",
        "Convenience Stores": "Main St",
        "ConvenienceStore": "Main St",
        "ConvenienceStores": "Main St",
        "Distillery": "Hospitality",
        "Fraternal": "Hospitality",
        "Fraternal Organizations": "Hospitality",
        "FraternalOrganizations": "Hospitality",
        "Gas Stations": "Main St",
        "GasStations": "Main St",
        "GentlemenClubs": "Hospitality",
        "GolfClubs": "Hospitality",
        "Hospitality_Bakeries": "Hospitality",
        "Hospitality_Bars and Taverns": "Hospitality",
        "Hospitality_Bowling Centers": "Hospitality",
        "Hospitality_Brewery": "Hospitality",
        "Hospitality_Craft Beverage": "Hospitality",
        "Hospitality_Fraternal Organizations": "Hospitality",
        "Hospitality_Liquor Liability Special Event": "Hospitality",
        "Hospitality_Lounges": "Hospitality",
        "Hospitality_Restaurants": "Hospitality",
        "Hospitality_Winery": "Hospitality",
        "Lounges": "Hospitality",
        "Mainstreet_Bars and Taverns": "Main St",
        "Mainstreet_Convenience Stores": "Main St",
        "Mainstreet_Gas Stations": "Main St",
        "Mainstreet_Restaurants": "Main St",
        "Mainstreet_Tobacco Stores": "Main St",
        "Mainstreet_Vape Stores": "Main St",
        "Mainstreet_Cigar Stores": "Main St",
        "Mainstreet_CraftBeverage": "Main St",
        "Mainstreet_Distillery": "Main St",
        "MLBA": "Hospitality",
        "RBT": "Hospitality",
        "Regular": None,  # No longer active
        "Restaurants": "Hospitality",
        "SpecialEventLiquor": "Hospitality",
        "Winery": "Hospitality",
        "Mainstreet_Lounges": "Main St",
        "Hospitality_Distillery": "Hospitality",
    },
    ConiferPolicyType.THC: {
        "SPECIALEVENT": "THC",
        "THC": "THC",
    },
    ConiferPolicyType.WC: {
        "AFC": "Hospitality",
        "AFPD": "Hospitality",
        "AlarmContractor": "Main St",
        "ArtisanContractor": "Main St",
        "Bowling": "Hospitality",
        "CBD": "THC",
        "CraftBeverage": "Hospitality",
        "Fraternal": "Hospitality",
        "Mainstreet": "Main St",
        "RBT": "Hospitality",
        "ScrapMetal": "Main St",
        "SecurityGuard": "Main St",
        "THC": "THC",
    },
}

# Coverages logic
# CA = "CA" -> businessAuto - PRIMARY
# NFAD = "NFAD" -> NOT SURE
# GPP = "GPP" -> garageDealers - PRIMARY
# GP = "GP" -> garageDealers - PRIMARY
# BOP = "BOP" -> businessOwners - PRIMARY
# CPP = "CPP"
# if packages are not listed maps to liability - PRIMARY and dont remove existing coverages
#   CA -> businessAuto - PRIMARY
#   CR -> crime - PRIMARY
#   DO -> fiduciaryLiability - PRIMARY (Directors and officers)
#   DW -> property - PRIMARY
#   GL -> liability - PRIMARY
#   GS -> NOT SURE
#   IM -> commercialInlandMarine - PRIMARY
#   PR -> property - PRIMARY
#
# LL = "LL" -> liquorLiability - PRIMARY
# WC = "WC" -> workersComp - PRIMARY
# GL = "GL" -> liability - PRIMARY
# CUP = "CUP" -> liability - EXCESS
# THC = "THC" ->
#   if packages are not listed maps to liability - PRIMARY and dont remove existing coverages
#   MP -> fiduciaryLiability - PRIMARY (Directors and officers)
#   GL -> liability - PRIMARY
#   ML -> NOT SURE
#   PL -> productLiability - PRIMARY
#   PR -> property - PRIMARY

PRIMARY_COVERAGE_MAPPINGS = {
    ConiferPolicyPackageMemberType.CA: ConiferCoverageMapping(
        coverage_name="businessAuto", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.CR: ConiferCoverageMapping(
        coverage_name="crime", coverage_type=CoverageType.PRIMARY
    ),
    # this is actually Directors and officers
    ConiferPolicyPackageMemberType.DO: ConiferCoverageMapping(
        coverage_name="fiduciaryLiability", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.DW: ConiferCoverageMapping(
        coverage_name="property", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.GL: ConiferCoverageMapping(
        coverage_name="liability", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.GS: None,  # glass, it seems like we are not getting this
    ConiferPolicyPackageMemberType.ML: ConiferCoverageMapping(
        coverage_name="cannabisLiability", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.IM: ConiferCoverageMapping(
        coverage_name="commercialInlandMarine", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.MP: ConiferCoverageMapping(
        coverage_name="fiduciaryLiability", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.PL: ConiferCoverageMapping(
        coverage_name="productLiability", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.PR: ConiferCoverageMapping(
        coverage_name="property", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.NFAD: None,  # Special case, Will be mapped by the packages
    ConiferPolicyPackageMemberType.GPP: ConiferCoverageMapping(
        coverage_name="garageDealers", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.GP: ConiferCoverageMapping(
        coverage_name="garageDealers", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.BOP: ConiferCoverageMapping(
        coverage_name="businessOwners", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.CPP: ConiferCoverageMapping(
        coverage_name="liability", coverage_type=CoverageType.PRIMARY, keep_other_coverage=True
    ),
    ConiferPolicyPackageMemberType.LL: ConiferCoverageMapping(
        coverage_name="liquorLiability", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.WC: ConiferCoverageMapping(
        coverage_name="workersComp", coverage_type=CoverageType.PRIMARY
    ),
    ConiferPolicyPackageMemberType.CUP: ConiferCoverageMapping(
        coverage_name="liability", coverage_type=CoverageType.EXCESS
    ),
    ConiferPolicyPackageMemberType.THC: ConiferCoverageMapping(
        coverage_name="liability", coverage_type=CoverageType.PRIMARY, keep_other_coverage=True
    ),
    ConiferPolicyPackageMemberType.OT: ConiferCoverageMapping(
        coverage_name="policyPremium", coverage_type=CoverageType.PRIMARY
    ),
}

SECONDARY_COVERAGE_MAPPINGS: dict[ConiferPolicyType, list[ConiferCoverageMapping]] = {
    ConiferPolicyType.CA: [ConiferCoverageMapping(coverage_name="businessAuto", coverage_type=CoverageType.PRIMARY)],
    ConiferPolicyType.NFAD: [
        ConiferCoverageMapping(coverage_name="liability", coverage_type=CoverageType.PRIMARY),
        ConiferCoverageMapping(coverage_name="businessAuto", coverage_type=CoverageType.PRIMARY),
        ConiferCoverageMapping(coverage_name="property", coverage_type=CoverageType.PRIMARY),
    ],
    ConiferPolicyType.GPP: [ConiferCoverageMapping(coverage_name="garageDealers", coverage_type=CoverageType.PRIMARY)],
    ConiferPolicyType.GP: [ConiferCoverageMapping(coverage_name="garageDealers", coverage_type=CoverageType.PRIMARY)],
    ConiferPolicyType.BOP: [ConiferCoverageMapping(coverage_name="businessOwners", coverage_type=CoverageType.PRIMARY)],
    ConiferPolicyType.CPP: [
        ConiferCoverageMapping(coverage_name="liability", coverage_type=CoverageType.PRIMARY, keep_other_coverage=True)
    ],
    ConiferPolicyType.LL: [ConiferCoverageMapping(coverage_name="liquorLiability", coverage_type=CoverageType.PRIMARY)],
    ConiferPolicyType.WC: [ConiferCoverageMapping(coverage_name="workersComp", coverage_type=CoverageType.PRIMARY)],
    ConiferPolicyType.GL: [ConiferCoverageMapping(coverage_name="liability", coverage_type=CoverageType.PRIMARY)],
    ConiferPolicyType.CUP: [ConiferCoverageMapping(coverage_name="liability", coverage_type=CoverageType.EXCESS)],
    ConiferPolicyType.THC: [
        ConiferCoverageMapping(coverage_name="liability", coverage_type=CoverageType.PRIMARY, keep_other_coverage=True)
    ],
}


@dataclass
class ConiferPolicyPackageMember:
    package_member_type: ConiferPolicyPackageMemberType
    premium: float | None


@dataclass
class ConiferExportRow:
    submission_number: str
    quote_number: str | None
    policy_number: str | None
    policy_effective: datetime
    insured_name: str | None
    dba_name: str | None
    insured_address: str | None
    issuance_status: ConiferIssuanceStatus
    underwriting_status: ConiferUnderwritingStatus | None
    underwriting_status_notes: str | None
    policy_type: ConiferPolicyType
    carrier: str | None
    rating_state: str | None
    underwriting_program: str | None
    new_renewal: ConiferNewOrRenewal
    term_sequence: int | None
    assigned_uw: str | None
    quote_created_date: datetime | None
    quote_effective_date: datetime | None
    quote_updated_date: datetime
    bound_date: datetime | None
    quoted_premium: float
    bound_premium: float
    producer: str | None
    producer_email: str | None
    producer_name: str | None
    sub_producer: str | None
    sub_producer_email: str | None
    sub_producer_name: str | None
    bound_policy_status: ConiferBoundPolicyStatus | None
    coverages: list[ConiferPolicyPackageMember] | None
    gross_premium: float | None
    underwriter: str | None
    prior_renewal_policy_number: str | None

    @property
    def should_skip(self) -> bool:
        if not self.insured_name:
            return True

        if self.new_renewal not in ConiferNewOrRenewal.valid_values():
            return True

        if self.bound_policy_status in {
            ConiferBoundPolicyStatus.RESCINDED,
        }:
            return True

        if self.issuance_status in {
            ConiferIssuanceStatus.CANCEL,
        }:
            return True

        if self.issuance_status in {
            ConiferIssuanceStatus.PENDING,
            ConiferIssuanceStatus.PENDING_REFERRAL,
        } and self.underwriting_status in {
            None,
            ConiferUnderwritingStatus.ALTERNATE,
            ConiferUnderwritingStatus.DUPLICATE_QUOTE,
            ConiferUnderwritingStatus.SETUP_IN_PROCESS,
        }:
            return True

        return False

    @property
    def is_renewal(self) -> bool:
        return self.new_renewal in {
            ConiferNewOrRenewal.REN,
            ConiferNewOrRenewal.BATCH_RENEW,
            ConiferNewOrRenewal.BATCH_PRE_RENEW,
        }

    @property
    def received_date(self) -> datetime:
        return self.effective_date - timedelta(days=15)

    @property
    def effective_date(self) -> datetime:
        return self.policy_effective or self.quote_effective_date

    @property
    def submission_name(self) -> str:
        name = self.insured_name
        if self.dba_name:
            name = name + " dba " + self.dba_name
        return name.replace("%", "")

    @property
    def copilot_stage(self):
        keys = [
            self.issuance_status,
            self.underwriting_status,
        ]
        result = COPILOT_STATUS_MAPPING
        for key in keys:
            result = result.get(key)
            if not result:
                raise ValueError(
                    f"Cannot find mapping for ConiferRow[submission_number={self.submission_number}]: {keys}"
                )
            if isinstance(result, SubmissionStage):
                return result

        raise ValueError(
            f"Iterated over mappings and cannot find mapping for "
            f"ConiferRow[submission_number={self.submission_number}]: {keys}"
        )

    @property
    def submission_client_id(self) -> str:
        # because we are receiving a transaction log, we have to generate the client_id ourselves
        # for now it will be a hash of
        # insured_name + policy_effective + policy_type + producer_name
        string_to_hash = f"{self.insured_name.lower()}-{self.policy_effective}-{self.policy_type}-{self.producer_name}"
        md5_hash = hashlib.md5(string_to_hash.encode()).hexdigest()
        return f"DERIVED-{md5_hash}"

    @property
    def submission_matcher_data(self) -> SubmissionSyncMatcherData | None:
        address = try_parse_address_string(self.insured_address)
        if not address:
            return None
        return SubmissionSyncMatcherData(
            named_insured=self.insured_name,
            named_insured_address=self.insured_address,
            additional_data=frozendict(
                {
                    "address_line_1": address.address_line_1,
                    "address_line_2": address.address_line_2,
                    "city": address.city,
                    "state": address.state,
                    "postal_code": address.zip_code,
                }
            ),
        )

    @property
    def primary_coverages(self) -> list[SubmissionSyncCoverage]:
        if not self.coverages or self.policy_type == ConiferPolicyType.NFAD:
            return []

        # it can happen that the package different coverages that map to the same coverage in Copilot
        # in this case we want to sum the premiums
        name_type_to_coverage: dict[str, SubmissionSyncCoverage] = {}
        for cov in self.coverages:
            if cov.package_member_type not in PRIMARY_COVERAGE_MAPPINGS:
                raise ValueError(f"Policy type {self.policy_type} not found in PRIMARY_COVERAGE_MAPPINGS")

            val = PRIMARY_COVERAGE_MAPPINGS.get(cov.package_member_type)
            if val is None:
                continue

            key = f"{val.coverage_name}-{val.coverage_type}"
            coverage_value = name_type_to_coverage.get(
                key,
                SubmissionSyncCoverage(
                    coverage_name=val.coverage_name,
                    coverage_type=val.coverage_type,
                    keep_other_coverage=val.keep_other_coverage,
                ),
            )

            # we do not want to set the premium to 0
            quoted_premium = (
                cov.premium
                if cov.premium and self.copilot_stage in {SubmissionStage.QUOTED, SubmissionStage.QUOTED_LOST}
                else None
            )
            bound_premium = cov.premium if cov.premium and self.copilot_stage == SubmissionStage.QUOTED_BOUND else None
            if quoted_premium:
                coverage_value.quoted_premium = (coverage_value.quoted_premium or 0) + quoted_premium
            if bound_premium:
                coverage_value.bound_premium = (coverage_value.bound_premium or 0) + bound_premium
            name_type_to_coverage[key] = coverage_value
        return list(name_type_to_coverage.values())

    @property
    def secondary_coverages(self) -> list[SubmissionSyncCoverage]:
        if self.policy_type not in SECONDARY_COVERAGE_MAPPINGS:
            raise ValueError(f"Policy type {self.policy_type} not found in SECONDARY_COVERAGE_MAPPINGS")

        values = SECONDARY_COVERAGE_MAPPINGS.get(self.policy_type)
        if values is None:
            return []

        quoted_prem = self.quoted_premium if self.quoted_premium else None  # we do not want to set the premium to 0
        bound_prem = self.bound_premium if self.bound_premium else None  # we do not want to set the premium to 0

        coverages = [
            SubmissionSyncCoverage(
                coverage_name=val.coverage_name,
                coverage_type=val.coverage_type,
                keep_other_coverage=val.keep_other_coverage,
            )
            for val in values
        ]

        coverages[0].quoted_premium = quoted_prem
        coverages[0].bound_premium = bound_prem
        return coverages

    @property
    def sync_coverages(self) -> list[SubmissionSyncCoverage]:
        return self.primary_coverages or self.secondary_coverages

    @property
    def normalized_uw(self) -> str | None:
        # do not sync UWs for THC
        if self.sync_report and self.sync_report.org_group == "THC":
            return None
        # we got "tc" as an UW name in the export. Ignore for now
        if self.underwriter and self.underwriter.strip().lower() == "st":
            return None
        return camel_to_normal(self.underwriter)

    @property
    def sync_report(self) -> SubmissionSyncReport | None:
        if self.policy_type not in COPILOT_ORG_GROUP_MAPPING:
            logger.error("Policy type not found in COPILOT_ORG_GROUP_MAPPING", policy_type=self.policy_type)
            return None

        uw_program_map = COPILOT_ORG_GROUP_MAPPING[self.policy_type]
        if self.underwriting_program not in uw_program_map:
            logger.error(
                "Underwriting program not found in COPILOT_ORG_GROUP_MAPPING",
                submission_number=self.submission_number,
                policy_type=self.policy_type,
                underwriting_program=self.underwriting_program,
            )
            return None

        org_group = COPILOT_ORG_GROUP_MAPPING[self.policy_type].get(self.underwriting_program)
        if not org_group:
            return None

        return SubmissionSyncReport(org_group=org_group)

    @property
    def policy_status(self) -> str | None:
        if self.issuance_status not in {ConiferIssuanceStatus.SUBMITTED}:
            return None
        if self.bound_policy_status is None:
            return "Active"
        return self.bound_policy_status.value.capitalize()


class ConiferIdMapping(BaseModel):
    __tablename__ = "conifer_id_mappings"

    submission_client_id = Column(String, nullable=False, index=True)
    quote_number = Column(String, nullable=False, index=True)

    def __repr__(self):
        return (
            f"<ConiferIdMapping id={self.id} quote_number={self.quote_number} "
            f"submission_client_id={self.submission_client_id}>"
        )
