from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from copilot_client_v3 import SubmissionSyncMatcherData
from dataclasses_json import dataclass_json
from frozendict import frozendict
from static_common.enums.enum import StrEnum
from static_common.enums.submission import SubmissionStage

from src.models.types import CoverageType


class ArchStatus(StrEnum):
    BOUND = "Bound"
    CLEARED = "Cleared"
    DECLINED = "Declined"
    QUOTE_NOT_TAKEN = "Quote Not Taken"
    QUOTED = "Quoted"
    STALE = "Stale"
    WITHDRAWN = "Withdrawn"
    CONFLICT = "Conflict"
    INDICATED = "Indicated"
    INDICATION_NOT_TAKEN = "Indication Not Taken"
    BLOCKED = "Blocked"

    @property
    def copilot_stage(self) -> str:
        return {
            ArchStatus.BOUND: SubmissionStage.QUOTED_BOUND,
            ArchStatus.CLEARED: SubmissionStage.ON_MY_PLATE,
            ArchStatus.DECLINED: SubmissionStage.DECLINED,
            ArchStatus.QUOTE_NOT_TAKEN: SubmissionStage.QUOTED_LOST,
            ArchStatus.QUOTED: SubmissionStage.QUOTED,
            ArchStatus.STALE: SubmissionStage.EXPIRED,
            ArchStatus.WITHDRAWN: SubmissionStage.ON_MY_PLATE,
            ArchStatus.CONFLICT: SubmissionStage.ON_MY_PLATE,
            ArchStatus.INDICATED: SubmissionStage.INDICATED,
            ArchStatus.INDICATION_NOT_TAKEN: SubmissionStage.QUOTED_LOST,
            ArchStatus.BLOCKED: SubmissionStage.BLOCKED,
        }[self]


class ArchRenewalType(StrEnum):
    NEW = "New"
    RENEW = "Renewal"

    def is_renewal(self) -> bool:
        return self == ArchRenewalType.RENEW


class ArchProductFamily(StrEnum):
    GENERAL_LIABILITY = "General Liability Primary"
    OWNERS_AND_CONTRACTORS = "Owners & Contractor's Protective"
    EXCESS_UMBRELLA = "Excess / Umbrella"
    COMMERCIAL_AUTO = "Commercial Auto"
    RAILROAD_EXCESS = "Railroad Excess"


@dataclass_json
@dataclass
class ArchClearanceReportRow:
    policy_number: str
    submission_name: str
    status: ArchStatus
    business_division: str
    sub_business_division: str
    created_date: datetime
    new_or_renew: ArchRenewalType
    underwriter_name: str
    effective_date: Optional[datetime]
    priority: Optional[str]
    account_name: str
    account_street_1: str
    account_street_2: Optional[str]
    account_city: str
    account_state: str
    account_zip_code: str
    account_country: str
    producer: str
    producer_city: str
    producer_state: str
    producer_contact_name: str
    product_family: Optional[ArchProductFamily] = None
    account_id: Optional[str] = None
    date_time_changed: Optional[datetime] = None
    submission_received_date: Optional[datetime] = None
    renewal_of_submission_number: Optional[str] = None

    @property
    def coverage_name(self) -> Optional[str]:
        if self.product_family == ArchProductFamily.EXCESS_UMBRELLA:
            return "liability"
        if self.product_family == ArchProductFamily.GENERAL_LIABILITY:
            return "liability"
        if self.product_family == ArchProductFamily.RAILROAD_EXCESS:
            return "liability"
        if self.product_family == ArchProductFamily.COMMERCIAL_AUTO:
            return "businessAuto"
        return None

    @property
    def coverage_type(self) -> Optional[CoverageType]:
        if self.product_family == ArchProductFamily.EXCESS_UMBRELLA:
            return CoverageType.EXCESS
        if self.product_family == ArchProductFamily.GENERAL_LIABILITY:
            return CoverageType.PRIMARY
        if self.product_family == ArchProductFamily.RAILROAD_EXCESS:
            return CoverageType.EXCESS
        if self.product_family == ArchProductFamily.COMMERCIAL_AUTO:
            return CoverageType.PRIMARY
        return None

    @property
    def matcher_data(self) -> SubmissionSyncMatcherData | None:
        street2 = self.account_street_2 or ""
        if self.account_country == "United States":
            address = (
                f"{self.account_street_1}, {street2}, {self.account_city}, {self.account_state} {self.account_zip_code}"
            )
            return SubmissionSyncMatcherData(
                self.account_name,
                address,
                additional_data=frozendict(
                    {
                        "address_line_1": self.account_street_1,
                        "address_line_2": self.account_street_2,
                        "city": self.account_city,
                        "state": self.account_state,
                        "postal_code": self.account_zip_code,
                    }
                ),
            )
        return None
