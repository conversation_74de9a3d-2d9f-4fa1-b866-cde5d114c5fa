from typing import Sequence
from uuid import UUID

from facts_client.model.bond import Bond
from facts_client.model.insurance import Insurance
from facts_client.model.insurance_information_observation import (
    InsuranceInformationObservation,
)
from facts_client.model.source import Source
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID
from static_common.enums.parent import ParentType

from src.models.buildzoom import BuildZoomBondInfo, BuildZoomInsuranceInfo
from src.models.ca_cslb import LicensePage


class InsuranceInformationObservationFactory:
    @classmethod
    def create(
        cls,
        business_id: UUID,
        insurance_info: Sequence[BuildZoomInsuranceInfo],
        bond_info: Sequence[BuildZoomBondInfo],
        source: Source,
    ) -> InsuranceInformationObservation:
        insurances = []
        if insurance_info:
            for insurance in insurance_info:
                insurances.append(
                    Insurance(insurer=insurance.insurer, amount=insurance.amount, insurance_type=insurance.type)
                )
        bonds = []
        for bond in bond_info:
            bonds.append(Bond(agent=bond.agent, value=bond.value))
        return InsuranceInformationObservation(
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            fact_type_id=FactTypeID.INSURANCE_INFORMATION.value,
            fact_subtype_id=FactSubtypeID.INSURANCE_INFORMATION.value,
            insurances=insurances,
            bonds=bonds,
            published_at=None,
            source=source,
        )

    @classmethod
    def create_from_ca_cslb(
        cls, business_id: UUID, license_page: LicensePage, source: Source
    ) -> InsuranceInformationObservation:
        insurances = []
        if license_page.workers_comp_information and license_page.workers_comp_information.insurance_company:
            insurances = [
                Insurance(
                    insurer=license_page.workers_comp_information.insurance_company,
                    amount=None,
                    insurance_type="Workers Comp",
                )
            ]
        bonds = []
        if license_page.bond_information.bond_agent:
            bonds = [
                Bond(agent=license_page.bond_information.bond_agent, value=license_page.bond_information.bond_amount)
            ]
        return InsuranceInformationObservation(
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            fact_type_id=FactTypeID.INSURANCE_INFORMATION.value,
            fact_subtype_id=FactSubtypeID.INSURANCE_INFORMATION.value,
            insurances=insurances,
            bonds=bonds,
            published_at=None,
            source=source,
        )
