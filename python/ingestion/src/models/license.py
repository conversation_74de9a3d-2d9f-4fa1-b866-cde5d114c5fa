import datetime
from enum import Enum
from typing import List, Optional, Sequence
from uuid import UUID

from common.utils.collections import filter_none
from common.utils.documents import identify_document
from common.utils.liquor_licenses import generate_liquor_license_uuid
from dateutil.parser import parse
from facts_client.model.license import License
from facts_client.model.source import Source
from facts_client.model.web_ingested_source import WebIngestedSource
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.license_type import LicenseType
from static_common.enums.parent import ParentType
from static_common.enums.states import USLegalStatesEnum
from static_common.models.yelp import YelpLicense

from src.models.abc_ca import AbcCaLiquorLicense
from src.models.atf.atf_firearm import FirearmLicense
from src.models.better_business_bureau import BetterBusinessBureauLicense
from src.models.buildzoom import BuildZoomLicense
from src.models.ca_cslb import LicensePage
from src.models.fmcsa import FMCSALicenseTypeDescriptionEnum, FMCSALicenseTypesEnum
from src.models.fmcsa import License as FMCSALicense
from src.models.liquor_licence.az_liquor_licence import AZLiquorLicense
from src.models.liquor_licence.co_liquor_licence import COLiquorLicense
from src.models.liquor_licence.fl_liquor_licence import FLLiquorLicense
from src.models.liquor_licence.il_liquor_licence import ILLiquorLicense
from src.models.liquor_licence.licence_interface import LiquorLicense
from src.models.liquor_licence.ma_liquor_licence import MALiquorLicense
from src.models.liquor_licence.mi_cra_thc_liquor_licence import MICraThcLiquorLicense
from src.models.liquor_licence.mi_liquor_licence import MILiquorLicense
from src.models.liquor_licence.mi_mlcc_liquor_licence import MIMLCCLiquorLicense
from src.models.liquor_licence.mn_liquor_licence import MNLiquorLicense
from src.models.liquor_licence.mo_liquor_licence import MOLiquorLicense
from src.models.liquor_licence.nj_liquor_licence import NJLiquorLicense
from src.models.liquor_licence.ny_liquor_licence import NYLiquorLicense
from src.models.liquor_licence.pa_liquor_licence import PALiquorLicense
from src.models.liquor_licence.sc_liquor_licence import SCLiquorLicense
from src.models.liquor_licence.tx_liquor_licence import TXLiquorLicense
from src.models.liquor_licence.va_liquor_licence import VALiquorLicense


class LicenseStatus(Enum):
    UNABLE_TO_VERIFY = "Unable To Verify"
    ACTIVE = "Active"
    EXPIRED = "Expired"
    INACTIVE = "Inactive"
    PENDING = "Pending"
    SUSPENDED = "Suspended"
    ISSUE_AND_HOLD = "Issue And Hold"
    REVOCATION_PENDING = "Revocation Pending"
    SURRENDERED = "Surrendered"


class LicenseFactory:
    @staticmethod
    def create_buildzoom_body(license_type: Optional[Sequence[str]], business_type: Optional[str]) -> Optional[str]:
        body = None
        if license_type:
            body = " ".join(license_type)

        if business_type:
            if not body:
                body = business_type
            else:
                body += f" {business_type}"

        if not body:
            body = "Buildzoom sourced license"

        return body

    @staticmethod
    def create_ca_clsb_body(license_page: LicensePage) -> Optional[str]:
        body = None
        if license_page.business_information.license_classifications:
            body = "License Classifications are " + ", ".join(license_page.business_information.license_classifications)

        if not body:
            body = "California Constructors State License Board sourced license"

        return body

    @staticmethod
    def create_bbb_body(bbb_license: BetterBusinessBureauLicense) -> str:
        body = "License sourced from Better Business Bureau."
        if bbb_license.notes:
            body += f" The type of the license is {bbb_license.notes.lower()}."
        if bbb_license.license_number:
            body += f" The number of the license is {bbb_license.license_number}."
        if bbb_license.license_agency and bbb_license.license_agency.name:
            body += f" It was issued by {bbb_license.license_agency.name}."
        if bbb_license.expiration_date:
            body += f" The expiry date is {parse(bbb_license.expiration_date).date()}."
        return body

    @staticmethod
    def create_fmcsa_body(fmcsa_license: FMCSALicense, license_type: FMCSALicenseTypesEnum) -> str:
        body = "License is sourced from Federal Motor Carrier Safety Administration."
        body += " " + FMCSALicenseTypeDescriptionEnum[license_type.name].value
        if fmcsa_license.number:
            body += f" The number of the license is {fmcsa_license.number}."
        if fmcsa_license.active is not None:
            if fmcsa_license.active:
                body += " It is active."
            else:
                body += " It is inactive."
        return body

    @staticmethod
    def create_alcohol_license_body(department_name: str, license: LiquorLicense, license_type: Optional[str]) -> str:
        body = f"{department_name} " f"License # {license.license_number} " f"{license_type if license_type else ''}"

        if license.owner:
            body = f"{body} issued for {license.owner}"

        body = f"Alcohol permit - {'Active ' if license.is_active else 'Inactive '} {body}"
        if license.issue_date:
            body = f"{body} issued on {license.issue_date.strftime('%Y-%m-%d')}"
        if license.expiration_date:
            body = f"{body} expires on {license.expiration_date.strftime('%Y-%m-%d')}"

        return body

    def create_from_buildzoom(
        self, business_id: UUID, buildzoom_license: BuildZoomLicense, source: Source
    ) -> Optional[License]:
        if not buildzoom_license.status or buildzoom_license.status.lower().strip() != "not required":
            date_issued = buildzoom_license.get_parsed_date_issue()
            if body := self.create_buildzoom_body(buildzoom_license.type, buildzoom_license.business_type):
                id = identify_document(
                    DocumentTypeID.LICENSE, ParentType.BUSINESS, business_id, buildzoom_license.number
                )
                return License(
                    id=str(id),
                    document_type_id=DocumentTypeID.LICENSE.value,
                    status=buildzoom_license.status,
                    county=buildzoom_license.county,
                    state=buildzoom_license.state,
                    types=buildzoom_license.type,
                    business_type=buildzoom_license.business_type,
                    date_issued=date_issued if date_issued else None,
                    expiration_date=None,
                    number=buildzoom_license.number,
                    is_verified=buildzoom_license.is_verified,
                    parent_id=str(business_id),
                    parent_type=ParentType.BUSINESS.value,
                    body=body,
                    source=source,
                    issued_by=buildzoom_license.issued_by,
                    verify_link=buildzoom_license.verify_link,
                )

    def create_from_ca_cslb(self, business_id: UUID, license_page: LicensePage, source: Source) -> Optional[License]:
        body = self.create_ca_clsb_body(license_page)
        id_ = identify_document(
            DocumentTypeID.LICENSE, ParentType.BUSINESS, business_id, license_page.contractor_data.license_number
        )
        return License(
            id=str(id_),
            document_type_id=DocumentTypeID.LICENSE.value,
            status=license_page.get_license_status(),
            county="US",
            state="California",
            types=license_page.business_information.license_classifications,
            business_type=license_page.business_information.entity_type,
            date_issued=license_page.business_information.license_issue_date_parsed,
            expiration_date=license_page.business_information.license_expiration_date_parsed,
            number=license_page.contractor_data.license_number,
            is_verified=True,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            issued_by="California Contractors State License Board",
            verify_link=license_page.url,
        )

    def create_many_from_buildzoom(
        self, business_id: UUID, buildzoom_licenses: Sequence[BuildZoomLicense], source: Source
    ) -> List[License]:
        return filter_none([self.create_from_buildzoom(business_id, lx, source) for lx in buildzoom_licenses])

    def create_from_bbb(
        self, business_id: UUID, bbb_license: BetterBusinessBureauLicense, source: WebIngestedSource
    ) -> License:
        if bbb_license.license_number:
            id = identify_document(DocumentTypeID.LICENSE, ParentType.BUSINESS, business_id, bbb_license.license_number)
            status = LicenseStatus.UNABLE_TO_VERIFY
            expiration_date = parse(bbb_license.expiration_date) if bbb_license.expiration_date else None
            if expiration_date:
                status = LicenseStatus.ACTIVE if expiration_date > datetime.datetime.utcnow() else LicenseStatus.EXPIRED
            body = self.create_bbb_body(bbb_license=bbb_license)

            return License(
                id=str(id),
                document_type_id=DocumentTypeID.LICENSE.value,
                status=status.value,
                county=None,
                types=[bbb_license.notes] if bbb_license.notes else [],
                state=bbb_license.license_agency.state if bbb_license.license_agency else None,
                expiration_date=expiration_date,
                number=bbb_license.license_number,
                parent_id=str(business_id),
                parent_type=ParentType.BUSINESS.value,
                body=body,
                source=source,
                issued_by=bbb_license.license_agency.name if bbb_license.license_agency else None,
                verify_link=bbb_license.license_agency.url if bbb_license.license_agency else None,
            )

    def create_many_from_bbb(
        self, business_id: UUID, bbb_licenses: Sequence[BetterBusinessBureauLicense], source: WebIngestedSource
    ) -> List[License]:
        return filter_none([self.create_from_bbb(business_id, lx, source) for lx in bbb_licenses])

    @staticmethod
    def create_from_atf(business_id: UUID, atf_license: FirearmLicense, source: Source) -> License:
        id = identify_document(DocumentTypeID.LICENSE, ParentType.BUSINESS, business_id, atf_license.ffl_number)
        status = LicenseStatus.UNABLE_TO_VERIFY
        if expiration_date := atf_license.expiration_date:
            status = LicenseStatus.ACTIVE if expiration_date > datetime.datetime.utcnow() else LicenseStatus.EXPIRED

        return License(
            id=str(id),
            document_type_id=DocumentTypeID.LICENSE.value,
            county=str(atf_license.county),
            state=atf_license.state_long_name,
            types=atf_license.types,
            business_type=atf_license.business_type,
            expiration_date=atf_license.expiration_date,
            number=atf_license.ffl_number,
            status=status.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=atf_license.body,
            source=source,
            published_at=atf_license.published_at,
            issued_by=atf_license.issued_by,
            is_verified=True,
            verify_link=atf_license.verification_link,
        )

    def create_from_fmcsa(
        self, business_id: UUID, fmcsa_license: FMCSALicense, source: Source, license_type: FMCSALicenseTypesEnum
    ) -> License:
        body = self.create_fmcsa_body(fmcsa_license, license_type)
        id = identify_document(
            DocumentTypeID.LICENSE, ParentType.BUSINESS, business_id, f"{license_type.name}={fmcsa_license.number}"
        )
        return License(
            id=str(id),
            document_type_id=DocumentTypeID.LICENSE.value,
            types=[license_type.value.title()],
            number=fmcsa_license.number,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            status=LicenseStatus.ACTIVE.value if fmcsa_license.active else LicenseStatus.INACTIVE.value,
            body=body,
            source=source,
            is_verified=True,
        )

    @staticmethod
    def create_from_yelp(business_id: UUID, yelp_license: YelpLicense, source: Source) -> License:
        yelp_license_identifier = f"Yelp License # {yelp_license.license_number}"
        if yelp_license.issuing_authority:
            yelp_license_identifier += f" issued_by {yelp_license.issuing_authority}"

        id_ = identify_document(DocumentTypeID.LICENSE, ParentType.BUSINESS, business_id, yelp_license_identifier)
        body = f"{yelp_license.license_number} type: {yelp_license.trade} issued by: {yelp_license.issuing_authority}"

        published_at = None
        if yelp_license.verification_date:
            published_at = yelp_license.verification_date.strftime("%Y-%m-%dT00:00:00Z")

        return License(
            id=str(id_),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            published_at=published_at,
            number=yelp_license.license_number,
            types=[yelp_license.trade],
            status=LicenseStatus.ACTIVE.value,
            issued_by=yelp_license.issuing_authority,
            is_verified=True,
        )

    @staticmethod
    def create_from_abc_ca(business_id: UUID, abc_ca_licence: AbcCaLiquorLicense, source: WebIngestedSource) -> License:
        body = f"Alcohol permit - ABC CA license #{abc_ca_licence.license_number} - {abc_ca_licence.license_types}"

        if abc_ca_licence.business_name:
            body = f"{body} issued for {abc_ca_licence.business_name}"

            if abc_ca_licence.premises_address:
                body = f"{body} {abc_ca_licence.premises_address}"

        if abc_ca_licence.issue_date:
            body = f"{body} issued on {abc_ca_licence.issue_date.strftime('%Y-%m-%d')}"
        if abc_ca_licence.expiration_date:
            body = f"{body} expires on {abc_ca_licence.expiration_date.strftime('%Y-%m-%d')}"

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.CA.name,
                    license_types=[abc_ca_licence.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            license_type=LicenseType.LIQUOR_LICENSE,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            published_at=str(abc_ca_licence.issue_date) if abc_ca_licence.issue_date else None,
            number=abc_ca_licence.license_number,
            types=abc_ca_licence.get_license_type_names(),
            status=(
                abc_ca_licence.status.to_license_status().value if abc_ca_licence.status.to_license_status() else None
            ),
            issued_by="California Department of Alcoholic Beverage Control",
            is_verified=True,
            verify_link=abc_ca_licence.source_url,
            date_issued=abc_ca_licence.issue_date,
            expiration_date=abc_ca_licence.expiration_date,
            state=USLegalStatesEnum.CA.name,
        )

    @staticmethod
    def create_from_ny(business_id: UUID, ny_license: NYLiquorLicense, source: WebIngestedSource) -> License:
        body = f"New York State Liquor Authority License # {ny_license.license_number} - {ny_license.description}"

        if ny_license.business_name:
            body = f"{body} issued for {ny_license.business_name}"

        body = f"Alcohol permit - {'Active ' if ny_license.is_active else 'Inactive '} {body}"
        if ny_license.issue_date:
            body = f"{body} issued on {ny_license.issue_date.strftime('%Y-%m-%d')}"
        if ny_license.expiration_date:
            body = f"{body} expires on {ny_license.expiration_date.strftime('%Y-%m-%d')}"

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.NY.name,
                    license_types=[ny_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            license_type=LicenseType.LIQUOR_LICENSE,
            body=body,
            source=source,
            published_at=str(ny_license.issue_date) if ny_license.issue_date else None,
            number=ny_license.license_number,
            types=[ny_license.description] if ny_license.description else [],
            status=LicenseStatus.ACTIVE.value if ny_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by="New York State Liquor Authority",
            is_verified=True,
            verify_link=source.url,
            date_issued=ny_license.issue_date,
            expiration_date=ny_license.expiration_date,
            state=USLegalStatesEnum.NY.name,
        )

    @staticmethod
    def create_from_tx(business_id: UUID, tx_license: TXLiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Texas Alcoholic Beverage Commission"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=tx_license,
            license_type=tx_license.full_license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.TX.name,
                    license_types=[tx_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            license_type=LicenseType.LIQUOR_LICENSE,
            body=body,
            source=source,
            published_at=str(tx_license.issue_date) if tx_license.issue_date else None,
            number=tx_license.license_number,
            types=[tx_license.full_license_type],
            status=LicenseStatus.ACTIVE.value if tx_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=tx_license.issue_date,
            expiration_date=tx_license.expiration_date,
            state=USLegalStatesEnum.TX.name,
        )

    @staticmethod
    def create_from_il(business_id: UUID, il_license: ILLiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Illinois Liquor Control Commission"
        license_type = (
            f"{il_license.business_type if il_license.business_type else ''} "
            f"{il_license.retail_type if il_license.retail_type else ''}"
        ).strip()
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=il_license,
            license_type=license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.IL.name,
                    license_types=[il_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            license_type=LicenseType.LIQUOR_LICENSE,
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            published_at=str(il_license.issue_date) if il_license.issue_date else None,
            number=il_license.license_number,
            types=[il_license.retail_type] if il_license.retail_type else [],
            status=LicenseStatus.ACTIVE.value if il_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=il_license.issue_date,
            expiration_date=il_license.expiration_date,
            state=USLegalStatesEnum.IL.name,
        )

    @staticmethod
    def create_from_fl(business_id: UUID, fl_license: FLLiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Florida Department of Business and Professional Regulation"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=fl_license,
            license_type=fl_license.full_license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.FL.name,
                    license_types=[fl_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            license_type=LicenseType.LIQUOR_LICENSE,
            source=source,
            published_at=str(fl_license.issue_date) if fl_license.issue_date else None,
            number=fl_license.license_number,
            types=[fl_license.full_license_type] if fl_license.full_license_type else [],
            status=LicenseStatus.ACTIVE.value if fl_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=fl_license.issue_date,
            expiration_date=fl_license.expiration_date,
            state=USLegalStatesEnum.FL.name,
        )

    @staticmethod
    def create_from_pa(business_id: UUID, pa_license: PALiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Pennsylvania Liquor Control Board"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=pa_license,
            license_type=pa_license.license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.PA.name,
                    license_types=[pa_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            license_type=LicenseType.LIQUOR_LICENSE,
            source=source,
            published_at=str(pa_license.issue_date) if pa_license.issue_date else None,
            number=pa_license.license_number,
            types=[pa_license.license_type] if pa_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if pa_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=pa_license.issue_date,
            expiration_date=pa_license.expiration_date,
            state=USLegalStatesEnum.PA.name,
        )

    @staticmethod
    def create_from_az(business_id: UUID, az_license: AZLiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Arizona Department of Liquor Licenses and Control"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=az_license,
            license_type=az_license.full_license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.AZ.name,
                    license_types=[az_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            license_type=LicenseType.LIQUOR_LICENSE,
            source=source,
            published_at=str(az_license.issue_date) if az_license.issue_date else None,
            number=az_license.license_number,
            types=[az_license.full_license_type] if az_license.full_license_type else [],
            status=LicenseStatus.ACTIVE.value if az_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=az_license.issue_date,
            expiration_date=az_license.expiration_date,
            state=USLegalStatesEnum.AZ.name,
        )

    @staticmethod
    def create_from_nj(business_id: UUID, nj_license: NJLiquorLicense, source: WebIngestedSource) -> License:
        department_name = "New Jersey Division of Alcoholic Beverage Control"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=nj_license,
            license_type=nj_license.license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.NJ.name,
                    license_types=[nj_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            license_type=LicenseType.LIQUOR_LICENSE,
            published_at=str(nj_license.issue_date) if nj_license.issue_date else None,
            number=nj_license.license_number,
            types=[nj_license.license_type] if nj_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if nj_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=nj_license.issue_date,
            expiration_date=nj_license.expiration_date,
            state=USLegalStatesEnum.NJ.name,
        )

    @staticmethod
    def create_from_mn(business_id: UUID, mn_license: MNLiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Minnesota Department of Public Safety"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=mn_license,
            license_type=mn_license.full_license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.MN.name,
                    license_types=[mn_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            license_type=LicenseType.LIQUOR_LICENSE,
            published_at=str(mn_license.issue_date) if mn_license.issue_date else None,
            number=mn_license.license_number,
            types=[mn_license.license_type] if mn_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if mn_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=mn_license.issue_date,
            expiration_date=mn_license.expiration_date,
            county=mn_license.county,
            state=USLegalStatesEnum.MN.name,
        )

    @staticmethod
    def create_from_ma(business_id: UUID, ma_license: MALiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Commonwealth of Massachusetts"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=ma_license,
            license_type=ma_license.full_license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.MA.name,
                    license_types=[ma_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            license_type=LicenseType.LIQUOR_LICENSE,
            published_at=str(ma_license.issue_date) if ma_license.issue_date else None,
            number=ma_license.license_number,
            types=[ma_license.license_type] if ma_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if ma_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=ma_license.issue_date,
            expiration_date=ma_license.expiration_date,
            state=USLegalStatesEnum.MA.name,
        )

    @staticmethod
    def create_from_mi(business_id: UUID, mi_license: MILiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Michigan Licensing and Regulatory Affairs"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=mi_license,
            license_type=mi_license.license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.MI.name,
                    license_types=[mi_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            license_type=LicenseType.LIQUOR_LICENSE,
            published_at=str(mi_license.issue_date) if mi_license.issue_date else None,
            number=mi_license.license_number,
            types=[mi_license.license_type] if mi_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if mi_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=mi_license.issue_date,
            expiration_date=mi_license.expiration_date,
            state=USLegalStatesEnum.MI.name,
        )

    @staticmethod
    def create_from_mi_cra_thc(
        business_id: UUID, mi_cra_thc_license: MICraThcLiquorLicense, source: WebIngestedSource
    ) -> License:
        department_name = "Michigan Department of Licensing and Regulatory Affairs"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=mi_cra_thc_license,
            license_type=mi_cra_thc_license.license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.MI.name,
                    license_types=[mi_cra_thc_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            license_type=LicenseType.THC_LICENSE,
            published_at=str(mi_cra_thc_license.issue_date) if mi_cra_thc_license.issue_date else None,
            number=mi_cra_thc_license.license_number,
            types=[mi_cra_thc_license.license_type] if mi_cra_thc_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if mi_cra_thc_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=mi_cra_thc_license.issue_date,
            expiration_date=mi_cra_thc_license.expiration_date,
            state=USLegalStatesEnum.MI.name,
        )

    @staticmethod
    def create_from_mi_mlcc(
        business_id: UUID, mi_mlcc_license: MIMLCCLiquorLicense, source: WebIngestedSource
    ) -> License:
        department_name = "Michigan Liquor Control Commission"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=mi_mlcc_license,
            license_type=mi_mlcc_license.license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.MI.name,
                    license_types=[f"MLCC{mi_mlcc_license.license_number}"],  # make it dont match with MI license
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            license_type=LicenseType.LIQUOR_LICENSE,
            status=LicenseStatus.ACTIVE.value if mi_mlcc_license.is_active else LicenseStatus.INACTIVE.value,
            county=mi_mlcc_license.county,
            state=USLegalStatesEnum.MI.name,
            types=[mi_mlcc_license.license_type] if mi_mlcc_license.license_type else [],
            date_issued=mi_mlcc_license.issue_date,
            expiration_date=mi_mlcc_license.expiration_date,
            number=mi_mlcc_license.license_number,
            is_verified=True,
            verify_link=source.url,  # type: ignore
            issued_by=department_name,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            published_at=str(mi_mlcc_license.issue_date) if mi_mlcc_license.issue_date else None,
            source=source,
        )

    @staticmethod
    def create_from_va(business_id: UUID, va_license: VALiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Virginia Alcoholic Beverage Control Authority"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=va_license,
            license_type=va_license.license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.VA.name,
                    license_types=[va_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            license_type=LicenseType.LIQUOR_LICENSE,
            source=source,
            published_at=str(va_license.issue_date) if va_license.issue_date else None,
            number=va_license.license_number,
            types=[va_license.license_type] if va_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if va_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=va_license.issue_date,
            expiration_date=va_license.expiration_date,
            state=USLegalStatesEnum.VA.name,
        )

    @staticmethod
    def create_from_mo(business_id: UUID, mo_license: MOLiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Missouri Division of Alcohol and Tobacco Control"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=mo_license,
            license_type=mo_license.license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.MO.name,
                    license_types=[mo_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            license_type=LicenseType.LIQUOR_LICENSE,
            published_at=str(mo_license.issue_date) if mo_license.issue_date else None,
            number=mo_license.license_number,
            types=[mo_license.license_type] if mo_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if mo_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=mo_license.issue_date,
            expiration_date=mo_license.expiration_date,
            state=USLegalStatesEnum.MO.name,
        )

    @staticmethod
    def create_from_co(business_id: UUID, co_license: COLiquorLicense, source: WebIngestedSource) -> License:
        department_name = "Colorado Department of Revenue"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=co_license,
            license_type=co_license.license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.CO.name,
                    license_types=[co_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            license_type=LicenseType.LIQUOR_LICENSE,
            published_at=str(co_license.issue_date) if co_license.issue_date else None,
            number=co_license.license_number,
            types=[co_license.license_type] if co_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if co_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=co_license.issue_date,
            expiration_date=co_license.expiration_date,
            state=USLegalStatesEnum.CO.name,
        )

    @staticmethod
    def create_from_sc(business_id: UUID, sc_license: SCLiquorLicense, source: WebIngestedSource) -> License:
        department_name = "South Carolina Department of Revenue"
        body = LicenseFactory.create_alcohol_license_body(
            department_name=department_name,
            license=sc_license,
            license_type=sc_license.license_type,
        )

        return License(
            id=str(
                generate_liquor_license_uuid(
                    state=USLegalStatesEnum.SC.name,
                    license_types=[sc_license.license_number],
                )
            ),
            document_type_id=DocumentTypeID.LICENSE.value,
            parent_id=str(business_id),
            parent_type=ParentType.BUSINESS.value,
            body=body,
            source=source,
            license_type=LicenseType.LIQUOR_LICENSE,
            published_at=str(sc_license.issue_date) if sc_license.issue_date else None,
            number=sc_license.license_number,
            types=[sc_license.license_type] if sc_license.license_type else [],
            status=LicenseStatus.ACTIVE.value if sc_license.is_active else LicenseStatus.INACTIVE.value,
            issued_by=department_name,
            is_verified=True,
            verify_link=source.url,
            date_issued=sc_license.issue_date,
            expiration_date=sc_license.expiration_date,
            state=USLegalStatesEnum.SC.name,
        )
