import os
from enum import Enum
from typing import Union

from infrastructure_common.logging import get_logger
from static_common.enums.enum import StrEnum
from static_common.enums.source_types import WebIngestedSourceTypeID

logger = get_logger()


class CoverageType(StrEnum):
    PRIMARY = "PRIMARY"
    EXCESS = "EXCESS"


class ReviewSource(StrEnum):
    YELP = "yelp"
    TRIP_ADVISOR = "trip_advisor"
    BUILDZOOM = "buildzoom"


class InsightStatus(StrEnum):
    succeeded = "succeeded"
    in_progress = "in_progress"
    failed = "failed"


class InsightParentResource(StrEnum):
    GENERAL_OPERATION = "general_operation"
    RESTAURANT_OPERATION = "restaurant_operation"
    HOTEL_OPERATION = "hotel_operation"
    EQUIPMENT = "equipment"


class ScrapingMode(StrEnum):
    DISCOVERY_ONLY = "discovery_only"
    FULL = "full"


class InsightType(StrEnum):
    # Binary
    HAS_ADULT_ENTERTAINMENT = "has_adult_entertainment"
    HAS_BOTTLE_SERVICE = "has_bottle_service"
    HAS_BOUNCER = "has_bouncers"
    HAS_BYOB = "has_byob"
    HAS_CATERING = "has_catering"
    HAS_CRIMES_IN_BUSINESS = "has_crimes_in_business"
    HAS_DAMAGES = "has_damages"
    HAS_DANCING = "has_dancing"
    HAS_DELIVERY = "has_delivery"
    HAS_EXPOSURE_TO_FLAMMABLES = "has_exposure_to_flammables"
    HAS_INFESTATION = "has_infestation"
    HAS_LIFEGUARD = "has_lifeguard"
    HAS_PROSTITUTION = "has_prostitution"
    HAS_ROOF_ACCESS = "has_roof_access"
    HAS_ROOMS_WITH_BALCONIES = "has_rooms_with_balconies"
    HAS_SECURITY_GUARDS = "has_security_guards"
    HAS_SWIMMING_POOL = "has_swimming_pool"
    HAS_WAITER_SERVICE = "has_waiter_service"
    HAS_WORKERS_ROLES = "has_workers_roles"
    # Multilabel
    COOKING_TYPES = "cooking_types"
    ENTERTAINMENT_TYPES = "entertainment_types"
    HOTEL_SPECIAL_FACILITIES_TYPES = "hotel_special_facilities_types"
    HAS_OUTDOOR_SEATING = "has_outdoor_seating"
    PARKING_TYPES = "parking_types"
    PERFORMANCES_TYPES = "performances_types"
    # Multiclass
    SERVES_ALCOHOL = "serves_alcohol"


class Grade(Enum):
    A = 1
    B = 2
    C = 3
    D = 4
    E = 5
    F = 5


class FloodRiskGrade(Enum):
    A = 100
    AE = 100
    A1_30 = 100
    AI_A30 = 100
    AH = 100
    AO = 100
    AR = 100
    A99 = 100
    B = 66
    C = 33
    D = 11
    V = 11
    VE = 11
    X = 1


RATING_TO_WEIGHT = {
    "No Risk": 0,
    "Low Risk": 33,
    "Medium Risk": 66,
    "High Risk": 100,
    "Within": 0,
    "Outside": 66,  # todo use distance instead
}


class Source(StrEnum):
    BUILDZOOM_BUSINESS = "buildzoom-business"
    BETTER_BUSINESS_BUREAU = "better-business-bureau"
    THOMASNET_BUSINESS = "thomasnet-business"
    HOUZZ_BUSINESS = "houzz-business"
    IQS_BUSINESS = "iqs-business"
    IQS_NEWS = "iqs-news"
    GOOGLE_BUSINESS = "google-business"
    FMCSA_BUSINESS = "fmcsa-business"
    DOT_REPORT_BUSINESS = "dot-report-business"
    SAFER_FMCSA_BUSINESS = "safer-fmcsa-business"
    FSIS = "fsis"
    GOOD_JOBS_FIRST_BUSINESS = "good-jobs-first-business"
    YELP_BUSINESS = "yelp-business"
    TRIPADVISOR_BUSINESS = "tripadvisor-business"
    FACEBOOK_BUSINESS = "facebook-business"
    EPA_BUSINESS = "epa-business"
    OSHA_BUSINESS = "osha-business"
    FDA_BUSINESS = "fda-business"
    NHTSA_FARS_REPORT = "nhtsa-fars-report"
    UNICOURT = "unicourt"
    SAM = "sam"
    OPENCORPORATES = "opencorporates"
    ERISA_5500_BUSINESS = "erisa-5500"
    APARTMENTS = "apartments"
    IRS_FORM_990 = "irs-form-990"
    CA_CSLB = "ca-cslb"


def map_source_to_web_ingestion_type(source: Union[Source, str, None]) -> WebIngestedSourceTypeID:
    if source is None:
        logger.warning(
            "Missing source for web ingestion. Default to WEBSITE_CONTENT",
            default_type=WebIngestedSourceTypeID.WEBSITE_CONTENT,
        )
        return WebIngestedSourceTypeID.WEBSITE_CONTENT
    value: str = source.name if isinstance(source, Source) else source
    value = value.upper()
    if matched_source_by_exact_name := WebIngestedSourceTypeID.try_parse_str(value):
        return matched_source_by_exact_name
    if matched_with_dash := WebIngestedSourceTypeID.try_parse_str(value.replace("-", "_")):
        return matched_with_dash
    parsed_to_convention = value.replace("-", "_").replace("BUSINESS", "").strip("_")
    if matched_source_by_convention := WebIngestedSourceTypeID.try_parse_str(parsed_to_convention):
        return matched_source_by_convention

    source_enum: Source | None = Source.try_parse_str(value)

    if source_enum == Source.OPENCORPORATES:
        return WebIngestedSourceTypeID.OPEN_CORPORATES
    elif source_enum == Source.NHTSA_FARS_REPORT:
        return WebIngestedSourceTypeID.NHTSA
    elif source_enum == Source.IQS_NEWS:
        return WebIngestedSourceTypeID.IQS
    elif source_enum == Source.GOOGLE_BUSINESS:
        return WebIngestedSourceTypeID.GOOGLE_LOCAL
    elif source_enum == Source.ERISA_5500_BUSINESS:
        return WebIngestedSourceTypeID.EFAST
    elif source_enum == Source.TRIPADVISOR_BUSINESS:
        return WebIngestedSourceTypeID.TRIP_ADVISOR
    else:
        # fallback
        logger.warning(
            "Not found a matching WebIngestedSourceTypeID for source will use WEBSITE_CONTENT", source=source
        )
        return WebIngestedSourceTypeID.WEBSITE_CONTENT


class WorkTypeEnum(StrEnum):
    EL = "electrical"
    FO = "foundational"
    OT = "other_construction_equipment"
    PL = "plumbing"
    SP = "sprinkler"


class FullSFNames(Enum):
    SCRAPE_BBB_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_BBB_WITH_INSIGHTS",
        "ingestion-scrape-better-business-bureau-with-insights",
    )
    SCRAPE_YELP_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_YELP_WITH_INSIGHTS",
        "ingestion-scrape-yelp-with-insights",
    )
    SCRAPE_TRIPADVISOR_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_TRIPADVISOR_WITH_INSIGHTS",
        "ingestion-scrape-trip-with-insights",
    )
    SCRAPE_THOMASNET_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_THOMASNET_WITH_INSIGHTS",
        "ingestion-scrape-thomasnet-with-insights",
    )
    SCRAPE_IQS_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_IQS_WITH_INSIGHTS",
        "ingestion-scrape-iqs-with-insights",
    )
    SCRAPE_HOUZZ_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_HOUZZ_WITH_INSIGHTS",
        "ingestion-scrape-houzz-with-insights",
    )
    SCRAPE_GOOGLE_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_GOOGLE_WITH_INSIGHTS",
        "ingestion-scrape-google-with-insights",
    )
    SCRAPE_BUILDZOOM_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_BUILDZOOM_WITH_INSIGHTS",
        "ingestion-scrape-buildzoom-with-insights",
    )
    SCRAPE_FMCSA_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_FMCSA_WITH_INSIGHTS",
        "ingestion-scrape-fmcsa-with-insights",
    )
    SCRAPE_SAFER_FMCSA_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_SAFER_FMCSA_WITH_INSIGHTS",
        "ingestion-scrape-safer-fmcsa-with-insights",
    )
    SCRAPE_FACEBOOK_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_FACEBOOK_WITH_INSIGHTS",
        "ingestion-scrape-facebook-with-insights",
    )
    SCRAPE_GOOD_JOBS_FIRST_WITH_INSIGHTS = os.environ.get(
        "SF_SCRAPE_GOOD_JOBS_FIRST_WITH_INSIGHTS",
        "ingestion-scrape-good-jobs-first-with-insights",
    )
    SF_SYNC_STATUS_REPORT = os.environ.get(
        "SF_SYNC_STATUS_REPORT",
        "ingestion-sync-status-report",
    )


class DiscoverySFNames(Enum):
    SCRAPE_BBB = os.environ.get(
        "SF_SCRAPE_BBB",
        "ingestion-scrape-all-better-business-bureau",
    )
    SCRAPE_YELP = os.environ.get(
        "SF_SCRAPE_YELP",
        "ingestion-scrape-all-yelp",
    )
    SCRAPE_FDA = os.environ.get(
        "SF_SCRAPE_FDA",
        "ingestion-scrape-all-fda",
    )
    SCRAPE_TRIPADVISOR = os.environ.get(
        "SF_SCRAPE_TRIPADVISOR",
        "ingestion-scrape-all-tripadvisor",
    )
    SCRAPE_THOMASNET = os.environ.get(
        "SF_SCRAPE_THOMASNET",
        "ingestion-scrape-all-thomasnet",
    )
    SCRAPE_IQS = os.environ.get(
        "SF_SCRAPE_IQS",
        "ingestion-scrape-all-iqs",
    )
    SCRAPE_HOUZZ = os.environ.get(
        "SF_SCRAPE_HOUZZ",
        "ingestion-scrape-all-houzz",
    )
    SCRAPE_GOOGLE_LOCAL = os.environ.get(
        "SF_SCRAPE_GOOGLE_LOCAL",
        "ingestion-scrape-all-google-local",
    )
    SCRAPE_BUILDZOOM = os.environ.get(
        "SF_SCRAPE_BUILDZOOM",
        "ingestion-scrape-all-buildzoom",
    )
    SCRAPE_FMCSA = os.environ.get(
        "SF_SCRAPE_FMCSA",
        "ingestion-scrape-all-fmcsa",
    )
    SCRAPE_SAFER_FMCSA = os.environ.get(
        "SF_SCRAPE_SAFER_FMCSA",
        "ingestion-scrape-safer-fmcsa",
    )
    SCRAPE_FACEBOOK = os.environ.get(
        "SF_SCRAPE_FACEBOOK",
        "ingestion-scrape-all-facebook",
    )
    SCRAPE_GOOD_JOBS_FIRST = os.environ.get(
        "SF_SCRAPE_GOOD_JOBS_FIRST",
        "ingestion-scrape-all-good-jobs-first",
    )
