from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional

from copilot_client_v3 import SubmissionSyncCoverage, SubmissionSyncMatcherData
from dataclasses_json import dataclass_json
from frozendict import frozendict
from infrastructure_common.logging import get_logger
from static_common.enums.enum import StrEnum
from static_common.enums.organization import OrganizationGroups
from static_common.enums.submission import SubmissionStage

from src.models.types import CoverageType

logger = get_logger()


class BowheadStatus(StrEnum):
    BOUND = "BOUND"
    COMPLETE = "COMPLETE"
    DECLINED = "DECLINED"
    LAPSED = "LAPSED"
    LOGGED = "LOGGED"
    NTU = "NTU"
    PROSPECT = "PROSPECT"
    QUOTE = "QUOTE"
    QUOTED = "QUOTED"
    WITHDRAWN = "WITHDRAWN"
    WORK_IN_PROGRESS = "WORK_IN_PROGRESS"

    @property
    def copilot_stage(self) -> str:
        return {
            BowheadStatus.BOUND: SubmissionStage.QUOTED_BOUND.value,
            BowheadStatus.COMPLETE: SubmissionStage.QUOTED_BOUND.value,
            BowheadStatus.DECLINED: SubmissionStage.DECLINED.value,
            BowheadStatus.LAPSED: SubmissionStage.EXPIRED.value,
            BowheadStatus.LOGGED: SubmissionStage.ON_MY_PLATE.value,
            BowheadStatus.NTU: SubmissionStage.QUOTED_LOST.value,
            BowheadStatus.PROSPECT: SubmissionStage.ON_MY_PLATE.value,
            BowheadStatus.QUOTE: SubmissionStage.ON_MY_PLATE.value,
            BowheadStatus.QUOTED: SubmissionStage.QUOTED.value,
            BowheadStatus.WITHDRAWN: SubmissionStage.DECLINED.value,
            BowheadStatus.WORK_IN_PROGRESS: SubmissionStage.ON_MY_PLATE.value,
        }[self]

    @property
    def status_to_compare(self) -> str:
        if self == BowheadStatus.BOUND or self == BowheadStatus.COMPLETE:
            return BowheadStatus.BOUND.value
        if self == BowheadStatus.NTU:
            return BowheadPolicyStatus.NOT_TAKEN_UP.value
        return self.value


class BowheadPolicyStatus(StrEnum):
    NOT_REQUIRED = "NOT_REQUIRED"
    REMOVED = "REMOVED"
    QUOTED = "QUOTED"
    QUOTE = "QUOTE"
    NOT_TAKEN_UP = "NOT_TAKEN_UP"
    BOUND = "BOUND"
    INDICATION = "INDICATION"


class BowheadRiskType(StrEnum):
    VERSIONED = "VERSIONED"
    RENEWAL = "RENEWAL"
    NEW = "NEW"

    def is_renewal(self) -> bool:
        return self == BowheadRiskType.RENEWAL


class BowheadLob(StrEnum):
    EXCESS_CASUALTY = "Excess Casualty"
    PRIMARY_CASUALTY = "Primary Casualty"
    PUBLIC_ENTITY = "Public Entity"

    @property
    def copilot_coverage_name(self) -> Optional[str]:
        return {
            BowheadLob.EXCESS_CASUALTY: "liability",
            BowheadLob.PRIMARY_CASUALTY: "liability",
            BowheadLob.PUBLIC_ENTITY: "liability",
        }.get(self)

    @property
    def copilot_coverage_type(self) -> Optional[str]:
        return {
            BowheadLob.EXCESS_CASUALTY: CoverageType.EXCESS,
            BowheadLob.PRIMARY_CASUALTY: CoverageType.PRIMARY,
            BowheadLob.PUBLIC_ENTITY: CoverageType.EXCESS,
        }.get(self)

    @property
    def user_group_to_match(self) -> str:
        return {
            BowheadLob.EXCESS_CASUALTY: OrganizationGroups.BOWHEAD_XS.value,
            BowheadLob.PRIMARY_CASUALTY: OrganizationGroups.BOWHEAD_PRIMARY.value,
            BowheadLob.PUBLIC_ENTITY: OrganizationGroups.BOWHEAD_XS.value,
        }.get(self)


@dataclass_json
@dataclass(eq=True, frozen=True)
class BowheadReportRow:
    risk_id: str
    risk_type: BowheadRiskType
    quote_reference: str
    named_insured: Optional[str]
    named_insured_street_address: Optional[str]
    named_insured_city: Optional[str]
    named_insured_county_or_state: Optional[str]
    named_insured_zip: Optional[str]
    uw: str
    broker_name: Optional[str]
    agency_name: Optional[str]
    created_date: Optional[datetime]
    received_date: datetime
    effective_date: datetime
    bound_date: Optional[datetime]
    status: BowheadStatus
    policy_status: Optional[BowheadPolicyStatus]
    attachment: Optional[float]
    bowhead_limit: Optional[float]
    bowhead_layer_premium: Optional[float]
    lob: BowheadLob
    latest_version: int
    brat_save_date: Optional[datetime]

    @property
    def created_date_start_of_day(self) -> datetime | None:
        return self.created_date.replace(hour=0, minute=0, second=0, microsecond=0) if self.created_date else None

    @property
    def copilot_stage(self) -> str:
        return self.status.copilot_stage

    @property
    def quoted_premium(self) -> Optional[float]:
        if self.status in [
            BowheadStatus.QUOTE,
            BowheadStatus.QUOTED,
            BowheadStatus.BOUND,
            BowheadStatus.COMPLETE,
            BowheadStatus.NTU,
        ]:
            return self.bowhead_layer_premium
        return None

    @property
    def bound_premium(self) -> Optional[float]:
        if self.status in [
            BowheadStatus.BOUND,
            BowheadStatus.COMPLETE,
        ]:
            return self.bowhead_layer_premium
        return None

    @property
    def sync_coverages(self) -> List[SubmissionSyncCoverage]:
        coverages: List[SubmissionSyncCoverage] = []
        if not self.lob.copilot_coverage_name:
            return coverages
        coverages.append(
            SubmissionSyncCoverage(
                coverage_name=self.lob.copilot_coverage_name,
                coverage_type=self.lob.copilot_coverage_type,
                quoted_premium=self.quoted_premium,
                bound_premium=self.bound_premium,
                limit=self.bowhead_limit,
                attachment_point=self.attachment,
            )
        )

        return coverages

    @property
    def matcher_data(self) -> SubmissionSyncMatcherData | None:
        if (
            self.named_insured_street_address
            and self.named_insured_city
            and self.named_insured_county_or_state
            and self.named_insured_zip
        ):
            address = (
                f"{self.named_insured_street_address}, {self.named_insured_city}, "
                f"{self.named_insured_county_or_state} {self.named_insured_zip}"
            )
            return SubmissionSyncMatcherData(
                self.named_insured,
                address,
                additional_data=frozendict(
                    {
                        "address_line_1": self.named_insured_street_address,
                        "city": self.named_insured_city,
                        "state": self.named_insured_county_or_state,
                        "postal_code": self.named_insured_zip,
                    }
                ),
            )
        return None

    @property
    def is_direct_match(self) -> bool:
        return (self.created_date_start_of_day or self.received_date) < datetime(2024, 3, 15)
