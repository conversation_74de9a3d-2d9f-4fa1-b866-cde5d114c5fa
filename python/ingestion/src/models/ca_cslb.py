from dataclasses import dataclass
from datetime import datetime

from dateutil import parser
from static_common.enums.entity import (
    EntityCategory,
    EntityNameType,
    EntityPremisesType,
)
from static_common.utils.date import is_date

from src.models.search.business_profile_source import BusinessProfileSource
from src.models.types import Source


def _parse_date(date_str: str) -> datetime | None:
    if date_str and is_date(date_str):
        return parser.parse(date_str)
    return None


@dataclass
class ContractorData:
    contractor_name: str | None
    name_type: str | None
    license_number: str
    city: str | None
    status: str | None

    @classmethod
    def from_license_number(cls, license_number: str):
        return cls(
            contractor_name=None,
            name_type=None,
            license_number=license_number,
            city=None,
            status=None,
        )


@dataclass
class BusinessInformation:
    business_name: str | None = None
    address: str | None = None
    phone_number: str | None = None
    entity_type: str | None = None
    license_issue_date: str | None = None
    license_expiration_date: str | None = None
    license_status: str | None = None
    license_classifications: list[str] | None = None

    @property
    def license_issue_date_parsed(self) -> str | None:
        return _parse_date(self.license_issue_date)

    @property
    def license_expiration_date_parsed(self) -> str | None:
        return _parse_date(self.license_expiration_date)


@dataclass
class BondInformation:
    bond_agent: str | None = None
    bond_number: str | None = None
    bond_amount: str | None = None
    bond_effective_date: str | None = None
    bond_cancellation_date: str | None = None

    @property
    def bond_effective_date_parsed(self) -> str | None:
        return _parse_date(self.bond_effective_date)

    @property
    def bond_cancellation_date_parsed(self) -> str | None:
        return _parse_date(self.bond_cancellation_date)


@dataclass
class WorkersCompInformation:
    insurance_company: str | None = None
    policy_number: str | None = None
    effective_date: str | None = None
    cancellation_date: str | None = None

    @property
    def effective_date_parsed(self) -> str | None:
        return _parse_date(self.effective_date)

    @property
    def cancellation_date_parsed(self) -> str | None:
        return _parse_date(self.cancellation_date)


@dataclass
class LicensePage:
    business_information: BusinessInformation
    bond_information: BondInformation
    workers_comp_information: WorkersCompInformation | None
    contractor_data: ContractorData

    def to_business_profile_source(self) -> BusinessProfileSource:
        if self.contractor_data.name_type == "DBA":
            name_type = EntityNameType.DBA_NAME
        elif self.contractor_data.name_type == "Name":
            name_type = EntityNameType.LEGAL_NAME
        else:
            name_type = EntityNameType.UNDEFINED
        return BusinessProfileSource(
            source_type=Source.CA_CSLB.value,
            url=self.url,
            name=self.business_information.business_name,
            address=self.business_information.address,
            phone=self.business_information.phone_number,
            name_type=name_type.value,
            entity_category=EntityCategory.UNDEFINED.value,
            premises_type=EntityPremisesType.UNDEFINED.value,
        )

    def get_license_status(self) -> str:
        if self.contractor_data.status is not None:
            return self.contractor_data.status
        if self.business_information.license_status:
            if "current and active" in self.business_information.license_status.lower():
                return "Active"
            elif "expired" in self.business_information.license_status.lower():
                return "Expired"
            elif "inactive" in self.business_information.license_status.lower():
                return "Inactive"
        return "Unknown"

    @property
    def url(self) -> str:
        return f"https://cslb.ca.gov/OnlineServices/CheckLicenseII/LicenseDetail.aspx?LicNum={self.contractor_data.license_number}"
