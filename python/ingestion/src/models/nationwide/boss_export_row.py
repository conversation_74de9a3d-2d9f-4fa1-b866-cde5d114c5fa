from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional

from copilot_client_v3 import SubmissionSyncCoverage
from infrastructure_common.logging import get_logger
from static_common.enums.contractor import ContractorSubmissionType
from static_common.enums.enum import StrEnum
from static_common.enums.insurance import ProjectInsuranceType
from static_common.enums.submission import SubmissionCoverageType, SubmissionStage

logger = get_logger()

COPILOT_STATUS_MAPPING = {
    "Binder": {
        "Bound - Not Sent": SubmissionStage.QUOTED_BOUND,
        "Bound-Sent": SubmissionStage.QUOTED_BOUND,
    },
    "Policy": {
        "Active": SubmissionStage.QUOTED_BOUND,
        "Completed": SubmissionStage.QUOTED_BOUND,
    },
    "Prepare to Issue": {
        "Bound-Sent": SubmissionStage.QUOTED_BOUND,
        "Bound - Not Sent": SubmissionStage.QUOTED_BOUND,
    },
    "Quote": {
        "In Process": {
            None: SubmissionStage.ON_MY_PLATE,
            "Blocked": {
                None: SubmissionStage.DECLINED,
                "Declined": SubmissionStage.DECLINED,
                "Closed": {
                    None: SubmissionStage.QUOTED_LOST,
                    "Bound by Competitor": SubmissionStage.QUOTED_LOST,
                    "Broker Lost Account": SubmissionStage.QUOTED_LOST,
                    "Duplicate Submission": SubmissionStage.DECLINED,
                    "Incomplete Submission": SubmissionStage.DECLINED,
                    "No Response From Broker": SubmissionStage.DECLINED,
                    "Not Reviewed": SubmissionStage.DECLINED,
                    "Other": SubmissionStage.DECLINED,
                    "Pricing": SubmissionStage.QUOTED_LOST,
                    "Renewed by Incumbent": SubmissionStage.QUOTED_LOST,
                    "Secondary": SubmissionStage.DECLINED,
                    "Submitted in Error": SubmissionStage.DECLINED,
                    "Unacceptable Coverage Need": SubmissionStage.DECLINED,
                },
            },
            "Released": {
                None: SubmissionStage.DECLINED,
                "Closed": {
                    "Duplicate Submission": SubmissionStage.DECLINED,
                    "No Response From Broker": SubmissionStage.DECLINED,
                    "Other": SubmissionStage.DECLINED,
                    "Pricing": SubmissionStage.QUOTED_LOST,
                    "Renewed by Incumbent": SubmissionStage.QUOTED_LOST,
                    "Secondary": SubmissionStage.DECLINED,
                    "Submitted in Error": SubmissionStage.DECLINED,
                    "System Closed - Pended Timeout": SubmissionStage.EXPIRED,
                    "Incomplete Submission": SubmissionStage.DECLINED,
                    "Bound by Competitor": SubmissionStage.QUOTED_LOST,
                    "Not Reviewed": SubmissionStage.DECLINED,
                    "Broker Lost Account": SubmissionStage.QUOTED_LOST,
                    "Unacceptable Coverage Need": SubmissionStage.DECLINED,
                },
                "Declined": {
                    "Unacceptable Exposure": SubmissionStage.DECLINED,
                    "Outside of Guidelines/Appetite": SubmissionStage.DECLINED,
                    "Other": SubmissionStage.DECLINED,
                    "Unacceptable Coverage Need": SubmissionStage.DECLINED,
                    "Account Size Too Small": SubmissionStage.DECLINED,
                    "Underlying Carrier Quality": SubmissionStage.DECLINED,
                },
            },
        },
        "Quoted": {
            None: SubmissionStage.QUOTED,
            "Released": SubmissionStage.QUOTED_LOST,
            "Blocked": {
                None: SubmissionStage.QUOTED_LOST,
                "Declined": SubmissionStage.DECLINED,
                "Closed": {
                    "Bound by Competitor": SubmissionStage.QUOTED_LOST,
                    "Broker Lost Account": SubmissionStage.QUOTED_LOST,
                    "No Response From Broker": SubmissionStage.QUOTED_LOST,
                    "Other": SubmissionStage.QUOTED_LOST,
                    "Pricing": SubmissionStage.QUOTED_LOST,
                    "Renewed by Incumbent": SubmissionStage.QUOTED_LOST,
                    "Unacceptable Coverage Need": SubmissionStage.DECLINED,
                    "Incomplete Submission": SubmissionStage.DECLINED,
                    "Duplicate Submission": SubmissionStage.DECLINED,
                    "Secondary": SubmissionStage.DECLINED,
                },
            },
        },
    },
    "Renewal": {
        "In Process": SubmissionStage.ON_MY_PLATE,
    },
    "Renewal Quote": {
        "In Process": SubmissionStage.ON_MY_PLATE,
    },
}

PROJECT_INSURANCE_TYPE_MAPPING = {
    "Yes": ProjectInsuranceType.WRAP_UP,
    "No": {
        "Yes": {
            "Owner": ProjectInsuranceType.OWNERS_INTEREST,
            "General Contractor": ProjectInsuranceType.PROJECT_SPECIFIC,
            "Owner & General Contractor": ProjectInsuranceType.PROJECT_SPECIFIC,
            None: ProjectInsuranceType.PROJECT_SPECIFIC,
        },
        "No": ProjectInsuranceType.PROJECT_SPECIFIC,
    },
    "0": {
        "No": ProjectInsuranceType.PROJECT_SPECIFIC,
    },
    None: {
        "No": ProjectInsuranceType.PROJECT_SPECIFIC,
    },
}


class NationwideBossTransactionCD(StrEnum):
    RENEWAL = "Renewal"
    CANCELLATION = "Cancellation"
    REWRITE_RENEWAL = "Rewrite-Renewal"
    NEW_BUSINESS = "New Business"
    EXTEND_POLICY = "Extend Policy"
    ENDORSEMENT = "Endorsement"
    REINSTATEMENT = "Reinstatement"
    RENEWAL_START = "Renewal Start"


class NationwideBossSubmissionStageDS(StrEnum):
    QUOTE = "Quote"
    POLICY = "Policy"
    PREPARE_TO_ISSUE = "Prepare to Issue"
    BINDER = "Binder"
    RENEWAL_QUOTE = "Renewal Quote"
    RENEWAL = "Renewal"


class NationwideBossSubmissionStatusDS(StrEnum):
    ACTIVE = "Active"
    COMPLETED = "Completed"
    IN_PROCESS = "In Process"
    BOUND_NOT_SENT = "Bound - Not Sent"
    BOUND_SENT = "Bound-Sent"
    QUOTED = "Quoted"
    RENEWED = "Renewed"


class NationwideBossCloseDispositionCD(StrEnum):
    RELEASED = "Released"
    BLOCKED = "Blocked"


class NationwideBossCloseReasonDS(StrEnum):
    NO_CONTACT_WITH_THE_INSURED = "No contact with the Insured"
    LINKED_QUOTE_BIND = "LinkedQuoteBind"
    CLOSED_BY_UNDERWRITER = "Closed by Underwriter"
    CLOSED = "Closed"
    DECLINED = "Declined"
    AUTOMATIC_CLOSE = "Automatic close"


class NationwideBossCloseSubReasonDS(StrEnum):
    SYSTEM_CLOSED_PENDED_TIMEOUT = "System Closed - Pended Timeout"
    UNACCEPTABLE_COVERAGE_NEED = "Unacceptable Coverage Need"
    INCOMPLETE_SUBMISSION = "Incomplete Submission"
    ACCOUNT_SIZE_TOO_LARGE = "Account Size Too Large"
    LOSS_HISTORY = "Loss History"
    NOT_REVIEWED = "Not Reviewed"
    SUBMITTED_IN_ERROR = "Submitted in Error"
    SUBMITTED_AND_NOT_WRITTEN_IN_PRIOR_YEARS = "Submitted and Not Written in Prior Years"
    NO_RESPONSE_FROM_BROKER = "No Response From Broker"
    SECONDARY = "Secondary"
    ACCOUNT_SIZE_TOO_SMALL = "Account Size Too Small"
    UNDERLYING_CARRIER_QUALITY = "Underlying Carrier Quality"
    NON_RENEWED_ACCOUNT = "Non-Renewed Account"
    NONE = "None"
    OTHER = "Other"
    BOUND_BY_COMPETITOR = "Bound by Competitor"
    BROKER_LOST_ACCOUNT = "Broker Lost Account"
    OUTSIDE_OF_GUIDELINES_APPETITE = "Outside of Guidelines/Appetite"
    PRICING = "Pricing"
    UNACCEPTABLE_EXPOSURE = "Unacceptable Exposure"
    DUPLICATE_SUBMISSION = "Duplicate Submission"
    RENEWED_BY_INCUMBENT = "Renewed by Incumbent"
    DRIVER_HISTORY_MVR = "Driver History (MVR)"


class NationwideBossNaicsSectorDS(StrEnum):
    EDUCATIONAL_SERVICES = "Educational Services"
    WHOLESALE_TRADE = "Wholesale Trade"
    TRANSPORTATION_AND_WAREHOUSING = "Transportation and Warehousing"
    ARTS_ENTERTAINMENT_AND_RECREATION = "Arts, Entertainment, and Recreation"
    AGRICULTURE_FORESTRY_FISHING_AND_HUNTING = "Agriculture, Forestry, Fishing and Hunting"
    ACCOMMODATION_AND_FOOD_SERVICES = "Accommodation and Food Services"
    HEALTH_CARE_AND_SOCIAL_ASSISTANCE = "Health Care and Social Assistance"
    PUBLIC_ADMINISTRATION = "Public Administration"
    FINANCE_AND_INSURANCE = "Finance and Insurance"
    MANAGEMENT_OF_COMPANIES_AND_ENTERPRISES = "Management of Companies and Enterprises"
    CONSTRUCTION = "Construction"
    INDUSTRIES_NOT_CLASSIFIED = "Industries Not Classified"
    MANUFACTURING = "Manufacturing"
    ADMINISTRATIVE_AND_SUPPORT = "Administrative and Support and Waste Management and Remediation Services"
    UTILITIES = "Utilities"
    REAL_ESTATE_AND_RENTAL_AND_LEASING = "Real Estate and Rental and Leasing"
    OTHER_SERVICES = "Other Services (except Public Administration)"
    RETAIL_TRADE = "Retail Trade"
    CONSTRUCTION_AUTO_ONLY = "Construction (Auto-Only)"
    PROFESSIONAL_SCIENTIFIC_AND_TECHNICAL_SERVICES = "Professional, Scientific, and Technical Services"
    MINING_QUARRYING_AND_OIL_AND_GAS_EXTRACTION = "Mining, Quarrying, and Oil and Gas Extraction"
    INFORMATION = "Information"


class NationwideBossProjSpecificPolicyIN(StrEnum):
    YES = "Yes"
    NO = "No"


class NationwideBossWrapPolicyIN(StrEnum):
    YES = "Yes"
    NO = "No"
    # I guess some bug in their export. NW has been notified
    ZERO_NO = "0"


class NationwideBossInsuredRoleDS(StrEnum):
    GENERAL_CONTRACTOR = "General Contractor"
    OWNER_AND_GENERAL_CONTRACTOR = "Owner & General Contractor"
    OWNER = "Owner"


class NationwideBossProductCD(StrEnum):
    EXCESS = "Excess"
    LIABILITY = "Liability"
    MONO_LIABILITY = "Mono-Liability"
    MONO_LIQUOR = "Mono-Liquor"

    @property
    def coverage_type(self) -> str:
        if self == NationwideBossProductCD.EXCESS:
            return SubmissionCoverageType.EXCESS.name
        return SubmissionCoverageType.PRIMARY.name


@dataclass
class NationwideClientStage:
    stage_id: int
    copilot_stage: str


@dataclass
class NationwideBossExportRow:
    submission_nb: str
    quote_nb: str | None
    policy_nb: str | None
    insured_nm: str
    transaction_cd: NationwideBossTransactionCD
    submission_stage_ds: NationwideBossSubmissionStageDS
    submission_status_ds: NationwideBossSubmissionStatusDS
    close_disposition_cd: NationwideBossCloseDispositionCD | None
    close_reason_ds: NationwideBossCloseReasonDS | None
    close_subreason_ds: NationwideBossCloseSubReasonDS | None
    effective_dt: datetime
    expriration_dt: datetime
    inception_dt: datetime | None
    attach: float | None
    occ_limit: float | None
    agg_limit: float | None
    naics_sector_ds: NationwideBossNaicsSectorDS | None
    proj_specific_policy_in: NationwideBossProjSpecificPolicyIN
    wrap_policy_in: NationwideBossWrapPolicyIN | None
    insured_role_ds: NationwideBossInsuredRoleDS | None
    product_cd: NationwideBossProductCD
    final_premium_am: float | None
    assigned_underwriter_user_id: str | None
    quote_creation_dt: datetime | None
    binder_creation_dt: datetime | None
    close_dt: datetime | None
    submission_add_dt: datetime
    controlling_state_cd: str | None

    @property
    def is_renewal(self) -> bool:
        return self.transaction_cd == NationwideBossTransactionCD.RENEWAL

    @property
    def copilot_stage(self):
        keys = [
            self.submission_stage_ds.value,
            self.submission_status_ds.value,
            self.close_disposition_cd.value if self.close_disposition_cd else None,
            self.close_reason_ds.value if self.close_reason_ds else None,
            self.close_subreason_ds.value if self.close_subreason_ds else None,
        ]
        result = COPILOT_STATUS_MAPPING
        for key in keys:
            result = result.get(key)
            if not result:
                raise ValueError(f"Cannot find mapping: {keys}")
            if isinstance(result, SubmissionStage):
                return result

        raise ValueError(f"Iterated over mappings and cannot find mapping: {keys}")

    def sync_coverages(self, copilot_stage: str) -> List[SubmissionSyncCoverage]:
        quoted_premium = self.final_premium_am if copilot_stage == SubmissionStage.QUOTED.value else None
        bound_premium = self.final_premium_am if copilot_stage == SubmissionStage.QUOTED_BOUND.value else None

        coverages: List[SubmissionSyncCoverage] = []
        if self.product_cd == NationwideBossProductCD.MONO_LIQUOR:
            return coverages
        coverages.append(
            SubmissionSyncCoverage(
                coverage_name="liability",
                coverage_type=self.product_cd.coverage_type,
                quoted_premium=quoted_premium,
                bound_premium=bound_premium,
                limit=self.agg_limit,
                attachment_point=self.attach,
            )
        )

        return coverages

    @property
    def underwriter_email(self) -> Optional[str]:
        if self.assigned_underwriter_user_id:
            return f"{self.assigned_underwriter_user_id}@nationwide.com"
        return None

    @property
    def contractor_submission_type(self) -> str | None:
        if self.proj_specific_policy_in == NationwideBossProjSpecificPolicyIN.YES:
            return ContractorSubmissionType.PROJECT.value
        if self.proj_specific_policy_in == NationwideBossProjSpecificPolicyIN.NO:
            return ContractorSubmissionType.PRACTICE.value
        return None

    @property
    def project_insurance_type(self) -> str:
        keys = [
            self.wrap_policy_in.value if self.wrap_policy_in else None,
            self.proj_specific_policy_in.value,
            self.insured_role_ds.value if self.insured_role_ds else None,
        ]
        result = PROJECT_INSURANCE_TYPE_MAPPING
        for key in keys:
            if key not in result:
                raise ValueError(f"Cannot find project insurance mapping: {keys}")
            result = result[key]
            if isinstance(result, ProjectInsuranceType):
                return result.value

        raise ValueError(f"Iterated over mappings and cannot find mapping: {keys}")

    @property
    def client_stage_tags(self) -> tuple[str, ...]:
        # the map will be resolved using the following order:
        # 1. submission_status_ds
        # 2. close_disposition_cd
        # 3. close_reason_ds
        # 4. close_subreason_ds
        # MAPPINGS can be found https://kalepa.atlassian.net/browse/ENG-23573
        other_stages_array = [self.close_disposition_cd, self.close_reason_ds, self.close_subreason_ds]

        if self.submission_status_ds in {
            NationwideBossSubmissionStatusDS.ACTIVE,
            NationwideBossSubmissionStatusDS.COMPLETED,
        }:
            return ("Active",)

        if self.submission_status_ds == NationwideBossSubmissionStatusDS.BOUND_NOT_SENT:
            return ("Bound - Not Sent",)

        if self.submission_status_ds == NationwideBossSubmissionStatusDS.BOUND_SENT:
            return ("Bound-Sent",)

        if (
            self.submission_status_ds == NationwideBossSubmissionStatusDS.IN_PROCESS
            and self.close_disposition_cd is None
        ):
            return ("In Review",)

        if self.submission_status_ds == NationwideBossSubmissionStatusDS.QUOTED and self.close_disposition_cd is None:
            return ("Quoted",)

        if self.submission_status_ds in {
            NationwideBossSubmissionStatusDS.ACTIVE,
            NationwideBossSubmissionStatusDS.COMPLETED,
        } and all(stage is not None for stage in other_stages_array):
            return tuple(["Closed"] + [stage.value for stage in other_stages_array])

        if self.submission_status_ds in {
            NationwideBossSubmissionStatusDS.IN_PROCESS,
            NationwideBossSubmissionStatusDS.QUOTED,
        } and self.close_disposition_cd in {
            NationwideBossCloseDispositionCD.BLOCKED,
            NationwideBossCloseDispositionCD.RELEASED,
        }:
            return tuple(["Closed", self.close_disposition_cd.value, "Declined", "Other"])

        if self.submission_status_ds == NationwideBossSubmissionStatusDS.RENEWED and all(
            stage is None for stage in other_stages_array
        ):
            return ("Active",)

        logger.error(
            "Failed to find client stage tags for NationwideBossExportRow",
            submission_status_ds=self.submission_status_ds,
            close_disposition_cd=self.close_disposition_cd,
            close_reason_ds=self.close_reason_ds,
            close_subreason_ds=self.close_subreason_ds,
        )
        return tuple()
