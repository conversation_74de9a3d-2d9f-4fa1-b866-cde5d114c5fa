from enum import Enum


class StepFunctionName(Enum):
    YELP_SCRAPER = "ingestion-scrape-all-yelp"
    TRIP_SCRAPER = "ingestion-scrape-all-tripadvisor"
    GOOGLE_LOCAL_SCRAPER = "ingestion-scrape-all-google-local"
    FACEBOOK_SCRAPER = "ingestion-scrape-all-facebook"
    BUILDZOOM_SCRAPER = "ingestion-scrape-all-buildzoom"
    BETTER_BUSINESS_BUREAU_SCRAPER = "ingestion-scrape-all-better-business-bureau"
    THOMASNET_SCRAPER = "ingestion-scrape-all-thomasnet"
    IQS_SCRAPER = "ingestion-scrape-all-iqs"
    HOUZZ_SCRAPER = "ingestion-scrape-all-houzz"
    FMCSA_SCRAPER = "ingestion-scrape-all-fmcsa"
    SAFER_FMCSA_SCRAPER = "ingestion-scrape-safer-fmcsa"
    GOOD_JOBS_FIRST_SCRAPER = "ingestion-scrape-all-good-jobs-first"
    EPA_SCRAPER = "ingestion-scrape-all-epa"
    OSHA_SCRAPER = "ingestion-scrape-all-osha-inspections"
    FDA_SCRAPER = "ingestion-scrape-all-fda"
    UNICOURT_SCRAPER = "ingestion-scrape-all-unicourt"
    SAM_SCRAPER = "ingestion-scrape-sam"
    APARTMENTS_SCRAPER = "ingestion-scrape-apartments"
    OPENCORPORATES_SCRAPER = "ingestion-scrape-batch-opencorporates"
    ERISA_5500_SCRAPER = "ingestion-scrape-erisa5500"
    IRS_FORM_990_SCRAPER = "ingestion-scrape-irs-form990"
    CA_CSLB_SCRAPER = "ingestion-scrape-cacslb"

    def get_name(self) -> str:
        return self.value
