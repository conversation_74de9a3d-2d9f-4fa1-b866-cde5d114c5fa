import os
from enum import Enum
from typing import List

from infrastructure_services_common.secrets_vault import (
    Secret,
    SecretResolver,
    SecretsMixin,
    aws_secrets_manager,
    aws_secrets_manager_json,
    resolve_and_cache_secrets,
)

DB_CONFIG_SECRET_VAR_NAME = "DB_CONFIG_SECRET"
FACTS_DB_CONFIG_SECRET_VAR_NAME = "FACTS_DB_CONFIG_SECRET"
ERS_DB_CONFIG_SECRET_VAR_NAME = "ERS_DB_CONFIG_SECRET"
CAPI_DB_CONFIG_SECRET_VAR_NAME = "CAPI_DB_CONFIG_SECRET"
FDA_SECRET_VAR_NAME = "FDA_SECRET"
# This is secret that keeps data required to connect to Redshift (on prod account) using SSH tunnel running
# through proxied-ingress network load balancer on prod account. This is for dev, stage but also prod when they
# want to connect to Redshift using SqlAlchemy tools.
REDSHIFT_SSH_TUNNELED_CONFIG_SECRET_VAR_NAME = "REDSHIFT_SSH_TUNNELED_CONFIG_SECRET"
GMAIL_CREDENTIALS_VAR_NAME = "GMAIL_CREDENTIALS"
REDSHIFT_CONFIG_SECRET_VAR_NAME = "REDSHIFT_CONFIG_SECRET"
SLACK_SECRET_VAR_NAME = "SLACK_SECRET"
ARCH_API_INTEGRATION_SECRET_VAR_NAME = "ARCH_API_INTEGRATION_SECRET"


class Secrets(SecretsMixin, Enum):
    PRIMARY_DB_HOST = (Secret(name="PrimaryDbHost", env_var=DB_CONFIG_SECRET_VAR_NAME, key="host"),)
    PRIMARY_DB_PORT = (Secret(name="PrimaryDbPort", env_var=DB_CONFIG_SECRET_VAR_NAME, key="port"),)
    PRIMARY_DB_USER = (Secret(name="PrimaryDbUser", env_var=DB_CONFIG_SECRET_VAR_NAME, key="username"),)
    PRIMARY_DB_PASSWORD = (
        Secret(
            name="PrimaryDbPassword",
            env_var=DB_CONFIG_SECRET_VAR_NAME,
            key="password",
            optional=True,  # If missing, RDS IaM Authentication will be used
        ),
    )
    PRIMARY_DB_DATABASE_NAME = (Secret(name="PrimaryDbDatabaseName", env_var=DB_CONFIG_SECRET_VAR_NAME, key="dbname"),)
    PROXY = (Secret(name="Proxy", env_var="PROXY", optional=True),)
    PREMIUM_PROXY = (Secret(name="PremiumProxy", env_var="PREMIUM_PROXY", optional=True),)
    RESIDENTIAL_PROXY = (Secret(name="ResidentialProxy", env_var="RESIDENTIAL_PROXY", optional=True),)
    GOOGLE_PREMIUM_PROXY = (Secret(name="GooglePremiumProxy", env_var="GOOGLE_PREMIUM_PROXY", optional=True),)
    BUILDZOOM_PREMIUM_PROXY = (Secret(name="BuildZoomPremiumProxy", env_var="BUILDZOOM_PREMIUM_PROXY", optional=True),)
    BBB_PROXY = (Secret(name="BBBProxy", env_var="BBB_PROXY", optional=True),)
    TA_PROXY = (Secret(name="TAProxy", env_var="TA_PROXY", optional=True),)
    FACEBOOK_PROXY = (Secret(name="FacebookProxy", env_var="FACEBOOK_PROXY", optional=True),)
    FMCSA_PROXY = (Secret(name="FMCSAProxy", env_var="FMCSA_PROXY", optional=True),)
    FMCSA_API_KEY = (Secret(name="FMCSAApiKey", env_var="FMCSA_API_KEY", optional=True),)
    UNICOURT_PROXY = (Secret(name="UnicourtProxy", env_var="UNICOURT_PROXY", optional=True),)
    SERPER_API_KEY = (Secret(name="SerperApiKey", env_var="SERPER_API_KEY", optional=True),)
    LUMINATI_API_TOKEN = (Secret(name="LuminatiApiToken", env_var="LUMINATI_API_TOKEN", optional=True),)
    SENDGRID_API_KEY = (Secret(name="SendGridApiKey", env_var="SENDGRID_API_KEY", optional=True),)
    BETTER_BUSINESS_BUREAU_CUSTOM_SEARCH_ID = (
        Secret(
            name="BetterBusinessBureauCustomSearchId", env_var="BETTER_BUSINESS_BUREAU_CUSTOM_SEARCH_ID", optional=True
        ),
    )
    SCALE_SERP_API_KEY = (Secret(name="ScaleSerpApiKey", env_var="SCALE_SERP_API_KEY", optional=True),)
    GOOGLE_API_KEY = (Secret(name="GoogleApiKey", env_var="GOOGLE_API_KEY", optional=True),)
    SAM_API_KEY = (Secret(name="SamApiKey", env_var="SAM_API_KEY", optional=True),)
    TRIPADVISOR_CUSTOM_SEARCH_ID = (
        Secret(name="TripAdvisorCustomSearchId", env_var="TRIPADVISOR_CUSTOM_SEARCH_ID", optional=True),
    )
    YELP_CUSTOM_SEARCH_ID = (Secret(name="YelpCustomSearchId", env_var="YELP_CUSTOM_SEARCH_ID", optional=True),)
    BUILDZOOM_CUSTOM_SEARCH_ID = (
        Secret(name="BuildZoomCustomSearchId", env_var="BUILDZOOM_CUSTOM_SEARCH_ID", optional=True),
    )
    THOMASNET_CUSTOM_SEARCH_ID = (
        Secret(name="ThomasnetCustomSearchId", env_var="THOMASNET_CUSTOM_SEARCH_ID", optional=True),
    )
    IQS_CUSTOM_SEARCH_ID = (Secret(name="IQSCustomSearchId", env_var="IQS_CUSTOM_SEARCH_ID", optional=True),)
    HOUZZ_CUSTOM_SEARCH_ID = (Secret(name="HouzzCustomSearchId", env_var="HOUZZ_CUSTOM_SEARCH_ID", optional=True),)
    GOOD_JOBS_FIRST_CUSTOM_SEARCH_ID = (
        Secret(name="GoodJobsFirstCustomSearchId", env_var="GOOD_JOBS_FIRST_CUSTOM_SEARCH_ID", optional=True),
    )
    FMCSA_SMS_CUSTOM_SEARCH_ID = (
        Secret(name="FMCSASmsCustomSearchId", env_var="FMCSA_SMS_CUSTOM_SEARCH_ID", optional=True),
    )
    SAFER_FMCSA_CUSTOM_SEARCH_ID = (
        Secret(name="SaferFMCSACustomSearchId", env_var="SAFER_FMCSA_CUSTOM_SEARCH_ID", optional=True),
    )
    UNICOURT_CUSTOM_SEARCH_ID = (
        Secret(name="UnicourtCustomSearchId", env_var="UNICOURT_CUSTOM_SEARCH_ID", optional=True),
    )
    FDA_KEY = (Secret(name="FDAKey", env_var=FDA_SECRET_VAR_NAME, key="key", optional=True),)
    FDA_USER = (Secret(name="FDAUser", env_var=FDA_SECRET_VAR_NAME, key="user", optional=True),)
    FDA_ENFORCEMENT_KEY = (
        Secret(name="FDAEnforcementKey", env_var=FDA_SECRET_VAR_NAME, key="enforcement_key", optional=True),
    )
    FDA_ENFORCEMENT_USER = (
        Secret(name="FDAEnforcementUser", env_var=FDA_SECRET_VAR_NAME, key="enforcement_user", optional=True),
    )
    OSHA_API_V4_KEY = (Secret(name="OSHAApiV4Key", env_var="OSHA_API_V4_KEY_ARN", optional=True),)
    OSHA_BATCH_API_V4_KEY = (Secret(name="OSHABatchApiV4Key", env_var="OSHA_BATCH_API_V4_KEY_ARN", optional=True),)
    FACTS_DB_USER = (
        Secret(name="FactsDbUser", env_var=FACTS_DB_CONFIG_SECRET_VAR_NAME, key="username", optional=True),
    )
    FACTS_DB_PASSWORD = (
        Secret(name="FactsDbPassword", env_var=FACTS_DB_CONFIG_SECRET_VAR_NAME, key="password", optional=True),
    )
    FACTS_DB_HOST = (Secret(name="FactsDbHost", env_var=FACTS_DB_CONFIG_SECRET_VAR_NAME, key="host", optional=True),)
    FACTS_DB_PORT = (Secret(name="FactsDbPort", env_var=FACTS_DB_CONFIG_SECRET_VAR_NAME, key="port", optional=True),)
    FACTS_DB_NAME = (Secret(name="FactsDbName", env_var=FACTS_DB_CONFIG_SECRET_VAR_NAME, key="dbname", optional=True),)
    ERS_DB_USER = (Secret(name="ErsDbUser", env_var=ERS_DB_CONFIG_SECRET_VAR_NAME, key="username", optional=True),)
    ERS_DB_PASSWORD = (
        Secret(name="ErsDbPassword", env_var=ERS_DB_CONFIG_SECRET_VAR_NAME, key="password", optional=True),
    )
    ERS_DB_HOST = (Secret(name="ErsDbHost", env_var=ERS_DB_CONFIG_SECRET_VAR_NAME, key="host", optional=True),)
    ERS_DB_PORT = (Secret(name="ErsDbPort", env_var=ERS_DB_CONFIG_SECRET_VAR_NAME, key="port", optional=True),)
    ERS_DB_NAME = (Secret(name="ErsDbName", env_var=ERS_DB_CONFIG_SECRET_VAR_NAME, key="dbname", optional=True),)
    CAPI_DB_USER = (Secret(name="CapiDbUser", env_var=CAPI_DB_CONFIG_SECRET_VAR_NAME, key="username", optional=True),)
    CAPI_DB_PASSWORD = (
        Secret(name="CapiDbPassword", env_var=CAPI_DB_CONFIG_SECRET_VAR_NAME, key="password", optional=True),
    )
    CAPI_DB_HOST = (Secret(name="CapiDbHost", env_var=CAPI_DB_CONFIG_SECRET_VAR_NAME, key="host", optional=True),)
    CAPI_DB_PORT = (Secret(name="CapiDbPort", env_var=CAPI_DB_CONFIG_SECRET_VAR_NAME, key="port", optional=True),)
    CAPI_DB_NAME = (Secret(name="CapiDbName", env_var=CAPI_DB_CONFIG_SECRET_VAR_NAME, key="dbname", optional=True),)
    REDSHIFT_SSH_TUNNELED_HOST = (
        Secret(
            name="RedshiftSshTunneledHost",
            env_var=REDSHIFT_SSH_TUNNELED_CONFIG_SECRET_VAR_NAME,
            key="host",
            optional=True,
        ),
    )
    REDSHIFT_SSH_TUNNELED_PORT = (
        Secret(
            name="RedshiftSshTunneledPort",
            env_var=REDSHIFT_SSH_TUNNELED_CONFIG_SECRET_VAR_NAME,
            key="port",
            optional=True,
        ),
    )
    REDSHIFT_SSH_TUNNELED_USER = (
        Secret(
            name="RedshiftSshTunneledUser",
            env_var=REDSHIFT_SSH_TUNNELED_CONFIG_SECRET_VAR_NAME,
            key="username",
            optional=True,
        ),
    )
    REDSHIFT_SSH_TUNNELED_PASSWORD = (
        Secret(
            name="RedshiftSshTunneledPassword",
            env_var=REDSHIFT_SSH_TUNNELED_CONFIG_SECRET_VAR_NAME,
            key="password",
            optional=True,
        ),
    )
    REDSHIFT_SSH_TUNNELED_DATABASE_NAME = (
        Secret(
            name="RedshiftSshTunneledDatabaseName",
            env_var=REDSHIFT_SSH_TUNNELED_CONFIG_SECRET_VAR_NAME,
            key="dbname",
            optional=True,
        ),
    )
    REDSHIFT_SSH_TUNNELED_PRIVATE_SSH_KEY = (
        Secret(
            name="RedshiftSshTunneledPrivateSshKey",
            env_var=REDSHIFT_SSH_TUNNELED_CONFIG_SECRET_VAR_NAME,
            key="ssh_tunnel_key",
            optional=True,
        ),
    )
    REDSHIFT_HOST = (Secret(name="RedshiftHost", env_var=REDSHIFT_CONFIG_SECRET_VAR_NAME, key="host", optional=True),)
    REDSHIFT_PORT = (Secret(name="RedshiftPort", env_var=REDSHIFT_CONFIG_SECRET_VAR_NAME, key="port", optional=True),)
    REDSHIFT_USER = (
        Secret(name="RedshiftUser", env_var=REDSHIFT_CONFIG_SECRET_VAR_NAME, key="username", optional=True),
    )
    REDSHIFT_PASSWORD = (
        Secret(name="RedshiftPassword", env_var=REDSHIFT_CONFIG_SECRET_VAR_NAME, key="password", optional=True),
    )
    REDSHIFT_DATABASE_NAME = (
        Secret(name="RedshiftDatabaseName", env_var=REDSHIFT_CONFIG_SECRET_VAR_NAME, key="database", optional=True),
    )
    SLACK_TOKEN = (Secret(name="SlackToken", env_var=SLACK_SECRET_VAR_NAME, key="token", optional=True),)
    ARCH_API_BASE_URL = (
        Secret(name="BaseURL", env_var=ARCH_API_INTEGRATION_SECRET_VAR_NAME, key="base_url", optional=True),
    )
    ARCH_API_TOKEN_URL = (
        Secret(name="TokenURL", env_var=ARCH_API_INTEGRATION_SECRET_VAR_NAME, key="token_url", optional=True),
    )
    ARCH_API_CLIENT_ID = (
        Secret(name="ClientId", env_var=ARCH_API_INTEGRATION_SECRET_VAR_NAME, key="client_id", optional=True),
    )
    ARCH_API_CLIENT_SECRET = (
        Secret(name="ClientSecret", env_var=ARCH_API_INTEGRATION_SECRET_VAR_NAME, key="client_secret", optional=True),
    )
    ARCH_API_AUDIENCE = (
        Secret(name="Audience", env_var=ARCH_API_INTEGRATION_SECRET_VAR_NAME, key="audience", optional=True),
    )
    LAUNCH_DARKLY_API_KEY = (
        Secret(name="LaunchDarklyApiKey", env_var="LAUNCH_DARKLY_API_KEY_SECRET_ARN", optional=True),
    )

    @staticmethod
    def resolve_secrets_from_secrets_vault() -> None:
        secret_resolvers: List[SecretResolver] = [
            aws_secrets_manager_json(
                DB_CONFIG_SECRET_VAR_NAME,
                Secrets.PRIMARY_DB_HOST.secret(),
                Secrets.PRIMARY_DB_PORT.secret(),
                Secrets.PRIMARY_DB_USER.secret(),
                Secrets.PRIMARY_DB_PASSWORD.secret(),
                Secrets.PRIMARY_DB_DATABASE_NAME.secret(),
            ),
            aws_secrets_manager(Secrets.PROXY.secret()),
            aws_secrets_manager(Secrets.PREMIUM_PROXY.secret()),
            aws_secrets_manager(Secrets.RESIDENTIAL_PROXY.secret()),
            aws_secrets_manager(Secrets.GOOGLE_PREMIUM_PROXY.secret()),
            aws_secrets_manager(Secrets.BUILDZOOM_PREMIUM_PROXY.secret()),
            aws_secrets_manager(Secrets.BBB_PROXY.secret()),
            aws_secrets_manager(Secrets.TA_PROXY.secret()),
            aws_secrets_manager(Secrets.FACEBOOK_PROXY.secret()),
            aws_secrets_manager(Secrets.FMCSA_PROXY.secret()),
            aws_secrets_manager(Secrets.UNICOURT_PROXY.secret()),
            aws_secrets_manager(Secrets.LUMINATI_API_TOKEN.secret()),
            aws_secrets_manager(Secrets.SENDGRID_API_KEY.secret()),
            aws_secrets_manager(Secrets.BETTER_BUSINESS_BUREAU_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.SCALE_SERP_API_KEY.secret()),
            aws_secrets_manager(Secrets.GOOGLE_API_KEY.secret()),
            aws_secrets_manager(Secrets.FMCSA_API_KEY.secret()),
            aws_secrets_manager(Secrets.SERPER_API_KEY.secret()),
            aws_secrets_manager(Secrets.SAM_API_KEY.secret()),
            aws_secrets_manager(Secrets.TRIPADVISOR_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.YELP_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.BUILDZOOM_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.THOMASNET_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.IQS_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.HOUZZ_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.FMCSA_SMS_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.SAFER_FMCSA_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.UNICOURT_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.GOOD_JOBS_FIRST_CUSTOM_SEARCH_ID.secret()),
            aws_secrets_manager(Secrets.LAUNCH_DARKLY_API_KEY.secret()),
            aws_secrets_manager_json(
                FDA_SECRET_VAR_NAME,
                Secrets.FDA_KEY.secret(),
                Secrets.FDA_USER.secret(),
                Secrets.FDA_ENFORCEMENT_KEY.secret(),
                Secrets.FDA_ENFORCEMENT_USER.secret(),
            ),
            aws_secrets_manager(Secrets.OSHA_API_V4_KEY.secret()),
            aws_secrets_manager(Secrets.OSHA_BATCH_API_V4_KEY.secret()),
            aws_secrets_manager_json(
                FACTS_DB_CONFIG_SECRET_VAR_NAME,
                Secrets.FACTS_DB_USER.secret(),
                Secrets.FACTS_DB_PASSWORD.secret(),
                Secrets.FACTS_DB_HOST.secret(),
                Secrets.FACTS_DB_PORT.secret(),
                Secrets.FACTS_DB_NAME.secret(),
            ),
            aws_secrets_manager_json(
                ERS_DB_CONFIG_SECRET_VAR_NAME,
                Secrets.ERS_DB_USER.secret(),
                Secrets.ERS_DB_PASSWORD.secret(),
                Secrets.ERS_DB_HOST.secret(),
                Secrets.ERS_DB_PORT.secret(),
                Secrets.ERS_DB_NAME.secret(),
            ),
            aws_secrets_manager_json(
                CAPI_DB_CONFIG_SECRET_VAR_NAME,
                Secrets.CAPI_DB_USER.secret(),
                Secrets.CAPI_DB_PASSWORD.secret(),
                Secrets.CAPI_DB_HOST.secret(),
                Secrets.CAPI_DB_PORT.secret(),
                Secrets.CAPI_DB_NAME.secret(),
            ),
            aws_secrets_manager_json(
                REDSHIFT_SSH_TUNNELED_CONFIG_SECRET_VAR_NAME,
                Secrets.REDSHIFT_SSH_TUNNELED_HOST.secret(),
                Secrets.REDSHIFT_SSH_TUNNELED_PORT.secret(),
                Secrets.REDSHIFT_SSH_TUNNELED_USER.secret(),
                Secrets.REDSHIFT_SSH_TUNNELED_PASSWORD.secret(),
                Secrets.REDSHIFT_SSH_TUNNELED_DATABASE_NAME.secret(),
                Secrets.REDSHIFT_SSH_TUNNELED_PRIVATE_SSH_KEY.secret(),
            ),
            aws_secrets_manager_json(
                REDSHIFT_CONFIG_SECRET_VAR_NAME,
                Secrets.REDSHIFT_HOST.secret(),
                Secrets.REDSHIFT_PORT.secret(),
                Secrets.REDSHIFT_USER.secret(),
                Secrets.REDSHIFT_PASSWORD.secret(),
                Secrets.REDSHIFT_DATABASE_NAME.secret(),
            ),
            aws_secrets_manager_json(SLACK_SECRET_VAR_NAME, Secrets.SLACK_TOKEN.secret()),
            aws_secrets_manager_json(
                GMAIL_CREDENTIALS_VAR_NAME,
                EmailsProcessorSecrets.NW_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.NW_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.NW_ML_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.NW_ML_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ARCH_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ARCH_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.MUNICHRE_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.MUNICHRE_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.KIS_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.KIS_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.KALEPA_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.KALEPA_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.NORTHSTARMUTUAL_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.NORTHSTARMUTUAL_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.NECSPECIALTY_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.NECSPECIALTY_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.KALEPA_NEW_DEMO_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.KALEPA_NEW_DEMO_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ALLY_AUTO_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ALLY_AUTO_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ADMIRAL_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ADMIRAL_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ADMIRAL_TEST_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ADMIRAL_TEST_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_WC_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_WC_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.QA_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.QA_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.BOWHEAD_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.BOWHEAD_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.BOWHEAD_TEST_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.BOWHEAD_TEST_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.MERCHANTSGROUP_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.MERCHANTSGROUP_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.MERCHANTSGROUP_STAFF_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.MERCHANTSGROUP_STAFF_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_PSP_E3_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_PSP_E3_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.BISHOP_CONIFER_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.BISHOP_CONIFER_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ARU_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ARU_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ISC_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ISC_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_ES_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_ES_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.MARKEL_DEMO_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.MARKEL_DEMO_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.INGESTION_DEMO_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.INGESTION_DEMO_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ISC_PRE_RENEWALS_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ISC_PRE_RENEWALS_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.CNA_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.CNA_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.SECURA_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.SECURA_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.VIVERE_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.VIVERE_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.AIG_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.AIG_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_TRIDENT_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_TRIDENT_GMAIL_APP_KEY.secret(),
            ),
            aws_secrets_manager_json(
                ARCH_API_INTEGRATION_SECRET_VAR_NAME,
                Secrets.ARCH_API_BASE_URL.secret(),
                Secrets.ARCH_API_TOKEN_URL.secret(),
                Secrets.ARCH_API_CLIENT_ID.secret(),
                Secrets.ARCH_API_CLIENT_SECRET.secret(),
                Secrets.ARCH_API_AUDIENCE.secret(),
            ),
        ]
        resolve_and_cache_secrets(*secret_resolvers, debug_logging_enabled=False)


class EmailsProcessorSecrets(SecretsMixin, Enum):
    NW_GMAIL_ACCOUNT = (
        Secret(name="NWGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="nw_gmail_account", optional=True),
    )
    NW_GMAIL_APP_KEY = (
        Secret(name="NWGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="nw_gmail_app_key", optional=True),
    )
    NW_ML_GMAIL_ACCOUNT = (
        Secret(
            name="NWMLGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="nationwide_ml_gmail_account", optional=True
        ),
    )
    NW_ML_GMAIL_APP_KEY = (
        Secret(
            name="NWMLGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="nationwide_ml_gmail_app_key", optional=True
        ),
    )
    ARCH_GMAIL_ACCOUNT = (
        Secret(name="ArchGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="arch_gmail_account", optional=True),
    )
    ARCH_GMAIL_APP_KEY = (
        Secret(name="ArchGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="arch_gmail_app_key", optional=True),
    )
    PARAGON_GMAIL_ACCOUNT = (
        Secret(
            name="ParagonGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="paragon_gmail_account", optional=True
        ),
    )
    PARAGON_GMAIL_APP_KEY = (
        Secret(
            name="ParagonGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="paragon_gmail_app_key", optional=True
        ),
    )
    MUNICHRE_GMAIL_ACCOUNT = (
        Secret(
            name="MunichreGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="munichre_gmail_account", optional=True
        ),
    )
    MUNICHRE_GMAIL_APP_KEY = (
        Secret(
            name="MunichreGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="munichre_gmail_app_key", optional=True
        ),
    )
    KIS_GMAIL_ACCOUNT = (
        Secret(name="KISGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="kis_gmail_account", optional=True),
    )
    KIS_GMAIL_APP_KEY = (
        Secret(name="KISGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="kis_gmail_app_key", optional=True),
    )
    KALEPA_GMAIL_ACCOUNT = (
        Secret(name="KalepaGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="kalepa_gmail_account", optional=True),
    )
    KALEPA_GMAIL_APP_KEY = (
        Secret(name="KalepaGmailAppKey", env_var=GMAIL_CREDENTIALS_VAR_NAME, key="kalepa_gmail_app_key", optional=True),
    )
    NORTHSTARMUTUAL_GMAIL_ACCOUNT = (
        Secret(
            name="NorthstarmutualGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="northstarmutual_gmail_account",
            optional=True,
        ),
    )
    NORTHSTARMUTUAL_GMAIL_APP_KEY = (
        Secret(
            name="NorthstarmutualGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="northstarmutual_gmail_app_key",
            optional=True,
        ),
    )
    NECSPECIALTY_GMAIL_ACCOUNT = (
        Secret(
            name="NecSpecialtyGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="necspecialty_gmail_account",
            optional=True,
        ),
    )
    NECSPECIALTY_GMAIL_APP_KEY = (
        Secret(
            name="NecSpecialtyGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="necspecialty_gmail_app_key",
            optional=True,
        ),
    )
    KALEPA_NEW_DEMO_GMAIL_ACCOUNT = (
        Secret(
            name="KalepaNewDemoGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="kalepa_new_demo_gmail_account",
            optional=True,
        ),
    )
    KALEPA_NEW_DEMO_GMAIL_APP_KEY = (
        Secret(
            name="KalepaNewDemoGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="kalepa_new_demo_gmail_app_key",
            optional=True,
        ),
    )
    ALLY_AUTO_GMAIL_ACCOUNT = (
        Secret(
            name="AllyAutoGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="ally_auto_gmail_account",
            optional=True,
        ),
    )
    ALLY_AUTO_GMAIL_APP_KEY = (
        Secret(
            name="AllyAutoGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="ally_auto_gmail_app_key",
            optional=True,
        ),
    )
    ADMIRAL_GMAIL_ACCOUNT = (
        Secret(
            name="AdmiralGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="admiral_gmail_account",
            optional=True,
        ),
    )
    ADMIRAL_GMAIL_APP_KEY = (
        Secret(
            name="AdmiralGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="admiral_gmail_app_key",
            optional=True,
        ),
    )
    ADMIRAL_TEST_GMAIL_ACCOUNT = (
        Secret(
            name="AdmiralTestGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="admiral_test_gmail_account",
            optional=True,
        ),
    )
    ADMIRAL_TEST_GMAIL_APP_KEY = (
        Secret(
            name="AdmiralTestGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="admiral_test_gmail_app_key",
            optional=True,
        ),
    )
    PARAGON_WC_GMAIL_ACCOUNT = (
        Secret(
            name="ParagonWCGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="paragon_wc_gmail_account",
            optional=True,
        ),
    )
    PARAGON_WC_GMAIL_APP_KEY = (
        Secret(
            name="ParagonWCGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="paragon_wc_gmail_app_key",
            optional=True,
        ),
    )
    QA_GMAIL_ACCOUNT = (
        Secret(
            name="QualityAuditGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="quality_audit_gmail_account",
            optional=True,
        ),
    )
    QA_GMAIL_APP_KEY = (
        Secret(
            name="QualityAuditGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="quality_audit_gmail_app_key",
            optional=True,
        ),
    )
    BOWHEAD_GMAIL_ACCOUNT = (
        Secret(
            name="BowheadGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="bowhead_gmail_account",
            optional=True,
        ),
    )
    BOWHEAD_GMAIL_APP_KEY = (
        Secret(
            name="BowheadGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="bowhead_gmail_app_key",
            optional=True,
        ),
    )
    BOWHEAD_TEST_GMAIL_ACCOUNT = (
        Secret(
            name="BowheadTestGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="bowhead_test_gmail_account",
            optional=True,
        ),
    )
    BOWHEAD_TEST_GMAIL_APP_KEY = (
        Secret(
            name="BowheadTestGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="bowhead_test_gmail_app_key",
            optional=True,
        ),
    )
    MERCHANTSGROUP_GMAIL_ACCOUNT = (
        Secret(
            name="MerchantsGroupGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="merchantsgroup_gmail_account",
            optional=True,
        ),
    )
    MERCHANTSGROUP_GMAIL_APP_KEY = (
        Secret(
            name="MerchantsGroupGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="merchantsgroup_gmail_app_key",
            optional=True,
        ),
    )
    MERCHANTSGROUP_STAFF_GMAIL_ACCOUNT = (
        Secret(
            name="MerchantsGroupStaffGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="merchants_staff_gmail_account",
            optional=True,
        ),
    )
    MERCHANTSGROUP_STAFF_GMAIL_APP_KEY = (
        Secret(
            name="MerchantsGroupStaffGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="merchants_staff_gmail_app_key",
            optional=True,
        ),
    )
    PARAGON_PSP_E3_GMAIL_ACCOUNT = (
        Secret(
            name="ParagonPSPE3GmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="paragon_pspe3_gmail_account",
            optional=True,
        ),
    )
    PARAGON_PSP_E3_GMAIL_APP_KEY = (
        Secret(
            name="ParagonPSPE3GmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="paragon_pspe3_gmail_app_key",
            optional=True,
        ),
    )
    BISHOP_CONIFER_GMAIL_ACCOUNT = (
        Secret(
            name="BishopConiferGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="bishop_conifer_gmail_account",
            optional=True,
        ),
    )
    BISHOP_CONIFER_GMAIL_APP_KEY = (
        Secret(
            name="BishopConiferGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="bishop_conifer_gmail_app_key",
            optional=True,
        ),
    )
    ARU_GMAIL_ACCOUNT = (
        Secret(
            name="AruGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="aru_gmail_account",
            optional=True,
        ),
    )
    ARU_GMAIL_APP_KEY = (
        Secret(
            name="AruGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="aru_gmail_app_key",
            optional=True,
        ),
    )
    ISC_GMAIL_ACCOUNT = (
        Secret(
            name="IscGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="isc_gmail_account",
            optional=True,
        ),
    )
    ISC_GMAIL_APP_KEY = (
        Secret(
            name="IscGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="isc_gmail_app_key",
            optional=True,
        ),
    )
    PARAGON_ES_GMAIL_ACCOUNT = (
        Secret(
            name="ParagonESGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="paragon_es_gmail_account",
            optional=True,
        ),
    )
    PARAGON_ES_GMAIL_APP_KEY = (
        Secret(
            name="ParagonESGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="paragon_es_gmail_app_key",
            optional=True,
        ),
    )
    MARKEL_DEMO_GMAIL_ACCOUNT = (
        Secret(
            name="MarkelDemoGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="markel_demo_gmail_account",
            optional=True,
        ),
    )
    MARKEL_DEMO_GMAIL_APP_KEY = (
        Secret(
            name="MarkelDemoGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="markel_demo_gmail_app_key",
            optional=True,
        ),
    )
    INGESTION_DEMO_GMAIL_ACCOUNT = (
        Secret(
            name="IngestionDemoGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="ingestion_demo_gmail_account",
            optional=True,
        ),
    )
    INGESTION_DEMO_GMAIL_APP_KEY = (
        Secret(
            name="IngestionDemoGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="ingestion_demo_gmail_app_key",
            optional=True,
        ),
    )
    ISC_PRE_RENEWALS_GMAIL_ACCOUNT = (
        Secret(
            name="IscPreRenewalsGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="isc_pre_renewals_gmail_account",
            optional=True,
        ),
    )
    ISC_PRE_RENEWALS_GMAIL_APP_KEY = (
        Secret(
            name="IscPreRenewalsGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="isc_pre_renewals_gmail_app_key",
            optional=True,
        ),
    )
    CNA_GMAIL_ACCOUNT = (
        Secret(
            name="CnaGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="cna_gmail_account",
            optional=True,
        ),
    )
    CNA_GMAIL_APP_KEY = (
        Secret(
            name="CnaGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="cna_gmail_app_key",
            optional=True,
        ),
    )
    SECURA_GMAIL_ACCOUNT = (
        Secret(
            name="SecuraGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="secura_gmail_account",
            optional=True,
        ),
    )
    SECURA_GMAIL_APP_KEY = (
        Secret(
            name="SecuraGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="secura_gmail_app_key",
            optional=True,
        ),
    )
    VIVERE_GMAIL_ACCOUNT = (
        Secret(
            name="VivereGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="vivere_gmail_account",
            optional=True,
        ),
    )
    VIVERE_GMAIL_APP_KEY = (
        Secret(
            name="VivereGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="vivere_gmail_app_key",
            optional=True,
        ),
    )
    AIG_GMAIL_ACCOUNT = (
        Secret(
            name="AigGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="aig_gmail_account",
            optional=True,
        ),
    )
    AIG_GMAIL_APP_KEY = (
        Secret(
            name="AigGmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="aig_gmail_app_key",
            optional=True,
        ),
    )
    PARAGON_TRIDENT_GMAIL_ACCOUNT = (
        Secret(
            name="ParagonTridentEmailAccount",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="paragon_trident_gmail_account",
            optional=True,
        ),
    )
    PARAGON_TRIDENT_GMAIL_APP_KEY = (
        Secret(
            name="ParagonTridentEmailAppKey",
            env_var=GMAIL_CREDENTIALS_VAR_NAME,
            key="paragon_trident_gmail_app_key",
            optional=True,
        ),
    )

    @staticmethod
    def resolve_secrets_from_secrets_vault() -> None:
        secret_resolvers: List[SecretResolver] = [
            aws_secrets_manager_json(
                GMAIL_CREDENTIALS_VAR_NAME,
                EmailsProcessorSecrets.NW_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.NW_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.NW_ML_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.NW_ML_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ARCH_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ARCH_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.MUNICHRE_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.MUNICHRE_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.KIS_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.KIS_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.KALEPA_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.KALEPA_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.NORTHSTARMUTUAL_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.NORTHSTARMUTUAL_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.NECSPECIALTY_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.NECSPECIALTY_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ADMIRAL_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ADMIRAL_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ADMIRAL_TEST_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ADMIRAL_TEST_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_WC_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_WC_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.QA_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.QA_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.BOWHEAD_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.BOWHEAD_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.BOWHEAD_TEST_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.BOWHEAD_TEST_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.MERCHANTSGROUP_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.MERCHANTSGROUP_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.MERCHANTSGROUP_STAFF_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.MERCHANTSGROUP_STAFF_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_PSP_E3_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_PSP_E3_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.BISHOP_CONIFER_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.BISHOP_CONIFER_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ARU_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ARU_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ISC_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ISC_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_ES_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_ES_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.MARKEL_DEMO_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.MARKEL_DEMO_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.INGESTION_DEMO_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.INGESTION_DEMO_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.ISC_PRE_RENEWALS_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.ISC_PRE_RENEWALS_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.CNA_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.CNA_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.SECURA_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.SECURA_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.VIVERE_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.VIVERE_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.AIG_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.AIG_GMAIL_APP_KEY.secret(),
                EmailsProcessorSecrets.PARAGON_TRIDENT_GMAIL_ACCOUNT.secret(),
                EmailsProcessorSecrets.PARAGON_TRIDENT_GMAIL_APP_KEY.secret(),
            )
        ]
        resolve_and_cache_secrets(*secret_resolvers, debug_logging_enabled=False)


def load_and_cache_secrets():
    if "LOAD_SECRETS_FROM_ENV_VARS" in os.environ and os.environ["LOAD_SECRETS_FROM_ENV_VARS"].lower() != "false":
        Secrets.resolve_secrets_from_env_vars()
    else:
        Secrets.resolve_secrets_from_secrets_vault()
