import datetime
from abc import ABC, abstractmethod
from dataclasses import dataclass
from functools import cached_property
from uuid import uuid4

from events_common.model.types import KalepaEvents, StepFunctionStatus
from infrastructure_common.logging import get_logger

from src.clients.lambda_client import LambdaClient
from src.clients.step_functions import StepF<PERSON>ctions<PERSON><PERSON>
from src.models.flow import RunScrapersState
from src.models.lambda_arn import LambdaARN
from src.models.sf_name import StepFunctionName

logger = get_logger()
step_functions_client = StepFunctionsClient(None)
lambda_client = LambdaClient()


@dataclass
class ScrapingExecutionMetadata:
    kalepa_id: str
    sf_execution_id: str
    selected_scrapers: dict


class IScraperRunner(ABC):
    _sf_name: StepFunctionName
    _scraper_name: str

    def __init__(self, scraper_name: str, sf_name: StepFunctionName):
        self._sf_name = sf_name
        self._scraper_name = scraper_name

    @property
    def _choice_param_name(self) -> str:
        return f"scrape_{self._scraper_name}"

    @cached_property
    def _sf_client(self) -> "StepFunctionsClient":
        return StepFunctionsClient(self._sf_name.get_name())

    @abstractmethod
    def run_scraper(self, execution_metadata: ScrapingExecutionMetadata) -> list[str] | None:
        """
        Run the scraper and return the SF execution ARN
        """
        ...


class ParamMappingScraperRunner(IScraperRunner):
    _param_map: dict[str, str]

    def __init__(self, scraper_name: str, sf_name: StepFunctionName, param_map: dict[str, str]):
        super().__init__(scraper_name, sf_name)
        self._param_map = param_map

    def run_scraper(self, execution_metadata: ScrapingExecutionMetadata) -> list[str] | None:
        should_run_scraper = execution_metadata.selected_scrapers.get(self._choice_param_name)
        if should_run_scraper is None:
            logger.error("Missing choice parameter for scraping, will skip scraper", scraper=self._scraper_name)
            return None

        if not should_run_scraper:
            return None

        scarper_input = {
            "kalepa_id": execution_metadata.kalepa_id,
            "started_by_execution_id": execution_metadata.sf_execution_id,
        }

        for orig_param_name, input_param_name in self._param_map.items():
            input_param = execution_metadata.selected_scrapers.get(orig_param_name)
            scarper_input[input_param_name] = input_param

        execution_id = str(uuid4())
        start_response = self._sf_client.invoke(scarper_input, execution_id)
        execution_arn = start_response["executionArn"]
        return [execution_arn]


class URLScraperRunner(ParamMappingScraperRunner):
    def __init__(self, scraper_name: str, sf_name: StepFunctionName):
        param_map = {f"{scraper_name}_url": "url"}

        super().__init__(scraper_name, sf_name, param_map)


class MultipleURLScraperRunner(ParamMappingScraperRunner):
    def __init__(self, scraper_name: str, sf_name: StepFunctionName):
        param_map = {f"scrape_{scraper_name}_urls": "urls"}

        super().__init__(scraper_name, sf_name, param_map)


class Erisa5500ScraperRunner(ParamMappingScraperRunner):
    def __init__(self):
        param_map = {"scrape_erisa_5500_record_ids": "record_ids"}

        super().__init__("erisa_5500", StepFunctionName.ERISA_5500_SCRAPER, param_map)


class OpenCorporatesScraperRunner(ParamMappingScraperRunner):
    def __init__(self):
        param_map = {"opencorporates_metadata": "opencorporates_sites"}

        super().__init__("opencorporates", StepFunctionName.OPENCORPORATES_SCRAPER, param_map)


SCRAPER_RUNNERS: list[IScraperRunner] = [
    URLScraperRunner("yelp", StepFunctionName.YELP_SCRAPER),
    URLScraperRunner("trip", StepFunctionName.TRIP_SCRAPER),
    URLScraperRunner("google_local", StepFunctionName.GOOGLE_LOCAL_SCRAPER),
    URLScraperRunner("facebook", StepFunctionName.FACEBOOK_SCRAPER),
    URLScraperRunner("buildzoom", StepFunctionName.BUILDZOOM_SCRAPER),
    URLScraperRunner("better_business_bureau", StepFunctionName.BETTER_BUSINESS_BUREAU_SCRAPER),
    URLScraperRunner("thomasnet", StepFunctionName.THOMASNET_SCRAPER),
    URLScraperRunner("iqs", StepFunctionName.IQS_SCRAPER),
    URLScraperRunner("houzz", StepFunctionName.HOUZZ_SCRAPER),
    URLScraperRunner("fmcsa", StepFunctionName.FMCSA_SCRAPER),
    URLScraperRunner("safer_fmcsa", StepFunctionName.SAFER_FMCSA_SCRAPER),
    URLScraperRunner("good_jobs_first", StepFunctionName.GOOD_JOBS_FIRST_SCRAPER),
    URLScraperRunner("epa", StepFunctionName.EPA_SCRAPER),
    URLScraperRunner("fda", StepFunctionName.FDA_SCRAPER),
    URLScraperRunner("sam", StepFunctionName.SAM_SCRAPER),
    URLScraperRunner("apartments", StepFunctionName.APARTMENTS_SCRAPER),
    URLScraperRunner("irs_form_990", StepFunctionName.IRS_FORM_990_SCRAPER),
    URLScraperRunner("cs_cslb", StepFunctionName.CA_CSLB_SCRAPER),
    MultipleURLScraperRunner("osha", StepFunctionName.OSHA_SCRAPER),
    MultipleURLScraperRunner("unicourt", StepFunctionName.UNICOURT_SCRAPER),
    Erisa5500ScraperRunner(),
    OpenCorporatesScraperRunner(),
]


def run_selected_scrapers(kalepa_id: str, sf_execution_id: str, selected_scrapers: dict) -> list[str]:
    execution_arns = []
    execution_metadata = ScrapingExecutionMetadata(
        kalepa_id=kalepa_id, sf_execution_id=sf_execution_id, selected_scrapers=selected_scrapers
    )

    for scraper_runner in SCRAPER_RUNNERS:
        try:
            if execution_arn := scraper_runner.run_scraper(execution_metadata):
                execution_arns.extend(execution_arn)
        except Exception:
            logger.exception("Failed to run scraper", runner=scraper_runner, execution_metadata=execution_metadata)

    return execution_arns


def update_running_arns(scrapers_state: RunScrapersState):
    new_running_arns = []

    for arn in scrapers_state.scraping_sf_arns:
        try:
            logger.info("Processing ARN", arn=arn)
            status = step_functions_client.get_run_status(arn)
            if status == "RUNNING":
                new_running_arns.append(arn)
            elif status != "SUCCEEDED":
                logger.error("Scraping failed for arn", arn=arn, status=status)
        except Exception:
            logger.exception("Failed to get status for ARN", arn=arn)

    scrapers_state.scraping_sf_arns = new_running_arns


def send_ingestion_succeeded_event(scrapers_state: RunScrapersState):
    lambda_payload = {
        "event_type": KalepaEvents.INGESTION_FINISHED,
        "time": str(datetime.datetime.now()),
        "business_id": scrapers_state.kalepa_id,
        "step_function_status": StepFunctionStatus.SUCCESS,
        "step_function_input": scrapers_state.sf_input,
        "step_function_execution_id": scrapers_state.sf_execution_id,
    }

    lambda_client.invoke_async(payload=lambda_payload, lambda_arn=LambdaARN.KALEPA_EVENT_SENDER)
