from collections import defaultdict
from dataclasses import dataclass
from typing import Callable

from infrastructure_common.logging import get_logger

from src.models.email_data import EmailData

MISSING_FILE_NAME = "missing_filename"

ISC_NEGATIVE_KEYWORDS = ["bind task", "bind request", "45-days subjectivities"]

logger = get_logger()


@dataclass
class EmailKeywords:
    body_positive_keywords: list[str] | None = None
    subject_positive_keywords: list[str] | None = None
    body_negative_keywords: list[str] | None = None
    subject_negative_keywords: list[str] | None = None

    def check_is_submission_email(
        self,
        email_subject: str,
        email_body: str,
    ) -> bool:
        if self.body_negative_keywords and any(keyword in email_body for keyword in self.body_negative_keywords):
            return False
        if self.subject_negative_keywords and any(
            keyword in email_subject for keyword in self.subject_negative_keywords
        ):
            return False
        if (
            (not self.body_positive_keywords and not self.subject_positive_keywords)
            or (self.body_positive_keywords and any(keyword in email_body for keyword in self.body_positive_keywords))
            or (
                self.subject_positive_keywords
                and any(keyword in email_subject for keyword in self.subject_positive_keywords)
            )
        ):
            return True
        return False


def is_submission_email(
    email: EmailData,
    keywords_ignore_attachments: EmailKeywords | None = None,
    keywords_no_attachments: EmailKeywords | None = None,
) -> bool:
    email_body = email.email_text_body or email.email_body
    lowered_body = email_body.lower() if email_body else ""
    lowered_subject = email.subject.lower() if email.subject else ""
    if keywords_ignore_attachments:
        if not keywords_ignore_attachments.check_is_submission_email(lowered_subject, lowered_body):
            return False
    attachments_with_names = [attachment for attachment in email.attachments if attachment.name != MISSING_FILE_NAME]
    if len(attachments_with_names) > 0 or not keywords_no_attachments:
        return True
    return keywords_no_attachments.check_is_submission_email(lowered_subject, lowered_body)


def is_iscmga_submission_email(email: EmailData) -> bool:
    log = logger.bind(filter="Is ISC submission email", subject=email.subject)

    if not is_submission_email(
        email,
        keywords_ignore_attachments=EmailKeywords(
            body_negative_keywords=ISC_NEGATIVE_KEYWORDS, subject_negative_keywords=ISC_NEGATIVE_KEYWORDS
        ),
    ):
        log.info("Skipping email that is not a submission email")
        return False
    return True


def no_filter() -> Callable:
    def f(email: EmailData) -> bool:
        return True

    return f


EMAIL_ACCOUNT_FILTERS = defaultdict(no_filter)
EMAIL_ACCOUNT_FILTERS["<EMAIL>"] = is_iscmga_submission_email
