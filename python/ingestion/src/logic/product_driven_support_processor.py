import html
import os
import re
from datetime import datetime
from typing import List, Optional
from uuid import UUID

import pytz
from copilot_client_v3 import Email, EmailResponse, Report
from infrastructure_common.logging import get_logger
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.emails import EmailType
from static_common.enums.file_type import FileType
from static_common.enums.origin import Origin

from src.clients.copilot_v3 import CopilotV3Client
from src.models.email_data import EmailAttachment, EmailData
from src.models.processed_email import ProcessedEmailAttachment
from src.schemas.processed_email import ProcessedEmailAttachmentSchema

logger = get_logger().bind(logic_module="ProductDrivenSupportProcessor")

AUTO_DECLINES = ["auto decline", "auto-decline"]
LOCK_TIMEOUT_SECONDS = 10
INBOXES_TO_REPORT_OWNERS = {
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
    "<EMAIL>": "<EMAIL>",
}

PREFIXES_TO_INBOXES = {
    "[NW]": "<EMAIL>",
    "[NW-ML]": "<EMAIL>",
    "[ARCH]": "<EMAIL>",
    "[PARAGON]": "<EMAIL>",
    "[MUNICHRE]": "<EMAIL>",
    "[NORTHSTAR]": "<EMAIL>",
    "[NECSPECIALTY]": "<EMAIL>",
    "[KALEPANEWDEMO]": "<EMAIL>",
    "[ALLYAUTO]": "<EMAIL>",
    "[ADMIRAL]": "<EMAIL>",
    "[PARAGON-WORKERS-COMP]": "<EMAIL>",
    "[QA]": "<EMAIL>",
    "[BOWHEAD]": "<EMAIL>",
    "[MERCHANTS]": "<EMAIL>",
    "[MERCHANTS-STAFF]": "<EMAIL>",
    "[PARAGON-PSP-E3]": "<EMAIL>",
    "[BISHOP-CONIFER]": "<EMAIL>",
    "[ARU]": "<EMAIL>",
    "[ISC]": "<EMAIL>",
    "[ISC-PRE-RENEWALS]": "<EMAIL>",
    "[PARAGON-ES]": "<EMAIL>",
    "[MARKEL-DEMO]": "<EMAIL>",
    "[INGESTION-DEMO]": "<EMAIL>",
    "[CNA]": "<EMAIL>",
    "[SECURA]": "<EMAIL>",
    "[VIVERE]": "<EMAIL>",
    "[AIG]": "<EMAIL>",
    "[PARAGON-TRIDENT]": "<EMAIL>",
}

NW_EMAIL_ACCOUNT = "<EMAIL>"
MUNICH_EMAIL_ACCOUNT = "<EMAIL>"
PARAGON_EMAIL_ACCOUNT = "<EMAIL>"
PRE_RENEWAL_ACCOUNTS = ["<EMAIL>"]

MALFORMED_EMAIL_DOMAINS_IN_SIGNATURES = {"burns%1ewilcox.com": "burns-wilcox.com"}


class ProductDrivenSupportProcessor:
    """Enables creating shell submission from email."""

    def __init__(
        self,
        email_data: EmailData,
        copilot_v3_client: Optional[CopilotV3Client] = None,
    ):
        self.email_data = email_data
        self.processing_email = None
        self.existing_submission_ids = []
        self.copilot_v3_client = copilot_v3_client or CopilotV3Client(os.environ["COPILOT_API_V3_URL"])

        env = os.environ["KALEPA_ENV"]
        self.is_prod = env == "prod"
        self.copilot_base_url = "https://copilot.kalepa.com" if self.is_prod else f"https://copilot.{env}.kalepa.com"
        self.log = logger.bind(
            account=self.email_data.account,
            subject=self.email_data.subject,
            thread_id=self.email_data.thread_id,
            message_id=self.email_data.message_id,
        )

        self._maybe_enrich_email_data()

    def _maybe_enrich_email_data(self):
        if self.email_data.copilot_email_id:
            if copilot_email_data := self.copilot_v3_client.get_emails(email_id=self.email_data.copilot_email_id):
                self.email_data.copilot_correspondence_id = (
                    self.email_data.copilot_correspondence_id or copilot_email_data[0].correspondence_id
                )

                if not self.email_data.references:
                    self.email_data.set_references(copilot_email_data[0].email_references)

    def run_shell_submission(self):
        if not self._should_be_processed():
            self.log.info("Skip processing new email")
            return

        self._maybe_store_thread_id()

        if self._email_was_already_processed():
            return

        # We want to be able to test the PDS flow in dev and staging for different accounts
        if not self.is_prod:
            for prefix, account in PREFIXES_TO_INBOXES.items():
                if self.email_data.subject.startswith(prefix):
                    self.log.info(
                        "Found prefix in subject, setting account",
                        prefix=prefix,
                        new_account=account,
                    )
                    self.email_data.account = account
                    self.email_data.subject = self.email_data.subject.replace(prefix, "").strip()
                    break

        if self.processing_email:
            self.log.info("Retrying processing email")
        else:
            email_response = self._store_email()
            self.processing_email = email_response.email
            self.existing_submission_ids = email_response.submission_ids

        if self.processing_email.type == EmailType.ROOT:
            self.log.info("Creating a new submission for email")
            self._run_shell_submission_on_create()
            self.log.info("Finished processing the new email")
        elif self.existing_submission_ids:
            self.log.info("Processing email for existing submissions", submission_ids=self.existing_submission_ids)
            self._run_shell_submission_on_update()
            self.log.info("Finished processing email for existing thread")

    def _maybe_store_thread_id(self):
        if self.email_data.copilot_correspondence_id:
            self.copilot_v3_client.update_correspondence_thread_id(
                correspondence_id=self.email_data.copilot_correspondence_id, thread_id=self.email_data.thread_id
            )

    def _run_shell_submission_on_create(self):
        report_owner_email = INBOXES_TO_REPORT_OWNERS[self.email_data.account]
        self._create_submission_with_attachments(report_owner_email)
        self.copilot_v3_client.update_email_as_processed(self.processing_email.id, self.processing_email.email_account)

    def _run_shell_submission_on_update(self):
        if new_attachments := self._get_new_attachments():
            for submission_id in self.existing_submission_ids:
                self._add_attachments_to_submission(submission_id, new_attachments)
        self.copilot_v3_client.update_email_as_processed(self.processing_email.id, self.processing_email.email_account)

    def _create_submission_with_attachments(self, owner_email: str) -> UUID:
        self.log.info("Creating Copilot report")
        origin = (
            Origin.SYNC
            if self.email_data.account == PARAGON_EMAIL_ACCOUNT
            and any(auto_decline in self.email_data.subject.lower() for auto_decline in AUTO_DECLINES)
            else Origin.EMAIL
        )
        is_pre_renewal = self.email_data.account in PRE_RENEWAL_ACCOUNTS
        report = self.copilot_v3_client.create_empty_report(
            owner_email=owner_email,
            report_name=self.email_data.subject,
            origin=origin,
            email_message_id=self.email_data.email_message_id,
            email_references=self.email_data.references,
            additional_data={"thread_id": self.email_data.thread_id},
            correspondence_id=self.processing_email.correspondence_id,
            is_pre_renewal=is_pre_renewal,
        )
        submission_id = report.submissions[0].id
        self.log.info(
            "Created Copilot report", report_id=report.id, submission_id=submission_id, owner_email=owner_email
        )
        self._add_attachments_to_submission(submission_id)
        self._create_pdf_with_email_body_in_submission(submission_id)
        self.copilot_v3_client.patch_report(
            report.id, Report(email_body=self.email_data.email_body, email_subject=self.email_data.subject)
        )
        return UUID(submission_id)

    def _create_pdf_with_email_body_in_submission(self, submission_id: str):
        cleaned_body = self.email_data.email_body or ""
        cleaned_body = cleaned_body.replace("height:0.0pt;", "")
        cleaned_body = cleaned_body.replace("height:0pt;", "")
        # Drop all images as we don't save them either way
        cleaned_body = re.sub("(<img.*?>)", "", cleaned_body, 0, re.IGNORECASE | re.DOTALL | re.MULTILINE)
        cleaned_body = re.sub("(<html.*?>)", "", cleaned_body, 0, re.IGNORECASE | re.DOTALL | re.MULTILINE)
        cleaned_body = re.sub("(<head.*?>)", "", cleaned_body, 0, re.IGNORECASE | re.DOTALL | re.MULTILINE)
        cleaned_body = re.sub("(</head.*?>)", "", cleaned_body, 0, re.IGNORECASE | re.DOTALL | re.MULTILINE)
        cleaned_body = re.sub(
            "(font-size:.*?p[tx])", "font-size:6pt", cleaned_body, 0, re.IGNORECASE | re.DOTALL | re.MULTILINE
        )
        for key, val in MALFORMED_EMAIL_DOMAINS_IN_SIGNATURES.items():
            cleaned_body = re.sub(key, val, cleaned_body, 0, re.IGNORECASE)
        enriched_email_body_html = f"""
            <html>
            <head>
            <style>
                @page {{
                    size: a3 portrait;
                    margin: 5mm 5mm 5mm 5mm;
                }}
                *
                {{
                   font-size: 6pt !important;
                   margin: 0.0px !important;
                   padding: 0.0px !important;
                }}
            </style>
            </head>
            <h1>{html.escape(self.email_data.subject)}</h1>
            <h2>From: {html.escape(self.email_data.email_from)}</h2>
            <h2>To: {html.escape(self.email_data.email_to or self.email_data.account)}</h2>
            {f"<h3>Cc: {html.escape(', '.join(self.email_data.email_cc))}</h3>"
                if self.email_data.email_cc else ""}
            {f"<h3>Reply-To: {html.escape(self.email_data.email_reply_to)}</h3>"
                if self.email_data.email_reply_to else ""}
            {f"<h3>Sent: {html.escape(self._convert_datetime_to_est_str(self.email_data.email_date))}</h3>"
                if self.email_data.email_date else ""}
            <br />
            <br />
            <div style="-pdf-word-wrap: CJK;">
            {cleaned_body}
            </div>
        """
        self.copilot_v3_client.create_file(
            file=enriched_email_body_html.encode("utf-8"),  # send bytes
            file_name="email_body.html",
            is_internal=True,
            submission_id=submission_id,
            file_type=FileType.EMAIL.value,
            file_classification=ClassificationDocumentType.EMAIL.value,
            convert_to_pdf=True,
            origin=Origin.EMAIL,
            additional_info={"email_id": self.processing_email.id},
        )
        self.log.info("Successfully uploaded email body", submission_id=submission_id)
        # File we create above is saved as PDF, we want to keep the original HTML as well to e.g. save the attachments
        self.copilot_v3_client.create_file(
            file=enriched_email_body_html.encode("utf-8"),  # send bytes
            file_name="email_body_html.html",
            is_internal=True,
            submission_id=submission_id,
            file_type=FileType.HTML_DOCUMENT.value,
            file_classification=ClassificationDocumentType.HTML_DOCUMENT.value,
            convert_to_pdf=False,
            origin=Origin.EMAIL,
        )
        self.log.info("Successfully uploaded email body as original HTML", submission_id=submission_id)

    @staticmethod
    def _convert_datetime_to_est_str(dt: datetime) -> str:
        return dt.astimezone(pytz.timezone("US/Eastern")).strftime("%Y-%m-%d %H:%M:%S %Z")

    def _store_email(self) -> EmailResponse:
        processed_email = Email(
            message_id=self.email_data.message_id,
            email_account=self.email_data.account,
            email_subject=self.email_data.subject,
            email_from=self.email_data.email_from,
            email_to=self.email_data.email_to or self.email_data.account,
            email_reply_to=self.email_data.email_reply_to,
            email_cc=self.email_data.email_cc,
            email_sent_at=self.email_data.email_date,
            email_body=self.email_data.email_body,
            email_attachments_count=len(self.email_data.attachments),
            attachments=ProcessedEmailAttachmentSchema().dump(
                [ProcessedEmailAttachment.from_email_attachment(att) for att in self.email_data.attachments], many=True
            ),
            is_processed=False,
        )
        return self.copilot_v3_client.store_email(email=processed_email, thread_id=self.email_data.thread_id)

    def _add_attachments_to_submission(self, submission_id: str, attachments: List[EmailAttachment] = None):
        self.log.info("Adding files to existing Copilot report")
        if not attachments:
            attachments = self.email_data.attachments

        for attachment in attachments:
            self.copilot_v3_client.create_file(
                file=attachment.content,
                file_name=attachment.name,
                submission_id=submission_id,
                file_type=FileType.UNKNOWN,
                origin=Origin.EMAIL,
            )
            self.log.info(
                "Uploaded attachment to submission", attachment_name=attachment.name, submission_id=submission_id
            )

        self.log.info("Successfully uploaded attachments to submission", submission_id=submission_id)

    def _should_be_processed(self) -> bool:
        if not self.email_data.account:
            self.log.warning("Refuse shell submission from email of unknown inbox")
            return False

        if self.email_data.account not in INBOXES_TO_REPORT_OWNERS:
            self.log.warning("Refuse shell submission from email for not supported inbox")
            return False

        if self.email_data.account == NW_EMAIL_ACCOUNT and self.email_data.subject.startswith("Co-Pilot Submissions"):
            self.log.info("Refuse shell submission for NW report")
            return False

        subject = self.email_data.subject.lower() if self.email_data.subject else ""
        if self.email_data.account == MUNICH_EMAIL_ACCOUNT:
            if "submissions_20" in subject:
                self.log.info("Refuse daily export for MunichRe", subject=subject)
                return False

        if (
            self.email_data.account == PARAGON_EMAIL_ACCOUNT
            and "Copilot could not assign UW for detected producer" in self.email_data.subject
        ):
            self.log.info("Refuse updated producer mapping reply for Paragon", subject=subject)
            return False

        self.log.info("Allow shell submission from email")
        return True

    def _email_was_already_processed(self) -> bool:
        if self.email_data.copilot_email_id and self.copilot_v3_client.get_emails(
            email_id=self.email_data.copilot_email_id
        ):
            self.log.info("Email originated from copilot and no need to process")
            return True
        if processing_emails := self.copilot_v3_client.get_emails(
            message_id=self.email_data.message_id, email_account=self.email_data.account
        ):
            self.processing_email = processing_emails[0]
            # the condition self.processing_email.is_processed is None is for backward compatibility
            if self.processing_email.is_processed is None or self.processing_email.is_processed:
                self.log.info("Email was already processed")
                return True

        return False

    def _get_new_attachments(self) -> List[EmailAttachment]:
        submission_attachments = self._get_attachments_from_submission()
        return [
            att
            for att in self.email_data.attachments
            if ProcessedEmailAttachment.from_email_attachment(att) not in submission_attachments
        ]

    def _get_attachments_from_submission(self) -> List[ProcessedEmailAttachment]:
        attachments_in_submission = set()
        for correspondence_email in self.copilot_v3_client.get_emails(
            correspondence_id=self.processing_email.correspondence_id
        ):
            if correspondence_email.attachments and correspondence_email.message_id != self.processing_email.message_id:
                attachments = ProcessedEmailAttachmentSchema().load(correspondence_email.attachments, many=True)
                attachments_in_submission.update(attachments)

        return list(attachments_in_submission)
