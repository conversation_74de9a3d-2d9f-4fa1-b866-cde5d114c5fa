import imaplib
import smtplib
from dataclasses import dataclass
from imaplib import IMAP4
from smtplib import SMTP_SSL

from src.ingestion_secrets import EmailsProcessorSecrets


@dataclass
class EmailCredentials:
    email_address: str
    application_key: str


@dataclass
class EmailAccountConfig:
    account_secret: EmailsProcessorSecrets
    app_key_secret: EmailsProcessorSecrets

    @property
    def credentials(self) -> EmailCredentials:
        return EmailCredentials(email_address=self.account_secret.get(), application_key=self.app_key_secret.get())

    @property
    def account(self) -> str:
        return self.account_secret.get()


def get_imap_server(credentials: EmailCredentials) -> IMAP4:
    imap_server = imaplib.IMAP4_SSL(host="imap.gmail.com")
    imap_server.login(credentials.email_address, credentials.application_key)
    return imap_server


def get_smtp_server(credentials: EmailCredentials) -> SMTP_SSL:
    smtp_server = smtplib.SMTP_SSL("smtp.gmail.com", 465)
    smtp_server.login(credentials.email_address, credentials.application_key)
    return smtp_server


EMAIL_ACCOUNTS = {
    "admiral": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.ADMIRAL_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.ADMIRAL_GMAIL_APP_KEY,
    ),
    "admiraltest": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.ADMIRAL_TEST_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.ADMIRAL_TEST_GMAIL_APP_KEY,
    ),
    "allyauto": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.ALLY_AUTO_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.ALLY_AUTO_GMAIL_APP_KEY,
    ),
    "arch": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.ARCH_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.ARCH_GMAIL_APP_KEY,
    ),
    "aru": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.ARU_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.ARU_GMAIL_APP_KEY,
    ),
    "bishopconifer": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.BISHOP_CONIFER_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.BISHOP_CONIFER_GMAIL_APP_KEY,
    ),
    "bowhead": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.BOWHEAD_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.BOWHEAD_GMAIL_APP_KEY,
    ),
    "bowheadtest": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.BOWHEAD_TEST_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.BOWHEAD_TEST_GMAIL_APP_KEY,
    ),
    "cna": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.CNA_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.CNA_GMAIL_APP_KEY,
    ),
    "ingestiondemo": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.INGESTION_DEMO_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.INGESTION_DEMO_GMAIL_APP_KEY,
    ),
    "isc": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.ISC_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.ISC_GMAIL_APP_KEY,
    ),
    "iscprerenewals": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.ISC_PRE_RENEWALS_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.ISC_PRE_RENEWALS_GMAIL_APP_KEY,
    ),
    "kalepa": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.KALEPA_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.KALEPA_GMAIL_APP_KEY,
    ),
    "kalepanewdemo": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.KALEPA_NEW_DEMO_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.KALEPA_NEW_DEMO_GMAIL_APP_KEY,
    ),
    "kis": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.KIS_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.KIS_GMAIL_APP_KEY,
    ),
    "markeldemo": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.MARKEL_DEMO_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.MARKEL_DEMO_GMAIL_APP_KEY,
    ),
    "merchantsgroup": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.MERCHANTSGROUP_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.MERCHANTSGROUP_GMAIL_APP_KEY,
    ),
    "merchantsgroupstaff": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.MERCHANTSGROUP_STAFF_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.MERCHANTSGROUP_STAFF_GMAIL_APP_KEY,
    ),
    "munichre": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.MUNICHRE_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.MUNICHRE_GMAIL_APP_KEY,
    ),
    "nationwide": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.NW_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.NW_GMAIL_APP_KEY,
    ),
    "nationwideml": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.NW_ML_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.NW_ML_GMAIL_APP_KEY,
    ),
    "necspecialty": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.NECSPECIALTY_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.NECSPECIALTY_GMAIL_APP_KEY,
    ),
    "northstarmutual": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.NORTHSTARMUTUAL_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.NORTHSTARMUTUAL_GMAIL_APP_KEY,
    ),
    "paragon": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.PARAGON_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.PARAGON_GMAIL_APP_KEY,
    ),
    "paragones": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.PARAGON_ES_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.PARAGON_ES_GMAIL_APP_KEY,
    ),
    "paragonpspe3": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.PARAGON_PSP_E3_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.PARAGON_PSP_E3_GMAIL_APP_KEY,
    ),
    "paragonwc": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.PARAGON_WC_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.PARAGON_WC_GMAIL_APP_KEY,
    ),
    "qualityaudit": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.QA_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.QA_GMAIL_APP_KEY,
    ),
    "secura": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.SECURA_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.SECURA_GMAIL_APP_KEY,
    ),
    "vivere": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.VIVERE_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.VIVERE_GMAIL_APP_KEY,
    ),
    "aig": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.AIG_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.AIG_GMAIL_APP_KEY,
    ),
    "paragontrident": EmailAccountConfig(
        account_secret=EmailsProcessorSecrets.PARAGON_TRIDENT_GMAIL_ACCOUNT,
        app_key_secret=EmailsProcessorSecrets.PARAGON_TRIDENT_GMAIL_APP_KEY,
    ),
}

FORWARD_EMAIL_ACCOUNTS = {
    "<EMAIL>": EMAIL_ACCOUNTS["admiral"],
}
