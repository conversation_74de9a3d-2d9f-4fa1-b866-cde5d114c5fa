locals {
  lambdas_tests = {
    triggerTests = {
      handler     = "src.test_handlers.trigger_handlers.trigger_tests"
      memory_size = 512
      timeout     = 60
    }
    yelpBusinessTest = {
      handler     = "src.test_handlers.yelp_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 900
      environment = {
        INGESTION_DATA_BUCKET = local.ingestion_data_bucket
      }
    }
    yelpMenuTest = {
      handler     = "src.test_handlers.yelp_test_handlers.test_menu_scraper"
      memory_size = 512
      timeout     = 900
    }
    yelpReviewsTest = {
      handler     = "src.test_handlers.yelp_test_handlers.test_reviews_scraper"
      memory_size = 512
      timeout     = 900
    }
    yelpPhotosTest = {
      handler     = "src.test_handlers.yelp_test_handlers.test_photos_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateYelpBusinessTests = {
      handler     = "src.test_handlers.yelp_test_handlers.aggregate_yelp_business_tests"
      memory_size = 512
      timeout     = 300
    }
    aggregateYelpMenuTests = {
      handler     = "src.test_handlers.yelp_test_handlers.aggregate_yelp_menu_tests"
      memory_size = 512
      timeout     = 300
    }
    aggregateYelpReviewsTests = {
      handler     = "src.test_handlers.yelp_test_handlers.aggregate_yelp_reviews_tests"
      memory_size = 512
      timeout     = 300
    }
    facebookBusinessTest = {
      handler     = "src.test_handlers.facebook_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 900
    }
    facebookPostsTest = {
      handler     = "src.test_handlers.facebook_test_handlers.test_posts_scraper"
      memory_size = 512
      timeout     = 900
    }
    facebookReviewsTest = {
      handler     = "src.test_handlers.facebook_test_handlers.test_reviews_scraper"
      memory_size = 512
      timeout     = 900
    }
    facebookPhotosTest = {
      handler     = "src.test_handlers.facebook_test_handlers.test_photos_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateFacebookTests = {
      handler     = "src.test_handlers.facebook_test_handlers.aggregate_facebook_tests"
      memory_size = 512
      timeout     = 300
    }
    googleBusinessTest = {
      handler     = "src.test_handlers.google_test_handlers.test_business_scraper"
      memory_size = 1024
      timeout     = 900
    }
    googleReviewsTest = {
      handler     = "src.test_handlers.google_test_handlers.test_reviews_scraper"
      memory_size = 512
      timeout     = 900
    }
    googleImagesTest = {
      handler     = "src.test_handlers.google_test_handlers.test_images_scraper"
      memory_size = 768
      timeout     = 900
    }
    opencorporatesTest = {
      handler     = "src.test_handlers.opencorporates_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateOpenCorporatesTests = {
      handler     = "src.test_handlers.opencorporates_test_handlers.aggregate_opencorporates_tests"
      memory_size = 1024
      timeout     = 900
    }
    goodjobfirstTest = {
      handler     = "src.test_handlers.goodjobfirst_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateGoodJobFirstTests = {
      handler     = "src.test_handlers.goodjobfirst_test_handlers.aggregate_goodjobfirst_tests"
      memory_size = 1024
      timeout     = 900
    }
    aggregateGoogleBusinessTests = {
      handler     = "src.test_handlers.google_test_handlers.aggregate_google_business_tests"
      memory_size = 512
      timeout     = 300
    }
    aggregateGoogleImagesTests = {
      handler     = "src.test_handlers.google_test_handlers.aggregate_google_images_tests"
      memory_size = 768
      timeout     = 300
    }
    tripadvisorBusinessTest = {
      handler     = "src.test_handlers.tripadvisor_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 900
    }
    tripadvisorReviewsTest = {
      handler     = "src.test_handlers.tripadvisor_test_handlers.test_reviews_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateTripAdvisorTests = {
      handler     = "src.test_handlers.tripadvisor_test_handlers.aggregate_tripadvisor_tests"
      memory_size = 512
      timeout     = 300
    }
    fdaImportAlertDetailsTest = {
      handler     = "src.test_handlers.fda_test_handlers.test_fda_import_alert_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateFDAImportAlertsTests = {
      handler     = "src.test_handlers.fda_test_handlers.aggregate_fda_tests"
      memory_size = 512
      timeout     = 300
    }
    betterBusinessBureauBusinessTest = {
      handler     = "src.test_handlers.better_business_bureau_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 900
    }
    betterBusinessBureauReviewsTest = {
      handler     = "src.test_handlers.better_business_bureau_test_handlers.test_reviews_scraper"
      memory_size = 512
      timeout     = 900
    }
    betterBusinessBureauComplaintTest = {
      handler     = "src.test_handlers.better_business_bureau_test_handlers.test_complaints_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateBetterBusinessBureauTests = {
      handler     = "src.test_handlers.better_business_bureau_test_handlers.aggregate_better_business_bureau_tests"
      memory_size = 512
      timeout     = 300
    }
    buildZoomBusinessTest = {
      handler     = "src.test_handlers.build_zoom_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 900
    }
    buildZoomPermitsTests = {
      handler     = "src.test_handlers.build_zoom_test_handlers.test_permit_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateBuildZoomTests = {
      handler     = "src.test_handlers.build_zoom_test_handlers.aggregate_build_zoom_tests"
      memory_size = 512
      timeout     = 300
    }
    thomasnetBusinessTest = {
      handler     = "src.test_handlers.thomasnet_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 900
    }
    thomasnetProductsTest = {
      handler     = "src.test_handlers.thomasnet_test_handlers.test_products_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateTestThomasnetBusiness = {
      handler     = "src.test_handlers.thomasnet_test_handlers.aggregate_thomasnet_business_tests"
      memory_size = 512
      timeout     = 300
    }
    aggregateTestThomasnetProducts = {
      handler     = "src.test_handlers.thomasnet_test_handlers.aggregate_thomasnet_products_tests"
      memory_size = 512
      timeout     = 300
    }
    houzzBusinessTest = {
      handler     = "src.test_handlers.houzz_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 900
    }
    aggregateTestHouzzBusiness = {
      handler     = "src.test_handlers.houzz_test_handlers.aggregate_houzz_business_tests"
      memory_size = 512
      timeout     = 300
    }
    fmcsaBusinessTest = {
      handler     = "src.test_handlers.fmcsa_test_handlers.test_business_scraper"
      memory_size = 512
      timeout     = 300
    }
    aggregateFmcsaBusinessTests = {
      handler     = "src.test_handlers.fmcsa_test_handlers.aggregate_fmcsa_business_tests"
      memory_size = 512
      timeout     = 300
    }
    fmcsaCrashTest = {
      handler     = "src.test_handlers.fmcsa_dependant_test_handlers.test_crash_scraper"
      memory_size = 512
      timeout     = 300
    }
    aggregateFmcsaCrashTests = {
      handler     = "src.test_handlers.fmcsa_dependant_test_handlers.aggregate_crash_tests"
      memory_size = 512
      timeout     = 300
    }
    fmcsaInspectionTest = {
      handler     = "src.test_handlers.fmcsa_dependant_test_handlers.test_inspection_scraper"
      memory_size = 3072
      timeout     = 900
    }
    aggregateFmcsaInspectionTests = {
      handler     = "src.test_handlers.fmcsa_dependant_test_handlers.aggregate_inspection_tests"
      memory_size = 512
      timeout     = 300
    }
    fmcsaComplaintTest = {
      handler     = "src.test_handlers.fmcsa_dependant_test_handlers.test_complaint_scraper"
      memory_size = 512
      timeout     = 300
    }
    aggregateFmcsaComplaintTests = {
      handler     = "src.test_handlers.fmcsa_dependant_test_handlers.aggregate_complaint_tests"
      memory_size = 512
      timeout     = 300
    }
    fmcsaHistoryTest = {
      handler     = "src.test_handlers.fmcsa_dependant_test_handlers.test_history_scraper"
      memory_size = 512
      timeout     = 300
    }
    aggregateFmcsaHistoryTests = {
      handler     = "src.test_handlers.fmcsa_dependant_test_handlers.aggregate_history_tests"
      memory_size = 512
      timeout     = 300
    }
    oshaInspectionTest = {
      handler     = "src.test_handlers.osha_test_handlers.test_osha_inspection_client",
      memory_size = 512
      timeout     = 300
      environment = {
        OSHA_API_V4_KEY_ARN               = local.region_specific_parameters.runtime_secret_arns.osha-api-v4-key
        SAFE_INIT_RESOLVE_SECRETS         = "true"
        SAFE_INIT_CACHE_SECRETS           = "true"
        SAFE_INIT_SECRET_CACHE_REDIS_HOST = local.redis_cache_host
        SAFE_INIT_SECRET_CACHE_REDIS_PORT = local.redis_cache_port
      }
    }
    aggregateOshaInspectionTests = {
      handler     = "src.test_handlers.osha_test_handlers.aggregate_osha_inspection_tests",
      memory_size = 512
      timeout     = 300
    }
    caCslbTest = {
      handler     = "src.test_handlers.ca_cslb_test_handlers.test_search_ca_cslb_license"
      memory_size = 512
      timeout     = 300
    }
    redshiftTunnelConnectionTest = {
      handler     = "src.test_handlers.redshift_test_handlers.test_connection"
      memory_size = 512
      timeout     = 300
      environment = {
        REDSHIFT_CONFIG_SECRET = local.regional_secret_arns["redshift-ingestion"]
      }
    }
    sendPdsEmail = {
      handler     = "src.test_handlers.pds_test_handlers.send_pds_email"
      memory_size = 512
      timeout     = 300
    }
    getReportIdForExecutionId = {
      handler     = "src.test_handlers.pds_test_handlers.get_report_id_for_execution_id"
      memory_size = 512
      timeout     = 300
    }
    handleClearing = {
      handler     = "src.test_handlers.pds_test_handlers.handle_clearing"
      memory_size = 512
      timeout     = 300
    }
    handleBusinessConfirmation = {
      handler     = "src.test_handlers.pds_test_handlers.handle_business_confirmation"
      memory_size = 512
      timeout     = 300
    }
    handleEntityMapping = {
      handler     = "src.test_handlers.pds_test_handlers.handle_entity_mapping"
      memory_size = 512
      timeout     = 300
    }
    handleDataOnboarding = {
      handler     = "src.test_handlers.pds_test_handlers.handle_data_onboarding"
      memory_size = 512
      timeout     = 300
    }
    handleManualVerification = {
      handler     = "src.test_handlers.pds_test_handlers.handle_manual_verification"
      memory_size = 512
      timeout     = 300
    }
    handleFailedPdsTest = {
      handler     = "src.test_handlers.pds_test_handlers.handle_failed_pds_test"
      memory_size = 512
      timeout     = 300
    }
  }
}
