locals {
  dd_sample_rate = lookup({ dev = "0.5", stage = "0.1", prod = "0.1" }, local.workflow_parameters.environment_key, "1")

  common_lambdas_env = {
    DB_CONFIG_SECRET                 = local.workflow_parameters.rds.write_user_secret_arn
    DB_SSL_ENABLED                   = "true"
    DD_REMOTE_CONFIGURATION_ENABLED  = "true"
    FACTS_API_URL                    = "http://facts-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
    FACTS_API_V2_URL                 = "http://facts-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v2.0"
    COPILOT_API_URL                  = "http://copilot-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v2.0"
    COPILOT_API_V3_URL               = "http://copilot-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
    LOCATION_SERVICE_URL             = "http://locations-api.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
    ENTITY_RESOLUTION_SERVICE_URL    = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v1.0"
    ENTITY_RESOLUTION_SERVICE_V3_URL = "http://entity-resolution.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}/api/v3.0"
    SPLASH_URL                       = "http://splash.${local.workflow_parameters.alb_host}:${local.workflow_parameters.alb_port}"
    INGESTION_DATA_BUCKET            = local.ingestion_data_bucket
    PROXY                            = local.region_specific_parameters.runtime_secret_arns.proxy-default
    PREMIUM_PROXY                    = local.region_specific_parameters.runtime_secret_arns.proxy-premium-default
    RESIDENTIAL_PROXY                = local.region_specific_parameters.runtime_secret_arns.proxy-residential
    GOOGLE_PREMIUM_PROXY             = local.region_specific_parameters.runtime_secret_arns.proxy-premium-google
    BUILDZOOM_PREMIUM_PROXY          = local.region_specific_parameters.runtime_secret_arns.proxy-premium-buildzoom
    BBB_PROXY                        = local.region_specific_parameters.runtime_secret_arns.proxy-better-business-bureau
    TA_PROXY                         = local.region_specific_parameters.runtime_secret_arns.proxy-tripadvisor
    FACEBOOK_PROXY                   = local.region_specific_parameters.runtime_secret_arns.proxy-facebook
    FMCSA_PROXY                      = local.region_specific_parameters.runtime_secret_arns.proxy-fmcsa
    UNICOURT_PROXY                   = local.region_specific_parameters.runtime_secret_arns.proxy-unicourt
    SENDGRID_API_KEY                 = local.region_specific_parameters.runtime_secret_arns.sendgrid-api-key
    SCALE_SERP_API_KEY               = local.region_specific_parameters.runtime_secret_arns.scale-serp-api-key
    FMCSA_API_KEY                    = local.region_specific_parameters.runtime_secret_arns.fmcsa-api-key
    SERPER_API_KEY                   = local.region_specific_parameters.runtime_secret_arns.serper-api-key
    SF_ARN_PREFIX                    = "arn:aws:states:us-east-1:${local.workflow_parameters.account_id}:stateMachine:"
    AWS_MAX_ATTEMPTS                 = 2
    AWS_RETRY_MODE                   = "standard"
    DD_CAPTURE_LAMBDA_PAYLOAD        = true
    DD_LOG_LEVEL                     = "INFO"
    DD_TRACE_ENABLED                 = true
    DD_SERVICE_MAPPING               = "postgres:postgres-ingestion"
    DD_TRACE_SAMPLING_RULES          = "[{\"service\": \"ingestion-lambdas\", \"sample_rate\": ${local.dd_sample_rate}}, {\"service\": \"postgres-ingestion\", \"sample_rate\": ${local.dd_sample_rate}}]"
    DD_TRACE_SAMPLE_RATE             = local.dd_sample_rate
    DD_APM_FEATURES                  = "error_rare_sample_tracer_drop"
    SLACK_SECRET                     = local.regional_secret_arns["slack-token"]
    ARCH_API_INTEGRATION_SECRET      = local.regional_secret_arns["arch-api-config"]
    REDIS_CACHE_HOST                 = local.redis_cache_host
    REDIS_CACHE_PORT                 = local.redis_cache_port
  }

  lambdas_app = {
    searchAndIdentifyLite = {
      handler              = "src.handlers.lite_search_handlers.lite_search"
      memory_size          = 2048 # (pczubak): do not lower, has to be fast coldstart
      reserved_concurrency = 20
      timeout              = 120
      environment = {
        YELP_API_KEY                     = local.yelp_api_key
        GOOGLE_API_KEY                   = local.google_api_key
        BUILDZOOM_CUSTOM_SEARCH_ID       = local.buildzoom_custom_search_id
        FMCSA_SMS_CUSTOM_SEARCH_ID       = local.fmcsa_custom_search_id
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
    }
    searchAndIdentifyLiteAsync = {
      handler     = "src.handlers.lite_search_handlers_async.async_lite_search"
      memory_size = 512
      timeout     = 20
      environment = {
        LITE_SEARCH_LAMBDA_NAME = "${local.lambda_name_prefix}search-and-identify-lite-lambda"
      }
    }
    scrapeYelpBatch = {
      handler     = "src.handlers.yelp_handlers.scrape_all_yelp_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_yelp_batch
            input      = local.env_config.scrape_yelp_batch_input
          }
        }
      ]
    }
    scrapeTripAdvisorBatch = {
      handler     = "src.handlers.tripadvisor_handlers.scrape_all_tripadvisor_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_tripadvisor_batch
            input      = local.env_config.scrape_tripadvisor_batch_input
          }
        }
      ]
    }
    nationwideSFSubmissionExportMonitoring = {
      handler     = "src.handlers.monitoring.nationwide_monitoring.monitor_nationwide_sf_submission_export"
      memory_size = 512
      timeout     = 60
      enabled     = local.env_config.monitor_nationwide_submission_from_salesforce_export_executions_enabled
      triggers = [
        { schedule = "rate(6 hours)" }
      ]
    }
    nationwideSyncSFMonitoring = {
      handler     = "src.handlers.monitoring.nationwide_monitoring.monitor_nationwide_sync_sf"
      memory_size = 512
      timeout     = 60
      triggers = [
        { schedule = "rate(6 hours)" }
      ]
      environment = {
        STEP_FUNCTION_ARN = local.monitor_nationwide_sync_sf_arn
      }
    }
    scrapeBuildZoomBatch = {
      handler     = "src.handlers.buildzoom_handlers.scrape_all_buildzoom_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_buildzoom_batch
            input      = local.env_config.scrape_buildzoom_batch_input
          }
        }
      ]
    }
    scrapeThomasnetBatch = {
      handler     = "src.handlers.thomasnet_handlers.scrape_all_thomasnet_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_thomasnet_batch
            input      = local.env_config.scrape_thomasnet_batch_input
          }
        }
      ]
    }
    scrapeIQSBatch = {
      handler     = "src.handlers.iqs_handlers.scrape_all_iqs_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_iqs_batch
            input      = local.env_config.scrape_iqs_batch_input
          }
        }
      ]
    }
    scrapeOshaStandards = {
      handler     = "src.handlers.osha_standard_handler.scrape_osha_standards"
      memory_size = 350
      timeout     = 900
    }
    scrapeHouzzBatch = {
      handler     = "src.handlers.houzz_handlers.scrape_all_houzz_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_houzz_batch
            input      = local.env_config.scrape_houzz_batch_input
          }
        }
      ]
    }
    scrapeGoodJobsFirstBatch = {
      handler     = "src.handlers.good_jobs_first_handlers.scrape_all_good_jobs_first_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_good_jobs_first_batch
            input      = local.env_config.scrape_good_jobs_first_batch_input
          }
        }
      ]
    }
    scrapeFacebookBatch = {
      handler     = "src.handlers.facebook_handlers.scrape_all_facebook_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_facebook_batch
            input      = local.env_config.scrape_facebook_batch_input
          }
        }
      ]
    }
    scrapeGoogleBatch = {
      handler     = "src.handlers.google_handlers.scrape_all_google_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_google_batch
            input      = local.env_config.scrape_google_batch_input
          }
        }
      ]
    }
    scrapeBetterBusinessBureauBatch = {
      handler     = "src.handlers.better_business_bureau.scrape_all_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_better_business_bureau_batch
            input      = local.env_config.scrape_better_business_bureau_batch_input
          }
        }
      ]
    }
    createPendingRenewals = {
      handler     = "src.handlers.copilot_handlers.create_renewals_for_valid_reports"
      memory_size = 376
      timeout     = 900
      triggers = [
        { schedule = local.schedules.create_pending_renewals }
      ]
    }
    cleanupScrapingMetadata = {
      handler     = "src.handlers.ers_handlers.cleanup_scraping_metadata"
      memory_size = 512
      timeout     = 900
    }
    scrapeFmcsaBatch = {
      handler     = "src.handlers.fmcsa_handlers.scrape_all_fmcsa_batch"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          schedule = {
            expression = local.schedules.scrape_fmcsa_batch
            input      = local.env_config.scrape_fmcsa_batch_input
          }
        }
      ]
    }
    scrapeSaferFmcsaBatch = {
      handler     = "src.handlers.fmcsa_handlers.scrape_all_safer_fmcsa_batch"
      memory_size = 420
      timeout     = 900
    }
    getSamExclusionFile = {
      handler     = "src.handlers.sam_handlers.handle_getting_exclusion_file"
      memory_size = 512
      timeout     = 900
      triggers = [
        { schedule = local.schedules.sam_daily }
      ]
      environment = {
        SAM_API_KEY           = local.sam_api_key
        INGESTION_DATA_BUCKET = local.ingestion_data_bucket
      }
    }
    getSamEntityFile = {
      handler     = "src.handlers.sam_handlers.handle_getting_entity_file"
      memory_size = 1024
      timeout     = 900
      triggers = [
        { schedule = local.schedules.sam_monthly }
      ]
      environment = {
        SAM_API_KEY           = local.sam_api_key
        INGESTION_DATA_BUCKET = local.ingestion_data_bucket
      }
    }
    refreshLuminatiIps = {
      handler     = "src.handlers.proxy_ip_refresh.refresh_luminati_ips"
      memory_size = 512
      timeout     = 120
      environment = {
        LUMINATI_API_TOKEN = local.luminati_api_token
      }
      triggers = [
        { schedule = local.schedules.refresh }
      ]
    }
    exportNationwideSalesforceReportToTsv = {
      handler     = "src.handlers.file_uploaded_handlers.export_nationwide_salesforce_report_to_tsv"
      memory_size = 256
      timeout     = 60
      environment = {
        SUBMISSIONS_UPLOAD_BUCKET  = local.submissions_upload_bucket
        NATIONWIDE_ORGANIZATION_ID = local.env_config.nationwide_organization_id
      }
    }
    nationwideEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "nationwide"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor }
      ]
    }
    nationwideMlEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "nationwideml"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    archEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "arch"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    munichreEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "munichre"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    paragonEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "paragon"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    kalepaAndKisEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "kalepa,kis"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    northstarmutualEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "northstarmutual"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    necspecialtyEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "necspecialty"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    kalepaNewDemoEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "kalepanewdemo"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    allyAutoEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "allyauto"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    admiralEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "admiral"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    admiralTestEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "admiraltest"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    paragonWorkCompEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "paragonwc"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    qualityAuditEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "qualityaudit"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    bowheadEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "bowhead"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    bowheadTestEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "bowheadtest"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }

    merchantsgroupEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "merchantsgroup"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    merchantsgroupStaffEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "merchantsgroupstaff"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    paragonPspE3EmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "paragonpspe3"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    bishopConiferEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "bishopconifer"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    cnaEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "cna"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    iscPreRenewalsEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "iscprerenewals"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    paragonEsEmailProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "paragones"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    paragonTridentEmailProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "paragontrident"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    markelDemoEmailProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "markeldemo"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_document_ingestion }
      ]
    }
    ingestionDemoEmailProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "ingestiondemo"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_document_ingestion }
      ]
    }
    securaEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "secura"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    vivereEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "vivere"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    aigEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 768
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "aig"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_processor_only_for_prod }
      ]
    }
    retryEmailsProcessor = {
      handler              = "src.handlers.emails_processor.process_emails"
      memory_size          = 1024
      timeout              = 900
      reserved_concurrency = 1
      environment = {
        EMAIL_PROCESSOR_ACCOUNT = "admiral,aig,allyauto,arch,bishopconifer,bowhead,cna,kalepa,kalepanewdemo,kis,merchantsgroup,merchantsgroupstaff,munichre,nationwide,nationwideml,necspecialty,northstarmutual,paragon,paragones,paragontrident,paragonpspe3,paragonwc,qualityaudit,secura,vivere,admiraltest,bowheadtest"
        RETRY_MODE              = "true"
        GMAIL_CREDENTIALS       = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.emails_retry_processor }
      ]
    }
    emailForwarder = {
      handler     = "src.handlers.emails_forwarder.forward_emails"
      memory_size = 768
      timeout     = 900
      environment = {
        GMAIL_CREDENTIALS = local.gmail_credentials
      }
    }
    nationwidePendingSyncProcessor = {
      handler     = "src.handlers.submission_sync_handler.sync_nationwide_pending_submissions"
      memory_size = 1024
      timeout     = 900
      environment = {
        NATIONWIDE_ORGANIZATION_ID = local.env_config.nationwide_organization_id
        SUBMISSIONS_GLUE_BUCKET    = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET  = local.submissions_upload_bucket
      }

      triggers = [
        { schedule = local.schedules.pending_sync_nationwide }
      ]
    }
    archPendingSyncProcessor = {
      handler     = "src.handlers.submission_sync_handler.sync_arch_pending_submissions"
      memory_size = 1024
      timeout     = 900
      environment = {
        ARCH_ORGANIZATION_ID      = local.env_config.arch_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        { schedule = local.schedules.pending_sync_arch }
      ]
    }
    syncMunichReSubmissionFromExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_munich_submissions_from_export"
      memory_size = 2048
      timeout     = 900
      environment = {
        MUNICH_RE_ORGANIZATION_ID = local.env_config.munich_re_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "munichre/"
          }
        }
      ]
    }
    munichRePendingSyncProcessor = {
      handler     = "src.handlers.submission_sync_handler.sync_munich_pending_submissions"
      memory_size = 1024
      timeout     = 900
      environment = {
        MUNICH_RE_ORGANIZATION_ID = local.env_config.munich_re_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        { schedule = local.schedules.pending_sync_munich_re }
      ]
    }
    syncAdmiralSubmissionFromExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_admiral_submissions_from_export"
      memory_size = 3048
      timeout     = 900
      environment = {
        ADMIRAL_ORGANIZATION_ID   = local.env_config.admiral_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      #       triggers = [
      #         {
      #           s3 = {
      #             bucket               = local.submissions_upload_bucket
      #             event                = "s3:ObjectCreated:*"
      #             object_filter_prefix = "status_reports/admiral/"
      #           }
      #         }
      #       ]
    }
    admiralPendingSyncProcessor = {
      handler     = "src.handlers.submission_sync_handler.sync_admiral_pending_submissions"
      memory_size = 1024
      timeout     = 900
      environment = {
        ADMIRAL_ORGANIZATION_ID   = local.env_config.admiral_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      #       triggers = [
      #         { schedule = local.schedules.pending_sync_admiral }
      #       ]
    }
    syncBowheadSubmissionFromExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_bowhead_submissions_from_export"
      memory_size = 3048
      timeout     = 900
      environment = {
        BOWHEAD_ORGANIZATION_ID   = local.env_config.bowhead_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "status_reports/bowhead/"
          }
        }
      ]
    }
    bowheadPendingSyncProcessor = {
      handler     = "src.handlers.submission_sync_handler.sync_bowhead_pending_submissions"
      memory_size = 1024
      timeout     = 900
      environment = {
        BOWHEAD_ORGANIZATION_ID   = local.env_config.bowhead_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        { schedule = local.schedules.pending_sync_bowhead }
      ]
    }
    aruSubmissionFromExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_aru_submissions_from_export"
      memory_size = 3048
      timeout     = 900
      environment = {
        ARU_ORGANIZATION_ID       = local.env_config.aru_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "status_reports/aru/"
          }
        }
      ]
    }
    aruPendingSyncProcessor = {
      handler     = "src.handlers.submission_sync_handler.sync_aru_pending_submissions"
      memory_size = 1024
      timeout     = 900
      environment = {
        ARU_ORGANIZATION_ID       = local.env_config.aru_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        { schedule = local.schedules.pending_sync_aru }
      ]
    }
    coniferSubmissionFromExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_conifer_submissions_from_export"
      memory_size = 3048
      timeout     = 900
      environment = {
        CONIFER_ORGANIZATION_ID   = local.env_config.conifer_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "status_reports/conifer/"
          }
        }
      ]
    }
    coniferPendingSyncProcessor = {
      handler     = "src.handlers.submission_sync_handler.sync_conifer_pending_submissions"
      memory_size = 1024
      timeout     = 900
      environment = {
        CONIFER_ORGANIZATION_ID   = local.env_config.conifer_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        { schedule = local.schedules.pending_sync_conifer }
      ]
    }
    securaSubmissionFromExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_secura_submissions_from_export"
      memory_size = 3048
      timeout     = 900
      environment = {
        SECURA_ORGANIZATION_ID    = local.env_config.secura_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "status_reports/secura/"
          }
        }
      ]
    }
    securaPendingSyncProcessor = {
      handler     = "src.handlers.submission_sync_handler.sync_secura_pending_submissions"
      memory_size = 1024
      timeout     = 900
      environment = {
        SECURA_ORGANIZATION_ID    = local.env_config.secura_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        { schedule = local.schedules.pending_sync_secura }
      ]
    }
    syncParagonSubmissionExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_paragon_submissions_export"
      memory_size = 3072
      timeout     = 900
      environment = {
        PARAGON_ORGANIZATION_ID   = local.env_config.paragon_organization_id
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
      }
      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "status_reports/paragon/"
          }
        }
      ]
    }
    syncNationwideMLSubmissionFromExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_nationwide_ml_submissions_export"
      memory_size = 3048
      timeout     = 900
      environment = {
        NATIONWIDE_ML_ORGANIZATION_ID = local.env_config.nationwide_ml_organization_id
        SUBMISSIONS_GLUE_BUCKET       = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET     = local.submissions_upload_bucket
      }

      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "status_reports/nationwide_ml/"
          }
        }
      ]
    }
    syncNationwideMLExperianData = {
      handler     = "src.handlers.file_uploaded_handlers.sync_nationwide_ml_experian_data"
      memory_size = 2048
      timeout     = 900
      environment = {
        SUBMISSIONS_GLUE_BUCKET = local.env_config.submissions_glue_bucket
      }

      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "external_data/nationwide_ml/experian_data/"
          }
        }
      ]
    }
    nationwideMLExperianDataHandler = {
      handler     = "src.handlers.nationwide_ml_handlers.handle_nationwide_ml_experian_data"
      memory_size = 1024
      timeout     = 900
      environment = {
      }

      triggers = [
        { schedule = local.schedules.nationwide_ml_data_handler }
      ]
    }
    iscSubmissionFromExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_isc_submissions_export"
      memory_size = 3048
      timeout     = 900
      environment = {
        ISC_ORGANIZATION_ID       = local.env_config.isc_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "status_reports/isc/"
          }
        }
      ]
    }
    iscPendingSyncProcessor = {
      handler     = "src.handlers.submission_sync_handler.sync_isc_pending_submissions"
      memory_size = 1024
      timeout     = 900
      environment = {
        ISC_ORGANIZATION_ID       = local.env_config.isc_organization_id
        SUBMISSIONS_GLUE_BUCKET   = local.env_config.submissions_glue_bucket
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }
    }
    onSamEntityFile = {
      handler     = "src.handlers.sam_handlers.handle_entity_file"
      memory_size = 512
      timeout     = 90
      environment = {
        JOB_QUEUE             = local.job_queue
        INGESTION_DATA_BUCKET = local.ingestion_data_bucket
      }

      triggers = [
        {
          s3 = {
            bucket               = local.ingestion_data_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "sam/entity/"
          }
        }
      ]
    }
    onSamExclusionFile = {
      handler     = "src.handlers.sam_handlers.handle_exclusion_file"
      memory_size = 768
      timeout     = 90
      environment = {
        JOB_QUEUE             = local.job_queue
        INGESTION_DATA_BUCKET = local.ingestion_data_bucket
      }
      triggers = [
        {
          s3 = {
            bucket               = local.ingestion_data_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "sam/exclusion/"
          }
        }
      ]
    }
    syncNationwideSubmissionFromBossExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_nationwide_submissions_from_boss_export"
      memory_size = 3072
      timeout     = 900
      environment = {
        SUBMISSIONS_UPLOAD_BUCKET  = local.submissions_upload_bucket
        SUBMISSIONS_GLUE_BUCKET    = local.env_config.submissions_glue_bucket
        NATIONWIDE_ORGANIZATION_ID = local.env_config.nationwide_organization_id
      }
      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "nationwide_boss_updated/"
          }
        }
      ]
    }
    mergeArchReports = {
      handler     = "src.handlers.submission_sync.arch_reports_merge_handler.merge_arch_files"
      memory_size = 2048
      timeout     = 900
      environment = {
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
      }

      triggers = [
        { schedule = local.schedules.arch_merge_files }
      ]
    }
    syncArchSubmissionFromSalesforceExport = {
      handler     = "src.handlers.file_uploaded_handlers.sync_arch_submissions_from_export"
      memory_size = 512
      timeout     = 900
      environment = {
        SUBMISSIONS_UPLOAD_BUCKET = local.submissions_upload_bucket
        SUBMISSIONS_GLUE_BUCKET   = local.submissions_glue_bucket
        ARCH_ORGANIZATION_ID      = local.arch_organization_id
      }
      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "status_reports/arch/"
          }
        }
      ]
    }
    cleanAndForwardAmplitudeFile = {
      handler     = "src.handlers.file_uploaded_handlers.clean_and_forward_amplitude_file"
      memory_size = 512
      timeout     = 900
      triggers = [
        {
          s3 = {
            bucket               = local.amplitude_files_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "staging/"
          }
        }
      ]
    }
    updateErsIndustries = {
      handler     = "src.handlers.ers_handlers.update_industries"
      memory_size = 512
      timeout     = 300
      triggers = [
        {
          eventBridge = {
            event_bus = local.facts_event_bus_arn
            event_pattern = {
              source = ["facts_api"]
              detail = {
                fact_created = {
                  fact_subtype = {
                    id = ["BUSINESS_CATEGORIES"]
                  }
                }
              }
            }
          }
        }
      ]
    }
    updateErsWebsite = {
      handler     = "src.handlers.ers_handlers.update_website"
      memory_size = 512
      timeout     = 300
      triggers = [
        {
          eventBridge = {
            event_bus = local.facts_event_bus_arn
            event_pattern = {
              source = ["facts_api"]
              detail = {
                fact_created = {
                  parent_type = ["BUSINESS"]
                  fact_subtype = {
                    id = ["WEBSITE"]
                  }
                  observation = {
                    source = {
                      source_type_id = [
                        {
                          anything-but = "FIRST_PARTY"
                        }
                      ]
                    }
                  }
                }
              }
            }
          }
        }
      ]
    }
    identifierCreatedERSSync = {
      handler     = "src.handlers.identifiers_handlers.handle_identifier_created"
      memory_size = 512
      timeout     = 60
      triggers = [
        {
          eventBridge = {
            event_bus = local.facts_event_bus_arn
            event_pattern = {
              source = ["facts_api"]
              detail = {
                fact_created = {
                  parent_type = ["BUSINESS"]
                  fact_subtype = {
                    id = ["USDOTS", "FEIN", "CAGE", "UEI", "DUNS", "EPA_REGISTRY_ID"]
                  }
                  observation = {
                    source = {
                      source_type_id = [
                        {
                          anything-but = "FIRST_PARTY"
                        }
                      ]
                    }
                  }
                }
              }
            }
          }
        }
      ]
    }
    scrapeYelpBusiness = {
      handler     = "src.handlers.yelp_handlers.scrape_business"
      memory_size = 512
      timeout     = 900
    }
    scrapeYelpPhotos = {
      handler     = "src.handlers.yelp_handlers.scrape_photos"
      memory_size = 512
      timeout     = 900
    }
    scrapeYelpReviews = {
      handler     = "src.handlers.yelp_handlers.scrape_reviews"
      memory_size = 512
      timeout     = 900
    }
    scrapeYelpMenu = {
      handler     = "src.handlers.yelp_handlers.scrape_menu"
      memory_size = 512
      timeout     = 900
    }
    scrapeFDABusiness = {
      handler     = "src.handlers.fda_handlers.scrape_business"
      memory_size = 512
      timeout     = 900
      environment = {
        FDA_SECRET = local.fda_secret
      }
    }
    scrapeFDAInspectionsWithCitations = {
      handler     = "src.handlers.fda_handlers.scrape_inspections_with_citations"
      memory_size = 512
      timeout     = 900
      environment = {
        FDA_SECRET = local.fda_secret
      }
    }
    scrapeFDARecalls = {
      handler     = "src.handlers.fda_handlers.scrape_recalls"
      memory_size = 512
      timeout     = 900
      environment = {
        FDA_SECRET = local.fda_secret
      }
    }
    emitException = {
      handler     = "src.handlers.exception.emit_exception"
      memory_size = 376
      timeout     = 300
    }
    getHomepageFact = {
      handler     = "src.handlers.homepage.homepage.get_homepage_fact"
      memory_size = 512
      timeout     = 240
    }
    crawlBusinessWebsite = {
      handler     = "src.handlers.homepage.homepage.crawl_business_website"
      memory_size = 640
      timeout     = 900
    }
    takeWebsiteScreenshot = {
      handler     = "src.handlers.homepage.screenshot_homepage.screenshot_website"
      memory_size = 1024
      timeout     = 900
    }
    loadPages = {
      handler     = "src.handlers.homepage.homepage.load_pages"
      memory_size = 512
      timeout     = 240
    }
    loadImages = {
      handler     = "src.handlers.homepage.homepage.load_website_images"
      memory_size = 1024
      timeout     = 900
    }
    scrapeTripAdvisorBusiness = {
      handler     = "src.handlers.tripadvisor_handlers.scrape_business"
      memory_size = 512
      timeout     = 600
    }
    scrapeTripAdvisorReviews = {
      handler     = "src.handlers.tripadvisor_handlers.scrape_reviews"
      memory_size = 476
      timeout     = 900
      environment = {
        TRIPADVISOR_REVIEW_LIMIT                          = 300
        TRIPADVISOR_REVIEW_MIN_PARALLEL_SIZE              = 4
        TRIPADVISOR_REVIEW_CONSECUTIVE_FAILS_ALLOWED_SIZE = 2
      }
    }
    scrapeBuildZoomBusiness = {
      handler     = "src.handlers.buildzoom_handlers.scrape_business"
      memory_size = 512
      timeout     = 900
    }
    scrapeCACSLBLicense = {
      handler     = "src.handlers.ca_cslb.scrape_ca_cslb"
      memory_size = 512
      timeout     = 900
    }
    searchCACSLBLicense = {
      handler     = "src.handlers.on_demand_handlers.search_ca_cslb"
      memory_size = 512
      timeout     = 900
    }
    scrapeBuildZoomPermits = {
      handler     = "src.handlers.buildzoom_handlers.scrape_permits"
      memory_size = 512
      timeout     = 900
    }
    scrapeThomasnetBusiness = {
      handler     = "src.handlers.thomasnet_handlers.scrape_business"
      memory_size = 512
      timeout     = 900
    }
    scrapeThomasnetProducts = {
      handler     = "src.handlers.thomasnet_handlers.scrape_products"
      memory_size = 512
      timeout     = 900
    }
    scrapeOtherThomasnetLocations = {
      handler     = "src.handlers.thomasnet_handlers.scrape_other_locations"
      memory_size = 512
      timeout     = 900
    }
    scrapeIQSBusiness = {
      handler     = "src.handlers.iqs_handlers.scrape_business"
      memory_size = 512
      timeout     = 900
    }
    scrapeIQSBusinessNews = {
      handler     = "src.handlers.iqs_handlers.scrape_news"
      memory_size = 512
      timeout     = 900
    }
    scrapeHouzzBusiness = {
      handler     = "src.handlers.houzz_handlers.scrape_business"
      memory_size = 512
      timeout     = 900
    }
    scrapeHouzzReviews = {
      handler     = "src.handlers.houzz_handlers.scrape_reviews"
      memory_size = 512
      timeout     = 900
    }
    scrapeHouzzProjects = {
      handler     = "src.handlers.houzz_handlers.scrape_projects"
      memory_size = 512
      timeout     = 900
    }
    scrapeHouzzPhotos = {
      handler     = "src.handlers.houzz_handlers.scrape_photos"
      memory_size = 400
      timeout     = 900
    }
    searchYelp = {
      handler     = "src.handlers.on_demand_handlers.search_yelp"
      memory_size = 612
      timeout     = 600
      environment = {
        YELP_API_KEY          = local.yelp_api_key
        YELP_CUSTOM_SEARCH_ID = local.yelp_custom_search_id
        GOOGLE_API_KEY        = local.google_api_key
      }
    }
    searchEPA = {
      handler     = "src.handlers.on_demand_handlers.search_epa"
      memory_size = 420
      timeout     = 600
    }
    scrapeEPAFacilities = {
      handler     = "src.handlers.epa_handlers.scrape_facilities"
      memory_size = 512
      timeout     = 900
    }
    scrapeEPAFacilityDetails = {
      handler     = "src.handlers.epa_handlers.scrape_facility_details"
      memory_size = 512
      timeout     = 900
    }
    loadEPAActions = {
      handler     = "src.handlers.epa_handlers.load_actions"
      memory_size = 512
      timeout     = 900
    }
    loadEPANotices = {
      handler     = "src.handlers.epa_handlers.load_notices"
      memory_size = 512
      timeout     = 900
    }
    loadEPAInspections = {
      handler     = "src.handlers.epa_handlers.load_inspections"
      memory_size = 512
      timeout     = 900
    }
    createUseDangerousChemicalsObservations = {
      handler     = "src.handlers.epa_handlers.create_use_dangerous_chemicals_observations"
      memory_size = 512
      timeout     = 900
    }
    searchOsha = {
      handler     = "src.handlers.on_demand_handlers.search_osha"
      memory_size = 576
      timeout     = 900
      environment = {
        OSHA_API_V4_KEY_ARN               = local.region_specific_parameters.runtime_secret_arns.osha-api-v4-key
        SAFE_INIT_RESOLVE_SECRETS         = "true"
        SAFE_INIT_CACHE_SECRETS           = "true"
        SAFE_INIT_SECRET_CACHE_REDIS_HOST = local.redis_cache_host
        SAFE_INIT_SECRET_CACHE_REDIS_PORT = local.redis_cache_port
      }
    }
    searchEfast = {
      handler     = "src.handlers.on_demand_handlers.search_efast"
      memory_size = 450
      timeout     = 900
    }
    scrapeErisa5500Forms = {
      handler     = "src.handlers.efast.scrape_erisa_5500"
      memory_size = 420
      timeout     = 900
    }
    searchFDA = {
      handler     = "src.handlers.on_demand_handlers.search_fda"
      memory_size = 512
      timeout     = 600
      environment = {
        FDA_SECRET = local.fda_secret
      }
    }
    searchFacebook = {
      handler     = "src.handlers.on_demand_handlers.search_facebook"
      memory_size = 512
      timeout     = 600
    }
    searchTripAdvisor = {
      handler     = "src.handlers.on_demand_handlers.search_trip_advisor"
      memory_size = 512
      timeout     = 360
      environment = {
        GOOGLE_API_KEY               = local.google_api_key
        TRIPADVISOR_CUSTOM_SEARCH_ID = local.tripadvisor_custom_search_id
      }
    }
    searchBuildZoom = {
      handler     = "src.handlers.on_demand_handlers.search_buildzoom"
      memory_size = 768
      timeout     = 360
      environment = {
        GOOGLE_API_KEY             = local.google_api_key
        BUILDZOOM_CUSTOM_SEARCH_ID = local.buildzoom_custom_search_id
      }
    }
    searchBetterBusinessBureau = {
      handler     = "src.handlers.on_demand_handlers.search_better_business_bureau"
      memory_size = 470
      timeout     = 300
      environment = {
        GOOGLE_API_KEY                          = local.google_api_key
        BETTER_BUSINESS_BUREAU_CUSTOM_SEARCH_ID = local.better_business_bureau_custom_search_id
      }
    }
    searchFMCSA = {
      handler     = "src.handlers.on_demand_handlers.search_fmcsa"
      memory_size = 650
      timeout     = 300
      environment = {
        GOOGLE_API_KEY             = local.google_api_key
        FMCSA_SMS_CUSTOM_SEARCH_ID = local.fmcsa_custom_search_id
      }
    }
    searchSaferFMCSAReport = {
      handler     = "src.handlers.on_demand_handlers.search_safer_fmcsa_report"
      memory_size = 512
      timeout     = 300
      environment = {
        GOOGLE_API_KEY               = local.google_api_key
        SAFER_FMCSA_CUSTOM_SEARCH_ID = local.safer_fmcsa_custom_search_id
      }
    }
    searchDotReport = {
      handler     = "src.handlers.on_demand_handlers.search_dot_report"
      memory_size = 470
      timeout     = 450
    }
    searchThomasnet = {
      handler     = "src.handlers.on_demand_handlers.search_thomasnet"
      memory_size = 470
      timeout     = 360
      environment = {
        GOOGLE_API_KEY             = local.google_api_key
        THOMASNET_CUSTOM_SEARCH_ID = local.thomasnet_custom_search_id
      }
    }
    searchIqs = {
      handler     = "src.handlers.on_demand_handlers.search_iqs"
      memory_size = 420
      timeout     = 300
      environment = {
        GOOGLE_API_KEY       = local.google_api_key
        IQS_CUSTOM_SEARCH_ID = local.iqs_custom_search_id
      }
    }
    searchHouzz = {
      handler     = "src.handlers.on_demand_handlers.search_houzz"
      memory_size = 585
      timeout     = 300
      environment = {
        GOOGLE_API_KEY         = local.google_api_key
        HOUZZ_CUSTOM_SEARCH_ID = local.houzz_custom_search_id
      }
    }
    searchGoodJobsFirst = {
      handler     = "src.handlers.on_demand_handlers.search_good_jobs_first"
      memory_size = 512
      timeout     = 300
      environment = {
        GOOGLE_API_KEY                   = local.google_api_key
        GOOD_JOBS_FIRST_CUSTOM_SEARCH_ID = local.good_jobs_first_custom_search_id
      }
    }
    searchUnicourt = {
      handler     = "src.handlers.on_demand_handlers.search_unicourt"
      memory_size = 768
      timeout     = 600
      environment = {
        GOOGLE_API_KEY            = local.google_api_key
        UNICOURT_CUSTOM_SEARCH_ID = local.unicourt_custom_search_id
      }
    }
    searchOpencorporates = {
      handler     = "src.handlers.on_demand_handlers.search_opencorporates"
      memory_size = 512
      timeout     = 600
    }
    searchSam = {
      handler     = "src.handlers.on_demand_handlers.search_sam"
      memory_size = 420
      timeout     = 900
    }
    searchApartments = {
      handler     = "src.handlers.on_demand_handlers.search_apartments"
      memory_size = 512
      timeout     = 900
    }
    searchIrsForm990 = {
      handler     = "src.handlers.on_demand_handlers.search_irs_form_990"
      memory_size = 512
      timeout     = 900
      environment = {
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
    }
    identifyBusinesses = {
      handler     = "src.handlers.on_demand_handlers.identify_businesses"
      memory_size = 512
      timeout     = 600
      environment = {
        SAFE_INIT_AUTO_TRACE_LAMBDAS = "true"
      }
    }
    selectScrapersBulk = {
      handler     = "src.handlers.select_scrapers.select_scrapers_bulk"
      memory_size = 768
      timeout     = 600
    }
    selectAndRunOrPollScrapers = {
      handler     = "src.handlers.select_scrapers.select_and_run_or_poll_scrapers"
      memory_size = 768
      timeout     = 600
    }
    searchGoogleLocal = {
      handler     = "src.handlers.on_demand_handlers.search_google_local"
      memory_size = 1200
      timeout     = 900
    }
    scrapeGoogleLocalBusiness = {
      handler     = "src.handlers.google_handlers.scrape_google_local_business"
      memory_size = 1024
      timeout     = 900
    }
    scrapeGoogleLocalReviews = {
      handler     = "src.handlers.google_handlers.scrape_google_local_reviews"
      memory_size = 512
      timeout     = 900
    }
    scrapeGoogleLocalImages = {
      handler     = "src.handlers.google_handlers.scrape_google_local_images"
      memory_size = 1175
      timeout     = 900
    }
    scrapeGoodJobsFirstBusiness = {
      handler     = "src.handlers.good_jobs_first_handlers.scrape_business"
      memory_size = 512
      timeout     = 900
    }
    scrapeFacebookBusiness = {
      handler     = "src.handlers.facebook_handlers.scrape_business"
      memory_size = 512
      timeout     = 900
    }
    scrapeFacebookPosts = {
      handler     = "src.handlers.facebook_handlers.scrape_posts"
      memory_size = 1300
      timeout     = 900
    }
    scrapeFacebookReviews = {
      handler     = "src.handlers.facebook_handlers.scrape_reviews"
      memory_size = 1300
      timeout     = 900
    }
    scrapeFacebookPhotos = {
      handler     = "src.handlers.facebook_handlers.scrape_photos"
      memory_size = 1300
      timeout     = 900
    }
    scrapeBetterBusinessBureauBusiness = {
      handler     = "src.handlers.better_business_bureau.scrape_business"
      memory_size = 512
      timeout     = 750
    }
    scrapeBetterBusinessBureauReviews = {
      handler     = "src.handlers.better_business_bureau.scrape_reviews"
      memory_size = 512
      timeout     = 900
    }
    scrapeBetterBusinessBureauComplaints = {
      handler     = "src.handlers.better_business_bureau.scrape_complaints"
      memory_size = 512
      timeout     = 900
    }
    scrapeOSHAInspection = {
      handler     = "src.handlers.osha_handlers.scrape_inspection"
      memory_size = 512
      timeout     = 900
      environment = {
        OSHA_API_V4_KEY_ARN               = local.region_specific_parameters.runtime_secret_arns.osha-api-v4-key
        SAFE_INIT_RESOLVE_SECRETS         = "true"
        SAFE_INIT_CACHE_SECRETS           = "true"
        SAFE_INIT_SECRET_CACHE_REDIS_HOST = local.redis_cache_host
        SAFE_INIT_SECRET_CACHE_REDIS_PORT = local.redis_cache_port
      }
    }
    processInspection = {
      handler     = "src.handlers.osha_handlers.process_inspection"
      memory_size = 450
      timeout     = 900
      environment = {
        OSHA_API_V4_KEY_ARN               = local.region_specific_parameters.runtime_secret_arns.osha-api-v4-key
        SAFE_INIT_RESOLVE_SECRETS         = "true"
        SAFE_INIT_CACHE_SECRETS           = "true"
        SAFE_INIT_SECRET_CACHE_REDIS_HOST = local.redis_cache_host
        SAFE_INIT_SECRET_CACHE_REDIS_PORT = local.redis_cache_port
      }
    }
    processManualOshaUpload = {
      handler     = "src.handlers.osha_handlers.process_manual_osha_upload"
      memory_size = 512
      timeout     = 900
      environment = {
        OSHA_API_V4_KEY_ARN               = local.region_specific_parameters.runtime_secret_arns.osha-api-v4-key
        SAFE_INIT_RESOLVE_SECRETS         = "true"
        SAFE_INIT_CACHE_SECRETS           = "true"
        SAFE_INIT_SECRET_CACHE_REDIS_HOST = local.redis_cache_host
        SAFE_INIT_SECRET_CACHE_REDIS_PORT = local.redis_cache_port
      }
    }
    updatePermitsWithPremises = {
      handler     = "src.handlers.buildzoom_handlers.update_permits_with_premises"
      memory_size = 512
      timeout     = 900
    }
    searchWebsiteViaScaleSERPAndSendEvents = {
      handler     = "src.handlers.scaleserp_handler.search_website_via_scale_serp_and_send_events"
      memory_size = 420
      timeout     = 900
    }
    scrapeUnicourtCaseDetails = {
      handler     = "src.handlers.unicourt_handler.scrape_case_details"
      memory_size = 450
      timeout     = 900
    }
    loadUnicourtCases = {
      handler     = "src.handlers.unicourt_handler.load_unicourt_cases"
      memory_size = 512
      timeout     = 900
    }
    ingestSAM = {
      handler     = "src.handlers.sam_handlers.ingest_sam"
      memory_size = 2450
      timeout     = 900
      environment = {
        INGESTION_DATA_BUCKET = local.ingestion_data_bucket
      }
    }
    scrapeOpencorporates = {
      handler     = "src.handlers.opencorporates_handler.scrape_opencorporates"
      memory_size = 512
      timeout     = 900
    }
    scrapeFMCSABusiness = {
      handler     = "src.handlers.fmcsa_handlers.scrape_business"
      memory_size = 512
      timeout     = 900
    }
    scrapeFMCSACrashes = {
      handler     = "src.handlers.fmcsa_dependent_handlers.scrape_crashes"
      memory_size = 768
      timeout     = 900
    }
    scrapeFMCSAComplaints = {
      handler     = "src.handlers.fmcsa_dependent_handlers.scrape_complaints"
      memory_size = 512
      timeout     = 900
    }
    scrapeFMCSAInspections = {
      handler                = "src.handlers.fmcsa_dependent_handlers.scrape_inspections"
      memory_size            = 2200
      timeout                = 900
      ephemeral_storage_size = 1024
    }
    loadFMCSAVehicles = {
      handler     = "src.handlers.fmcsa_dependent_handlers.load_vehicles"
      memory_size = 512
      timeout     = 900
    }
    scrapeSaferFMCSABusiness = {
      handler     = "src.handlers.fmcsa_handlers.scrape_safer_business"
      memory_size = 512
      timeout     = 900
    }
    scrapeApartments = {
      handler     = "src.handlers.apartments_handler.scrape_business"
      memory_size = 512
      timeout     = 600
    }
    scrapeIrsForm990 = {
      handler     = "src.handlers.irs_form_990.scrape_business"
      memory_size = 512
      timeout     = 600
      environment = {
        LAUNCH_DARKLY_API_KEY_SECRET_ARN = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
    }
    ingestCPSC = {
      handler     = "src.handlers.recalls_handlers.ingest_cpsc_recalls"
      memory_size = 540
      timeout     = 900
    }
    ingestFSIS = {
      handler     = "src.handlers.recalls_handlers.ingest_fsis"
      memory_size = 512
      timeout     = 900
    }
    notifyUnresolvedOrganizations = {
      handler     = "src.handlers.recalls_handlers.notify_unresolved_organizations"
      memory_size = 512
      timeout     = 120
    }
    searchNhtsaFarsReport = {
      handler     = "src.handlers.nhtsa_fars_handlers.handle_nhtsa_fars_search"
      memory_size = 512
      timeout     = 900
    }
    identifierCreatedSelectScrapers = {
      handler     = "src.handlers.external_identifiers_handlers.select_scrapers"
      memory_size = 420
      timeout     = 900
    }
    flagInvalidCoveragesForNationwideReport = {
      handler     = "src.handlers.file_uploaded_handlers.flag_invalid_coverages_for_nationwide_report"
      memory_size = 440
      timeout     = 900
      environment = {
        SUBMISSIONS_UPLOAD_BUCKET  = local.submissions_upload_bucket
        NATIONWIDE_ORGANIZATION_ID = local.nationwide_organization_id
      }
    }
    getNationwidePremiumRows = {
      handler     = "src.handlers.file_uploaded_handlers.get_nationwide_premium_rows"
      memory_size = 512
      timeout     = 900
      environment = {
        SUBMISSIONS_UPLOAD_BUCKET  = local.submissions_upload_bucket
        NATIONWIDE_ORGANIZATION_ID = local.nationwide_organization_id
      }
    }
    syncNationwidePremiumRow = {
      handler     = "src.handlers.file_uploaded_handlers.sync_nationwide_premium_row"
      memory_size = 256
      timeout     = 900
      environment = {
        SUBMISSIONS_UPLOAD_BUCKET  = local.submissions_upload_bucket
        NATIONWIDE_ORGANIZATION_ID = local.nationwide_organization_id
      }
    }
    syncSplitIntoChunks = {
      handler     = "src.handlers.submission_sync.chunk_sync.split_into_chunks"
      memory_size = 440
      timeout     = 300
      environment = {
      }
    }
    syncSynchronizeChunk = {
      handler     = "src.handlers.submission_sync.chunk_sync.sync_status_report_chunk"
      memory_size = 440
      timeout     = 900
      environment = {
      }
    }
    syncSendNotificationsAndUploadData = {
      handler     = "src.handlers.submission_sync.reporting.send_notifications_and_upload_data"
      memory_size = 2048
      timeout     = 240
      environment = {
        SUBMISSIONS_GLUE_BUCKET = local.env_config.submissions_glue_bucket
      }
    }
    syncCompleteRequest = {
      handler     = "src.handlers.submission_sync.reporting.complete_request"
      memory_size = 440
      timeout     = 900
      environment = {
      }
    }
    syncQueueHandler = {
      handler     = "src.handlers.submission_sync.schedule.submission_sync_queue_handler"
      memory_size = 540
      timeout     = 300
      environment = {
      }

      triggers = [
        { schedule = local.schedules.kalepa_submission_sync_queue }
      ]
    }
    syncQueueNotificationHandler = {
      handler     = "src.handlers.submission_sync.schedule.submission_sync_queue_notification_handler"
      memory_size = 440
      timeout     = 300
      environment = {
      }

      triggers = [
        { schedule = local.schedules.kalepa_submission_sync_queue }
      ]
    }
    updateSubmissionSyncQueue = {
      handler     = "src.handlers.submission_sync.update_submission_sync_queue.update_submission_sync_queue"
      memory_size = 512
      timeout     = 300
      environment = {
      }
    }
    allyAutoSftpProcessor = {
      handler              = "src.handlers.sftp_processor.process_sftp_folder"
      memory_size          = 2048
      timeout              = 600
      reserved_concurrency = 1
      environment = {
        SFTP_BUCKET_NAME                = "kalepa-sftp-bucket-us-east-1"
        KALEPA_CLIENT                   = "ally-auto"
        KALEPA_CLIENT_EMAIL_ACCOUNT_KEY = "allyauto"
        KALEPA_GROUP_EMAIL              = "<EMAIL>"
        GMAIL_CREDENTIALS               = local.gmail_credentials
      }

      triggers = [
        { schedule = local.schedules.sftp_processor }
      ]
    }

    getErisa5500ForPlan = {
      handler     = "src.handlers.efast.get_erisa_5500_for_plan"
      memory_size = 512
      timeout     = 300
      environment = {
        SAFE_INIT_RESOLVE_SECRETS         = "true"
        SAFE_INIT_CACHE_SECRETS           = "true"
        SAFE_INIT_SECRET_CACHE_REDIS_HOST = local.redis_cache_host
        SAFE_INIT_SECRET_CACHE_REDIS_PORT = local.redis_cache_port
        LAUNCH_DARKLY_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
    }

    batchComputeIsoFromGVWRSqsHandler = {
      handler     = "src.handlers.to_iso_vehicle_rating_handler.batch_compute_iso_vehicle_rating_from_gvwr_sqs"
      memory_size = 512
      timeout     = 100

      triggers = [
        {
          sqs = {
            queue_arn               = local.gvwr_iso_computing_sqs_arn
            message_batch_size      = 20
            maximum_concurrency     = 10
            function_response_types = []
          }
        }
      ]
    }

    batchDecodeVinDlqHandler = {
      handler     = "src.handlers.nhtsa_handlers.vins_dlq_handler"
      memory_size = 512
      timeout     = 300

      environment = {
        VIN_SQS_DEADLETTER_QUEUE_URL = local.vin_decoding_dlq_sqs_url
        VIN_SQS_QUEUE_URL            = local.vin_decoding_sqs_url
        NUMBER_OF_REDRIVE_BATCHES    = 50 # each batch is up to 10 messages
      }

      triggers = [
        { schedule = local.schedules.vin_decoding_dlq_sqs_handler }
      ]
    }

    batchDecodeVinSqsHandler = {
      handler     = "src.handlers.nhtsa_handlers.decode_vins_batch_from_sqs"
      memory_size = 512
      timeout     = 600

      environment = {
        BATCH_COMPUTE_ISO_VEHICLE_RATING_LAMBDA_NAME = local.batch_compute_iso_vehicle_rating_lambda_name
      }

      triggers = [
        {
          sqs = {
            queue_arn           = local.vin_decoding_sqs_arn
            message_batch_size  = 20
            maximum_concurrency = 10
            function_response_types = [
              "ReportBatchItemFailures"
            ]
          }
        }
      ]
    }

    batchComputeISOVehicleRating = {
      handler              = "src.handlers.to_iso_vehicle_rating_handler.batch_compute_iso_vehicle_rating_for_vins"
      memory_size          = 512
      timeout              = 900
      reserved_concurrency = 50
      environment = {
        BATCH_COMPUTE_ISO_VEHICLE_RATING_LAMBDA_NAME = local.batch_compute_iso_vehicle_rating_lambda_name
      }
    }

    nwUpdatePolicyNumber = {
      handler     = "src.handlers.nw_update_policy_number.handle"
      memory_size = 512
      timeout     = 300
      environment = {
        REDSHIFT_CONFIG_SECRET = local.regional_secret_arns["redshift-ingestion"]
      }
    }

    getRelevantScrapingMetadata = {
      handler     = "src.handlers.scraping_metadata.get_relevant_scraping_metadata"
      memory_size = 1024
      timeout     = 300
    }

    equipmentRelationshipUpdated = {
      handler     = "src.handlers.equipment_relationship_update_handler.on_equipment_relationship_update"
      memory_size = 512
      timeout     = 900
      environment = {
        SAFE_INIT_RESOLVE_SECRETS         = "true"
        SAFE_INIT_CACHE_SECRETS           = "true"
        SAFE_INIT_SECRET_CACHE_REDIS_HOST = local.redis_cache_host
        SAFE_INIT_SECRET_CACHE_REDIS_PORT = local.redis_cache_port
        LAUNCH_DARKLY_API_KEY_SECRET_ARN  = local.region_specific_parameters.runtime_secret_arns.launch-darkly-api-key
      }
      triggers = [
        {
          eventBridge = {
            event_bus = local.facts_event_bus_arn
            event_pattern = {
              source = ["facts_api"]
              detail = {
                fact_created = {
                  parent_type = ["SUBMISSION"]
                  fact_subtype = {
                    id = ["EQUIPMENT"]
                  }
                }
              }
            }
          }
        }
      ]
    }

    securaDailyMappingProcessor = {
      handler     = "src.handlers.secura.daily_mappings_processor.process_daily_mappings_from_s3"
      memory_size = 512
      timeout     = 120
      environment = {}

      triggers = [
        {
          s3 = {
            bucket               = local.submissions_upload_bucket
            event                = "s3:ObjectCreated:*"
            object_filter_prefix = "secura/daily_mappings/"
          }
        }
      ]
    }
  }

  submissions_upload_bucket                    = local.region_specific_parameters.s3_buckets.submissions.name
  gmail_credentials                            = local.region_specific_parameters.runtime_secret_arns.gmail-forwarder-credentials
  sam_api_key                                  = local.region_specific_parameters.runtime_secret_arns.sam-api-key
  luminati_api_token                           = local.region_specific_parameters.runtime_secret_arns.luminati-api-key
  monitor_nationwide_sync_sf_arn               = "arn:aws:states:us-east-1:${local.workflow_parameters.account_id}:stateMachine:${local.workflow_parameters.workload_name}-sync-sf-from-nationwide"
  fmcsa_custom_search_id                       = local.region_specific_parameters.runtime_secret_arns.fmcsa-custom-search-id
  buildzoom_custom_search_id                   = local.region_specific_parameters.runtime_secret_arns.buildzoom-custom-search-id
  google_api_key                               = local.region_specific_parameters.runtime_secret_arns.google-search-api-key
  yelp_api_key                                 = local.region_specific_parameters.runtime_secret_arns.yelp-api-key
  unicourt_custom_search_id                    = local.region_specific_parameters.runtime_secret_arns.unicourt-custom-search-id
  good_jobs_first_custom_search_id             = local.region_specific_parameters.runtime_secret_arns.good-jobs-first-custom-search-id
  houzz_custom_search_id                       = local.region_specific_parameters.runtime_secret_arns.houzz-custom-search-id
  iqs_custom_search_id                         = local.region_specific_parameters.runtime_secret_arns.iqs-custom-search-id
  thomasnet_custom_search_id                   = local.region_specific_parameters.runtime_secret_arns.thomasnet-custom-search-id
  safer_fmcsa_custom_search_id                 = local.region_specific_parameters.runtime_secret_arns.safer-fmcsa-custom-search-id
  better_business_bureau_custom_search_id      = local.region_specific_parameters.runtime_secret_arns.better-business-bureau-custom-search-id
  tripadvisor_custom_search_id                 = local.region_specific_parameters.runtime_secret_arns.tripadvisor-custom-search-id
  yelp_custom_search_id                        = local.region_specific_parameters.runtime_secret_arns.yelp-custom-search-id
  fda_secret                                   = local.region_specific_parameters.runtime_secret_arns.fda-users-and-keys
  job_queue                                    = "ingestion"
  submissions_glue_bucket                      = "kalepa-${local.env}-glue-us-east-1"
  nationwide_organization_id                   = 6
  arch_organization_id                         = 10
  munich_re_organization_id                    = 36
  amplitude_files_bucket                       = "kalepa-${local.env}-amplitude-export-us-east-1"
  facts_event_bus_arn                          = "arn:aws:events:us-east-1:${local.workflow_parameters.account_id}:event-bus/facts-api"
  compute_iso_vehicle_rating_lambda_name       = "${local.lambda_name_prefix}compute-iso-vehicle-rating-lambda"
  batch_compute_iso_vehicle_rating_lambda_name = "${local.lambda_name_prefix}batch-compute-iso-vehicle-rating-lambda"
  dlq_sqs_arn                                  = local.region_specific_parameters.events.ingestion_dlq_sqs.arn

  vin_decoding_sqs_arn     = local.region_specific_parameters.events.vin_decoding_sqs.arn
  vin_decoding_sqs_url     = local.region_specific_parameters.events.vin_decoding_sqs.url
  vin_decoding_dlq_sqs_url = local.region_specific_parameters.events.vin_decoding_dlq_sqs.url

  gvwr_iso_computing_sqs_arn = local.region_specific_parameters.events.gvwr_iso_computing_sqs.arn

  lambdas = merge(local.lambdas_app, local.lambdas_tests)
}
