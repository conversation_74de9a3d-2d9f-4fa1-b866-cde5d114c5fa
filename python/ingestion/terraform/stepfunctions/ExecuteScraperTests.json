{"StartAt": "TriggerTests", "States": {"TriggerTests": {"Type": "Task", "Resource": "${triggerTests_lambda_arn}", "Next": "ParallelTestSources"}, "ParallelTestSources": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "MapTestFacebook", "States": {"MapTestFacebook": {"Type": "Map", "ItemsPath": "$.facebook", "ResultPath": "$.results", "Next": "AggregateTestFacebook", "Iterator": {"StartAt": "ParallelFacebookTests", "States": {"ParallelFacebookTests": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "TestFacebookBusiness", "States": {"TestFacebookBusiness": {"Type": "Task", "Resource": "${facebookBusinessTest_lambda_arn}", "End": true}}}, {"StartAt": "TestFacebookReviews", "States": {"TestFacebookReviews": {"Type": "Task", "Resource": "${facebookReviewsTest_lambda_arn}", "End": true}}}, {"StartAt": "TestFacebookPhotos", "States": {"TestFacebookPhotos": {"Type": "Task", "Resource": "${facebookPhotosTest_lambda_arn}", "End": true}}}, {"StartAt": "TestFacebookPosts", "States": {"TestFacebookPosts": {"Type": "Task", "Resource": "${facebookPostsTest_lambda_arn}", "End": true}}}]}}}}, "AggregateTestFacebook": {"Type": "Task", "Resource": "${aggregateFacebookTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestGoogleBusiness", "States": {"MapTestGoogleBusiness": {"Type": "Map", "ItemsPath": "$.google_business", "ResultPath": "$.results", "Next": "AggregateTestGoogleBusiness", "Iterator": {"StartAt": "ParallelGoogleBusinessTests", "States": {"ParallelGoogleBusinessTests": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "TestGoogleBusiness", "States": {"TestGoogleBusiness": {"Type": "Task", "Resource": "${googleBusinessTest_lambda_arn}", "End": true}}}, {"StartAt": "TestGoogleReviews", "States": {"TestGoogleReviews": {"Type": "Task", "Resource": "${googleReviewsTest_lambda_arn}", "End": true}}}]}}}}, "AggregateTestGoogleBusiness": {"Type": "Task", "Resource": "${aggregateGoogleBusinessTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestGoogleImages", "States": {"MapTestGoogleImages": {"Type": "Map", "ItemsPath": "$.google_image", "ResultPath": "$.results", "Next": "AggregateTestGoogleImages", "Iterator": {"StartAt": "TestGoogleImage", "States": {"TestGoogleImage": {"Type": "Task", "Resource": "${googleImagesTest_lambda_arn}", "End": true}}}}, "AggregateTestGoogleImages": {"Type": "Task", "Resource": "${aggregateGoogleImagesTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestYelpReviews", "States": {"MapTestYelpReviews": {"Type": "Map", "ItemsPath": "$.yelp_reviews", "ResultPath": "$.results", "Next": "AggregateTestYelpReviews", "Iterator": {"StartAt": "TestYelpReviews", "States": {"TestYelpReviews": {"Type": "Task", "Resource": "${yelpReviewsTest_lambda_arn}", "End": true}}}}, "AggregateTestYelpReviews": {"Type": "Task", "Resource": "${aggregateYelpReviewsTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestYelpBusiness", "States": {"MapTestYelpBusiness": {"Type": "Map", "ItemsPath": "$.yelp_business", "ResultPath": "$.results", "Next": "AggregateTestYelpBusiness", "Iterator": {"StartAt": "ParallelTestYelpBusiness", "States": {"ParallelTestYelpBusiness": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "TestYelpBusiness", "States": {"TestYelpBusiness": {"Type": "Task", "Resource": "${yelpBusinessTest_lambda_arn}", "End": true}}}, {"StartAt": "TestYelpPhotos", "States": {"TestYelpPhotos": {"Type": "Task", "Resource": "${yelpPhotosTest_lambda_arn}", "End": true}}}]}}}}, "AggregateTestYelpBusiness": {"Type": "Task", "Resource": "${aggregateYelpBusinessTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestYelpMenu", "States": {"MapTestYelpMenu": {"Type": "Map", "ItemsPath": "$.yelp_menu", "ResultPath": "$.results", "Next": "AggregateTestYelpMenu", "Iterator": {"StartAt": "TestYelpMenu", "States": {"TestYelpMenu": {"Type": "Task", "Resource": "${yelpMenuTest_lambda_arn}", "End": true}}}}, "AggregateTestYelpMenu": {"Type": "Task", "Resource": "${aggregateYelpMenuTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestTripAdvisor", "States": {"MapTestTripAdvisor": {"Type": "Map", "ItemsPath": "$.tripadvisor", "ResultPath": "$.results", "Next": "AggregateTestTripAdvisor", "Iterator": {"StartAt": "ParallelTripAdvisorTests", "States": {"ParallelTripAdvisorTests": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "TestTripAdvisorBusiness", "States": {"TestTripAdvisorBusiness": {"Type": "Task", "Resource": "${tripadvisorBusinessTest_lambda_arn}", "End": true}}}, {"StartAt": "TestTripAdvisorReviews", "States": {"TestTripAdvisorReviews": {"Type": "Task", "Resource": "${tripadvisorReviewsTest_lambda_arn}", "End": true}}}]}}}}, "AggregateTestTripAdvisor": {"Type": "Task", "Resource": "${aggregateTripAdvisorTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestBetterBusinessBureau", "States": {"MapTestBetterBusinessBureau": {"Type": "Map", "ItemsPath": "$.better_business_bureau", "ResultPath": "$.results", "Next": "AggregateBetterBusinessBureauTests", "Iterator": {"StartAt": "ParallelBetterBusinessBureauTests", "States": {"ParallelBetterBusinessBureauTests": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "TestBetterBusinessBureau", "States": {"TestBetterBusinessBureau": {"Type": "Task", "Resource": "${betterBusinessBureauBusinessTest_lambda_arn}", "End": true}}}, {"StartAt": "TestBetterBusinessBureauReviews", "States": {"TestBetterBusinessBureauReviews": {"Type": "Task", "Resource": "${betterBusinessBureauReviewsTest_lambda_arn}", "End": true}}}, {"StartAt": "TestBetterBusinessBureauComplaint", "States": {"TestBetterBusinessBureauComplaint": {"Type": "Task", "Resource": "${betterBusinessBureauComplaintTest_lambda_arn}", "End": true}}}]}}}}, "AggregateBetterBusinessBureauTests": {"Type": "Task", "Resource": "${aggregateBetterBusinessBureauTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestBuildZoom", "States": {"MapTestBuildZoom": {"Type": "Map", "ItemsPath": "$.build_zoom", "ResultPath": "$.results", "Next": "AggregateBuildZoomTests", "Iterator": {"StartAt": "ParallelBuildZoomTests", "States": {"ParallelBuildZoomTests": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "TestBuildZoomBusiness", "States": {"TestBuildZoomBusiness": {"Type": "Task", "Resource": "${buildZoomBusinessTest_lambda_arn}", "End": true}}}, {"StartAt": "TestBuildZoomPermits", "States": {"TestBuildZoomPermits": {"Type": "Task", "Resource": "${buildZoomPermitsTests_lambda_arn}", "End": true}}}]}}}}, "AggregateBuildZoomTests": {"Type": "Task", "Resource": "${aggregateBuildZoomTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestThomasnetBusiness", "States": {"MapTestThomasnetBusiness": {"Type": "Map", "ItemsPath": "$.thomasnet_business", "ResultPath": "$.results", "Next": "AggregateTestThomasnetBusiness", "Iterator": {"StartAt": "ParallelTestThomasnetBusiness", "States": {"ParallelTestThomasnetBusiness": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "TestThomasnetBusiness", "States": {"TestThomasnetBusiness": {"Type": "Task", "Resource": "${thomasnetBusinessTest_lambda_arn}", "End": true}}}]}}}}, "AggregateTestThomasnetBusiness": {"Type": "Task", "Resource": "${aggregateTestThomasnetBusiness_lambda_arn}", "End": true}}}, {"StartAt": "MapTestThomasnetProducts", "States": {"MapTestThomasnetProducts": {"Type": "Map", "ItemsPath": "$.thomasnet_products", "ResultPath": "$.results", "Next": "AggregateTestThomasnetProducts", "Iterator": {"StartAt": "ParallelTestThomasnetProducts", "States": {"ParallelTestThomasnetProducts": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "TestThomasnetProducts", "States": {"TestThomasnetProducts": {"Type": "Task", "Resource": "${thomasnetProductsTest_lambda_arn}", "End": true}}}]}}}}, "AggregateTestThomasnetProducts": {"Type": "Task", "Resource": "${aggregateTestThomasnetProducts_lambda_arn}", "End": true}}}, {"StartAt": "MapTestHouzzBusiness", "States": {"MapTestHouzzBusiness": {"Type": "Map", "ItemsPath": "$.houzz_business", "ResultPath": "$.results", "Next": "AggregateTestHouzzBusiness", "Iterator": {"StartAt": "ParallelTestHouzzBusiness", "States": {"ParallelTestHouzzBusiness": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "TestHouzzBusiness", "States": {"TestHouzzBusiness": {"Type": "Task", "Resource": "${houzzBusinessTest_lambda_arn}", "End": true}}}]}}}}, "AggregateTestHouzzBusiness": {"Type": "Task", "Resource": "${aggregateTestHouzzBusiness_lambda_arn}", "End": true}}}, {"StartAt": "MapTestFmcsaBusinessTests", "States": {"MapTestFmcsaBusinessTests": {"Type": "Map", "ItemsPath": "$.fmcsa_business", "ResultPath": "$.results", "Next": "AggregateFmcsaBusinessTests", "Iterator": {"StartAt": "TestFmcsaBusiness", "States": {"TestFmcsaBusiness": {"Type": "Task", "Resource": "${fmcsaBusinessTest_lambda_arn}", "End": true}}}}, "AggregateFmcsaBusinessTests": {"Type": "Task", "Resource": "${aggregateFmcsaBusinessTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestFmcsaCrashTests", "States": {"MapTestFmcsaCrashTests": {"Type": "Map", "ItemsPath": "$.fmcsa_crashes", "ResultPath": "$.results", "Next": "AggregateFmcsaCrashTests", "Iterator": {"StartAt": "TestFmcsaCrash", "States": {"TestFmcsaCrash": {"Type": "Task", "Resource": "${fmcsaCrashTest_lambda_arn}", "End": true}}}}, "AggregateFmcsaCrashTests": {"Type": "Task", "Resource": "${aggregateFmcsaCrashTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestFmcsaInspectionTests", "States": {"MapTestFmcsaInspectionTests": {"Type": "Map", "ItemsPath": "$.fmcsa_inspections", "ResultPath": "$.results", "Next": "AggregateFmcsaInspectionTests", "Iterator": {"StartAt": "TestFmcsaInspection", "States": {"TestFmcsaInspection": {"Type": "Task", "Resource": "${fmcsaInspectionTest_lambda_arn}", "End": true}}}}, "AggregateFmcsaInspectionTests": {"Type": "Task", "Resource": "${aggregateFmcsaInspectionTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestFmcsaComplaintTests", "States": {"MapTestFmcsaComplaintTests": {"Type": "Map", "ItemsPath": "$.fmcsa_complaints", "ResultPath": "$.results", "Next": "AggregateFmcsaComplaintTests", "Iterator": {"StartAt": "TestFmcsaComplaint", "States": {"TestFmcsaComplaint": {"Type": "Task", "Resource": "${fmcsaComplaintTest_lambda_arn}", "End": true}}}}, "AggregateFmcsaComplaintTests": {"Type": "Task", "Resource": "${aggregateFmcsaComplaintTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestFmcsaHistoryTests", "States": {"MapTestFmcsaHistoryTests": {"Type": "Map", "ItemsPath": "$.fmcsa_history", "ResultPath": "$.results", "Next": "AggregateFmcsaHistoryTests", "Iterator": {"StartAt": "TestFmcsaHistory", "States": {"TestFmcsaHistory": {"Type": "Task", "Resource": "${fmcsaHistoryTest_lambda_arn}", "End": true}}}}, "AggregateFmcsaHistoryTests": {"Type": "Task", "Resource": "${aggregateFmcsaHistoryTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestFDAImportAlerts", "States": {"MapTestFDAImportAlerts": {"Type": "Map", "ItemsPath": "$.fda_import_alerts", "ResultPath": "$.results", "Next": "AggregateFDAImportAlertsTests", "Iterator": {"StartAt": "ParallelFDAImportAlertsTests", "States": {"ParallelFDAImportAlertsTests": {"Type": "<PERSON><PERSON><PERSON>", "End": true, "Branches": [{"StartAt": "FDAImportAlertsTests", "States": {"FDAImportAlertsTests": {"Type": "Task", "Resource": "${fdaImportAlertDetailsTest_lambda_arn}", "End": true}}}]}}}}, "AggregateFDAImportAlertsTests": {"Type": "Task", "Resource": "${aggregateFDAImportAlertsTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestOpenCorporates", "States": {"MapTestOpenCorporates": {"Type": "Map", "ItemsPath": "$.opencorporates", "ResultPath": "$.results", "Next": "AggregateTestOpenCorporates", "Iterator": {"StartAt": "TestOpenCorporates", "States": {"TestOpenCorporates": {"Type": "Task", "Resource": "${opencorporatesTest_lambda_arn}", "End": true}}}}, "AggregateTestOpenCorporates": {"Type": "Task", "Resource": "${aggregateOpenCorporatesTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestOshaInspections", "States": {"MapTestOshaInspections": {"Type": "Map", "ItemsPath": "$.osha_inspections", "ResultPath": "$.results", "Next": "AggregateTestOshaInspection", "Iterator": {"StartAt": "TestOshaInspection", "States": {"TestOshaInspection": {"Type": "Task", "Resource": "${oshaInspectionTest_lambda_arn}", "End": true}}}}, "AggregateTestOshaInspection": {"Type": "Task", "Resource": "${aggregateOshaInspectionTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestGoodJobFirst", "States": {"MapTestGoodJobFirst": {"Type": "Map", "ItemsPath": "$.goodjobfirst", "ResultPath": "$.results", "Next": "AggregateTestGoodJobFirst", "Iterator": {"StartAt": "TestGoodJobFirst", "States": {"TestGoodJobFirst": {"Type": "Task", "Resource": "${goodjobfirstTest_lambda_arn}", "End": true}}}}, "AggregateTestGoodJobFirst": {"Type": "Task", "Resource": "${aggregateGoodJobFirstTests_lambda_arn}", "End": true}}}, {"StartAt": "MapTestCACSLB", "States": {"MapTestCACSLB": {"Type": "Map", "ItemsPath": "$.ca_cslb", "ResultPath": "$.results", "End": true, "Iterator": {"StartAt": "TestCACSLB", "States": {"TestCACSLB": {"Type": "Task", "Resource": "${caCslbTest_lambda_arn}", "End": true}}}}}}]}}}