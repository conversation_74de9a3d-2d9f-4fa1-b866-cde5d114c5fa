{"Comment": "Search all sources to find business entities", "StartAt": "SearchAndIdentifyAllSourcesParallel", "States": {"SearchAndIdentifyAllSourcesParallel": {"Type": "<PERSON><PERSON><PERSON>", "ResultPath": "$.search_results", "Next": "IdentifyBusinesses", "Branches": [{"StartAt": "MaybeSearchYelp", "States": {"MaybeSearchYelp": {"Type": "Task", "Resource": "${searchYelp_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchYelpFailed"}], "End": true}, "MaybeSearchYelpFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchEPA", "States": {"MaybeSearchEPA": {"Type": "Task", "Resource": "${searchEPA_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchEPAFailed"}], "End": true}, "MaybeSearchEPAFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchOsha", "States": {"MaybeSearchOsha": {"Type": "Task", "Resource": "${searchOsha_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchOshaFailed"}], "End": true}, "MaybeSearchOshaFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchEfast", "States": {"MaybeSearchEfast": {"Type": "Task", "Resource": "${searchEfast_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchEfastFailed"}], "End": true}, "MaybeSearchEfastFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchFDA", "States": {"MaybeSearchFDA": {"Type": "Task", "Resource": "${searchFDA_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchFDAFailed"}], "End": true}, "MaybeSearchFDAFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchTripAdvisor", "States": {"MaybeSearchTripAdvisor": {"Type": "Task", "Resource": "${searchTripAdvisor_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchTripAdvisorFailed"}], "End": true}, "MaybeSearchTripAdvisorFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchGoogleLocal", "States": {"MaybeSearchGoogleLocal": {"Type": "Task", "Resource": "${searchGoogleLocal_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchGoogleLocalFailed"}], "End": true}, "MaybeSearchGoogleLocalFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchFacebook", "States": {"MaybeSearchFacebook": {"Type": "Task", "Resource": "${searchFacebook_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchFacebookFailed"}], "End": true}, "MaybeSearchFacebookFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchBuildZoom", "States": {"MaybeSearchBuildZoom": {"Type": "Task", "Resource": "${searchBuildZoom_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchBuildZoomFailed"}], "End": true}, "MaybeSearchBuildZoomFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchBetterBusinessBureau", "States": {"MaybeSearchBetterBusinessBureau": {"Type": "Task", "Resource": "${searchBetterBusinessBureau_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchBetterBusinessBureauFailed"}], "End": true}, "MaybeSearchBetterBusinessBureauFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchThomasnet", "States": {"MaybeSearchThomasnet": {"Type": "Task", "Resource": "${searchThomasnet_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchThomasnetFailed"}], "End": true}, "MaybeSearchThomasnetFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchIqs", "States": {"MaybeSearchIqs": {"Type": "Task", "Resource": "${searchIqs_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchIqsFailed"}], "End": true}, "MaybeSearchIqsFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchHouzz", "States": {"MaybeSearchHouzz": {"Type": "Task", "Resource": "${searchHouzz_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchHouzzFailed"}], "End": true}, "MaybeSearchHouzzFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchFMCSA", "States": {"MaybeSearchFMCSA": {"Type": "Task", "Resource": "${searchFMCSA_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchFMCSAFailed"}], "End": true}, "MaybeSearchFMCSAFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchDotReport", "States": {"MaybeSearchDotReport": {"Type": "Task", "Resource": "${searchDotReport_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchDotReportFailed"}], "End": true}, "MaybeSearchDotReportFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchSaferFMCSA", "States": {"MaybeSearchSaferFMCSA": {"Type": "Task", "Resource": "${searchSaferFMCSAReport_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchSaferFMCSAFailed"}], "End": true}, "MaybeSearchSaferFMCSAFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchGoodJobsFirst", "States": {"MaybeSearchGoodJobsFirst": {"Type": "Task", "Resource": "${searchGoodJobsFirst_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchGoodJobsFirstFailed"}], "End": true}, "MaybeSearchGoodJobsFirstFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchUnicourt", "States": {"MaybeSearchUnicourt": {"Type": "Task", "Resource": "${searchUnicourt_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchUnicourtFailed"}], "End": true}, "MaybeSearchUnicourtFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchSam", "States": {"MaybeSearchSam": {"Type": "Task", "Resource": "${searchSam_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchSamFailed"}], "End": true}, "MaybeSearchSamFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchApartments", "States": {"MaybeSearchApartments": {"Type": "Task", "Resource": "${searchApartments_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchApartmentsFailed"}], "End": true}, "MaybeSearchApartmentsFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchIrsForm990", "States": {"MaybeSearchIrsForm990": {"Type": "Task", "Resource": "${searchIrsForm990_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchIrsForm990Failed"}], "End": true}, "MaybeSearchIrsForm990Failed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchOpencorporates", "States": {"MaybeSearchOpencorporates": {"Type": "Task", "Resource": "${searchOpencorporates_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchOpencorporatesFailed"}], "End": true}, "MaybeSearchOpencorporatesFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}, {"StartAt": "MaybeSearchCACSLB", "States": {"MaybeSearchCACSLB": {"Type": "Task", "Resource": "${searchCACSLBLicense_lambda_arn}", "Catch": [{"ErrorEquals": ["States.TaskFailed", "States.Timeout"], "Next": "MaybeSearchCACSLBFailed"}], "End": true}, "MaybeSearchCACSLBFailed": {"Type": "Task", "Parameters": {"execution_id.$": "$$.Execution.Id", "error.$": "$.Error", "cause.$": "$.Cause"}, "Resource": "${emitException_lambda_arn}", "End": true}}}]}, "IdentifyBusinesses": {"Type": "Task", "Resource": "${identifyBusinesses_lambda_arn}", "End": true}}}