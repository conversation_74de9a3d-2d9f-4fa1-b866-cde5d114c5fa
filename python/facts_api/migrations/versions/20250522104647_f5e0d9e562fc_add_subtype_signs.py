from uuid import uuid4

from alembic import op
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID
from static_common.enums.group import GroupID
from static_common.enums.parent import ParentType
from static_common.enums.units import Units
from static_common.enums.value import AcceptedValueType

from src.enums import SummaryTypeID
from src.utils.migrations.migration_v2 import NewFactSubtypeConfigSchema

revision = "f5e0d9e562fc"
down_revision = "a98fa3084209"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    signs_tiv_config = {
        "fact_subtype_id": FactSubtypeID.SIGNS_TIV.value,
        "fact_type_id": FactTypeID.MEASUREMENT.value,
        "description": "Signs TIV (Total Insured Value) refers to the amount of insurance coverage requested for outdoor or attached signage on the property. This includes signs affixed to buildings or freestanding signs (e.g., pole or monument signs). The TIV represents the maximum value the insurer would pay in the event the sign is damaged or destroyed due to a covered peril (such as fire, wind, vandalism). It is listed under property coverage, typically with a corresponding deductible and no coinsurance.",
        "display_name": "Signs TIV",
        "accepted_value_type": AcceptedValueType.NUMBER.value,
        "accepted_values": {"field": {"min": 0}, "is_selectable": True},
        "accepted_value_hint": "Please provide the insured value of signage.",
        "group_id": GroupID.PROPERTY_INSURANCE_VALUATION.value,
        "is_summarizable": True,
        "is_deprecated": False,
        "is_selectable": True,
        "summary_config": {
            "summary_type_id": SummaryTypeID.MEAN.value,
            "value_attr": "number",
            "units_label": Units.USD,
        },
        "default_parent_type": ParentType.PREMISES.value,
        "parent_type": ParentType.PREMISES.value,
        "line_of_business_id": "COMMERCIAL_PROPERTY",
    }

    fact_subtype_config = NewFactSubtypeConfigSchema().load(signs_tiv_config, many=False)
    fact_subtype_config.insert_all(conn)


def downgrade():
    pass
