from alembic import op

# revision identifiers, used by Alembic.
revision = "7d6ddaa473de"
down_revision = "6ef6d26bbcef"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        f"""
        UPDATE fact_subtype
        SET is_selectable = 'true', is_deprecated = 'false'
        WHERE id = 'REPLACEMENT_COST'
        """
    )
    op.execute(
        f"""
        UPDATE fact_subtype
        SET is_selectable = 'true', is_deprecated = 'false'
        WHERE id = 'CONTENT_VALUE'
        """
    )


def downgrade():
    pass
