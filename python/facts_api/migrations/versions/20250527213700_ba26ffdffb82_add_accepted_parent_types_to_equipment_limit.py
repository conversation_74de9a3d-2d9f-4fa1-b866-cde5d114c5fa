from alembic import op

# revision identifiers, used by Alembic.
revision = "ba26ffdffb82"
down_revision = "8d6ddaa473de"
branch_labels = None
depends_on = None

SLEEP_TIME = 0.5


def upgrade():
    op.execute(
        f"""
               UPDATE fact_subtype
               SET accepted_parent_types = ARRAY['EQUIPMENT', 'STRUCTURE', 'PREMISES']::parenttype[]
               WHERE id = 'EQUIPMENT_LIMIT'
               """
    )


def downgrade():
    ...
