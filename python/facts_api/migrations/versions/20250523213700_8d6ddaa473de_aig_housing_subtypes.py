from uuid import uuid4

from alembic import op
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fact_type import FactTypeID
from static_common.enums.group import GroupID
from static_common.enums.parent import ParentType
from static_common.enums.units import Units
from static_common.enums.value import AcceptedValueType

from src.enums import SummaryTypeID
from src.utils.migrations.migration_v2 import NewFactSubtypeConfigSchema

# revision identifiers, used by Alembic.
revision = "8d6ddaa473de"
down_revision = "7d6ddaa473de"
branch_labels = None
depends_on = None


def upgrade():
    subtypes = [
        {
            "fact_subtype_id": FactSubtypeID.HAS_STUDENT_HOUSING.value,
            "fact_type_id": FactTypeID.BINARY_CLASSIFICATION.value,
            "description": "The building has student housing units intended for college or university students.",
            "display_name": "Has Student Housing?",
            "accepted_value_hint": "Please determine if the building has student housing units intended for college or university students.",
            "accepted_value_type": AcceptedValueType.BOOLEAN.value,
            "group_id": GroupID.OPERATIONS_CARD.value,
            "is_selectable": True,
            "is_summarizable": False,
            "is_deprecated": False,
            "summary_config": {
                "summary_type_id": SummaryTypeID.CATEGORICAL,
            },
            "parent_type": ParentType.BUSINESS.value,
            "default_parent_type": ParentType.BUSINESS.value,
            "line_of_business_id": None,
            "accepted_values": {
                "items": [
                    {"label": "Yes", "value": "YES"},
                    {"label": "No", "value": "NO"},
                    {"label": "Y", "value": "Y"},
                    {"label": "N", "value": "N"},
                    {"label": "True", "value": "TRUE"},
                    {"label": "False", "value": "FALSE"},
                ]
            },
        },
        {
            "fact_subtype_id": FactSubtypeID.HAS_HUD_HOUSING.value,
            "fact_type_id": FactTypeID.BINARY_CLASSIFICATION.value,
            "description": "The entity includes housing that is funded, managed, or regulated by the U.S. Department of Housing and Urban Development (HUD).",
            "display_name": "Has HUD Housing?",
            "accepted_value_hint": "Please determine if the entity includes housing that is funded, managed, or regulated by the U.S. Department of Housing and Urban Development (HUD).",
            "accepted_value_type": AcceptedValueType.BOOLEAN.value,
            "group_id": GroupID.OPERATIONS_CARD.value,
            "is_selectable": True,
            "is_summarizable": False,
            "is_deprecated": False,
            "summary_config": {
                "summary_type_id": SummaryTypeID.CATEGORICAL,
            },
            "parent_type": ParentType.BUSINESS.value,
            "default_parent_type": ParentType.BUSINESS.value,
            "line_of_business_id": None,
            "accepted_values": {
                "items": [
                    {"label": "Yes", "value": "YES"},
                    {"label": "No", "value": "NO"},
                    {"label": "Y", "value": "Y"},
                    {"label": "N", "value": "N"},
                    {"label": "True", "value": "TRUE"},
                    {"label": "False", "value": "FALSE"},
                ]
            },
        },
        {
            "fact_subtype_id": FactSubtypeID.HAS_MODULAR_STRUCTURES.value,
            "fact_type_id": FactTypeID.BINARY_CLASSIFICATION.value,
            "description": "The building has modular structures that allow flexible reconfiguration of internal spaces",
            "display_name": "Has Modular Structures?",
            "accepted_value_hint": "Please determine if the building has modular structures that allow flexible reconfiguration of internal spaces.",
            "accepted_value_type": AcceptedValueType.BOOLEAN.value,
            "group_id": GroupID.OPERATIONS_CARD.value,
            "is_selectable": True,
            "is_summarizable": False,
            "is_deprecated": False,
            "summary_config": {
                "summary_type_id": SummaryTypeID.CATEGORICAL,
            },
            "parent_type": ParentType.BUSINESS.value,
            "default_parent_type": ParentType.BUSINESS.value,
            "line_of_business_id": None,
            "accepted_values": {
                "items": [
                    {"label": "Yes", "value": "YES"},
                    {"label": "No", "value": "NO"},
                    {"label": "Y", "value": "Y"},
                    {"label": "N", "value": "N"},
                    {"label": "True", "value": "TRUE"},
                    {"label": "False", "value": "FALSE"},
                ]
            },
        },
    ]

    with op.get_context().autocommit_block():
        fact_subtype_configs = NewFactSubtypeConfigSchema().load(subtypes, many=True)
        conn = op.get_bind()
        for fact_subtype_config in fact_subtype_configs:
            fact_subtype_config.insert_all(conn)


def downgrade():
    pass
