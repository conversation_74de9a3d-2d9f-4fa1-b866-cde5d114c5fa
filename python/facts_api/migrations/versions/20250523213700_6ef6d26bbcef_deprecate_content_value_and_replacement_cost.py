from alembic import op

# revision identifiers, used by Alembic.
revision = "6ef6d26bbcef"
down_revision = "f5e0d9e562fc"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        f"""
        UPDATE fact_subtype
        SET is_selectable = 'false', is_deprecated = 'true'
        WHERE id = 'REPLACEMENT_COST'
        """
    )
    op.execute(
        f"""
        UPDATE fact_subtype
        SET is_selectable = 'false', is_deprecated = 'true'
        WHERE id = 'CONTENT_VALUE'
        """
    )


def downgrade():
    pass
