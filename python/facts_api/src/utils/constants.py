from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.source_types import SourceTypeID

PRIMARY_DATABASE = "primary_database"
READER_DATABASE = "reader_database"

SNAPSHOT_FACT_SUBTYPES_GROUPS = "SNAPSHOT_TIME_SERIES"
DEFAULT_CREDIBILITY_SCORE = 0.75
"""The default credibility score if no credibility is provided. A fallback mechanism for old data."""
GROUND_TRUTH_CREDIBILITY_SCORE = 1.0
"""The credibility score for Ground Truth Observations."""
DEFAULT_TRUSTED_FEEDBACK_CREDIBILITY_SCORE = GROUND_TRUTH_CREDIBILITY_SCORE
DERIVED_FROM_FEEDBACK_CREDIBILITY_SCORE = 0.9
DAYS_BACK_IN_CREDIBILITY_CALCULATION = 90
WORST_PRECEDENCE = 0
DEFAULT_PRECEDENCE = 1
SOURCE_TYPE_TO_PRECEDENCE: dict[SourceTypeID, int] = {
    **{SourceTypeID.try_parse_str(it): DEFAULT_PRECEDENCE for it in SourceTypeID},
    **{
        SourceTypeID.USER_FEEDBACK: 20,
        SourceTypeID.FIRST_PARTY: 10,
        SourceTypeID.INFERENCE: 5,  # that will be used only if org id is not None
    },
}
"""
The precedence of the source type. The higher the precedence, the more important the source is.
"""


TIV_FACT_SUBTYPES = {
    FactSubtypeID.BPP,
    FactSubtypeID.RENT_INCOME,
    FactSubtypeID.BUILDING_VALUE,
    FactSubtypeID.RPV,
    FactSubtypeID.EDP,
    FactSubtypeID.OTHER_VALUE_TIV,
    FactSubtypeID.BI_EE,
    FactSubtypeID.TOTAL_PROPERTY_VALUE,
    FactSubtypeID.BUSINESS_INCOME,
    FactSubtypeID.AR_LIMIT,
    FactSubtypeID.ED_HARDWARE_LIMIT,
    FactSubtypeID.VALUABLES_LIMIT,
    FactSubtypeID.TOOL_VALUE,
    FactSubtypeID.ED_EXTRA_LIMIT,
    FactSubtypeID.TENANTS_IMPROVEMENTS_AND_BETTERMENTS,
    FactSubtypeID.EQUIPMENT_LIMIT,
    FactSubtypeID.INVENTORY_VALUE,
    FactSubtypeID.SOLAR_PANELS_LIMIT,
    FactSubtypeID.ED_SOFTWARE_LIMIT,
    FactSubtypeID.SIGNS_TIV,
}

# The order is important. The earlier the subtype in the list the higher the priority.
TIV_INTERCHANGEABLE_FACT_SUBTYPES = [
    [FactSubtypeID.BUILDING_VALUE, FactSubtypeID.RPV],
    [FactSubtypeID.BPP, FactSubtypeID.CONTENT_VALUE],
    [FactSubtypeID.BI_EE, FactSubtypeID.BUSINESS_INCOME, FactSubtypeID.RENT_INCOME],
]


ALL_TIV_SUBTYPES = TIV_FACT_SUBTYPES | {FactSubtypeID.TIV, FactSubtypeID.TOTAL_TIV}
