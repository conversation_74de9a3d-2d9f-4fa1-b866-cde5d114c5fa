# Merge Checklist

- [ ] Security impact of change has been considered
- [ ] Code follows [company security practices](https://kalepa.atlassian.net/wiki/spaces/IN/pages/1617592340/Secure+Development+Policy) and [guidelines](https://owasp.org/www-pdf-archive/OWASP_SCP_Quick_Reference_Guide_v2.pdf)
- [ ] If I have modified a query or created a new query, I have included the explanation of the query plan in this PR.
- [ ] I have verified that my migration is safe. See the [Postgres 11 documentation](https://www.postgresql.org/docs/11/sql-altertable.html), this [incomplete cheat sheet](https://leopard.in.ua/2016/09/20/safe-and-unsafe-operations-postgresql#.YElgUXVKgUE), and this [guide](https://gist.github.com/jcoleman/1e6ad1bf8de454c166da94b67537758b).
- [ ] I have verified that my migration is compatible with the blue/green deployment strategy.
- [ ] If I have configured a new fact type or sub-type, I have followed [the steps in the README](https://github.com/Kalepa/facts_api/blob/env/stage/README.md).
- [ ] If I have added a source, I have followed [the steps in the README](https://github.com/Kalepa/facts_api/blob/env/stage/README.md).
- [ ] Tests section below is filled out, describing what kind of tests were performed for this PR

### Tests Performed
