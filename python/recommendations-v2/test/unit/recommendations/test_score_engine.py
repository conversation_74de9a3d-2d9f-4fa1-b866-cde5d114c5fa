from datetime import date, datetime
from unittest.mock import MagicMock, patch
from uuid import UUID, uuid4

import pytest
from static_common.enums.parent import ParentType

from src.recommendations.ml_explanation_utils import (
    MlExplanationDetail,
    MlExplanationDetails,
)
from src.recommendations.models import OutcomeExplanation, OutcomeExplanationTrigger
from src.recommendations.score_engine import ScoreEngine
from src.variables.models import Variable, VariableTypesEnum

POSITIVE_SCORE_VALUE = 0.5
NEGATIVE_SCORE_VALUE = -0.5


@pytest.fixture
def mock_db_session():
    return MagicMock()


@pytest.fixture
def mock_redshift_api_client():
    return MagicMock()


@pytest.fixture
def score_engine(mock_db_session, mock_redshift_api_client):
    return ScoreEngine(session=mock_db_session, redshift_api_client=mock_redshift_api_client)


def __get_mock_variable(
    id: UUID, display_name: str, unit: str | None, variable_type: VariableTypesEnum | None = None
) -> Variable:
    mock = MagicMock()
    mock.id = id
    mock.display_name = display_name
    mock.unit = unit
    if variable_type:
        mock.type = variable_type

    return mock


def __verify_trigger(
    trigger: OutcomeExplanationTrigger,
    expected_description: str,
    expected_values: list[str | float | int | datetime],
    expected_score: float,
    expected_variable_ids: list[UUID],
    expected_parent_type: ParentType,
    expected_parent_ids: list[UUID],
) -> None:
    expected_values_as_strings: set[str] = {str(value) for value in expected_values}
    for triggering_value_dict in trigger.triggering_values:
        assert triggering_value_dict["value"] in expected_values_as_strings
        assert expected_score == triggering_value_dict["score"]

    assert trigger.description == expected_description
    assert set(trigger.variable_ids) == set(expected_variable_ids)
    assert trigger.parent_type == expected_parent_type
    assert trigger.parent_ids == expected_parent_ids


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_sales(mock_get_variable_for_explanation, score_engine: ScoreEngine):
    expected_description = "A Total Sales value of $10,000 results in a higher likelihood to bind"
    expected_score = POSITIVE_SCORE_VALUE
    expected_value = 10000.0
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    fni_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(
        expected_variable_id, "Total Sales", "USD", None
    )

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_value=expected_value,
                feature_name="sales",
                input_parameter_names=["sales"],
                input_parameter_values={"sales": expected_value},
            )
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_payroll_avg(mock_get_variable_for_explanation, score_engine: ScoreEngine):
    expected_description = "A Payroll value of $42,000 results in a lower likelihood to bind"
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = 42000.0
    expected_parent_type = ParentType.BUSINESS
    expected_submission_id: UUID = uuid4()
    fni_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(
        expected_variable_id, "Payroll", "USD", VariableTypesEnum.FACT
    )

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="payroll_avg",
                feature_value=expected_value,
                input_parameter_names=["payroll"],
                input_parameter_values={"payroll": expected_value},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[fni_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_payroll_value(mock_get_variable_for_explanation, score_engine: ScoreEngine):
    expected_description = "A Payroll value of $42,000 results in a lower likelihood to bind"
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = 42000.0
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    fni_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(expected_variable_id, "Payroll", "USD", None)

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="payroll_value",
                feature_value=expected_value,
                input_parameter_names=["payroll"],
                input_parameter_values={"payroll": expected_value},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_historical_broker_bind_rate(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "Test Tester's historical bind rate of 15.4% results in a lower likelihood to bind"
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = 0.15444
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    fni_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(
        expected_variable_id, "Broker Name", None, None
    )

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="historical_broker_bind_rate",
                feature_value=expected_value,
                input_parameter_names=["broker_name"],
                input_parameter_values={"broker_name": "Test Tester"},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_two_digits_primary_naics(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "11 - this 2 digit NAICS Code results in a lower likelihood to bind"
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = "11"
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(
        expected_variable_id, "Primary NAICS Code", None, None
    )

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="two_digits_primary_naics",
                feature_value=expected_value,
                input_parameter_names=["two_digits_primary_naics"],
                input_parameter_values={"two_digits_primary_naics": "11"},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_proximity_to_effective_date(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "A Proximity to Effective Date of 365 days results in a higher likelihood to bind"
    expected_score = POSITIVE_SCORE_VALUE
    expected_value = 365
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    fni_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(
        expected_variable_id, "Effective Date", None, None
    )

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="proximity_to_effectivedate",
                feature_value=expected_value,
                input_parameter_names=["Effective Date", "Received Date"],
                input_parameter_values={  # type: ignore
                    "Effective Date": date(year=2023, month=12, day=21),
                    "Received Date": date(year=2024, month=12, day=21),
                },
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 2 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_coverage_str_single(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "Coverage - Primary Liability results in a lower likelihood to bind"
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = "PRIMARY liability"
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(expected_variable_id, "Coverages", None)

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="coverage_str",
                feature_value=expected_value,
                input_parameter_names=["coverages"],
                input_parameter_values={"coverages": '[{"coverage_type":"PRIMARY","name":"liability"}]'},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_contractor_submission_type(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "PRACTICE - this Contractor Submission Type results in a higher likelihood to bind"
    expected_score = POSITIVE_SCORE_VALUE
    expected_value = "PRACTICE"
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(
        expected_variable_id, "Contractor Submission Type", None
    )

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="contractor_submission_type",
                feature_value=expected_value,
                input_parameter_names=["contractor_submission_type"],
                input_parameter_values={"contractor_submission_type": "PRACTICE"},
            )
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_excess_limit_single(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "An Excess Limit value of $10,000 results in a lower likelihood to bind"
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = 10000.0
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(expected_variable_id, "Excess Limit", "USD")

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="excess_limit",
                feature_value=expected_value,
                input_parameter_names=["excess_limit"],
                input_parameter_values={"excess_limit": 10000.0},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_coverage_str_multiple(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "Coverages - Primary Liability, Excess Crime result in a lower likelihood to bind"
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = "PRIMARY liability EXCESS crime"
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(expected_variable_id, "Coverages", None)

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="coverage_str",
                feature_value=expected_value,
                input_parameter_names=["coverages"],
                input_parameter_values={
                    "coverages": '[{"coverage_type":"PRIMARY","name":"liability"}, {"coverage_type":"EXCESS":"crime"}]'
                },
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_building_age(mock_get_variable_for_explanation, score_engine: ScoreEngine):
    expected_description = "A Building Age value of 50 results in a lower likelihood to bind"
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = 50
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(expected_variable_id, "Building Age", "YEARS")

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="building_age_avg",
                feature_value=expected_value,
                input_parameter_names=["building_age_avg"],
                input_parameter_values={"building_age_avg": 50},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_is_renewal(mock_get_variable_for_explanation, score_engine: ScoreEngine):
    expected_description = "Renewals result in a higher likelihood to bind"
    expected_score = POSITIVE_SCORE_VALUE
    expected_value = True
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(expected_variable_id, "Is Renewal", None)

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="is_renewal",
                feature_value=expected_value,
                input_parameter_names=["is_renewal"],
                input_parameter_values={"is_renewal": expected_value},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_is_new_business_submissions(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = False
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(expected_variable_id, "Is Renewal", None)

    is_renewal_independent_explanation: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="is_renewal",
                feature_value=expected_value,
                input_parameter_names=["is_renewal"],
                input_parameter_values={"is_renewal": expected_value},
            ),
        ],
    )

    is_renewal_dependent_explanation: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=5,
        details=[
            MlExplanationDetail(
                feature_name="excess_limit",
                feature_value=1000,
                input_parameter_names=["excess_limit"],
                input_parameter_values={"excess_limit": 1000},
            ),
            MlExplanationDetail(
                feature_name="is_renewal",
                feature_value=expected_value,
                input_parameter_names=["is_renewal"],
                input_parameter_values={"is_renewal": expected_value},
            ),
        ],
    )

    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id,
        0,
        fni_id,
        explanation_details_list=[is_renewal_independent_explanation, is_renewal_dependent_explanation],
    )

    assert 0 == len(result.triggers)
    assert 0 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_is_renewal_true(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "Renewals result in a higher likelihood to bind"
    expected_score = POSITIVE_SCORE_VALUE
    expected_value = True
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(expected_variable_id, "Is Renewal", None)

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="is_renewal",
                feature_value=expected_value,
                input_parameter_names=["is_renewal"],
                input_parameter_values={"is_renewal": True},
            )
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, fni_id, 0, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_multi_variable_positive(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = (
        "Renewals combined with an Excess Limit value of $10,000 result in a higher likelihood to bind"
    )
    expected_score = POSITIVE_SCORE_VALUE
    expected_excess_limit_value = 10000.0
    expected_is_renewal_value = True
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id_1: UUID = uuid4()
    expected_variable_id_2: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.side_effect = [
        __get_mock_variable(expected_variable_id_1, "Is Renewal", None),
        __get_mock_variable(expected_variable_id_2, "Excess Limit", "USD"),
    ]

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="is_renewal",
                feature_value=expected_is_renewal_value,
                input_parameter_names=["is_renewal"],
                input_parameter_values={"is_renewal": True},
            ),
            MlExplanationDetail(
                feature_name="excess_limit",
                feature_value=expected_excess_limit_value,
                input_parameter_names=["excess_limit"],
                input_parameter_values={"excess_limit": expected_excess_limit_value},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_is_renewal_value, expected_excess_limit_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id_1, expected_variable_id_2],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 2 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_multi_variable_negative(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "A Payroll value of $42,000,000 combined with an Excess Limit value of $10,000 results in a lower likelihood to bind"

    expected_score = NEGATIVE_SCORE_VALUE
    expected_payroll_value = 42000000.0
    expected_excess_limit_value = 10000.0
    expected_parent_type = ParentType.SUBMISSION  # expecting submission due to conflicting parent types
    expected_submission_id: UUID = uuid4()
    expected_variable_id_1: UUID = uuid4()
    expected_variable_id_2: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.side_effect = [
        __get_mock_variable(expected_variable_id_1, "Payroll", "USD", VariableTypesEnum.FACT),
        __get_mock_variable(expected_variable_id_2, "Excess Limit", "USD"),
    ]

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="payroll_avg",
                feature_value=expected_payroll_value,
                input_parameter_names=["payroll"],
                input_parameter_values={"payroll": expected_payroll_value},
            ),
            MlExplanationDetail(
                feature_name="excess_limit",
                feature_value=expected_excess_limit_value,
                input_parameter_names=["excess_limit"],
                input_parameter_values={"excess_limit": expected_excess_limit_value},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_payroll_value, expected_excess_limit_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id_1, expected_variable_id_2],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 2 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_multi_variable_coverages(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = "Coverages - Primary Liability, Excess Crime combined with an Excess Limit value of $10,000 result in a higher likelihood to bind"
    expected_score = POSITIVE_SCORE_VALUE
    expected_excess_limit_value = 10000.0
    expected_coverages_value = "PRIMARY liability EXCESS crime"
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id_1: UUID = uuid4()
    expected_variable_id_2: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.side_effect = [
        __get_mock_variable(expected_variable_id_1, "Coverages", None),
        __get_mock_variable(expected_variable_id_2, "Excess Limit", "USD"),
    ]

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="coverage_str",
                feature_value=expected_coverages_value,
                input_parameter_names=["coverages"],
                input_parameter_values={
                    "coverages": '[{"coverage_type":"PRIMARY","name":"liability"}, {"coverage_type":"EXCESS":"crime"}]'
                },
            ),
            MlExplanationDetail(
                feature_name="excess_limit",
                feature_value=expected_excess_limit_value,
                input_parameter_names=["excess_limit"],
                input_parameter_values={"excess_limit": expected_excess_limit_value},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_coverages_value, expected_excess_limit_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id_1, expected_variable_id_2],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 2 == mock_get_variable_for_explanation.call_count


@pytest.mark.parametrize(
    "input_value, expected_value",
    [
        (
            '[{"id":"4f626938-016f-42d7-a9e0-c28a7910b4fb","name":"E&S Casualty"}]',
            UUID("4f626938-016f-42d7-a9e0-c28a7910b4fb"),
        ),
        (
            '[{"id":"4f626938-016f-42d7-a9e0-c28a7910b4fb","name":"E&S Casualty"},{"id":"4f626938-016f-42d7-a9e0-c28a7910b4fb","name":"E&S Casualty"}]',
            UUID("4f626938-016f-42d7-a9e0-c28a7910b4fb"),
        ),
        (None, "no_user_group"),
        ("", "no_user_group"),
    ],
)
def test_user_group_parsing(input_value, expected_value):
    assert ScoreEngine.get_user_group_id(input_value, 1) == expected_value


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_naics_boolean(mock_get_variable_for_explanation, score_engine: ScoreEngine):
    expected_description = "Construction submissions combined with Submissions outside of Accommodation and Food Services result in a higher likelihood to bind"
    expected_score = POSITIVE_SCORE_VALUE
    expected_values = [True, False]
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()
    fni_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(
        expected_variable_id, "Primary NAICS Code", None, None
    )

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="is_construction",
                feature_value=True,
                input_parameter_names=["two_digits_primary_naics"],
                input_parameter_values={"two_digits_primary_naics": "23"},
            ),
            MlExplanationDetail(
                feature_name="is_food_accommodation",
                feature_value=False,
                input_parameter_names=["two_digits_primary_naics"],
                input_parameter_values={"two_digits_primary_naics": "72"},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )
    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=expected_values,
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 2 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_historical_broker_bind_rate_with_naics(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = (
        "Test Tester's historical bind rate of 15.4% for Construction results in a lower likelihood to bind"
    )
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = 0.15444
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    fni_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(
        expected_variable_id, "Broker Name", None, None
    )

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="construction_bind_rate",
                feature_value=expected_value,
                input_parameter_names=["broker_name"],
                input_parameter_values={"broker_name": "Test Tester"},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count


@patch("src.recommendations.ml_explanation_utils.get_variable_for_explanation")
def test__get_ml_score_outcome_explanation_historical_broker_bind_rate_with_naics(
    mock_get_variable_for_explanation, score_engine: ScoreEngine
):
    expected_description = (
        "Test Tester's historical bind rate of 15.4% outside of Construction results in a lower likelihood to bind"
    )
    expected_score = NEGATIVE_SCORE_VALUE
    expected_value = 0.15444
    expected_parent_type = ParentType.SUBMISSION
    expected_submission_id: UUID = uuid4()
    fni_id: UUID = uuid4()
    expected_variable_id: UUID = uuid4()

    mock_get_variable_for_explanation.return_value = __get_mock_variable(
        expected_variable_id, "Broker Name", None, None
    )

    explanation_details: MlExplanationDetails = MlExplanationDetails(
        score=expected_score,
        priority=6,
        details=[
            MlExplanationDetail(
                feature_name="non_construction_bind_rate",
                feature_value=expected_value,
                input_parameter_names=["broker_name"],
                input_parameter_values={"broker_name": "Test Tester"},
            ),
        ],
    )
    result: OutcomeExplanation = score_engine._get_ml_score_outcome_explanation(
        expected_submission_id, 0, fni_id, explanation_details_list=[explanation_details]
    )

    assert 1 == len(result.triggers)
    __verify_trigger(
        result.triggers[0],
        expected_description=expected_description,
        expected_values=[expected_value],
        expected_score=expected_score,
        expected_variable_ids=[expected_variable_id],
        expected_parent_type=expected_parent_type,
        expected_parent_ids=[expected_submission_id],
    )
    assert 1 == mock_get_variable_for_explanation.call_count
