"""Add submission iso gl code to variables

Revision ID: 6b855f145a2d
Revises: fb14e63c8d43
Create Date: 2025-05-23 16:08:04.182366+00:00

"""
import json

import sqlalchemy as sa
from alembic import op

from migrations.data.gl_code_to_description import GL_CODE_TO_DESCRIPTION

# revision identifiers, used by Alembic.
revision = "6b855f145a2d"
down_revision = "fb14e63c8d43"
branch_labels = None
depends_on = None

VARIABLE_ID = "1839aa59-6674-420a-bca0-1d3948ac8285"


def upgrade() -> None:
    variable_options = [
        {"label": f"{code.removeprefix('ISO_GL_')} {GL_CODE_TO_DESCRIPTION[code]}", "value": code}
        for code in GL_CODE_TO_DESCRIPTION
    ]
    variable_options = json.dumps(variable_options)
    conn = op.get_bind()
    with op.get_context().autocommit_block():
        conn.execute(
            sa.text(
                """
            INSERT INTO variables (id, created_at, updated_at, type, display_name, description, variable_group_id, is_enabled, value_type, options) 
            VALUES (:variable_id, NOW(), NOW(), 'SUBMISSION', 'Primary GL Code', 'Multi label classification containing the main GL code for a submission', 'e9d205c3-1f4c-4d6b-8d71-0480cd29c546', True, 'ENUM', :variable_options);
            INSERT INTO submission_variables (id, key) VALUES (:variable_id, 'iso_gl_code');
            """
            ),
            {"variable_id": VARIABLE_ID, "variable_options": variable_options},
        )


def downgrade() -> None:
    conn = op.get_bind()

    conn.execute(
        f"""
    delete from variables where id = '{VARIABLE_ID}';
    """
    )
