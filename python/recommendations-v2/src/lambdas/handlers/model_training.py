import datetime
import os
from typing import TYPE_CHECKING

from common.utils.logging import log_function_inputs
from infrastructure_common.logging import bind_lambda_logging_context, get_logger
from static_common.enums.organization import ExistingOrganizations
from static_common.schemas.recommendation_model import RecommendationModelSchema

from src.recommendations.models import RecommendationScoreModel
from src.rules.models import Rule

if TYPE_CHECKING:
    from sqlalchemy.orm import Session

logger = get_logger()

lazy_clients = {}

SLACK_ALERT_CHANNEL_BY_ENV = {
    "prod": "winnability-models-log",
    "dev": "winnability-models-log",
    "stage": "winnability-models-log",
}

ORG_TO_USER_GROUPS = {
    ExistingOrganizations.Paragon: ["4f626938-016f-42d7-a9e0-c28a7910b4fb", "3a1c70f4-5d97-4a42-b98a-01372fcb8ae0"],
}

MODEL_TRAINING_LAMBDA = "insights-scikit-112-train-recommendations-model"


PREPROCESSORS_BY_FEATURE_NAME = {
    "historical_broker_bind_rate": {
        "type": "HISTORICAL_BIND_RATE",
        "model_parameter": "historical_broker_bind_rate",
        "input_parameter": "broker_name",
    },
    "construction_bind_rate": {
        "type": "HISTORICAL_BIND_RATE",
        "model_parameter": "construction_bind_rate",
        "input_parameter": "broker_name",
        "params": {
            "naics_filter": "23",
            "filter_negated": False,
        },
    },
    "non_construction_bind_rate": {
        "type": "HISTORICAL_BIND_RATE",
        "model_parameter": "non_construction_bind_rate",
        "input_parameter": "broker_name",
        "params": {
            "naics_filter": "23",
            "filter_negated": True,
        },
    },
    "real_estate_bind_rate": {
        "type": "HISTORICAL_BIND_RATE",
        "model_parameter": "real_estate_bind_rate",
        "input_parameter": "broker_name",
        "params": {
            "naics_filter": "53",
            "filter_negated": False,
        },
    },
    "non_real_estate_bind_rate": {
        "type": "HISTORICAL_BIND_RATE",
        "model_parameter": "non_real_estate_bind_rate",
        "input_parameter": "broker_name",
        "params": {
            "naics_filter": "53",
            "filter_negated": True,
        },
    },
    "food_accommodation_bind_rate": {
        "type": "HISTORICAL_BIND_RATE",
        "model_parameter": "food_accommodation_bind_rate",
        "input_parameter": "broker_name",
        "params": {
            "naics_filter": "72",
            "filter_negated": False,
        },
    },
    "non_food_accommodation_bind_rate": {
        "type": "HISTORICAL_BIND_RATE",
        "model_parameter": "non_food_accommodation_bind_rate",
        "input_parameter": "broker_name",
        "params": {
            "naics_filter": "72",
            "filter_negated": True,
        },
    },
    "is_construction": {
        "type": "MAPPING",
        "model_parameter": "is_construction",
        "input_parameter": "two_digits_primary_naics",
        "params": {
            "mapping": {"23": True},
            "default_value": False,
        },
    },
    "is_real_estate": {
        "type": "MAPPING",
        "model_parameter": "is_real_estate",
        "input_parameter": "two_digits_primary_naics",
        "params": {
            "mapping": {"53": True},
            "default_value": False,
        },
    },
    "is_food_accommodation": {
        "type": "MAPPING",
        "model_parameter": "is_food_accommodation",
        "input_parameter": "two_digits_primary_naics",
        "params": {
            "mapping": {"72": True},
            "default_value": False,
        },
    },
    "proximity_to_effectivedate": {
        "type": "DATE_DIFF",
        "model_parameter": "proximity_to_effectivedate",
        "params": {"recent_date": "effective_date", "older_date": "created_date"},
    },
    "days_to_quote": {
        "type": "DATE_DIFF",
        "model_parameter": "days_to_quote",
        "params": {"recent_date": "effective_date"},
    },
    "coverage_str": {
        "type": "COVERAGE_TYPE",
        "model_parameter": "coverage_str",
        "input_parameter": "coverages",
    },
}


@bind_lambda_logging_context
@log_function_inputs
def register_winnability_model(event, context=None):
    from src.db import get_session_raw
    from src.lazy_clients import get_slack_client

    try:
        session = get_session_raw()
        recommendation_model = RecommendationModelSchema().load(event)
        slack_client = get_slack_client()
        if recommendation_model.error:
            logger.warning("Model training failed", error=recommendation_model.error)
            slack_client.send_slack_message(
                SLACK_ALERT_CHANNEL_BY_ENV.get(os.getenv("KALEPA_ENV", "dev"), "winnability-models-log"),
                f"Model training failed {ExistingOrganizations(recommendation_model.organization_id).name} "
                f"in {os.getenv('KALEPA_ENV')} environment:"
                f"Error: {recommendation_model.error}",
                only_for_prod=False,
            )
            return event
        preprocessors = [
            PREPROCESSORS_BY_FEATURE_NAME[feature_name]
            for feature_name in recommendation_model.model_parameters
            if feature_name in PREPROCESSORS_BY_FEATURE_NAME
        ]
        model = RecommendationScoreModel(
            organization_id=recommendation_model.organization_id,
            model_name=recommendation_model.model_file_name,
            auc_score=recommendation_model.auc_score,
            support=recommendation_model.support,
            user_group=recommendation_model.user_group,
            input_parameters=recommendation_model.input_parameters,
            model_parameters=recommendation_model.model_parameters,
            starting_cutoff_date=datetime.datetime.now(),
            score_normalization_denominator=1.0,
            is_active=False,
            preprocessors={"preprocessors": preprocessors},
            metrics_data={
                "accuracy": recommendation_model.accuracy,
                "precision": recommendation_model.precision,
                "recall": recommendation_model.recall,
                "f1_score": recommendation_model.f1_score,
                "classification_threshold": recommendation_model.classification_threshold,
            },
        )
        session.add(model)
        session.commit()

        logger.info("Model registered", model_id=model.id)

        model_data = RecommendationModelSchema().dump(recommendation_model)
        slack_client.send_slack_message(
            SLACK_ALERT_CHANNEL_BY_ENV.get(os.getenv("KALEPA_ENV", "dev"), "winnability-models-log"),
            f"New model registered for {ExistingOrganizations(model.organization_id).name} "
            f"in {os.getenv('KALEPA_ENV')} environment:"
            f"Model data: {model_data}",
            only_for_prod=False,
        )
        return model_data
    except Exception as e:
        logger.error("Failed to register model", exc_info=True, e=e)
    finally:
        session.close()
    return {"status": "FAILED"}


def _format_result(organization_list: list[int]) -> list[dict]:
    results = []
    for organization in organization_list:
        try:
            if ExistingOrganizations(organization) in ORG_TO_USER_GROUPS:
                for user_group in ORG_TO_USER_GROUPS[ExistingOrganizations(organization)]:
                    results.append({"organization_id": organization, "user_group": user_group})
            else:
                results.append({"organization_id": organization, "user_group": None})
        except ValueError:
            logger.warning("Organization not found", organization=organization)
    return results


@bind_lambda_logging_context
def get_organizations_and_user_groups(event, context=None):
    if "organizations" in event and isinstance(event["organizations"], list):
        orgs = event["organizations"]
        return {"results": _format_result(orgs)}
    from src.db import get_session_raw

    session: "Session" = get_session_raw()  # type: ignore
    try:
        rows = session.query(Rule.organization_id).filter(Rule.is_active.is_(True)).distinct().all()  # type: ignore
        return {"results": _format_result([row[0] for row in rows])}
    except Exception:
        logger.exception("Error getting all organizations")
        return {"results": []}
    finally:
        session.close()
