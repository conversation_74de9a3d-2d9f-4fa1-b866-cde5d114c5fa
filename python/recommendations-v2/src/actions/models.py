from typing import Dict, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, TEX<PERSON>, <PERSON>umn, DateT<PERSON>, En<PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import ARRAY, BO<PERSON>EA<PERSON>, JSON<PERSON>, UUID
from sqlalchemy.orm import relationship
from static_common.enums.enum import StrEnum
from static_common.enums.recommendation import RecommendationActionEnum

from src.models import ActionTypeEnum, BaseModel


class SubmissionActionOperationEnum(StrEnum):
    ENABLE_PDS = "ENABLE_PDS"
    TRIAGE_ACCEPT = "TRIAGE_ACCEPT"
    TRIAGE_DECLINE = "TRIAGE_DECLINE"
    TRIAGE_DECLINE_NO_EMAIL = "TRIAGE_DECLINE_NO_EMAIL"
    TRIAGE_DECLINE_CLEARING_EMAIL = "TRIAGE_DECLINE_CLEARING_EMAIL"
    TRIAGE_DECLINE_NOT_ELIGIBLE_PRODUCER = "TRIAGE_DECLINE_NOT_ELIGIBLE_PRODUCER"
    AUTO_CLEARING = "AUTO_CLEARING"


class ActionClassEnum(StrEnum):
    RECOMMENDATION = "REC<PERSON><PERSON><PERSON>ATION"
    TEXT = "TEXT"
    SUBMISSION_UPDATE = "SUBMISSION_UPDATE"
    ASSIGN_UNDERWRITER = "ASSIGN_UNDERWRITER"
    REFER_SUBMISSION = "REFER_SUBMISSION"
    FLAG_MISSING_DOCUMENTS = "FLAG_MISSING_DOCUMENTS"
    ATTACH_FILES = "ATTACH_FILES"
    SEND_EMAIL = "SEND_EMAIL"
    SET_STATUS = "SET_STATUS"
    NONE = "NONE"


class ThresholdSelectorEnum(StrEnum):
    ABOVE = "ABOVE"
    IN = "IN"
    BELOW = "BELOW"


class Action(BaseModel):
    __tablename__ = "actions"

    source = relationship("Source", uselist=False, viewonly=True)
    source_id = Column(UUID(as_uuid=True), ForeignKey("sources.id", ondelete="CASCADE"), nullable=True, index=True)
    condition_group = relationship("ConditionGroup", uselist=False, viewonly=True)
    condition_group_id = Column(
        UUID(as_uuid=True), ForeignKey("condition_groups.id", ondelete="CASCADE"), nullable=True, index=True
    )
    action_class = Column(Enum(ActionClassEnum), nullable=False)
    action_type = Column(Enum(ActionTypeEnum), nullable=False)
    rule_id = Column(UUID(as_uuid=True), ForeignKey("rules.id", ondelete="CASCADE"), nullable=True, index=True)
    is_negative = Column(BOOLEAN, nullable=True)
    # { "threshold_selector": ThresholdSelectorEnum }
    condition = Column(JSONB, nullable=True)

    __mapper_args__ = {
        "polymorphic_identity": "variables",
        "polymorphic_on": action_class,
    }

    def copy(self, values: Optional[Dict] = None):
        values = values or {}
        values["source_id"] = self.source_id
        values["action_class"] = self.action_class
        values["action_type"] = self.action_type
        values["condition"] = self.condition
        if "rule_id" not in values:
            values["rule_id"] = self.rule_id
        if "condition_group_id" not in values:
            values["condition_group_id"] = self.condition_group_id
        return super().copy(values=values)


class RecommendationAction(Action):
    __tablename__ = "recommendation_actions"

    id = Column(UUID(as_uuid=True), ForeignKey("actions.id", ondelete="CASCADE"), primary_key=True)
    value = Column(Enum(RecommendationActionEnum), nullable=False)
    relative_ranking = Column(DECIMAL, nullable=False, default=5.0)

    __mapper_args__ = {"polymorphic_identity": ActionClassEnum.RECOMMENDATION}


class TextAction(Action):
    __tablename__ = "text_actions"

    id = Column(UUID(as_uuid=True), ForeignKey("actions.id", ondelete="CASCADE"), primary_key=True)
    value = Column(ARRAY(TEXT), nullable=False)
    is_editable = Column(BOOLEAN, nullable=True)

    __mapper_args__ = {"polymorphic_identity": ActionClassEnum.TEXT}


class SubmissionAction(Action):
    __tablename__ = "submission_actions"

    id = Column(UUID(as_uuid=True), ForeignKey("actions.id", ondelete="CASCADE"), primary_key=True)
    type = Column(TEXT, nullable=False)
    config = Column(JSONB, nullable=True)

    __mapper_args__ = {"polymorphic_identity": ActionClassEnum.SUBMISSION_UPDATE}


class AssignUnderwriterAction(Action):
    __tablename__ = "assign_underwriter_actions"

    id = Column(UUID(as_uuid=True), ForeignKey("actions.id", ondelete="CASCADE"), primary_key=True)
    underwriter_id = Column(Integer, nullable=False)

    __mapper_args__ = {"polymorphic_identity": ActionClassEnum.ASSIGN_UNDERWRITER}


class FlagMissingDocumentsAction(Action):
    __tablename__ = "flag_missing_documents_actions"

    id = Column(UUID(as_uuid=True), ForeignKey("actions.id", ondelete="CASCADE"), primary_key=True)

    __mapper_args__ = {"polymorphic_identity": ActionClassEnum.FLAG_MISSING_DOCUMENTS}


class ReferAction(Action):
    __tablename__ = "refer_actions"

    id = Column(UUID(as_uuid=True), ForeignKey("actions.id", ondelete="CASCADE"), primary_key=True)

    __mapper_args__ = {"polymorphic_identity": ActionClassEnum.REFER_SUBMISSION}


class AttachFilesAction(Action):
    __tablename__ = "attach_files_actions"

    id = Column(UUID(as_uuid=True), ForeignKey("actions.id", ondelete="CASCADE"), primary_key=True)
    files: List["AttachedFile"] = relationship("AttachedFile", uselist=True, cascade="all,delete")

    __mapper_args__ = {"polymorphic_identity": ActionClassEnum.ATTACH_FILES}


class AttachedFile(BaseModel):
    __tablename__ = "attached_files"

    action_id = Column(
        UUID(as_uuid=True), ForeignKey("attach_files_actions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    s3_key = Column(TEXT, nullable=False)
    uploaded_at = Column(DateTime(timezone=True), nullable=False)


class SendEmailAction(Action):
    __tablename__ = "send_email_actions"

    id = Column(UUID(as_uuid=True), ForeignKey("actions.id", ondelete="CASCADE"), primary_key=True)
    email_template_id = Column(UUID(as_uuid=True), nullable=False)
    # Toggle either shell or full submissions
    shell_submissions = Column(BOOLEAN, nullable=False)
    decline_submission = Column(BOOLEAN, nullable=True)
    only_submissions_after = Column(DateTime(timezone=True), nullable=True)

    __mapper_args__ = {"polymorphic_identity": ActionClassEnum.SEND_EMAIL}


class SetStatusAction(Action):
    __tablename__ = "set_status_actions"

    id = Column(UUID(as_uuid=True), ForeignKey("actions.id", ondelete="CASCADE"), primary_key=True)
    status_id = Column(Integer, nullable=False)

    __mapper_args__ = {"polymorphic_identity": ActionClassEnum.SET_STATUS}


class NoneAction(Action):
    # This does not map to any table.
    # It's part of the polymorphic hierarchy but does not have a table.

    __mapper_args__ = {
        "polymorphic_identity": ActionClassEnum.NONE,
        "polymorphic_on": None,  # No actual table for NoneAction
    }
