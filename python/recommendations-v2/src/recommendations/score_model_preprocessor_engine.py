import json
from collections import defaultdict
from datetime import datetime
from typing import Any
from uuid import UUID

from redshift_api_client.models.historical_bind_rates_request import (
    HistoricalBindRatesRequest,
)

from src.clients.redshift_api import RedshiftApiClient
from src.recommendations.score_model_preprocessor_config import (
    PreprocessorType,
    ScoreModelPreprocessorsSchema,
)
from src.recommendations.utils import load_json_string


class ScoreModelPreprocessorEngine:
    def __init__(self, preprocessor_dict: dict):
        preprocessors_wrapper = ScoreModelPreprocessorsSchema().load(preprocessor_dict)
        self.preprocessors = defaultdict(list)
        for preprocessor in preprocessors_wrapper.preprocessors:
            self.preprocessors[preprocessor.type].append(preprocessor)
        self.historical_bind_rate_parameters = {}

    def init_for_parameters(
        self, parameters_list: list[dict], organization_id: int, redshift_api_client: RedshiftApiClient
    ):
        for preprocessor in self.preprocessors[PreprocessorType.HISTORICAL_BIND_RATE]:
            input_parameter = preprocessor.input_parameter
            parameter_values = {
                parameters[input_parameter].lower().replace("'", "''")
                for parameters in parameters_list
                if input_parameter in parameters and parameters[input_parameter] is not None
            }
            if not parameter_values:
                continue

            bind_rates = redshift_api_client.get_historical_bind_rates(
                organization_id,
                HistoricalBindRatesRequest(
                    parameter_name=input_parameter,
                    parameter_values=list(parameter_values),
                    naics_filter=preprocessor.params.get("naics_filter"),
                    filter_negated=preprocessor.params.get("filter_negated", False),
                ),
            )
            self.historical_bind_rate_parameters[input_parameter] = bind_rates.parameter_values

    @staticmethod
    def parse_coverages(coverages_str: str | None) -> str | None:
        data = load_json_string(coverages_str)
        if not data:
            return None
        coverage_values = [f'{cvg["coverage_type"]} {cvg["name"]}' for cvg in data]
        return " ".join(sorted(coverage_values))

    @staticmethod
    def calculate_time_series_avg(time_series_obj: str | None) -> float | None:
        if not time_series_obj:
            return None
        data = json.loads(time_series_obj)
        if isinstance(data, str):
            data = json.loads(data)
        return sum([float(d) for d in data.values()]) / len(data) if data else None

    @staticmethod
    def _normalize_value(value: Any) -> Any:
        if isinstance(value, str):
            return value.title()
        return value

    @staticmethod
    def value_filter(actual_value: Any, filtering_value: Any, target_value: Any) -> Any | None:
        if actual_value == filtering_value:
            return target_value
        return None

    def process(self, submissions_parameters: dict[UUID, dict]) -> dict:
        mapping_model_to_input_parameters = {}
        for preprocessor_type, preprocessor_list in self.preprocessors.items():
            for preprocessor in preprocessor_list:
                if preprocessor_type == PreprocessorType.DATE_DIFF:
                    mapping_model_to_input_parameters[preprocessor.model_parameter] = [
                        preprocessor.params["recent_date"],
                        preprocessor.params["older_date"],
                    ]
                else:
                    mapping_model_to_input_parameters[preprocessor.model_parameter] = [preprocessor.input_parameter]
        for submission_id, parameters in submissions_parameters.items():
            for preprocessor in self.preprocessors[PreprocessorType.ARRAY_TO_VALUE]:
                if not parameters[preprocessor.input_parameter]:
                    parameters[preprocessor.model_parameter] = None
                    continue
                value_to_return = preprocessor.params["value_to_return"]
                array_items = json.loads(parameters[preprocessor.input_parameter])
                if preprocessor.params.get("take_first_value", False):
                    parameters[preprocessor.model_parameter] = self._normalize_value(array_items[0][value_to_return])
                else:
                    parameters[preprocessor.model_parameter] = [
                        self._normalize_value(array_item[value_to_return]) for array_item in array_items
                    ]
            for preprocessor in self.preprocessors[PreprocessorType.HISTORICAL_BIND_RATE]:
                parameters[preprocessor.model_parameter] = (
                    self.historical_bind_rate_parameters[preprocessor.input_parameter].get(
                        parameters[preprocessor.input_parameter].lower()
                    )
                    if parameters[preprocessor.input_parameter]
                    else None
                )
            for preprocessor in self.preprocessors[PreprocessorType.MAPPING]:
                parameters[preprocessor.model_parameter] = preprocessor.params["mapping"].get(
                    parameters[preprocessor.input_parameter], preprocessor.params["default_value"]
                )
            for preprocessor in self.preprocessors[PreprocessorType.RENAME]:
                parameters[preprocessor.model_parameter] = parameters[preprocessor.input_parameter]
            for preprocessor in self.preprocessors[PreprocessorType.DATE_DIFF]:
                recent_date_param = parameters[preprocessor.params["recent_date"]]
                if "older_date" in preprocessor.params:
                    older_date_param = parameters[preprocessor.params["older_date"]]
                else:
                    older_date_param = datetime.now()
                recent_date = (
                    (
                        recent_date_param
                        if isinstance(recent_date_param, datetime)
                        else datetime.fromisoformat(recent_date_param)
                    )
                    if recent_date_param
                    else None
                )
                older_date = (
                    (
                        older_date_param
                        if isinstance(older_date_param, datetime)
                        else datetime.fromisoformat(older_date_param)
                    )
                    if older_date_param
                    else None
                )
                parameters[preprocessor.model_parameter] = (
                    (recent_date - older_date).days if recent_date and older_date else None
                )
            for preprocessor in self.preprocessors[PreprocessorType.COVERAGE_TYPE]:
                parameters[preprocessor.model_parameter] = self.parse_coverages(
                    parameters[preprocessor.input_parameter]
                )
            for preprocessor in self.preprocessors[PreprocessorType.TIME_SERIES_AVERAGE]:
                parameters[preprocessor.model_parameter] = self.calculate_time_series_avg(
                    parameters[preprocessor.input_parameter]
                )
            for preprocessor in self.preprocessors[PreprocessorType.VALUE_FILTER]:
                parameters[preprocessor.model_parameter] = self.value_filter(
                    parameters[preprocessor.params["filtering_param"]],
                    preprocessor.params["filtering_value"],
                    parameters[preprocessor.input_parameter],
                )
        return mapping_model_to_input_parameters
