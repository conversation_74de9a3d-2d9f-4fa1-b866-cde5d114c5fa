from dataclasses import dataclass
from datetime import datetime
from functools import partial
from typing import Callable, TypeAlias
from uuid import UUID, uuid4

from redshift_api_client.models.profitability_score_entry import ProfitabilityScoreEntry
from sqlalchemy import func, nullslast, or_
from sqlalchemy.orm import Session
from static_common.enums.parent import ParentType
from structlog.stdlib import Bound<PERSON>ogger

from src.recommendations.models import OutcomeExplanation, OutcomeExplanationTrigger
from src.utils import rreplace
from src.variables.models import Variable, VariableTypesEnum

BIND_RATES_FEATURES = [
    "historical_broker_bind_rate",
    "construction_bind_rate",
    "non_construction_bind_rate",
    "real_estate_bind_rate",
    "non_real_estate_bind_rate",
    "food_accommodation_bind_rate",
    "non_food_accommodation_bind_rate",
]


@dataclass
class MlExplanationDetail:
    feature_name: str
    feature_value: str | float | int | datetime
    input_parameter_names: list[str]
    input_parameter_values: dict[str, str | float | int | datetime]

    @property
    def broker_name(self) -> str | None:
        if self.input_parameter_values is None:
            return None

        return self.input_parameter_values.get("broker_name")


@dataclass
class MlExplanationDetails:
    details: list[MlExplanationDetail]
    score: float
    priority: float


@dataclass
class MlScoreExplanationDescriptionRequirement:
    feature_name: str
    feature_value: str | float | int | datetime
    score: float
    variable_display_name: str
    variable_units: str | None


@dataclass
class PartialFormattingResult:
    feature_value_description: str
    is_plural: bool


FormattableValue: TypeAlias = str | float | int | datetime | bool
PartialExplanationFormatterType: TypeAlias = Callable[[FormattableValue, str, str | None], PartialFormattingResult]
VariableRetrieverType: TypeAlias = Callable[[str, int, Session], Variable | None]


def __filter_explanation_details(
    explanation_details_for_submission: list[MlExplanationDetails],
) -> list[MlExplanationDetails]:
    # If we have a feature that is present in multiple explanations, we only want the
    # explanation with the highest priority
    explanation_details_for_submission.sort(key=lambda detail: detail.priority, reverse=True)

    seen_feature_names: set[str] = set()
    filtered_explanation_details: list[MlExplanationDetails] = []
    for explanation_details in explanation_details_for_submission:
        feature_names_for_explanation: set[str] = {detail.feature_name for detail in explanation_details.details}
        if feature_names_for_explanation.isdisjoint(seen_feature_names):
            seen_feature_names.update(feature_names_for_explanation)
            filtered_explanation_details.append(explanation_details)

    return filtered_explanation_details


def extract_explanation_details(
    submission_id: str,
    explanations_for_submission: list[dict],
    mapping_model_to_input_parameters: dict,
    submissions_score_model_parameters: dict,
) -> list[MlExplanationDetails]:
    explanation_details_for_submission: list[MlExplanationDetails] = []
    for explanation in explanations_for_submission:
        details: list[MlExplanationDetail] = []
        for feature_name in explanation["feature_names"]:
            input_parameter_names: list[str] = mapping_model_to_input_parameters.get(feature_name, [feature_name])
            input_parameter_values = {
                input_parameter_name: submissions_score_model_parameters[submission_id][input_parameter_name]
                for input_parameter_name in input_parameter_names
            }
            feature_value = submissions_score_model_parameters[submission_id][feature_name]
            details.append(
                MlExplanationDetail(
                    feature_name=feature_name,
                    feature_value=feature_value,
                    input_parameter_names=input_parameter_names,
                    input_parameter_values=input_parameter_values,
                )
            )
        explanation_details_for_submission.append(
            MlExplanationDetails(details=details, score=explanation["score"], priority=explanation["priority"])
        )

    return __filter_explanation_details(explanation_details_for_submission)


def __format_usd(value: float | int) -> str:
    return f"${value:,.0f}"


def __format_numeric(value: float | int) -> str:
    return f"{value:,.2f}"


def __format_integer(value: float | int) -> str:
    return f"{int(value)}"


def __default_categorical_explanation_formatter(
    value: FormattableValue,
    variable_display_name: str,
    _variable_units: str | None,
) -> PartialFormattingResult:
    description: str = f"{value} - this {variable_display_name}"
    return PartialFormattingResult(feature_value_description=description, is_plural=False)


def __sic_categorical_explanation_formatter(
    value: FormattableValue,
    variable_display_name: str,
    _variable_units: str | None,
) -> PartialFormattingResult:
    description: str = f"{value.replace('_', ' ')} - this {variable_display_name}"
    return PartialFormattingResult(feature_value_description=description, is_plural=False)


def __renewal_explanation_formatter(
    value: str | float | int | datetime | bool,
    _variable_display_name: str,
    _variable_units: str | None,
) -> PartialFormattingResult:
    assert isinstance(value, bool)

    renewal_str: str = "Renewals" if value else "New business submissions"
    return PartialFormattingResult(feature_value_description=renewal_str, is_plural=True)


def __is_two_digit_naics_formatter(
    value: bool,
    _variable_display_name: str,
    _variable_units: str | None,
    segment_name: str,
) -> PartialFormattingResult:
    assert isinstance(value, bool)

    description = f"{segment_name} submissions" if value else f"Submissions outside of {segment_name}"
    return PartialFormattingResult(feature_value_description=description, is_plural=True)


def __default_numeric_explanation_formatter(
    value: FormattableValue,
    variable_display_name: str,
    variable_units: str | None,
) -> PartialFormattingResult:
    assert isinstance(value, (float, int))

    formatted_numeric_value: str = ""
    if variable_units == "USD":
        formatted_numeric_value = __format_usd(value)  # type: ignore
    elif variable_units == "YEARS":
        formatted_numeric_value = __format_integer(value)  # type: ignore
    else:
        formatted_numeric_value = __format_numeric(value)  # type: ignore

    a_or_an: str = "an" if variable_display_name[0].lower() in "aeiou" else "a"
    description: str = f"{a_or_an} {variable_display_name} value of {formatted_numeric_value}"
    return PartialFormattingResult(feature_value_description=description, is_plural=False)


def __coverages_str_formatter(
    value: FormattableValue,
    _variable_display_name: str,
    _variable_units: str | None,
) -> PartialFormattingResult:
    assert isinstance(value, str)

    # Note: ML input looks like "PRIMARY liability EXCESS crime"
    coverage_data_split: list[str] = value.split(" ")  # type: ignore
    coverage_types: list[str] = coverage_data_split[::2]
    coverage_names: list[str] = coverage_data_split[1::2]

    coverages_str = ", ".join(
        [
            f"{coverage_type.title()} {coverage_name.title()}"
            for coverage_type, coverage_name in zip(coverage_types, coverage_names)
        ]
    )

    has_multiple_coverages: bool = len(coverage_names) > 1
    coverage_or_coverages: str = "Coverages" if has_multiple_coverages else "Coverage"
    description: str = f"{coverage_or_coverages} - {coverages_str}"
    return PartialFormattingResult(feature_value_description=description, is_plural=has_multiple_coverages)


def __proximity_to_effectivedate_formatter(
    value: FormattableValue,
    _variable_display_name: str,
    _variable_units: str | None,
) -> PartialFormattingResult:
    assert isinstance(value, (float, int))

    day_or_days: str = "day" if value == 1 else "days"
    description: str = f"a Proximity to Effective Date of {value} {day_or_days}"
    return PartialFormattingResult(feature_value_description=description, is_plural=False)


def __historical_broker_bind_rate_formatter(
    value: FormattableValue,
    broker_name: str,
    _variable_units: str | None,
    segment_addition: str | None = None,
) -> PartialFormattingResult:
    assert isinstance(value, (float, int))

    description: str = f"{broker_name}'s historical bind rate of {value*100:.1f}%"
    if segment_addition:
        description += f" {segment_addition}"

    return PartialFormattingResult(feature_value_description=description, is_plural=False)


FEATURE_NAME_TO_PARTIAL_EXPLANATION_FORMATTER: dict[str, PartialExplanationFormatterType] = {
    "proximity_to_effectivedate": __proximity_to_effectivedate_formatter,
    "historical_broker_bind_rate": __historical_broker_bind_rate_formatter,
    "historical_agency_bind_rate": __historical_broker_bind_rate_formatter,
    "construction_bind_rate": partial(__historical_broker_bind_rate_formatter, segment_addition="for Construction"),
    "non_construction_bind_rate": partial(
        __historical_broker_bind_rate_formatter, segment_addition="outside of Construction"
    ),
    "real_estate_bind_rate": partial(
        __historical_broker_bind_rate_formatter, segment_addition="for Real Estate, Rental and Leasing"
    ),
    "non_real_estate_bind_rate": partial(
        __historical_broker_bind_rate_formatter, segment_addition="outside of Real Estate, Rental and Leasing"
    ),
    "food_accommodation_bind_rate": partial(
        __historical_broker_bind_rate_formatter, segment_addition="for Accommodation and Food Services"
    ),
    "non_food_accommodation_bind_rate": partial(
        __historical_broker_bind_rate_formatter, segment_addition="outside of Accommodation and Food Services"
    ),
    "sales": __default_numeric_explanation_formatter,
    "payroll_avg": __default_numeric_explanation_formatter,
    "payroll_value": __default_numeric_explanation_formatter,
    "coverage_str": __coverages_str_formatter,
    "two_digits_primary_naics": __default_categorical_explanation_formatter,
    "excess_limit": __default_numeric_explanation_formatter,
    "excess_attachment_point": __default_numeric_explanation_formatter,
    "contractor_submission_type": __default_categorical_explanation_formatter,
    "building_age_avg": __default_numeric_explanation_formatter,
    "is_renewal": __renewal_explanation_formatter,
    "fni_state": __default_categorical_explanation_formatter,
    "primary_state": __default_categorical_explanation_formatter,
    "fni_county": __default_categorical_explanation_formatter,
    "project_insurance_type": __default_categorical_explanation_formatter,
    "sic_code": __sic_categorical_explanation_formatter,
    "is_construction": partial(__is_two_digit_naics_formatter, segment_name="Construction"),
    "is_real_estate": partial(__is_two_digit_naics_formatter, segment_name="Real Estate"),
    "is_food_accommodation": partial(__is_two_digit_naics_formatter, segment_name="Accommodation and Food Services"),
}

FEATURE_NAME_TO_VARIABLE_NAME_OVERRIDE: dict[str, str] = {
    "two_digits_primary_naics": "2 digit NAICS Code"  # originally Primary NAICS Code
}


def __get_variables_for_explanation_detail(
    explanation_detail: MlExplanationDetail, organization_id: int, session: Session, logger: BoundLogger
) -> list[Variable]:
    variables: list[Variable] = []
    for input_parameter_name in explanation_detail.input_parameter_names:
        variable: Variable | None = get_variable_for_explanation(input_parameter_name, organization_id, session)
        if not variable:
            logger.error(
                "Unable to identify variable for input parameter",
                input_parameter_name=input_parameter_name,
            )
            raise Exception("Unable to identify variable for input parameter")

        variables.append(variable)
    return variables


def __is_new_submission_business_explanation(detail: MlExplanationDetail) -> bool:
    return detail.feature_name == "is_renewal" and detail.feature_value == False


def _get_quintile_explanation(rating: str, logger: BoundLogger) -> str:
    if rating == "Very Low":
        return "bottom 20%"
    if rating == "Low":
        return "bottom 40%"
    if rating == "Average":
        return "bottom 60%"
    if rating == "High":
        return "top 40%"
    if rating == "Very High":
        return "top 20%"
    logger.error("Received unexpected rating", rating=rating)
    return ""


def create_oe_for_profitability(
    submission_id: UUID,
    profitability_score: ProfitabilityScoreEntry,
    organization_id: int,
    session: Session,
    logger: BoundLogger,
) -> list[OutcomeExplanation]:
    outcome_explanations = []
    if (
        profitability_score.profit_per_policy_2025_rating is None
        and profitability_score.remaining_premium_rating is None
    ):
        return []

    sic_code = profitability_score.sic_code.split("_")[1]
    variable: Variable | None = get_variable_for_explanation("sic_code", organization_id, session)
    sic_code_description = next(
        (option["label"] for option in variable.options if option["value"] == profitability_score.sic_code),
        None,
    )
    if not sic_code_description:
        logger.warning("No code description found for sic code", sic_code=profitability_score.sic_code)
    else:
        sic_code_description = " ".join(sic_code_description.split(" ")[1:])
    if profitability_score.profit_per_policy_2025 is not None:
        quintile_piece = _get_quintile_explanation(profitability_score.profit_per_policy_2025_rating, logger)
        forecast_explanation = OutcomeExplanationTrigger(
            parent_ids=[submission_id],
            parent_type=ParentType.SUBMISSION,
            variable_ids=[variable.id] if variable else [],
            description=f"The forecasted profit net of losses for this submission based on "
            f"its coverages and SIC code is in the {quintile_piece}.",
            triggering_values=[
                {
                    "value": profitability_score.sic_code,
                    "quintile": quintile_piece,
                    "score": profitability_score.profit_per_policy_2025,
                    "rating": profitability_score.profit_per_policy_2025_rating,
                }
            ],
        )
        outcome_explanations.append(
            OutcomeExplanation(
                id=uuid4(), description="Profitability Forecast Explanation", triggers=[forecast_explanation]
            )
        )
    if profitability_score.remaining_premium_rating is not None:
        quintile_piece = _get_quintile_explanation(profitability_score.remaining_premium_rating, logger)
        historical_explanation = OutcomeExplanationTrigger(
            parent_ids=[submission_id],
            parent_type=ParentType.SUBMISSION,
            variable_ids=[variable.id] if variable else [],
            description=f"Based on historical data SIC code "
            f"{sic_code}{(' - ' + sic_code_description) if sic_code_description else ''} is "
            f"one of the {quintile_piece} most profitable classes for the coverages requested.",
            triggering_values=[
                {
                    "value": profitability_score.sic_code,
                    "quintile": quintile_piece,
                    "score": profitability_score.remaining_premium_rating_numerical,
                    "rating": profitability_score.remaining_premium_rating,
                }
            ],
        )
        outcome_explanations.append(
            OutcomeExplanation(
                id=uuid4(), description="Profitability Historical Explanation", triggers=[historical_explanation]
            )
        )
    return outcome_explanations


def create_oet_for_ml_explanation_details(
    submission_id: UUID,
    ml_explanation_details: MlExplanationDetails,
    organization_id: int,
    fni_id: UUID | None,
    session: Session,
    logger: BoundLogger,
) -> OutcomeExplanationTrigger | None:
    if len(ml_explanation_details.details) == 0:
        logger.error("Unable to create OutcomeExplanationTrigger for ML Score Explanation")
        return None

    if any(explanation_detail.feature_value is None for explanation_detail in ml_explanation_details.details):
        logger.debug(
            "Unable to create OutcomeExplanationTrigger for ML Score Explanation without a feature value",
            ml_explanation_details=ml_explanation_details,
        )
        return None

    # Explicit request from business to exclude this explanation to avoid UW confusion
    if any(__is_new_submission_business_explanation(detail) for detail in ml_explanation_details.details):
        return None

    description_requirements: list[MlScoreExplanationDescriptionRequirement] = []
    triggering_values: list[dict] = []
    variable_ids: list[UUID] = []
    parent_ids: list[UUID] = []
    parent_types: list[str] = []
    for explanation_detail in ml_explanation_details.details:
        variables: list[Variable] = __get_variables_for_explanation_detail(
            explanation_detail, organization_id, session, logger
        )
        # Note: There is currently no use case for utilizing multiple variables for displaying
        # the explanation or for navigation on the FE, so we just use the first id
        variable_id: UUID = variables[0].id
        variable_unit: str | None = variables[0].unit
        variable_type: VariableTypesEnum = variables[0].type
        variable_display_name: str = variables[0].display_name
        if explanation_detail.feature_name in BIND_RATES_FEATURES:
            broker_name: str | None = explanation_detail.broker_name
            if broker_name is None:
                raise Exception("Unable to extract broker name from model score explanation")
            variable_display_name = broker_name

        description_requirement: MlScoreExplanationDescriptionRequirement = MlScoreExplanationDescriptionRequirement(
            feature_name=explanation_detail.feature_name,
            feature_value=explanation_detail.feature_value,
            score=ml_explanation_details.score,
            variable_display_name=variable_display_name,
            variable_units=variable_unit,
        )

        description_requirements.append(description_requirement)
        variable_ids.append(variable_id)

        parent_ids.append(fni_id if variable_type == VariableTypesEnum.FACT and fni_id else submission_id)
        parent_types.append(
            ParentType.BUSINESS if variable_type == VariableTypesEnum.FACT and fni_id else ParentType.SUBMISSION
        )

        triggering_values.append(
            {"value": str(explanation_detail.feature_value), "score": ml_explanation_details.score}
        )

    trigger_description: str = __get_ml_score_explanation_description(description_requirements)

    if len(set(parent_types)) > 1:
        # TODO: scope and handle this scenario
        logger.warning(
            "Unable to create OutcomeExplanationTrigger for ML Score Explanation with multiple parent types",
            parent_types=parent_types,
            variable_ids=variable_ids,
        )
        parent_ids = [submission_id]
        parent_types = [ParentType.SUBMISSION]

    return OutcomeExplanationTrigger(  # type: ignore
        parent_ids=[parent_ids[0]],
        parent_type=parent_types[0],
        variable_ids=variable_ids,
        description=trigger_description,
        triggering_values=triggering_values,
    )


def __capitalize_first_letter_of_string(value: str) -> str:
    return value[0].upper() + value[1:]


def __get_ml_score_explanation_description(requirements: list[MlScoreExplanationDescriptionRequirement]) -> str:
    partial_formatting_results: list[PartialFormattingResult] = []
    for requirement in requirements:
        explanation_formatter: PartialExplanationFormatterType | None = (
            FEATURE_NAME_TO_PARTIAL_EXPLANATION_FORMATTER.get(requirement.feature_name, None)
        )
        if explanation_formatter is None:
            raise NotImplementedError(
                f"ML score explanations are not currently supported for feature: {requirement.feature_name}"
            )

        variable_display_name = FEATURE_NAME_TO_VARIABLE_NAME_OVERRIDE.get(
            requirement.feature_name, requirement.variable_display_name
        )
        partial_result: PartialFormattingResult = explanation_formatter(
            requirement.feature_value, variable_display_name, requirement.variable_units
        )
        partial_formatting_results.append(partial_result)

    ml_score: float = requirements[0].score
    is_positive: bool = ml_score > 0
    direction: str = "higher" if is_positive else "lower"

    if len(partial_formatting_results) == 1:
        result: PartialFormattingResult = partial_formatting_results[0]
        formatted_partial_description: str = __capitalize_first_letter_of_string(result.feature_value_description)
        result_or_results: str = "result" if result.is_plural else "results"
        return f"{formatted_partial_description} {result_or_results} in a {direction} likelihood to bind"

    if len(partial_formatting_results) == 2:
        first_result: PartialFormattingResult = partial_formatting_results[0]
        second_result: PartialFormattingResult = partial_formatting_results[1]

        formatted_first_description: str = __capitalize_first_letter_of_string(first_result.feature_value_description)
        result_or_results: str = "result" if first_result.is_plural or second_result.is_plural else "results"
        return f"{formatted_first_description} combined with {second_result.feature_value_description} {result_or_results} in a {direction} likelihood to bind"  # noqa: E501

    raise NotImplementedError(
        "ML score explanations are not currently supported for groups of more than 2 dependent variables"
    )


def __retrieve_variable_by_portfolio_column_name(
    input_parameter_name: str, organization_id: int, db_session: Session
) -> Variable | None:
    variable_names_mapping = {"two_digits_primary_naics": "primary_naics", "created_date": "received_date"}
    column_name = variable_names_mapping.get(input_parameter_name, input_parameter_name)
    portfolio_column = (
        f'location.{rreplace(column_name, "_avg", "", 1)}'
        if column_name.endswith("_avg")
        else f'location.{rreplace(column_name, "_sum", "", 1)}'
        if column_name.endswith("_sum")
        else f"submission.{column_name}"
    )

    return (
        db_session.query(Variable)
        .filter(Variable.portfolio_column == portfolio_column)
        .filter(or_(Variable.organization_id == organization_id, Variable.organization_id.is_(None)))
        .order_by(nullslast(Variable.organization_id))
        .first()
    )


def __retrieve_variable_by_display_name(
    input_parameter_name: str, organization_id: int, db_session: Session
) -> Variable | None:
    input_parameter_name_lower: str = input_parameter_name.lower()
    return (
        db_session.query(Variable)
        .filter(func.lower(Variable.display_name) == input_parameter_name_lower)
        .filter(or_(Variable.organization_id == organization_id, Variable.organization_id.is_(None)))
        .order_by(nullslast(Variable.organization_id))
        .first()
    )


def __retrieve_total_sales_variable(
    _input_parameter_name: str, organization_id: int, db_session: Session
) -> Variable | None:
    # Note: this is currently a hacky solution for working around a data discrepency
    # recs does not expose a submission-level sales variable, which is what the ml model uses from redshift
    # so, we return the location-level total sales variable instead because it's the closest Variable we have
    # this will also allow FE to navigate to the correct data in Risk Analyzer
    return __retrieve_variable_by_display_name("total sales", organization_id, db_session)


DEFAULT_VARIABLE_RETRIEVER: VariableRetrieverType = __retrieve_variable_by_portfolio_column_name
INPUT_PARAMETER_NAME_TO_VARIABLE_RETRIEVER: dict[str, VariableRetrieverType] = {
    "payroll": __retrieve_variable_by_display_name,
    "sales": __retrieve_total_sales_variable,
    "coverages": __retrieve_variable_by_display_name,
}


def get_variable_for_explanation(
    input_parameter_name: str, organization_id: int, db_session: Session
) -> Variable | None:
    variable_retriever: VariableRetrieverType = INPUT_PARAMETER_NAME_TO_VARIABLE_RETRIEVER.get(
        input_parameter_name, DEFAULT_VARIABLE_RETRIEVER
    )
    return variable_retriever(input_parameter_name, organization_id, db_session)
