import uuid
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Set, Tuple
from uuid import UUID

from copilot_client_v3 import RecommendationSubmissionNoteRequest, Report, Submission
from infrastructure_common.logging import get_logger
from sqlalchemy.orm import Session, joinedload, noload
from sqlalchemy.orm.exc import ObjectDeletedError
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.parent import ParentType
from static_common.enums.recommendation import RecommendationActionEnum
from static_common.enums.submission import SubmissionStage
from structlog.stdlib import BoundLogger

from src.actions.directors import ActionDirector
from src.actions.models import Action, ActionClassEnum, SubmissionActionOperationEnum
from src.actions.utils import create_initial_priority
from src.models import ActionTypeEnum
from src.portfolio_optimization.logic import (
    build_portfolio_condition_requests,
    check_if_portfolio_condition_is_met,
    get_portfolio_condition_responses,
    is_portfolio_aggregation_source,
    parse_source_data,
)
from src.portfolio_optimization.utils import PortfolioValueRequirements
from src.recommendations.models import (
    AssignUnderwriterOutcome,
    DryRunRecommendation,
    Recommendation,
    RecommendationOutcome,
    RecommendationRiskScore,
    RecommendationTypeEnum,
    ReferOutcome,
    SendEmailOutcome,
    SetStatusOutcome,
    SubmissionOutcome,
    TextOutcome,
)
from src.recommendations.score_engine import ScoreEngine
from src.recommendations.utils import sort_recommendation_outcomes
from src.rules.models import Rule, RuleTypeEnum
from src.sources.utils import (
    DetailedValue,
    DetailedValueStoreItem,
    EngineOutcomeStorage,
    SourceValueItem,
    SourceValueStoreItem,
    check_if_threshold_is_met,
    fetch_submission_drivers_ids,
    fetch_submission_people_ids,
    fetch_submission_structure_ids,
    fetch_submission_vehicles_ids,
    fetch_transaction_ids,
    get_submission_business_ids,
    get_submission_premises_ids,
    get_submission_premises_ids_without_hidden_property_facts,
)
from src.variables.models import VariableTypesEnum

from ..utils import get_redis_cache_client
from .models import ConditionGroup, ConditionGroupInput, ConditionGroupTypeEnum
from .utils import (
    DEFAULT_REPORT_EXPAND,
    RecommendationEngineOutcome,
    SubmissionContext,
    fetch_warehouse_values,
    get_condition_groups_by_id,
    get_condition_relationship_discovered_in,
    get_condition_relationship_types,
    get_sources_by_id,
    stringify_triggers,
)

if TYPE_CHECKING:
    from common.clients.cache import RedisCacheClient

    from src.clients.copilot_v1 import CopilotV1Client
    from src.clients.copilot_v3 import CopilotV3Client
    from src.clients.ers_v3 import ERSClientV3
    from src.clients.facts import FactsClient
    from src.clients.redshift_api import RedshiftApiClient
    from src.sources.models import Source

    from .. import Variable

logger = get_logger()


class SubmissionNotFoundException(Exception):
    pass


def flatten(matrix):
    return [item for row in matrix for item in row]


class RecommendationBuilder:
    submission: Optional[Submission]
    report: Optional[Report]
    warehouse_values: Optional[Dict[str, str | None]] = None
    organization_id: int
    engine_outcomes: List[RecommendationEngineOutcome]
    rules: List[Rule]
    source_directors_by_source_id: Dict[UUID, "ConditionsSourceDirector"]  # noqa: F821
    condition_groups_by_id: Dict[UUID, ConditionGroup] | None
    sources_by_id: dict[UUID, "Source"] | None
    conditions_cache: Dict[UUID, Tuple[bool, List[SourceValueItem]]] = {}
    __session: Optional[Session] = None

    def __init__(
        self,
        submission_id: UUID,
        copilot_v1_client: "CopilotV1Client",
        copilot_v3_client: "CopilotV3Client",
        facts_client: "FactsClient",
        ers_v3_client: "ERSClientV3",
        redshift_api_client: "RedshiftApiClient",
        rules: Optional[List[Rule]] = None,
        session: Optional[Any] = None,
        load_all_rules: Optional[bool] = False,
        submission_context: SubmissionContext | None = None,
        cache_client: Optional["RedisCacheClient"] = None,
        condition_groups_by_id: dict[UUID, ConditionGroup] | None = None,
        sources_by_id: dict[UUID, "Source"] | None = None,
        has_warehouse_variables: Optional[bool] = False,
    ):
        self.copilot_v1_client = copilot_v1_client
        self.copilot_v3_client = copilot_v3_client
        self.facts_client = facts_client
        self.ers_v3_client = ers_v3_client
        self.redshift_api_client = redshift_api_client
        self.conditions_cache = {}
        self.source_directors_by_source_id = {}
        self.condition_groups_by_id = {}
        self.__session = session
        self.__submission_context = submission_context
        self.engine_outcomes = []
        self.cache_client = cache_client or get_redis_cache_client()
        self.condition_groups_by_id = condition_groups_by_id
        self.sources_by_id = sources_by_id

        # Fetch all needed metadata for running the recommendation logic once at the start
        self.__get_submission(submission_id)
        self.__get_organization()
        self.__get_report(self.submission.report_ids[0])
        if has_warehouse_variables:
            self.__get_warehouse_values(submission_id)

        # Set up values
        if rules is not None:
            self.rules = rules
        else:
            self.rules = self.__get_organization_active_rules(all_rules=load_all_rules)
        self.inactive_rules = self.__get_organization_inactive_rules()

    def execute_engine_logic(self):
        # build maps for faster access by the ids without creating nested schema
        self.__build_source_directors_map()
        self.__build_conditions_map()

        # for each rule execute the conditions logic and check if the action should be executed
        # each action have a condition group or a source attached to it.
        engine_outcomes = []
        for rule in self.rules:
            for action in rule.actions:
                value, triggers = self.__evaluate_action(action)
                if value:
                    engine_outcomes.append(
                        RecommendationEngineOutcome(
                            triggers=triggers,
                            action=action,
                            rule_id=rule.id,
                        )
                    )
        self.engine_outcomes = engine_outcomes

    def __get_priority(self, outcomes):
        priority_outcome = next(
            (outcome for outcome in outcomes if outcome.type == ActionTypeEnum.PRIORITY),
            None,
        )
        return priority_outcome.value if priority_outcome else create_initial_priority(self.submission)

    def save_recommendation(
        self,
        redis_keys: list[str] | None = None,
        store=True,
        patch_submission=False,
        save_submission_score=False,
        rule_id: UUID | None = None,
        only_log_results: bool | None = False,
        bound_logger: BoundLogger | None = None,
    ):
        log = bound_logger or logger

        engine_outcomes = self.get_stored_engine_outcomes(redis_keys) if redis_keys else self.engine_outcomes
        outcomes = ActionDirector(engine_outcomes, self.rules, self.submission).create_outcomes()

        triage_decline_outcome = None
        set_status_outcome = None
        for outcome in outcomes:
            if isinstance(outcome, SubmissionOutcome):
                if SubmissionActionOperationEnum.TRIAGE_DECLINE_NO_EMAIL.value in outcome.value:
                    triage_decline_outcome = outcome
                    break

                if SubmissionActionOperationEnum.TRIAGE_DECLINE_CLEARING_EMAIL.value in outcome.value:
                    triage_decline_outcome = outcome
                    continue

                if SubmissionActionOperationEnum.TRIAGE_DECLINE_NOT_ELIGIBLE_PRODUCER.value in outcome.value:
                    triage_decline_outcome = outcome
                    continue

                if not triage_decline_outcome and SubmissionActionOperationEnum.TRIAGE_DECLINE.value in outcome.value:
                    triage_decline_outcome = outcome
            elif isinstance(outcome, SetStatusOutcome):
                set_status_outcome = outcome.status_id

        recommendation_outcomes = [outcome for outcome in outcomes if isinstance(outcome, RecommendationOutcome)]
        submission_score = None
        log.info("Calculating submission score")
        score_engine = ScoreEngine(self.session, self.redshift_api_client)
        if score_outcome := (
            score_engine.calculate_submission_score(self.submission, self.organization_id, recommendation_outcomes)
            or (
                score_engine.get_score_outcome_with_zero_value(
                    self.submission, self.organization_id, recommendation_outcomes
                )
                if triage_decline_outcome
                else None
            )
        ):
            outcomes.append(score_outcome)
            submission_score = score_outcome.score

        if not rule_id:
            recommendation = Recommendation(
                submission_id=self.submission.id,
                organization_id=self.organization_id,
                outcomes=outcomes,
                type=RecommendationTypeEnum.REGULAR,
            )
        else:
            condition_groups_map = {str(key): value[0] for key, value in self.conditions_cache.items()}
            sources_map = {
                str(key): value.get_value()
                for key, value in self.source_directors_by_source_id.items()
                if not is_portfolio_aggregation_source(value.builder.source)
            }
            recommendation = DryRunRecommendation(
                submission_id=self.submission.id,
                organization_id=self.organization_id,
                outcomes=outcomes,
                rule_id=rule_id,
                type=RecommendationTypeEnum.DRY_RUN,
                condition_groups=condition_groups_map,
                evaluated_sources=sources_map,
                evaluated_successfully=True,
            )

        if recommendation_outcomes:
            sort_recommendation_outcomes(recommendation_outcomes)
        priority = self.__get_priority(outcomes)

        if only_log_results:
            is_refer = len([outcome for outcome in outcomes if isinstance(outcome, ReferOutcome)]) > 0
            log.info(
                "Recommendation calculated, but not committed.",
                existing_action=(
                    self.submission.recommendation_v2_action if self.submission.recommendation_v2_action else "none"
                ),
                new_action=recommendation_outcomes[0].value if recommendation_outcomes else "none",
                existing_priority=(
                    self.submission.recommendation_v2_priority
                    if self.submission.recommendation_v2_priority is not None
                    else "none"
                ),
                new_priority=priority if priority is not None else "none",
                existing_submission_score=(
                    self.submission.recommendation_v2_score
                    if self.submission.recommendation_v2_score is not None
                    else "none"
                ),
                new_submission_score=submission_score if submission_score is not None else "none",
                was_refer=(
                    self.submission.recommendation_v2_is_refer
                    if self.submission.recommendation_v2_is_refer is not None
                    else "none"
                ),
                is_refer=is_refer,
                stage=self.submission.stage if self.submission.stage is not None else "none",
            )

        if only_log_results:
            return

        if store and not only_log_results:
            if not rule_id:
                self.session.query(Recommendation).filter(Recommendation.submission_id == self.submission.id).delete()
            self.session.add(recommendation)
            self.session.commit()
            if patch_submission:
                try:
                    triage_decline_explanations = []
                    if triage_decline_outcome:
                        triage_result = self.__determine_triage_result(triage_decline_outcome)
                        rule_ids = [e.rule_id for e in triage_decline_outcome.outcome_explanations]
                        rules = self.session.query(Rule).filter(Rule.id.in_(rule_ids)).all()
                        triage_decline_explanations = [r.name for r in rules]
                    else:
                        triage_result = "ACCEPT"
                    refer_outcomes = [outcome for outcome in outcomes if isinstance(outcome, ReferOutcome)]

                    if len(recommendation_outcomes) > 0:
                        recommendation_outcome = recommendation_outcomes[0]
                        explanations = []

                        if rule_ids := (
                            [e.rule_id for e in recommendation_outcome.outcome_explanations]
                            if recommendation_outcome.value == RecommendationActionEnum.DECLINE
                            else []
                        ):
                            rules = self.session.query(Rule).filter(Rule.id.in_(rule_ids)).all()
                            explanations = [r.name for r in rules]
                        self.copilot_v3_client.patch_submission(
                            self.submission.id,
                            recommendation_outcome.value,
                            priority=priority,
                            score=submission_score,
                            explanations=explanations,
                            is_refer=len(refer_outcomes) > 0,
                            triage_result=triage_result,
                            triage_decline_explanations=triage_decline_explanations,
                            set_status_result=set_status_outcome,
                            score_ml=score_outcome.score_ml if score_outcome else None,
                            pm_rules_modifier=score_outcome.pm_rules_modifier if score_outcome else None,
                        )
                    else:
                        self.copilot_v3_client.patch_submission(
                            self.submission.id,
                            RecommendationActionEnum.NO_ACTION,
                            priority=create_initial_priority(self.submission),
                            score=submission_score,
                            is_refer=len(refer_outcomes) > 0,
                            triage_result=triage_result,
                            triage_decline_explanations=triage_decline_explanations,
                            set_status_result=set_status_outcome,
                            score_ml=score_outcome.score_ml if score_outcome else None,
                            pm_rules_modifier=score_outcome.pm_rules_modifier if score_outcome else None,
                        )
                except ObjectDeletedError:
                    log.warning("Race condition... the recommendation was deleted before submission was updated")
                    return None

                submission_note_requests: list[RecommendationSubmissionNoteRequest] = []
                submission_id: UUID | None = self.submission.id if self.submission else None
                if submission_id:
                    rule_ids_to_delete = self.__get_notes_rule_ids()

                    for outcome in [  # type: ignore
                        o for o in outcomes if isinstance(o, TextOutcome) and o.type == ActionTypeEnum.NOTES
                    ]:
                        rule_id: UUID | None = next((e.rule_id for e in outcome.outcome_explanations), None)
                        text: str | None = "\n".join(outcome.value) if outcome.value else None  # type: ignore

                        if rule_id is not None and text is not None:
                            if rule_id in rule_ids_to_delete:
                                rule_ids_to_delete.remove(rule_id)

                            request: RecommendationSubmissionNoteRequest = RecommendationSubmissionNoteRequest(
                                submission_id=str(submission_id),
                                rule_id=str(rule_id),
                                text=text,
                                is_editable=outcome.is_editable,
                                is_delete_request=False,
                            )
                            submission_note_requests.append(request)

                    if rule_ids_to_delete:
                        for rule_id in rule_ids_to_delete:
                            submission_note_requests.append(
                                RecommendationSubmissionNoteRequest(
                                    submission_id=str(submission_id),
                                    rule_id=str(rule_id),
                                    text="Note Deleted",
                                    is_editable=False,
                                    is_delete_request=True,
                                )
                            )
                    if submission_note_requests:
                        self.copilot_v3_client.add_recommendation_submission_notes(submission_note_requests)

                for outcome in [o for o in outcomes if isinstance(o, AssignUnderwriterOutcome)]:
                    for uw_id in outcome.value:
                        self.copilot_v3_client.add_submission_user(self.submission.id, int(uw_id))

                for outcome in [o for o in outcomes if isinstance(o, SendEmailOutcome)]:
                    outcome: SendEmailOutcome
                    rule_ids = [e.rule_id for e in outcome.outcome_explanations]
                    if not rule_ids:
                        continue

                    rule = self.session.query(Rule).filter(Rule.id.in_(rule_ids)).first()
                    if not rule:
                        continue

                    self.copilot_v3_client.send_submission_email(
                        self.submission.id,
                        rule.name,
                        outcome.email_template_id,
                    )

                    if outcome.decline_submission:
                        self.copilot_v3_client.patch_submission_lite(
                            self.submission.id,
                            {
                                "stage": SubmissionStage.DECLINED,
                            },
                        )

                    # only one email can be sent per submission
                    break

        if (
            save_submission_score or (self.organization_id == 6 and submission_score is not None)
        ) and not only_log_results:
            self.session.query(RecommendationRiskScore).filter(
                RecommendationRiskScore.submission_id == self.submission.id
            ).delete()
            self.session.add(
                RecommendationRiskScore(
                    submission_id=self.submission.id,
                    organization_id=self.organization_id,
                    score=submission_score,
                )
            )
            self.session.commit()

        return recommendation

    def get_stored_engine_outcomes(self, redis_keys: list[str]) -> list[RecommendationEngineOutcome]:
        all_outcomes: list[EngineOutcomeStorage] = flatten(
            self.cache_client.get_from_cache_bulk(redis_keys, None, None)
        )
        action_ids = [outcome.action_id for outcome in all_outcomes]
        all_actions = self.session.query(Action).filter(Action.id.in_(action_ids)).all()
        id_to_action = {action.id: action for action in all_actions}

        if len(all_outcomes) > 0 and not self.source_directors_by_source_id:
            self.__build_source_directors_map()

        engine_outcomes = []
        for outcome in all_outcomes:
            triggers = []
            for trigger in outcome.triggers:
                source_director = self.source_directors_by_source_id.get(UUID(trigger.source_id))
                if not source_director:
                    continue
                triggers.append(
                    SourceValueItem(
                        values=trigger.values,
                        display_values=trigger.display_values,
                        values_ids=(
                            [UUID(value_id) for value_id in (trigger.values_ids or [])] if trigger.values_ids else None
                        ),
                        parent_id=UUID(trigger.parent_id) if trigger.parent_id else None,
                        source=source_director.builder.source,
                        variable_type=trigger.variable_type,
                        parent_type=trigger.parent_type,
                        parent_description=trigger.parent_description,
                        detailed_values=(
                            [
                                DetailedValue(
                                    values=dv.values,
                                    parent_id=UUID(dv.parent_id) if dv.parent_id else None,
                                    parent_type=dv.parent_type,
                                )
                                for dv in trigger.detailed_values
                            ]
                            if trigger.detailed_values
                            else None
                        ),
                    )
                )
            engine_outcomes.append(
                RecommendationEngineOutcome(
                    rule_id=outcome.rule_id, triggers=triggers, action=id_to_action[outcome.action_id]
                )
            )

        return engine_outcomes

    def store_engine_outcomes(self, execution_id: UUID) -> str:
        outcome_storages = []
        for engine_outcome in self.engine_outcomes:
            store_triggers = [
                SourceValueStoreItem(
                    values=stringify_triggers(trigger.values),
                    values_ids=(
                        [str(value_id) for value_id in (trigger.values_ids or [])] if trigger.values_ids else None
                    ),
                    display_values=trigger.display_values,
                    parent_id=str(trigger.parent_id) if trigger.parent_id else None,
                    source_id=str(trigger.source.id),
                    variable_type=trigger.variable_type,
                    parent_type=trigger.parent_type,
                    parent_description=trigger.parent_description,
                    detailed_values=(
                        [
                            DetailedValueStoreItem(
                                values=stringify_triggers(dv.values),
                                parent_id=str(dv.parent_id) if dv.parent_id else None,
                                parent_type=dv.parent_type,
                            )
                            for dv in trigger.detailed_values
                        ]
                        if trigger.detailed_values
                        else None
                    ),
                )
                for trigger in engine_outcome.triggers
            ]
            outcome_storages.append(
                EngineOutcomeStorage(
                    triggers=store_triggers,
                    execution_id=execution_id,
                    action_id=engine_outcome.action.id,
                    rule_id=engine_outcome.rule_id,
                )
            )
        redis_key = f"engine_outcomes_{execution_id}_{uuid.uuid4()}"
        self.cache_client.add_to_cache(redis_key, outcome_storages, 3600)
        return redis_key

    @property
    def session(self):
        if not self.__session:
            raise RuntimeError("Session is not set in RecommendationBuilder")
        return self.__session

    def get_parent_ids(
        self,
        parent_type: ParentType,
        source_id: UUID,
        parent_ids: Optional[List[UUID]] = None,
        with_owner=False,
        hide_mailing_address=False,
    ):
        submission = self.submission
        submission_id = UUID(submission.id)
        organization_id = self.organization_id

        if parent_ids:
            if parent_type == ParentType.PREMISES:
                if hide_mailing_address:
                    return get_submission_premises_ids_without_hidden_property_facts(
                        submission, business_ids=parent_ids
                    )
                return get_submission_premises_ids(submission, business_ids=parent_ids)
            return parent_ids

        if parent_type == ParentType.BUSINESS:
            return get_submission_business_ids(submission, with_owner=with_owner)
        if parent_type == ParentType.PREMISES:
            if hide_mailing_address:
                return get_submission_premises_ids_without_hidden_property_facts(submission)
            return get_submission_premises_ids(submission)
        if parent_type == ParentType.STRUCTURE:
            return fetch_submission_structure_ids(self.facts_client, submission, organization_id)
        if parent_type == ParentType.TRANSACTION:
            return fetch_transaction_ids(self.facts_client, submission, organization_id)
        if parent_type == ParentType.SUBMISSION:
            source: "Source" = self.source_directors_by_source_id.get(source_id).builder.source
            variable: "Variable" = source.variable

            if variable.type == VariableTypesEnum.SUBMISSION:
                variable_key: str = variable.key
                if variable_key.startswith("coverage"):
                    return [UUID(str(coverage.id)) for coverage in submission.coverages]
            return [submission_id]
        if parent_type == ParentType.VEHICLE:
            source: "Source" = self.source_directors_by_source_id.get(source_id).builder.source
            condition = source.source_condition.condition
            relationship_types: List[FactSubtypeID] = get_condition_relationship_types(condition)
            discovered_in = get_condition_relationship_discovered_in(condition) or None
            return fetch_submission_vehicles_ids(
                self.copilot_v3_client,
                self.facts_client,
                submission,
                organization_id,
                relationship_types,
                discovered_in,
            )
        if parent_type == ParentType.DRIVER:
            return fetch_submission_drivers_ids(self.facts_client, submission_id, organization_id)
        if parent_type == ParentType.PERSON:
            return fetch_submission_people_ids(self.facts_client, submission, organization_id)
        logger.error("Parent type is not supported", parent_type=parent_type)
        return []

    def __determine_triage_result(self, outcome: SubmissionOutcome):
        if outcome.value[0] == SubmissionActionOperationEnum.TRIAGE_DECLINE_NO_EMAIL:
            return "DECLINE_NO_EMAIL"
        elif outcome.value[0] == SubmissionActionOperationEnum.TRIAGE_DECLINE_CLEARING_EMAIL:
            return "DECLINE_CLEARING_EMAIL"
        elif outcome.value[0] == SubmissionActionOperationEnum.TRIAGE_DECLINE:
            return "DECLINE"
        elif outcome.value[0] == SubmissionActionOperationEnum.TRIAGE_DECLINE_NOT_ELIGIBLE_PRODUCER:
            return "DECLINE_NOT_ELIGIBLE_PRODUCER"
        return None

    def __get_submission(self, submission_id: UUID):
        if self.__submission_context:
            self.submission = self.__submission_context.submission
            entities = self.__submission_context.entities
        else:
            self.submission = self.copilot_v3_client.get_submission_lite(submission_id)
            if not self.submission:
                logger.warning("Submission was not found", submission_id=submission_id)
                raise SubmissionNotFoundException
            entity_ids = {sb.business_id for sb in self.submission.businesses if sb.business_id}
            entities = self.ers_v3_client.bulk_get_entities(list(entity_ids), with_relations=True)
            entities = {e.id: e for e in entities}
        for sb in self.submission.businesses:
            sb.entity_data = entities.get(sb.business_id)

    def __get_report(self, report_id: UUID | str):
        if self.__submission_context:
            self.report = self.__submission_context.report
        else:
            self.report = self.copilot_v3_client.get_report(report_id, expand=",".join(DEFAULT_REPORT_EXPAND))

    def __get_warehouse_values(self, submission_id: UUID):
        if self.__submission_context and self.__submission_context.warehouse_values:
            self.warehouse_values = self.__submission_context.warehouse_values
        else:
            self.warehouse_values = fetch_warehouse_values(
                submission_id, self.organization_id, self.redshift_api_client
            )

    def __get_organization(self):
        if self.__submission_context:
            self.organization_id = self.__submission_context.organization_id
            return
        if not self.submission:
            raise ValueError("RecommendationBuilder was not initiated properly")

        user = self.copilot_v1_client.get_user(user_id=self.submission.owner_id)
        if not user["organization"]:
            raise ValueError("Recommendations cannot be calculated if owner has no organization")
        self.organization_id = user["organization"]["id"]

    def __get_organization_active_rules(self, all_rules=False):
        query = (
            self.session.query(Rule)
            .filter(Rule.organization_id == self.organization_id)
            .filter(Rule.is_active.is_(True))
            .options(joinedload(Rule.actions))
            .options(noload(Rule.sources))
            .options(noload(Rule.condition_groups))
            .options(noload(Rule.outcomes_explanations))
        )
        if not all_rules:
            query = query.filter(Rule.rule_type != RuleTypeEnum.PORTFOLIO_OPTIMIZATION)
        return query.all()

    def __get_organization_inactive_rules(self):
        return (
            self.session.query(Rule)
            .filter(Rule.organization_id == self.organization_id)
            .filter(Rule.is_active.is_(False))
            .options(joinedload(Rule.actions))
            .options(noload(Rule.sources))
            .options(noload(Rule.condition_groups))
            .options(noload(Rule.outcomes_explanations))
        ).all()

    def __create_source_director(self, source: "Source", rules_map: dict[str, Rule]):
        from src.sources.directors import (
            ConditionsSourceDirector,
            PortfolioOptimizationSourceDirector,
        )

        rule = rules_map.get(source.rule_id)

        if rule.rule_type == RuleTypeEnum.PORTFOLIO_OPTIMIZATION:
            return PortfolioOptimizationSourceDirector(source, self)
        return ConditionsSourceDirector(source, self)

    def __build_source_directors_map(self):
        rules_map = {rule.id: rule for rule in self.rules}
        sources_by_id: dict[UUID, "Source"] | None = self.sources_by_id

        if sources_by_id is None:
            rule_ids = list(rules_map.keys())
            sources_by_id = get_sources_by_id(self.session, rule_ids)

        self.source_directors_by_source_id = {
            source_id: self.__create_source_director(source, rules_map) for source_id, source in sources_by_id.items()
        }

    def __build_conditions_map(self):
        if not self.condition_groups_by_id:
            rule_ids = [rule.id for rule in self.rules]
            self.condition_groups_by_id = get_condition_groups_by_id(self.session, rule_ids)

    def __evaluate_action(self, action: Action) -> Tuple[bool, List[SourceValueItem]]:
        if action.action_class == ActionClassEnum.NONE:
            # These actions do not yield any effects on submission
            # so we don't even need to run them for specific submissions
            # Their purpose is .e.g KPI Monitoring in PO - just displaying current
            # state of data
            return False, []
        if condition_group_id := action.condition_group_id:
            value, triggers = self.__evaluate_condition(condition_group_id, action.condition)
            if action.is_negative:
                triggers = self.__get_condition_all_blank_source_values(action.condition_group_id)
                return not value, triggers
            return value, triggers
        if condition_director := self.source_directors_by_source_id.get(action.source_id):
            value = condition_director.get_value()
            triggering_values = condition_director.builder.triggering_values
            return value, triggering_values
        return False, []

    def __evaluate_condition(
        self, condition_id: UUID, action_condition: dict | None
    ) -> Tuple[bool, List[SourceValueItem]]:
        cached_value = self.conditions_cache.get(condition_id)
        if cached_value is not None:
            return cached_value

        if condition := self.condition_groups_by_id.get(condition_id):
            if condition.type == ConditionGroupTypeEnum.AND:
                return self.__evaluate_and_condition(condition_id, action_condition)
            if condition.type == ConditionGroupTypeEnum.OR:
                return self.__evaluate_or_condition(condition_id, action_condition)
            if condition.type == ConditionGroupTypeEnum.NOT:
                return self.__evaluate_not_condition(condition_id, action_condition)
            if condition.type == ConditionGroupTypeEnum.AND_ENTITY:
                return self.__evaluate_and_entity_condition(condition_id)
            if condition.type == ConditionGroupTypeEnum.AND_PORTFOLIO:
                return self.__evaluate_portfolio_condition(condition_id, action_condition)
            if condition.type == ConditionGroupTypeEnum.OR_PORTFOLIO:
                return self.__evaluate_portfolio_condition(condition_id, action_condition, is_or=True)
        return False, []

    def __evaluate_and_condition(
        self, condition_id: UUID, action_condition: dict | None
    ) -> Tuple[bool, List[SourceValueItem]]:
        condition = self.condition_groups_by_id.get(condition_id)
        if condition is None:
            return False, []

        triggers = []
        for condition_input in condition.condition_group_inputs:
            if not self.__condition_should_be_evaluated(condition_input.source_id):
                continue

            condition_input_value = self.__get_condition_input_value(condition_input, action_condition)
            if not condition_input_value[0]:
                self.conditions_cache[condition_id] = condition_input_value
                return condition_input_value
            triggers.extend(condition_input_value[1])

        result = (True, triggers)
        self.conditions_cache[condition_id] = result
        return result

    def __evaluate_or_condition(
        self, condition_id: UUID, action_condition=dict | None
    ) -> Tuple[bool, List[SourceValueItem]]:
        condition = self.condition_groups_by_id.get(condition_id)
        if condition is None:
            return False, []

        for condition_input in condition.condition_group_inputs:
            if not self.__condition_should_be_evaluated(condition_input.source_id):
                continue

            condition_input_value = self.__get_condition_input_value(condition_input, action_condition)
            if condition_input_value[0]:
                self.conditions_cache[condition_id] = condition_input_value
                return condition_input_value

        result = (False, [])
        self.conditions_cache[condition_id] = result
        return result

    def __evaluate_not_condition(
        self, condition_id: UUID, action_condition=dict | None
    ) -> Tuple[bool, List[SourceValueItem]]:
        condition = self.condition_groups_by_id.get(condition_id)
        if condition is None:
            return True, []

        for condition_input in condition.condition_group_inputs:
            condition_input_value = self.__get_condition_input_value(condition_input, action_condition)
            if condition_input_value[0]:
                self.conditions_cache[condition_id] = (False, [])
                return False, []

        triggers = self.__get_condition_all_blank_source_values(condition_id)
        result = (True, triggers)
        self.conditions_cache[condition_id] = result
        return result

    def __evaluate_and_entity_condition(self, condition_id: UUID) -> Tuple[bool, List[SourceValueItem]]:
        condition = self.condition_groups_by_id.get(condition_id)
        if condition is None or not condition.condition_group_inputs or self.__has_nested_group(condition):
            self.conditions_cache[condition_id] = (False, [])
            return False, []

        all_parent_ids = (
            self.__get_structure_entity_condition_parent_ids()
            if condition.is_structure_level_condition
            else self.__get_entity_condition_parent_ids(condition.condition_group_inputs[0].source_id)
        )
        matching_parent_ids = set(all_parent_ids)
        triggers: List[SourceValueItem] = []

        for condition_input in condition.condition_group_inputs:
            if condition_director := self.source_directors_by_source_id.get(condition_input.source_id):
                value = condition_director.get_value(parent_ids=list(matching_parent_ids))
                if not value:
                    self.conditions_cache[condition_id] = (False, [])
                    return False, []
                triggering_values: List[SourceValueItem] = condition_director.builder.triggering_values
                triggers.extend(triggering_values)

                matching_value_ids: Set[UUID] = set()
                for triggering_value in triggering_values:
                    matching_value_ids.add(triggering_value.parent_id)
                    for value_id in triggering_value.values_ids or []:
                        matching_value_ids.add(value_id)

                matching_parent_ids = matching_parent_ids & matching_value_ids
            if len(matching_parent_ids) == 0:
                self.conditions_cache[condition_id] = (False, [])
                return False, []

        matching_triggers = []
        for trigger in triggers:
            if trigger.parent_id in matching_parent_ids or any(
                (value_id in matching_parent_ids) for value_id in (trigger.values_ids or [])
            ):
                matching_triggers.append(trigger)

        if check_if_threshold_is_met(condition.condition or {}, matching_triggers, len(all_parent_ids)):
            result = (True, matching_triggers)
            self.conditions_cache[condition_id] = result
            return result
        self.conditions_cache[condition_id] = (False, [])
        return False, []

    def __evaluate_portfolio_condition(self, condition_id: UUID, action_condition: dict | None, is_or=False):
        if is_or:
            is_submission_fitting, matching_triggers = self.__evaluate_or_condition(condition_id, action_condition)
        else:
            is_submission_fitting, matching_triggers = self.__evaluate_and_condition(condition_id, action_condition)
        if not is_submission_fitting:
            return False, []

        sources_by_id = {
            k: self.source_directors_by_source_id[k].builder.source for k in self.source_directors_by_source_id.keys()
        }
        rule_id = self.condition_groups_by_id[condition_id].rule_id
        rule = next(r for r in self.rules if r.id == rule_id)

        request_building_requirements: PortfolioValueRequirements = parse_source_data(
            rule,
            self.condition_groups_by_id,
            sources_by_id,
        )

        query_data_request, total_request = build_portfolio_condition_requests(request_building_requirements)
        query_data_response, total_response = get_portfolio_condition_responses(
            rule.organization_id, query_data_request, total_request, self.redshift_api_client
        )

        portfolio_result = check_if_portfolio_condition_is_met(
            rule,
            query_data_response,
            total_response,
            request_building_requirements.threshold_condition,
            action_condition,
        )

        if not portfolio_result.is_met:
            return False, []

        source_item = SourceValueItem(
            values=[portfolio_result.value] if portfolio_result.value is not None else [],
            parent_id=UUID(self.submission.id),  # type: ignore
            source=request_building_requirements.portfolio_aggregation_source,
            variable_type=request_building_requirements.portfolio_aggregation_source.variable.type,
            parent_type=ParentType.SUBMISSION,
            parent_description="Portfolio",
        )

        triggers = [source_item]
        triggers.extend(matching_triggers)
        return True, triggers

    def __get_condition_input_value(
        self, condition_input: ConditionGroupInput, action_condition: dict | None
    ) -> Tuple[bool, List[SourceValueItem]]:
        if parent_condition_id := condition_input.parent_condition_group_id:
            return self.__evaluate_condition(parent_condition_id, action_condition)

        if condition_director := self.source_directors_by_source_id.get(condition_input.source_id):
            value = condition_director.get_value()
            triggering_values = condition_director.builder.triggering_values
            return value, triggering_values
        return False, []

    @staticmethod
    def __has_nested_group(condition: ConditionGroup) -> bool:
        return any(condition_input.parent_condition_group_id for condition_input in condition.condition_group_inputs)

    def __has_same_parent_type(self, condition: ConditionGroup) -> bool:
        uniq_parent_types = set()
        for condition_input in condition.condition_group_inputs:
            if condition_director := self.source_directors_by_source_id.get(condition_input.source_id):
                uniq_parent_types.add(condition_director.builder.get_parent_type())
            else:
                return False

        if ParentType.BUSINESS in uniq_parent_types and ParentType.PREMISES in uniq_parent_types:
            return len(uniq_parent_types) == 2
        return len(uniq_parent_types) == 1 and None not in uniq_parent_types

    def __get_entity_condition_parent_ids(self, source_id: UUID) -> List[UUID]:
        if condition_director := self.source_directors_by_source_id.get(source_id):
            parent_type = condition_director.builder.get_parent_type()
            parent_type = ParentType.BUSINESS if parent_type == ParentType.PREMISES else parent_type

            return self.get_parent_ids(parent_type, source_id=source_id)
        return []

    def __get_structure_entity_condition_parent_ids(self) -> List[UUID]:
        premises_ids = get_submission_premises_ids(self.submission)
        structure_ids = fetch_submission_structure_ids(self.facts_client, self.submission, self.organization_id)

        return premises_ids + structure_ids

    def __get_condition_all_blank_source_values(self, condition_id: UUID) -> List[SourceValueItem]:
        condition = self.condition_groups_by_id.get(condition_id)
        source_values = []
        if condition is None:
            return source_values

        for condition_input in condition.condition_group_inputs:
            if parent_condition_id := condition_input.parent_condition_group_id:
                source_values.extend(self.__get_condition_all_blank_source_values(parent_condition_id))
            elif condition_director := self.source_directors_by_source_id.get(condition_input.source_id):
                source_values.extend(condition_director.builder.create_blank_source_values())

        return source_values

    def __is_portfolio_status_condition(self, source_id: Optional[UUID]) -> bool:
        if not source_id:
            return False
        source = self.source_directors_by_source_id.get(source_id).builder.source
        if source.rule.rule_type != RuleTypeEnum.PORTFOLIO_OPTIMIZATION:
            return False
        return source.variable.type == VariableTypesEnum.SUBMISSION and source.variable.key == "stage"

    def __is_portfolio_aggregation_source(self, source_id: Optional[UUID]):
        if not source_id:
            return False
        return is_portfolio_aggregation_source(self.source_directors_by_source_id.get(source_id).builder.source)

    def __condition_should_be_evaluated(self, source_id: UUID | None) -> bool:
        # Some conditions should not be evaluated for PO rules, in the context of the current submission.
        # These conditions are only utilized for the portfolio aggregation.
        if self.__is_portfolio_status_condition(source_id):
            return False
        if self.__is_portfolio_aggregation_source(source_id):
            return False

        return True

    def __get_notes_rule_ids(self) -> Set[UUID]:
        return {
            rule.id
            for rule in (self.rules + self.inactive_rules)
            if any(action.action_type == ActionTypeEnum.NOTES for action in rule.actions)
        }
