import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  CircularProgress,
  Typography,
} from "@mui/material";
import { useGetBulkPrometrixRisks } from "@queries/submission";
import { useReport } from "@utils/useReport";
import { useEffect, useMemo, useState } from "react";
import { RequestPrometrixDatModal } from "./RequestPrometrixDataModal";
import { Structures } from "@features/modes/jsx/reusables/Structures";
import { NoItems } from "@features/no-items";
import DocumentScannerOutlinedIcon from "@mui/icons-material/DocumentScannerOutlined";
import { useStructuresContextData } from "@features/modes-display/entity-picker/context/structuresContext";
import { useRowContextEntities } from "@features/modes-display/utils";
import Image from "next/image";
import { ELEMENT_ID } from "@features/modes/jsx/constants";
import { useQueryParam } from "@features/modes-display/entity-picker/query";
import { useFacts } from "@queries/facts";
import { PARENT_TYPES } from "@legacy/constants/facts";
import { PrometrixRequest } from "@features/cards/prometrix/ConfirmPrometrixRequestModal";
import { useSnackbar } from "notistack";
import workflowsApiClient from "@legacy/clients/workflowsApiClient";
import { usePrometrixDataState } from "@features/hazard-hub/hooks/useHazardHubData";
import { useQueryClient } from "@tanstack/react-query";

export const PrometrixBody = () => {
  const [requestingData, setRequestingData] = useState(false);

  const report = useReport();

  const parentIds = useRowContextEntities()?.parentIds ?? [];
  const businessId = parentIds[0] ?? "";
  const premiseId = report.getPremiseIdForBusinessId(businessId) ?? "";
  const allegedStructureCount = useStructuresContextData(
    (state) =>
      state.prometrixOnlyPremiseIdToStructureIds.get(premiseId)?.size ?? 0
  );
  const structureIds = useStructuresContextData((x) =>
    x.prometrixOnlyPremiseIdToStructureIds.get(premiseId)
  );
  const { data: existingRisks, isLoading: isLoadingExistingRisks } = useFacts({
    parentType: PARENT_TYPES.STRUCTURE,
    factSubtypeIds: ["PROMETRIX_RISK_ID"],
    report,
    parentIds: [...(structureIds ?? [])],
    submissionId: report.submission.id,
  });

  const businessQueryParam = useQueryParam(
    ELEMENT_ID.PREMISES_VIEWER,
    "business"
  );

  const { enqueueSnackbar } = useSnackbar();

  const businessIds = useMemo(() => {
    return [businessQueryParam!];
  }, [businessQueryParam]);

  const { data, isLoading } = useGetBulkPrometrixRisks(
    report.submission.id,
    businessIds
  );

  const queryClient = useQueryClient();

  const risks = useMemo(() => {
    const getSubmissionBusinessId = (riskId: string) => {
      return data.find((x) => x.risks?.some((r) => r?.risk_id === riskId))
        ?.submissionBusinessId;
    };

    return data
      .flatMap((x) => x.risks!)
      .map((x) => ({
        riskId: x.risk_id!,
        name: `Risk ID: ${x.risk_id} - ${x.building_description}`,
        occupants: x.occupants?.map((o) => o.name!) ?? [],
        submissionBusinessId: getSubmissionBusinessId(x.risk_id!)!,
      }));
  }, [data]);

  const { showLoader, recordIngestionRequested } = usePrometrixDataState(
    report.submission.id,
    premiseId
  );

  useEffect(() => {
    if (showLoader) return;

    queryClient.invalidateQueries(["facts"]);
  }, [queryClient, showLoader]);

  const requestableRisks = useMemo(() => {
    if (isLoadingExistingRisks) return [];

    return risks.filter((x) => {
      return !existingRisks?.some((r) => r.getValue() == x.riskId);
    });
  }, [risks, existingRisks, isLoadingExistingRisks]);

  if (isLoading) {
    return <CircularProgress />;
  }

  if (!risks.length) {
    return (
      <NoItems title="No Prometrix BUR reports are available for this address" />
    );
  }

  const handleConfirm = (requests: PrometrixRequest[]) => {
    requests.forEach((request) => {
      workflowsApiClient.publishPrometrixRequestedEvent({
        submissionId: report.submission.id,
        premisesId: request.premisesId,
        riskId: request.riskId,
      });
    });

    recordIngestionRequested();

    enqueueSnackbar("Prometrix data has been requested");

    setRequestingData(false);
  };

  return (
    <Box>
      {showLoader && (
        <Box>
          <Image
            src="/loading-animation.gif"
            width={170 / 2}
            height={66 / 2}
            alt="Loading"
          />
          <Typography variant="h6" sx={{ my: 0.5, fontWeight: 500 }}>
            We are currently processing the reports
          </Typography>

          <Typography>Information should be available soon</Typography>
        </Box>
      )}

      {!allegedStructureCount && !showLoader && (
        <>
          <Alert
            severity="info"
            action={
              <Button
                color="inherit"
                onClick={() => setRequestingData(true)}
                size="small"
              >
                REQUEST DATA
              </Button>
            }
          >
            To retrieve the report for this address, we need to confirm the Risk
            IDs
          </Alert>

          <Button
            startIcon={
              <DocumentScannerOutlinedIcon sx={{ color: "#B7B7B7" }} />
            }
            sx={{ mt: 1 }}
            size="large"
            color="primary"
            onClick={() => setRequestingData(true)}
          >
            Request Prometrix Data
          </Button>
        </>
      )}

      {!!allegedStructureCount && (
        <>
          <Box display="flex" justifyContent="space-between">
            <Box flex={1}>
              <Structures source="prometrix" />
            </Box>

            {requestableRisks.length > 0 && (
              <Box>
                <Button onClick={() => setRequestingData(true)}>
                  + Request Additional Prometrix Data
                </Button>
              </Box>
            )}
          </Box>
        </>
      )}

      {requestingData && (
        <RequestPrometrixDatModal
          risks={requestableRisks}
          onClose={() => setRequestingData(false)}
          onConfirm={handleConfirm}
        />
      )}
    </Box>
  );
};
