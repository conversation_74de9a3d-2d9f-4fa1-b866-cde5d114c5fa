import { useMemo } from "react";
import { getFormattedMetricValue } from "@legacy/helpers/metricsHelpers";
import {
  CategoricalDataSummaryV2,
  MeanAndSumSummaryV2,
  MetricV2,
} from "@legacy/models/MetricV2";
import { useMemoArray } from "@utils/useMemoArray";
import { MetricV2ComputedUnitsEnum } from "@legacy/api_clients/copilot_api_client";

const formatValueWithUnits = (
  name: string | undefined,
  metricValue: number | null,
  units: MetricV2ComputedUnitsEnum
) => {
  const value = getFormattedMetricValue(name, metricValue, units, 1, true);

  if (units === MetricV2ComputedUnitsEnum.Usd) {
    return `$${value}`;
  }
  if (units === MetricV2ComputedUnitsEnum.SquareFeet) {
    return `${value} ft²`;
  }
  return value;
};

export type SummaryData = {
  label: string;
  value: string | number;
  metric: MetricV2;
};
export const useCreateStructureSummaries = (
  metrics: MetricV2[],
  parentIds: ReadonlyArray<string> | undefined,
  namesFilter?: ReadonlyArray<string> | undefined
): SummaryData[] => {
  const metricsArray = useMemoArray(metrics);
  return useMemo(
    () =>
      (metricsArray ?? []).flatMap((metric) => {
        if (parentIds && !parentIds.includes(metric.parent_id ?? "")) {
          return [];
        }
        if (namesFilter && !namesFilter.includes(metric.name ?? "")) {
          return [];
        }

        // This metric does not make sense when we look for single premise, because
        // metric would be calculated from just a single fact taht is STRUCTURES_COUNT
        // for that premises
        if (metric.summary_config_id === "STRUCTURES_COUNT_SUMMARY_CONFIG") {
          return [];
        }

        if (metric.metric_type === "categorical_data_summary") {
          const yes = (
            metric as CategoricalDataSummaryV2
          ).category_summaries.find(
            (s) => s.display_name?.toUpperCase() === "YES"
          );
          const no = (
            metric as CategoricalDataSummaryV2
          ).category_summaries.find(
            (s) => s.display_name?.toUpperCase() === "NO"
          );

          if (!yes) {
            return [];
          }

          const yesCount = yes?.frequency || 0;
          const noCount = no?.frequency || 0;
          const totalCount = yesCount + noCount;
          const percentage =
            totalCount === 0 ? 0 : (yesCount / totalCount) * 100;

          return {
            label: metric.name ?? "",
            value: `${yesCount}/${totalCount} (${percentage.toFixed(1)}%)`,
            metric,
          };
        }

        if (metric.metric_type === "mean" || metric.metric_type === "sum") {
          let label = metric.name ?? "";
          if (metric.metric_type === "mean") {
            label = `Average ${label}`;
          } else if (!label?.toLocaleLowerCase().startsWith("total")) {
            label = `Total ${label}`;
          }

          return {
            label,
            value: formatValueWithUnits(
              metric.name,
              (metric as MeanAndSumSummaryV2).value,
              (metric as MeanAndSumSummaryV2).units
            ),
            metric,
          };
        }

        return [];
      }),
    [metricsArray, parentIds, namesFilter]
  );
};
