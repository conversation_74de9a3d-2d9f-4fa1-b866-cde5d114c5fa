import { useQuery } from "@tanstack/react-query";
import copilotApiClient from "@legacy/clients/copilotApiClient";

export const useSubmissionBusinessMetrics = ({
  reportId,
  businessId,
  businessIds,
  restrictedParentIds,
  summaryConfigIds,
}: {
  reportId: string | undefined | null;
  businessId?: string;
  businessIds?: string[];
  restrictedParentIds?: string[];
  summaryConfigIds?: string[];
}) =>
  useQuery(
    [
      "structures-summary",
      reportId,
      businessId,
      businessIds,
      restrictedParentIds,
      summaryConfigIds,
    ],
    () =>
      reportId && (businessId || businessIds)
        ? copilotApiClient.getMetricsV2({
            id: reportId,
            getMetricsV2Request: {
              submission_business_id: businessId,
              submission_business_ids: businessIds,
              restricted_parent_ids: restrictedParentIds,
              summary_config_ids: summaryConfigIds,
            },
          })
        : Promise.reject(),
    {
      enabled: Boolean(reportId && (businessId || businessIds)),
      staleTime: 60 * 60 * 1000,
    }
  );
