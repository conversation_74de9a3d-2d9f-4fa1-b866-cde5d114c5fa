import Report, { BusinessSelectFunctionNames } from "@legacy/models/Report";
import { useDocumentsQuery } from "@queries/document";
import { DOCUMENT_TYPES, PARENT_TYPES } from "@legacy/constants/facts";
import { useMemo } from "react";
import groupBy from "lodash/groupBy";
import forEach from "lodash/forEach";
import sum from "lodash/sum";
import { FMCSAInspection } from "@legacy/api_clients/facts_api_client";
import { useFacts } from "@queries/facts";
import sumBy from "lodash/sumBy";
import RelationshipsObservation from "@legacy/models/observation/RelationshipsObservation";
import uniq from "lodash/uniq";
import { MapData } from "@features/cards/overview-map/types";
import { useStructureFacts } from "@features/reusable-queries/structures";
import { usePremisesNumberOfUnits } from "@features/reusable-queries/numberOfUnits";

const buildItemsByRegion = (items: any[], key: string) => {
  const grouped = groupBy(
    items,
    key.includes("county")
      ? (i) => `${i.premises?.state}-${i.premises?.county}`
      : key
  );
  const result: MapData = {};
  forEach(grouped, (value, key) => {
    if (key !== null && key !== undefined) {
      const ids = uniq(value.map((x) => x.business_id ?? x.id));
      result[key] = { total: ids?.length ?? 0, ids };
    }
  });
  return result;
};

export const useCrashesByState = (report: Report | undefined) => {
  const { isLoading, hasNextPage, documents } = useDocumentsQuery({
    documentType: DOCUMENT_TYPES.FMCSA_CRASH,
    parentType: PARENT_TYPES.BUSINESS,
    expand: ["body", "crash_details", "source"],
    parentIds: report?.getBusinessIdsWithActiveOwners() ?? [],
  });

  const { allCrashes, fatalities } = useMemo(() => {
    const groupedByState = groupBy(
      documents.filter((d) => !!d.state),
      "state"
    );
    const allCrashes: MapData = {};
    const fatalities: MapData = {};
    forEach(groupedByState, (value, key) => {
      if (key !== null && key !== undefined) {
        allCrashes[key] = { total: value.length };
        fatalities[key] = {
          total: sum(
            groupedByState[key].map((item) => item.number_of_fatalities ?? 0)
          ),
        };
      }
    });
    return { allCrashes, fatalities };
  }, [documents]);

  return { allCrashes, fatalities, isLoading: !!(isLoading || hasNextPage) };
};
export const useViolationsByState = (report: Report | undefined) => {
  const { documents, isLoading, hasNextPage } = useDocumentsQuery({
    documentType: DOCUMENT_TYPES.FMCSA_INSPECTION,
    parentType: PARENT_TYPES.BUSINESS,
    parentIds: report?.getBusinessIdsWithActiveOwners() ?? [],
    expand: ["body", "crash_details", "source"],
  });

  const violations = useMemo(() => {
    const seriousViolations = documents.flatMap((d) => {
      const inspection = d as FMCSAInspection;
      return inspection?.violations
        ?.filter((v) => v.is_serious_violation)
        .map((v) => {
          return { state: inspection?.state, ...v };
        });
    });
    return buildItemsByRegion(seriousViolations, "state");
  }, [documents]);

  return { violations, isLoading: !!(isLoading || hasNextPage) };
};

const premisesDataShouldBeDisplayed = (
  report: Report | undefined,
  premisesId: string
) => {
  const business = report?.getBusinessByPremiseId(premisesId);
  return business && !business.hide_property_facts;
};

export const useUnitsByKey = (report: Report | undefined, key: string) => {
  const {
    premisesFacts: { data: structures },
  } = useStructureFacts();

  const structureIds = useMemo(() => {
    return structures
      .flatMap((s) => (s.observation as RelationshipsObservation).children)
      .map((x) => x.remote_id);
  }, [structures]);

  const structureIdToPremId = useMemo(() => {
    const res: Record<string, string> = {};
    structures.forEach((s) => {
      (s.observation as RelationshipsObservation).children.forEach((c) => {
        res[c.remote_id] = s.parent_id;
      });
    });
    return res;
  }, [structures]);

  const { data: structureCounts, isLoading: isLoadingStructureCount } =
    useFacts({
      report,
      factSubtypeIds: ["NUMBER_OF_UNITS"],
      submissionId: report?.getSubmission().id,
      parentIds: structureIds,
      parentType: PARENT_TYPES.STRUCTURE,
    });

  const { data: premisesCounts, isLoading: isLoadingPremisesCount } =
    usePremisesNumberOfUnits();

  const merged = useMemo(() => {
    return [
      ...structureCounts.map((x) => ({
        premisesId: structureIdToPremId[x.parent_id],
        value: x.getValue() as number,
      })),
      ...premisesCounts.map((x) => ({
        premisesId: x.parent_id,
        value: x.getValue() as number,
      })),
    ];
  }, [structureCounts, premisesCounts, structureIdToPremId]);

  const filtered = merged.filter((x) => {
    return premisesDataShouldBeDisplayed(report, x.premisesId);
  });

  const byRegion = groupBy(filtered, (x) => {
    const business = report?.getBusinessByPremiseId(x.premisesId);
    if (key.includes("county")) {
      return `${business?.premises?.state}-${business?.premises?.county}`;
    }
    return business?.premises?.state;
  });

  const byPremise = groupBy(filtered, (x) => {
    return x.premisesId;
  });

  const numPremisesWithoutUnits = report
    ?.getPremisesIds()
    .filter((premisesId) => premisesDataShouldBeDisplayed(report, premisesId))
    .filter((premisesId) => {
      const atPremise = byPremise[premisesId];
      return !atPremise?.find((count) => count.value > 0);
    }).length;

  const res: MapData = {};

  Object.entries(byRegion).forEach(([region, facts]) => {
    const total = sumBy(facts, (x) => x.value as number);
    if (total > 0) {
      res[region] = {
        total: sumBy(facts, (x) => x.value as number),
        ids: facts.map((x) => x.premisesId),
      };
    }
  });

  return {
    unitsByRegion: res,
    numPremisesWithoutUnits: numPremisesWithoutUnits,
    isLoading: isLoadingStructureCount || isLoadingPremisesCount,
  };
};

export const useBusinessesByKey = (
  report: Report | undefined,
  key: string,
  businessSelect?: BusinessSelectFunctionNames
) => {
  const businesses = useBusinesses(report, businessSelect);

  const businessesByRegion = useMemo(() => {
    return buildItemsByRegion(businesses, key);
  }, [businesses, key]);

  return { businessesByRegion };
};

export const useBusinesses = (
  report: Report | undefined,
  businessSelect?: BusinessSelectFunctionNames
) => {
  return useMemo(() => {
    if (businessSelect) {
      const parentIds =
        report?.getParentIdsForParentType(
          PARENT_TYPES.BUSINESS,
          businessSelect
        ) ?? [];
      return report?.getBusinessesByIds(parentIds) ?? [];
    } else {
      return (
        report?.getBusinesses()?.filter((b) => !b.hide_property_facts) ?? []
      );
    }
  }, [report, businessSelect]);
};

export const getMapDataTotal = (mapData: MapData) => {
  return sumBy(Object.values(mapData ?? {}), (x) => x.total);
};
