import { UrlObject } from "url";
// eslint-disable-next-line import/no-unresolved
import { ParsedUrlQueryInput } from "node:querystring";
import { SubmissionProcessingStateEnum } from "@legacy/api_clients/copilot_api_client";

type UrlOptions = Omit<UrlObject, "pathname" | "query"> & {
  query?: ParsedUrlQueryInput;
};

const buildUrl = (
  pathname: string,
  params: Record<string, string | number> = {},
  options: UrlOptions = {}
): UrlObject => {
  return {
    pathname,
    ...options,
    query: {
      ...(options.query ?? {}),
      ...params,
    },
  };
};

export const routes = {
  hub: (
    withoutDefaultFilters?: boolean,
    queryParams: Record<string, string> = {}
  ): UrlObject =>
    buildUrl(
      "/",
      {},
      {
        query: {
          ...(withoutDefaultFilters ? {} : { applyDefaultFilters: 1 }),
          ...queryParams,
        },
      }
    ),
  genericReport: (): UrlObject => {
    return buildUrl("/report");
  },

  reportFiles: (reportId: string): UrlObject => {
    return buildUrl(
      "/report/[reportId]/all-files",
      { reportId },
      {
        query: {
          reportId,
        },
      }
    );
  },

  report: (
    reportId: string,
    navigationSource?: string,
    options?: UrlOptions
  ): UrlObject => {
    return buildUrl(
      "/report/[reportId]/overview",
      { reportId },
      {
        ...options,
        query: {
          ...options?.query,
          reportId,
          ...(navigationSource ? { navigationSource } : {}),
        },
      }
    );
  },
  reportNotes: (reportId: string): UrlObject => {
    return buildUrl("/report/[reportId]/notes", { reportId });
  },
  reportFilePreview: (reportId: string, fileId: string): UrlObject => {
    return buildUrl("/report/[reportId]/file-preview/[fileId]", {
      reportId,
      fileId,
    });
  },
  reportSection: (
    reportId: string,
    section: string | string[],
    options: UrlOptions = {}
  ): UrlObject => {
    return buildUrl(
      "/report/[reportId]/[...section]",
      { reportId },
      {
        ...options,
        query: {
          ...(options.query ?? {}),
          section,
        },
      }
    );
  },
  reportAttachments: (reportId: string, options?: UrlOptions): UrlObject =>
    buildUrl(
      `/v2/flight-control/[reportId]/attachments`,
      { reportId },
      options
    ),
  reportAttachment: (
    reportId: string,
    attachmentId: string,
    options?: UrlOptions
  ): UrlObject =>
    buildUrl(
      `/v2/flight-control/[reportId]/attachments/[attachmentId]`,
      { reportId, attachmentId },
      options
    ),
  portfolioManager: {
    hub: (): UrlObject => {
      return buildUrl("/portfolio-manager");
    },
    details: (id: string): UrlObject => {
      return buildUrl("/portfolio-manager/[id]", { id });
    },
    edit: (id: string): UrlObject => {
      return buildUrl("/portfolio-manager/[id]/edit", { id });
    },
    create: (): UrlObject => {
      return buildUrl("/portfolio-manager/new-guideline");
    },
  },
  managementDashboard: (
    type: "overview" | "exposures" | "document" = "overview"
  ): UrlObject => {
    return buildUrl(`/management/${type}`);
  },
  emailClassification: (): UrlObject => {
    return buildUrl("/email-classification");
  },
  pds: {
    toAnyStep: (reportId: string): UrlObject => {
      return buildUrl("/pds/[reportId]", {
        reportId,
      });
    },
    toStep: (
      reportId: string,
      processingState: SubmissionProcessingStateEnum
    ): UrlObject => {
      return buildUrl("/pds/[reportId]/" + (processingState ?? ""), {
        reportId,
      });
    },
    toDebugger: (reportId: string): UrlObject => {
      return buildUrl("/pds/[reportId]/debugger", { reportId });
    },
  },
  quote: (reportId: string): UrlObject => {
    return buildUrl("/report/[reportId]/quote", { reportId });
  },
  rate: (reportId: string): UrlObject => {
    return buildUrl("/report/[reportId]/rate", { reportId });
  },
  renewals: {
    index: (reportId: string): UrlObject => {
      return buildUrl("/report/[reportId]/renewals", { reportId });
    },
  },
  factsGroupEditor: (): UrlObject => {
    return buildUrl("/fact-group-editor");
  },
  classifiers: (): UrlObject => {
    return buildUrl("/classifiers-metadata");
  },
  documentIngestionReview: (reportId: string, fileId: string): UrlObject => {
    return buildUrl("/ingestion/[reportId]/[fileId]/review", {
      reportId,
      fileId,
    });
  },
  pdsSingleFileReview: (reportId: string, fileId: string): UrlObject => {
    return buildUrl("/pds/[reportId]/file/[fileId]", { reportId, fileId });
  },
  fileTypes: (): UrlObject => {
    return buildUrl("/file-types");
  },
  subtypesPhraseMatching: (): UrlObject => {
    return buildUrl("/subtypes-phrase-matching");
  },
  subtypesBenchmarkTestCases: (): UrlObject => {
    return buildUrl("/subtypes-benchmark-test-cases");
  },
  submissionQueue: (): UrlObject => {
    return buildUrl("/submission-queue");
  },
  exportTemplates: {
    list: (): UrlObject => {
      return buildUrl("/export-templates");
    },
    edit: (id: string): UrlObject => {
      return buildUrl("/export-templates/[id]/edit", { id });
    },
    create: (): UrlObject => {
      return buildUrl("/export-templates/new");
    },
  },
};
