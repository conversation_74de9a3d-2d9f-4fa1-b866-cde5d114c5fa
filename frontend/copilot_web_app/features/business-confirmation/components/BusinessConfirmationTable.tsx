import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  Checkbox,
  FormControlLabel,
  ListItem,
  Typography,
  useTheme,
} from "@mui/material";
import {
  DataGridPro,
  GRID_DETAIL_PANEL_TOGGLE_FIELD,
  GridRenderCellParams,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import { CardTableColumn } from "@ui-patterns/card-table";
import React, { useEffect, useMemo, useState } from "react";
import { useSubmissionBusinessResolutionDataQuery } from "@queries/files";
import some from "lodash/some";
import { ModelFileFileTypeEnum } from "@legacy/api_clients/copilot_api_client";
import { FileDownloadButton } from "@features/files/components/FileDownloadButton";
import { generateUUID } from "@legacy/helpers/numberHelpers";
import { extendConfigWithExperimentData } from "@features/experiments/business-confirmation";
import { useExperimentsStore } from "@features/experiments/state";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { EVENT_BUS } from "@features/events";
import { TrackingEventType } from "@features/tracking/types";
import { FilePreviewButton } from "@features/files/components/FilePreviewButton";
import {
  BusinessResolutionDataRow,
  TableRow,
} from "@features/business-confirmation/types";
import { BusinessConfirmationBottomBar } from "@features/business-confirmation/components/BusinessConfirmationBottomBar";
import { useReport } from "@utils/useReport";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import CircleIcon from "@mui/icons-material/Circle";
import { ConfirmationPanel } from "@features/business-confirmation/components/ConfirmationPanel";
import { DebouncedSearchField } from "@ui-patterns/card-table/components/DebouncedSearchField";
import { AddressColumn } from "@features/business-confirmation/components/columns/AddressColumn";
import { NamedInsuredDropdown } from "@features/business-confirmation/components/NamedInsuredDropdown";
import { RoleDropdown } from "@features/business-confirmation/components/RoleDropdown";
import { useUpdateEntity } from "@features/business-confirmation/hooks/useUpdateEntity";

export const BusinessConfirmationTable = () => {
  const report = useReport();
  const theme = useTheme();
  const { showExperiments } = useFeatureFlags();
  const gridApiRef = useGridApiRef();
  const [searchTerm, setSearchTerm] = useState<string>();
  const loadedEventSent = React.useRef(false);
  const [hidden, setHidden] = useState(false);

  const [showOnlyMultipleConfirmed, setShowOnlyMultipleConfirmed] =
    useState(false);
  const {
    data: files = [],
    isLoading: isLoadingResolutionData,
    isFetching,
  } = useSubmissionBusinessResolutionDataQuery(
    report.getSubmission().id,
    "WAITING_FOR_BUSINESS_CONFIRMATION"
  );

  const { isCSManager } = useAccessPermissions();
  const experiments = useExperimentsStore((state) => state.experiments);

  const businessResolutionDataById: Record<
    string,
    BusinessResolutionDataRow[]
  > = useMemo(() => {
    const byId: Record<string, BusinessResolutionDataRow[]> = {};
    files
      // @ts-expect-error fix this
      .filter((f) => !!f.business_resolution_data?.resolution_data)
      .forEach((f) => {
        byId[f.id ?? ""] = (
          f.business_resolution_data as any
        )?.resolution_data.map((x: any) => {
          return { id: generateUUID(), ...x };
        }) as BusinessResolutionDataRow[];
      });
    return byId;
  }, [files]);

  const rows: TableRow[] = useMemo(() => {
    const rows = Object.entries(businessResolutionDataById)
      .map(([k, v]) =>
        v.map((b) => {
          return {
            ...b,
            file_id: k,
            addressForSearch: `${b.resolved_address ?? ""} ${
              b.requested_address ?? ""
            }`,
          };
        })
      )
      .flat();
    experiments.forEach((e) => extendConfigWithExperimentData(e, rows));
    return rows;
  }, [businessResolutionDataById, experiments]);

  const requestedAddressToResolvedMap = useMemo(() => {
    const result: Record<
      string,
      { resolvedAddress: Set<string>; notAutoConfirmedCount: number }
    > = {};
    rows.forEach((row) => {
      if (!row.requested_address) {
        return;
      }

      if (!result[row.requested_address]) {
        result[row.requested_address] = {
          resolvedAddress: new Set(),
          notAutoConfirmedCount: 0,
        };
      }

      if (row.resolved_address) {
        result[row.requested_address].resolvedAddress.add(row.resolved_address);
        if (!row.is_autoconfirmed) {
          result[row.requested_address].notAutoConfirmedCount += 1;
        }
      }
    });

    return result;
  }, [rows]);

  useEffect(() => {
    if (!isLoadingResolutionData && !isFetching && !loadedEventSent.current) {
      EVENT_BUS.emit("tracking/action/generic", {
        name: "business confirmation data loaded",
        props: {
          numberOfEntities: rows.length,
        },
      });
      loadedEventSent.current = true;
    }
  }, [isFetching, isLoadingResolutionData, rows.length]);

  const filteredRows = useMemo(() => {
    let data = rows;
    if (showOnlyMultipleConfirmed) {
      data = rows.filter(
        (row) =>
          (requestedAddressToResolvedMap[row.requested_address]?.resolvedAddress
            .size ?? 0) > 1
      );
    }
    const lowerSearchTerm = searchTerm?.toLowerCase();
    return data
      .filter((row) => {
        if (lowerSearchTerm) {
          return [
            row.resolved_name,
            row.requested_name,
            row.resolved_address,
            row.requested_address,
          ]
            .filter((s): s is string => Boolean(s))
            .map((x) => x.toLowerCase())
            .some((s) => s.includes(lowerSearchTerm));
        }
        return true;
      })
      .sort((a, b) => {
        const constructSortArray = (
          row: BusinessResolutionDataRow
        ): (boolean | string)[] => [
          Boolean(row.entity_id),
          requestedAddressToResolvedMap[row.requested_address]
            ? requestedAddressToResolvedMap[row.requested_address]
                .resolvedAddress.size <= 1
            : true,
          row.is_autoconfirmed ?? true,
          row.named_insured !== "FIRST_NAMED_INSURED",
          row.submission_entity_type !== "General Contractor" &&
            row.entity_role !== "GENERAL_CONTRACTOR",
          row.submission_entity_type !== "Project" &&
            row.entity_role !== "PROJECT",
          row.resolved_name ?? "",
          row.requested_name ?? "",
        ];

        const aSortArray = constructSortArray(a);
        const bSortArray = constructSortArray(b);
        for (let i = 0; i < aSortArray.length; i++) {
          const aValue = aSortArray[i];
          const bValue = bSortArray[i];
          if (aValue !== bValue) {
            if (typeof aValue === "boolean") {
              return aValue ? 1 : -1;
            } else {
              return (aValue as string).localeCompare(bValue as string);
            }
          }
        }
        return 0;
      });
  }, [
    rows,
    showOnlyMultipleConfirmed,
    searchTerm,
    requestedAddressToResolvedMap,
  ]);

  const shouldDisplayRequestedAddressAlert = useMemo(() => {
    return Object.values(requestedAddressToResolvedMap).some(
      (resolvedAddresses) =>
        resolvedAddresses.resolvedAddress.size > 1 &&
        resolvedAddresses.notAutoConfirmedCount > 0
    );
  }, [requestedAddressToResolvedMap]);

  useEffect(() => {
    if (shouldDisplayRequestedAddressAlert) {
      EVENT_BUS.emit("tracking/action", {
        type: TrackingEventType.PDS_VALIDATION_ERRORS,
        message:
          "There is at least one instance where the requested address was confirmed to multiple addresses.",
      });
    }
  }, [shouldDisplayRequestedAddressAlert]);

  const updateEntity = useUpdateEntity(businessResolutionDataById);

  const columns: CardTableColumn<BusinessResolutionDataRow>[] = useMemo(
    () => [
      {
        headerName: "Name",
        field: "requested_name",
        flex: 1,
        minWidth: 300,
        valueGetter: ({ row }) =>
          row.requested_name
            ? `${row.resolved_name} (${row.requested_name})`
            : row.resolved_name,
        renderCell: ({
          row,
        }: GridRenderCellParams<any, BusinessResolutionDataRow, any>) => {
          return (
            <Typography
              variant="body2"
              sx={{
                wordBreak: "break-word",
                textWrap: "wrap",
                minWidth: 0,
                overflow: "hidden",
              }}
            >
              {row.resolved_name &&
              row.requested_name &&
              row.resolved_name !== row.requested_name
                ? `${row.resolved_name} (${row.requested_name})`
                : row.resolved_name || row.requested_name}
            </Typography>
          );
        },
      },
      {
        headerName: "Address",
        field: "addressForSearch",
        flex: 1,
        minWidth: 300,
        valueGetter: ({ row }) => row.resolved_address || row.requested_address,
        renderCell: ({
          row,
        }: GridRenderCellParams<any, BusinessResolutionDataRow, any>) => (
          <AddressColumn row={row} />
        ),
      },
      {
        headerName: "Address Source",
        field: "file_id",
        flex: 0.75,
        minWidth: 200,
        valueGetter: ({ row }) => {
          const brdFile = (files ?? []).find((file) => file.id === row.file_id);
          const file = report
            .getSubmission()
            .files.find((f) => brdFile?.file_id === f.id);
          return file?.name;
        },
        renderCell: ({
          row,
        }: GridRenderCellParams<any, BusinessResolutionDataRow, any>) => {
          const brdFile = (files ?? []).find((file) => file.id === row.file_id);
          const file = report
            .getSubmission()
            .files.find((f) => brdFile?.file_id === f.id);
          return file ? (
            <Box
              display="flex"
              flexDirection="row"
              alignItems="center"
              minWidth={0}
              overflow="hidden"
            >
              <Typography
                variant="body2"
                sx={{
                  wordBreak: "break-word",
                  textWrap: "wrap",
                  minWidth: 0,
                  overflow: "hidden",
                }}
              >
                {file.name}
              </Typography>
              <FilePreviewButton file={file} />
              {file.file_type === ModelFileFileTypeEnum.Sov && (
                <FileDownloadButton file={file} />
              )}
            </Box>
          ) : null;
        },
      },
      {
        headerName: "",
        field: GRID_DETAIL_PANEL_TOGGLE_FIELD,
        minWidth: 200,
        sortable: false,
        renderCell: ({ row }) => {
          const allowToEdit = isCSManager || !row.is_autoconfirmed;
          return (
            <Box
              alignItems="center"
              display="flex"
              pl={2}
              sx={
                showExperiments && row.experiment
                  ? {
                      backgroundColor: "lightblue",
                    }
                  : undefined
              }
            >
              {!row.is_autoconfirmed &&
                requestedAddressToResolvedMap[row.requested_address]
                  ?.resolvedAddress.size > 1 && <CircleIcon color="error" />}
              {!row.entity_id && <CircleIcon color="warning" />}
              <Button
                size="small"
                sx={{ color: theme.colors.grayText }}
                disabled={!allowToEdit}
                onClick={() => {
                  EVENT_BUS.emit("tracking/action/generic", {
                    name: "business confirmation confirm location clicked",
                    props: {
                      locationsWithSameAddress:
                        requestedAddressToResolvedMap[row.requested_address]
                          ?.resolvedAddress.size ?? 0,
                    },
                  });
                }}
              >
                {row.is_autoconfirmed &&
                  row.entity_id &&
                  `Auto-confirmed ${allowToEdit ? "(Edit)" : ""}`}
                {!row.is_autoconfirmed &&
                  (row.entity_id ? "Edit" : "Confirm location")}
              </Button>
            </Box>
          );
        },
      },
      {
        headerName: "Named Insured",
        field: "named_insured",
        width: 200,
        renderCell: ({ row }) => (
          <NamedInsuredDropdown bdr={row} updateHandler={updateEntity} />
        ),
      },
      {
        headerName: "Role",
        field: "entity_role",
        width: 200,
        renderCell: ({ row }) => (
          <RoleDropdown bdr={row} updateHandler={updateEntity} />
        ),
      },
    ],
    [
      files,
      report,
      isCSManager,
      showExperiments,
      requestedAddressToResolvedMap,
      theme.colors.grayText,
      updateEntity,
    ]
  );

  const getBusinessSearch = ({ row }: { row: BusinessResolutionDataRow }) => {
    if (!row || (!!row.is_autoconfirmed && !isCSManager)) {
      return null;
    }
    return (
      <ConfirmationPanel
        row={row}
        businessResolutionDataById={businessResolutionDataById}
        allRows={rows}
        gridApi={gridApiRef.current}
      />
    );
  };

  const isLoading = isLoadingResolutionData || isFetching;

  return (
    <Box display="flex" flexDirection="column" minHeight={0} flex="1 1 0">
      {shouldDisplayRequestedAddressAlert && !hidden && (
        <Alert
          onClose={() => setHidden(true)}
          severity="error"
          sx={{
            maxHeight: 150,
            py: 0,
            "& > .MuiAlert-message": { width: "100%" },
          }}
        >
          <AlertTitle sx={{ m: 0 }}>
            There is at least one instance where the requested address was
            confirmed to multiple addresses. Please correct and confirm to the
            correct address:
          </AlertTitle>
          {Object.entries(requestedAddressToResolvedMap).flatMap(
            ([requestedAddress, resolvedAddresses]) => {
              if (resolvedAddresses.resolvedAddress.size <= 1) {
                return [];
              }

              return (
                <ListItem sx={{ display: "list-item", p: 0 }}>
                  The requested address ({requestedAddress}) was confirmed to
                  multiple addresses:{" "}
                  {Array.from(resolvedAddresses.resolvedAddress).join("; ")}
                </ListItem>
              );
            }
          )}
        </Alert>
      )}

      <Box display="flex" flexDirection="row" justifyContent="space-between">
        <FormControlLabel
          label="Display locations where the requested address was confirmed to multiple addresses"
          disabled={
            !shouldDisplayRequestedAddressAlert && !showOnlyMultipleConfirmed
          }
          control={
            <Checkbox
              checked={showOnlyMultipleConfirmed}
              onChange={() => {
                setShowOnlyMultipleConfirmed((value) => !value);
                if (!showOnlyMultipleConfirmed) {
                  EVENT_BUS.emit("tracking/action/generic", {
                    name: "business confirmation multiple locations filtered",
                    props: {
                      numberOfInvalid: rows.filter(
                        (row) =>
                          (requestedAddressToResolvedMap[row.requested_address]
                            ?.resolvedAddress.size ?? 0) > 1
                      ).length,
                    },
                  });
                }
              }}
            />
          }
        />
        <Typography variant="body1" sx={{ alignSelf: "center" }}>
          <strong>Total Entities: {rows.length}</strong>
        </Typography>
        <DebouncedSearchField
          initialValue=""
          onChange={setSearchTerm}
          placeholder="Search..."
        />
      </Box>
      <DataGridPro
        columns={columns}
        rows={filteredRows}
        apiRef={gridApiRef}
        getDetailPanelContent={getBusinessSearch}
        loading={isLoading}
        checkboxSelection={false}
        getRowHeight={() => "auto"}
        disableRowSelectionOnClick
        pagination
        pageSizeOptions={[25, 50, 100]}
        initialState={{
          pagination: {
            paginationModel: { pageSize: 25 },
          },
        }}
        sx={{
          "& .MuiDataGrid-main": {
            border: `1px solid ${theme.palette.grey[200]}`,
            borderRadius: 1,
          },
          "& .MuiDataGrid-columnHeaders": {
            backgroundColor: theme.palette.grey[100],
            borderBottom: `1px solid ${theme.palette.grey[200]}`,
          },
          "& .MuiDataGrid-columnHeaderTitle": {
            textTransform: "uppercase",
          },
          "& .MuiDataGrid-row": {
            borderBottom: `2px solid ${theme.palette.grey[300]}`,
          },
          "& .MuiDataGrid-cell > *": {
            py: 1,
          },
        }}
      />
      <BusinessConfirmationBottomBar
        allowConfirm={!some(rows.filter((r) => !r.entity_id))}
        isLoading={isLoading}
      />
    </Box>
  );
};
