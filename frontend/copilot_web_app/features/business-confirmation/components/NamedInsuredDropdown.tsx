import React from "react";
import {
  BusinessResolutionDataRow,
  UpdateEntityHandler,
} from "@features/business-confirmation/types";
import { SelectWithLabel } from "@ui-patterns/form-components/SelectWithLabel";
import { SubmissionBusinessNamedInsuredEnum } from "@legacy/api_clients/copilot_api_client";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const OPTIONS = [
  {
    value: SubmissionBusinessNamedInsuredEnum.FirstNamedInsured,
    label: "First Named Insured",
  },
  {
    value: SubmissionBusinessNamedInsuredEnum.OtherNamedInsured,
    label: "Other Named Insured",
  },
  {
    value: "-",
    label: "-",
  },
];

type NamedInsuredDropdownProps = {
  bdr: BusinessResolutionDataRow;
  updateHandler: UpdateEntityHandler;
};

export const NamedInsuredDropdown = ({
  bdr,
  updateHandler,
}: NamedInsuredDropdownProps) => {
  const { isCSManager: canEdit } = useAccessPermissions();
  return (
    <SelectWithLabel
      label=""
      options={OPTIONS}
      value={bdr.named_insured ?? "-"}
      disabled={!canEdit}
      onChange={(event) => {
        const value = event.target.value;
        let newEntityValue:
          | SubmissionBusinessNamedInsuredEnum
          | null
          | undefined;
        if (value === "-" || !value) {
          newEntityValue = null;
        } else {
          newEntityValue = value as SubmissionBusinessNamedInsuredEnum;
        }
        if (newEntityValue !== bdr.named_insured) {
          updateHandler(bdr, {
            named_insured: newEntityValue as SubmissionBusinessNamedInsuredEnum,
          });
        }
      }}
      size="small"
    />
  );
};
