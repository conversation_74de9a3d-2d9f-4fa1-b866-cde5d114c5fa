import React from "react";
import { SelectWithLabel } from "@ui-patterns/form-components/SelectWithLabel";
import {
  BusinessResolutionDataRow,
  UpdateEntityHandler,
} from "@features/business-confirmation/types";
import { SubmissionEntityEntityRoleEnum } from "@legacy/api_clients/copilot_api_client";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const OPTIONS = [
  {
    value: SubmissionEntityEntityRoleEnum.GeneralContractor,
    label: "General Contractor",
  },
  {
    value: SubmissionEntityEntityRoleEnum.Project,
    label: "Project",
  },
  {
    value: "-",
    label: "-",
  },
];

type RoleDropdownProps = {
  bdr: BusinessResolutionDataRow;
  updateHandler: UpdateEntityHandler;
};

export const RoleDropdown = ({ bdr, updateHandler }: RoleDropdownProps) => {
  const { isCSManager: canEdit } = useAccessPermissions();
  return (
    <SelectWithLabel
      label=""
      options={OPTIONS}
      value={bdr.entity_role ?? "-"}
      disabled={!canEdit}
      onChange={(event) => {
        const value = event.target.value;
        let newEntityValue: SubmissionEntityEntityRoleEnum | null | undefined;
        if (value === "-" || !value) {
          newEntityValue = null;
        } else {
          newEntityValue = value as SubmissionEntityEntityRoleEnum;
        }
        if (newEntityValue !== bdr.entity_role) {
          updateHandler(bdr, {
            entity_role: newEntityValue as SubmissionEntityEntityRoleEnum,
          });
        }
      }}
      size="small"
    />
  );
};
