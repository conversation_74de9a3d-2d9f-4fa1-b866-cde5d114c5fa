import { Alert, Box, Typography, useTheme } from "@mui/material";
import React, { useMemo } from "react";
import { BusinessConfirmationTable } from "@features/business-confirmation/components/BusinessConfirmationTable";
import { ModelFileFileTypeEnum } from "@legacy/api_clients/copilot_api_client";
import { ExperimentStarter } from "@features/experiments/components/ExperimentStarter";
import { RunningExperimentsInfo } from "@features/experiments/components/RunningExperimentsInfo";
import { useReport } from "@utils/useReport";
import { BcInfoMessage } from "@features/business-confirmation/components/BcInfoMessage";

export const BusinessConfirmation = () => {
  const [hidden, setHidden] = React.useState(false);
  const report = useReport();
  const theme = useTheme();

  const errorMessage = useMemo(() => {
    const sov = report
      .getSubmission()
      .files?.find((f) => f.file_type === ModelFileFileTypeEnum.Sov);
    if ((sov?.issues ?? []).length > 0) {
      return "The SOV does not seem to have ingested properly. Please review it and ensure all locations, vehicles, drivers (and accompanying data points) were ingested correctly, where applicable.";
    }
    return null;
  }, [report]);

  return (
    <Box
      sx={{
        mt: 2,
        display: "flex",
        flexDirection: "column",
        flex: "1 1 0",
        mb: 8,
        overflowY: "hidden",
      }}
    >
      <RunningExperimentsInfo />
      <BcInfoMessage />
      {errorMessage && !hidden && (
        <Alert severity="error" onClose={() => setHidden(true)}>
          <Typography
            sx={{
              color: theme.palette.error.main,
              fontWeight: "bold",
            }}
          >
            {errorMessage}
          </Typography>
        </Alert>
      )}
      <ExperimentStarter type="business_confirmation">
          <BusinessConfirmationTable />
      </ExperimentStarter>
    </Box>
  );
};
