import { CardTable } from "@ui-patterns/card-table";
import React, { useState } from "react";
import { useGetSubmissionLevelExtractedDataByReportIdQuery } from "@queries/pdsDebugger";
import { GridColDef } from "@mui/x-data-grid/models/colDef/gridColDef";
import {
  JsonDialog,
  safeJsonParse,
} from "@features/pds-debugger/ProcessedFileDataTable";
import { CircularProgress, Typography } from "@mui/material";

type rowType = {
  field: string;
  value: string;
  file_id: string;
  source_details: string;
  generation_method?: string;
};

export const SubmissionLevelExtractedDataTable = ({
  reportId,
}: {
  reportId: string;
}) => {
  const [selectedRow, setSelectedRow] = useState<rowType | null>(null);

  const { data, isLoading } =
    useGetSubmissionLevelExtractedDataByReportIdQuery(reportId);

  const columns: GridColDef[] = [
    { field: "field", headerName: "Field", flex: 1 },
    { field: "value", headerName: "Value", flex: 1 },
    { field: "file_id", headerName: "File ID", flex: 1 },
    { field: "source_details", headerName: "Source Details", flex: 1 },
    { field: "generation_method", headerName: "Generation Method", flex: 1 },
  ];

  return (
    <>
      {isLoading ? (
        <CircularProgress />
      ) : (
        <CardTable
          rows={
            data?.map((row) => ({
              ...row,
              generation_method: row?.generation_method ?? "N/A",
            })) ?? []
          }
          trackingTableType="sub level extracted data table"
          columns={columns}
          autoHeight
          defaultPageSize={50}
          allowSearch
          onRowClick={(params) => {
            setSelectedRow({
              ...params.row,
              value: safeJsonParse(params.row.value),
            });
          }}
          title={
            <Typography variant="h5">
              Submission Level Extracted Data{" "}
            </Typography>
          }
        />
      )}
      {selectedRow && (
        <JsonDialog
          title="Row JSON"
          json={selectedRow}
          onClose={() => {
            setSelectedRow(null);
          }}
          maxWidth="xl"
        />
      )}
    </>
  );
};
