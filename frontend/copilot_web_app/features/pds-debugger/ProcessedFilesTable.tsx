import { useGetPdsDebuggerFilesDataQuery } from "@queries/pdsDebugger";
import { GridColDef } from "@mui/x-data-grid/models/colDef/gridColDef";
import { CardTable } from "@ui-patterns/card-table";
import React, { useState } from "react";
import { PdsDebuggerFileData } from "@legacy/api_clients/copilot_api_client";
import { Dialog } from "@ui-patterns/dialog";
import { ProcessedFileDataTable } from "@features/pds-debugger/ProcessedFileDataTable";
import { CircularProgress, Typography } from "@mui/material";

export const ProcessedFilesTable = ({ reportId }: { reportId: string }) => {
  const [selectedFile, setSelectedFile] = useState<PdsDebuggerFileData | null>(
    null
  );
  const { data: pdsDebuggerFilesData, isLoading } =
    useGetPdsDebuggerFilesDataQuery(reportId);

  const columns: GridColDef[] = [
    { field: "file_id", headerName: "File ID", flex: 1 },
    { field: "name", headerName: "File Name", flex: 1 },
    { field: "file_type", headerName: "File Type", flex: 1 },
    {
      field: "hasRawProcessedData",
      headerName: "Has Raw Processed Data",
      width: 200,
    },
    { field: "hasProcessedData", headerName: "Has Processed Data", width: 200 },
    { field: "hasEmData", headerName: "Has EM Data", width: 200 },
    { field: "hasDoData", headerName: "Has DO Data", width: 200 },
  ];

  return (
    <>
      {isLoading ? (
        <CircularProgress />
      ) : (
        <CardTable
          rows={
            pdsDebuggerFilesData?.map((file) => ({
              ...file,
              hasRawProcessedData: !!file.raw_processed_data,
              hasProcessedData: !!file.processed_data,
              hasEmData: !!file.entity_mapped_data,
              hasDoData: !!file.onboarded_data,
            })) ?? []
          }
          trackingTableType="pds debufer files"
          columns={columns}
          autoHeight
          defaultPageSize={50}
          allowSearch
          onRowClick={(params) => {
            setSelectedFile(params.row as PdsDebuggerFileData);
          }}
          title={
            <Typography variant="h5">Processed Submission Files </Typography>
          }
        />
      )}
      {selectedFile && (
        <Dialog
          title={selectedFile?.name}
          onClose={() => {
            setSelectedFile(null);
          }}
          maxWidth="xl"
          fullWidth
          hidePrimary
        >
          <ProcessedFileDataTable file={selectedFile} />
        </Dialog>
      )}
    </>
  );
};
