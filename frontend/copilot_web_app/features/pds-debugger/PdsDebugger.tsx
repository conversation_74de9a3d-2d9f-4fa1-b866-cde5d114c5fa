import { useRouter } from "next/router";
import { ProcessedFilesTable } from "@features/pds-debugger/ProcessedFilesTable";
import { SyntheticEvent, useState } from "react";
import { SubmissionLevelExtractedDataTable } from "@features/pds-debugger/SubmissionLevelExtractedDataTable";
import { Tabs, Tab } from "@mui/material";
import { useInternalUsersOnlyPage } from "@utils/auth";

enum TabName {
  PROCESSED_FILES = "Processed Files",
  SUBMISSION_EXTRACTED_DATA = "Submission Extracted Data",
}

export const PdsDebugger = () => {
  useInternalUsersOnlyPage();

  const router = useRouter();
  const { reportId } = router.query as { reportId: string };
  const [selectedTab, setSelectedTab] = useState<TabName>(
    TabName.PROCESSED_FILES
  );

  const handleTabChange = (event: SyntheticEvent, newValue: TabName) => {
    setSelectedTab(newValue);
  };
  return (
    <>
      <Tabs value={selectedTab} onChange={handleTabChange} variant="fullWidth">
        {Object.values(TabName).map((tabName) => (
          <Tab
            label={tabName}
            key={tabName}
            value={tabName}
            sx={{ fontSize: "1.2rem", fontWeight: "bold" }}
          />
        ))}
      </Tabs>
      {selectedTab === TabName.PROCESSED_FILES && (
        <ProcessedFilesTable reportId={reportId} />
      )}
      {selectedTab === TabName.SUBMISSION_EXTRACTED_DATA && (
        <SubmissionLevelExtractedDataTable reportId={reportId} />
      )}
    </>
  );
};
