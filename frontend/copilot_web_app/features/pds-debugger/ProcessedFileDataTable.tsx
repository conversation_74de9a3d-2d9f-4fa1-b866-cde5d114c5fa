import { PdsDebuggerFileData } from "@legacy/api_clients/copilot_api_client";
import React, { useMemo, useState } from "react";
import {
  SelectOption,
  SelectWithLabel,
} from "@ui-patterns/form-components/SelectWithLabel";
import { GridColDef } from "@mui/x-data-grid/models/colDef/gridColDef";
import { CardTable } from "@ui-patterns/card-table";
import { uuid4 } from "@sentry/utils";
import { Button, Typography } from "@mui/material";
import { Dialog } from "@ui-patterns/dialog";

const dataTypeOptions: SelectOption[] = [
  { value: "PROCESSED_DATA", label: "PROCESSED_DATA" },
  { value: "RAW_PROCESSED_DATA", label: "RAW_PROCESSED_DATA" },
  { value: "DO_DATA", label: "DO_DATA" },
  { value: "EM_DATA", label: "EM_DATA" },
];

const dataTypeKeyMap: Record<string, keyof PdsDebuggerFileData> = {
  PROCESSED_DATA: "processed_data",
  RAW_PROCESSED_DATA: "raw_processed_data",
  DO_DATA: "onboarded_data",
  EM_DATA: "entity_mapped_data",
};

type rowType = {
  id: string;
  fact_subtype_id: string;
  name: string;
  observed_name: string;
  observed_values: string;
  normalized_values: string;
  matcher_type: string;
  matching_explanation: string;
};

export const safeJsonParse = (jsonString: string, maxDepth = 5) => {
  let result: any = jsonString;
  let depth = 0;

  try {
    while (typeof result === "string" && depth < maxDepth) {
      result = JSON.parse(result);
      depth++;
    }
  } catch (e) {}
  return result;
};

export const JsonDialog = ({
  title,
  json,
  onClose,
  maxWidth = "lg",
}: {
  title: string;
  json: any;
  onClose: () => void;
  maxWidth?: "sm" | "md" | "lg" | "xl";
}) => {
  return (
    <Dialog
      title={title}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth
      hidePrimary
    >
      <Typography
        component="pre"
        sx={{ whiteSpace: "pre-wrap", fontFamily: "monospace" }}
      >
        {JSON.stringify(json, null, 2)}
      </Typography>
    </Dialog>
  );
};

const extractData = (data: any[]): rowType[] => {
  return data.map(
    (field: any): rowType => ({
      id: uuid4().toString(),
      name: field.name ?? "-",
      observed_name: field.observed_name ?? "-",
      fact_subtype_id: field.fact_subtype_id ?? "-",
      observed_values:
        (field.values?.map((val: any) => val.observed_value) ?? []).join(
          ", "
        ) ?? "-",
      normalized_values:
        (field.values?.map((val: any) => val.value) ?? []).join(", ") ?? "-",
      matcher_type: field.matching_metadata?.matcher_type ?? "-",
      matching_explanation:
        JSON.stringify(
          field.matching_metadata?.matching_explanation,
          null,
          2
        ) ?? "-",
    })
  );
};

export const ProcessedFileDataTable = ({
  file,
}: {
  file: PdsDebuggerFileData;
}) => {
  const [dataType, setDataType] = useState<string>("PROCESSED_DATA");
  const [showJsonModal, setShowJsonModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);

  const columns: GridColDef[] = [
    { field: "fact_subtype_id", headerName: "Fact Subtype ID", flex: 0.5 },
    { field: "name", headerName: "Field Name", flex: 0.5 },
    { field: "observed_name", headerName: "Observed Name", flex: 0.5 },
    {
      field: "observed_values",
      headerName: "Observed Values",
      flex: 1,
    },
    {
      field: "normalized_values",
      headerName: "Normalized Values",
      flex: 1,
    },
    {
      field: "matcher_type",
      headerName: "Matcher Type",
      flex: 0.5,
    },
    {
      field: "matching_explanation",
      headerName: "Matching Explanation",
      flex: 1,
    },
  ];
  const rows: rowType[] = useMemo(() => {
    const section = file[dataTypeKeyMap[dataType]];
    if (typeof section !== "object" || section === null) {
      return [];
    }
    let data: rowType[] = [];
    if ("fields" in section && Array.isArray(section.fields)) {
      data = extractData(section.fields);
    }
    if (
      "entity_information" in section &&
      Array.isArray(section.entity_information)
    ) {
      data = [...data, ...extractData(section.entity_information)];
    }
    return data;
  }, [file, dataType]);

  return (
    <>
      <CardTable
        rows={rows}
        trackingTableType="Processed File Data"
        columns={columns}
        autoHeight
        defaultPageSize={50}
        allowSearch
        onRowClick={(params) => {
          setSelectedRow({
            ...params.row,
            observed_values: safeJsonParse(params.row.observed_values),
            normalized_values: safeJsonParse(params.row.normalized_values),
            matching_explanation: safeJsonParse(
              params.row.matching_explanation
            ),
          });
        }}
        title={
          <>
            <SelectWithLabel
              id="selected-processed-file-data-type"
              label="Data Type"
              options={dataTypeOptions}
              value={dataType}
              onChange={(event) => setDataType(event.target.value as string)}
              sx={{ width: 200 }}
            />
            <Button
              variant="contained"
              size="large"
              onClick={() => setShowJsonModal(true)}
            >
              See Data as JSON
            </Button>
          </>
        }
      />
      {showJsonModal && (
        <JsonDialog
          title={dataType + " Of " + file.name}
          json={file}
          onClose={() => setShowJsonModal(false)}
        />
      )}
      {selectedRow && (
        <JsonDialog
          title={selectedRow.name}
          json={selectedRow}
          onClose={() => {
            setSelectedRow(null);
          }}
        />
      )}
    </>
  );
};
