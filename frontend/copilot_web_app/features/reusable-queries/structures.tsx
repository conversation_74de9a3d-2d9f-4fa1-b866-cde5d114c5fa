import { useFacts } from "@queries/facts";
import { PARENT_TYPES } from "@legacy/constants/facts";
import { useReport } from "@utils/useReport";
import Report from "@legacy/models/Report";

export const useStructureFacts = (enabled = true, reportToUse?: Report) => {
  const contextReport = useReport() as Report | undefined;
  const report = reportToUse ?? contextReport;
  const premisesFacts = useFacts({
    factSubtypeIds: ["STRUCTURES"],
    parentIds: report?.getPremisesIdsForPropertyData() ?? [],
    parentType: PARENT_TYPES.PREMISES,
    lightweightResponse: true,
    submissionId: report?.getSubmission()?.id,
    doLargeChunks: true,
    enabled: enabled,
  });
  const businessFacts = useFacts({
    factSubtypeIds: ["STRUCTURES"],
    parentIds: report?.getBusinessIdsForPropertyData() ?? [],
    parentType: PARENT_TYPES.BUSINESS,
    lightweightResponse: true,
    submissionId: report?.getSubmission()?.id,
    doLargeChunks: true,
    enabled: enabled,
  });
  return { premisesFacts, businessFacts };
};
