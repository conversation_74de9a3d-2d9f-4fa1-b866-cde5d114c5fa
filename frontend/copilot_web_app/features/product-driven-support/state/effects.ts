import Submission from "@legacy/models/Submission";
import { useClearLastConfirmedStep, useSubmissionLastConfirmedState } from ".";
import { useEffect } from "react";
import { useRouter } from "next/router";
import { useIsPdsDebugPage } from "@features/pds-data-model/debug/PdsDebugPageContext";

export const useClearConfirmStepEffect = (
  submission: Submission | undefined
) => {
  const submissionConfirmState = useSubmissionLastConfirmedState();
  const clearConfirmStep = useClearLastConfirmedStep();
  const processingState = submission?.processing_state;
  const isProcessing = submission?.isProcessing();

  useEffect(() => {
    if (
      processingState &&
      submissionConfirmState &&
      submissionConfirmState !== processingState
    ) {
      clearConfirmStep();
    }
  }, [processingState, submissionConfirmState, clearConfirmStep]);

  useEffect(() => {
    if (!isProcessing) {
      clearConfirmStep();
    }
  }, [isProcessing, clearConfirmStep]);
};

export const useLeavePageWarning = (
  isSubLoading: boolean,
  allowBrowseAway = false
) => {
  const router = useRouter();
  const isPdsDebug = useIsPdsDebugPage();
  const submissionConfirmState = useSubmissionLastConfirmedState();

  useEffect(() => {
    if (submissionConfirmState || isSubLoading || isPdsDebug) {
      return;
    }

    const warningText =
      "Are you sure you want to leave this page? All progress will be lost";

    const handleWindowClose = (event: BeforeUnloadEvent) => {
      event.preventDefault();
      event.returnValue = warningText;
    };

    const handleBrowseAway = (url: string) => {
      if (allowBrowseAway) {
        return;
      }
      if (
        url === "/?applyDefaultFilters=1" ||
        url.startsWith("/pds") ||
        url.startsWith("/report") ||
        window.confirm(warningText)
      ) {
        return;
      }
      router.events.emit("routeChangeError");
      throw "routeChange aborted.";
    };

    window.addEventListener("beforeunload", handleWindowClose);
    router.events.on("routeChangeStart", handleBrowseAway);

    return () => {
      window.removeEventListener("beforeunload", handleWindowClose);
      router.events.off("routeChangeStart", handleBrowseAway);
    };
  }, [
    isSubLoading,
    router,
    submissionConfirmState,
    isPdsDebug,
    allowBrowseAway,
  ]);
};
