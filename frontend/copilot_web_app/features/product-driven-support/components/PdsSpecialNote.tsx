import { useState } from "react";
import {
  Box,
  InputAdornment,
  TextField,
  TextFieldProps,
  Typography,
} from "@mui/material";
import { useUpdateSubmission } from "@queries/report";
import Report from "@legacy/models/Report";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { theme } from "@ui-patterns/theme";

type Props = {
  report: Report;
};

export const PdsSpecialNote = ({ report }: Props) => {
  const [specialNote, setSpecialNote] = useState(
    () => report.getSubmission().pds_special_note ?? ""
  );
  const { mutate: updateSubmission, isLoading } = useUpdateSubmission();
  const { isCSManager } = useAccessPermissions();

  const handleNoteUpdate = () => {
    const reportSpecialNote = report.getSubmission().pds_special_note ?? "";
    if (specialNote !== reportSpecialNote) {
      updateSubmission({
        submissionId: report.getSubmission().id,
        reportId: report.id,
        data: {
          pds_special_note: specialNote === "" ? null : specialNote,
        },
      });
    }
  };

  return (
    <Box py={1} flexGrow={1}>
      <HighlightedTextField
        label="Special Instruction / Notes"
        value={specialNote}
        onChange={(event) => setSpecialNote(event.target.value)}
        onBlur={handleNoteUpdate}
        disabled={!isCSManager || isLoading}
        multiline
        fullWidth
      />
    </Box>
  );
};

const HighlightedTextField = ({ value, ...props }: TextFieldProps) => {
  const textFieldStyles = {
    "& .MuiOutlinedInput-root": {
      fontWeight: "bold",
      bgcolor: value ? "#64B6F730" : "white",
      borderColor: value ? theme.colors.chart.blue5 : theme.colors.darkBlue,
      "& .MuiOutlinedInput-notchedOutline": {
        borderColor: value ? theme.colors.chart.blue5 : theme.colors.darkBlue,
        borderWidth: "2px",
      },
      "&.Mui-focused": {
        "& .MuiOutlinedInput-notchedOutline": {
          borderColor: value ? theme.colors.chart.blue5 : theme.colors.darkBlue,
          borderWidth: "2px",
        },
      },
      "& .MuiInputLabel-outlined": {
        fontWeight: "bold",
        "&.Mui-focused": {
          borderColor: value ? theme.colors.chart.blue5 : theme.colors.darkBlue,
          fontWeight: "bold",
        },
      },
    },
  };
  return (
    <TextField
      variant="outlined"
      sx={textFieldStyles}
      value={value}
      InputProps={{
        endAdornment: value ? (
          <InputAdornment position="end">
            <Typography
              variant="h5"
              fontWeight="bold"
              color={theme.colors.chart.blue5}
            >
              !
            </Typography>
          </InputAdornment>
        ) : undefined,
      }}
      {...props}
    />
  );
};
