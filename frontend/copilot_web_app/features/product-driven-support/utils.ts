import { useGetCurrentSupportUser } from "@queries/submissionAssignment";
import { useMaybeCurrentUser } from "@utils/auth";

export const useAccessPermissions = () => {
  const user = useMaybeCurrentUser();
  const { data: supportUser } = useGetCurrentSupportUser();

  const isCSManager = user?.applicable_settings?.is_cs_manager;
  const isTier2Specialist = supportUser?.is_tier_2;

  return {
    isTier2: <PERSON><PERSON><PERSON>(isTier2Specialist),
    isAtLeastTier2: <PERSON><PERSON><PERSON>(isCSManager || isTier2Specialist),
    isCSManager: <PERSON><PERSON><PERSON>(isCSManager),
  };
};
