import { actionNameMap } from "@features/guideline-editor/actions/ActionTypePicker";
import {
  createActivationRuleErrorMessage,
  useSelectedRulesStore,
} from "@features/guideline-manager/rules/utils";
import { CellText } from "@features/item-list";
import { routes } from "@features/routes";
import {
  ActionTypeEnum,
  RuleResponse,
  TextActionResponse,
} from "@legacy/api_clients/recommendations-client";
import { formatDate, toSentenceCase } from "@legacy/helpers/reportHelpers";
import ArrowRightIcon from "@mui/icons-material/ArrowCircleRightOutlined";
import {
  Checkbox,
  IconButton,
  Switch,
  TableCell,
  TableRow as MuiTableRow,
} from "@mui/material";
import { useUpdateRuleMutation } from "@queries/recommendationsV2";
import { formatRecommendationValue } from "@utils/text";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import uniq from "lodash/uniq";
import Link from "next/link";
import { useSnackbar } from "notistack";
import { useRuleValidation } from "./useRuleValidation";

const ActionsValue = ({ rule }: Props) => {
  const actions = uniq(
    Object.entries(rule)
      .filter(
        ([key, val]) =>
          key.endsWith("_actions") && Array.isArray(val) && val.length > 0
      )
      .flatMap(([, val]) => {
        return (val as Pick<TextActionResponse, "action_type">[]).map(
          (x) => x.action_type
        );
      })
  )
    .map((x: ActionTypeEnum) => actionNameMap[x])
    .filter(Boolean);

  return (
    <>
      {actions.map((x) => (
        <CellText key={x}>{x}</CellText>
      ))}
    </>
  );
};

type Props = {
  rule: RuleResponse;
};

export const TableRow = ({ rule }: Props) => {
  const { mutate: updateRule, isLoading } = useUpdateRuleMutation(rule.id);
  const { enqueueSnackbar } = useSnackbar();
  const { showPortfolioOptimization } = useFeatureFlags();

  const { unselectRule, selectRule, selectedRules } = useSelectedRulesStore();
  const isSelected = selectedRules.includes(rule.id);

  const handleChangeActive = (isActive: boolean) => {
    updateRule(
      { is_active: isActive },
      {
        onSuccess: () => {
          enqueueSnackbar("Rule updated successfully");
        },
        onError: (error) => {
          enqueueSnackbar(createActivationRuleErrorMessage(isActive, error), {
            variant: "error",
          });
        },
      }
    );
  };

  const ruleHref = routes.portfolioManager.details(rule.id);

  const validations = useRuleValidation(
    rule.rule_type,
    rule.condition_groups,
    rule.sources,
    rule.text_actions
  );

  const formattedRecommendationValue = formatRecommendationValue(
    rule.recommendation_actions?.[0]?.value,
    "-"
  );
  const relativeRanking = rule.recommendation_actions?.[0]?.relative_ranking;
  return (
    <MuiTableRow
      hover
      title={rule.name}
      sx={{
        "& .MuiTableCell-root": { verticalAlign: "top" },
        "&& *": { border: "none" },
      }}
    >
      <TableCell
        padding="none"
        sx={{ "&&": { paddingTop: 1 } }}
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          color="primary"
          checked={isSelected}
          onChange={() =>
            isSelected ? unselectRule(rule.id) : selectRule(rule.id)
          }
        />
      </TableCell>

      <Link href={ruleHref} legacyBehavior>
        <TableCell sx={{ cursor: "pointer" }}>
          <CellText>{rule.name}</CellText>
        </TableCell>
      </Link>

      <TableCell sx={{ minWidth: "140px" }}>
        <Switch
          checked={rule.is_active ?? false}
          disabled={isLoading || (validations.length > 0 && !rule.is_active)}
          size="small"
          color="success"
          onChange={(e) => handleChangeActive(e.target.checked)}
        />
      </TableCell>
      <TableCell sx={{ minWidth: "140px" }}>
        <ActionsValue rule={rule} />
      </TableCell>
      <TableCell sx={{ minWidth: "140px" }}>
        <CellText>{formattedRecommendationValue}</CellText>
      </TableCell>
      {showPortfolioOptimization && (
        <TableCell sx={{ minWidth: "140px" }}>
          <CellText>{toSentenceCase(rule.rule_type)}</CellText>
        </TableCell>
      )}
      <TableCell sx={{ minWidth: "140px" }}>
        <CellText>{relativeRanking ? relativeRanking : "-"}</CellText>
      </TableCell>
      <TableCell sx={{ minWidth: "140px" }}>
        <CellText>{formatDate(rule.expired_at, undefined, "-")}</CellText>
      </TableCell>
      <TableCell sx={{ minWidth: "140px" }}>
        <CellText>{formatDate(rule.created_at)}</CellText>
      </TableCell>
      <TableCell sx={{ minWidth: "170px" }}>
        <CellText>{rule.number_of_invocations}</CellText>
      </TableCell>
      <TableCell>
        <Link passHref href={ruleHref} legacyBehavior>
          <IconButton sx={{ color: "neutral.300" }}>
            <ArrowRightIcon />
          </IconButton>
        </Link>
      </TableCell>
    </MuiTableRow>
  );
};
