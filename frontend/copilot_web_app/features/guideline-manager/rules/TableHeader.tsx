import { TableCell, TableHead } from "@mui/material";
import React, { type JSX } from "react";

import { Header<PERSON>ell } from "@ui-patterns/table/HeaderCell";
import {
  TABLE_ACTIVE_ORDER_OPTIONS,
  TABLE_ALPHABETICAL_ORDER_OPTIONS,
  TABLE_DATE_ORDER_OPTIONS,
  TABLE_NUMERICAL_ORDER_OPTIONS,
  TABLE_RECOMMENDATION_ORDER_OPTIONS,
  TableHeaderOption,
  TableSortOptions,
} from "@ui-patterns/table/constants";
import {
  CheckboxDropdown,
  CheckboxDropdownState,
} from "@ui-patterns/checkbox-dropdown";
import {
  HeaderRow,
  TABLE_SELECT_OPTIONS,
  TableSelectOptions,
} from "@features/item-list";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";

export enum RulesSortableFields {
  NAME = "NAME",
  ACTIVE_STATUS = "ACTIVE_STATUS",
  RECOMMENDATION = "RECOMMENDATION",
  RELATIVE_RANKING = "RELATIVE_RANKING",
  EXPIRATION_DATE = "EXPIRATION_DATE",
  DATE_CREATED = "DATE_CREATED",
  AFFECTED_SUBMISSIONS = "AFFECTED_SUBMISSIONS",
  RULE_TYPE = "RULE_TYPE",
}

type Props = {
  sortBy?: RulesSortableFields | null;
  descending?: boolean;
  onSortChange: (
    sortBy: RulesSortableFields | undefined,
    descending: boolean
  ) => void;
  state: CheckboxDropdownState;
  onSelect: (option: { value: TableSelectOptions }) => void;
};

export const TableHeader = ({
  sortBy,
  descending,
  onSortChange,
  state,
  onSelect,
}: Props): JSX.Element => {
  const { showPortfolioOptimization } = useFeatureFlags();
  const calcCurrentSort = (type: RulesSortableFields) => {
    if (type === sortBy) {
      return descending ? "desc" : "asc";
    }
    return "none";
  };
  const onSortChangeHandler =
    (type: RulesSortableFields) => (option: TableHeaderOption | null) => {
      if (!option) {
        // @ts-expect-error fix this
        onSortChange(null, false);
      } else if (option.value === TableSortOptions.ASC) {
        onSortChange(type, false);
      } else {
        onSortChange(type, true);
      }
    };

  return (
    <TableHead sx={{ height: "62px" }}>
      <HeaderRow>
        <TableCell
          padding="none"
          sx={{ "&&": { p: 1, pr: 0, bgcolor: "mainGrayBackground" } }}
          width={40}
        >
          <CheckboxDropdown
            items={TABLE_SELECT_OPTIONS}
            onSelect={onSelect}
            state={state}
          />
        </TableCell>
        <HeaderCell
          title="RULE NAME"
          sortOptions={TABLE_ALPHABETICAL_ORDER_OPTIONS}
          currentSort={calcCurrentSort(RulesSortableFields.NAME)}
          onSortChange={onSortChangeHandler(RulesSortableFields.NAME)}
        />
        <HeaderCell
          title="ACTIVE STATUS"
          sortOptions={TABLE_ACTIVE_ORDER_OPTIONS}
          currentSort={calcCurrentSort(RulesSortableFields.ACTIVE_STATUS)}
          onSortChange={onSortChangeHandler(RulesSortableFields.ACTIVE_STATUS)}
        />
        <HeaderCell title="ACTIONS" />
        <HeaderCell
          title="RECOMMENDATION"
          sortOptions={TABLE_RECOMMENDATION_ORDER_OPTIONS}
          currentSort={calcCurrentSort(RulesSortableFields.RECOMMENDATION)}
          onSortChange={onSortChangeHandler(RulesSortableFields.RECOMMENDATION)}
        />
        {showPortfolioOptimization && (
          <HeaderCell
            title="RULE TYPE"
            sortOptions={TABLE_ALPHABETICAL_ORDER_OPTIONS}
            currentSort={calcCurrentSort(RulesSortableFields.RULE_TYPE)}
            onSortChange={onSortChangeHandler(RulesSortableFields.RULE_TYPE)}
          />
        )}
        <HeaderCell
          title="RELATIVE RANKING"
          sortOptions={TABLE_NUMERICAL_ORDER_OPTIONS}
          currentSort={calcCurrentSort(RulesSortableFields.RELATIVE_RANKING)}
          onSortChange={onSortChangeHandler(
            RulesSortableFields.RELATIVE_RANKING
          )}
        />
        <HeaderCell
          title="EXPIRATION DATE"
          sortOptions={TABLE_DATE_ORDER_OPTIONS}
          currentSort={calcCurrentSort(RulesSortableFields.EXPIRATION_DATE)}
          onSortChange={onSortChangeHandler(
            RulesSortableFields.EXPIRATION_DATE
          )}
        />
        <HeaderCell
          title="DATE CREATED"
          sortOptions={TABLE_DATE_ORDER_OPTIONS}
          currentSort={calcCurrentSort(RulesSortableFields.DATE_CREATED)}
          onSortChange={onSortChangeHandler(RulesSortableFields.DATE_CREATED)}
        />
        <HeaderCell
          title="AFFECTED SUBMISSIONS"
          sortOptions={TABLE_NUMERICAL_ORDER_OPTIONS}
          currentSort={calcCurrentSort(
            RulesSortableFields.AFFECTED_SUBMISSIONS
          )}
          onSortChange={onSortChangeHandler(
            RulesSortableFields.AFFECTED_SUBMISSIONS
          )}
        />
        <HeaderCell title="" />
      </HeaderRow>
    </TableHead>
  );
};
