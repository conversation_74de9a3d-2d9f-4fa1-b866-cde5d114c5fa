import React, { type JSX } from "react";
import { Box, useTheme } from "@mui/material";
import { VtHeader } from "@features/entity-mapping-v2/components/VerticalTable/VtHeader";
import { AddNewValueAction } from "@features/entity-mapping-v2/components/VerticalTable/AddNewValueAction";
import { EntityTypeIndicator } from "@features/entity-mapping-v2/components/VerticalTable/EntityTypeIndicator";
import { PdsInput } from "@features/pds-entity-table/input/PdsInput";
import { CopyToAnotherEntityAction } from "@features/entity-mapping-v2/components/VerticalTable/CopyToAnotherEntityAction";
import { MoveToAnotherEntityAction } from "@features/entity-mapping-v2/components/VerticalTable/MoveToAnotherEntityAction";
import { PdsEntityTableConfigContextProvider } from "@features/pds-entity-table/context/PdsEntityTableConfigContext";
import { useOnboardingDataContext } from "@features/pds-data-model/context/OnboardingDataContextData";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { ReviewTab } from "@features/entity-mapping-v2/EntityMappingV2State";
import { SubmissionEntityTypeEnum } from "@legacy/api_clients/copilot_api_client";
import LocationOnIcon from "@mui/icons-material/LocationOnOutlined";
import { OutlinedMail } from "@ui-patterns/icons/outlined-mail";
import { useAccessPermissions } from "@features/product-driven-support/utils";

type VerticalTableUiProps = {
  fieldsInOrder: string[];
  tab: ReviewTab;
  stickyRows?: number;
  disableStickyFields?: boolean;
  isLockedTable?: boolean;
  showFieldValueOnlyForCurrentFile?: boolean;
  extraInputAction?: React.ReactNode;
  blockCopySwapActions?: boolean;
  allowValueRemoval?: boolean;
  allowDateFormatter?: boolean;
};

const indexToClassName: Record<number, string> = {
  0: "sticky-first-row",
  1: "sticky-second-row",
  2: "sticky-third-row",
};

const fieldIcons: Record<string, JSX.Element> = {
  businessma: <OutlinedMail />,
  address: <LocationOnIcon />,
};

const getFieldIcon = (fieldId: string): JSX.Element | undefined => {
  const keys = Object.keys(fieldIcons);
  const key = keys.find((key) => fieldId.includes(key));
  return key ? fieldIcons[key] : undefined;
};

export const VerticalTableUi = React.memo(
  ({
    fieldsInOrder,
    tab,
    stickyRows,
    disableStickyFields = false,
    isLockedTable = false,
    blockCopySwapActions = false,
    showFieldValueOnlyForCurrentFile,
    extraInputAction,
    allowValueRemoval = false,
    allowDateFormatter = false,
  }: VerticalTableUiProps) => {
    const activeFileId = useOnboardingDataContext().use.activeFileId();
    const { pdsEmTransactions } = useFeatureFlags();
    const { isCSManager } = useAccessPermissions();
    const theme = useTheme();

    const renderRow = (
      feFieldId: string,
      showEntityTypeIndicator = false,
      type?: "name" | "address",
      className?: string,
      icon?: JSX.Element,
      showCopyTo = false
    ) => {
      let extraActions: React.ReactNode = null;
      if (isLockedTable && !isCSManager) {
        // keep extraActions null
      } else if (tab.feEntityId) {
        if (!blockCopySwapActions) {
          if (showCopyTo) {
            extraActions = (
              <CopyToAnotherEntityAction
                entityId={tab.feEntityId}
                fieldId={feFieldId}
                type={type!}
              />
            );
          } else {
            extraActions = (
              <MoveToAnotherEntityAction
                entityId={tab.feEntityId}
                fieldId={feFieldId}
              />
            );
          }
        }
      }
      if (extraInputAction) {
        extraActions = (
          <>
            {extraInputAction}
            {extraActions}
          </>
        );
      }
      return (
        <Box
          key={feFieldId}
          display="contents"
          sx={{
            "&:hover": {
              ".visible-on-hover-pds": {
                visibility: "visible",
              },
            },
          }}
        >
          <VtHeader
            feFieldId={feFieldId}
            className={className}
            allowEditableFieldNames={pdsEmTransactions}
            icon={icon}
          />
          <Box
            className={className}
            sx={{
              position: "relative",
            }}
          >
            {tab.feEntityId &&
              !showCopyTo &&
              (!isLockedTable || isCSManager) && (
                <AddNewValueAction
                  entityId={tab.feEntityId}
                  fieldId={feFieldId}
                  activeFileId={activeFileId!}
                />
              )}
            {showEntityTypeIndicator && tab.feEntityId && (
              <EntityTypeIndicator entityId={tab.feEntityId} />
            )}
            <PdsInput
              fieldId={feFieldId}
              entityId={tab.feEntityId!}
              extraAction={extraActions}
            />
          </Box>
        </Box>
      );
    };

    return (
      <PdsEntityTableConfigContextProvider
        showFieldValueOnlyForCurrentFile={showFieldValueOnlyForCurrentFile}
        showValuesWithNullSourceFileId
        allowedEntityTypes={[SubmissionEntityTypeEnum.Business]}
        allowForValueRemoval={allowValueRemoval}
        allowDateFormatter={allowDateFormatter}
      >
        <Box
          key={tab.feEntityId}
          data-testid="vertical-table"
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            backgroundColor: "white",
            background: theme.palette.grey[100],
            border: `1px solid ${theme.palette.divider}`,
            minHeight: 0,
            "& > * > *": {
              border: `1px solid ${theme.palette.divider}`,
            },
            "& .sticky-first-row": {
              position: "sticky",
              zIndex: 1,
              backgroundColor: theme.palette.grey[200],
              top: 0,
              height: 80,
            },
            "& .sticky-second-row": {
              position: "sticky",
              zIndex: 1,
              backgroundColor: theme.palette.grey[200],
              top: 80,
              height: 80,
            },
            "& .sticky-third-row": {
              position: "sticky",
              zIndex: 1,
              backgroundColor: theme.palette.grey[200],
              top: 160,
              height: 80,
            },
          }}
        >
          {!disableStickyFields &&
            fieldsInOrder
              .slice(0, stickyRows)
              .map((feFieldId, index) =>
                renderRow(
                  feFieldId,
                  index === 0,
                  index === 0 ? "name" : "address",
                  indexToClassName[index],
                  getFieldIcon(feFieldId),
                  true
                )
              )}
          {fieldsInOrder
            .slice(disableStickyFields ? 0 : stickyRows)
            .map((feFieldId) => renderRow(feFieldId))}
        </Box>
      </PdsEntityTableConfigContextProvider>
    );
  }
);
