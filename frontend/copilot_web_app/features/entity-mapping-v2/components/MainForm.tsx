import { useReport } from "@utils/useReport";
import React, { useMemo, useRef, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { FeOnboardedFile } from "@features/pds-data-model/types";
import { useExperimentsStore } from "@features/experiments/state";
import { useOnboardingDataContext } from "@features/pds-data-model/context/OnboardingDataContextData";
import { useFinancialFactsContext } from "@features/data-onboarding/FinancialFactsContext";
import { useFactSubtypeContext } from "@features/entity-mapping/context/FactSubtypeContext";
import { useIsDataOnboardingFinancialSplit } from "@features/data-onboarding/useIsDataOnboardingFinancialSplit";
import { useUpdateEntityMappingMutation } from "@queries/files";
import { useMemoArray } from "@utils/useMemoArray";
import { BeToFeMappingContext } from "@features/pds-data-model/converters/types";
import { useConfirmLastStep } from "@features/product-driven-support/state";
import { useSnackbar } from "notistack";
import { mapFeOnboardedFileToBe } from "@features/pds-data-model/converters/fe-to-be";
import { parseErrorFromApi } from "@utils/error";
import { sentryCaptureException } from "@legacy/services/sentryService";
import { useOnStuckStore } from "@features/pds-stuck-dialog/context/onStuckAction";
import { NewOnboardingDataFormProvider } from "@features/pds-data-model/form/NewOnboardingDataFormProvider";
import { BottomDrawer } from "@features/entity-mapping-v2/components/BottomDrawer";
import { EntityMappingV2ContextProvider } from "@features/entity-mapping-v2/EntityMappingV2State";
import { StateResolver } from "@features/entity-mapping-v2/StateResolver";
import { EmFilePreview } from "@features/entity-mapping-v2/components/EmFilePreview";
import { VerticalTable } from "@features/entity-mapping-v2/components/VerticalTable";
import { ActionBar } from "@features/entity-mapping-v2/components/ActionBar";
import { Resizer } from "@features/entity-mapping-v2/components/Resizer";
import { SubmissionEntityTypeEnum } from "@legacy/api_clients/copilot_api_client";
import { LockedFieldsTracker } from "@features/entity-mapping-v2/LockedFieldsTracker";
import { PdsVerificationModals } from "@features/add-document-modal/pds-verification";
import { VerificationResultWithIntent } from "@features/add-document-modal/pds-verification/types";

export const MainForm = () => {
  const report = useReport();
  const [saveResult, setSaveResult] = useState<VerificationResultWithIntent>();
  const formMethods = useRef<UseFormReturn<FeOnboardedFile> | null>(null);
  const submissionId = report?.getSubmission()?.id;
  const experiments = useExperimentsStore.use.experiments();
  const onboardedFile = useOnboardingDataContext().use.onboardedFile();
  const {
    factSubtypeIdsInOrder: financialFactSubtypes,
    snapshotFactSubtypeIds,
  } = useFinancialFactsContext();
  const { factSubtypesMap } = useFactSubtypeContext();
  const isSplittedDo = useIsDataOnboardingFinancialSplit();
  const { mutateAsync, isLoading } = useUpdateEntityMappingMutation(
    submissionId,
    report.id
  );

  const allReportFileIds = useMemoArray(
    report.submission.files.map((f) => f.id)
  );
  const mappingContext = useMemo((): BeToFeMappingContext => {
    return {
      experiments,
      submissionId,
      allReportFileIds,
      skipLoadingFactSubtypes: [
        ...(isSplittedDo ? financialFactSubtypes : []),
        "OPERATIONS",
      ],
      snapshotTimeSeriesFactSubtypes: snapshotFactSubtypeIds,
      factSubtypesMap,
      doSimplifiedTimeSeriesLoading: !isSplittedDo,
      skipAddingEmptyValues: true,
      whitelistedEntityTypes: new Set([
        SubmissionEntityTypeEnum.Business,
        SubmissionEntityTypeEnum.Transaction,
      ]),
      readOnlyFieldMatcher: () => {
        return false;
      },
    };
  }, [
    experiments,
    submissionId,
    allReportFileIds,
    isSplittedDo,
    financialFactSubtypes,
    snapshotFactSubtypeIds,
    factSubtypesMap,
  ]);

  const confirmLastStep = useConfirmLastStep();
  const { enqueueSnackbar } = useSnackbar();
  const onSuccessfulConfirm = async () => {
    if (!saveResult?.isOnlySaveData) {
      confirmLastStep(report.submission);
    }
    setSaveResult(undefined);
  };
  const onSave = async (onlySaveData: boolean, withForce = false) => {
    const currentFormData = formMethods.current?.getValues();
    if (!currentFormData) {
      return;
    }
    const toSaveData = await mapFeOnboardedFileToBe(
      currentFormData,
      onboardedFile,
      mappingContext
    );
    const result = await mutateAsync(
      {
        filePatch: {
          force: withForce,
          submissionId,
          onlySaveData,
          onboardedDataUpdate: {
            data: toSaveData,
            confirmed_files: toSaveData.files,
          },
        },
      },
      {
        onError: (error) => {
          const errorData = parseErrorFromApi(error);
          if (!errorData) {
            enqueueSnackbar("Error encountered when confirming.", {
              variant: "error",
              persist: true,
            });
          } else {
            const isStaleDataError =
              (errorData.detailMessage?.indexOf("data is stale") ?? -1) >= 0;
            // When stale data error occurs it means that set of files dedicated for EM
            // has changed in the background - in such case we do not know if specialist just
            // onboarded invalid data or did not review some new data from incoming files
            // In either case, unfortunately the only way to fix it is to refresh the page
            enqueueSnackbar(
              `Error encountered when confirming. ${errorData.detailMessage} ${
                isStaleDataError
                  ? " - Unfortunately, to fix this issue you need to refresh the page and review this step again."
                  : ""
              }`,
              {
                variant: "error",
                persist: true,
              }
            );
            if (errorData.statusCode >= 400 && errorData.statusCode < 500) {
              // 4xx errors are client errors, so we should report that to Sentry
              sentryCaptureException(error);
            }
          }
        },
      }
    );
    setSaveResult({ ...result, isOnlySaveData: onlySaveData });
  };

  const onStuckStore = useOnStuckStore();
  onStuckStore?.use.updateOnStuck()(async () => onSave(true));

  return (
    <NewOnboardingDataFormProvider
      context={mappingContext}
      formMethodsRef={formMethods}
      allowZeroEntites
    >
      <EntityMappingV2ContextProvider>
        <>
          <StateResolver />
          <LockedFieldsTracker />
          <ActionBar />

            <Resizer
              leftChild={<VerticalTable showEvidences />}
              rightChild={<EmFilePreview />}
            />

          <BottomDrawer
            onConfirm={() => onSave(false)}
            isLoading={isLoading}
            onSave={() => onSave(true)}
          />
          <PdsVerificationModals
            result={saveResult}
            isLoading={isLoading}
            onForceConfirm={() => onSave(false, true)}
            onSuccess={() => onSuccessfulConfirm()}
            onClose={() => setSaveResult(undefined)}
          />
        </>
      </EntityMappingV2ContextProvider>
    </NewOnboardingDataFormProvider>
  );
};
