import {
  Box,
  Button,
  Chip,
  CircularProgress,
  Divider,
  FormControl,
  FormControlLabel,
  IconButton,
  ListItemIcon,
  Menu,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  SxProps,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { styled } from "@mui/system";
import Link from "next/link";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import FileCopyOutlinedIcon from "@mui/icons-material/FileCopyOutlined";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import KeyboardBackspaceIcon from "@mui/icons-material/KeyboardBackspace";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import TextSnippetOutlinedIcon from "@mui/icons-material/TextSnippetOutlined";
import { ExternalLink } from "@ui-patterns/icons/external-link";
import { useState } from "react";
import { FormProvider, useForm, useFormContext } from "react-hook-form";
import { transformerServiceClient } from "@legacy/clients/transformerServiceClient";
import {
  ExportDTO,
  ExportDefinitionStatus,
} from "@legacy/api_clients/report-transformer-service-client";
import { LabeledField } from "@features/submission-information-section/components/ui/LabeledField";
import capitalize from "lodash/capitalize";
import { useRouter } from "next/router";
import { routes } from "@features/routes";
import {
  ExportTemplateContentType,
  ExportTemplateFormData,
  JsonAsFileExportTemplateFormData,
  JsonExportTemplateOutputType,
  PdfExportTemplateFormData,
} from "./types";
import { useOrganizationId } from "@utils/auth";
import { enqueueSnackbar } from "notistack";
import { useExportTemplates } from "@queries/export_templates";

const buttonFontSizeStyle = { fontSize: "1rem" };

const StatusBadge = ({ status }: { status: ExportDefinitionStatus }) => {
  const theme = useTheme();
  const color =
    status === ExportDefinitionStatus.Published
      ? theme.palette.text.success
      : theme.palette.brand.text.default;
  const bgColor =
    status === ExportDefinitionStatus.Published
      ? "#E0F2F1"
      : theme.palette.background.surfaceHovered;
  return (
    <Chip
      variant="outlined"
      label={capitalize(status)}
      sx={{ backgroundColor: bgColor, borderColor: bgColor, color }}
    />
  );
};

const NewTemplateButton = ({
  label,
  isOutlined,
}: {
  label: string;
  isOutlined?: boolean;
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const router = useRouter();

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const startCreatingTemplate = (type: "pdf" | "json") => {
    router.push({ pathname: `${router.asPath}/create`, query: { type } });
  };

  return (
    <div>
      <Button
        variant={isOutlined ? "outlined" : "text"}
        endIcon={anchorEl ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        onClick={handleMenuOpen}
        sx={buttonFontSizeStyle}
      >
        {label}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        slotProps={{
          list: { sx: { width: anchorEl && anchorEl.offsetWidth } },
        }}
      >
        <MenuItem onClick={() => startCreatingTemplate("json")}>JSON</MenuItem>
        <MenuItem onClick={() => startCreatingTemplate("pdf")}>PDF</MenuItem>
      </Menu>
    </div>
  );
};

const EmptyPage = () => {
  const theme = useTheme();
  return (
    <Table>
      <TableCell
        sx={{
          alignItems: "center",
          border: `1px solid ${theme.palette.neutrals.border}`,
          borderRadius: "4px",
          display: "flex",
          flexDirection: "column",
          gap: 1,
        }}
      >
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          height="72px"
          width="72px"
          sx={{
            backgroundColor: theme.palette.neutrals.border,
            borderRadius: "100%",
          }}
        >
          <TextSnippetOutlinedIcon
            sx={{
              color: theme.palette.brand.text.default,
              height: "36px",
              width: "36px",
            }}
          />
        </Box>
        <Box
          sx={{
            color: theme.palette.brand.text.default,
            fontWeight: "500",
            fontSize: "1rem",
            marginTop: 1,
          }}
        >
          No Templates Yet
        </Box>
        <Box
          sx={{
            color: theme.palette.text.subdued,
            fontSize: "1rem",
            marginBottom: 3,
          }}
        >
          Create the first template to streamline workflows.
        </Box>
        <NewTemplateButton label="Create New Template" isOutlined={true} />
      </TableCell>
    </Table>
  );
};

export const ExportTemplates = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [actionMenuId, setActionMenuId] = useState("");
  const router = useRouter();
  const { isLoading, data: exportTemplates } = useExportTemplates();

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, id: string) => {
    setAnchorEl(event.currentTarget);
    setActionMenuId(id);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
    setActionMenuId("");
  };

  const theme = useTheme();
  const borderStyle = { border: `1px solid ${theme.palette.neutrals.border}` };
  const cellStyle = { fontSize: "1rem" };
  const rowStyle = {
    ...borderStyle,
    "& .MuiTableCell-root": {
      ...cellStyle,
      backgroundColor: "white",
      textTransform: "none",
    },
  };
  const grayIconStyle = { color: theme.palette.neutral[500] };
  const StyledTableCell = styled(TableCell)(cellStyle);

  return (
    <Box sx={{ padding: 3 }}>
      <Box display="flex">
        <Typography flexGrow={1} variant="h3" marginBlock={2} color="black">
          Export Templates
        </Typography>
        <Box sx={{ marginBlock: "auto" }}>
          <NewTemplateButton label="New Template" />
        </Box>
      </Box>
      {isLoading && <CircularProgress />}
      <TableContainer>
        {!isLoading && !exportTemplates && <EmptyPage />}
        {exportTemplates && (
          <Table sx={borderStyle}>
            <TableHead>
              <TableRow sx={rowStyle}>
                <TableCell sx={{ width: "50%" }}>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Last Updated</TableCell>
                <TableCell>Last Updated by</TableCell>
                <TableCell>Status</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {exportTemplates.map((exportTemplate) => (
                <TableRow key={exportTemplate.id}>
                  <StyledTableCell
                    onClick={() =>
                      router.push(
                        routes.exportTemplates.edit(exportTemplate.id)
                      )
                    }
                    sx={{
                      color: theme.palette.brand.text.default,
                      fontWeight: 500,
                      cursor: "pointer",
                    }}
                  >
                    {exportTemplate.name}
                  </StyledTableCell>
                  <StyledTableCell>PDF</StyledTableCell>
                  <StyledTableCell>Just now</StyledTableCell>
                  <StyledTableCell>me</StyledTableCell>
                  <StyledTableCell>
                    <StatusBadge status={exportTemplate.status} />
                  </StyledTableCell>
                  <TableCell align="right">
                    <Box display="flex" gap={3} justifyContent="end">
                      <Link
                        legacyBehavior
                        passHref
                        href={routes.exportTemplates.edit(exportTemplate.id)}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <a target="_blank">
                          <IconButton>
                            <ExternalLink />
                          </IconButton>
                        </a>
                      </Link>
                      <IconButton
                        key="1"
                        onClick={(ev) => handleMenuOpen(ev, exportTemplate.id)}
                      >
                        <MoreHorizIcon sx={grayIconStyle} />
                      </IconButton>
                      <Menu
                        anchorEl={anchorEl}
                        open={
                          Boolean(anchorEl) &&
                          actionMenuId === exportTemplate.id
                        }
                        onClose={handleMenuClose}
                      >
                        <MenuItem>
                          <ListItemIcon>
                            <FileCopyOutlinedIcon sx={grayIconStyle} />
                          </ListItemIcon>
                          Duplicate
                        </MenuItem>
                        <MenuItem>
                          <ListItemIcon>
                            <FileDownloadOutlinedIcon sx={grayIconStyle} />
                          </ListItemIcon>
                          Download Sample Report
                        </MenuItem>
                        <MenuItem
                          disabled={
                            exportTemplate.status ===
                            ExportDefinitionStatus.Published
                          }
                        >
                          <ListItemIcon>
                            <DeleteOutlinedIcon sx={grayIconStyle} />
                          </ListItemIcon>
                          Delete
                        </MenuItem>
                      </Menu>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </TableContainer>
    </Box>
  );
};

const FileNameField = ({
  contentType,
  labeledFieldStyle,
}: {
  contentType: string;
  labeledFieldStyle?: SxProps;
}) => {
  const fileNameField = "fileName";
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const reportFields = [
    ["Policy Effective Date", "policy_effective_date"],
    ["Policy Number", "policy_number"],
    ["Report ID", "report_id"],
    ["Report Name", "report_name"],
    ["Submission ID", "submission_id"],
  ];
  const templateFields = [
    ["Creation Date", "creation_date"],
    ["Template Name", "template_name"],
  ];

  const { formState, register, getValues, setValue } = useFormContext<
    PdfExportTemplateFormData | JsonAsFileExportTemplateFormData
  >();
  const DynamicFieldMenuItem = ({ name, id }: { name: string; id: string }) => {
    return (
      <MenuItem
        onClick={() =>
          setValue(fileNameField, getValues(fileNameField) + `{{${id}}}`, {
            shouldValidate: true,
          })
        }
      >
        {name}
      </MenuItem>
    );
  };

  return (
    <Box display="flex" flexDirection="column">
      <LabeledField text="File Name" sx={labeledFieldStyle}>
        <TextField
          {...register("fileName", {
            required: "File Name is a required field",
          })}
          size="small"
          label={formState.errors.fileName?.message}
          error={!!formState.errors.fileName}
          fullWidth
        />
      </LabeledField>
      <Button
        onClick={handleMenuOpen}
        sx={{ ...buttonFontSizeStyle, alignSelf: "flex-end" }}
      >
        Use variables {"{ }"}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {contentType === "report" &&
          reportFields.map(([name, id]) => (
            <DynamicFieldMenuItem key={id} name={name} id={id} />
          ))}
        <Divider />
        {templateFields.map(([name, id]) => (
          <DynamicFieldMenuItem key={id} name={name} id={id} />
        ))}
      </Menu>
    </Box>
  );
};

const PdfAdditionalFields = ({
  contentType,
  labeledFieldStyle,
}: {
  contentType: string;
  labeledFieldStyle?: SxProps;
}) => {
  const { register } = useFormContext<ExportTemplateFormData>();

  return (
    <Box>
      <FileNameField
        contentType={contentType}
        labeledFieldStyle={labeledFieldStyle}
      />
      <LabeledField text="Include Report URL" sx={labeledFieldStyle}>
        <FormControl>
          <RadioGroup row>
            <FormControlLabel
              {...register("includeReportUrl")}
              control={<Radio />}
              value="yes"
              label="Yes"
            />
            <FormControlLabel
              {...register("includeReportUrl")}
              control={<Radio />}
              value="no"
              label="No"
            />
          </RadioGroup>
        </FormControl>
      </LabeledField>
    </Box>
  );
};

const JsonAdditionalFields = ({
  contentType,
  labeledFieldStyle,
}: {
  contentType: string;
  labeledFieldStyle?: SxProps;
}) => {
  const defaultOutputType = JsonExportTemplateOutputType.COPILOT_EXPORT_API;
  const [isFileOutput, setIsFileOutput] = useState(false);
  const { register, getValues } = useFormContext<ExportTemplateFormData>();
  const toggleFileNameField = () => {
    setIsFileOutput(getValues("outputType") === "file");
  };

  return (
    <Box>
      <LabeledField text="Output Type" sx={labeledFieldStyle}>
        <Select
          {...register("outputType", {
            value: defaultOutputType,
            required: true,
            onChange: toggleFileNameField,
          })}
          defaultValue={defaultOutputType}
          size="small"
          fullWidth
        >
          <MenuItem value="copilot_export_api">Copilot Export API</MenuItem>
          <MenuItem value="file">File</MenuItem>
          <MenuItem value="client_system_payload">
            Payload to Client System
          </MenuItem>
        </Select>
      </LabeledField>
      {isFileOutput && (
        <FileNameField
          contentType={contentType}
          labeledFieldStyle={labeledFieldStyle}
        />
      )}
    </Box>
  );
};

const CreateEditExportTemplateForm = ({
  exportTemplate,
  exportTemplateType,
}: {
  exportTemplate?: ExportDTO;
  exportTemplateType?: "pdf" | "json";
}) => {
  const contentType = ExportTemplateContentType.REPORT;

  const router = useRouter();
  const orgId = useOrganizationId();
  const { data: exportTemplates } = useExportTemplates();

  const labeledFieldStyle: SxProps = {
    gridTemplateColumns: "33% 67%",
    height: "44px",
  };
  const isPdf =
    exportTemplateType === "pdf" ||
    (exportTemplate && exportTemplate.name.toLowerCase().indexOf("pdf") > -1);

  const form = useForm<ExportTemplateFormData>({
    defaultValues: { contentType, templateName: exportTemplate?.name ?? "" },
    mode: "onChange",
  });
  const navigateBackToExportTemplatesList = () =>
    router.push("/export-templates");
  const save = form.handleSubmit(async (data: ExportTemplateFormData) => {
    const shouldCreate = exportTemplate === undefined;
    if (shouldCreate) {
      try {
        await transformerServiceClient.createEmptyExport({
          name: data.templateName,
          organization_id: orgId,
        });
        navigateBackToExportTemplatesList();
      } catch (err: any) {
        enqueueSnackbar(err?.message ?? "Error creating export template", {
          variant: "error",
        });
      }
    } else {
      enqueueSnackbar("Updating not yet supported", {
        variant: "error",
      });
    }
  });

  return (
    <Box sx={{ padding: 3 }}>
      <FormProvider {...form}>
        <Box display="flex" gap={1}>
          <Box sx={{ marginBlock: "auto" }}>
            <IconButton onClick={navigateBackToExportTemplatesList}>
              <KeyboardBackspaceIcon />
            </IconButton>
          </Box>
          <Typography flexGrow={1} variant="h5" marginBlock={2} color="black">
            Export Template
          </Typography>
          <Box sx={{ marginBlock: "auto" }}>
            <Button type="submit" onClick={save} sx={buttonFontSizeStyle}>
              Save and Exit
            </Button>
          </Box>
          <Box sx={{ marginBlock: "auto" }}>
            <Button
              variant="contained"
              type="submit"
              sx={buttonFontSizeStyle}
              disabled
            >
              Publish
            </Button>
          </Box>
        </Box>
        <Box sx={{ paddingInlineStart: 5.625, width: "600px" }}>
          <LabeledField text="Export Type" sx={labeledFieldStyle}>
            {exportTemplateType
              ? exportTemplateType.toUpperCase()
              : isPdf
                ? "PDF"
                : "JSON"}
          </LabeledField>
          <LabeledField text="Content Type" sx={labeledFieldStyle}>
            <Select value={contentType} size="small" fullWidth disabled>
              <MenuItem value="report">Report</MenuItem>
              <MenuItem value="broker">Broker</MenuItem>
              <MenuItem value="user">User</MenuItem>
            </Select>
          </LabeledField>
          <LabeledField text="Template Name" sx={labeledFieldStyle}>
            <TextField
              label={form.formState.errors.templateName?.message}
              error={!!form.formState.errors.templateName}
              {...form.register("templateName", {
                required: "Template Name is a required field",
                validate: (value) =>
                  value === exportTemplate?.name ||
                  !exportTemplates ||
                  !exportTemplates.map((et) => et.name).includes(value) ||
                  "Export template with that name already exists",
              })}
              size="small"
              fullWidth
            />
          </LabeledField>

          {isPdf && (
            <PdfAdditionalFields
              contentType={contentType}
              labeledFieldStyle={labeledFieldStyle}
            />
          )}
          {!isPdf && (
            <JsonAdditionalFields
              contentType={contentType}
              labeledFieldStyle={labeledFieldStyle}
            />
          )}
        </Box>
      </FormProvider>
    </Box>
  );
};

export const CreateExportTemplate = ({ type }: { type: "pdf" | "json" }) => {
  return <CreateEditExportTemplateForm exportTemplateType={type} />;
};

export const EditExportTemplate = ({ id }: { id: string }) => {
  const { data: exportTemplates, isLoading } = useExportTemplates();

  if (isLoading) return <CircularProgress />;

  const exportTemplate = exportTemplates!.find((et) => et.id === id);
  return exportTemplate ? (
    <CreateEditExportTemplateForm exportTemplate={exportTemplate} />
  ) : (
    <Box>Export template not found</Box>
  );
};
