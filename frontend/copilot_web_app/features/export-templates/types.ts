export enum ExportTemplateContentType {
  REPORT = "report",
  BROKER = "broker",
  USER = "user",
}

export enum JsonExportTemplateOutputType {
  COPILOT_EXPORT_API = "copilot_export_api",
  FILE = "file",
  CLIENT_SYSTEM_PAYLOAD = "client_system_payload",
}

interface BaseExportTemplateFormData {
  contentType: ExportTemplateContentType;
  templateName: string;
}
interface ExportTemplateAsFileFormData {
  fileName: string;
}

export type PdfExportTemplateFormData = BaseExportTemplateFormData &
  ExportTemplateAsFileFormData & { includeReportUrl: "yes" | "no" };
type JsonExportTemplateFormData = BaseExportTemplateFormData & {
  outputType: Exclude<
    JsonExportTemplateOutputType,
    JsonExportTemplateOutputType.FILE
  >;
};
export type JsonAsFileExportTemplateFormData = BaseExportTemplateFormData & {
  outputType: JsonExportTemplateOutputType.FILE;
} & ExportTemplateAsFileFormData;

export type ExportTemplateFormData =
  | PdfExportTemplateFormData
  | JsonExportTemplateFormData
  | JsonAsFileExportTemplateFormData;
