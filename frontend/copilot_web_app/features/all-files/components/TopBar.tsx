import { Box, Button, TextField, Typography } from "@mui/material";
import { ReprocessSubmissionButton } from "@features/product-driven-support/components/ReprocessSubmissionButton";
import { CancelPdsButton } from "@features/product-driven-support/components/CancelPdsButton";
import { DownloadAllButton } from "@features/files/components/DownloadAllButton";
import { Tooltip } from "@ui-patterns/tooltip";
import AddIcon from "@mui/icons-material/Add";
import React from "react";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { useStartFileProcessing } from "@features/product-driven-support/submission-processing/utils";
import { useOrganizationId } from "@utils/auth";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { useReport } from "@utils/useReport";
import { ORGANIZATION, STAGE_ID_TO_NAME } from "@utils/constants";
import { REPORT_STAGES } from "@legacy/constants/hubConstants";
import { RevertToStateButton } from "@features/product-driven-support/components/RevertToStateButton";
import { useIsNWVerifiedShell } from "@legacy/hooks/useIsNWVerifiedShell";

type TopBarProps = {
  setSearch: (value: string) => void;
  setShowUploadModal: (value: boolean) => void;
};

export const TopBar = ({ setSearch, setShowUploadModal }: TopBarProps) => {
  const { internalUseFeatures } = useFeatureFlags();
  const report = useReport();
  const orgId = useOrganizationId();
  const { isCSManager } = useAccessPermissions();
  const isAutoProcessed = report.getSubmission().is_auto_processed ?? false;
  const pdsLabel = isAutoProcessed
    ? `Auto processing is enabled (with full PDS): ${
        report.getSubmission().processing_state_label
      }`
    : "Auto processing is disabled";
  const startFileProcessing = useStartFileProcessing(report.id);
  const isNWVerifiedShell = useIsNWVerifiedShell(report);

  const isFrozenOrTerminal = report.isFrozen() || report.isTerminal();
  const isNW = orgId === ORGANIZATION.NATIONWIDE;
  const isAdmiral = orgId === ORGANIZATION.ADMIRAL;
  const isKalepa = orgId === ORGANIZATION.KALEPA_TEST;

  const canEditGeneral =
    !isFrozenOrTerminal ||
    (isNW && report.getStage() !== REPORT_STAGES.QUOTED_BOUND) ||
    ((isAdmiral || isKalepa) &&
      [REPORT_STAGES.QUOTED, REPORT_STAGES.QUOTED_BOUND].includes(
        report.getStage()
      ));
  const disableEditing = !report.hasEditPermissions();
  const canNWVerifiedShell = !isNWVerifiedShell || internalUseFeatures;
  const allowAdding = canEditGeneral && !disableEditing && canNWVerifiedShell;

  const addDocumentButtonTooltipTitle = disableEditing
    ? "Read only account cannot add documents."
    : !allowAdding
      ? `The submission is ${
          STAGE_ID_TO_NAME[report.getStage()]
        } and cannot be modified`
      : "";

  return (
    <Box
      display={"flex"}
      flexDirection={"row"}
      justifyContent={"space-between"}
      alignItems={"center"}
      width={"100%"}
    >
      <Box>
        <Typography px={1} variant="h4">
          All Files
        </Typography>
      </Box>
      <Box display={"flex"} flexDirection={"row"} alignItems={"center"}>
        {internalUseFeatures && (
          <>
            <Typography variant="body2" color="textSecondary" sx={{ mr: 1 }}>
              {pdsLabel}
            </Typography>
            {!isAutoProcessed && (
              <Button size="small" onClick={() => startFileProcessing()}>
                Turn on PDS
              </Button>
            )}
          </>
        )}
        {isCSManager &&
          report.submission.processing_state === "COMPLETED" &&
          !report.submission.is_processing && (
            <Box sx={{ mr: 1 }}>
              <RevertToStateButton toState="DATA_ONBOARDING" />
            </Box>
          )}
        {isCSManager && (
          <Box sx={{ mr: 1 }}>
            <ReprocessSubmissionButton />
          </Box>
        )}
        {isCSManager && (
          <Box sx={{ mr: 1 }}>
            <CancelPdsButton />
          </Box>
        )}
        <Box sx={{ px: 1 }}>
          <TextField
            size="small"
            placeholder="Search"
            onChange={(e) => setSearch(e.target.value)}
          />
        </Box>
        {(isCSManager || !report.getSubmission().needsEntityMapping()) && (
          <Box sx={{ mr: 1 }}>
            <DownloadAllButton report={report} />
          </Box>
        )}
        <Tooltip title={addDocumentButtonTooltipTitle}>
          <span>
            <Button
              size="small"
              startIcon={<AddIcon />}
              disabled={!allowAdding}
              onClick={() => setShowUploadModal(true)}
            >
              Add document
            </Button>
          </span>
        </Tooltip>
      </Box>
    </Box>
  );
};
