import {
  Box,
  Button,
  Grid,
  Link,
  Sx<PERSON><PERSON>,
  <PERSON>lt<PERSON>,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import ErrorIcon from "@mui/icons-material/Error";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import Submission from "@legacy/models/Submission";
import { useReport } from "@utils/useReport";
import { PROJECT_INSURANCE_TYPE_TO_DISPLAY_NAME } from "@features/submission-overview/constants";
import { formatCurrency } from "@utils/text";
import { QuoteSubmissionModal } from "@features/report-list/components/quote-submission/QuoteSubmissionModal";
import { useCoveragesQuery } from "@queries/coverages";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { InfoOutlined } from "@mui/icons-material";
import {
  ORGANIZATION,
  TOTAL_PREMIUM_TOOLTIP_EXPLANATION_TEXT,
} from "@utils/constants";
import { EditCoveragesModal } from "@features/submission-overview/components/coverage/EditCoveragesModal";
import { trackSubmissionInfoEdit } from "@utils/amplitude";
import { managementLiabilityCoverageNames } from "@legacy/models/Coverage";
import { useEditableSectionContext } from "@features/submission-information/context/EditableSectionContext";

const formatCurrencyOrEmpty = (
  value: number | null,
  maximumFractionDigits = 2
) => {
  if (value === null) return "-";
  return formatCurrency(value, maximumFractionDigits);
};

type CoveragesGridProps = {
  submission: Submission;
  showPremiums?: boolean;
  onClick?: () => void;
  canEdit?: boolean;
  sx?: SxProps;
};

const CoveragesGrid = ({
  submission,
  showPremiums,
  onClick,
  canEdit = true,
  sx,
}: CoveragesGridProps) => {
  const coverages = submission.coverages;
  const hasAnyQuotedAmount =
    showPremiums &&
    coverages.some((c) => c.quoted_premium !== null && c.quoted_premium > 0) &&
    ["QUOTED", "QUOTED_LOST"].includes(submission.stage);
  const hasAnyBoundAmount =
    showPremiums &&
    coverages.some((c) => c.bound_premium !== null && c.bound_premium > 0) &&
    submission.stage === "QUOTED_BOUND";
  const hasAnyTotalPremiumAmount =
    showPremiums &&
    coverages.some(
      (c) =>
        c.total_premium_or_bound_premium !== null &&
        c.total_premium_or_bound_premium > 0
    ) &&
    submission.stage === "QUOTED_BOUND";

  const showEstimatedPremium =
    showPremiums &&
    coverages.some(
      (c) => c.estimated_premium !== null && c.estimated_premium > 0
    ) &&
    !submission.stage.includes("QUOTED");

  const showSir = coverages.some(
    (c) => c.self_insurance_retention !== null && c.self_insurance_retention > 0
  );

  const part =
    12 /
    (2 +
      (showSir ? 1 : 0) +
      (hasAnyQuotedAmount ? 1 : 0) +
      (hasAnyTotalPremiumAmount ? 2 : 0) +
      (showEstimatedPremium ? 1 : 0));
  return (
    <Grid
      container
      display="flex"
      flexDirection="row"
      rowGap={0.5}
      onClick={onClick}
      sx={sx}
    >
      <Grid item xs={part}>
        <Typography variant="body1" fontWeight={500}>
          Coverage
        </Typography>
      </Grid>
      <Grid item xs={part}>
        <Typography variant="body1" fontWeight={500}>
          Type
        </Typography>
      </Grid>

      {showSir && (
        <Grid item xs={part}>
          <Typography variant="body1" fontWeight={500}>
            SIR
          </Typography>
        </Grid>
      )}

      {showEstimatedPremium && (
        <Grid item xs={part}>
          <Typography variant="body1" fontWeight={500}>
            Estimated Premium
          </Typography>
        </Grid>
      )}

      {hasAnyQuotedAmount && (
        <Grid item xs={part}>
          <Typography variant="body1" fontWeight={500}>
            Quoted Premium
          </Typography>
        </Grid>
      )}
      {hasAnyBoundAmount && (
        <Grid item xs={part}>
          <Typography variant="body1" fontWeight={500}>
            Bound Premium
          </Typography>
        </Grid>
      )}
      {hasAnyTotalPremiumAmount && (
        <Grid item xs={part}>
          <Box display="flex" alignItems="center">
            <Typography variant="body1" fontWeight={500}>
              Total Premium
            </Typography>
            <Tooltip title={TOTAL_PREMIUM_TOOLTIP_EXPLANATION_TEXT}>
              <InfoOutlined fontSize="small" />
            </Tooltip>
          </Box>
        </Grid>
      )}

      {submission.coverages.map((coverage) => (
        <Grid
          item
          xs={12}
          key={coverage.id}
          sx={{ ":hover": { textDecoration: canEdit ? "underline" : "none" } }}
        >
          <Grid container spacing={1} alignItems="center">
            <Grid item xs={part}>
              <Typography
                variant="body1"
                key={coverage.id}
                sx={{ fontSize: 12, mr: 2 }}
              >
                {coverage.coverage.display_name}
              </Typography>
            </Grid>
            <Grid item xs={part}>
              <Typography
                variant="body1"
                key={coverage.id}
                sx={{ fontSize: 12 }}
              >
                {coverage.formatType()}
                {(coverage.coverage_type === "EXCESS" ||
                  managementLiabilityCoverageNames.includes(
                    coverage.coverage.name!
                  )) && (
                  <>
                    <br />
                    Limit: {formatCurrencyOrEmpty(coverage.limit, 0)} /
                    Attachment{" "}
                    {formatCurrencyOrEmpty(coverage.attachment_point, 0)}
                  </>
                )}
              </Typography>
            </Grid>

            {showSir && (
              <Grid item xs={part}>
                <Typography
                  variant="body1"
                  key={coverage.id}
                  sx={{ fontSize: 12 }}
                >
                  {formatCurrencyOrEmpty(coverage.self_insurance_retention)}
                </Typography>
              </Grid>
            )}

            {showEstimatedPremium && (
              <Grid item xs={part}>
                <Typography
                  variant="body1"
                  key={coverage.id}
                  sx={{ fontSize: 12 }}
                >
                  {formatCurrencyOrEmpty(coverage.estimated_premium)}
                </Typography>
              </Grid>
            )}
            {hasAnyQuotedAmount && (
              <Grid item xs={part}>
                <Typography
                  variant="body1"
                  key={coverage.id}
                  sx={{ fontSize: 12 }}
                >
                  {formatCurrencyOrEmpty(coverage.quoted_premium)}
                </Typography>
              </Grid>
            )}
            {hasAnyBoundAmount && (
              <Grid item xs={part}>
                <Typography
                  variant="body1"
                  key={coverage.id}
                  sx={{ fontSize: 12 }}
                >
                  {formatCurrencyOrEmpty(coverage.bound_premium)}
                </Typography>
              </Grid>
            )}
            {hasAnyTotalPremiumAmount && (
              <Grid item xs={part}>
                <Typography
                  variant="body1"
                  key={coverage.id}
                  sx={{ fontSize: 12 }}
                >
                  {formatCurrencyOrEmpty(
                    coverage.total_premium_or_bound_premium
                  )}
                </Typography>
              </Grid>
            )}
          </Grid>
        </Grid>
      ))}
    </Grid>
  );
};

const PdsNote = () => {
  const [showAll, setShowAll] = useState(false);
  const { data: allCoverages } = useCoveragesQuery();
  const allCoveragesCount = (allCoverages ?? []).length;

  return (
    <Box>
      <Typography variant="body2" sx={{ pt: 2 }}>
        The following coverages are the only valid coverages for this
        submission. Mark stuck only if the listed coverages should be included
        but aren{"'"}t:
      </Typography>
      {allCoverages?.map((c, i) => {
        if (!showAll && i > 2) return null;
        const name =
          c.name === "liability"
            ? `General/Primary, Umbrella/Excess Liability`
            : c.display_name;
        return (
          <Typography key={i} variant="body2">
            - {name}
          </Typography>
        );
      })}
      {!showAll && allCoveragesCount > 3 && (
        <Typography variant="body2">
          <Link sx={{ cursor: "pointer" }} onClick={() => setShowAll(true)}>
            {allCoveragesCount - 3} more
          </Link>
        </Typography>
      )}
    </Box>
  );
};

export const Coverages = () => {
  const report = useReport();
  const isEditing = useEditableSectionContext().use.isEditing();
  const setIsEditing = useEditableSectionContext().use.setIsEditing();
  const submission = report.getSubmission();
  const { isCSManager } = useAccessPermissions();
  const orgId = useOrganizationId();
  const user = useCurrentUser();
  const isSupport = user.applicable_settings.is_support;
  const isNonEditableOrg = orgId === ORGANIZATION.NATIONWIDE;
  const orgGroup = report.submission.organization_group;
  const isBowheadXSorPrimary =
    orgGroup === "BOWHEAD_XS" || orgGroup === "BOWHEAD_PRIMARY";
  const isBowheadEnvironmental = orgGroup === "BOWHEAD_ENVIRONMENTAL";
  const canEditCoverages =
    !isNonEditableOrg && !(report.isK2Aegis() && isSupport);

  const hasAnyQuotedAmount = submission.coverages.some(
    (c) => c.quoted_premium !== null && c.quoted_premium > 0
  );
  const hasAnyBoundAmount = submission.coverages.some(
    (c) => c.bound_premium !== null && c.bound_premium > 0
  );
  const hasAnyTotalPremiumAmount = submission.coverages.some(
    (c) =>
      c.total_premium_or_bound_premium !== null &&
      c.total_premium_or_bound_premium > 0
  );
  const hasAnySIR = submission.coverages.some(
    (c) => c.self_insurance_retention !== null && c.self_insurance_retention > 0
  );
  const hasBoundOrTotalPremium = hasAnyBoundAmount || hasAnyTotalPremiumAmount;
  // Note: Either only single coverage or hard-defined rules.
  const hasNoCoverages = report.getCoverages().length === 0;
  const lastEditDate = sessionStorage.getItem(`coverages_${report.id}`);
  const lastEditLess1Hr =
    !!lastEditDate && new Date().getTime() - parseInt(lastEditDate) < 3600000;
  const isNWML = orgId === ORGANIZATION.NATIONWIDE_ML;
  const isEditable =
    !isNonEditableOrg &&
    !isBowheadXSorPrimary &&
    (hasBoundOrTotalPremium ||
      hasAnyQuotedAmount ||
      (hasAnySIR && isNWML) ||
      isCSManager ||
      hasNoCoverages ||
      lastEditLess1Hr);

  const projectInsuranceType = submission.businesses.find(
    (b) => b.project_insurance_type
  )?.project_insurance_type;

  const startEditing = () => {
    trackSubmissionInfoEdit(report, {
      item: "coverages",
    });
    setIsEditing(!isEditing);
  };

  return (
    <>
      {report.getCoverages().length > 0 ? (
        <CoveragesGrid
          submission={report.getSubmission()}
          showPremiums
          canEdit={canEditCoverages}
          onClick={canEditCoverages ? startEditing : undefined}
          sx={{
            cursor: canEditCoverages ? "pointer" : "default",
          }}
        />
      ) : (
        canEditCoverages && (
          <Grid container display="flex" flexDirection="row" spacing={2}>
            <Grid item xs={4} sx={{ alignItems: "center", display: "flex" }}>
              <Button
                variant="contained"
                color="secondary"
                size="small"
                onClick={startEditing}
              >
                Select coverage
              </Button>

              <Tooltip title="Select coverage" sx={{ ml: 1 }}>
                <ErrorIcon color="warning" />
              </Tooltip>
            </Grid>
          </Grid>
        )
      )}
      <PdsNote />
      {isEditing && !hasBoundOrTotalPremium && !hasAnyQuotedAmount && (
        <EditCoveragesModal
          report={report}
          onClose={() => setIsEditing(false)}
          isPds
          isReadOnly={!isEditable}
          onlyNonLiabilityEditable={isBowheadEnvironmental}
        />
      )}

      {isEditing && hasBoundOrTotalPremium && (
        <QuoteSubmissionModal
          reportId={report.id}
          type="bind"
          source="edit-coverage"
          onClose={() => setIsEditing(false)}
          changeStage={false}
          showCoverageDates={false}
          readOnly={!isEditable}
          onlyNonLiabilityEditable={isBowheadEnvironmental}
        />
      )}
      {isEditing && hasAnyQuotedAmount && !hasBoundOrTotalPremium && (
        <QuoteSubmissionModal
          reportId={report.id}
          type="quote"
          source="edit-coverage"
          onClose={() => setIsEditing(false)}
          changeStage={false}
          showCoverageDates={false}
          readOnly={!isEditable}
          onlyNonLiabilityEditable={isBowheadEnvironmental}
        />
      )}
      {projectInsuranceType && (
        <Box mt={2}>
          <Typography variant="body1" fontWeight={500}>
            Project Insurance type
          </Typography>
          <Typography variant="body1">
            {PROJECT_INSURANCE_TYPE_TO_DISPLAY_NAME[projectInsuranceType]}
          </Typography>
        </Box>
      )}
    </>
  );
};
