import React, { useEffect, useMemo } from "react";
import { useExperimentsStore } from "@features/experiments/state";
import { NaicsCodesList } from "@features/submission-overview/components/NaicsCodes";
import { DuringExperimentProvider } from "@features/experiments/context/DuringExperiment";
import { useReport } from "@utils/useReport";
import { useEditableSectionContext } from "@features/submission-information/context/EditableSectionContext";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { useOrganizationId } from "@utils/auth";
import { ItemTitle } from "@features/submission-information/components/ItemTitle";
import { ORGANIZATION } from "@utils/constants";

export const Naics = () => {
  const report = useReport();
  const isEditing = useEditableSectionContext().use.isEditing();
  const setIsEditing = useEditableSectionContext().use.setIsEditing();
  const setIsVisible = useEditableSectionContext().use.setIsVisible();
  const experimentRuns = useExperimentsStore((state) => state.experiments);
  const naicsExperimentRun = useMemo(() => {
    return experimentRuns.find((e) => e.experiment?.name === "naics_code");
  }, [experimentRuns]);
  const { isCSManager } = useAccessPermissions();
  const orgId = useOrganizationId();

  const hasGeneratedNAICS =
    report.submission.is_naics_verified &&
    (!naicsExperimentRun || (naicsExperimentRun?.samples ?? []).length === 0);
  const isNWMLAndCS = isCSManager && orgId === ORGANIZATION.NATIONWIDE_ML;
  const isSecura = orgId === ORGANIZATION.SECURA;
  const hasSic = !!report.submission.sic_code;

  const showNaics = !hasGeneratedNAICS || isNWMLAndCS || (isSecura && !hasSic);

  useEffect(() => {
    setIsVisible(showNaics);
  }, [setIsVisible, showNaics]);

  return (
    <ItemTitle title="Codes">
      <DuringExperimentProvider
        sample={naicsExperimentRun?.samples?.[0]}
        experimentRun={naicsExperimentRun}
      >
        <NaicsCodesList
          report={report}
          isEditing={isEditing}
          setIsEditing={setIsEditing}
        />
      </DuringExperimentProvider>
    </ItemTitle>
  );
};
