import { toSentenceCase } from "@legacy/helpers/reportHelpers";
import { Box, MenuItem, Select } from "@mui/material";
import { GridRenderCellParams } from "@mui/x-data-grid-pro";
import { CardTable, CardTableColumn } from "@ui-patterns/card-table";
import React, { useMemo } from "react";
import SubmissionBusiness from "@legacy/models/SubmissionBusiness";
import { useUpdateSubmissionBusinessMutation } from "@queries/submission";
import {
  SubmissionBusinessEntityRoleEnum,
  SubmissionBusinessNamedInsuredEnum,
} from "@legacy/api_clients/copilot_api_client";
import { useSnackbar } from "notistack";
import { sentryCaptureException } from "@legacy/services/sentryService";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { useReport } from "@utils/useReport";

export const AllLocations = () => {
  const report = useReport();
  const { enqueueSnackbar } = useSnackbar();
  const { isCSManager } = useAccessPermissions();
  const isConstruction =
    report.submission.primary_naics_code?.startsWith("NAICS_23");

  const { mutate: updateBusiness, isLoading: isUpdating } =
    useUpdateSubmissionBusinessMutation();

  const businesses = report.getBusinesses();

  let columns: CardTableColumn<SubmissionBusiness>[] = useMemo(() => {
    const updateSubmissionBusiness = (rowId: string, data: any) => {
      updateBusiness(
        {
          submissionBusiness: data,
          id: rowId,
          submissionId: report.getSubmission().id,
          reportId: report.id,
        },
        {
          onSuccess: () => {
            enqueueSnackbar("Row updated");
          },
          onError: (e) => {
            enqueueSnackbar("Could not update row", { variant: "error" });
            sentryCaptureException(e);
          },
        }
      );
    };
    return [
      {
        headerName: "Name",
        field: "name",
        flex: 1,
        renderCell: ({
          row,
        }: GridRenderCellParams<any, SubmissionBusiness, any>) => {
          const poBox = row.getPoBox();
          return (
            <Box
              sx={{
                display: "flex",
                width: "100%",
                justifyContent: "space-between",
              }}
            >
              <Box sx={{ my: "auto" }}>
                {row.name || "-"}
                {poBox && <Box>{poBox}</Box>}
                {row.getAliases().length > 0 && (
                  <Box>Aliases: {row.getAliases().join(", ")}</Box>
                )}
              </Box>
            </Box>
          );
        },
      },
      {
        headerName: "Address",
        field: "address",
        flex: 1,
      },
      {
        headerName: "Role",
        field: "entity_role",
        width: 250,
        renderCell: ({
          row,
        }: GridRenderCellParams<any, SubmissionBusiness, any>) => {
          return (
            <Select
              sx={{ width: "90%" }}
              value={row.entity_role ?? "none"}
              onChange={(e) => {
                updateSubmissionBusiness(row.id, {
                  entity_role:
                    e.target.value === "none" ? null : (e.target.value as any),
                });
              }}
            >
              {[
                SubmissionBusinessEntityRoleEnum.GeneralContractor,
                SubmissionBusinessEntityRoleEnum.Project,
              ].map((v) => (
                <MenuItem value={v} key={v}>
                  {toSentenceCase(v!)}
                </MenuItem>
              ))}
              <MenuItem value={"none"}>-</MenuItem>
            </Select>
          );
        },
      },
      {
        headerName: "Named Insured",
        field: "named_insured",
        width: 250,
        renderCell: ({
          row,
        }: GridRenderCellParams<any, SubmissionBusiness, any>) => {
          return (
            <Select
              sx={{ width: "90%" }}
              value={row.named_insured ?? "none"}
              onChange={(e) => {
                updateSubmissionBusiness(row.id, {
                  named_insured:
                    e.target.value === "none" ? null : (e.target.value as any),
                });
              }}
            >
              {Object.values(SubmissionBusinessNamedInsuredEnum).map((id) => (
                <MenuItem key={id} value={id}>
                  {toSentenceCase(id)}
                </MenuItem>
              ))}
              <MenuItem value={"none"}>-</MenuItem>
            </Select>
          );
        },
      },
    ];
  }, [enqueueSnackbar, report, updateBusiness]);

  if (!isConstruction && !isCSManager) {
    return null;
  }

  if (!isCSManager) {
    columns = columns.filter((c) => c.field !== "named_insured");
  }

  return (
    <Box sx={{ width: "100%", overflow: "hidden" }}>
      <CardTable
        loading={isUpdating}
        columns={columns}
        rows={businesses}
        title={
          <Box display="flex" justifyContent="center">
            Premises and Locations ({businesses.length}){" "}
          </Box>
        }
        allowSearch
        getRowHeight={() => "auto"}
        trackingTableType="premises and locations"
      />
    </Box>
  );
};
