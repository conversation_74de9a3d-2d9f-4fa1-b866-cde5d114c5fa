import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
} from "@mui/material";
import React, { useMemo, useState } from "react";
import VerificationButton from "@legacy/components/report/VerificationButton";
import { useRouter } from "next/router";
import { useReportQuery } from "@queries/report";
import { SubmissionName } from "@features/submission-information/components/SubmissionName";
import { EmbeddedFiles } from "@features/entity-mapping/components/EmbeddedFiles";
import { Dialog } from "@ui-patterns/dialog";
import { routes } from "@features/routes";
import { useNaicsCodes } from "@queries/naicsCodes";
import { AllLocations } from "@features/submission-information/components/sections/AllLocations";
import {
  ModelFileFileTypeEnum,
  SubmissionBusinessEntityRoleEnum,
} from "@legacy/api_clients/copilot_api_client";
import { RunningExperimentsInfo } from "@features/experiments/components/RunningExperimentsInfo";
import { useExperimentsStore } from "@features/experiments/state";
import { DuringExperimentProvider } from "@features/experiments/context/DuringExperiment";
import { useShouldHideCoverages } from "@features/submission-overview/hooks";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { OrgGroup } from "@features/submission-information/components/sections/OrgGroup";
import { useIsPdsPage } from "@utils/useIsPdsPage";
import { Naics } from "@features/submission-information/components/sections/Naics";
import { PdsCompletedSection } from "@features/submission-information/components/PdsCompletedSection";
import { GeotechFile } from "@features/submission-information/components/sections/GeotechFile";
import { Brokerage } from "@features/submission-information/components/sections/Brokerage";
import { DateRow } from "@features/submission-information/components/sections/DateRow";
import { AssigneeRow } from "@features/submission-information/components/sections/AssigneeRow";
import { ItemTitle } from "@features/submission-information/components/ItemTitle";
import { Coverages } from "@features/submission-information/components/sections/Coverages";
import { DescriptionOfOperations } from "@features/submission-information/components/sections/DescriptionOfOperations";
import { PdsNewsLegalFiling } from "@features/submission-information/components/sections/PdsNewsLegalFiling";
import { SicView } from "@features/submission-information/components/sections/sic/SicView";
import { ORGANIZATION } from "@utils/constants";

const FILE_TYPES_TO_SHOW: ModelFileFileTypeEnum[] = [
  ModelFileFileTypeEnum.Email,
  ModelFileFileTypeEnum.AcordForm,
  ModelFileFileTypeEnum.SupplementalForm,
  ModelFileFileTypeEnum.CoverSheet,
];

const FILE_TYPES_ORDER: ModelFileFileTypeEnum[] = [
  ModelFileFileTypeEnum.Email,
  ModelFileFileTypeEnum.AcordForm,
  ModelFileFileTypeEnum.SupplementalForm,
  ModelFileFileTypeEnum.CoverSheet,
];

const SUSPICIOUS_PREFIXES = ["FW:", "RE:", "FWD:", "RE FW:", "RE FWD:"];

export const SubmissionInformation = () => {
  const router = useRouter();
  const reportId = (router.query.reportId ?? router.query.id) as string;
  const isPds = useIsPdsPage();
  const { data: report } = useReportQuery({
    id: reportId,
    usePdsExpand: isPds,
  });
  const experimentRuns = useExperimentsStore((state) => state.experiments);
  const orgId = useOrganizationId();
  const user = useCurrentUser();
  const isBowhead = orgId === ORGANIZATION.BOWHEAD;

  const naicsExperimentRun = useMemo(() => {
    return experimentRuns.find((e) => e.experiment?.name === "naics_code");
  }, [experimentRuns]);

  const effectiveDateExperimentRun = useMemo(() => {
    return experimentRuns.find((e) => e.experiment?.name === "effective_date");
  }, [experimentRuns]);

  const [nameEdited, setNameEdited] = React.useState(false);
  const [showVerifyModal, setShowVerifyModal] = React.useState(false);
  const [showConfirmation, setShowConfirmation] = React.useState(false);
  const [activeEmbeddedFilesIndex, setActiveEmbeddedFilesIndex] = useState<
    number | undefined
  >(0);

  const submission = report?.getSubmission();
  const { isCSManager } = useAccessPermissions();

  const { submission: submissionNaicsCodes, isLoading } = useNaicsCodes(report);

  const shouldHideCoverages = useShouldHideCoverages();

  const nameErrorMessage = useMemo(() => {
    const isReportNameSuspicious = SUSPICIOUS_PREFIXES.some(
      (prefix) =>
        (report?.name.toLowerCase().indexOf(prefix.toLowerCase()) ?? -1) > -1
    );
    if (isReportNameSuspicious) {
      return "FW: or similar prefix still appears in the report name. Please check the name and correct if necessary.";
    }
    if (!nameEdited) {
      return "Submission's name has not been changed. Do you want to proceed to verify?";
    }
    return "";
  }, [report, nameEdited]);

  if (!report || isLoading || !submission) {
    return <CircularProgress />;
  }

  const isNWMLAndCS = isCSManager && orgId === ORGANIZATION.NATIONWIDE_ML;

  const hasGeneratedDescription = report.hasGeneratedDescription();

  const hasAnyDescription =
    hasGeneratedDescription || submission.description_of_operations;
  const isDuringNaicsExperiment =
    (naicsExperimentRun?.samples ?? []).length > 0;
  const hasNAICS = isDuringNaicsExperiment
    ? ((naicsExperimentRun?.samples?.[0]?.user_input as any)?.["naics"]
        ?.length ?? 0) > 0
    : !!submissionNaicsCodes?.length;
  const missingFieldsMessage =
    hasAnyDescription && hasNAICS
      ? ""
      : "Please fill the required fields: Description of Operations and Primary NAICS Code. ";

  const businesses = report.getBusinesses();
  const isMultiGC =
    businesses.filter(
      (b) =>
        b.entity_role === SubmissionBusinessEntityRoleEnum.GeneralContractor
    ).length > 1;
  const roleErrorMessage = isMultiGC
    ? "Make sure you only have one General Contractor."
    : "";
  const errorMessage = `${missingFieldsMessage}${roleErrorMessage}`;

  const files = (report!.getSubmission()?.files || [])
    .filter(
      (f) =>
        f.file_type &&
        FILE_TYPES_TO_SHOW.includes(f.file_type) &&
        !(
          f.isEntityExtractingAcord() &&
          f.isSensibleCompleted() &&
          !f.isAcordProcessingFailed()
        )
    )
    .sort((a, b) => {
      if (a.isAcordForm() && b.isAcordForm()) {
        return a.isAcord125() ? -10 : 10;
      }
      return (
        FILE_TYPES_ORDER.indexOf(
          a.file_type ?? ModelFileFileTypeEnum.SupplementalForm
        ) -
        FILE_TYPES_ORDER.indexOf(
          b.file_type ?? ModelFileFileTypeEnum.SupplementalForm
        )
      );
    });

  const isBossSupport =
    user.isSupport() &&
    user.organization.id === 6 &&
    report.submission.origin === "API";

  return (
    <Box mb={7}>
      <RunningExperimentsInfo />
      <EmbeddedFiles
        reportId={report!.id}
        files={files}
        activeFileIndex={activeEmbeddedFilesIndex}
        setActiveFileIndex={setActiveEmbeddedFilesIndex}
      />
      <Box mt={3}>
        <PdsCompletedSection title="NAICS Codes" editable>
          <Naics />
        </PdsCompletedSection>
        <PdsCompletedSection title="SIC Codes" editable>
          <SicView />
        </PdsCompletedSection>
        <PdsCompletedSection title="Premises and Location">
          <AllLocations />
        </PdsCompletedSection>
        <PdsCompletedSection title="Geotech Report">
          <GeotechFile />
        </PdsCompletedSection>
        <PdsCompletedSection title="Submission Name" editable={!isBossSupport}>
          <SubmissionName setNameEdited={setNameEdited} />
        </PdsCompletedSection>
        <PdsCompletedSection
          title="Broker / Agent & Brokerage / Agency"
          editable
        >
          <Brokerage />
        </PdsCompletedSection>
        <PdsCompletedSection title="Date" editable={!isBossSupport}>
          <DuringExperimentProvider
            sample={effectiveDateExperimentRun?.samples?.[0]}
            experimentRun={effectiveDateExperimentRun}
          >
            <DateRow />
          </DuringExperimentProvider>
        </PdsCompletedSection>
        <PdsCompletedSection title="Assignee" editable={!isBossSupport}>
          <AssigneeRow />
        </PdsCompletedSection>
        {user?.getOrgGroups().length > 0 && (
          <PdsCompletedSection
            title="Program"
            editable={isCSManager && !isBowhead}
          >
            <OrgGroup />
          </PdsCompletedSection>
        )}
        {!shouldHideCoverages && (
          <PdsCompletedSection title="Coverages" editable>
            <ItemTitle title="Coverages">
              <Coverages />
            </ItemTitle>
          </PdsCompletedSection>
        )}
        {(!hasGeneratedDescription || isNWMLAndCS) && (
          <PdsCompletedSection title="Description of Operations">
            <DescriptionOfOperations />
          </PdsCompletedSection>
        )}
        <PdsNewsLegalFiling type="news" />
        <PdsNewsLegalFiling type="legal-fling" />
      </Box>
      <Drawer open variant="persistent" anchor="bottom">
        <Tooltip title={errorMessage} placement="top-end">
          <Box px={2} py={1}>
            <VerificationButton
              disabled={!!errorMessage || !!nameErrorMessage}
              justifyContent="end"
              refetch={() => router.replace(routes.hub())}
              report={report}
              showConfirmation={showConfirmation}
              onCancel={() => {
                setShowVerifyModal(false);
                setShowConfirmation(false);
              }}
              verifyButton={
                <Button
                  disabled={!!errorMessage}
                  color="error"
                  size="small"
                  variant="contained"
                  onClick={() => setShowVerifyModal(true)}
                >
                  Manually Verify Submission
                </Button>
              }
            />
          </Box>
        </Tooltip>
      </Drawer>
      {!!nameErrorMessage && showVerifyModal && !showConfirmation && (
        <Dialog
          secondaryActionText="Cancel"
          primaryActionText="Proceed to verify"
          onClose={() => setShowVerifyModal(false)}
          onPrimaryAction={() => setShowConfirmation(true)}
          fullWidth
        >
          <Typography variant="h5">{nameErrorMessage}</Typography>
        </Dialog>
      )}
    </Box>
  );
};
