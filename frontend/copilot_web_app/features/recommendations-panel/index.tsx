import { FocusDrawer } from "@ui-patterns/focus-drawer";
import React, { type JSX, useMemo } from "react";
import { useEventBus } from "@features/events";
import {
  RecommendationsPanelOpenEvent,
  RecommendationsPanelType,
} from "@features/recommendations-panel/types";
import { ExclusionsContent } from "@features/recommendations-panel/panels/ExclusionsContent";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import { Box, Link } from "@mui/material";
import { SingleGuidelineContent } from "@features/recommendations-panel/panels/SingleGuidelineContent";
import { AttachFilesContent } from "@features/recommendations-panel/panels/AttachFilesContent";
import {
  getOutcomeAndExplanation,
  getRuleName,
} from "@features/recommendations-panel/utils";
import { useRecommendationContext } from "@features/recommendation/hooks/useRecommendationData";
import { MissingDocsContent } from "@features/recommendations-panel/panels/MissingDocsContent";
import { FactFocusContent } from "@features/recommendations-panel/panels/FactFocusContent";
import { RecommendationPanelContextProvider } from "@features/recommendations-panel/context/RecommendationPanelContext";
import { ReferContent } from "@features/recommendations-panel/panels/ReferContent";
import truncate from "lodash/truncate";
import { useSubmissionBladesStore } from "@features/blades/submissionBladesStore";
import { useReport } from "@utils/useReport";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { NewSingleGuideContent } from "@features/recommendations-panel/panels/NewSingleGuideContent";

export const RecommendationsPanel = () => {
  const [event, setEvent] = React.useState<RecommendationsPanelOpenEvent>();
  const [previousNavigationEvents, setPreviousNavigationEvents] =
    React.useState<RecommendationsPanelOpenEvent[]>([]);
  const { recommendation } = useRecommendationContext();
  const report = useReport();
  const { open: openBlade } = useSubmissionBladesStore();
  const { newRecommendationPanel } = useFeatureFlags();

  const DEFINITIONS: Record<
    RecommendationsPanelType,
    {
      title: string;
      Component: (props: any) => JSX.Element;
    }
  > = useMemo(() => {
    return {
      [RecommendationsPanelType.EXCLUSIONS]: {
        title: "Exclusions",
        Component: ExclusionsContent,
      },
      [RecommendationsPanelType.SINGLE_GUIDELINE]: {
        title: "",
        Component: newRecommendationPanel
          ? NewSingleGuideContent
          : SingleGuidelineContent,
      },
      [RecommendationsPanelType.RECOMMENDED_FILES]: {
        title: "Recommended files to review",
        Component: AttachFilesContent,
      },
      [RecommendationsPanelType.MISSING_DOCS]: {
        title: "Missing documents",
        Component: MissingDocsContent,
      },
      [RecommendationsPanelType.FACT_FOCUS]: {
        title: "Fact",
        Component: FactFocusContent,
      },
      [RecommendationsPanelType.REFER]: {
        title: "Refer",
        Component: ReferContent,
      },
    };
  }, [newRecommendationPanel]);

  useEventBus(
    (newEvent) => {
      if (newEvent.openNested && event) {
        setPreviousNavigationEvents((prev) => [...prev, event]);
      } else {
        setPreviousNavigationEvents([]);
      }
      setEvent(newEvent);
    },
    [event],
    "recommendations-panel/open"
  );

  useEventBus(
    () => {
      if (previousNavigationEvents.length > 0) {
        setEvent(previousNavigationEvents[previousNavigationEvents.length - 1]);
        setPreviousNavigationEvents((prev) => prev.slice(0, -1));
      }
    },
    [previousNavigationEvents],
    "recommendations-panel/back"
  );

  useEventBus(
    () => {
      setEvent(undefined);
      setPreviousNavigationEvents([]);
    },
    [],
    "recommendations-panel/close"
  );

  useEventBus(
    () => {
      openBlade({
        containerId: "report-wrapper",
        items: [{ type: "notes" }],
        reportId: report.id,
        preventCloseOnClickOutside: true,
      });
      setEvent(undefined);
      setPreviousNavigationEvents([]);
    },
    [openBlade, report.id],
    "recommendations-panel/refer"
  );

  const prepareTitle = (event: RecommendationsPanelOpenEvent) => {
    let title = DEFINITIONS[event.type].title;
    if (event.type === RecommendationsPanelType.SINGLE_GUIDELINE) {
      const { outcomeExplanation } = getOutcomeAndExplanation(
        recommendation,
        event.outcomeId,
        event.outcomeExplanationId
      );
      title += " " + getRuleName(outcomeExplanation?.rule, { length: 300 });
    } else if (event.type === RecommendationsPanelType.FACT_FOCUS) {
      title += `: ${truncate(event.title, { length: 30 })}`;
    }
    return title;
  };

  if (event) {
    const { Component } = DEFINITIONS[event.type];
    let titleComponent: React.ReactNode = prepareTitle(event);
    if (previousNavigationEvents.length > 0) {
      const previousParts = previousNavigationEvents.map((prevEvent, index) => {
        return (
          <React.Fragment key={prevEvent.type + index}>
            <Link
              sx={{ cursor: "pointer" }}
              onClick={(e) => {
                e.preventDefault();
                setEvent(prevEvent);
                setPreviousNavigationEvents((prev) => prev.slice(0, index));
              }}
            >
              {prepareTitle(prevEvent)}
            </Link>{" "}
            <KeyboardArrowRightIcon />
          </React.Fragment>
        );
      });
      titleComponent = (
        <Box display="flex" flexDirection="row" gap={2}>
          {previousParts} {prepareTitle(event)}
        </Box>
      );
    }
    return (
      <RecommendationPanelContextProvider>
        <FocusDrawer title={titleComponent} onClose={() => setEvent(undefined)}>
          <Component data={event} />
        </FocusDrawer>
      </RecommendationPanelContextProvider>
    );
  }
  return null;
};
