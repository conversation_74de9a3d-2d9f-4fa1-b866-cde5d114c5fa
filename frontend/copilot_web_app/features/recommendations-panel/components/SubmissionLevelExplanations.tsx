import { TriggerItem } from "@features/recommendations-panel/panels/useNewSingleGuideContentData";
import { ExplanationBox } from "@features/recommendations-panel/components/ExplanationBox";
import { Typography } from "@mui/material";
import { TriggerValueLink } from "@features/recommendations-panel/panels/TriggerValueLink";
import React from "react";
import { SourceConditionText } from "@features/recommendations-panel/components/SourceConditionText";
import { formatVariableItem } from "@features/recommendations-panel/utils";

type SubmissionLevelExplanationsProps = {
  items: TriggerItem[];
  ruleId?: string;
};

export const SubmissionLevelExplanations = ({
  ruleId,
  items,
}: SubmissionLevelExplanationsProps) => {
  if (items.length === 0) {
    return null;
  }
  return (
    <ExplanationBox title="Submission" ruleIdForLink={ruleId}>
      {items.map((item) => {
        const formattedSubmissionValue = formatVariableItem(
          item.submissionValues,
          item.variable
        );
        return (
          <Typography key={item.source.id}>
            <SourceConditionText
              source={item.source}
              variable={item.variable}
            />
            :{" "}
            <TriggerValueLink trigger={item.submissionTrigger!}>
              {formattedSubmissionValue}
            </TriggerValueLink>
          </Typography>
        );
      })}
    </ExplanationBox>
  );
};
