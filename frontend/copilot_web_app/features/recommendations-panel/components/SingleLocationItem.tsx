import { PerLocationTriggerItem } from "@features/recommendations-panel/panels/useNewSingleGuideContentData";
import { Link as MuiLink, Typography } from "@mui/material";
import { ExplanationBox } from "@features/recommendations-panel/components/ExplanationBox";
import React from "react";
import { NicePrintArray } from "@features/recommendation/components/NicePrintArray";
import { EVENT_BUS } from "@features/events";
import { TriggerValueLink } from "@features/recommendations-panel/panels/TriggerValueLink";
import {
  formatVariableItem,
  getConditionName,
} from "@features/recommendations-panel/utils";
import {
  SourceResponse,
  VariableResponse,
} from "@legacy/api_clients/recommendations-client";
import { ELEMENT_ID } from "@features/modes/jsx/constants";

type SingleLocationItemProps = {
  item: PerLocationTriggerItem;
};

export const SingleLocationItem = ({ item }: SingleLocationItemProps) => {
  const openLocation = () => {
    EVENT_BUS.emit("navigation/report", {
      elementId: "NAVIGATE_TO_CAPABILITY",
      capabilityRequest: {
        type: "SUBMISSION_BUSINESS",
        parentType: "SUBMISSION_BUSINESS",
        parentId: item.submissionBusiness.id!,
      },
    });
  };

  const openStructures = (
    structureIds: string[],
    variable: VariableResponse,
    source: SourceResponse
  ) => {
    const conditionName = getConditionName(
      source.source_condition?.condition.name
    );
    const name =
      variable.display_name +
      " " +
      conditionName +
      " " +
      formatVariableItem(source.source_condition?.condition.item, variable);
    EVENT_BUS.emit("ui/premises-viewer/filter-picked", {
      parentType: "STRUCTURE",
      name: "1 location with " + name,
      parentIds: structureIds,
      source: "guideline",
    });
    setTimeout(() => {
      EVENT_BUS.emit("ui/premises-viewer/single-business-focus", {
        anyId: item.submissionBusiness.id!,
        skipNavigation: true,
      });
    }, 50);
    setTimeout(() => {
      EVENT_BUS.emit("navigation/report", {
        elementId: ELEMENT_ID.STRUCTURES_TAB,
      });
    }, 50);
  };

  return (
    <ExplanationBox key={item.submissionBusiness.id}>
      <MuiLink
        sx={{ fontWeight: 500, cursor: "pointer" }}
        onClick={openLocation}
      >
        {item.submissionBusiness.getDisplayName()}
      </MuiLink>
      <Typography>{item.submissionBusiness.getDisplayAddress()}</Typography>
      {item.sources.map((source) => {
        return (
          <Typography key={source.source.id}>
            {source.variable.display_name}:{" "}
            {source.locationValues && source.locationValues.length > 0 && (
              <TriggerValueLink trigger={source.locationTriggers[0]}>
                {formatVariableItem(source.locationValues, source.variable)}
              </TriggerValueLink>
            )}{" "}
            {source.structureValues.length > 0 && (
              <NicePrintArray onlyCommas>
                {source.structureValues.map((v) => {
                  return (
                    <>
                      {" "}
                      {formatVariableItem(v.value, source.variable)} (
                      <MuiLink
                        sx={{ fontWeight: 500, cursor: "pointer" }}
                        onClick={() =>
                          openStructures(
                            v.structureIds,
                            source.variable,
                            source.source
                          )
                        }
                      >
                        {item.totalStructuresCount === 1
                          ? "1/1 structures"
                          : `${v.structureIds.length}/${item.totalStructuresCount} structures`}
                      </MuiLink>
                      )
                    </>
                  );
                })}
              </NicePrintArray>
            )}
          </Typography>
        );
      })}
    </ExplanationBox>
  );
};
