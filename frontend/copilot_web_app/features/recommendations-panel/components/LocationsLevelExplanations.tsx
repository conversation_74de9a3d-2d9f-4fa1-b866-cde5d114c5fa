import { TriggerItem } from "../panels/useNewSingleGuideContentData";
import { ExplanationBox } from "@features/recommendations-panel/components/ExplanationBox";
import { Link, Typography } from "@mui/material";
import React from "react";
import { SourceConditionText } from "@features/recommendations-panel/components/SourceConditionText";
import { EVENT_BUS } from "@features/events";
import {
  formatVariableItem,
  getConditionName,
} from "@features/recommendations-panel/utils";

type LocationsLevelExplanationsProps = {
  items: TriggerItem[];
  ruleId?: string;
};

export const LocationsLevelExplanations = ({
  items,
  ruleId,
}: LocationsLevelExplanationsProps) => {
  if (items.length === 0) {
    return null;
  }
  const openLocations = (item: TriggerItem) => {
    const conditionName = getConditionName(
      item.source.source_condition?.condition.name
    );
    const name =
      item.variable.display_name +
      " " +
      conditionName +
      " " +
      formatVariableItem(
        item.source.source_condition?.condition.item,
        item.variable
      );
    EVENT_BUS.emit("ui/premises-viewer/filter-picked", {
      source: "guideline",
      name,
      parentIds:
        item.triggeredLocations
          ?.map((l) => l.submissionBusiness.business_id)
          .filter((s): s is string => Boolean(s)) ?? [],
      parentType: "BUSINESS",
    });
  };

  return (
    <ExplanationBox title="Locations" ruleIdForLink={ruleId}>
      {items.map((item) => {
        return (
          <Typography key={item.source.id}>
            <SourceConditionText
              source={item.source}
              variable={item.variable}
            />
            :{" "}
            <Link
              onClick={() => openLocations(item)}
              sx={{ cursor: "pointer", fontWeight: 500 }}
            >
              {item.triggeredLocations?.length} location
              {(item.triggeredLocations ?? []).length > 1 ? "s" : ""}
            </Link>
          </Typography>
        );
      })}
    </ExplanationBox>
  );
};
