import {
  SourceResponse,
  VariableResponse,
} from "@legacy/api_clients/recommendations-client";
import { Tooltip } from "@mui/material";
import {
  formatVariableItem,
  getConditionName,
} from "@features/recommendations-panel/utils";

type SourceConditionTextProps = {
  source: SourceResponse;
  variable: VariableResponse;
};

export const SourceConditionText = ({
  source,
  variable,
}: SourceConditionTextProps) => {
  const condition = source.source_condition?.condition;
  const conditionNiceName = getConditionName(
    source.source_condition?.condition.name
  );
  let itemName = condition?.item;
  if (Array.isArray(itemName)) {
    if (itemName.length > 1) {
      itemName = (
        <Tooltip title={formatVariableItem(itemName, variable)}>
          <span style={{ textDecoration: "underline" }}>
            {itemName.length} items
          </span>
        </Tooltip>
      );
    }
  } else {
    itemName = formatVariableItem(itemName, variable);
  }

  return (
    <>
      {variable.display_name} {conditionNiceName} {itemName}
    </>
  );
};
