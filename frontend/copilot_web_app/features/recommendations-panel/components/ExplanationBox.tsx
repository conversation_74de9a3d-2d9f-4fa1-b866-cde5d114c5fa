import React from "react";
import { Box, Link as MuiLink, Typography, useTheme } from "@mui/material";
import { routes } from "@features/routes";
import Link from "next/link";

type ExplanationBoxProps = {
  children?: React.ReactNode;
  title?: string;
  ruleIdForLink?: string;
};
export const ExplanationBox = ({
  title,
  ruleIdForLink,
  children,
}: ExplanationBoxProps) => {
  const theme = useTheme();
  return (
    <Box
      sx={{
        border: `1px solid ${theme.palette.neutrals.border}`,
        borderRadius: 0.5,
        p: 1,
        pb: 0.5,
      }}
    >
      {title && (
        <Typography
          variant="subtitle1"
          sx={{
            textTransform: "uppercase",
            fontWeight: 500,
            fontSize: 14,
            lineHeight: 1.4,
            mb: 0.5,
          }}
        >
          {title}
        </Typography>
      )}
      <Box display="flex" flexDirection="column" gap={0.5} sx={{ my: 0.5 }}>
        {children}
        {ruleIdForLink && (
          <Link
            href={routes.portfolioManager.details(ruleIdForLink)}
            passHref
            legacyBehavior
          >
            <MuiLink sx={{ mt: 0.5, mb: -0.5 }} target="_blank">
              View in Portfolio Manager
            </MuiLink>
          </Link>
        )}
      </Box>
    </Box>
  );
};
