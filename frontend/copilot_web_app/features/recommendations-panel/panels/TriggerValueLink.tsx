import { OutcomeExplanationTriggerResponse } from "@legacy/api_clients/recommendations-client";
import { useTriggerDescription } from "@ui-patterns/recommendation/hooks/useTriggerDescription";
import { Link } from "@mui/material";

type TriggerValueLinkProps = {
  trigger: OutcomeExplanationTriggerResponse;
  children?: React.ReactNode;
};

export const TriggerValueLink = ({
  trigger,
  children,
}: TriggerValueLinkProps) => {
  const { canNavigateToSource, navigateToSource } = useTriggerDescription({
    trigger,
  });

  if (canNavigateToSource) {
    return (
      <Link
        onClick={() => navigateToSource()}
        sx={{ fontWeight: 500, cursor: "pointer" }}
      >
        {children}
      </Link>
    );
  } else {
    return <>{children}</>;
  }
};
