import { RecommendationsPanelOpenSingleGuidelineEvent } from "@features/recommendations-panel/types";
import { useReport } from "@utils/useReport";
import { useRecommendationContext } from "@features/recommendation/hooks/useRecommendationData";
import { useMemo, useState } from "react";
import { getOutcomeAndExplanation } from "@features/recommendations-panel/utils";
import {
  OutcomeExplanationTriggerResponse,
  SourceResponse,
  VariableResponse,
} from "@legacy/api_clients/recommendations-client";
import SubmissionBusiness from "@legacy/models/SubmissionBusiness";
import uniq from "lodash/uniq";
import { useStructuresContextData } from "@features/modes-display/entity-picker/context/structuresContext";
import { useStore } from "zustand";

export type TriggerItem = {
  source: SourceResponse;
  variable: VariableResponse;
  isSubmissionLevel?: boolean;
  triggeredLocations?: {
    submissionBusiness: SubmissionBusiness;
    structureTriggers: { structureId: string; values: any[] }[];
    locationValues?: any[];
    locationTriggers: OutcomeExplanationTriggerResponse[];
  }[];
  submissionValues?: any[];
  submissionTrigger?: OutcomeExplanationTriggerResponse;
};

export type PerLocationTriggerItem = {
  submissionBusiness: SubmissionBusiness;
  totalStructuresCount: number;
  sources: {
    source: SourceResponse;
    variable: VariableResponse;
    structureValues: { value: any; structureIds: string[] }[];
    locationValues?: any[];
    locationTriggers: OutcomeExplanationTriggerResponse[];
  }[];
};

const mergeTriggerItems = (items: TriggerItem[]): TriggerItem[] => {
  const merged: Record<string, TriggerItem> = {};

  items.forEach((item) => {
    const key = `${item.source.id}-${item.variable.id}`;
    if (!merged[key]) {
      merged[key] = { ...item };
    } else {
      item.submissionValues?.forEach((value) => {
        if (!merged[key].submissionValues) {
          merged[key].submissionValues = [];
        }
        if (!merged[key].submissionValues.includes(value)) {
          merged[key].submissionValues.push(value);
        }
      });
      item.triggeredLocations?.forEach((l) => {
        if (!merged[key].triggeredLocations) {
          merged[key].triggeredLocations = [];
        }
        const existingLocation = merged[key].triggeredLocations.find(
          (loc) => loc.submissionBusiness.id === l.submissionBusiness.id
        );
        if (existingLocation) {
          l.structureTriggers.forEach((st) => {
            if (
              !existingLocation.structureTriggers.some(
                (et) => et.structureId === st.structureId
              )
            ) {
              existingLocation.structureTriggers.push(st);
            } else {
              const existingTrigger = existingLocation.structureTriggers.find(
                (et) => et.structureId === st.structureId
              );
              if (existingTrigger) {
                existingTrigger.values.push(...st.values);
              }
            }
          });
          existingLocation.locationValues = uniq([
            ...(existingLocation.locationValues || []),
            ...(l.locationValues || []),
          ]);
          existingLocation.locationTriggers = uniq([
            ...(existingLocation.locationTriggers || []),
            ...(l.locationTriggers || []),
          ]);
        } else {
          merged[key].triggeredLocations.push(l);
        }
      });
    }
  });

  return Object.values(merged);
};

export const useNewSingleGuideContentData = (
  data: RecommendationsPanelOpenSingleGuidelineEvent
) => {
  const currentReport = useReport();
  const [isPerLocationExpanded, setIsPerLocationExpanded] = useState(false);
  const { recommendation, fullRules, variablesMap } =
    useRecommendationContext();

  const { outcomeExplanation } = useMemo(() => {
    return getOutcomeAndExplanation(
      recommendation,
      data.outcomeId,
      data.outcomeExplanationId
    );
  }, [data.outcomeExplanationId, data.outcomeId, recommendation]);

  const rule = fullRules.find((r) => outcomeExplanation?.rule_id === r.id);

  const triggerItems = useMemo(() => {
    if (!outcomeExplanation) {
      return [];
    }
    const rule = fullRules.find((r) => outcomeExplanation?.rule_id === r.id);
    if (!rule) {
      return [];
    }
    return mergeTriggerItems(
      outcomeExplanation.triggers.flatMap((trigger) => {
        const source = rule.sources?.find((s) => s.id === trigger.source_id);
        if (!source) {
          return [];
        }
        const variable = variablesMap.get(source?.variable_id);
        if (!variable) {
          return [];
        }

        const isSubmissionLevel = trigger.parent_type === "SUBMISSION";
        const submissionBusiness: SubmissionBusiness | undefined =
          trigger.parent_ids
            .map((parentId) =>
              currentReport.getSubmissionBusinessByAnyId(parentId)
            )
            .filter((sb): sb is SubmissionBusiness => !!sb)[0];

        const structureTriggers = trigger.triggering_values
          .map((tv) => {
            if (tv.parent_type === "STRUCTURE") {
              return { structureId: tv.parent_id, values: tv.values };
            }
          })
          .filter(Boolean);
        const notStructureTriggers = trigger.triggering_values
          .filter((tv) => !tv.parent_type || tv.parent_type !== "STRUCTURE")
          .flatMap((tv) => {
            if ("values" in tv) {
              return tv.values;
            } else if ("value" in tv) {
              return [tv.value];
            }
            return [];
          });

        return [
          {
            source,
            variable,
            isSubmissionLevel,
            submissionValues: isSubmissionLevel
              ? notStructureTriggers
              : undefined,
            submissionTrigger: isSubmissionLevel ? trigger : undefined,
            triggeredLocations: submissionBusiness
              ? [
                  {
                    submissionBusiness,
                    structureTriggers,
                    locationValues: isSubmissionLevel
                      ? []
                      : notStructureTriggers,
                    locationTriggers: isSubmissionLevel ? [] : [trigger],
                  },
                ]
              : undefined,
          } as TriggerItem,
        ];
      })
    );
  }, [currentReport, fullRules, outcomeExplanation, variablesMap]);

  const premiseIdToStructureIds = useStore(
    useStructuresContextData,
    (state) => state.premiseIdToStructureIds
  );
  const perLocationTriggerItems = useMemo(() => {
    const items: PerLocationTriggerItem[] = [];
    triggerItems.forEach((item) => {
      item.triggeredLocations?.forEach((location) => {
        const existingLocation = items.find(
          (i) => i.submissionBusiness.id === location.submissionBusiness.id
        );
        const uniqueStructureValues = Array.from(
          location.structureTriggers.reduce((map, st) => {
            st.values.forEach((value) => {
              if (!map.has(value)) {
                map.set(value, []);
              }
              map.get(value)!.push(st.structureId);
            });
            return map;
          }, new Map<any, string[]>())
        ).map(([value, structureIds]) => ({
          value,
          structureIds,
        }));

        if (existingLocation) {
          existingLocation.sources.push({
            source: item.source,
            variable: item.variable,
            structureValues: uniqueStructureValues,
            locationValues: location.locationValues,
            locationTriggers: location.locationTriggers,
          });
        } else {
          items.push({
            submissionBusiness: location.submissionBusiness,
            totalStructuresCount:
              premiseIdToStructureIds.get(
                location.submissionBusiness.getPremisesId() ?? ""
              )?.size ?? 1,
            sources: [
              {
                source: item.source,
                variable: item.variable,
                structureValues: uniqueStructureValues,
                locationValues: location.locationValues,
                locationTriggers: location.locationTriggers,
              },
            ],
          });
        }
      });
    });
    return items;
  }, [premiseIdToStructureIds, triggerItems]);
  const submissionItems = useMemo(() => {
    return triggerItems.filter((item) => item.isSubmissionLevel);
  }, [triggerItems]);

  const locationItems = useMemo(() => {
    return triggerItems.filter((item) => !item.isSubmissionLevel);
  }, [triggerItems]);

  return {
    submissionItems,
    locationItems,
    rule,
    perLocationTriggerItems,
    isPerLocationExpanded,
    setIsPerLocationExpanded,
  };
};
