import React from "react";
import { RecommendationsPanelOpenSingleGuidelineEvent } from "@features/recommendations-panel/types";
import { useNewSingleGuideContentData } from "@features/recommendations-panel/panels/useNewSingleGuideContentData";
import {
  Box,
  Collapse,
  Divider,
  IconButton,
  Typography,
  useTheme,
} from "@mui/material";
import { ChevronDown } from "@ui-patterns/icons/chevron-down";
import { SubmissionLevelExplanations } from "@features/recommendations-panel/components/SubmissionLevelExplanations";
import { LocationsLevelExplanations } from "@features/recommendations-panel/components/LocationsLevelExplanations";
import { SingleLocationItem } from "@features/recommendations-panel/components/SingleLocationItem";

type NewSingleGuideContentProps = {
  data: RecommendationsPanelOpenSingleGuidelineEvent;
};

export const NewSingleGuideContent = ({ data }: NewSingleGuideContentProps) => {
  const theme = useTheme();
  const {
    submissionItems,
    locationItems,
    rule,
    perLocationTriggerItems,
    isPerLocationExpanded,
    setIsPerLocationExpanded,
  } = useNewSingleGuideContentData(data);

  return (
    <Box>
      <Box display="flex" flexDirection="column" gap={1}>
        <SubmissionLevelExplanations
          items={submissionItems}
          ruleId={rule?.id}
        />
        <LocationsLevelExplanations items={locationItems} ruleId={rule?.id} />
      </Box>
      {perLocationTriggerItems.length > 0 && (
        <Box mt={3}>
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            sx={{
              p: 1,
              "&:hover": {
                backgroundColor: theme.palette.grey[50],
                cursor: "pointer",
              },
              "&:active": {
                backgroundColor: theme.palette.grey[100],
              },
            }}
            onClick={() => setIsPerLocationExpanded(!isPerLocationExpanded)}
          >
            <Typography
              variant="subtitle1"
              sx={{
                textTransform: "uppercase",
                fontWeight: 500,
                fontSize: 14,
                lineHeight: 1.4,
                mb: 0.5,
              }}
            >
              Locations
            </Typography>
            <IconButton>
              <ChevronDown
                sx={{
                  transform: isPerLocationExpanded
                    ? "rotate(180deg)"
                    : "rotate(0deg)",
                }}
              />
            </IconButton>
          </Box>
          <Divider />
          <Collapse in={isPerLocationExpanded}>
            <Box display="flex" flexDirection="column" gap={2} mt={2}>
              {perLocationTriggerItems.map((item) => {
                return (
                  <SingleLocationItem
                    item={item}
                    key={item.submissionBusiness.id}
                  />
                );
              })}
            </Box>
          </Collapse>
        </Box>
      )}
    </Box>
  );
};
