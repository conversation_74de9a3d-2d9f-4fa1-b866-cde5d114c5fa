import { AnyRecommendationOutcome } from "@features/recommendations-panel/types";
import type {
  RecommendationResponse,
  RuleResponseLite,
  VariableResponse,
} from "@legacy/api_clients/recommendations-client";
import truncate from "lodash/truncate";
import { CONDITION_NAME_DICTIONARY } from "./constants";
import isNil from "lodash/isNil";
import capitalize from "lodash/capitalize";

export const getRuleName = (
  rule?: RuleResponseLite | null,
  options: Parameters<typeof truncate>[1] = {
    length: 20,
  }
): string => {
  return truncate(rule?.name ?? "...", options);
};

export const getAllOutcomes = (
  recommendation: RecommendationResponse | undefined
): AnyRecommendationOutcome[] => {
  return [
    recommendation?.recommendation_outcomes ?? [],
    recommendation?.text_outcomes ?? [],
    recommendation?.refer_outcomes ?? [],
    recommendation?.submission_outcomes ?? [],
    recommendation?.assign_underwriter_outcomes ?? [],
    recommendation?.flag_missing_documents_outcomes ?? [],
    recommendation?.attach_files_outcomes ?? [],
    recommendation?.send_email_outcomes ?? [],
    recommendation?.score_outcomes ?? [],
  ].flat();
};

export const getOutcomeAndExplanation = (
  recommendation: RecommendationResponse | undefined,
  outcomeId: string,
  outcomeExplanationId: string
) => {
  const outcome: AnyRecommendationOutcome | undefined = getAllOutcomes(
    recommendation
  ).find((o) => o.id === outcomeId);
  const outcomeExplanation = outcome?.outcome_explanations?.find(
    (oe) => oe.id === outcomeExplanationId
  );
  return { outcome, outcomeExplanation };
};

export const getConditionName = (conditionCodeName?: string | null) => {
  if (!conditionCodeName) {
    return "";
  }
  return (
    CONDITION_NAME_DICTIONARY[conditionCodeName] ||
    conditionCodeName.replace(/_/g, " ")
  );
};

const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 0,
});

export const formatVariableItem = (item: any, variable: VariableResponse) => {
  if (isNil(item)) {
    return "";
  }
  let formatter: (val: any) => string = (val: any) => String(val);
  if (["USD", "$"].includes(variable.unit?.toUpperCase() ?? "")) {
    formatter = (val: any) => currencyFormatter.format(val);
  } else if (["FEET"].includes(variable.unit?.toUpperCase() ?? "")) {
    formatter = (val: any) => {
      const feet = Number(val);
      if (isNaN(feet)) {
        return String(val);
      }
      return `${feet} ft`;
    };
  } else if (variable.unit) {
    formatter = (val: any) =>
      `${String(val)} ${capitalize(variable.unit ?? "")}`;
  }
  if (Array.isArray(item)) {
    if (item.length > 1) {
      return truncate(item.map(formatter).join(", "), { length: 100 });
    } else {
      return formatter(item[0]);
    }
  } else {
    return formatter(item);
  }
};
