import { Box, GlobalStyles, Stack, Typography } from "@mui/material";
import { useRouter } from "next/router";
import React, { useEffect, useMemo, useRef, useState } from "react";

import { useSubmissionBladesStore } from "@features/blades/submissionBladesStore";
import { QueryEventsHandler } from "@features/display-report/components/QueryEventsHandler";
import { FactPanel } from "@features/fact-panel";
import { useConflictingFacts } from "@features/fact-panel/conflictingSubmissionFactsStore";
import { DisplayMode } from "@features/modes-core";
import { MainViewVisibility } from "@features/modes-core/components/MainViewVisiblity";
import { ModeSidebar } from "@features/modes-core/components/ModeSidebar";
import { ModeStructure } from "@features/modes-core/components/ModeStructure";
import { FactAndDocumentVisibilityResolver } from "@features/modes-core/context/FactAndDocumentVisibilityResolver";
import { useModeState } from "@features/modes-core/context/useModeState";
import { LimitedDisplayMode } from "@features/modes-core/LimitedDisplay";
import { Navigator } from "@features/navigator";
import { PdsStuckDialog } from "@features/pds-stuck-dialog";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { RecommendationContextProvider } from "@features/recommendation/hooks/useRecommendationData";
import { RecommendationsPanel } from "@features/recommendations-panel";
import { routes } from "@features/routes";
import { SubmissionOverview } from "@features/submission-overview";
import { ClearingIssuesBanner } from "@features/submission-overview/components/ClearingIssuesBanner";
import { ShadowBanner } from "@features/submission-overview/components/ShadowBanner";
import { VerifySubmissionBanner } from "@features/submission-overview/components/VerifySubmissionBanner";
import { useReportQuery } from "@queries/report";
import {
  trackReportViewedFromNotesEmail,
  trackSubmissionPageView,
} from "@utils/amplitude";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { useSnackbar } from "notistack";
import { usePublishReportOpenedEvent } from "@queries/events";
import { HazardHubContextProvider } from "@features/HazardHubContext";
import { FactGroupsProvider } from "@features/dashboards/fact-groups-store";
import { useIsNWVerifiedShell } from "@legacy/hooks/useIsNWVerifiedShell";
import { InfoPanel } from "features/info-panel";
import { useIsDocumentIngestion } from "@features/files/hooks/useIsDocumentIngestion";
import { useWatchSubmissionReprocessing } from "@features/display-report/hooks";
import { ReportMetadataResolver } from "@features/metadata";
import { BrokersPanel } from "@features/dashboards/side-panels/brokers-panel";
import { useParagonVerifiedShell } from "@legacy/hooks/useParagonVerifiedShell";
import { StructuresContextProvider } from "@features/modes-display/entity-picker/components/StructuresContextProvider";

export const DisplayReport = () => {
  const [showStuckModal, setShowStuckModal] = useState(false);
  const router = useRouter();
  const user = useCurrentUser();
  const { supportFlow, imsRetryEnabled } = useFeatureFlags();
  const pageVisitTracked = useRef(false);

  const reportId = router.query.reportId as string;
  const openNotes = router.query.openNotes as string;

  const { isCSManager } = useAccessPermissions();
  const [refetchInterval, setRefetchInterval] = useState<number>();

  const { data } = useReportQuery({ id: reportId, refetchInterval });

  useEffect(() => {
    if (!data?.is_user_waiting_for_shadow) setRefetchInterval(undefined);
    else setRefetchInterval(30_000);
  }, [data?.is_user_waiting_for_shadow]);
  useWatchSubmissionReprocessing(data);

  const isNWVerifiedShell = useIsNWVerifiedShell(data, true);
  const { isParagonVerifiedShell } = useParagonVerifiedShell(data);

  const orgId = useOrganizationId();
  const { enqueueSnackbar } = useSnackbar();
  const isDocumentIngestion = useIsDocumentIngestion();

  const submissionId = data?.getSubmission().id;
  const { open: openBlade } = useSubmissionBladesStore();
  const { mutate: publishReportOpenedEvent } = usePublishReportOpenedEvent();
  useConflictingFacts(data!);

  useEffect(() => {
    if (submissionId) {
      publishReportOpenedEvent(submissionId);
    }
  }, [submissionId, publishReportOpenedEvent]);

  useEffect(() => {
    if (data?.organization_id && orgId && data?.organization_id !== orgId) {
      enqueueSnackbar("You don't have access to this report");
      router.push(routes.hub());
    }
  }, [data?.organization_id, orgId, router, enqueueSnackbar]);

  const submission = data?.getSubmission();
  useEffect(() => {
    if (!!submission?.stuck_reason && supportFlow) {
      setShowStuckModal(true);
    }
  }, [submission?.stuck_reason, supportFlow]);

  useEffect(() => {
    if (data && openNotes) {
      openBlade({
        containerId: "report-wrapper",
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        isReferring: false,
        items: [{ type: "notes" }],
        reportId: data.id,
        preventCloseOnClickOutside: true,
      });
      const { pathname, query } = router;
      delete router.query.openNotes;
      router.replace({ pathname, query }, undefined, { shallow: true });
      trackReportViewedFromNotesEmail(data, {});
    }
  }, [openNotes, data, openBlade, router]);

  useEffect(() => {
    useModeState.getState().cleanUnsafeHiddenCards();
  }, [reportId]);

  useEffect(() => {
    if (pageVisitTracked.current || !data) return;

    const { navigationSource } = router.query;
    const from = (navigationSource as string | undefined) ?? "url";

    if (typeof navigationSource === "string") {
      router.replace(
        router.asPath.replace(
          `navigationSource=${navigationSource.replace(" ", "+")}`,
          ""
        )
      );
    }
    const currentPage =
      router.asPath.split("/").filter(Boolean).pop() ?? "overview";

    trackSubmissionPageView(data, {
      report: data,
      from,
      page: currentPage,
    });

    pageVisitTracked.current = true;

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const verifiedShellMessage = data?.getVerifiedShellMessage() ?? null;

  const shouldDisplayVerifiedShellMessage =
    !!verifiedShellMessage && !isCSManager;

  const displayClearingIssuesBanner = useMemo(() => {
    if (!data || !user) return false;

    if (user.applicable_settings.is_clearing_enabled && !data.isCleared()) {
      return true;
    }
    if (
      user.applicable_settings.is_light_clearing_enabled &&
      !data.isLightCleared()
    ) {
      return true;
    }

    return false;
  }, [data, user]);

  if (
    shouldDisplayVerifiedShellMessage &&
    !imsRetryEnabled &&
    !isNWVerifiedShell &&
    !isParagonVerifiedShell
  ) {
    return (
      <Box width="100%" mt={8} mb={4} display="flex" justifyContent="center">
        <Stack direction="column" spacing={1}>
          <Typography variant="h6" mt={2}>
            {verifiedShellMessage}
          </Typography>
        </Stack>
      </Box>
    );
  }

  const displayLimitedAccessView =
    (shouldDisplayVerifiedShellMessage && imsRetryEnabled) ||
    isNWVerifiedShell ||
    isDocumentIngestion;

  const displayVerifySubmissionBanner =
    supportFlow && data && data.getSubmission().is_verification_required;
  const displayShadowBanner = isCSManager && data;

  return (
    <RecommendationContextProvider>
      <HazardHubContextProvider>
        <FactGroupsProvider>
          <ReportMetadataResolver>
            <Box
              display="flex"
              flexDirection="row"
              sx={{ backgroundColor: "white" }}
              flex="1 1 0"
              overflow="hidden"
            >
              <ModeSidebar limitedAccessView={displayLimitedAccessView} />
              {showStuckModal && (
                <PdsStuckDialog
                  report={data!}
                  onClose={() => setShowStuckModal(false)}
                />
              )}
              <Box
                height="100%"
                width="100%"
                minWidth={0}
                display="flex"
                flexDirection="column"
                flexShrink={1}
                // LOOK OUT! This key here is pretty important. It forces react
                // to unmount whole section and remount it again when report is changing
                // thanks to that any state saved inside these components is reset
                // It causes small performance drop (whole view is trashed and recreated)
                // but it makes sure that we don't leak state between different reports
                key={reportId}
                sx={{
                  ["& > :not(:last-child)"]: {
                    flex: "0 0 0",
                  },
                }}
              >
                {shouldDisplayVerifiedShellMessage &&
                  imsRetryEnabled &&
                  !isParagonVerifiedShell && (
                    <Box
                      width="100%"
                      mt={8}
                      mb={4}
                      display="flex"
                      justifyContent="center"
                    >
                      <Stack direction="column" spacing={1}>
                        <Typography variant="h6" mt={2}>
                          Submission is triaged out, some report functionality
                          is limited.
                        </Typography>
                      </Stack>
                    </Box>
                  )}
                {displayClearingIssuesBanner && (
                  <ClearingIssuesBanner report={data!} />
                )}
                {displayVerifySubmissionBanner && (
                  <VerifySubmissionBanner report={data} />
                )}
                {displayShadowBanner && <ShadowBanner report={data} />}
                <SubmissionOverview />
                <Box
                  overflow="auto"
                  flexGrow={1}
                  id="report-wrapper"
                  position="relative"
                >
                  {displayLimitedAccessView ? (
                    <LimitedDisplayMode
                      filesOnly={isNWVerifiedShell || isDocumentIngestion}
                    />
                  ) : (
                    <DisplayMode />
                  )}
                </Box>
              </Box>
              <FactAndDocumentVisibilityResolver />
              <ModeStructure />
              <Navigator />
              <MainViewVisibility />
              <Fun />

              {/* Hiding tooltip doesn't destroy the div container and it prevents hover over other components */}
              <GlobalStyles
                styles={{
                  ".highcharts-tooltip-container": {
                    "pointer-events": "none !important",
                  },
                }}
              />

              <FactPanel />
              <RecommendationsPanel />
              <InfoPanel />
              <BrokersPanel />
              <QueryEventsHandler />
              <StructuresContextProvider />
            </Box>
          </ReportMetadataResolver>
        </FactGroupsProvider>
      </HazardHubContextProvider>
    </RecommendationContextProvider>
  );
};

const Fun = () => {
  // const modeState = useModeState();
  // const visibilityState = useVisibilityState();
  // const capabilityState = useCapabilityState();

  // Add your console log statement here

  return null;
};
