import { useMaybeCurrentUser } from "@utils/auth";
import { useGetCurrentSupportUser } from "@queries/submissionAssignment";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import React, { useEffect, useState } from "react";
import { routes } from "@features/routes";
import { useRouter } from "next/router";
import { EmptyState } from "@ui-patterns/full-page-state";
import { useReport } from "@utils/useReport";

const QUALITY_AUDIT_ORGANIZATION_ID = 53;

type CheckSupportAssignedReportsProps = {
  children: React.ReactNode;
};

export const CheckSupportAssignedReports = ({
  children,
}: CheckSupportAssignedReportsProps) => {
  const user = useMaybeCurrentUser();
  const router = useRouter();
  const { data: currentSupportUser } = useGetCurrentSupportUser();
  const { isCSManager } = useAccessPermissions();
  const reportId = router?.query?.reportId as string;
  const report = useReport();

  const [showNotAssignedError, setShowNotAssignedError] = useState(false);

  useEffect(() => {
    if (
      !!currentSupportUser &&
      !isCSManager &&
      !currentSupportUser.can_assign_naics &&
      user?.organization?.id !== QUALITY_AUDIT_ORGANIZATION_ID &&
      !report?.isVerified()
    ) {
      const assignedReportsIds = currentSupportUser.assigned_reports?.map(
        (r) => r.report_id
      );
      if (!assignedReportsIds?.includes(reportId)) {
        setShowNotAssignedError(true);
      }
    }
  }, [
    currentSupportUser,
    isCSManager,
    reportId,
    user?.organization?.id,
    report,
  ]);

  if (showNotAssignedError) {
    return (
      <EmptyState
        title="You are not allowed to work on this report."
        subtitle="Check the one currently assigned to you. If you think this is a mistake, please contact engineering."
        action={{
          label: "Go back to Hub",
          onClick: () => router.push(routes.hub()),
        }}
      />
    );
  }

  return <>{children}</>;
};
