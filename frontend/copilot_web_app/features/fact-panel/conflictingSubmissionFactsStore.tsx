import {
  Fact,
  FirstPartySource,
  Observation,
} from "@legacy/api_clients/facts_api_client";
import { ParentType } from "@legacy/api_clients/recommendations-client";
import Report from "@legacy/models/Report";
import RelationshipsObservation from "@legacy/models/observation/RelationshipsObservation";
import { useGetFactsWithConflictingObservationsQuery } from "@queries/facts";
import orderBy from "lodash/orderBy";
import { useCallback, useEffect, useMemo } from "react";
import { create } from "zustand";
import { useStructureFacts } from "@features/reusable-queries/structures";
import { FACT_TYPES } from "@legacy/constants/facts";

type Store = {
  conflictingFacts: Fact[];
  structureIdToPremId: Record<string, string>;

  setConflictingFacts: (facts: Fact[]) => void;
  setStructureIdToPremId: (data: Record<string, string>) => void;
};

const useConflictingSubmissionFactsStore = create<Store>((set) => ({
  conflictingFacts: [],
  structureIdToPremId: {},

  setConflictingFacts: (facts) => set({ conflictingFacts: facts }),
  setStructureIdToPremId: (data) => set({ structureIdToPremId: data }),
}));

export const useConflictingFacts = (report: Report) => {
  const {
    premisesFacts: { data: structures, isLoading: isLoadingStructures },
  } = useStructureFacts();

  const structureIdToPremId = useMemo(() => {
    const res: Record<string, string> = {};
    structures.forEach((s) => {
      (s.observation as RelationshipsObservation).children.forEach((c) => {
        res[c.remote_id] = s.parent_id;
      });
    });
    return res;
  }, [structures]);

  const { data } = useGetFactsWithConflictingObservationsQuery(
    {
      submissionId: report.submission.id,
      organizationId: report.organization_id,
      parentIds: {
        parent_ids: [...report.getBusinessIds(), ...report.getPremisesIds()],
      },
    },
    !isLoadingStructures
  );

  const setConflictingFacts = useConflictingSubmissionFactsStore(
    (state) => state.setConflictingFacts
  );

  const setStructureIdToPremId = useConflictingSubmissionFactsStore(
    (state) => state.setStructureIdToPremId
  );

  useEffect(() => {
    const relevant = data?.filter((x) => {
      if (x.observation?.fact_type_id !== "BINARY_CLASSIFICATION") return true;

      // Don't show conflicts with unknown values
      return x.observation?.conflicting_observations?.some(
        (x: any) => x.predicted_class_id
      );
    });

    setConflictingFacts(
      orderBy(
        relevant,
        (x) =>
          (x.observation?.source as FirstPartySource)?.submission_id !==
          report.submission.id
      ) ?? []
    );
  }, [data, setConflictingFacts, report]);
  useEffect(() => {
    setStructureIdToPremId(structureIdToPremId);
  }, [structureIdToPremId, setStructureIdToPremId]);
};

type ConflictingFactLookupParams = {
  factSubtypeId?: string;
  factSubtypeIds?: string[];
  parentId?: string;
  parentIds?: string[];
  parentType?: ParentType;
};

export const useConflictingFactsLookup = (include3rdPartyFacts = true) => {
  const conflictingFacts = useConflictingSubmissionFactsStore(
    (state) => state.conflictingFacts
  );
  const structureIdToPremId = useConflictingSubmissionFactsStore(
    (state) => state.structureIdToPremId
  );

  const getConflictingFacts = useCallback(
    ({
      factSubtypeId,
      factSubtypeIds,
      parentId,
      parentIds,
      parentType,
    }: ConflictingFactLookupParams) => {
      const usedFactSubtypeIds =
        factSubtypeIds ?? (factSubtypeId ? [factSubtypeId] : []);
      const usedParentIds = parentIds ?? (parentId ? [parentId] : []);

      const include3rdPartyForFact = (fact: Fact) => {
        return [
          FACT_TYPES.BINARY_CLASSIFICATION,
          FACT_TYPES.INTEGER,
          FACT_TYPES.FLOAT_VALUE,
          FACT_TYPES.TIME_SERIES,
        ].includes(fact?.observation?.fact_type_id ?? "");
      };

      const usableConflictingFacts = include3rdPartyFacts
        ? conflictingFacts
        : conflictingFacts.filter(
            (fact) =>
              include3rdPartyForFact(fact) ||
              fact.observation?.conflicting_observations?.some(
                (co: Observation) => co.source?.source_type_id === "FIRST_PARTY"
              )
          );

      return usableConflictingFacts.filter((fact) => {
        if (
          usedFactSubtypeIds.length &&
          !usedFactSubtypeIds.includes(
            fact.observation?.fact_subtype_id as string
          )
        ) {
          return false;
        }

        if (
          usedParentIds.length &&
          !usedParentIds.includes(fact.parent_id as string)
        ) {
          return false;
        }

        if (parentType && fact.parent_type !== parentType) return false;

        return true;
      });
    },
    [conflictingFacts, include3rdPartyFacts]
  );

  const getConflictingFact = useCallback(
    (params: ConflictingFactLookupParams) => {
      return getConflictingFacts(params)[0];
    },
    [getConflictingFacts]
  );

  const getPremisesIdFromStructureId = useCallback(
    (structureId: string) => {
      return structureIdToPremId[structureId];
    },
    [structureIdToPremId]
  );

  return useMemo(() => {
    return {
      getConflictingFact,
      getConflictingFacts,
      getPremisesIdFromStructureId,
    };
  }, [getConflictingFact, getConflictingFacts, getPremisesIdFromStructureId]);
};
