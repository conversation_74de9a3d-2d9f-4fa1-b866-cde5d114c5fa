import styled from "@emotion/styled";
import { Chip, IconButton, Typography } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid/models/colDef/gridColDef";
import { useOrganizationId } from "@utils/auth";
import { useAdminGetUsersQuery, useGetUserGroupsQuery } from "@queries/user";
import React, { useEffect, useMemo, useRef, useState } from "react";
import ArrowRightIcon from "@mui/icons-material/ChevronRight";
import { AddCircleOutlineOutlined as AddCircleIcon } from "@mui/icons-material";
import { Box } from "@mui/system";
import { EditUserBlade } from "./components/EditUserBlade";
import { NewUserDialog } from "@features/settings-page/components/NewUserDialog";
import { DataGrid } from "@ui-patterns/data-grid";
import { DebouncedSearchField } from "@ui-patterns/card-table/components/DebouncedSearchField";
import { AdminUser } from "@legacy/api_clients/copilot_api_client";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { useRouter } from "next/router";
import { queryKeyFactory } from "@queries/queryKey";
import { useQueryClient } from "@tanstack/react-query";
import { Tabs } from "@ui-patterns/tabs";
import { useSubmissionBladesStore } from "@features/blades/submissionBladesStore";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const usersColumns: GridColDef[] = [
  {
    field: "userName",
    headerName: "User Name",
    minWidth: 300,
    resizable: true,
    renderCell: (params) => {
      return params.row.isEnabled ? (
        <div>{params.row.userName}</div>
      ) : (
        <div style={{ color: "#9fa4ab" }}>{params.row.userName} (inactive)</div>
      );
    },
  },
  {
    field: "email",
    headerName: "Email",
    minWidth: 300,
    resizable: true,
    renderCell: (params) => {
      return (
        <div style={{ color: params.row.isEnabled ? "unset" : "#9fa4ab" }}>
          {params.row.email}
        </div>
      );
    },
  },
  {
    field: "role",
    headerName: "Role",
    minWidth: 150,
    flex: 1,
    resizable: true,
    renderCell: (params) => {
      return (
        <div style={{ display: "flex", alignItems: "center" }}>
          <div
            style={{
              width: "60px",
              color: params.row.isEnabled ? "unset" : "#9fa4ab",
            }}
          >
            {params.row.role}
          </div>
          <IconButton sx={{ ml: 2 }}>
            <ArrowRightIcon onClick={params.row.onClick} />
          </IconButton>
        </div>
      );
    },
  },
];

const groupColumns: GridColDef[] = [
  {
    field: "name",
    headerName: "Group Name",
    resizable: true,
    minWidth: 300,
  },
  {
    field: "users",
    headerName: "Users",
    resizable: true,
  },
  {
    field: "actions",
    headerName: "",
    resizable: false,
    minWidth: 100,

    renderCell: ({ row }) => {
      return (
        <IconButton sx={{ ml: 2 }}>
          <ArrowRightIcon onClick={row.onClick} />
        </IconButton>
      );
    },
  },
];

const GridContainer = styled.div({
  marginTop: "5em",
  width: "100%",
});

export const UsersManagement = () => {
  const { masterAccount } = useFeatureFlags();
  const router = useRouter();
  const [activeUser, setActiveUser] = useState<AdminUser>();
  const [searchText, setSearchText] = useState("");
  const tableWrapperRef = useRef<HTMLElement>(undefined);
  const { top } = tableWrapperRef.current?.getBoundingClientRect() ?? {};
  const orgId = useOrganizationId();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [newUserModalIsOpen, setNewUserModalIsOpen] = useState<boolean>();
  const queryClient = useQueryClient();
  const { open: openBlade } = useSubmissionBladesStore();

  const [activeTab, setActiveTab] = useState<"users" | "groups">("users");
  const { isCSManager } = useAccessPermissions();
  const excludeCrossOrgUsers = !isCSManager;

  const { data: userData, isLoading: isLoadingUsers } = useAdminGetUsersQuery(
    orgId,
    excludeCrossOrgUsers
  );
  const { data: groups, isLoading: isLoadingGroups } =
    useGetUserGroupsQuery(orgId);

  useEffect(() => {
    if (userData) {
      setUsers(userData);
    }
  }, [userData]);

  const onUserUpdate = (updatedUser: AdminUser) => {
    const outdatedUserIndex =
      users?.findIndex((user) => user.id === updatedUser.id) ?? -1;
    if (users && outdatedUserIndex > -1) {
      users[outdatedUserIndex] = updatedUser;
      setUsers([...users]);
    }
    setActiveUser(updatedUser);
  };

  const userRows = useMemo(() => {
    const userRows =
      users?.map((user) => ({
        id: user.id,
        userName: user.name,
        email: user.email,
        role: user.role,
        isEnabled: user.is_enabled,
        onClick: () => setActiveUser(user),
      })) ?? [];

    userRows.sort((user) => (user.isEnabled ? -1 : 1));

    return userRows;
  }, [users]);

  const groupRows = useMemo(() => {
    return (
      groups?.map((group) => ({
        id: group.id,
        name: group.name,
        users: group.users?.length,
        onClick: () => {
          openBlade({
            containerId: "table-wrapper",
            // @ts-expect-error fix this
            editingGroupId: group.id,
            items: [
              {
                type: "addOrEditUserGroup",
                title: group.name,
              },
            ],
          });
        },
      })) ?? []
    );
  }, [groups, openBlade]);

  if (!masterAccount) {
    router.push(`/`);
  }

  const onNewUserDialogClose = (createdNewUsers: boolean) => {
    if (createdNewUsers) {
      queryClient.invalidateQueries(
        queryKeyFactory.adminGetUsers(orgId, excludeCrossOrgUsers)
      );
    }
    setNewUserModalIsOpen(false);
  };

  const showNewUserGroupBlade = () => {
    // @ts-expect-error fix this
    openBlade({
      containerId: "table-wrapper",
      items: [
        {
          type: "addOrEditUserGroup",
        },
      ],
    });
  };

  return (
    <GridContainer>
      <div
        style={{
          width: "90%",
          marginLeft: "5em",
          marginRight: "5em",
        }}
      >
        <Typography variant="h4">Users</Typography>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            width: "95%",
            marginTop: "3em",
            marginBottom: "3em",
            marginLeft: "auto",
            marginRight: "auto",
          }}
        >
          <DebouncedSearchField
            onChange={setSearchText}
            initialValue={searchText}
            placeholder="Search by name or email"
            label="Search"
            sx={{ width: "30%" }}
          />

          <Box>
            <Chip
              label="Add User"
              deleteIcon={<AddCircleIcon />}
              onDelete={() => setNewUserModalIsOpen(true)}
              onClick={() => setNewUserModalIsOpen(true)}
            />

            <Chip
              label="Add Group"
              deleteIcon={<AddCircleIcon />}
              onDelete={showNewUserGroupBlade}
              onClick={showNewUserGroupBlade}
              sx={{ ml: 1 }}
            />
          </Box>

          {newUserModalIsOpen && (
            <NewUserDialog onDialogClose={onNewUserDialogClose} />
          )}
        </div>
      </div>

      <Tabs
        id="settings-tabs"
        tabs={[
          // @ts-expect-error fix this
          {
            name: "users",
            label: `Users (${users.length})`,
            children: (
              <Box
                ref={tableWrapperRef}
                sx={{ position: "relative" }}
                id="table-wrapper"
              >
                <DataGrid
                  trackingTableType="user management"
                  loading={isLoadingUsers}
                  searchText={searchText}
                  height={600}
                  columns={usersColumns}
                  rows={userRows}
                />
              </Box>
            ),
          },
          // @ts-expect-error fix this
          {
            name: "groups",
            label: `Groups (${groups?.length ?? 0})`,
            children: (
              <Box
                ref={tableWrapperRef}
                sx={{ position: "relative" }}
                id="table-wrapper"
              >
                <DataGrid
                  trackingTableType="user group management"
                  loading={isLoadingGroups}
                  searchText={searchText}
                  height={600}
                  columns={groupColumns}
                  rows={groupRows}
                />
              </Box>
            ),
          },
        ]}
        ariaLabel="Users and Groups"
        value={activeTab}
        onChange={(tab) => setActiveTab(tab as "users" | "groups")}
      />

      {activeUser && (
        <Box
          sx={{
            position: "fixed",
            right: 0,
            top: top,
            height: "100%",
            zIndex: 101,
          }}
        >
          <EditUserBlade
            storedUser={activeUser}
            onSuccessfulUpdate={onUserUpdate}
            onClose={() => setActiveUser(undefined)}
          />
        </Box>
      )}
    </GridContainer>
  );
};
