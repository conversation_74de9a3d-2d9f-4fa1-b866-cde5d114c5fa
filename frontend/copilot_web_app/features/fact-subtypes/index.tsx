import {
  <PERSON><PERSON>ple<PERSON>,
  <PERSON>,
  <PERSON>Field,
  <PERSON><PERSON><PERSON>,
  Typography,
} from "@mui/material";
import React, { useMemo } from "react";
import { useFactSubtypes } from "@legacy/hooks/useFactSubtypes";
import FactSubtype from "@legacy/models/FactSubtype";
import uniqBy from "lodash/uniqBy";
import { PARENT_TYPES } from "@legacy/constants/facts";
import { SubmissionEntityTypeEnum } from "@legacy/api_clients/copilot_api_client";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { OnboardingFactSubtypeSuggestion } from "@features/product-driven-support/onbording-data/mapOnboardingData";
import { MULTI_PARENT_SUBTYPES } from "@features/data-onboarding/fields-mapping/parts/FactSubtypePicker";

const PREMISES_LIKE_PARENT_TYPES = [
  PARENT_TYPES.STRUCTURE,
  PARENT_TYPES.BUSINESS,
  PARENT_TYPES.PREMISES,
];

const ENTITY_TYPE_TO_PARENT_TYPE = {
  Vehicle: PARENT_TYPES.VEHICLE,
  Driver: PARENT_TYPES.DRIVER,
  Equipment: PARENT_TYPES.EQUIPMENT,
};

export const FactSubtypesDropdown = ({
  submissionId,
  value,
  onChange,
  label = "Copilot Fact",
  entityType,
  factSubtypeSuggestions,
}: {
  submissionId: string;
  value?: string | null;
  onChange: (id: string, factSubtype: FactSubtype | null) => void;
  label?: string;
  entityType?: SubmissionEntityTypeEnum;
  factSubtypeSuggestions?: OnboardingFactSubtypeSuggestion[];
}) => {
  const { isCSManager } = useAccessPermissions();
  const { data: factSubtypes, isLoading } = useFactSubtypes(
    submissionId,
    undefined,
    true
  );

  const explanationBySuggestedSubtype = useMemo(() => {
    return (
      factSubtypeSuggestions?.reduce(
        (acc, s) => {
          if (
            s.factSubtypeId &&
            !acc[s.factSubtypeId] &&
            s.explanations?.length
          ) {
            acc[s.factSubtypeId] = s.explanations[0];
          }
          return acc;
        },
        {} as Record<string, string>
      ) ?? {}
    );
  }, [factSubtypeSuggestions]);

  const options = useMemo(() => {
    const entityTypeToFilter = (factSubtype: FactSubtype) => {
      if (
        !entityType ||
        (MULTI_PARENT_SUBTYPES.includes(factSubtype.id) &&
          !["Vehicle", "Driver"].includes(entityType))
      )
        return true;
      if (entityType in ENTITY_TYPE_TO_PARENT_TYPE) {
        return (
          factSubtype.default_parent_type ===
          ENTITY_TYPE_TO_PARENT_TYPE[
            entityType as keyof typeof ENTITY_TYPE_TO_PARENT_TYPE
          ]
        );
      }
      return PREMISES_LIKE_PARENT_TYPES.includes(
        factSubtype.default_parent_type as PARENT_TYPES
      );
    };

    const uniqueFactSubtypes =
      uniqBy(
        factSubtypes?.filter((fs) => entityTypeToFilter(fs)),
        "display_name"
      ) ?? [];
    return (
      uniqueFactSubtypes.sort((a, b) =>
        a.display_name?.localeCompare(b.display_name ?? "") &&
        a.id in explanationBySuggestedSubtype
          ? -1
          : 1
      ) ?? []
    );
  }, [factSubtypes, entityType, explanationBySuggestedSubtype]);

  if (isLoading) return null;

  const autocompleteValue = options.find((o) => o.id === value);
  const isDisabledVinField =
    autocompleteValue?.id === "VEHICLE_INFORMATION_NUMBER" && !isCSManager;
  return (
    <Autocomplete
      options={options}
      value={autocompleteValue}
      loading={isLoading}
      limitTags={1}
      autoHighlight
      disabled={isDisabledVinField}
      sx={{ width: 320 }}
      onChange={(_, v: FactSubtype | null) => {
        onChange(v?.id as string, v);
      }}
      getOptionLabel={(option) => option.display_name ?? ""}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          size="small"
          helperText={
            isDisabledVinField ? "Changing VIN is not allowed" : undefined
          }
        />
      )}
      renderOption={(props, option) => (
        <Tooltip
          title={
            option.id in explanationBySuggestedSubtype
              ? explanationBySuggestedSubtype[option.id]
              : ""
          }
        >
          <Box
            component="li"
            {...props}
            style={{
              display: "block",
            }}
          >
            <Typography
              variant="body2"
              sx={{
                fontWeight:
                  option.id in explanationBySuggestedSubtype
                    ? "bold"
                    : "normal",
              }}
            >
              {option.display_name}
            </Typography>
            {option.description &&
              option.display_name !== option.description && (
                <Typography variant="body2" color="gray">
                  {option.description}
                </Typography>
              )}
          </Box>
        </Tooltip>
      )}
    />
  );
};
