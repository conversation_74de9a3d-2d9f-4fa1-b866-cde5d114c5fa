import {
  Box,
  Chip,
  CircularProgress,
  InputAdornment,
  ListItemText,
  Menu,
  MenuItem,
  Tab,
  <PERSON>bs,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import React, { useCallback, useEffect, useMemo, type JSX } from "react";
import { useSearchQuery } from "@queries/search";
import SearchIcon from "@mui/icons-material/Search";
import { routes } from "@features/routes";
import { SearchReportHit } from "@legacy/api_clients/copilot_api_client";
import Highlighter from "react-highlight-words";
import { useCurrentUser } from "@utils/auth";
import { ReportStage } from "@legacy/constants/hubConstants";
import { format } from "date-fns";
import uniq from "lodash/uniq";
import { useSubmissionBladesStore } from "@features/blades/submissionBladesStore";
import { trackGlobalSearch } from "@utils/amplitude";
import { HubLink } from "@features/top-bar/components/HubLink";
import { useRouter } from "next/router";
import { STAGE_ID_TO_NAME } from "@utils/constants";
import { useClientStage } from "@legacy/hooks/useClientStage";
import { getDateWithoutTimezoneOffset } from "@utils/date";
import { useCopilotTabSettings } from "@features/hub/constants";
import { useParagonVerifiedShell } from "@legacy/hooks/useParagonVerifiedShell";
import { useReportQuery } from "@queries/report";
import { RenewalIcon } from "@ui-patterns/renewal-icon";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";

const SearchField = ({
  querySearch,
  setQuerySearch,
  setAnchorEl,
}: {
  querySearch: string;
  setQuerySearch: (a: string) => void;
  setAnchorEl: (el: HTMLElement | null) => void;
}): JSX.Element => {
  const theme = useTheme();

  return (
    <TextField
      sx={{
        width: "80%",
        minWidth: "300px",
        maxWidth: "800px",
        maxHeight: "100%",
        fieldset: {
          backgroundColor: theme.colors.grayBackground,
          border: "none",
          zIndex: -1,
          maxHeight: "100%",
          borderRadius: querySearch ? "8px 8px 0px 0px" : "8px",
        },
      }}
      value={querySearch}
      id={"search"}
      InputProps={{
        sx: {
          height: "32px",
        },
        startAdornment: (
          <InputAdornment position="start" sx={{ marginBottom: "3px" }}>
            <SearchIcon />
          </InputAdornment>
        ),
      }}
      onInput={(e) => setAnchorEl(e.currentTarget)}
      onChange={(e) => setQuerySearch(e.target.value.trimStart())}
    />
  );
};

const GroupHeader = ({ title }: { title: string }): JSX.Element => {
  return (
    <MenuItem
      sx={{
        padding: "8px 20px 8px 44px",
      }}
      key={`menuItem_${title}`}
    >
      <Typography sx={{ fontWeight: 700 }}>{title}</Typography>
    </MenuItem>
  );
};

const ItemField = ({
  fields,
  query,
  title,
  alwaysShown,
}: {
  fields?: (string | null | undefined)[] | null;
  query: string;
  title: string;
  alwaysShown?: boolean;
}): JSX.Element | null => {
  const matchingFields = useMemo(() => {
    if (!fields) return null;
    return fields.flatMap((f) =>
      f?.toLowerCase().includes(query.toLowerCase()) || (!!f && alwaysShown)
        ? f
        : []
    );
  }, [fields, query, alwaysShown]);

  if (!matchingFields?.length) return null;
  return (
    <Typography variant="body2" sx={{ maxWidth: "100%" }}>
      {title}
      <Highlighter
        searchWords={[query]}
        autoEscape
        textToHighlight={matchingFields[0]}
      />
      {matchingFields.slice(1).map((f) => (
        <Typography key={f} variant="body2" ml={7.5}>
          <Highlighter
            searchWords={[query]}
            autoEscape
            textToHighlight={f}
            key={f}
          />
        </Typography>
      ))}
    </Typography>
  );
};

const ReportItem = ({
  hit,
  query,
  handleClose,
  showClientSubmissionId = false,
}: {
  hit: SearchReportHit;
  query: string;
  handleClose: () => void;
  showClientSubmissionId?: boolean;
}): JSX.Element => {
  const user = useCurrentUser();
  const { open: openBlade } = useSubmissionBladesStore();
  const router = useRouter();

  const { clientStageEnabled } = useClientStage();

  const tabSettings = useCopilotTabSettings();

  const tab = useMemo(() => {
    return tabSettings.find((t) =>
      t.stages.map((x) => x.id).includes(hit.stage as ReportStage)
    )?.label;
  }, [hit, tabSettings]);

  const copilotStage =
    hit.stage && hit.stage in STAGE_ID_TO_NAME && STAGE_ID_TO_NAME[hit.stage];
  const clientStage = hit.client_stage_name;
  const stage = clientStageEnabled ? clientStage : copilotStage;

  let title = hit.name ?? "";
  if (stage) {
    title = `${hit.name} (${stage})`;
  }

  const effectiveDate = hit.effective_date
    ? format(getDateWithoutTimezoneOffset(hit.effective_date), "yyyy/MM/dd")
    : null;

  const isVerifiedShell = !!hit?.is_verified_shell;

  const shellSearchPhrase = `${hit.report_id}${hit.name}`;
  let href = routes.report(hit.report_id || "", "search results");

  const { data: report } = useReportQuery({
    id: hit.report_id!,
  });
  const { isParagonVerifiedShell } = useParagonVerifiedShell(report);

  if (isVerifiedShell && hit.report_id && !isParagonVerifiedShell) {
    const params: Record<string, string> = { searchPhrase: shellSearchPhrase };
    if (tab) {
      params["tab"] = tab;
    }
    href = routes.hub(true, params);
  }

  const onClick = async () => {
    await router.push(href);
    if (isVerifiedShell && hit.report_id && !isParagonVerifiedShell) {
      openBlade({
        items: [{ type: "submissionPreview", title: hit.name }],
        containerId: "hub-body",
        reportId: hit.report_id,
      });
    }
    handleClose();
  };

  return (
    <MenuItem>
      <Box
        sx={{ padding: "8px 20px 8px 60px", textDecoration: "none" }}
        onClick={onClick}
      >
        <Typography variant="subtitle2">
          <Highlighter
            autoEscape
            searchWords={[query]}
            textToHighlight={title}
          />
        </Typography>
        {hit.is_renewal && <RenewalIcon />}
        <ItemField
          fields={[effectiveDate]}
          query={query}
          title="Effective date: "
          alwaysShown
        />

        <ItemField
          fields={hit.assigned_underwriters}
          query={query}
          title="Assignee: "
        />
        <ItemField
          fields={[hit.brokerage]}
          query={query}
          title={`${user.getBrokerageLabel()}: `}
        />
        <ItemField fields={hit.locations} query={query} title="Locations: " />
        <ItemField
          fields={[hit.broker]}
          query={query}
          title={`${user.getBrokerLabel()}: `}
        />
        <ItemField
          fields={hit.external_ids}
          query={query}
          title={`${
            user.applicable_settings.submission_number_label ||
            "Submission Number"
          }: `}
        />
        <ItemField
          fields={[hit.policy_number]}
          query={query}
          title="Policy Number: "
        />
        <ItemField
          fields={[hit.quote_number]}
          query={query}
          title="Quote Number: "
        />
        <ItemField
          fields={[hit.primary_naics_code?.replace("NAICS_", "")]}
          query={query}
          title="NAICS: "
        />
        {showClientSubmissionId && hit.external_ids && (
          <Typography variant="body2" sx={{ mt: 0.5 }}>
            Submission Number:{" "}
            <Highlighter
              autoEscape
              searchWords={[query]}
              textToHighlight={hit.external_ids[0]}
            />
          </Typography>
        )}
      </Box>
    </MenuItem>
  );
};

export const Search = (): JSX.Element | null => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [querySearch, setQuerySearch] = React.useState<string>("");
  const [activeTab, setActiveTab] = React.useState<"active" | "archived">(
    "active"
  );
  const [queryFilter, setQueryFilter] = React.useState<string>("");

  const { showArchivedTabsInSearchBar } = useFeatureFlags();

  const tabHeaderItem = showArchivedTabsInSearchBar ? (
    <MenuItem
      disableRipple
      sx={{
        padding: "0 0 0 44px",
        "&.Mui-selected": {
          backgroundColor: "transparent",
        },
      }}
      key="searchTabHeader"
    >
      <Tabs
        value={activeTab}
        onChange={(_, value) => setActiveTab(value)}
        aria-label="Search Tabs"
      >
        <Tab value="active" label="Active" />
        <Tab value="archived" label="Archived" />
      </Tabs>
    </MenuItem>
  ) : null;

  React.useEffect(() => {
    if (showArchivedTabsInSearchBar) {
      const oneYearAgo = new Date(
        new Date().setFullYear(new Date().getFullYear() - 1)
      ).toISOString();

      const effectiveDateFilter =
        activeTab === "active"
          ? `(and effective_date:['${oneYearAgo}', '9999-12-31T23:59:59Z'])`
          : `(and effective_date:['0000-01-01T00:00:00Z', '${oneYearAgo}'])`;

      setQueryFilter(effectiveDateFilter);
    }
  }, [activeTab, showArchivedTabsInSearchBar]);

  const { data, isLoading } = useSearchQuery(querySearch, queryFilter);
  const theme = useTheme();

  const tabSettings = useCopilotTabSettings();

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, [setAnchorEl]);

  const duplicateNames = useMemo(() => {
    if (!data?.matches) return new Set<string>();

    const nameCounts = data.matches.reduce<Record<string, number>>(
      (acc, hit) => {
        if (hit.name) {
          acc[hit.name] = (acc[hit.name] || 0) + 1;
        }
        return acc;
      },
      {}
    );

    return new Set(
      Object.entries(nameCounts)
        .filter(([, count]) => count > 1)
        .map(([name]) => name)
    );
  }, [data]);

  const items: JSX.Element[] | null = useMemo(() => {
    if (!data && !querySearch) {
      setAnchorEl(null);
      return null;
    }
    if (!data?.matches?.length) {
      return [
        <MenuItem sx={{ padding: "8px 20px 8px 60px" }} key="noResultsMenuItem">
          <ListItemText>
            {querySearch?.length > 1
              ? "No results found."
              : "Search query needs to have at least 2 characters"}
          </ListItemText>
        </MenuItem>,
      ];
    }
    return data.matches.map((hit) => {
      return (
        <ReportItem
          query={querySearch.toLowerCase()}
          hit={hit}
          key={`reportItem_${hit.report_id}`}
          handleClose={handleClose}
          showClientSubmissionId={!!(hit.name && duplicateNames.has(hit.name))}
        />
      );
    });
  }, [data, querySearch, handleClose, duplicateNames]);

  const totalCount = useMemo(() => data?.found || 0, [data]);

  useEffect(() => {
    if (querySearch.length > 1) trackGlobalSearch(querySearch, totalCount);
  }, [querySearch, totalCount]);

  let searchResultsHeader = `Submissions (${totalCount})`;
  const searchLimit = 100;
  if (data?.found && data.found > searchLimit) {
    searchResultsHeader = `Submissions (${searchLimit} of ${data.found})`;
  }

  const tab = useMemo(() => {
    const stages = data?.matches?.map((r) => r.stage) || [];
    const tabs = stages.map(
      (s) =>
        tabSettings.find((t) =>
          t.stages.map((x) => x.id).includes(s as ReportStage)
        )?.label
    );
    const uniqueTabs = uniq(tabs.filter((x) => !!x));
    return uniqueTabs.length === 1 ? uniqueTabs[0] : undefined;
  }, [data, tabSettings]);

  return (
    <>
      <SearchField
        querySearch={querySearch}
        setQuerySearch={setQuerySearch}
        setAnchorEl={setAnchorEl}
      />
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        disableAutoFocus={true}
        PaperProps={{
          elevation: 0,
          sx: {
            overflow: "auto",
            width: anchorEl?.clientWidth,
            backgroundColor: "#F8F8F8",
            borderRadius: theme.spacing(0, 0, 1, 1),
            marginTop: theme.spacing(0.5),
            [theme.breakpoints.up("xl")]: {
              marginTop: 0,
            },
            ".MuiBox-root": {
              maxWidth: "100%",
              whiteSpace: "normal",
            },
          },
        }}
      >
        {isLoading && (
          <Box width="100%" display="flex" justifyContent="center">
            <CircularProgress size={20} />
          </Box>
        )}
        {!isLoading && [
          <Box key="menu-header" pr={2}>
            {tabHeaderItem}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <GroupHeader title={searchResultsHeader} />
              {!!data?.matches?.length && (
                <HubLink
                  newParams={{
                    searchPhrase: querySearch,
                    ...(tab ? { tab } : {}),
                  }}
                  onClick={handleClose}
                >
                  <Chip size="small" label="Show results in Hub" />
                </HubLink>
              )}
            </Box>
          </Box>,
          ...(items ?? []),
        ]}
      </Menu>
    </>
  );
};
