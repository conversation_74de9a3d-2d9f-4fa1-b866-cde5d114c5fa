import { <PERSON>, Divider, <PERSON>u, <PERSON>uI<PERSON>, <PERSON>ack, Typography } from "@mui/material";
import { useMaybeCurrentUser } from "@utils/auth";
import { Kalepa } from "@ui-patterns/icons/kalepa";
import { routes } from "@features/routes";
import React from "react";
import { useRouter } from "next/router";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { UrlObject } from "url";
import { useGetCurrentSupportUser } from "@queries/submissionAssignment";
import Link from "next/link";
import { useIsDocumentIngestion } from "@features/files/hooks/useIsDocumentIngestion";
import { useIsInboxFeatureAvailable } from "@utils/useIsInboxFeatureAvailable";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const NavigationLink = ({
  href,
  label,
  isSelected,
}: {
  href: string | UrlObject;
  label: string;
  isSelected?: boolean;
}) => {
  return (
    <Link
      href={href}
      style={{
        textDecoration: "none",
      }}
    >
      <FixedWidthLabel label={label} isSelected={isSelected} />
    </Link>
  );
};

const FixedWidthLabel = ({
  label,
  isSelected,
  fontWeight = 400,
  fontWeightOnHover = 600,
}: {
  label: string;
  isSelected?: boolean;
  fontWeight?: number;
  fontWeightOnHover?: number;
}) => {
  return (
    <Box>
      <Typography
        variant={"subtitle1"}
        fontSize={"16px"}
        fontWeight={isSelected ? fontWeightOnHover : fontWeight}
        sx={{
          cursor: isSelected ? "default" : "pointer",
          position: "relative",
          "&:hover": {
            fontWeight: fontWeightOnHover,
          },
        }}
      >
        {label}
      </Typography>
      <Typography
        variant={"subtitle1"}
        fontSize={"16px"}
        fontWeight={fontWeightOnHover}
        color={"transparent"}
        sx={{
          height: 0,
        }}
      >
        {label}
      </Typography>
    </Box>
  );
};

export const HubHeaderNavigation = ({}) => {
  const user = useMaybeCurrentUser();
  const router = useRouter();
  const { data: currentSupportUser } = useGetCurrentSupportUser();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const isSupport = user?.isSupport();
  const canAdjustEmailClassification = useIsInboxFeatureAvailable();
  const isDocumentIngestion = useIsDocumentIngestion();
  const { isCSManager } = useAccessPermissions();

  const showManagementDashboard =
    user?.applicable_settings?.show_management_dashboard ?? false;

  const portfolioItems: {
    title: string;
    href: UrlObject;
    isSelected: boolean;
  }[] = [];
  if (showManagementDashboard) {
    portfolioItems.push({
      title: "Dashboard",
      href: isDocumentIngestion
        ? routes.managementDashboard("document")
        : routes.managementDashboard(),
      isSelected:
        router.pathname === (routes.managementDashboard().pathname ?? ""),
    });
  }
  if (isDocumentIngestion) {
    portfolioItems.push({
      title: "Email Classification",
      href: routes.emailClassification(),
      isSelected:
        router.pathname === (routes.emailClassification().pathname ?? ""),
    });
  }

  if (user?.isManager() && !isDocumentIngestion) {
    portfolioItems.push({
      title: "Portfolio Manager",
      href: routes.portfolioManager.hub(),
      isSelected:
        router.pathname === (routes.portfolioManager.hub().pathname ?? ""),
    });
  }
  if (isDocumentIngestion) {
    portfolioItems.push({
      title: "Keys and Classifiers",
      href: routes.classifiers(),
      isSelected: router.pathname === (routes.classifiers().pathname ?? ""),
    });
    portfolioItems.push({
      title: "File Types",
      href: routes.fileTypes(),
      isSelected: router.pathname === (routes.fileTypes().pathname ?? ""),
    });
    portfolioItems.push({
      title: "Clearance Manager",
      href: routes.portfolioManager.hub(),
      isSelected:
        router.pathname === (routes.portfolioManager.hub().pathname ?? ""),
    });
  }

  const supportItems: { title: string; href: string; isSelected: boolean }[] =
    [];
  if (isCSManager) {
    const href = "/submission-queue";
    supportItems.push({
      title: "Submission Queue",
      href,
      isSelected: router.pathname.includes(href),
    });
  }

  if (isCSManager || !!currentSupportUser?.can_assign_naics) {
    const href = "/submission-naics-queue";
    supportItems.push({
      title: "NAICS Queue",
      href,
      isSelected: router.pathname.includes(href),
    });
  }

  return (
    <Stack
      pl={3}
      display="flex"
      direction="row"
      spacing={2}
      fontSize={"16px"}
      alignItems={"center"}
    >
      <Link
        href={routes.hub()}
        passHref
        style={{
          textDecoration: "none",
        }}
      >
        <Box
          display={"flex"}
          flexDirection={"row"}
          alignItems={"center"}
          sx={{ cursor: "pointer" }}
        >
          <Kalepa fontSize={"large"} />
          <Typography variant={"subtitle1"} pl={1}>
            COPILOT
          </Typography>
        </Box>
      </Link>
      <Divider orientation="vertical" flexItem />

      {(canAdjustEmailClassification || isDocumentIngestion) && (
        <NavigationLink
          href={"/inbox"}
          label="Inbox"
          isSelected={router.pathname === "/inbox"}
        />
      )}

      <NavigationLink
        href={routes.hub()}
        label="Hub"
        isSelected={router.pathname === "/"}
      />
      {!isSupport && (
        <NavigationLink
          href={routes.genericReport()}
          label="Risk Analyzer"
          isSelected={router.pathname.includes("report")}
        />
      )}
      {supportItems.map((item) => (
        <NavigationLink
          key={item.title}
          href={item.href}
          label={item.title}
          isSelected={item.isSelected}
        />
      ))}
      {portfolioItems.length === 1 && (
        <NavigationLink
          href={portfolioItems[0].href}
          label={isDocumentIngestion ? "Admin Tools" : "Portfolio"}
          isSelected={portfolioItems[0].isSelected}
        />
      )}
      {portfolioItems.length > 1 && (
        <>
          <Box
            display={"flex"}
            flexDirection={"row"}
            alignItems={"center"}
            onClick={handleClick}
          >
            <FixedWidthLabel
              label={isDocumentIngestion ? "Admin Tools" : "Portfolio"}
              isSelected={!!portfolioItems.find((i) => i.isSelected)}
            />
            {open ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </Box>
          <Menu
            anchorEl={anchorEl}
            transformOrigin={{
              vertical: "top",
              horizontal: "left",
            }}
            open={open}
            onClose={handleClose}
          >
            {portfolioItems.map((item) => (
              <MenuItem key={item.title}>
                <NavigationLink href={item.href} label={item.title} />
              </MenuItem>
            ))}
          </Menu>
        </>
      )}
    </Stack>
  );
};
