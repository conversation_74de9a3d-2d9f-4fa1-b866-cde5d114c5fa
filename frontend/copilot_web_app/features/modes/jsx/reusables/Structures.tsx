import { useRowContextEntities } from "@features/modes-display/utils";
import React, { useMemo, useState } from "react";
import { useCreateStructureSummaries } from "@features/cards/structures-summary/utils";
import { useReport } from "@utils/useReport";
import { useSubmissionBusinessMetrics } from "@features/cards/structures-summary/queries";
import { StructuresSummary } from "@features/cards/structures-summary";
import { NewModeCard } from "@features/modes-core/components/NewModeCard";
import { Facts } from "@features/modes/jsx/reusables/Facts";
import { StructuresTabbed } from "@features/modes/jsx/reusables/StructuresTabbed";
import { HasDocumentsOrFactsCondition } from "@features/modes/jsx/conditions/HasDocumentsOrFactsCondition";
import { FACT_SUBTYPES, PARENT_TYPES } from "@legacy/constants/facts";
import { AlwaysShowCard } from "@features/modes-core/components/AlwaysShowCard";
import flatten from "lodash/flatten";
import { useEventBus } from "@features/events";
import intersection from "lodash/intersection";
import { useMemoArray } from "@utils/useMemoArray";
import { useStructuresContextData } from "@features/modes-display/entity-picker/context/structuresContext";
import { ModeRenderCondition } from "@features/modes-core/context/VisibilityDebuggingContext";
import { FactsLoader } from "./loaders/FactsLoader";
import { useParentElementId } from "@features/modes-core/context/ParentIdContext";
import { usePremisesPickerStore } from "@features/modes-display/premises-picker/context/PremisesPickerData";

type Props = {
  source: "regular" | "prometrix";
  columns?: number;
  hideDivider?: boolean;
  factLabelFontWeight?: number;
};

export const Structures = ({
  source,
  columns,
  hideDivider = false,
  factLabelFontWeight = 400,
}: Props) => {
  const report = useReport();
  const rowContextEntities = useRowContextEntities();
  const premisesIds = useMemo(() => report?.getPremisesIds(), [report]);
  const businessIds = useMemo(() => report?.getBusinessIds(), [report]);

  const premisesPickerStore = usePremisesPickerStore() ?? null;
  const activeFilterEvent = premisesPickerStore?.use?.activeFilterEvent();
  const activeFilterIds =
    activeFilterEvent?.parentType === "STRUCTURE"
      ? activeFilterEvent?.parentIds
      : undefined;
  const [restrictedParentIds, setRestrictedParentIds] = useState<
    string[] | undefined
  >(activeFilterIds ?? []);

  const { data: metrics } = useSubmissionBusinessMetrics({
    reportId: report.id,
    businessId: rowContextEntities?.submissionBusinesses[0]?.id ?? undefined,
    restrictedParentIds: restrictedParentIds,
  });

  const hasPropertyCoverage = report?.hasPropertyCoverage();

  const restrictedParentIdMemoArray = useMemoArray(restrictedParentIds ?? []);

  const parentIds = useRowContextEntities()?.parentIds ?? [];
  const businessId = parentIds[0] ?? "";
  const premiseId = report.getPremiseIdForBusinessId(businessId) ?? "";

  const allegedStructureCount = useStructuresContextData((state) =>
    source === "prometrix"
      ? state.prometrixOnlyPremiseIdToStructureIds.get(premiseId)?.size ?? 0
      : state.businessIdToStructureIds.get(businessId)?.size ?? 1
  );
  const structureIds = useStructuresContextData((state) =>
    source === "prometrix"
      ? state.prometrixOnlyPremiseIdToStructureIds.get(premiseId)
      : state.businessIdToStructureIds.get(businessId)
  );

  const filteredStructureIds = useMemo(() => {
    if (!structureIds) {
      return [];
    }
    if (!restrictedParentIdMemoArray.length) {
      return [...structureIds];
    }

    return intersection([...structureIds], restrictedParentIdMemoArray);
  }, [structureIds, restrictedParentIdMemoArray]);
  const filteredStructureIdsMemoArray = useMemoArray(filteredStructureIds);

  const {
    numStructuresToRender,
    structureCountDisplay,
    isFilteringStructures,
  } = useMemo(() => {
    const totalKnownStructureCount = structureIds?.size;
    const totalStructureCount =
      allegedStructureCount ?? totalKnownStructureCount ?? 0;
    const filteredStructureCount =
      !!restrictedParentIds?.length && structureIds
        ? restrictedParentIds.filter((id) => structureIds.has(id)).length
        : totalStructureCount;

    const isFilteringStructures =
      filteredStructureCount !== totalStructureCount;
    const structureCountDisplay = `${
      isFilteringStructures
        ? `${filteredStructureCount}/${totalStructureCount}`
        : totalStructureCount
    }`;

    return {
      numStructuresToRender: filteredStructureCount,
      structureCountDisplay: structureCountDisplay,
      isFilteringStructures: isFilteringStructures,
    };
  }, [restrictedParentIds, allegedStructureCount, structureIds]);

  useEventBus(
    (filter) => {
      if (
        filter.parentType === PARENT_TYPES.STRUCTURE &&
        filter.parentIds !== undefined &&
        filter.parentIds !== null
      ) {
        setRestrictedParentIds(filter.parentIds);
      } else {
        setRestrictedParentIds(undefined);
      }
    },
    [setRestrictedParentIds],
    "ui/premises-viewer/filter-picked"
  );
  useEventBus(
    () => {
      setRestrictedParentIds(undefined);
    },
    [setRestrictedParentIds],
    "ui/premises-viewer/filter-cleared"
  );

  const premisesSummaries = useCreateStructureSummaries(
    flatten(metrics) ?? [],
    premisesIds
  );

  const businessSummaries = useCreateStructureSummaries(
    flatten(metrics) ?? [],
    businessIds
  );

  const summaries =
    businessSummaries.length > 0 ? businessSummaries : premisesSummaries;

  const parentElementId = useParentElementId();
  const alwaysShowCardId = "structures-always-show-card-" + parentElementId;
  const summariesCardId = "structures-summary-" + parentElementId;

  return (
    <HasDocumentsOrFactsCondition
      parentType={PARENT_TYPES.PREMISES}
      factSubtypeIds={[FACT_SUBTYPES.STRUCTURES]}
    >
      {summaries.length > 0 && source !== "prometrix" && (
        <ModeRenderCondition
          condition={summaries.length > 0}
          description="Has at least one structure summary"
        >
          <NewModeCard id={summariesCardId}>
            <StructuresSummary
              summaries={summaries}
              structureCountDisplay={structureCountDisplay}
              isFilteringStructures={isFilteringStructures}
            />
          </NewModeCard>
        </ModeRenderCondition>
      )}
      <StructuresTabbed
        filteredStructureIds={filteredStructureIdsMemoArray}
        numStructuresToRender={numStructuresToRender}
        structureCountDisplay={structureCountDisplay}
        isFilteringStructures={isFilteringStructures}
        syncTitle={source !== "prometrix"}
      >
        {hasPropertyCoverage ? (
          <Facts
            title="Property Insurance Valuation"
            facts={{
              group: "PROPERTY_INSURANCE_VALUATION",
              alwaysDisplay: [
                "BPP",
                "BUILDING_VALUE",
                "CONTENT_VALUE",
                "BUSINESS_INCOME",
              ],
              allowAdditionalFields: true,
            }}
            contextFacts={{ useOnlyContextFacts: true }}
            hideDivider={hideDivider}
            columns={columns}
            factLabelFontWeight={factLabelFontWeight}
          />
        ) : (
          <Facts
            title="Total Insurable Value"
            facts={{ group: "MULTI_COVERAGE_TIV" }}
            contextFacts={{ useOnlyContextFacts: true }}
            hideDivider={hideDivider}
            columns={columns}
            factLabelFontWeight={factLabelFontWeight}
          />
        )}
        <Facts
          title="Building exposure"
          facts={{
            group: "BUILDING_EXPOSURE",
          }}
          contextFacts={{ useOnlyContextFacts: true }}
          hideDivider={hideDivider}
          columns={columns}
          factLabelFontWeight={factLabelFontWeight}
        />
        <Facts
          title="Building Integrity risk"
          facts={{ group: "BUILDING_INTEGRITY_RISK" }}
          contextFacts={{ useOnlyContextFacts: true }}
          hideDivider={hideDivider}
          columns={columns}
          factLabelFontWeight={factLabelFontWeight}
        />
        <Facts
          title="Roof"
          facts={{ group: "ROOF" }}
          contextFacts={{ useOnlyContextFacts: true }}
          hideDivider={hideDivider}
          columns={columns}
          factLabelFontWeight={factLabelFontWeight}
        />
        <Facts
          title="Fire risk"
          facts={{ group: "FIRE_RISK" }}
          contextFacts={{ useOnlyContextFacts: true }}
          hideDivider={hideDivider}
          columns={columns}
          factLabelFontWeight={factLabelFontWeight}
        />
        <Facts
          title="Crime Protection"
          facts={{ group: "CRIME_PROTECTION" }}
          contextFacts={{ useOnlyContextFacts: true }}
          hideDivider={hideDivider}
          columns={columns}
          factLabelFontWeight={factLabelFontWeight}
        />
        <Facts
          title="Ownership"
          facts={{ group: "OWNERS" }}
          contextFacts={{ useOnlyContextFacts: true }}
          hideDivider={hideDivider}
          columns={columns}
          factLabelFontWeight={factLabelFontWeight}
        />
        <Facts
          title="Structures Other"
          contextFacts={{
            useOnlyContextFacts: true,
            omitFactsFromGroups: [
              "PROPERTY_INSURANCE_VALUATION",
              "BUILDING_EXPOSURE",
              "BUILDING_INTEGRITY_RISK",
              "FIRE_RISK",
              "CRIME_PROTECTION",
              "OWNERS",
              "ROOF",
            ],
          }}
          hideDivider={hideDivider}
          columns={columns}
          factLabelFontWeight={factLabelFontWeight}
        />
        <FactsLoader
          checkSectionId={parentElementId}
          ignoreCardIds={[summariesCardId, alwaysShowCardId]}
        />
        <AlwaysShowCard id={alwaysShowCardId} />
      </StructuresTabbed>
    </HasDocumentsOrFactsCondition>
  );
};
