import React from "react";
import { PARENT_TYPES } from "@legacy/constants/facts";
import { ELEMENT_ID } from "@features/modes/jsx/constants";
import { Structures } from "@features/modes/jsx/reusables/Structures";
import { EntityPickerTab } from "@features/modes-display/entity-picker/components/EntityPickerTab";

export const RegularStructuresTab = () => {
  return (
    <EntityPickerTab
      title="Structures"
      parentType={PARENT_TYPES.BUSINESS}
      id={ELEMENT_ID.STRUCTURES_TAB}
    >
      <Structures source="regular" />
    </EntityPickerTab>
  );
};
