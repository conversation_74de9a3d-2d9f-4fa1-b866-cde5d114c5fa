import { EntityPickerTab } from "@features/modes-display/entity-picker/components/EntityPickerTab";
import { DOCUMENT_TYPES, PARENT_TYPES } from "@legacy/constants/facts";
import { Facts } from "@features/modes/jsx/reusables/Facts";
import {
  ClosedAtEvaluator,
  CrimeCardEvaluators,
  LocationRisksEvaluators,
} from "@features/modes/fact-error-state-evaluators";
import { HasDocumentsOrFactsCondition } from "@features/modes/jsx/conditions/HasDocumentsOrFactsCondition";
import { NewModeCard } from "@features/modes-core/components/NewModeCard";
import { DocumentsCard } from "@features/cards/documents";
import { ELEMENT_ID } from "@features/modes/jsx/constants";
import { PermitsCard } from "@features/cards/permits";
import { ImagesCard } from "@features/cards/images";
import { BusinessesAtThisLocation } from "@features/cards/businesses-at-this-location";
import { EntityPicker } from "@features/modes-display/entity-picker";
import React, { useMemo } from "react";
import { OshaViolations } from "@features/modes/jsx/reusables/tables/OshaViolations";
import { EpaInspections } from "@features/modes/jsx/reusables/tables/EpaInspections";
import { NewsCard } from "@features/modes-display/NewsCard";
import { LegalFilingsCard } from "@features/modes-display/LegalFilingsCard";
import { AdditionalInformationCard } from "@features/modes/jsx/reusables/AdditionalInformationCard";
import { OperationsCardPremisesFacts } from "@features/modes-display/entity-picker/components/OperationsCardPremisesFacts";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { HasSubmissionBusinessCondition } from "@features/modes/jsx/conditions/HasSubmissionBusinessCondition";
import SubmissionBusiness from "@legacy/models/SubmissionBusiness";
import Report from "@legacy/models/Report";
import { useOperationCardsGroups } from "@features/modes/jsx/hooks/useOperationCardsGroups";
import { CertificatesCard } from "@features/cards/certificates";
import { useCurrentReportQuery } from "@queries/report";
import { PrometrixCard } from "@features/cards/prometrix";
import { ContentWithEmailSidebar } from "@features/modes-core/components/ContentWithEmailSidebar";
import { SIDEBAR_GROUP } from "@features/modes-core/constants";
import { PremisesAndStructures } from "@features/modes/jsx/reusables/entity-picker/PremisesAndStructures";
import { RiskmeterCard } from "@features/cards/riskmeter";
import { WeatherDataCard } from "@features/cards/weather-data";
import { useMemoArray } from "@utils/useMemoArray";
import { PremisesPicker } from "@features/modes-display/premises-picker/PremisesPicker";

const SUBMISSIONS_TO_DISABLE_FULL_INGESTION = [
  "28e7f788-e080-46d8-8c76-17bfb631fc94",
  "9692eafa-c095-4b49-960b-12095bfe84fb",
  "6c35f676-1b31-4f92-8f62-19ded62d997e",
  "46b6786d-10ee-426a-bdda-752174f2e860",
  "d5476eca-8467-4db9-9483-aebb3716bc92",
  "4a18a4ff-8b7b-44ae-9afe-a1857ac340be",
  "da446041-8115-442c-a6a1-5cebf57e6a55",
  "d6cc7a87-bcdf-4d42-a1eb-ec7cf5b17580",
  "9641df46-554f-4cfa-a773-a968196efe58",
];

export const EntityPickerConfigured = () => {
  const { internalUseFeatures } = useFeatureFlags();
  const user = useCurrentUser();
  const organizationId = useOrganizationId();
  const operationsGroups = useOperationCardsGroups();
  const report = useCurrentReportQuery();
  const coverages = useMemoArray(report?.getSubmission().coverages);
  const isGarage = useMemo(
    () => coverages?.some((c) => c.coverage.name === "garageDealers"),
    [coverages]
  );

  const businesses = (report?.getBusinesses() ?? []).length;
  const isK2 = organizationId === 61;

  const usedOperationGroups =
    businesses > 1
      ? ["BUSINESS_HEADER", ...operationsGroups]
      : operationsGroups;

  const showNewPremisesViewer = report?.hasPropertyCoverage();

  return (
    <ContentWithEmailSidebar
      title="Premises Viewer"
      sidebarTitle="Premises"
      elementId={ELEMENT_ID.PREMISES_VIEWER_WRAPPER}
      sidebarGroup={SIDEBAR_GROUP.GENERAL}
    >
      {!showNewPremisesViewer && (
        <EntityPicker title="Premises">
          <EntityPickerTab
            title="Business Operations"
            parentType={PARENT_TYPES.BUSINESS}
            id={ELEMENT_ID.TAB_BUSINESS_OPERATIONS}
          >
            <Facts
              title="Operations"
              facts={{
                groups: usedOperationGroups,
                excludeSubtypeIds: internalUseFeatures
                  ? undefined
                  : ["MULTI_LABEL_NAICS_CODES"],
              }}
              errorStateEvaluators={[ClosedAtEvaluator]}
            />
            <OperationsCardPremisesFacts />
            <Facts
              title="Financials"
              facts={{
                groups: ["OPERATIONS_FINANCIALS"],
                excludeSynonyms: [["TOTAL_SALES", "ANNUAL_SALES_RANGE"]],
              }}
            />
            <Facts
              title="Operations - Other"
              facts={{
                groups: isGarage
                  ? ["GARAGE_OPERATIONS_OTHER", "OPERATIONS_OTHER"]
                  : ["OPERATIONS_OTHER"],
              }}
            />
            <HasDocumentsOrFactsCondition
              parentType={[PARENT_TYPES.BUSINESS]}
              documentIds={[DOCUMENT_TYPES.LICENSE]}
            >
              <NewModeCard title="Licenses">
                <DocumentsCard
                  documentType={DOCUMENT_TYPES.LICENSE}
                  expand={["body"]}
                  hideWhenEmpty={true}
                />
              </NewModeCard>
            </HasDocumentsOrFactsCondition>
          </EntityPickerTab>
          <EntityPickerTab
            title="Additional Submission Data"
            parentType={PARENT_TYPES.BUSINESS}
            id={ELEMENT_ID.TAB_ADDITIONAL_SUBMISSION_DATA}
          >
            <AdditionalInformationCard title="Other" />
          </EntityPickerTab>
          <EntityPickerTab
            title="Certificates"
            parentType={PARENT_TYPES.BUSINESS}
            type="fat-section"
            id={ELEMENT_ID.TAB_CERTIFICATS}
          >
            <HasDocumentsOrFactsCondition
              parentType={PARENT_TYPES.BUSINESS}
              groupIds={["CERTIFICATES"]}
              documentIds={[DOCUMENT_TYPES.CERTIFICATE]}
            >
              <NewModeCard>
                <CertificatesCard />
              </NewModeCard>
            </HasDocumentsOrFactsCondition>
          </EntityPickerTab>
          <PremisesAndStructures />
          <EntityPickerTab
            title="Premises Building Permits"
            parentType={PARENT_TYPES.PREMISES}
            type="fat-section"
            id={ELEMENT_ID.TAB_PREMISES_BUILDING_PERMITS}
          >
            <HasDocumentsOrFactsCondition
              parentType={PARENT_TYPES.PREMISES}
              documentIds={[DOCUMENT_TYPES.PERMIT]}
            >
              <NewModeCard>
                <PermitsCard />
              </NewModeCard>
            </HasDocumentsOrFactsCondition>
          </EntityPickerTab>
          <HasSubmissionBusinessCondition
            condition={(sb: SubmissionBusiness, report: Report) => {
              const shouldSkipSubmission =
                SUBMISSIONS_TO_DISABLE_FULL_INGESTION.includes(
                  report.getSubmission().id
                );
              if (shouldSkipSubmission) {
                return false;
              }
              const coverages = report.getSubmission().coverages;
              const isConifer = report.organization_id === 58;
              const isK2andGarage =
                report.organization_id === 61 &&
                coverages.some((c) => c.coverage.name === "garageDealers");
              const isPropertyOrBusinessOwners = coverages.some(
                (c) =>
                  c.coverage.name === "property" ||
                  c.coverage.name === "businessOwners" ||
                  c.coverage.name === "events"
              );
              const shouldSkipEntity =
                sb.entity_role === "GENERAL_CONTRACTOR" ||
                sb.entity_role === "DUPLICATE";

              return (
                report.organization_id !== 51 &&
                !report.submission.isManagementLiabilityOnly() &&
                ((isPropertyOrBusinessOwners && !shouldSkipEntity) ||
                  sb.entity_role === "PROJECT" ||
                  isK2andGarage ||
                  isConifer)
              );
            }}
            description="Is one of magically defined reports OR is not management liability only OR (is not org 51 ; AND is property or business owners ; AND is not general contractor or duplicate)"
          >
            <EntityPickerTab
              title="CAT Scores"
              parentType={PARENT_TYPES.PREMISES}
              id={ELEMENT_ID.NATURAL_HAZARDS_RISKS}
            >
              <Facts
                title="CAT Scores"
                facts={{ group: "LOCATION_RISKS" }}
                processors={["orderByRawValue"]}
                errorStateEvaluators={LocationRisksEvaluators}
              />
            </EntityPickerTab>
          </HasSubmissionBusinessCondition>
          {!report?.submission?.isManagementLiabilityOnly() && (
            <EntityPickerTab
              title="Crime"
              parentType={PARENT_TYPES.PREMISES}
              id={ELEMENT_ID.CRIME_SCORE}
            >
              <Facts
                title="Crime Score"
                facts={{ factSubtypeIds: ["OVERALL_CRIME_GRADE"] }}
              />

              <Facts
                facts={{ group: "CRIME_CARD" }}
                processors={["orderByCrimeScoreAndName"]}
                ignoreCustomOrdering={true}
                errorStateEvaluators={CrimeCardEvaluators}
              />
            </EntityPickerTab>
          )}
          {isK2 && (
            <EntityPickerTab
              title={"Weather Data"}
              parentType={PARENT_TYPES.PREMISES}
              showEvenIfEmpty
            >
              <WeatherDataCard title={"Weather Data"} />
            </EntityPickerTab>
          )}
          <EntityPickerTab
            title="Violations"
            parentType={PARENT_TYPES.BUSINESS}
            type="fat-section"
            id={ELEMENT_ID.TAB_VIOLATIONS}
          >
            <OshaViolations id={ELEMENT_ID.OSHA_VIOLATIONS_ENTITY_PICKER} />
            <EpaInspections />
          </EntityPickerTab>
          <EntityPickerTab
            title="News"
            parentType={PARENT_TYPES.BUSINESS}
            type="fat-section"
            id={ELEMENT_ID.TAB_NEWS}
          >
            <HasDocumentsOrFactsCondition
              parentType={PARENT_TYPES.BUSINESS}
              documentIds={[DOCUMENT_TYPES.NEWS]}
              showAnyway={user.cross_organization_access}
              showAnywayReason="User has cross organization access"
            >
              <NewModeCard>
                <NewsCard />
              </NewModeCard>
            </HasDocumentsOrFactsCondition>
          </EntityPickerTab>
          {!report?.hasPropertyCoverage() && (
            <EntityPickerTab
              title="Legal Filings"
              parentType={PARENT_TYPES.BUSINESS}
              type="fat-section"
              id={ELEMENT_ID.TAB_LEGAL_FILING}
            >
              <HasDocumentsOrFactsCondition
                parentType={PARENT_TYPES.BUSINESS}
                documentIds={[DOCUMENT_TYPES.LEGAL_FILING]}
                showAnyway={user.cross_organization_access}
                showAnywayReason="User has cross organization access"
              >
                <NewModeCard>
                  <LegalFilingsCard />
                </NewModeCard>
              </HasDocumentsOrFactsCondition>
            </EntityPickerTab>
          )}
          <EntityPickerTab
            title="Images"
            parentType={PARENT_TYPES.BUSINESS}
            id={ELEMENT_ID.TAB_IMAGES}
          >
            <ImagesCard title="Images" />
          </EntityPickerTab>
          <EntityPickerTab
            title={"Businesses at this Location"}
            parentType={PARENT_TYPES.BUSINESS}
            id={ELEMENT_ID.TAB_BUSINESS_AT_LOCATION}
          >
            <BusinessesAtThisLocation />
          </EntityPickerTab>
          {report?.canSeePrometrix() && (
            <EntityPickerTab
              title="Prometrix"
              id="prometrix"
              parentType={PARENT_TYPES.BUSINESS}
            >
              <PrometrixCard />
            </EntityPickerTab>
          )}
          {report?.canSeeRiskmeter() && (
            <EntityPickerTab
              title="RiskMeter"
              id="riskmeter"
              parentType={PARENT_TYPES.PREMISES}
            >
              <RiskmeterCard />
            </EntityPickerTab>
          )}
        </EntityPicker>
      )}
      {showNewPremisesViewer && <PremisesPicker />}
    </ContentWithEmailSidebar>
  );
};
