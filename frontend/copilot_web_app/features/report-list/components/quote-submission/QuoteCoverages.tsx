import {
  coverageName,
  QuoteModalType,
} from "@features/report-list/components/quote-submission/QuoteSubmissionModal";
import { Box, IconButton, Tooltip, Typography } from "@mui/material";
import { FieldArrayWithId } from "react-hook-form";
import DeleteIcon from "@mui/icons-material/Delete";
import { UseCopilotFormReturn } from "@utils/form";
import {
  CoverageType,
  managementLiabilityCoverageNames,
} from "@legacy/models/Coverage";
import {
  QuoteSubmissionCoverageItem,
  QuoteSubmissionForm,
} from "@features/report-list/components/quote-submission/schema";
import { InfoOutlined } from "@mui/icons-material";
import { TOTAL_PREMIUM_TOOLTIP_EXPLANATION_TEXT } from "@utils/constants";
import { format, parse } from "date-fns";
import { formatValue } from "@features/dashboards/utils";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { useOrganizationId } from "@utils/auth";

type Props = {
  type: QuoteModalType;
  form: UseCopilotFormReturn<QuoteSubmissionForm>;
  coverages: FieldArrayWithId<QuoteSubmissionForm, "coverages", "id">[];
  removeCoverage: (index: number) => void;
  removeDeductible: (index: number) => void;
  showCoverageDates?: boolean;
  readOnly?: boolean;
  onlyNonLiabilityEditable?: boolean;
};

export const QuoteCoverages = ({
  type,
  form,
  coverages,
  removeCoverage,
  removeDeductible,
  showCoverageDates = true,
  readOnly,
  onlyNonLiabilityEditable = false,
}: Props) => {
  const { DatePicker, getValues } = form;

  const deleteCoverage = (
    coverageId: string,
    coverageType: CoverageType | null
  ) => {
    removeCoverage(
      coverages.findIndex(
        (c) => c.coverageId === coverageId && c.coverageType == coverageType
      )
    );

    removeDeductible(
      coverages.findIndex(
        (c) => c.coverageId === coverageId && c.coverageType == coverageType
      )
    );
  };

  const { isCSManager } = useAccessPermissions();

  const allowEditingLimitAndAttachment = Boolean(isCSManager);

  if (!coverages.length) return null;

  const [effectiveDate, expirationDate] = getValues([
    "effectiveDate",
    "expirationDate",
  ]);

  return (
    <Box>
      <Box>
        <Box
          display="flex"
          sx={{ px: 4, bgcolor: "mainGrayBackground", py: 2 }}
        >
          <Box display="flex" flex={1}>
            <Typography sx={{ flex: 6 }} variant="overline">
              QUOTED COVERAGES
            </Typography>

            {type === "editCoverages" && (
              <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
                <Typography variant="overline">SIR</Typography>
              </Box>
            )}
            {allowEditingLimitAndAttachment && (
              <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
                <Typography variant="overline">Limit</Typography>
              </Box>
            )}
            {allowEditingLimitAndAttachment && (
              <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
                <Typography variant="overline">Attachment Point</Typography>
              </Box>
            )}

            {type !== "quote" && (
              <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
                <Typography variant="overline">ESTIMATED PREMIUM</Typography>
              </Box>
            )}
            {type !== "editCoverages" && (
              <>
                <Typography
                  sx={{ flex: type === "bind" ? 3.5 : 4 }}
                  variant="overline"
                >
                  QUOTED PREMIUM
                </Typography>

                {type === "bind" && (
                  <Typography sx={{ flex: 3.5 }} variant="overline">
                    BOUND PREMIUM
                  </Typography>
                )}

                {type === "bind" && (
                  <Box display="flex" alignItems="center" sx={{ flex: 4 }}>
                    <Typography variant="overline">TOTAL PREMIUM</Typography>
                    <Tooltip title={TOTAL_PREMIUM_TOOLTIP_EXPLANATION_TEXT}>
                      <InfoOutlined fontSize="small" />
                    </Tooltip>
                  </Box>
                )}
              </>
            )}
            {type === "editCoverages" && <Box sx={{ flex: 0.7 }} />}
          </Box>
        </Box>

        <Box sx={{ px: 4, py: 1 }}>
          {coverages.map((coverage, index) => {
            const nonEditableRow =
              readOnly ||
              (onlyNonLiabilityEditable && coverage.name === "liability");
            return nonEditableRow ? (
              <QuoteCoveragesReadOnlyRow
                coverage={coverage}
                type={type}
                allowEditingLimitAndAttachment={allowEditingLimitAndAttachment}
              />
            ) : (
              <QuoteCoveragesFormRow
                form={form}
                coverage={coverage}
                index={index}
                allowEditingLimitAndAttachment={allowEditingLimitAndAttachment}
                type={type}
                deleteCoverage={deleteCoverage}
                key={coverage.coverageId + coverage.coverageType}
              />
            );
          })}
        </Box>
      </Box>

      {showCoverageDates && (
        <Box sx={{ px: 4, mt: coverages.length > 0 ? 1 : 6, mb: 3 }}>
          <Box sx={{ mb: 2 }}>
            {readOnly ? (
              <Typography variant="body1">
                Effective date:{" "}
                {format(
                  parse(effectiveDate, "yyyy-MM-dd", new Date()),
                  "MM/dd/yyyy"
                )}
              </Typography>
            ) : (
              <DatePicker
                sx={{ width: "350px" }}
                name="effectiveDate"
                label="Effective date"
                placeholder="Select date"
                size="small"
              />
            )}
          </Box>

          <Box>
            {readOnly ? (
              <Typography variant="body1">
                Expiration date:{" "}
                {format(
                  parse(expirationDate, "yyyy-MM-dd", new Date()),
                  "MM/dd/yyyy"
                )}
              </Typography>
            ) : (
              <DatePicker
                sx={{ width: "350px" }}
                name="expirationDate"
                label="Expiration date"
                placeholder="Select date"
                size="small"
              />
            )}
          </Box>
        </Box>
      )}
    </Box>
  );
};

const QuoteCoveragesReadOnlyRow = ({
  coverage,
  type,
  allowEditingLimitAndAttachment,
}: {
  coverage: QuoteSubmissionCoverageItem;
  type: QuoteModalType;
  allowEditingLimitAndAttachment: boolean;
}) => {
  const orgId = useOrganizationId();
  const isAIG = orgId === 66;
  const canCoverageHaveLimitAndAttachment =
    coverage.coverageType === "EXCESS" ||
    managementLiabilityCoverageNames.includes(coverage.name);

  return (
    <Box
      key={coverage.coverageId + coverage.coverageType}
      sx={{ py: 1 }}
      display="flex"
      alignItems="center"
    >
      <Typography variant="body1" sx={{ flex: 6 }}>
        {coverageName(coverage)}
      </Typography>

      {type === "editCoverages" && (
        <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
          <Typography variant="body1" sx={{ width: "144px" }}>
            {formatValue(coverage.selfInsuranceRetention, "currency", "-")}
          </Typography>
        </Box>
      )}

      {(allowEditingLimitAndAttachment || isAIG) && (
        <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
          {(canCoverageHaveLimitAndAttachment || isAIG) && (
            <Typography variant="body1" sx={{ width: "144px" }}>
              {formatValue(coverage.limit, "currency", "-")}
            </Typography>
          )}
        </Box>
      )}
      {allowEditingLimitAndAttachment && (
        <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
          {canCoverageHaveLimitAndAttachment && (
            <Typography variant="body1" sx={{ width: "144px" }}>
              {formatValue(coverage.attachmentPoint, "currency", "-")}
            </Typography>
          )}
        </Box>
      )}

      {type !== "quote" && (
        <Box sx={{ flex: 3.5 }}>
          <Typography variant="body1" sx={{ width: "144px" }}>
            {formatValue(coverage.estimatedPremium, "currency", "-")}
          </Typography>
        </Box>
      )}

      {type !== "editCoverages" && (
        <>
          <Box
            sx={{ flex: type === "bind" ? 3.5 : 4 }}
            display="flex"
            justifyContent="space-between"
          >
            <Typography variant="body1" sx={{ width: "144px" }}>
              {formatValue(coverage.quotedAmount, "currency", "-")}
            </Typography>
          </Box>

          {type === "bind" && (
            <Box
              sx={{ flex: 7.5 }}
              display="flex"
              justifyContent="space-between"
            >
              <Typography variant="body1" sx={{ width: "144px", mr: 0.5 }}>
                {formatValue(coverage.boundAmount, "currency", "-")}
              </Typography>

              <Typography variant="body1" sx={{ width: "144px" }}>
                {formatValue(coverage.totalPremium, "currency", "-")}
              </Typography>
            </Box>
          )}
        </>
      )}
    </Box>
  );
};

const QuoteCoveragesFormRow = ({
  form,
  coverage,
  index,
  allowEditingLimitAndAttachment,
  type,
  deleteCoverage,
}: {
  form: UseCopilotFormReturn<QuoteSubmissionForm>;
  coverage: QuoteSubmissionCoverageItem;
  allowEditingLimitAndAttachment: boolean;
  index: number;
  type: QuoteModalType;
  deleteCoverage: (
    coverageId: string,
    coverageType: CoverageType | null
  ) => void;
}) => {
  const orgId = useOrganizationId();
  const isAIG = orgId === 66;
  const { FormTextField } = form;
  const canCoverageHaveLimitAndAttachment =
    coverage.coverageType === "EXCESS" ||
    managementLiabilityCoverageNames.includes(coverage.name);

  return (
    <Box
      key={coverage.coverageId + coverage.coverageType}
      sx={{ py: 1 }}
      display="flex"
      alignItems="center"
    >
      <Typography variant="body1" sx={{ flex: 6 }}>
        {coverageName(coverage)}
      </Typography>

      {type === "editCoverages" && (
        <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
          <FormTextField
            sx={{ width: "144px" }}
            name={`coverages.${index}.selfInsuranceRetention`}
            label="USD"
            size="small"
            placeholder="Value"
          />
        </Box>
      )}
      {(allowEditingLimitAndAttachment || isAIG) && (
        <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
          {(canCoverageHaveLimitAndAttachment || isAIG) && (
            <FormTextField
              sx={{ width: "144px" }}
              name={`coverages.${index}.limit`}
              label="USD"
              size="small"
              placeholder="Value"
            />
          )}
        </Box>
      )}
      {allowEditingLimitAndAttachment && (
        <Box display="flex" alignItems="center" sx={{ flex: 3.5 }}>
          {canCoverageHaveLimitAndAttachment && (
            <FormTextField
              sx={{ width: "144px" }}
              name={`coverages.${index}.attachmentPoint`}
              label="USD"
              size="small"
              placeholder="Value"
            />
          )}
        </Box>
      )}

      {type !== "quote" && (
        <Box sx={{ flex: 3.5 }}>
          <FormTextField
            name={`coverages.${index}.estimatedPremium`}
            sx={{ width: "144px" }}
            label="USD"
            size="small"
            placeholder="Value"
          />
        </Box>
      )}

      {type !== "editCoverages" && (
        <>
          <Box
            sx={{ flex: type === "bind" ? 3.5 : 4 }}
            display="flex"
            justifyContent="space-between"
          >
            <FormTextField
              name={`coverages.${index}.quotedAmount`}
              sx={{ width: "144px" }}
              label="USD"
              size="small"
              placeholder="Value"
            />

            {type === "quote" && (
              <IconButton
                onClick={() =>
                  deleteCoverage(coverage.coverageId, coverage.coverageType)
                }
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Box>

          {type === "bind" && (
            <Box
              sx={{ flex: 7.5 }}
              display="flex"
              justifyContent="space-between"
            >
              <FormTextField
                name={`coverages.${index}.boundAmount`}
                sx={{ width: "144px", mr: 0.5 }}
                label="USD"
                size="small"
                placeholder="Value"
              />

              <FormTextField
                name={`coverages.${index}.totalPremium`}
                sx={{ width: "144px" }}
                label="USD"
                size="small"
                placeholder="Value"
              />

              <IconButton
                onClick={() =>
                  deleteCoverage(coverage.coverageId, coverage.coverageType)
                }
              >
                <DeleteIcon />
              </IconButton>
            </Box>
          )}
        </>
      )}

      {type === "editCoverages" && (
        <IconButton
          onClick={() =>
            deleteCoverage(coverage.coverageId, coverage.coverageType)
          }
        >
          <DeleteIcon />
        </IconButton>
      )}
    </Box>
  );
};
