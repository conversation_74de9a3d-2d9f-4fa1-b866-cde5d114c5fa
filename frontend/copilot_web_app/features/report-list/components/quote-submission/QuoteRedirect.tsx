import { useMemo } from "react";
import { <PERSON>, Stack, Typography } from "@mui/material";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import { Dialog } from "@ui-patterns/dialog";
import { useCurrentUser } from "@utils/auth";
import {
  QuoteModalType,
  getCoverageModalTitle,
} from "@features/report-list/components/quote-submission/QuoteSubmissionModal";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const redirectUrls = {
  development:
    "https://estst.arch.adaptikcloud.com/AdaptikPolicy/AMEntryPoint?pageCode=SEARCH",
  production:
    "https://adkapp.corp.archcapservices.com/AMPolicyWriter/AMEntryPoint?pageCode=Search",
  productionNew:
    "https://esprod.arch.adaptikcloud.com/AdaptikPolicy/AMEntryPoint?pageCode=SEARCH",
} as const;

const getEnvFromUrl = (useNewArchRedirectUrl?: boolean) =>
  Boolean(window.location.href.match(/(stage|dev)/))
    ? "development"
    : useNewArchRedirectUrl
      ? "productionNew"
      : "production";

const getRedirectUrl = (useNewArchRedirectUrl?: boolean) =>
  redirectUrls[getEnvFromUrl(useNewArchRedirectUrl)];

export const useShouldDisplayRedirectModal = (
  useNewArchRedirectUrl: boolean
) => {
  const redirectUrl = useMemo(
    () => getRedirectUrl(useNewArchRedirectUrl),
    [useNewArchRedirectUrl]
  );
  const user = useCurrentUser();
  const { isCSManager } = useAccessPermissions();

  return Boolean(redirectUrl && user.isArch() && !isCSManager);
};

type Props = {
  type: QuoteModalType;
  onClose: () => void;
};

export const QuoteRedirect = ({ type, onClose }: Props) => {
  const infoTextStatus = type === "quote" ? "Quoted" : "Bound";
  const { useNewArchRedirectUrl } = useFeatureFlags();

  const redirectUrl = useMemo(
    () => getRedirectUrl(useNewArchRedirectUrl),
    [useNewArchRedirectUrl]
  );

  return (
    <Dialog title={getCoverageModalTitle(type)} onClose={onClose} hidePrimary>
      <Stack spacing={3}>
        <Typography variant="body1">
          The Copilot status will be updated to <b>{infoTextStatus}</b> once it
          has been <b>{infoTextStatus}</b> in Policy Writer
        </Typography>
        <Typography variant="body1">
          <Link
            href={redirectUrl}
            onClick={onClose}
            target="_blank"
            rel="noreferrer noopener"
          >
            Click here to to go Policy Writer{" "}
            <OpenInNewIcon fontSize="small" sx={{ mb: "-2px" }} />
          </Link>
        </Typography>
      </Stack>
    </Dialog>
  );
};
