import Report from "@legacy/models/Report";
import { Divider, Tooltip } from "@mui/material";
import { trackSubmissionActionInitiated } from "@utils/amplitude";
import { useCurrentUser } from "@utils/auth";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import React, { useMemo, useState, type JSX } from "react";
import { useSubmissionBladesStore } from "@features/blades/submissionBladesStore";
import { useSnackbar } from "notistack";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { serverlessApiClient } from "@legacy/clients/serverlessApiClient";
import { queryKeyFactory } from "@queries/queryKey";
import { useClientStage } from "@legacy/hooks/useClientStage";
import BookmarkOutlinedIcon from "@mui/icons-material/BookmarkOutlined";
import {
  FileCopyOutlined,
  History,
  OutboundOutlined,
  PeopleAltOutlined,
  PersonAddOutlined,
  SummarizeOutlined,
} from "@mui/icons-material";
import FileDownloadIcon from "@mui/icons-material/FileDownloadOutlined";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import { MenuItemWithIcon } from "@ui-patterns/menu/MenuItemWithIcon";
import { BookmarkButton } from "@features/report-list/components/BookmarkButton";
import LinkIcon from "@mui/icons-material/Link";
import { EmailUnread } from "@ui-patterns/icons/email-unread";
import {
  useReadSubmissionMutation,
  useUnreadSubmissionMutation,
} from "@queries/submission";
import { useIsNWVerifiedShell } from "@legacy/hooks/useIsNWVerifiedShell";
import { routes } from "@features/routes";
import { resolveHref } from "next/dist/client/resolve-href";
import Router from "next/router";
import { useGeneratePspE3Export } from "@features/report-list/components/new-report-list/hooks/useGeneratePspE3Export";
import { useIsParagonE3Report } from "@utils/useIsParagonE3Report";
import { usePdfExportContext } from "@features/pdf-export/PdfExport";
import { ORGANIZATION } from "@utils/constants";
import { useAccessPermissions } from "@features/product-driven-support/utils";

export type TopBarAction = "assign" | "delete" | "recover";

export type Action =
  | "share"
  | "duplicate"
  | "assign"
  | "audit"
  | "delete"
  | "refer"
  | "recover"
  | "bookmark"
  | "copy-link"
  | "mark-as-read"
  | "mark-as-unread"
  | "generate-review-file"
  | "e3-cd-export"
  | "export-pdf";

export const ACTION_TO_ICON_MAP: Record<TopBarAction, JSX.Element> = {
  assign: <PeopleAltOutlined />,
  delete: <DeleteOutlineIcon />,
  recover: <History />,
};

type Props = {
  report?: Report;
  onItemClick?: () => void;
  setSelectedAction: (action: Action | null) => void;
  source: "hub" | "preview" | "report";
};

export const ActionsMenu = ({
  report,
  onItemClick,
  setSelectedAction,
  source,
}: Props) => {
  const currentUser = useCurrentUser();
  const {
    allowReportDuplication: canDuplicate,
    supportFlow,
    internalUseFeatures,
    imsRetryEnabled,
    exportPdf: canExportPdf,
  } = useFeatureFlags();
  const { toggle } = useSubmissionBladesStore();

  const [deletedTooltipIsHovered, setDeletedTooltipIsHovered] = useState(false);
  const [assignedTooltipIsHovered, setAssignedTooltipIsHovered] =
    useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const { getStageName } = useClientStage();
  const { open: openBlade } = useSubmissionBladesStore();
  const submission = report?.getSubmission();
  const hasUniqueClientId = submission?.hasUniqueSubmissionClientId();
  const canEdit = report?.hasEditPermissions();
  const canDelete = report?.isOwner();
  const { mutateAsync: readSubmission } = useReadSubmissionMutation();
  const { mutateAsync: unreadSubmission } = useUnreadSubmissionMutation();
  const isNWVerifiedShell = useIsNWVerifiedShell(report);
  const { generateExport } = useGeneratePspE3Export(submission?.id, report?.id);
  const isReportAssignedToParagonE3 = useIsParagonE3Report(report);
  const { requestExport } = usePdfExportContext();

  const isSecura = report?.organization_id === ORGANIZATION.SECURA;
  const canShare = canEdit && (supportFlow || internalUseFeatures);
  const canAssign = canEdit && !report?.isFrozen();
  const deleteActionVisible =
    currentUser.isInternalUser() || (imsRetryEnabled && canDelete);
  const { isCSManager } = useAccessPermissions();
  const canAudit = submission?.canAudit() && isCSManager;
  const wasRead = submission?.was_read;
  const isPreview = source === "preview";

  const allActionsToComponentMap: Record<Action, JSX.Element> = {
    share: <OutboundOutlined />,
    duplicate: <FileCopyOutlined />,
    refer: <PersonAddOutlined />,
    audit: <BookmarkOutlinedIcon />,
    bookmark: <BookmarkButton report={report} showLabel />,
    "mark-as-read": <EmailUnread />,
    "mark-as-unread": <EmailUnread />,
    "generate-review-file": <SummarizeOutlined />,
    "copy-link": <LinkIcon />,
    "e3-cd-export": <FileDownloadIcon />,
    "export-pdf": <FileDownloadIcon />,
    ...ACTION_TO_ICON_MAP,
  };

  // only for NW ML org!
  const canGenerateReviewFile = currentUser.organization?.id === 57;

  const queryClient = useQueryClient();
  const { mutate: generateAccountReviewDocument } = useMutation(
    () => {
      return serverlessApiClient.generateAccountReviewDocument(
        submission?.id ?? "",
        true
      );
    },
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(
          queryKeyFactory.notes(submission?.id)
        );
        enqueueSnackbar(
          "Newly generated review file is available for download in the notes section"
        );
      },
    }
  );

  const actions = useMemo(() => {
    const unreadAction = wasRead ? "mark-as-unread" : "mark-as-read";
    return [
      { label: "", value: "bookmark" },
      { label: `Mark as ${wasRead ? "Unread" : "Read"}`, value: unreadAction },
      { label: "Copy Link", value: "copy-link" },
      (canExportPdf || (isSecura && internalUseFeatures)) && {
        label: "Export PDF",
        value: "export-pdf",
      },
      isReportAssignedToParagonE3 && {
        label: "CD Export",
        value: "e3-cd-export",
      },
      canShare && {
        label: "Share",
        value: "share",
        disabled: isNWVerifiedShell,
      },
      canGenerateReviewFile && {
        label: "Create Review File",
        value: "generate-review-file",
      },
      {
        label: "Assign",
        value: "assign",
        disabled: !canAssign || isNWVerifiedShell,
        disabledTooltip:
          "Assignee changes are not possible for " + !!report
            ? getStageName(report as Report).toLowerCase()
            : "" + " Submissions.",
        isHovered: assignedTooltipIsHovered,
        onHoverChange: (set: boolean) => setAssignedTooltipIsHovered(set),
      },
      canDuplicate && {
        label: "Duplicate",
        value: "duplicate",
        disabled: isNWVerifiedShell,
      },
      deleteActionVisible && {
        label: "Delete",
        value: "delete",
        disabled: hasUniqueClientId || isNWVerifiedShell,
        disabledTooltip:
          "This report has a unique client submission id that must be removed before deleting the report.",
        isHovered: deletedTooltipIsHovered,
        onHoverChange: (set: boolean) => setDeletedTooltipIsHovered(set),
      },
      canAudit && { label: "Audit", value: "audit" },
    ].filter(Boolean) as {
      label: string;
      value: Action;
      disabled?: boolean;
      disabledTooltip?: string;
      isHovered?: boolean;
      onHoverChange?: (isHovered: boolean) => void;
    }[];
  }, [
    wasRead,
    canShare,
    isNWVerifiedShell,
    canGenerateReviewFile,
    canAssign,
    report,
    getStageName,
    assignedTooltipIsHovered,
    canDuplicate,
    isReportAssignedToParagonE3,
    deleteActionVisible,
    hasUniqueClientId,
    deletedTooltipIsHovered,
    canAudit,
    canExportPdf,
    isSecura,
    internalUseFeatures,
  ]);

  if (!report) return null;

  if (
    !canShare &&
    !canAssign &&
    !deleteActionVisible &&
    !canDuplicate &&
    !isReportAssignedToParagonE3
  )
    return null;

  const handleAction = (action: Action) => {
    trackSubmissionActionInitiated(report, {
      action,
      method: "hub action",
    });
    if (action === "export-pdf") {
      requestExport(report.organization_id, report.submission.id, report.name);
    } else if (action === "refer")
      isPreview
        ? toggle({ type: "notes" }, { isReferring: true })
        : openBlade({
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            isReferring: true,
            containerId: source === "hub" ? "hub-body" : "report-wrapper",
            items: [{ type: "notes" }],
            reportId: report.id,
          });
    else if (action === "audit")
      openBlade({
        items: [{ type: "audit", title: `Quality Audit (${report.name})` }],
        containerId: "hub-body",
        reportId: report.id,
      });
    else if (action === "generate-review-file") {
      generateAccountReviewDocument();
      enqueueSnackbar(
        "File is being created. We'll notify you when it's ready"
      );
    } else if (action === "mark-as-read") {
      if (!!submission?.id) {
        readSubmission(submission?.id);
        enqueueSnackbar("Submission marked as read");
      }
    } else if (action === "mark-as-unread") {
      if (!!submission?.id) {
        unreadSubmission(submission?.id);
        enqueueSnackbar("Submission marked as unread");
      }
    } else if (action === "copy-link") {
      const url =
        window.location.origin +
        resolveHref(Router, routes.report(report.id), true)[1];
      navigator.clipboard?.writeText(url);
    } else if (action === "e3-cd-export") {
      generateExport();
    } else {
      setSelectedAction(action);
    }
    onItemClick?.();
  };

  return (
    <>
      {actions.map(
        (
          { label, value, disabled, disabledTooltip, isHovered, onHoverChange },
          i
        ) => {
          if (disabled) {
            return (
              <div
                key={value}
                onMouseEnter={() => onHoverChange?.(true)}
                onMouseLeave={() => onHoverChange?.(false)}
              >
                <MenuItemWithIcon
                  disabled
                  key={value}
                  label={label}
                  reverse
                  icon={allActionsToComponentMap[value]}
                />
                <Tooltip title={disabledTooltip} open={isHovered}>
                  <span></span>
                </Tooltip>
              </div>
            );
          }
          return (
            <>
              <MenuItemWithIcon
                key={value}
                label={label}
                reverse
                icon={allActionsToComponentMap[value]}
                onClick={(e) => {
                  handleAction(value);
                  e.stopPropagation();
                }}
              />
              {["assign", "copy-link"].includes(value) &&
                i < actions.length - 1 && <Divider />}
            </>
          );
        }
      )}
    </>
  );
};
