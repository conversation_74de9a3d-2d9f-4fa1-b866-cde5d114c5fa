import { Box, Menu, MenuItem } from "@mui/material";
import PreviewIcon from "@mui/icons-material/Preview";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { useState } from "react";
import { CancelPdsButton } from "@features/product-driven-support/components/CancelPdsButton";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { ReprocessSubmissionButton } from "@features/product-driven-support/components/ReprocessSubmissionButton";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { RevertToStateButton } from "@features/product-driven-support/components/RevertToStateButton";
import { useIsPdsDebugPage } from "@features/pds-data-model/debug/PdsDebugPageContext";
import { useRouter } from "next/router";
import Report from "@legacy/models/Report";
import { routes } from "@features/routes";
import { usePreferencesMutation } from "@queries/preferences";
import { usePreferences } from "@features/preferences";
import { DefaultApiUpdatePreferenceRequest } from "@legacy/api_clients/copilot_api_client";

type Props = {
  report: Report;
};

export const SubmissionWizardSidebarActions = ({ report }: Props) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const submission = report.submission;

  const { isCSManager } = useAccessPermissions();
  const { masterAccount, allowSubReverting } = useFeatureFlags();
  const isPdsDebug = useIsPdsDebugPage();
  const router = useRouter();

  const isPdsForcedSplittedDo = usePreferences(
    (state) =>
      !!state.submissionPreferences[submission.id]?.isPdsForcedSplittedDo
  );
  const { mutate: updatePreference } = usePreferencesMutation();

  const handleClose = () => {
    setAnchorEl(null);
  };

  if (isPdsDebug) return null;

  const canRevertSubs = isCSManager || allowSubReverting;
  const canUseSubmissionView = masterAccount || canRevertSubs;

  if (!canUseSubmissionView) return null;

  return (
    <Box
      border="1px solid"
      borderColor="link"
      borderRadius="8px"
      display="flex"
      sx={{
        cursor: "pointer",
        width: "100%",
        mb: 2,
        mx: 2,
      }}
    >
      <Box
        py={0.2}
        px={1.5}
        color="link"
        fontWeight={500}
        display="flex"
        flex={1}
        sx={{
          ":hover": {
            borderRadius: "8px 0 0 8px",
            backgroundColor: "neutral.100",
          },
        }}
        onClick={() => {
          router.push(
            routes.report(report.id, undefined, {
              query: { submissionView: "Y" },
            })
          );
        }}
      >
        <PreviewIcon sx={{ mr: 0.5 }} />
        Submission View
      </Box>
      <Box
        display="flex"
        alignItems="center"
        px={0.5}
        borderLeft="1px solid"
        borderColor="link"
        sx={{
          ":hover": {
            backgroundColor: "neutral.100",
            borderRadius: "0 8px 8px 0",
          },
        }}
        onClick={handleClick}
      >
        {open ? <ExpandLessIcon /> : <ExpandMoreIcon />}
      </Box>

      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        {canRevertSubs &&
          submission.processing_state === "COMPLETED" &&
          !submission.is_processing && (
            <RevertToStateButton toState="DATA_ONBOARDING" variant="menuItem" />
          )}
        {canRevertSubs &&
          (submission.processing_state === "DATA_ONBOARDING" ||
            submission.processing_state === "COMPLETED") && (
            <RevertToStateButton
              toState="BUSINESS_CONFIRMATION"
              variant="menuItem"
            />
          )}
        {canRevertSubs &&
          ["COMPLETED", "DATA_ONBOARDING", "BUSINESS_CONFIRMATION"].includes(
            submission.processing_state ?? ""
          ) && (
            <RevertToStateButton toState="ENTITY_MAPPING" variant="menuItem" />
          )}

        {canRevertSubs && <ReprocessSubmissionButton variant="menuItem" />}
        {canRevertSubs && (
          <MenuItem
            onClick={() =>
              updatePreference({
                submissionId: submission.id,
                body: {
                  isPdsForcedSplittedDo: !isPdsForcedSplittedDo,
                },
              } as DefaultApiUpdatePreferenceRequest)
            }
          >
            Switch to{" "}
            {isPdsForcedSplittedDo
              ? "Normal DO Mode (No Financial Step)"
              : "Splitted DO Mode (With Financial Step)"}
          </MenuItem>
        )}
        {isCSManager && <CancelPdsButton variant="menuItem" />}
      </Menu>
    </Box>
  );
};
