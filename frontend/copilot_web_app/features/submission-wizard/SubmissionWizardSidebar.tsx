import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Chip,
} from "@mui/material";
import CheckIcon from "@mui/icons-material/CheckCircle";
import { NaicsVerificationButton } from "@features/naics";
import { PdsStuckDialog } from "@features/pds-stuck-dialog";
import { FilesDialog } from "@features/files-dialog";
import FileName from "@legacy/pages/files/FileName";
import { FilePreviewButton } from "@features/files/components/FilePreviewButton";
import React, { useEffect, useState } from "react";
import { useReport } from "@utils/useReport";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { useFilesList } from "@features/files/hooks/useFilesList";
import { FileIcon } from "@features/submission-wizard/components/FileIcon";
import File from "@legacy/models/File";
import Submission from "@legacy/models/Submission";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import Link from "next/link";
import { routes } from "@features/routes";
import { useIsPdsDebugPage } from "@features/pds-data-model/debug/PdsDebugPageContext";
import { SubmissionWizardSidebarActions } from "@features/submission-wizard/SubmissionWizardSidebarActions";
import { PdsSpecialNote } from "@features/product-driven-support/components/PdsSpecialNote";
import { Tooltip } from "@ui-patterns/tooltip";
import { HelpDrawer } from "@features/submission-wizard/HelpDrawer";
import HelpIcon from "@mui/icons-material/Help";

const isFileSelected = (file: File, submission?: Submission) => {
  if (submission?.needsEntityMapping() && file.isForEntityMapping())
    return true;
  if (
    submission?.isWaitingForBusinessConfirmation() &&
    file.isForBusinessConfirmation()
  )
    return true;
  if (submission?.isWaitingForDataOnboarding() && file.isForDataOnboarding())
    return true;
  return false;
};

export const SubmissionWizardSidebar = () => {
  const [showStuckModal, setShowStuckModal] = useState<boolean>(false);
  const [showFilesModal, setShowFilesModal] = useState<boolean>(false);
  const [showHelp, setShowHelp] = useState(false);

  const { pdsDebugger } = useFeatureFlags();
  const isPdsDebug = useIsPdsDebugPage();
  const report = useReport();
  const submission = report.getSubmission();
  const submissionId = submission.id;
  const { isCSManager } = useAccessPermissions();

  const stuckLabel =
    isCSManager && !!submission.stuck_reason ? "Unstuck" : "I'm stuck";

  const isPreviewEnabled = (f: File) => {
    return (
      (isCSManager ||
        !f.isWaitingForCompletion() ||
        submission?.isCompleted()) &&
      (!f.isForEntityMapping() || !submission?.needsEntityMapping())
    );
  };

  const { files } = useFilesList({ report });

  useEffect(() => {
    if (!!submission?.stuck_reason) {
      setShowStuckModal(true);
    }
  }, [submission?.stuck_reason]);

  return (
    <Box
      sx={{
        minWidth: 270,
        width: 240,
        bgcolor: "neutral.200",
        flexShrink: 0,
        mb: 7,
        pb: 2,
        overflowX: "hidden",
        overflowY: "auto",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <List
        sx={{
          "& .MuiListItemButton-root": {
            m: 0,
            p: 0.25,
            px: 2,
          },
          "& .MuiListItem-root": {
            mx: 0,
            my: 0.5,
            p: 0,
          },
        }}
      >
        <ListItem>
          <SubmissionWizardSidebarActions report={report} />
        </ListItem>
        <ListItem>
          <ListItemButton selected={!report}>
            <ListItemText primary="Submission Details & Files" />
            {submission?.isSubmissionDetailsCompleted() && <CheckIcon />}
          </ListItemButton>
        </ListItem>
        {submission?.needsFileClearing() && (
          <ListItem>
            <ListItemButton disabled={!report}>
              <ListItemText primary="File Clearing" />
              {submission?.isSubmissionDetailsCompleted() && <CheckIcon />}
            </ListItemButton>
          </ListItem>
        )}
        <ListItem>
          <ListItemButton
            selected={submission?.needsEntityMapping()}
            disabled={!report || !submission?.isSubmissionDetailsCompleted()}
          >
            <ListItemText primary="Entity Mapping" />
            {submission?.isEntityMappingCompleted() && <CheckIcon />}
          </ListItemButton>
        </ListItem>
        <ListItem>
          <ListItemButton
            selected={submission?.isWaitingForBusinessConfirmation()}
            disabled={!report || !submission?.isEntityMappingCompleted()}
          >
            <ListItemText primary="Business Confirmation" />
            {submission?.isBusinessConfirmationCompleted() && <CheckIcon />}
          </ListItemButton>
        </ListItem>
        <ListItem>
          <ListItemButton
            selected={submission?.isWaitingForDataOnboarding()}
            disabled={!report || !submission?.isBusinessConfirmationCompleted()}
          >
            <ListItemText primary="Data Onboarding" />
            {submission?.isCompleted() && <CheckIcon />}
          </ListItemButton>
        </ListItem>
        <ListItem>
          <ListItemButton
            selected={submission?.isCompleted()}
            disabled={!submission?.isCompleted()}
          >
            <ListItemText primary="Submission Information" />
          </ListItemButton>
        </ListItem>
      </List>

      {!!report && (
        <Box>
          <List
            sx={{
              "& .MuiListItem-root": {
                py: 0.5,
              },
            }}
          >
            {!isPdsDebug && (
              <>
                <ListItem>
                  <PdsSpecialNote report={report} />
                </ListItem>

                <ListItem
                  onClick={() => setShowHelp((current) => !current)}
                  sx={{ cursor: "pointer" }}
                >
                  <Chip
                    sx={{ width: "100%" }}
                    label={
                      <Box
                        display="flex"
                        justifyContent={"center"}
                        textAlign="center"
                      >
                        <Box lineHeight={"24px"}>Wizard Instructions</Box>
                        <Tooltip
                          title={`Click to ${
                            showHelp ? "close" : "open"
                          } help panel`}
                        >
                          <HelpIcon sx={{ cursor: "pointer", ml: 1 }} />
                        </Tooltip>
                      </Box>
                    }
                    variant="outlined"
                  />
                </ListItem>
              </>
            )}

            {report.isBossReport() && (
              <ListItem>
                <NaicsVerificationButton report={report} />
              </ListItem>
            )}
            {pdsDebugger && (
              <ListItem>
                <Link
                  href={routes.pds.toDebugger(report.id)}
                  passHref
                  target="_blank"
                  style={{ width: "100%" }}
                >
                  <Chip
                    sx={{ width: "100%" }}
                    label="PDS Debugger"
                    variant="outlined"
                  />
                </Link>
              </ListItem>
            )}
            {!isPdsDebug && (
              <ListItem>
                <Chip
                  sx={{ width: "100%" }}
                  label={stuckLabel}
                  variant="outlined"
                  onClick={() => {
                    setShowStuckModal(true);
                  }}
                />
                {showStuckModal && (
                  <PdsStuckDialog
                    report={report}
                    onClose={() => setShowStuckModal(false)}
                  />
                )}
              </ListItem>
            )}
            {(isCSManager || !submission?.needsEntityMapping()) && (
              <>
                <ListItem>
                  <ListItemText primary="Attachments" />
                  <Chip
                    label="Manage Files"
                    variant="outlined"
                    onClick={() => {
                      setShowFilesModal(true);
                    }}
                  />
                </ListItem>

                {files.map((f) => (
                  <ListItem key={f.id}>
                    <ListItemButton selected={isFileSelected(f, submission)}>
                      <FileIcon file={f} />
                      {submissionId && (
                        <FileName
                          file={f}
                          submissionId={submissionId}
                          openDisabled={!isPreviewEnabled(f)}
                        />
                      )}
                      {isPreviewEnabled(f) && <FilePreviewButton file={f} />}
                    </ListItemButton>
                  </ListItem>
                ))}
              </>
            )}
          </List>
        </Box>
      )}
      {showFilesModal && (
        <FilesDialog report={report} onClose={() => setShowFilesModal(false)} />
      )}
      <HelpDrawer open={showHelp} onClose={() => setShowHelp(false)} />
    </Box>
  );
};
