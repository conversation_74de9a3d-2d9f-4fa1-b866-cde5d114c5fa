import Report from "@legacy/models/Report";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Alert,
  Typography,
} from "@mui/material";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { FilePreviewButton } from "@features/files/components/FilePreviewButton";
import React from "react";
import { ProcessFileButton } from "@features/submission-wizard/components/ProcessFileButton";

type HiddenFilesAlertProps = {
  report: Report;
};

export const HiddenFilesAlert = ({ report }: HiddenFilesAlertProps) => {
  const hiddenFiles =
    report?.submission.files.filter((file) => file.hidden) ?? [];
  const { isCSManager } = useAccessPermissions();

  if (hiddenFiles.length === 0 || !isCSManager) {
    return null;
  }
  return (
    <Alert severity="info" sx={{ mb: 1 }}>
      <Accordion>
        <AccordionSummary aria-controls="panel1-content" id="panel1-header">
          <Typography>
            {hiddenFiles.length} hidden files are not visible in the submission
            wizard (click to expand)
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            These files are currently not processed, but you can use button
            below to process specified files
          </Typography>
          <ul>
            {hiddenFiles.map((file) => (
              <li key={file.id}>
                {file.name} <FilePreviewButton file={file} />{" "}
                <ProcessFileButton
                  reportId={report.id}
                  submissionId={report.submission.id}
                  file={file}
                />
              </li>
            ))}
          </ul>
        </AccordionDetails>
      </Accordion>
    </Alert>
  );
};
