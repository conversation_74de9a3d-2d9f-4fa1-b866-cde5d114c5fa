import {
  <PERSON>complete,
  Box,
  create<PERSON><PERSON><PERSON><PERSON><PERSON>s,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import React, { useMemo } from "react";
import FactSubtype from "@legacy/models/FactSubtype";
import { useFactSubtypeContext } from "@features/entity-mapping/context/FactSubtypeContext";
import { useNewOnboardingDataForm } from "@features/pds-data-model/form";
import { FactSubtypeSuggestion } from "@legacy/api_clients/copilot_api_client";
import { Controller, useWatch } from "react-hook-form";
import { useFieldIdContext } from "@features/data-onboarding/fields-mapping/FieldIdContext";
import { FieldExperiment } from "@features/pds-data-model/types";
import { EVENT_BUS } from "@features/events";
import { useStore } from "zustand";
import { useOnboardingDataContext } from "@features/pds-data-model/context/OnboardingDataContextData";
import { FactSubtypeSuggestionView } from "@features/data-onboarding/fields-mapping/parts/FactSubtypeSuggestionView";
import { mapFactSubtypeToOptions } from "@features/data-onboarding/fields-mapping/parts/optionsUtil";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const EMPTY_ARR: FactSubtypeSuggestion[] = [];

const filterOptions = createFilterOptions<FactSubtype>({
  limit: 30,
});

export const MULTI_PARENT_SUBTYPES = [
  "OWNER",
  "ZIP_CODE",
  "POLICY_NUMBER",
  "TIV",
];

export const FactSubtypePicker = () => {
  const { fieldId, sortedFactSubtypesByNameScore } = useFieldIdContext();
  const { isCSManager } = useAccessPermissions();
  const { nonDeprecatedFactSubtypes } = useFactSubtypeContext();
  const { getValues } = useNewOnboardingDataForm();
  const fieldName = getValues().fields[fieldId]?.name;

  const canShowFactSubtypeSuggestions = useStore(
    useOnboardingDataContext(),
    (state) =>
      Object.values(state.factSubtypeSuggestions)
        .flatMap((o) => Object.keys(o ?? {}))
        .includes(fieldName ?? "")
  );
  const suggestions =
    getValues(`fields.${fieldId}.fact_subtype_suggestions`) ?? EMPTY_ARR;
  const entityTypes = getValues(`fields.${fieldId}.entityTypes`);
  const experiment: FieldExperiment | undefined = useWatch({
    name: `fields.${fieldId}.experiment`,
  });

  const explanationBySuggestedSubtype = useMemo(() => {
    return (
      suggestions?.reduce(
        (acc, s) => {
          if (
            s.fact_subtype_id &&
            !acc[s.fact_subtype_id] &&
            s.explanations?.length
          ) {
            acc[s.fact_subtype_id] = s.explanations[0];
          }
          return acc;
        },
        {} as Record<string, string>
      ) ?? {}
    );
  }, [suggestions]);

  const suggestedFactSubtypeIds = suggestions
    .map((s) => s.fact_subtype_id)
    .filter((s): s is string => !!s);

  const options = mapFactSubtypeToOptions(
    nonDeprecatedFactSubtypes,
    entityTypes,
    [
      ...suggestedFactSubtypeIds,
      ...sortedFactSubtypesByNameScore.filter(
        (fact) => !suggestedFactSubtypeIds.includes(fact)
      ),
    ]
  );

  return (
    <Box display="flex" flexDirection="column">
      <Controller
        name={
          experiment
            ? `fields.${fieldId}.experiment.factSubtypeId`
            : `fields.${fieldId}.fact_subtype_id`
        }
        render={({ field: { onChange, value, ...otherProps } }) => {
          let selectedOption = options.find((option) => option.id === value);
          if (!selectedOption && value) {
            selectedOption = options.find((option) => option.id === value);
          }
          const isDisabledVinField =
            selectedOption?.id === "VEHICLE_INFORMATION_NUMBER" && !isCSManager;
          return (
            <Autocomplete
              options={options}
              filterOptions={filterOptions}
              value={selectedOption ?? value}
              limitTags={1}
              autoHighlight
              disabled={isDisabledVinField}
              sx={{ width: 320 }}
              onChange={(_, v: FactSubtype | null) => {
                EVENT_BUS.emit("tracking/action/generic", {
                  name: "data onboarding fact subtype change",
                  props: {
                    oldFactSubtypeId: selectedOption?.id ?? value,
                    newFactSubtypeId: v?.id ?? "",
                  },
                });
                onChange(v?.id ?? "");
              }}
              getOptionLabel={(option: FactSubtype) =>
                option.display_name ?? ""
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Fact Subtype"
                  size="small"
                  helperText={
                    isDisabledVinField
                      ? "Changing VIN is not allowed"
                      : undefined
                  }
                />
              )}
              renderOption={(props, option) => (
                <Tooltip
                  title={
                    option.id in explanationBySuggestedSubtype
                      ? explanationBySuggestedSubtype[option.id]
                      : ""
                  }
                >
                  <Box
                    component="li"
                    {...props}
                    style={{
                      display: "block",
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight:
                          option.id in explanationBySuggestedSubtype
                            ? "bold"
                            : "normal",
                      }}
                    >
                      {option.display_name}
                    </Typography>
                    {option.description &&
                      option.display_name !== option.description && (
                        <Typography variant="body2" color="gray">
                          {option.description}
                        </Typography>
                      )}
                  </Box>
                </Tooltip>
              )}
              {...otherProps}
            />
          );
        }}
      />
      {canShowFactSubtypeSuggestions && <FactSubtypeSuggestionView />}
    </Box>
  );
};
