import { useWatch } from "react-hook-form";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Typography,
} from "@mui/material";
import { FeResolvedDataField } from "@features/pds-data-model/types";
import React from "react";
import { FieldsForEntityType } from "@features/data-onboarding/fields-mapping/FieldsForEntityType";
import { NewFieldButton } from "@features/data-onboarding/fields-mapping/NewFieldButton";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useOnboardingDataContext } from "@features/pds-data-model/context/OnboardingDataContextData";
import { mapDoStepToEntities } from "@features/data-onboarding/utils";
import { useNewOnboardingDataForm } from "@features/pds-data-model/form";
import { SubmissionEntityTypeEnum } from "@legacy/api_clients/copilot_api_client";

type FieldsMappingProps = {
  disableAddField?: boolean;
  hideSourcesIndicator?: boolean;
  allowedEntities?: SubmissionEntityTypeEnum[];
  showEvidences?: boolean;
  hideToTableNavigation?: boolean;
};

export const FieldsMapping = ({
  disableAddField = false,
  hideSourcesIndicator = false,
  showEvidences = false,
  hideToTableNavigation = false,
  allowedEntities: allowedEntitiesProp,
}: FieldsMappingProps) => {
  const doStep = useOnboardingDataContext().use.currentStep();
  const entityTypeFieldIdsMap =
    useOnboardingDataContext().use.entityTypeFieldIdsMap();
  const initialFirstPartyFieldIds =
    useNewOnboardingDataForm().getValues().extras.initialFirstPartyFieldIds;
  const allowedEntities = allowedEntitiesProp ?? mapDoStepToEntities(doStep);
  const fieldsOrder: string[] = useWatch({
    name: "fieldsOrder",
  });
  const fields: Record<string, FeResolvedDataField> = useWatch({
    name: "fields",
  });
  const fieldsInOrder = fieldsOrder
    .map((fieldId) => fields[fieldId])
    .filter((f) => f.origin === "field");

  const fieldsForCurrentEntities = fieldsInOrder.filter((field) =>
    allowedEntities.some(
      (e) => entityTypeFieldIdsMap[e]?.has(field.feFieldId ?? "") ?? false
    )
  );

  const shownFields = fieldsForCurrentEntities.filter((f) =>
    initialFirstPartyFieldIds.includes(f.feFieldId)
  );
  const hiddenFields = fieldsForCurrentEntities.filter(
    (f) => !initialFirstPartyFieldIds.includes(f.feFieldId)
  );
  const shownEntities = Array.from(
    new Set(shownFields.flatMap((field) => field.entityTypes))
  ).filter((e) => !allowedEntities || allowedEntities.includes(e));
  const hiddenEntities = Array.from(
    new Set(hiddenFields.flatMap((field) => field.entityTypes))
  ).filter((e) => !allowedEntities || allowedEntities.includes(e));

  return (
      <Box display="flex" flexDirection="column">
        <Box>
          {!disableAddField && (
            <Box
              display="flex"
              flexDirection="row-reverse"
              justifyContent="space-between"
            >
              <NewFieldButton />
            </Box>
          )}
          {shownEntities.map((entityType) => (
            <FieldsForEntityType
              key={entityType}
              entityType={entityType}
              nameSuffix="- Unmapped fields"
              fieldIds={shownFields
                .filter((f) => f.entityTypes.includes(entityType))
                .map((f) => f.feFieldId)}
              hideSourcesIndicator={hideSourcesIndicator}
              showEvidences={showEvidences}
              hideToTableNavigation={hideToTableNavigation}
            />
          ))}
        </Box>
        <Accordion slotProps={{ transition: { unmountOnExit: true } }}>
          <AccordionSummary>
            <Typography variant="h6">Automatically matched facts</Typography>{" "}
            <ExpandMoreIcon fontSize="medium" />
          </AccordionSummary>
          <AccordionDetails>
            {hiddenEntities.map((entityType) => (
              <FieldsForEntityType
                key={entityType}
                entityType={entityType}
                fieldIds={hiddenFields
                  .filter((f) => f.entityTypes.includes(entityType))
                  .map((f) => f.feFieldId)}
                hideSourcesIndicator={hideSourcesIndicator}
                showEvidences={showEvidences}
                hideToTableNavigation={hideToTableNavigation}
              />
            ))}
          </AccordionDetails>
        </Accordion>
      </Box>
  );
};
