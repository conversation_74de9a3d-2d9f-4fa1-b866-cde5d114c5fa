import { Box } from "@mui/material";
import React from "react";
import { FormTextField } from "@utils/form/FormTextField";
import { useWatch } from "react-hook-form";
import { UnitsPicker } from "@features/data-onboarding/fields-mapping/parts/UnitsPicker";
import { FactSubtypePicker } from "@features/data-onboarding/fields-mapping/parts/FactSubtypePicker";
import { FirstPartyGroupPicker } from "@features/data-onboarding/fields-mapping/parts/FirstPartyGroupPicker";
import { AggregationTypePicker } from "@features/data-onboarding/fields-mapping/parts/AggregationTypePicker";
import { useFieldIdContext } from "@features/data-onboarding/fields-mapping/FieldIdContext";
import { FieldExperiment } from "@features/pds-data-model/types";
import { SourcesIndicator } from "@features/data-onboarding/fields-mapping/parts/SourcesIndicator";
import { ExampleValuesIndicator } from "@features/data-onboarding/fields-mapping/parts/ExampleValuesIndicator";
import { EvidenceButton } from "@features/data-onboarding/fields-mapping/parts/EvidenceButton";
import { useAccessPermissions } from "@features/product-driven-support/utils";

type SingleFieldFormProps = {
  hideSourcesIndicator?: boolean;
  showEvidences?: boolean;
  hideToTableNavigation?: boolean;
};

export const SingleFieldForm = React.memo(
  ({
    hideSourcesIndicator = false,
    showEvidences = false,
    hideToTableNavigation = false,
  }: SingleFieldFormProps) => {
    const { fieldId } = useFieldIdContext();
    const realFactSubtypeId = useWatch({
      name: `fields.${fieldId}.fact_subtype_id`,
    });
    const experiment: FieldExperiment | undefined = useWatch({
      name: `fields.${fieldId}.experiment`,
    });
    const factSubtypeId = experiment
      ? experiment.factSubtypeId
      : realFactSubtypeId;
    const { isCSManager } = useAccessPermissions();
    return (
      <Box display="flex" flexDirection="row" gap={1}>
        <FormTextField
          size="small"
          label="Field"
          name={`fields.${fieldId}.name`}
        />
        {factSubtypeId && <Box sx={{ width: 200, height: 1 }} />}
        {!factSubtypeId && <UnitsPicker />}
        <FactSubtypePicker />
        {!factSubtypeId && isCSManager && (
          <>
            <FirstPartyGroupPicker />
            <AggregationTypePicker />
          </>
        )}
        {!hideSourcesIndicator && <SourcesIndicator />}
        <ExampleValuesIndicator hideToTableNavigation={hideToTableNavigation} />
        {showEvidences && <EvidenceButton />}
      </Box>
    );
  }
);
