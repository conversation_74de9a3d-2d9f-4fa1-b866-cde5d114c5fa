import File from "@legacy/models/File";
import {
  Box,
  Checkbox,
  Chip,
  IconButton,
  TableCell,
  TableRow,
  Typography,
} from "@mui/material";
import { format } from "date-fns";
import { useUpdateFileMutation } from "@queries/files";
import { useEffect, useMemo, useState } from "react";
import { FileTypesDropdown } from "@features/files/components/FileTypesDropdown";
import {
  FilePatch,
  FilePatchProcessingStateEnum,
  ModelFileFileTypeEnum,
  ModelFileProcessingStateEnum,
  SubmissionProcessingStateEnum,
} from "@legacy/api_clients/copilot_api_client";
import Submission from "@legacy/models/Submission";
import { FileList } from "@features/files/components/FileList";
import { GroupedFileType } from "@features/files/hooks/useFilesList";
import { FilePreviewButton } from "@features/files/components/FilePreviewButton";
import { useCurrentUser } from "@utils/auth";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import {
  FileDownloadButton,
  FileOpenInNewTab,
} from "@features/files/components/FileDownloadButton";
import { useReportQuery } from "@queries/report";
import { trackFileReclassified } from "@utils/amplitude";
import { useSnackbar } from "notistack";
import DrawRoundedIcon from "@mui/icons-material/DrawRounded";
import ClearRoundedIcon from "@mui/icons-material/ClearRounded";
import DynamicFeedIcon from "@mui/icons-material/DynamicFeed";
import { useLossRunFiles } from "@legacy/hooks/useLossRunFiles";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { FileIgnoreButton } from "@features/files/components/FileIgnoreButton";
import { FileContextMenu } from "@features/files/components/FileContextMenu";
import { isFilePreviewEnabled } from "@features/files/utils";
import { useIsPdsPage } from "@utils/useIsPdsPage";
import { ReprocessLossButton } from "./ReprocessLossButton";
import { theme } from "@ui-patterns/theme";
import { useReportEmailsQuery } from "@queries/email";
import merge from "lodash/merge";
import { Tooltip } from "@ui-patterns/tooltip";
import { WarningMessage } from "@features/cards/important-highlights/WarningMessage";
import { useIsDocumentIngestion } from "@features/files/hooks/useIsDocumentIngestion";
import { FileProcessingProgressBar } from "@features/files/components/FileProcessingProgressBar";
import { CollapseIndicator } from "@ui-patterns/collapse-indicator";
import { useQueryClient } from "@tanstack/react-query";
import { queryKeyFactory } from "@queries/queryKey";

const formatEmailAddresses = (addresses: (string | null | undefined)[]) => {
  const emails = addresses.filter(Boolean);

  if (!emails.length) return "Not Available";

  const rendered = emails.slice(0, 3);

  const moreEmails = emails.length - rendered.length;

  return (
    <Tooltip title={emails.join("\n")}>
      <Box>
        {rendered.map((email) => (
          <Box
            sx={{
              maxWidth: "300px",
              minWidth: "150px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
            key={email}
          >
            {email}
          </Box>
        ))}

        {moreEmails > 0 && `(+${moreEmails} more)`}
      </Box>
    </Tooltip>
  );
};

const getFileType = (
  reportId: string,
  file: File,
  userFileType = false,
  isDocumentIngestion = false
) => {
  if (isDocumentIngestion && file.file_type === ModelFileFileTypeEnum.Custom) {
    return file.custom_file_type?.file_type_name;
  }

  return userFileType ? file.user_file_type ?? file.file_type : file.file_type;
};

const getClientFileType = (
  reportId: string,
  file: File,
  clientFileTypesEnabled: boolean
) => {
  if (!clientFileTypesEnabled) return getFileType(reportId, file);

  return file.client_file_type;
};

const FileType = ({
  file,
  updateFileType,
  reportId,
  disabled,
}: {
  file: File;
  updateFileType: (
    newFileType: ModelFileFileTypeEnum,
    customFileId?: string
  ) => void;
  reportId: string;
  disabled?: boolean;
}) => {
  const isDocumentIngestion = useIsDocumentIngestion();

  return (
    <FileTypesDropdown
      // @ts-expect-error fix this
      fileType={getFileType(reportId, file, true, isDocumentIngestion)}
      onChange={updateFileType}
      disabled={disabled}
      width={200}
      showCustomFileTypes
    />
  );
};

type FileTab = "submission" | "email";

type FileRowProps = {
  file: File;
  childFiles?: File[];
  reportId: string;
  submission: Submission;
  groupedFiles: Record<string, GroupedFileType>;
  paddingLeft?: number;
  readOnly?: boolean;
  onSelect?: (file: File) => void;
  selectedFiles?: string[];
  selectableFiles?: string[];
  allowFileInfo?: boolean;
  fileTab?: FileTab;
};

export const FileRow = ({
  file,
  childFiles = [],
  reportId,
  submission,
  groupedFiles,
  paddingLeft = 3,
  readOnly,
  onSelect,
  selectedFiles,
  selectableFiles,
  allowFileInfo,
  fileTab,
}: FileRowProps) => {
  const { data: report } = useReportQuery({ id: reportId });
  const userData = useCurrentUser();
  const isDocumentIngestion = useIsDocumentIngestion();
  const [showChildren, setShowChildren] = useState(
    userData.applicable_settings?.is_support || isDocumentIngestion
  );
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { data: emails } = useReportEmailsQuery(reportId, fileTab !== "email");

  const email = useMemo(() => {
    return emails?.find((e) => e.id === file.additional_info?.email_id);
  }, [emails, file]);

  const isPdsPage = useIsPdsPage();

  const fileName = isPdsPage ? file.name : file.display_name;

  const { lossRunFilesById } = useLossRunFiles(report);
  const lossRunFile = lossRunFilesById.get(file.id);
  const lossRunWarningLabel =
    file.processing_state === FilePatchProcessingStateEnum.Processing
      ? undefined
      : lossRunFile?.warningMessage;
  const { isCSManager } = useAccessPermissions();

  const submissionId = submission.id;

  const queryClient = useQueryClient();
  useEffect(() => {
    if (isDocumentIngestion) {
      queryClient.invalidateQueries(
        queryKeyFactory.file(submissionId!, file.id)
      );
    }
  }, [queryClient, file.file_type, submissionId, file.id, isDocumentIngestion]);

  const { internalUseFeatures, showDocumentSigned } = useFeatureFlags();
  const isProcessingStateIgnored =
    file.processing_state == FilePatchProcessingStateEnum.Ignored;
  const isFileHidden = file.hidden;

  const shouldRenderReprocessLossButton =
    file.file_type === ModelFileFileTypeEnum.LossRun && isCSManager;

  const childFilesLen = childFiles.length;

  const hasClientFileType =
    !!userData.applicable_settings.client_file_types_config?.client_file_types;

  const isFileConfidenceOverrideable =
    isCSManager ||
    !file.initial_classification_confidence ||
    file.initial_classification_confidence! < 0.95;

  const isFrozen = ["REPLACED", "DELETED"].includes(file.user_shadow_state!);

  const isFileTypeEditable =
    !isFrozen &&
    !readOnly &&
    isFileConfidenceOverrideable &&
    submission.processing_state !== SubmissionProcessingStateEnum.Completed &&
    file.processing_state !== ModelFileProcessingStateEnum.Processing;

  const mutation = useUpdateFileMutation(reportId, submissionId, file.id);

  const { enqueueSnackbar } = useSnackbar();

  const updateFileType = (
    newFileType: ModelFileFileTypeEnum,
    customFileId?: string
  ) => {
    const mutateBody: FilePatch = customFileId
      ? {
          user_file_type: "Custom",
          custom_file_type_id: customFileId,
        }
      : { user_file_type: newFileType };
    mutation.mutate(mutateBody, {
      onSuccess: () => {
        if (report?.getSubmission().needsEntityMapping()) {
          trackFileReclassified(report, {
            newClassification: newFileType,
            originalClassification: file.file_type!,
          });
        }
      },
      onError: (error) => {
        const err = error as {
          message: string;
          response: { data: { detail: string } };
        };
        const msg =
          err?.response?.data?.detail ??
          err?.message ??
          "Failed to update File Type";
        enqueueSnackbar(msg, { variant: "error" });
      },
    });
  };

  const getEmailsTo = (emailTo: string | undefined) => {
    if (!emailTo) return [];

    const split = emailTo.split(">, ");
    for (let i = 0; i < split.length - 1; i++) {
      split[i] += ">";
    }

    return split;
  };

  return (
    <>
      <TableRow
        sx={{
          "&:hover .file-row-actions": {
            visibility: "visible",
          },
          background: isMenuOpen ? theme.palette.action.hover : "",
          "&:hover": {
            backgroundColor: theme.palette.action.hover,
          },

          "p, td": {
            color: isFrozen ? "text.secondary" : undefined,
          },
        }}
      >
        {!!onSelect && (
          <TableCell
            sx={{ position: "sticky", left: 0, background: "white", zIndex: 1 }}
          >
            {(!selectableFiles || selectableFiles.includes(file.id)) && (
              <Checkbox
                onClick={() => onSelect(file)}
                checked={selectedFiles?.includes(file.id)}
              />
            )}
          </TableCell>
        )}
        <TableCell />
        <Tooltip
          title={
            internalUseFeatures && !isCSManager
              ? file?.getInternalTooltip()
              : ""
          }
        >
          <TableCell
            sx={{
              paddingLeft,
              minWidth: 200,
              maxWidth: 600,
              wordBreak: "break-word",
            }}
          >
            <Box
              display={childFilesLen > 0 ? "flex" : undefined}
              alignItems={"center"}
              width={childFilesLen > 0 && showChildren ? 300 : "auto"}
            >
              {childFilesLen > 0 && <DynamicFeedIcon sx={{ mr: 2 }} />}
              {fileName}&nbsp;
              {isDocumentIngestion &&
                file.file_type === ModelFileFileTypeEnum.AcordForm &&
                file.additional_info?.acord?.form_name && (
                  <Chip
                    label={file.additional_info?.acord?.form_name}
                    sx={{ ml: 2 }}
                  />
                )}
              {file.is_internal && !isDocumentIngestion ? (
                <Box sx={{ color: "grey.500", display: "inline" }}>
                  (internal)
                </Box>
              ) : (
                ""
              )}
              {internalUseFeatures && !isPdsPage && fileName !== file.name && (
                <Typography>
                  <Box color="GrayText" sx={{ fontSize: "10px !important" }}>
                    ({file.name})
                  </Box>
                </Typography>
              )}
              {lossRunWarningLabel && (
                <WarningMessage message={lossRunWarningLabel} />
              )}
              {isProcessingStateIgnored && isFileHidden && (
                <WarningMessage
                  message={"File from hidden tab, excluded from analysis"}
                />
              )}
              {isFrozen && (
                <WarningMessage
                  message={`File ${
                    file.user_shadow_state === "DELETED"
                      ? "deleted"
                      : "replaced"
                  }. Waiting for new file processing`}
                />
              )}
              {childFilesLen > 0 && (
                <IconButton
                  onClick={(event) => {
                    setShowChildren(!showChildren);
                    event.stopPropagation();
                  }}
                >
                  <CollapseIndicator isOpen={!!showChildren} />
                </IconButton>
              )}
            </Box>
          </TableCell>
        </Tooltip>
        <TableCell>{format(new Date(file.created_at), "MM/dd/yyyy")}</TableCell>
        {(internalUseFeatures || !hasClientFileType) && (
          <TableCell>
            <FileType
              file={file}
              updateFileType={updateFileType}
              reportId={reportId}
              disabled={!isFileTypeEditable}
            />
          </TableCell>
        )}
        {isDocumentIngestion && (
          <TableCell>
            <FileProcessingProgressBar file={file} />
          </TableCell>
        )}
        {hasClientFileType && (
          <TableCell>
            {getClientFileType(
              reportId,
              file,
              !!userData.applicable_settings.client_file_types_config
            )}
          </TableCell>
        )}
        {internalUseFeatures && (
          <TableCell>{file.processing_state_label}</TableCell>
        )}
        {showDocumentSigned && (
          <TableCell>
            <HandsignedIcon file={file} />
          </TableCell>
        )}
        {fileTab === "email" && (
          <>
            <TableCell>
              {formatEmailAddresses([email?.email_from || email?.email_sender])}
            </TableCell>
            <TableCell>
              {formatEmailAddresses(
                merge(getEmailsTo(email?.email_to), email?.email_cc)
              )}
            </TableCell>
          </>
        )}
        <>
          <TableCell>
            <Box
              display="flex"
              alignItems="center"
              columnGap={1.5}
              justifyContent="flex-end"
            >
              {!isFrozen && (
                <>
                  {file &&
                    isFilePreviewEnabled(
                      file,
                      isCSManager ?? false,
                      submission
                    ) && (
                      <>
                        <Box
                          columnGap={1.5}
                          display="flex"
                          className="file-row-actions"
                          visibility="hidden"
                        >
                          <FileDownloadButton file={file} />
                          <FileOpenInNewTab file={file} reportId={reportId} />
                        </Box>

                        <FilePreviewButton file={file} />
                      </>
                    )}
                </>
              )}

              {!readOnly && file && (
                <FileContextMenu
                  file={file}
                  isFilePreviewEnabled={isFilePreviewEnabled(
                    file,
                    isCSManager ?? false,
                    submission
                  )}
                  allowFileInfo={allowFileInfo}
                  isOpenStatus={(val) => setIsMenuOpen(val)}
                  disabled={isFrozen}
                />
              )}
              {shouldRenderReprocessLossButton && (
                <ReprocessLossButton
                  fileId={file.id}
                  submissionId={submission.id}
                />
              )}
            </Box>
          </TableCell>

          {!readOnly && (
            <TableCell>
              {file && isCSManager && !report?.getSubmission().is_verified && (
                <FileIgnoreButton file={file} />
              )}
            </TableCell>
          )}
        </>
      </TableRow>
      {showChildren && (
        <FileList
          parentId={file.id}
          files={childFiles ?? []}
          reportId={reportId}
          submission={submission}
          groupedFiles={groupedFiles}
          paddingLeft={paddingLeft + 10}
          readOnly={readOnly}
          onSelect={onSelect}
          selectedFiles={selectedFiles}
          selectableFiles={selectableFiles}
        />
      )}
    </>
  );
};

function HandsignedIcon({ file }: { file: File }) {
  if (!file.isAcordForm()) {
    return null;
  }

  return file.is_handsigned ? (
    <DrawRoundedIcon color="success" />
  ) : (
    <ClearRoundedIcon />
  );
}
