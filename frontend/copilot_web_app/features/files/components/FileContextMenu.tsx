import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import File from "@legacy/models/File";
import {
  Box,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
} from "@mui/material";
import { useState } from "react";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import DownloadIcon from "@mui/icons-material/FileDownloadOutlined";
import DeleteIcon from "@mui/icons-material/DeleteOutlined";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useReport } from "@utils/useReport";
import { FilePreviewDialog } from "@features/files/components/FilePreviewDialog";
import { routes } from "@features/routes";
import Link from "next/link";
import { useDownloadReportFile } from "@features/files/components/FileDownloadButton";
import { DeleteFileModal } from "@features/files/components/DeleteFileModal";
import { useSubmissionBladesStore } from "@features/blades/submissionBladesStore";
import { useCurrentUser } from "@utils/auth";
import DynamicFeedIcon from "@mui/icons-material/DynamicFeed";
import { SplitFileDialog } from "@features/files/components/SplitFileDialog";
import { ModelFileFileTypeEnum } from "@legacy/api_clients/copilot_api_client";
import { useIsDocumentIngestion } from "@features/files/hooks/useIsDocumentIngestion";
import LabelIcon from "@mui/icons-material/Label";
import { NewCustomFileTypeModal } from "@features/files/components/NewCustomFileTypeModal";
import SwapCallsIcon from "@mui/icons-material/SwapCalls";
import { UploadSubmissionFiles } from "@features/files-dialog/components/UploadSubmissionFiles";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { useAccessPermissions } from "@features/product-driven-support/utils";

type Props = {
  file: File;
  isFilePreviewEnabled: boolean;
  allowFileInfo?: boolean;
  isOpenStatus?: (val: boolean) => void;
  disabled?: boolean;
};

export function FileContextMenu({
  file,
  isFilePreviewEnabled,
  allowFileInfo = true,
  isOpenStatus = () => null,
  disabled,
}: Props) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [showSplitFileDialog, setShowSplitFileDialog] = useState(false);
  const [showNewCustomFileType, setShowNewCustomFileType] = useState(false);
  const report = useReport();
  const userData = useCurrentUser();
  const isDocumentIngestion = useIsDocumentIngestion();
  const [replacingSov, setReplacingSov] = useState<File>();
  const { allowCreateOrReplaceFilesForVerifiedSub: canReplace } =
    useFeatureFlags();

  const { isCSManager } = useAccessPermissions();

  const { open: openBlade } = useSubmissionBladesStore();

  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
    isOpenStatus(true);
  };
  const handleClose = () => {
    setAnchorEl(null);
    isOpenStatus(false);
  };

  const handleShowPreview = () => {
    setShowPreview(true);
    handleClose();
  };

  const { isEnabled: isDownloadEnabled, download } = useDownloadReportFile({
    file,
  });

  const handleDownload = () => {
    download();
    handleClose();
  };

  const handleDelete = () => {
    setShowDelete(true);
    handleClose();
  };

  const handleShowInfo = () => {
    openBlade({
      containerId: "report-wrapper",
      items: [{ type: "fileInfo", title: file.display_name }],
      reportId: report.id,
      // @ts-expect-error type blades to have params
      fileId: file.id,
      onClose: () => isOpenStatus(false),
    });
    setAnchorEl(null);
  };

  const parentFile = file.parent_file_id
    ? report?.getFileById(file.parent_file_id)
    : null;

  const isNw = userData.organization.id === 6;

  const nwCond = (() => {
    const isDeletable = (file: File): boolean => {
      const source = file.client_file_tags?.["source_type"];
      const isRose = source?.toLowerCase() === "rose";

      if (isRose) return false;
      if (!file.parent_file_id) return true;

      const parentFile = report.getFileById(file.parent_file_id);
      if (!parentFile) return true;

      return isDeletable(parentFile);
    };

    return isDeletable(file);
  })();

  const canDelete = (() => {
    if (!report.hasEditPermissions()) {
      return false;
    }

    const baseCond =
      isCSManager || !(file.is_internal && parentFile && parentFile.isAcord());

    if (isNw) {
      return baseCond && nwCond;
    }

    return baseCond;
  })();

  const deleteTooltip =
    isNw && !nwCond
      ? "File received in the original submission from ROSE. Unable to delete in Copilot and BOSS"
      : undefined;

  const allowDefineNewType = useIsDocumentIngestion();

  return (
    <Box>
      <IconButton onClick={handleClick} disabled={disabled}>
        <MoreHorizIcon />
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
        sx={{
          "& .MuiPaper-root": {
            backgroundColor: "mainGrayBackground",
            minWidth: "180px",
          },
          "& *": {
            color: "neutral.500",
          },
          "& .MuiMenuItem-root": {
            display: "flex",
            columnGap: 1,
          },
        }}
      >
        {isFilePreviewEnabled && (
          <Box>
            <MenuItem onClick={handleShowPreview} disabled={!file.canPreview()}>
              <VisibilityOutlinedIcon /> Preview
            </MenuItem>

            <Link
              href={routes.reportFilePreview(report.id, file.id)}
              passHref
              target="_blank"
              style={{ textDecoration: "none" }}
            >
              <MenuItem onClick={handleClose}>
                <OpenInNewIcon /> Open in new tab
              </MenuItem>
            </Link>

            <MenuItem onClick={handleDownload} disabled={!isDownloadEnabled}>
              <DownloadIcon /> Download
            </MenuItem>

            {file.file_type === ModelFileFileTypeEnum.Sov && canReplace && (
              <MenuItem
                onClick={() => setReplacingSov(file)}
                disabled={!isDownloadEnabled}
              >
                <SwapCallsIcon /> Replace
              </MenuItem>
            )}
          </Box>
        )}

        <Tooltip title={deleteTooltip}>
          <Box
            sx={!canDelete && !deleteTooltip ? { display: "none" } : undefined}
          >
            <MenuItem onClick={handleDelete} disabled={!canDelete}>
              <DeleteIcon /> Delete
            </MenuItem>
          </Box>
        </Tooltip>
        {isDocumentIngestion &&
          file.file_type === ModelFileFileTypeEnum.Unknown &&
          file.name.toLowerCase().indexOf(".pdf") >= 0 && (
            <Box>
              <Divider sx={{ borderColor: "neutral.300" }} />

              <MenuItem onClick={() => setShowSplitFileDialog(true)}>
                <DynamicFeedIcon /> Split File
              </MenuItem>
            </Box>
          )}

        {(allowDefineNewType || allowFileInfo) && (
          <Box>
            <Divider sx={{ borderColor: "neutral.300" }} />
            {allowDefineNewType && (
              <MenuItem onClick={() => setShowNewCustomFileType(true)}>
                <LabelIcon /> Create a new file type
              </MenuItem>
            )}
            {allowFileInfo && (
              <MenuItem onClick={handleShowInfo}>
                <InfoOutlinedIcon /> File details
              </MenuItem>
            )}
          </Box>
        )}
      </Menu>

      {showPreview && (
        <FilePreviewDialog
          file={file}
          onClose={() => setShowPreview(false)}
          report={report}
        />
      )}
      {showDelete && (
        <DeleteFileModal
          submissionId={file.submission_id as string}
          fileId={file.id}
          onClose={() => setShowDelete(false)}
        />
      )}
      {showNewCustomFileType && (
        <NewCustomFileTypeModal
          onClose={() => setShowNewCustomFileType(false)}
          fileId={file.id}
        />
      )}
      {showSplitFileDialog && (
        <SplitFileDialog
          file={file}
          onClose={() => setShowSplitFileDialog(false)}
        />
      )}

      {replacingSov && (
        <UploadSubmissionFiles
          onClose={() => setReplacingSov(undefined)}
          report={report}
          replaceSov={replacingSov}
        />
      )}
    </Box>
  );
}
