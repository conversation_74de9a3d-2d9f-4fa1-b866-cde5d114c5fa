import React, { ReactElement, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useInternalUsersOnlyPage } from "@utils/auth";
import {
  ClassifierMetadata,
  CustomizableClassifier,
} from "@legacy/api_clients/copilot_api_client";
import copilotApiClient from "@legacy/clients/copilotApiClient";
import { useFactSubtypes } from "@legacy/hooks/useFactSubtypes";
import { Box, Button, Chip, CircularProgress } from "@mui/material";
import { GridColDef, GridEventListener } from "@mui/x-data-grid-pro";
import { CardTable } from "@ui-patterns/card-table";
import { Dialog } from "@ui-patterns/dialog";
import {
  ClassifierValidation,
  ConfigurableClassifierForm,
} from "@features/configurable-classifiers/ConfigurableClassifierForm";
import { ConfigurableClassifiers } from "@features/configurable-classifiers/ConfigurableClassifiers";
import { AddNewBinarySubtypeModal } from "@features/configurable-classifiers/AddNewBinarySubtypeModal";
import { AddNewMultiLabelClassifier } from "@features/configurable-classifiers/AddNewMultiLabelClassifier";
import { AddNewFactSubtypeModal } from "@features/configurable-classifiers/AddNewFactSubtypeModal";
import { MainLayout } from "@features/main-layout";
import { useIsDocumentIngestion } from "@features/files/hooks/useIsDocumentIngestion";
import { formatNumber } from "@legacy/helpers/numberHelpers";
import {
  useCreateClassifier,
  useDivideClassifierSubtypes,
} from "@features/configurable-classifiers/utils";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { useCustomizableClassifiersV2 } from "@queries/customizableClassifiers";
import { ClassifiersVersionsDialog } from "@features/configurable-classifiers/new-classifiers/ClassifiersVersionsDialog";
import { useRouter } from "next/router";
import orderBy from "lodash/orderBy";

const ActiveStatus = ({ status }: { status: string }) => {
  return (
    <Chip
      size="small"
      variant={status === "Active" ? "filled" : "outlined"}
      color={status === "Active" ? "success" : "primary"}
      label={status}
    />
  );
};

const ClassifiersMetadataList = (): ReactElement => {
  const [classifierMetadata, setClassifierMetadata] =
    useState<ClassifierMetadata | null>();
  const [showAddModal, setShowAddModal] = useState<boolean>();
  const [classifier, setClassifier] = useState<CustomizableClassifier>();
  const [showNewSubtypeModal, setShowAddNewSubtypeModal] = useState(false);
  const [showNewMultiLabelClassifier, setShowNewMultiLabelClassifier] =
    useState(false);
  const [showNewFactSubtypesModal, setShowNewFactSubtypesModal] =
    useState(false);
  const isDocumentIngestion = useIsDocumentIngestion();
  const { showNewClassifiers } = useFeatureFlags();

  const [validation, setValidation] = useState<ClassifierValidation>({});
  const saveClassifier = useCreateClassifier({
    classifier: classifier!,
    setValidation,
  });
  const router = useRouter();

  const { data: newClassifiers } = useCustomizableClassifiersV2();

  const { isLoading, data: results } = useQuery(
    ["classifier-metadata"],
    () =>
      copilotApiClient.getClassifiersMetadata(
        isDocumentIngestion ? 62 : undefined
      ),
    {
      staleTime: 60 * 60 * 1000, // 1h cache
    }
  );

  const rows = showNewClassifiers
    ? orderBy(
        newClassifiers,
        [(x) => (x.fact_subtype_id ?? "").toLowerCase()],
        ["asc"]
      )
    : results;

  useInternalUsersOnlyPage();

  const { data: factSubtypes } = useFactSubtypes();

  const { binaryFactSubtypeOptions, nonBinaryFactSubtypeOptions } =
    useDivideClassifierSubtypes(factSubtypes ?? []);

  if (isLoading) return <CircularProgress />;

  const columns: GridColDef[] = [
    {
      field: "fact_subtype_id",
      headerName: "Name",
      minWidth: 350,
    },
    { headerName: "# of Versions", field: "number_of_versions", minWidth: 150 },
    {
      headerName: "Precision",
      field: "total_precision",
      renderCell: (params) => {
        return params.row.active_version_test_run?.total_precision
          ? formatNumber(params.row.active_version_test_run?.total_precision, 2)
          : "";
      },
      minWidth: 180,
    },
    {
      headerName: "Recall",
      field: "total_recall",
      renderCell: (params) => {
        return params.row.active_version_test_run?.total_recall
          ? formatNumber(params.row.active_version_test_run?.total_recall, 2)
          : "";
      },
      minWidth: 180,
    },
    {
      headerName: "Support",
      field: "total_support",
      renderCell: (params) => {
        return params.row.active_version_test_run?.total_support;
      },
      minWidth: 180,
    },
    {
      headerName: "F1 Score",
      field: "total_f1_score",
      renderCell: (params) => {
        return params.row.active_version_test_run?.total_f1_score
          ? formatNumber(params.row.active_version_test_run?.total_f1_score, 2)
          : "";
      },
      minWidth: 180,
    },
    {
      headerName: "Status",
      field: "is_active",
      renderCell: (params) => (
        <ActiveStatus status={params.row.is_active ? "Active" : "Disabled"} />
      ),
      minWidth: 100,
    },
    {
      headerName: "",
      field: "action",
      renderCell: (params) => (
        <Button
          variant={params.row.is_active ? "outlined" : "contained"}
          size="small"
          color="primary"
          onClick={() => {
            setClassifierMetadata(params.row);
          }}
        >
          {params.row.is_active ? "Disable" : "Activate"}
        </Button>
      ),
    },
  ];

  const toggleAddModal = () => {
    if (!showAddModal) {
      setClassifier({});
    }
    setShowAddModal(!showAddModal);
  };

  const updateClassifier = (updated: CustomizableClassifier) => {
    setClassifier({ ...classifier, ...updated });
  };

  const onRowClick: GridEventListener<"rowClick"> = (params) => {
    setClassifierMetadata(params.row);
  };

  return (
    <>
      <Box width={"100%"} overflow={"scroll"}>
        {isLoading ? (
          <CircularProgress />
        ) : (
          <CardTable
            autoHeight
            defaultPageSize={50}
            trackingTableType="classifiers metadata"
            columns={columns}
            rows={(rows ?? []).map((r) => ({
              ...r,
              id: showNewClassifiers ? (r as any).id : r.fact_subtype_id,
              active_version_test_run: showNewClassifiers
                ? (r as any).latest_test_run
                : (r as any).active_version_test_run,
              number_of_versions: showNewClassifiers
                ? (r as any).versions?.length
                : (r as any).number_of_versions,
              is_active: showNewClassifiers
                ? (r as any).versions?.some((v: any) => v.is_active)
                : (r as any).is_active,
            }))}
            title={
              <Box display="flex" justifyContent="center" flexDirection="row">
                <Box alignSelf="center">
                  Classifiers Metadata
                  <Button
                    variant="contained"
                    color="primary"
                    size="small"
                    sx={{ ml: 2 }}
                    onClick={
                      showNewClassifiers
                        ? () => router.push("/classifiers/new")
                        : toggleAddModal
                    }
                    data-testid="addNewClassifier"
                  >
                    Add New Classifier
                  </Button>
                  {!isDocumentIngestion && (
                    <>
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        sx={{ ml: 2 }}
                        onClick={() => setShowAddNewSubtypeModal(true)}
                      >
                        Add new Binary Fact Subtype
                      </Button>
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        sx={{ ml: 2 }}
                        onClick={() => setShowNewMultiLabelClassifier(true)}
                      >
                        Multi-label Classifier Group
                      </Button>

                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        sx={{ ml: 2 }}
                        onClick={() => setShowNewFactSubtypesModal(true)}
                      >
                        Add new Fact Subtypes
                      </Button>
                    </>
                  )}
                </Box>
              </Box>
            }
            allowSearch
            loading={isLoading}
            onRowClick={onRowClick}
          />
        )}
      </Box>
      {classifierMetadata?.fact_subtype_id && (
        <Dialog
          title={classifierMetadata?.fact_subtype_id}
          onClose={() => setClassifierMetadata(null)}
          fullWidth
          maxWidth="xl"
          hidePrimary
          secondaryActionText="Close"
        >
          {!showNewClassifiers && (
            <ConfigurableClassifiers
              factSubtypeId={classifierMetadata.fact_subtype_id!}
              binaryFactSubtypeOptions={binaryFactSubtypeOptions}
              nonBinaryFactSubtypeOptions={nonBinaryFactSubtypeOptions}
              onClose={() => setClassifierMetadata(null)}
            />
          )}
          {showNewClassifiers && (
            <ClassifiersVersionsDialog
              factSubtypeId={classifierMetadata.fact_subtype_id!}
            />
          )}
        </Dialog>
      )}
      {showAddModal && !showNewClassifiers && (
        <Dialog
          title="Add Configurable Classifier"
          onClose={toggleAddModal}
          primaryActionText="Save"
          onPrimaryAction={saveClassifier}
          maxWidth="md"
          fullWidth
        >
          <div style={{ display: "flex" }}>
            <div style={{ flex: 1 }}>
              <ConfigurableClassifierForm
                configurableClassifier={classifier}
                binaryFactSubtypeOptions={binaryFactSubtypeOptions}
                nonBinaryFactSubtypeOptions={nonBinaryFactSubtypeOptions}
                updateClassifier={updateClassifier}
                validation={validation}
              />
            </div>
          </div>
        </Dialog>
      )}

      {showNewSubtypeModal && (
        <AddNewBinarySubtypeModal
          onClose={() => setShowAddNewSubtypeModal(false)}
        />
      )}

      {showNewMultiLabelClassifier && (
        <AddNewMultiLabelClassifier
          onClose={() => setShowNewMultiLabelClassifier(false)}
        />
      )}

      {showNewFactSubtypesModal && (
        <AddNewFactSubtypeModal
          onClose={() => setShowNewFactSubtypesModal(false)}
        />
      )}
    </>
  );
};

export const ClassifiersMetadataPage = (): React.ReactElement | null => {
  return (
    <MainLayout>
      <Box display="flex" flex="1 1 0" overflow="hidden" mx={2}>
        <ClassifiersMetadataList />
      </Box>
    </MainLayout>
  );
};
