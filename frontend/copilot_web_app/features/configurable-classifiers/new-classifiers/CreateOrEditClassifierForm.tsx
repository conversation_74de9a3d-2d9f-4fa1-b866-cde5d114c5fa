import { Typography, Box, Switch } from "@mui/material";
import { ModeInfoBanner } from "@features/configurable-classifiers/new-classifiers/ModeInfoBanner";
import { ConfigurableClassifierV2Form } from "@features/configurable-classifiers/new-classifiers/ConfigurableClassifierV2Form";
import React, { useEffect } from "react";
import { useCustomizableClassifiersV2 } from "@queries/customizableClassifiers";
import { convertToFormData } from "@features/configurable-classifiers/new-classifiers/utils";
import { useRouter } from "next/router";
import { useFormContext } from "react-hook-form";
import { CustomizableClassifierFormData } from "@features/configurable-classifiers/new-classifiers/types";
import { KALEPA_MODEL } from "@features/configurable-classifiers/new-classifiers/DocumentGranularForm";

export const CreateOrEditClassifierForm = ({
  classifierId,
}: {
  classifierId?: string;
}) => {
  const [isSimplified, setIsSimplified] = React.useState(true);

  const { reset } = useFormContext<CustomizableClassifierFormData>();

  const router = useRouter();
  const factSubtypeId = router.query.factSubtypeId as string | undefined;
  const versionId = router.query.versionId as string | undefined;
  const { data: classifiers } = useCustomizableClassifiersV2(factSubtypeId);

  useEffect(() => {
    if (classifierId && classifiers?.length) {
      const currentApiClassifier = classifiers?.find(
        (classifier) => classifier.id === classifierId
      );
      if (currentApiClassifier) {
        const formData = convertToFormData(currentApiClassifier, versionId);
        reset(formData);
        if (
          Object.keys(formData.applicableDocumentTypes).some(
            (k) =>
              formData.applicableDocumentTypes[k].config.modelingStrategy !==
              KALEPA_MODEL
          ) ||
          formData.timingBeforeConsolidation
        ) {
          setIsSimplified(false);
        }
      }
    }
  }, [classifierId, classifiers, reset, versionId]);

  return (
    <>
      <Box
        display={"flex"}
        justifyContent={"space-between"}
        alignItems={"center"}
        borderBottom={"1px solid #E0E0E0"}
      >
        <Typography variant={"h4"}>
          {" "}
          {!!classifierId ? "Edit" : "Create"} Configurable Classifier
        </Typography>
        <Box display="flex" ml={4} alignItems={"center"}>
          <Switch
            checked={!isSimplified}
            size="small"
            color="primary"
            onChange={() => setIsSimplified((prev) => !prev)}
          />
          <Typography variant={"subtitle1"} ml={0}>
            Advanced editor
          </Typography>
        </Box>
      </Box>
      <ModeInfoBanner isSimplified={isSimplified} />
      <ConfigurableClassifierV2Form
        isSimplified={isSimplified}
        classifierId={classifierId}
      />
    </>
  );
};
