import { Box, Button, IconButton, Typography } from "@mui/material";
import { useForm, FormProvider } from "react-hook-form";
import {
  useCreateCustomizableClassifierMutation,
  useUpdateCustomizableClassifierMutation,
} from "@queries/customizableClassifiers";
import { CustomizableClassifierFormData } from "@features/configurable-classifiers/new-classifiers/types";

import React from "react";
import { convertToApiData } from "@features/configurable-classifiers/new-classifiers/utils";
import { useOrganizationId } from "@utils/auth";
import { CreateOrEditClassifierForm } from "@features/configurable-classifiers/new-classifiers/CreateOrEditClassifierForm";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useRouter } from "next/router";
import { routes } from "@features/routes";

export const ConfigurableClassifierPage = ({
  classifierId,
}: {
  classifierId?: string;
}) => {
  const orgId = useOrganizationId();
  const router = useRouter();

  const defaultValues: CustomizableClassifierFormData = {
    factName: "",

    timingBeforeConsolidation: false,

    naics: {
      category: "Sector (2-digit)",
      operator: "include",
      values: [],
    },

    coverage: {
      operator: "include",
      values: [],
    },

    applicableDocumentTypes: {},

    description: "",

    outcomeValueType: "",
  };

  const formMethods = useForm<CustomizableClassifierFormData>({
    mode: "onChange",
    defaultValues,
  });

  const onClose = () => router.push(routes.classifiers());

  const { mutate: createClassifier } =
    useCreateCustomizableClassifierMutation();
  const { mutate: updateClassifier } =
    useUpdateCustomizableClassifierMutation();

  const onSubmit = (data: CustomizableClassifierFormData) => {
    const apiData = convertToApiData(data, orgId);
    if (classifierId) {
      updateClassifier(
        { id: classifierId, customizableClassifierV2: apiData },
        {
          onSuccess: () => {
            onClose();
          },
        }
      );
    } else {
      createClassifier(
        { customizableClassifierV2: apiData },
        {
          onSuccess: () => {
            onClose();
          },
        }
      );
    }
  };

  return (
    <Box p={4}>
      <FormProvider {...formMethods}>
        <Box display="flex" justifyContent={"space-between"} mb={4}>
          <Box display="flex" alignItems="center">
            <IconButton onClick={onClose}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h6" ml={2}>
              All Classifiers
            </Typography>
          </Box>
        </Box>
        <CreateOrEditClassifierForm classifierId={classifierId} />
        <Box display={"flex"} justifyContent={"flex-end"} mt={3}>
          <Button onClick={onClose} color="primary">
            Cancel
          </Button>
          <Button
            onClick={formMethods.handleSubmit(onSubmit)}
            color="primary"
            disabled={!formMethods.formState.isValid}
          >
            {classifierId ? "Save as new version" : "Create"}
          </Button>
        </Box>
      </FormProvider>
    </Box>
  );
};
