import {
  Box,
  Button,
  FormControl,
  IconButton,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import { Controller } from "react-hook-form";
import { Phrase } from "@features/configurable-classifiers/new-classifiers/types";
import { FormField } from "@ui-patterns/form-components/FormField";

interface PhraseInputListProps {
  control: any;
  name: string;
  label: string;
  description: string;
  showPhraseRelevancy?: boolean;
  handleConfigChange: (value: Phrase[]) => void;
}

export const PhraseInputList = ({
  control,
  name,
  label,
  description,
  showPhraseRelevancy = false,
  handleConfigChange,
}: PhraseInputListProps) => {
  return (
    <FormControl fullWidth margin="normal">
      <Controller
        name={name}
        control={control}
        defaultValue={[{ phrase: "" }]}
        render={({ field }) => {
          const { value = [], onChange } = field;

          const update = (index: number, newValue: Phrase) => {
            const updated = [...value];
            updated[index] = newValue;
            onChange(updated);
            handleConfigChange(updated);
          };

          const remove = (index: number) => {
            const updated = [...value];
            updated.splice(index, 1);
            onChange(updated);
            handleConfigChange(updated);
          };

          const add = () => {
            const updated = [...value, { phrase: "" }];
            onChange(updated);
            handleConfigChange(updated);
          };

          return (
            <FormField label={label} sx={{ my: 1, alignItems: "flex-start" }}>
              <>
                <Typography variant="body2" mb={1}>
                  {description}
                </Typography>
                <Box bgcolor="#FAFCFF" sx={{ px: 3, py: 1 }}>
                  <Stack spacing={2}>
                    {value.map((item: Phrase, index: number) => (
                      <Stack key={index} spacing={1}>
                        <IconButton
                          onClick={() => remove(index)}
                          edge="end"
                          sx={{ alignSelf: "flex-end" }}
                          aria-label="delete"
                        >
                          <DeleteOutlineIcon />
                        </IconButton>
                        <TextField
                          fullWidth
                          value={item.phrase}
                          onChange={(e) =>
                            update(index, { ...item, phrase: e.target.value })
                          }
                          label="Phrase"
                          size="small"
                        />
                        <Box display={"flex"} flexDirection={"row"} gap={2}>
                          {showPhraseRelevancy && (
                            <TextField
                              fullWidth
                              value={(item as Phrase).phraseRelevancy || 0}
                              onChange={(e) =>
                                update(index, {
                                  ...item,
                                  phraseRelevancy: e.target.value
                                    ? Number(e.target.value)
                                    : undefined,
                                })
                              }
                              label="Phrase Relevancy"
                              size="small"
                              helperText="Enter value between 0.0 and 1.0"
                            />
                          )}
                          <TextField
                            fullWidth
                            value={item.excludes || ""}
                            onChange={(e) =>
                              update(index, {
                                ...item,
                                excludes: e.target.value,
                              })
                            }
                            label="Excludes (optional)"
                            size="small"
                            helperText="Filters out similar but incorrect phrases"
                          />
                        </Box>
                      </Stack>
                    ))}
                    <Button
                      onClick={add}
                      variant="text"
                      sx={{
                        justifyContent: "flex-start",
                        alignItems: "center",
                      }}
                    >
                      + Add Phrases
                    </Button>
                  </Stack>
                </Box>
              </>
            </FormField>
          );
        }}
      />
    </FormControl>
  );
};
