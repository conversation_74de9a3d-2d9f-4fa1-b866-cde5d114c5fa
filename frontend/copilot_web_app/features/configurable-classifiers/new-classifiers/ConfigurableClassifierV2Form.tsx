import React, { use<PERSON>emo } from "react";
import {
  TextField,
  FormControl,
  Typography,
  Box,
  Divider,
  Autocomplete,
  Checkbox,
  createFilterOptions,
  CircularProgress,
} from "@mui/material";
import { Controller, useFormContext } from "react-hook-form";
import { FormField } from "@ui-patterns/form-components/FormField";
import { LightbulbAlert } from "@ui-patterns/lightbulb-alert";
import { NaicsCodeFilterComponent } from "@features/hub/components/filters/single/NaicsCodeFilter";
import { HubNaicsCodeFilter } from "@features/hub/components/filters/utils";
import { useGetInsightsDocumentTypes } from "@queries/classifiers";
import orderBy from "lodash/orderBy";
import { useOrganizationId } from "@utils/auth";
import {
  CustomizableClassifierFormData,
  Phrase,
} from "@features/configurable-classifiers/new-classifiers/types";
import { CoverageFilter } from "@features/hub/components/filters/single/CoverageFilter";
import { useDivideClassifierSubtypes } from "@features/configurable-classifiers/utils";
import { useFactSubtypes } from "@legacy/hooks/useFactSubtypes";
import { Option } from "@features/hub/types";
import { OutcomeValueTypeField } from "@features/configurable-classifiers/new-classifiers/OutcomeValueTypeField";
import { AdvancedFieldsForm } from "@features/configurable-classifiers/new-classifiers/AdvancedFieldsForm";
import { titleCase } from "@features/configurable-classifiers/ConfigurableClassifierForm";
import { KALEPA_MODEL } from "@features/configurable-classifiers/new-classifiers/DocumentGranularForm";

interface ConfigurableClassifierV2FormProps {
  isSimplified?: boolean;
  classifierId?: string;
}

export const ConfigurableClassifierV2Form = ({
  isSimplified,
  classifierId,
}: ConfigurableClassifierV2FormProps) => {
  const orgId = useOrganizationId();
  const {
    watch,
    control,
    formState: { errors },
  } = useFormContext<CustomizableClassifierFormData>();

  const { data: factSubtypes, isLoading } = useFactSubtypes(
    undefined,
    undefined,
    true
  );

  const { binaryFactSubtypeOptions, nonBinaryFactSubtypeOptions } =
    useDivideClassifierSubtypes(
      factSubtypes?.filter((f) => !f.is_deprecated) ?? []
    );

  const factSubtypeOptions = useMemo(() => {
    return Array.from(
      new Map(
        [...binaryFactSubtypeOptions, ...nonBinaryFactSubtypeOptions].map(
          (item) => [item.value, item]
        )
      ).values()
    );
  }, [binaryFactSubtypeOptions, nonBinaryFactSubtypeOptions]);

  const filter = createFilterOptions<Option>();

  const updateNaicsFilter = (newFilter: HubNaicsCodeFilter, field: any) => {
    field.onChange({
      category: newFilter.digits === 2 ? "Sector (2-digit)" : "Group (6-digit)",
      operator: newFilter.inclusion,
      values: newFilter.value,
    });
  };

  const { data: documentTypes } = useGetInsightsDocumentTypes(orgId);

  const analyzeOptions = useMemo(() => {
    return orderBy(
      (documentTypes ?? []).map((x) => ({
        label: titleCase(x).replaceAll("Pdf", "PDF"),
        id: x,
        config: {
          modelingStrategy: KALEPA_MODEL,
          phrases: [] as Phrase[],
        },
      })),
      (x) => x.label
    );
  }, [documentTypes]);

  return (
    <>
      <FormControl fullWidth margin="normal" error={!!errors.factName}>
        <FormField label={"Fact Name"} sx={{ my: 0 }}>
          <Controller
            name="factName"
            control={control}
            defaultValue=""
            rules={{ required: "Fact Name is required" }}
            render={({ field }) => (
              <Autocomplete
                selectOnFocus
                clearOnBlur
                disabled={!!classifierId}
                options={factSubtypeOptions}
                loading={isLoading}
                value={
                  !!field.value
                    ? factSubtypeOptions.find(
                        (opt) => opt.value === field.value
                      ) ?? {
                        label: field.value,
                        value: field.value,
                      }
                    : null
                }
                filterOptions={(options, params) => {
                  const filtered = filter(options, params);
                  const { inputValue } = params;
                  const isExisting = options.some(
                    (option) => inputValue === option.label
                  );
                  if (inputValue !== "" && !isExisting) {
                    filtered.push({
                      value: inputValue,
                      label: `Add "${inputValue}"`,
                    });
                  }

                  return filtered;
                }}
                onChange={(_, selected) => {
                  field.onChange(selected?.value);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Fact Subtype ID"
                    size="small"
                    error={!!errors.factName}
                    helperText={errors.factName?.message}
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {isLoading ? (
                            <CircularProgress color="inherit" size={20} />
                          ) : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                  />
                )}
              />
            )}
          />
        </FormField>
      </FormControl>

      <Divider />

      {!isSimplified && (
        <FormControl fullWidth margin="normal">
          <FormField label="Timing" sx={{ my: 0 }}>
            <Controller
              name="timingBeforeConsolidation"
              control={control}
              render={({ field }) => (
                <Box display={"flex"} gap={1} alignItems={"center"}>
                  <Checkbox
                    onChange={(_, checked) => field.onChange(checked)}
                    checked={field.value}
                  />
                  <Typography>
                    Should run before Data Consolidation (only 1st party
                    documents)
                  </Typography>
                </Box>
              )}
            />
          </FormField>
        </FormControl>
      )}
      {!watch("timingBeforeConsolidation") && (
        <>
          <FormControl fullWidth margin="normal">
            <FormField label="NAICS" sx={{ my: 0 }}>
              <Controller
                name="naics"
                control={control}
                render={({ field }) => (
                  <Box display={"flex"} gap={1}>
                    <NaicsCodeFilterComponent
                      filter={{
                        id: "naicsCode",
                        type: "naicsCode",
                        digits: 2,
                        inclusion: field.value?.operator ?? "include",
                        value: field.value?.values ?? [],
                      }}
                      replaceFilter={(_: string, filter: HubNaicsCodeFilter) =>
                        updateNaicsFilter(filter, field)
                      }
                    />
                  </Box>
                )}
              />
            </FormField>
          </FormControl>

          <FormControl fullWidth margin="normal">
            <FormField label={"Coverage"} sx={{ my: 0 }}>
              <Controller
                name="coverage"
                control={control}
                render={({ field }) => (
                  <Box display={"flex"} gap={1}>
                    <CoverageFilter
                      filter={{
                        id: "coverage",
                        type: "coverage",
                        inclusion: field.value?.operator ?? "include",
                        operator: "AND",
                        value: field.value?.values ?? [],
                      }}
                      updateFilterParam={(_, newFilter) => {
                        field.onChange({
                          operator: newFilter.inclusion,
                          values: newFilter.value,
                        });
                      }}
                    />
                  </Box>
                )}
              />
            </FormField>
          </FormControl>
        </>
      )}

      <FormControl
        fullWidth
        margin="normal"
        error={!!errors.applicableDocumentTypes}
      >
        <FormField label={"Applicable Document Types"} sx={{ my: 0 }}>
          <Controller
            name="applicableDocumentTypes"
            control={control}
            defaultValue={{}}
            rules={{
              required: "At least one document type is required",
              validate: (value) =>
                Object.keys(value || {}).length > 0 ||
                "At least one document type is required",
            }}
            render={({ field }) => {
              const currentValue = field.value || {};
              const selectedOptions = Object.entries(currentValue).map(
                ([id, data]) => ({
                  id,
                  label: data.label,
                  config: data.config || {},
                })
              );

              return (
                <>
                  <Autocomplete
                    multiple
                    freeSolo={false}
                    options={analyzeOptions}
                    value={selectedOptions}
                    onChange={(_, selected) => {
                      const newValue = selected.reduce(
                        (acc, option) => {
                          acc[option.id] = {
                            id: option.id,
                            label: option.label,
                            config: option.config || {},
                          };
                          return acc;
                        },
                        {} as Record<
                          string,
                          { id: string; label: string; config: any }
                        >
                      );

                      field.onChange(newValue);
                    }}
                    getOptionLabel={(option) => option.label}
                    isOptionEqualToValue={(option, value) =>
                      option.id === value.id
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        size="small"
                        error={!!errors.applicableDocumentTypes}
                        helperText={errors.applicableDocumentTypes?.message?.toString()}
                      />
                    )}
                    data-testid="shouldOnlyAnalyze"
                  />
                </>
              );
            }}
          />
        </FormField>
      </FormControl>

      <FormControl fullWidth margin="none" error={!!errors.description}>
        <FormField
          label={"Description"}
          sx={{ my: 0, alignItems: "flex-start" }}
          labelSx={{ mt: 2 }}
        >
          <Box display={"flex"} flexDirection={"column"}>
            <Controller
              name="description"
              control={control}
              defaultValue=""
              rules={{ required: "Description is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  margin="normal"
                  multiline
                  rows={3}
                  error={!!errors.description}
                  helperText={errors.description?.message}
                />
              )}
            />
            <LightbulbAlert>
              <strong>Hint: </strong>
              Specify the key details you want to extract from documents, such
              as policy terms, clauses, or risk factors. E.g., Identify
              exclusions related to specific risk and coverage limits for type
              of claim.
              <br />
              Description should contain 50–100 words.
            </LightbulbAlert>
          </Box>
        </FormField>
      </FormControl>

      <OutcomeValueTypeField classifierId={classifierId} />
      {!isSimplified && <AdvancedFieldsForm />}
    </>
  );
};
