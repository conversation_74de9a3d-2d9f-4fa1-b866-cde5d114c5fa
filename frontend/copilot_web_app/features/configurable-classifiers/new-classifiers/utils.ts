import {
  ClassifierConfigVersionExtractionTypeEnum,
  ClassifierVersion,
  CustomizableClassifierV2,
  FilterRuleFilterTypeEnum,
} from "@legacy/api_clients/copilot_api_client/api";
import {
  CoverageRule,
  CustomizableClassifierFormData,
  NaicsRule,
} from "./types";
import { KALEPA_MODEL } from "@features/configurable-classifiers/new-classifiers/DocumentGranularForm";
import { denormalizeNaics2, normalizeNaics2 } from "@utils/naics";

export const convertToApiData = (
  form: CustomizableClassifierFormData,
  orgId: number
): CustomizableClassifierV2 => {
  const naicsRule = form.naics.values.length
    ? {
        negated: form.naics.operator !== "include",
        values: normalizeNaics2(form.naics.values) as string[],
        filter_type: FilterRuleFilterTypeEnum.Naics,
      }
    : null;

  const coverageRule = form.coverage.values.length
    ? {
        negated: form.coverage.operator !== "include",
        values: form.coverage.values,
        filter_type: FilterRuleFilterTypeEnum.Coverage,
      }
    : null;

  const filterRules = [naicsRule, coverageRule].filter((x) => x !== null);

  const documentTypes = form.applicableDocumentTypes || {};
  const inputTypes = Object.keys(documentTypes);

  const configs = inputTypes
    .map((docType) => {
      const docConfig = documentTypes[docType].config;

      if (docConfig.modelingStrategy === KALEPA_MODEL) return null;

      const [inputProcessingType, extractionType] = (
        docConfig.inputProcessingType || ""
      ).split("__");

      return {
        input_type: docType,
        versions: [
          {
            input_processing_type: docConfig.inputProcessingType
              ? inputProcessingType
              : undefined,
            extraction_type: docConfig.inputProcessingType
              ? (extractionType as ClassifierConfigVersionExtractionTypeEnum)
              : undefined,
            prompt: docConfig.prompt,
            phrases: !docConfig.phrases?.length
              ? undefined
              : (docConfig.phrases || []).map((p) => ({
                  phrase: p.phrase,
                  excludes: p.excludes ? [p.excludes] : undefined,
                  weight: p.phraseRelevancy ?? undefined,
                })),
            llm_model: docConfig.model,
          },
        ],
      };
    })
    .filter(Boolean);

  const versions: ClassifierVersion[] = [
    {
      classifier_description: form.description,
      name: form.factName,
      configs: configs as NonNullable<ClassifierVersion["configs"]>,
    },
  ];

  const apiData: CustomizableClassifierV2 = {
    id: form.id,
    fact_subtype_id: form.factName,
    name: form.factName,
    run_in_pds: form.timingBeforeConsolidation,
    is_internal: false,
    tags: form.tags,
    input_types: inputTypes,
    organization_ids: [orgId],
    output_type:
      form.outcomeValueType as CustomizableClassifierV2["output_type"],
    output_unit: form.outcomeValueUnit || undefined,
    filter_rules: filterRules,
    versions,
  };
  return apiData;
};

export const convertToFormData = (
  api: CustomizableClassifierV2,
  versionId?: string
): CustomizableClassifierFormData => {
  const classifierVersion = api.versions?.find((v) => v.id === versionId);
  const baseConfigList = classifierVersion?.configs || [];

  const applicableDocumentTypes = baseConfigList.reduce(
    (acc, config) => {
      const docType = config.input_type;
      const version = config.versions?.[0];

      if (!version) return acc;

      const inputProcessingType = version.input_processing_type;
      const extractionType = version.extraction_type;
      const model = version.llm_model ?? undefined;
      const prompt = version.prompt ?? undefined;

      const phrases =
        version.phrases?.map((p) => ({
          phrase: p.phrase ?? "",
          excludes: p.excludes?.[0] || "",
          phraseRelevancy: p.weight || undefined,
        })) || [];

      const isKEM = (config.versions?.length ?? 0) > 1 || !model;

      acc[docType as string] = {
        config: {
          modelingStrategy: !isKEM ? "Manual" : KALEPA_MODEL,
          model,
          prompt,
          inputProcessingType: `${inputProcessingType}__${extractionType}`,
          phrases,
        },
        id: docType ?? "",
        label: docType ?? "",
      };

      return acc;
    },
    {} as CustomizableClassifierFormData["applicableDocumentTypes"]
  );

  for (const docType of api.input_types ?? []) {
    if (!applicableDocumentTypes[docType]) {
      applicableDocumentTypes[docType] = {
        id: docType,
        label: docType,
        config: {
          modelingStrategy: KALEPA_MODEL,
          model: undefined,
          prompt: undefined,
          inputProcessingType: undefined,
          phrases: [],
        },
      };
    }
  }

  const getNaicsCategory = (values: string[]) => {
    if (values.length && values[0].length === 6) {
      return "Industry (6-digit)";
    }
    return "Sector (2-digit)";
  };

  const getCoverage = (): CoverageRule => {
    const rule = api.filter_rules?.find(
      (r) => r.filter_type === FilterRuleFilterTypeEnum.Coverage
    );
    const values = rule?.values || [];
    return {
      values,
      operator: rule?.negated ? "exclude" : "include",
    };
  };

  const getNaics = (): NaicsRule => {
    const rule = api.filter_rules?.find(
      (r) => r.filter_type === FilterRuleFilterTypeEnum.Naics
    );
    const values = denormalizeNaics2(rule?.values || []) as string[];
    return {
      values,
      operator: rule?.negated ? "exclude" : "include",
      category: getNaicsCategory(values),
    };
  };

  const formData: CustomizableClassifierFormData = {
    id: api.id,
    factName: api.fact_subtype_id ?? "",
    description: classifierVersion?.classifier_description || "",
    timingBeforeConsolidation: api.run_in_pds ?? false,
    outcomeValueType: api.output_type ?? "",
    outcomeValueUnit: api.output_unit ?? undefined,
    naics: getNaics(),
    coverage: getCoverage(),
    tags: api.tags,
    applicableDocumentTypes,
  };
  return formData;
};
