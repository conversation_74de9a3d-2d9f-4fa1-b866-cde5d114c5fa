import {
  Autocomplete,
  Box,
  Button,
  FormControl,
  IconButton,
  MenuItem,
  Select,
  Stack,
  TextField,
} from "@mui/material";
import { Controller, useFormContext } from "react-hook-form";
import { FormField } from "@ui-patterns/form-components/FormField";
import React from "react";
import { CustomizableClassifierFormData } from "@features/configurable-classifiers/new-classifiers/types";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import { CustomizableClassifierV2OutputUnitEnum } from "@legacy/api_clients/copilot_api_client";

const outcomeValueTypes = [
  { value: "BOOLEAN", label: "Boolean (True/False)" },
  { value: "TAGS", label: "Tags" },
  { value: "NUMERIC", label: "Numeric" },
  { value: "DATE", label: "Date" },
  { value: "TEXT", label: "Short Text" },
];

export const OutcomeValueTypeField = ({
  classifierId,
}: {
  classifierId?: string;
}) => {
  const { watch, control } = useFormContext<CustomizableClassifierFormData>();

  const unitOptions = (
    Object.keys(CustomizableClassifierV2OutputUnitEnum) as Array<
      keyof typeof CustomizableClassifierV2OutputUnitEnum
    >
  ).map((key) => {
    return {
      value: CustomizableClassifierV2OutputUnitEnum[key],
      label: key,
    };
  });

  const outcomeValueType = watch("outcomeValueType");

  const applicableDocumentTypes = watch("applicableDocumentTypes") || {};

  const isAnyPhraseMatching = Object.keys(applicableDocumentTypes).some((k) => {
    const docType = applicableDocumentTypes[k];
    return (
      docType?.config?.inputProcessingType === "OCR_TEXT__PHRASES" ||
      docType?.config?.inputProcessingType === "KV_PAIRS__PHRASES"
    );
  });

  return (
    <>
      <FormControl fullWidth margin="none">
        <Controller
          name="outcomeValueType"
          control={control}
          defaultValue=""
          rules={{ required: "Outcome value type is required" }}
          render={({ field }) => (
            <FormField label="Outcome Value Type">
              <Select
                {...field}
                displayEmpty
                fullWidth
                disabled={!!classifierId}
              >
                {outcomeValueTypes.map((type) => (
                  <MenuItem
                    key={type.value}
                    value={type.value}
                    disabled={isAnyPhraseMatching && type.value !== "BOOLEAN"}
                  >
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormField>
          )}
        />
      </FormControl>
      {outcomeValueType === "NUMERIC" && (
        <FormControl fullWidth margin="none">
          <Controller
            name="outcomeValueUnit"
            control={control}
            render={({ field }) => (
              <FormField label=" " sx={{ mt: 0 }}>
                <Box bgcolor="#FAFCFF" sx={{ padding: 3 }}>
                  <Autocomplete
                    selectOnFocus
                    clearOnBlur
                    options={unitOptions}
                    value={
                      unitOptions.find((opt) => opt.value === field.value) ||
                      null
                    }
                    onChange={(_, selected) => {
                      const value = selected?.value || "";
                      field.onChange(value);
                    }}
                    renderInput={(params) => (
                      <TextField {...params} label={"Unit"} size="small" />
                    )}
                  />
                </Box>
              </FormField>
            )}
          />
        </FormControl>
      )}
      {outcomeValueType === "TAGS" && (
        <FormControl fullWidth margin="none">
          <Controller
            name="tags"
            control={control}
            defaultValue={[""]}
            render={({ field }) => {
              const { value = [], onChange } = field;

              const updateTag = (index: number, newValue: string) => {
                const updated = [...value];
                updated[index] = newValue;
                onChange(updated);
              };

              const removeTag = (index: number) => {
                const updated = [...value];
                updated.splice(index, 1);
                onChange(updated);
              };

              const addTag = () => {
                onChange([...value, ""]);
              };

              return (
                <FormField label=" " sx={{ mt: 0 }}>
                  <Box bgcolor="#FAFCFF" sx={{ padding: 3 }}>
                    <Stack spacing={2}>
                      {value.map((tag, index) => (
                        <Stack direction="row" spacing={1} key={index}>
                          <TextField
                            fullWidth
                            value={tag}
                            onChange={(e) => updateTag(index, e.target.value)}
                            label={`Tag ${index + 1}`}
                            size="small"
                          />
                          <IconButton
                            onClick={() => removeTag(index)}
                            edge="end"
                            aria-label="delete"
                          >
                            <DeleteOutlineIcon />
                          </IconButton>
                        </Stack>
                      ))}
                      <Button
                        onClick={addTag}
                        variant="text"
                        sx={{
                          justifyContent: "flex-start",
                          alignItems: "center",
                        }}
                      >
                        + Add Tag
                      </Button>
                    </Stack>
                  </Box>
                </FormField>
              );
            }}
          />
        </FormControl>
      )}
    </>
  );
};
