import { CustomizableClassifierV2OutputUnitEnum } from "@legacy/api_clients/copilot_api_client/api";

export type CustomizableClassifierFormData = {
  id?: string;
  factName: string;
  timingBeforeConsolidation: boolean;

  naics: NaicsRule;

  coverage: CoverageRule;

  applicableDocumentTypes: {
    [key: string]: {
      label: string;
      id: string;
      config: AdvancedConfigFormData;
    };
  };

  description: string;
  outcomeValueType: string;
  outcomeValueUnit?: CustomizableClassifierV2OutputUnitEnum;
  tags?: string[];
};

export type AdvancedConfigFormData = {
  modelingStrategy: string;
  model?: string;
  inputProcessingType?: string;
  prompt?: string;
  phrases: Phrase[];
};

export type Phrase = {
  phrase: string;
  excludes?: string;
  phraseRelevancy?: number;
};

export type NaicsRule = {
  values: string[];
  operator: "include" | "exclude";
  category: string;
};

export type CoverageRule = {
  values: string[];
  operator: "include" | "exclude";
};
