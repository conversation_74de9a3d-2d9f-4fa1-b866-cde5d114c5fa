import { Divide<PERSON>, <PERSON>, Switch, Typography, Tab, Tabs } from "@mui/material";
import { useFormContext } from "react-hook-form";
import { CustomizableClassifierFormData } from "@features/configurable-classifiers/new-classifiers/types";
import { DocumentGranularForm } from "@features/configurable-classifiers/new-classifiers/DocumentGranularForm";
import React, { useEffect } from "react";

export const AdvancedFieldsForm = () => {
  const { watch } = useFormContext<CustomizableClassifierFormData>();

  const documentTypes = watch("applicableDocumentTypes") || {};
  const documentTypeKeys = Object.keys(documentTypes);

  useEffect(() => {
    const hasVaryingDocTypes = (): boolean => {
      const values = Object.values(watch("applicableDocumentTypes") || {});
      if (values.length < 2) return false;
      const [first, ...rest] = values;
      return rest.some(
        (item) => JSON.stringify(item.config) !== JSON.stringify(first.config)
      );
    };
    setDivideByDocType(hasVaryingDocTypes());
  }, [watch]);

  const [divideByDocType, setDivideByDocType] = React.useState(false);
  const [currentTab, setCurrentTab] = React.useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleDivideByDocTypeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newDivideByDocType = event.target.checked;
    setDivideByDocType(newDivideByDocType);
  };

  return (
    <>
      <Box
        display={"flex"}
        flexDirection={"row"}
        justifyContent={"space-between"}
        alignItems="center"
      >
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          {divideByDocType ? (
            documentTypeKeys.map((docType, index) => (
              <Tab
                key={docType}
                label={documentTypes[docType].label}
                value={index}
              />
            ))
          ) : (
            <Tab label="All Files" value={0} />
          )}
        </Tabs>
        <Box
          display={"flex"}
          flexDirection={"row"}
          alignItems={"center"}
          gap={1}
        >
          <Switch
            checked={divideByDocType}
            size="small"
            color="primary"
            disabled={documentTypeKeys.length < 2 || divideByDocType}
            onChange={handleDivideByDocTypeChange}
          />
          <Typography>Divide by Document Type</Typography>
        </Box>
      </Box>
      <Divider sx={{ my: 2 }} />
      {divideByDocType ? (
        <DocumentGranularForm
          documentType={documentTypeKeys[currentTab]}
          key={documentTypeKeys[currentTab]}
        />
      ) : (
        <DocumentGranularForm
          documentType={documentTypeKeys[0]}
          isGlobalConfig={true}
        />
      )}
    </>
  );
};
