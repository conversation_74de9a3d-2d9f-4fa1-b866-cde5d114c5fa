import React, { ReactNode } from "react";
import { TextField, FormControl, Autocomplete, Box } from "@mui/material";
import { Controller, Path, useFormContext } from "react-hook-form";
import { FormField } from "@ui-patterns/form-components/FormField";
import {
  CustomizableClassifierFormData,
  Phrase,
} from "@features/configurable-classifiers/new-classifiers/types";
import { LLM_MODEL_TYPES_OPTIONS } from "@features/configurable-classifiers/constants";
import { LightbulbAlert } from "@ui-patterns/lightbulb-alert";
import { PhraseInputList } from "@features/configurable-classifiers/new-classifiers/PhraseInputList";

export const KALEPA_MODEL = "Kalepa Ensemble Model";

const KALEPA_MODEL_OPTION = {
  label: KALEPA_MODEL,
  value: KALEPA_MODEL,
};
const MANUAL_OPTION = {
  label: "Manual",
  value: "Manual",
};

interface AutocompleteFormControlProps {
  name: Path<CustomizableClassifierFormData>;
  label: string;
  options: { label: string; value: string; disabled?: boolean }[];
  bottomComponent?: ReactNode;
  onChange?: (value: string) => void;
  rules?: { required: boolean | string };
  error?: boolean;
}

const AutocompleteFormControl = ({
  name,
  label,
  options,
  bottomComponent,
  onChange,
  rules,
  error,
}: AutocompleteFormControlProps) => {
  const { control } = useFormContext<CustomizableClassifierFormData>();

  return (
    <FormControl fullWidth margin="normal" error={error}>
      <FormField
        label={label}
        sx={{ my: 0, alignItems: !!bottomComponent ? "flex-start" : undefined }}
      >
        <Box display={"flex"} flexDirection={"column"} gap={1}>
          <Controller
            name={name}
            control={control}
            rules={rules}
            render={({ field }) => (
              <Autocomplete
                selectOnFocus
                clearOnBlur
                options={options}
                getOptionDisabled={(option) => option.disabled === true}
                value={options.find((opt) => opt.value === field.value) || null}
                onChange={(_, selected) => {
                  const value = selected?.value || "";
                  field.onChange(value);
                  onChange?.(value);
                }}
                renderInput={(params) => (
                  <TextField {...params} label={label} size="small" />
                )}
              />
            )}
          />
          {bottomComponent}
        </Box>
      </FormField>
    </FormControl>
  );
};

interface DocumentGranularFormProps {
  documentType: string;
  isGlobalConfig?: boolean;
}

export const DocumentGranularForm = ({
  documentType,
  isGlobalConfig,
}: DocumentGranularFormProps) => {
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext<CustomizableClassifierFormData>();

  const documentTypes = watch("applicableDocumentTypes") || {};
  const currentDocumentConfig = documentTypes[documentType]?.config;
  const isPhraseMatching = ["OCR_TEXT__PHRASES", "KV_PAIRS__PHRASES"].includes(
    currentDocumentConfig?.inputProcessingType ?? ""
  );

  const outcomeValueType = watch("outcomeValueType");
  const isManual = currentDocumentConfig?.modelingStrategy !== KALEPA_MODEL;

  const handleConfigChange = (
    field: keyof NonNullable<typeof currentDocumentConfig>,
    value: string | Phrase[]
  ) => {
    if (isGlobalConfig) {
      // Update all document types with the same config
      const updatedDocTypes = { ...documentTypes };
      Object.keys(updatedDocTypes).forEach((key) => {
        updatedDocTypes[key] = {
          ...updatedDocTypes[key],
          config: {
            ...updatedDocTypes[key].config,
            [field]: value,
          },
        };
      });
      setValue("applicableDocumentTypes", updatedDocTypes);
    } else {
      // Update only the current document type
      setValue(
        `applicableDocumentTypes.${documentType}.config.${field}`,
        value
      );
    }
  };

  const classifierTypeOptions = [
    {
      label: "LLM with phrases on text",
      value: "OCR_TEXT__PHRASES_WITH_LLM",
    },

    {
      label: "LLM with phrases on processed data",
      value: "KV_PAIRS__PHRASES_WITH_LLM",
    },
    {
      label: "LLM on Image",
      value: "IMAGE__LLM",
    },
    {
      label: "Phrase matching on Text",
      value: "OCR_TEXT__PHRASES",
      disabled: outcomeValueType !== "BOOLEAN",
    },
    {
      label: "Phrase matching on processed data",
      value: "KV_PAIRS__PHRASES",
      disabled: outcomeValueType !== "BOOLEAN",
    },
  ];

  return (
    <>
      <AutocompleteFormControl
        name={`applicableDocumentTypes.${documentType}.config.modelingStrategy`}
        label="Modeling Strategy"
        options={[KALEPA_MODEL_OPTION, MANUAL_OPTION]}
        onChange={(value) => handleConfigChange("modelingStrategy", value)}
        bottomComponent={
          isManual ? undefined : (
            <LightbulbAlert>
              <strong>
                Ideal for getting top results without the manual effort.
              </strong>
              <br />
              {
                "We'll optimize the setup for you by testing multiple approaches and selecting the best strategy for performance and cost."
              }
            </LightbulbAlert>
          )
        }
      />
      {isManual && (
        <>
          <AutocompleteFormControl
            name={`applicableDocumentTypes.${documentType}.config.inputProcessingType`}
            label="Input Processing Type"
            options={classifierTypeOptions}
            onChange={(value) =>
              handleConfigChange("inputProcessingType", value)
            }
            rules={{
              required: isManual
                ? "Input Processing Type is required when using Manual modeling strategy"
                : true,
            }}
            error={
              !!errors.applicableDocumentTypes?.[documentType]?.config
                ?.inputProcessingType
            }
          />
          {!isPhraseMatching && (
            <>
              <AutocompleteFormControl
                name={`applicableDocumentTypes.${documentType}.config.model`}
                label="Model"
                options={LLM_MODEL_TYPES_OPTIONS}
                onChange={(value) => handleConfigChange("model", value)}
                rules={{
                  required: isManual
                    ? "Model is required when using Manual modeling strategy"
                    : true,
                }}
                error={
                  !!errors.applicableDocumentTypes?.[documentType]?.config
                    ?.model
                }
              />
              <FormControl
                fullWidth
                margin="normal"
                error={
                  !!errors.applicableDocumentTypes?.[documentType]?.config
                    ?.prompt
                }
              >
                <FormField
                  label="Prompt"
                  sx={{ my: 0, alignItems: "flex-start" }}
                >
                  <Controller
                    name={`applicableDocumentTypes.${documentType}.config.prompt`}
                    control={control}
                    defaultValue=""
                    rules={{
                      required: isManual
                        ? "Prompt is required when using Manual modeling strategy"
                        : true,
                    }}
                    render={({ field }) => (
                      <Box display={"flex"} flexDirection={"column"} gap={1}>
                        <TextField
                          {...field}
                          fullWidth
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            handleConfigChange("prompt", e.target.value);
                          }}
                          multiline
                          size="small"
                          rows={3}
                          error={
                            !!errors.applicableDocumentTypes?.[documentType]
                              ?.config?.prompt
                          }
                          helperText={
                            errors.applicableDocumentTypes?.[documentType]
                              ?.config?.prompt?.message
                          }
                        />
                        <LightbulbAlert>
                          <strong>Hint: </strong>
                          Provide a prompt for the LLM that ensures a neutral,
                          informative, and concise response without specifying
                          the format of the answer.
                        </LightbulbAlert>
                      </Box>
                    )}
                  />
                </FormField>
              </FormControl>
            </>
          )}
          <PhraseInputList
            control={control}
            name={`applicableDocumentTypes.${documentType}.config.phrases`}
            label={isPhraseMatching ? "Phrases" : "Phrases Optimizations"}
            description={
              isPhraseMatching
                ? ""
                : "Add phrases to filter relevant snippets for the LLM prompt to analyze"
            }
            showPhraseRelevancy={isPhraseMatching}
            handleConfigChange={(value: Phrase[]) =>
              handleConfigChange("phrases", value)
            }
          />
        </>
      )}
    </>
  );
};
