import React, { ReactElement, useMemo, useState } from "react";
import { useRouter } from "next/router";
import {
  Box,
  CircularProgress,
  IconButton,
  Switch,
  Typography,
} from "@mui/material";
import { CardTable } from "@ui-patterns/card-table";
import { GridColDef } from "@mui/x-data-grid-pro";
import { Dialog } from "@ui-patterns/dialog";
import {
  useActivateClassifierVersionMutation,
  useCustomizableClassifiersV2,
  useDeleteCustomizableClassifierV2,
} from "@queries/customizableClassifiers";
import { useSnackbar } from "notistack";
import { AxiosError } from "axios";
import { Trash } from "@ui-patterns/icons/trash";
import EditIcon from "@mui/icons-material/Edit";

type VersionRow = {
  classifierId: string;
  id: string;
  is_active: boolean;
};

interface ConfigurableClassifiersDialogProps {
  factSubtypeId: string;
}

export const ClassifiersVersionsDialog: React.FC<
  ConfigurableClassifiersDialogProps
> = ({ factSubtypeId }): ReactElement => {
  const router = useRouter();
  const { enqueueSnackbar } = useSnackbar();

  const [selectedClassifier, setSelectedClassifier] = useState<VersionRow>();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const { data: classifiers, isLoading } =
    useCustomizableClassifiersV2(factSubtypeId);

  const { mutate: activateVersion } =
    useActivateClassifierVersionMutation(factSubtypeId);

  const { mutateAsync: deleteClassifier } =
    useDeleteCustomizableClassifierV2(factSubtypeId);

  const rows: VersionRow[] = useMemo(() => {
    return classifiers
      ?.flatMap(
        (c) =>
          c.versions?.map((version) => ({
            classifierId: c.id,
            id: version.id,
            is_active: version.is_active,
          })) || []
      )
      .filter((x) => !!x) as VersionRow[];
  }, [classifiers]);

  if (isLoading || !classifiers) return <CircularProgress />;

  const handleToggleActive = (row: VersionRow) => {
    activateVersion(
      {
        classifierId: row.classifierId,
        versionId: row.id,
        isActive: !row.is_active,
      },
      {
        onSuccess: () => {
          enqueueSnackbar(
            `Classifier ${row.is_active ? "deactivated" : "activated"}`
          );
        },
        onError: (error) => {
          const axiosError = error as AxiosError;
          enqueueSnackbar(
            `Error updating classifier: ${
              (axiosError.response?.data as any)?.detail || axiosError.message
            }`,
            { variant: "error" }
          );
        },
      }
    );
  };

  const handleEdit = (classifierId: string, versionId: string) => {
    if (classifierId) {
      router.push(
        `/classifiers/${classifierId}/edit?factSubtypeId=${factSubtypeId}&versionId=${versionId}`
      );
    }
  };

  const handleDelete = (version: VersionRow) => {
    if (!version.id) return;
    setSelectedClassifier(version);
    setShowDeleteModal(true);
  };

  const columns: GridColDef[] = [
    {
      field: "id",
      headerName: "ID",
      minWidth: 350,
      renderCell: (params) => (
        <Box
          sx={{ cursor: "pointer" }}
          onClick={() => handleEdit(params.row.classifierId, params.row.id)}
        >
          {params.row.id}
        </Box>
      ),
    },
    {
      field: "edit",
      headerName: "",
      width: 50,
      renderCell: (params) => (
        <IconButton
          onClick={() => handleEdit(params.row.classifierId, params.row.id)}
        >
          <EditIcon />
        </IconButton>
      ),
    },
    {
      field: "is_active",
      headerName: "Status",
      width: 150,
      renderCell: (params) => (
        <Box display="flex" alignItems="center" gap={1}>
          <Switch
            size="small"
            checked={params.row?.is_active}
            onChange={() => handleToggleActive(params.row)}
          />
          <Typography variant="body2">
            {params.row?.is_active ? "Active" : "Inactive"}
          </Typography>
        </Box>
      ),
    },
    {
      field: "delete",
      headerName: "",
      width: 50,
      renderCell: (params) => (
        <IconButton onClick={() => handleDelete(params.row)}>
          <Trash />
        </IconButton>
      ),
    },
  ];

  return (
    <Box>
      <CardTable
        trackingTableType="configurable classifiers"
        title={
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h6">Versions</Typography>
          </Box>
        }
        columns={columns}
        rows={rows}
        allowSearch
      />

      {showDeleteModal && selectedClassifier && (
        <Dialog
          title="Delete Classifier"
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedClassifier(undefined);
          }}
          primaryActionText="Delete"
          onPrimaryAction={() => {
            if (selectedClassifier.id) {
              deleteClassifier({
                classifierId: selectedClassifier?.classifierId,
                versionId: selectedClassifier.id,
              }).then(() => setShowDeleteModal(false));
            }
          }}
        >
          <Typography>
            Are you sure you want to delete this classifier?
          </Typography>
        </Dialog>
      )}
    </Box>
  );
};
