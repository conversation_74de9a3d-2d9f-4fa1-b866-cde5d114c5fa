import { useMemo, useState } from "react";
import { FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Box,
  Button,
  Collapse,
  InputAdornment,
  Stack,
  Badge,
  Typography,
  Tooltip,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import { usePlacesWidget } from "react-google-autocomplete";
import { useCopilotForm } from "@utils/form";
import {
  ErsSearchData,
  ErsSearchFormFields,
  useErsSearchSchema,
  useInitialIdentifiersValues,
  useInitialIndustriesValues,
} from "./utils";
import { config } from "config";
import { RequestedPropertiesRequestedIdentifiersInner } from "@legacy/api_clients/copilot_api_client";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { ForceCreateSimpleEntity } from "./components/ForceCreateSimpleEntity";
import { AdvancedSearchFields } from "./components/AdvancedSearchFields";
import { AdvancedSearchDialog } from "./components/AdvancedSearchDialog";
import { useSnackbar } from "notistack";

type Props = {
  initialName?: string;
  initialAddress?: string;
  initialNamedInsured?: string;
  initialIdentifiers?: ReadonlyArray<RequestedPropertiesRequestedIdentifiersInner>;
  initialIndustries?: ReadonlyArray<string>;
  onSearch: (form: ErsSearchData, category?: string) => void;
};

export const ErsSearchForm = ({
  initialName,
  initialAddress,
  initialNamedInsured,
  initialIdentifiers,
  initialIndustries,
  onSearch,
}: Props) => {
  const [isAdvancedOpen, setAdvancedOpen] = useState(false);
  const [isLegalEntityDialogOpen, setIsLegalEntityDialogOpen] = useState(false);
  const { schema, createSearchData, ersIdentifiers } = useErsSearchSchema();
  const identifiers = useInitialIdentifiersValues(initialIdentifiers);
  const industries = Array.from(useInitialIndustriesValues(initialIndustries));
  const [creatingAreaEstate, setCreatingAreaEstate] = useState(false);
  const [creatingShell, setCreatingShell] = useState(false);
  const { isCSManager } = useAccessPermissions();
  const { enqueueSnackbar } = useSnackbar();

  const isAreaEstateDisabled =
    initialNamedInsured === "FIRST_NAMED_INSURED" ||
    initialNamedInsured === "OTHER_NAMED_INSURED";
  const form = useCopilotForm<ErsSearchFormFields>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: initialName,
      address: initialAddress,
      identifiers,
      industries,
      forceCreate: false,
    },
  });

  const { ref } = usePlacesWidget({
    apiKey: config.googleMapsApiKey,
    onPlaceSelected: (place: { formatted_address: string }) =>
      form.setValue("address", place.formatted_address),
    options: {
      types: ["address"],
    },
  });

  const { FormTextField, handleSubmit, control, watch } = form;

  const watchedFields = watch(["identifiers", "industries"]);

  const advancedOptions = useMemo(() => {
    let count = 0;
    let text = "";

    if (watchedFields[0]) {
      const identifiers = watchedFields[0] as Record<string, string>;
      Object.keys(identifiers).forEach((key) => {
        if (identifiers[key]) {
          count++;
          text += `${key}:${identifiers[key]} `;
        }
      });
    }

    if (watchedFields[1]?.length) {
      count++;
      const industries = watchedFields[1] as string[];
      text += `INDUSTRIES:${industries
        .filter((industry) => industry)
        .join(",")} `;
    }

    return {
      count,
      text,
    };
  }, [watchedFields]);

  const selectedAdvancedOptionsCount = advancedOptions.count;
  const advancedSearchText = advancedOptions.text.trim();

  const handleCreatingAreaEstate = () => {
    setCreatingAreaEstate(true);
  };

  const handleLegalEntityDialogOpen = () => {
    setIsLegalEntityDialogOpen(true);
  };

  const handleLegalEntityDialogClose = () => {
    setIsLegalEntityDialogOpen(false);
  };

  const handleLegalEntityDialogSubmit = async (data: ErsSearchFormFields) => {
    const { name, address, identifiers, industries } = data;
    const category = "LEGAL";
    try {
      form.setValue("name", name);
      form.setValue("address", address);
      form.setValue("identifiers", identifiers || []);
      form.setValue("industries", industries || []);
      form.setValue("forceCreate", true);

      handleSubmit((data) => onSearch(createSearchData(data), category))();
      setIsLegalEntityDialogOpen(false);
    } catch (error) {
      enqueueSnackbar("Error encountered upon creating entity.", {
        variant: "error",
      });
    }
  };

  const handleForceCreatingAreaEstateSubmit = (
    entity: string | undefined,
    entityAddress: string
  ) => {
    if (entity) form.setValue("name", entity);
    form.setValue("address", entityAddress);
    form.setValue("forceCreate", true);
    handleSubmit((data) => onSearch(createSearchData(data)))();
  };

  const handleCreateShell = (
    entity: string | undefined,
    entityAddress: string
  ) => {
    if (entity) form.setValue("name", entity);
    form.setValue("address", entityAddress);
    form.setValue("forceCreate", true);
    handleSubmit((data) => onSearch(createSearchData(data), "SHELL"))();
  };

  return (
    <FormProvider {...form}>
      {advancedSearchText && (
        <Box mb={2}>
          <Typography variant="body1" color="#4BAAC8">
            Advanced Search Fields Applied:
          </Typography>
          <Box>
            {advancedSearchText.split(/\s(?=\w+:)/).map((item, index) => {
              const [key, ...rest] = item.split(":");
              const value = rest.join(":");
              return (
                <Typography key={index} variant="body1" color="#4BAAC8">
                  {`${key}: ${value}`}
                </Typography>
              );
            })}
          </Box>
        </Box>
      )}
      <Box
        component="form"
        onSubmit={handleSubmit((data) => {
          onSearch(createSearchData(data));
        })}
      >
        <Stack spacing={1}>
          <FormTextField
            label="Search entity"
            name="name"
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <FormTextField label="Address" name="address" inputRef={ref} />
          <Box display="flex" justifyContent="space-between">
            <Button
              variant="text"
              onClick={() => setAdvancedOpen(!isAdvancedOpen)}
              startIcon={
                <Badge
                  badgeContent={selectedAdvancedOptionsCount}
                  color="primary"
                >
                  <FilterListIcon />
                </Badge>
              }
            >
              {isAdvancedOpen ? "Close advanced options" : "Advanced Options"}
            </Button>
            {!isAdvancedOpen && (
              <Button variant="outlined" type="submit">
                Search
              </Button>
            )}
          </Box>
          <Collapse in={isAdvancedOpen}>
            <AdvancedSearchFields
              ersIdentifiers={ersIdentifiers}
              control={control}
            />

            <Box display="flex" justifyContent="flex-end" mt={3}>
              <Button variant="outlined" type="submit">
                Search
              </Button>
            </Box>
            {isCSManager && (
              <Typography
                variant="body2"
                mb={2}
                ml={1}
                fontSize={14}
                color={"gray"}
              >
                OR Force creating entity
              </Typography>
            )}
            {isCSManager && (
              <Box display="flex" alignItems="center">
                <Tooltip
                  title={isAreaEstateDisabled ? "Disabled for FNI and ONI" : ""}
                >
                  <div>
                    <Button
                      disabled={isAreaEstateDisabled}
                      onClick={handleCreatingAreaEstate}
                      sx={{ minWidth: "130px", mb: "25px" }}
                      variant="outlined"
                    >
                      AREA ESTATE
                    </Button>
                  </div>
                </Tooltip>
                <Typography variant="body2" ml={1} mb={3}>
                  -{">"} for example Vacant Land, or 10 acres parcel
                </Typography>
                {creatingAreaEstate && (
                  <ForceCreateSimpleEntity
                    onClose={() => setCreatingAreaEstate(false)}
                    setForceCreate={(value) =>
                      form.setValue("forceCreate", value)
                    }
                    onSubmit={handleForceCreatingAreaEstateSubmit}
                  />
                )}
              </Box>
            )}
            {isCSManager && (
              <Box display="flex" alignItems="center">
                <Button
                  onClick={() => setCreatingShell(true)}
                  sx={{ minWidth: "130px", mb: "25px" }}
                  variant="outlined"
                >
                  SHELL
                </Button>

                <Typography variant="body2" ml={1} mb={3}>
                  -{">"} shell entity
                </Typography>
                {creatingShell && (
                  <ForceCreateSimpleEntity
                    title="SHELL"
                    onClose={() => setCreatingShell(false)}
                    setForceCreate={(value) =>
                      form.setValue("forceCreate", value)
                    }
                    onSubmit={handleCreateShell}
                  />
                )}
              </Box>
            )}
            {isCSManager && (
              <Box display="flex" alignItems="center">
                <Button
                  onClick={handleLegalEntityDialogOpen}
                  sx={{ minWidth: "130px" }}
                  variant="outlined"
                >
                  LEGAL ENTITY
                </Button>
                <Typography variant="body2" ml={1}>
                  -{">"} Business without exact address. Make sure that the name
                  you are using is the full business (legal) name (like Kalepa
                  Corporation)
                </Typography>
              </Box>
            )}
          </Collapse>
        </Stack>
      </Box>
      <AdvancedSearchDialog
        open={isLegalEntityDialogOpen}
        onClose={handleLegalEntityDialogClose}
        defaultValues={form.getValues()}
        onSubmit={handleLegalEntityDialogSubmit}
      />
    </FormProvider>
  );
};
export type { ErsSearchData };
