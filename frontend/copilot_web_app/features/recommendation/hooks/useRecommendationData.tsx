import { useReport } from "@utils/useReport";
import {
  useRecommendationVariablesQuery,
  useRulesQuery,
  useSubmissionRecommendationsQuery,
} from "@queries/recommendationsV2";
import { createContext, useContext, useMemo } from "react";
import { useMemoArray } from "@utils/useMemoArray";
import {
  type AttachFilesResponse,
  type FlagMissingDocumentsResponse,
  type ScoreOutcomeResponse,
  TextOutcomeResponse,
  VariableResponse,
} from "@legacy/api_clients/recommendations-client";
import { getAllOutcomes } from "@features/recommendations-panel/utils";
import Report from "@legacy/models/Report";
import { isDefined } from "@utils/types";

const EMPTY_ARR: any[] = [];

const useRecommendationData = (submissionId?: string) => {
  const report: Report | undefined = useReport();
  const { data: recommendation, isLoading: isLoadingRecommendation } =
    useSubmissionRecommendationsQuery(
      submissionId ?? report?.getSubmission().id
    );

  const rules = useMemo(() => {
    return Array.from(
      new Set(
        getAllOutcomes(recommendation).flatMap((outcome) =>
          outcome.outcome_explanations
            .flatMap((oe) => oe.rule)
            .filter(isDefined)
        )
      )
    );
  }, [recommendation]);

  const { data: fullRules } = useRulesQuery(
    {
      ruleIds: rules.map((r) => r.id),
      expand: ["FULL_SOURCES"],
    },
    {
      enabled: rules.length > 0,
    }
  );

  const portfolioRuleIds = useMemo(
    () =>
      new Set(
        rules
          .filter((r) => r.rule_type === "PORTFOLIO_OPTIMIZATION")
          .map((r) => r.id)
      ),
    [rules]
  );
  const { data: variables, isLoading: isLoadingVariables } =
    useRecommendationVariablesQuery(
      Boolean(submissionId ?? report?.getSubmission().id),
      {
        submissionIds: [submissionId ?? report?.getSubmission().id].filter(
          (id): id is string => Boolean(id)
        ),
      }
    );

  const variablesMap = useMemo(() => {
    const result = new Map<string, VariableResponse>();
    variables?.forEach((variable) => {
      result.set(variable.id, variable);
    });
    return result;
  }, [variables]);

  const mainRecommendationAction =
    recommendation?.recommendation_outcomes?.[0]?.value;

  const exclusions: TextOutcomeResponse[] = useMemoArray(
    recommendation?.text_outcomes?.filter(
      (outcome) => outcome.type === "EXCLUSION"
    ) ?? EMPTY_ARR
  );

  const missingDocs: FlagMissingDocumentsResponse[] = useMemoArray(
    recommendation?.flag_missing_documents_outcomes?.filter(
      (v) => v.value.length > 0
    ) ?? EMPTY_ARR
  );

  const attachFiles: AttachFilesResponse[] = useMemoArray(
    recommendation?.attach_files_outcomes ?? EMPTY_ARR
  );

  const refers = useMemoArray(recommendation?.refer_outcomes ?? []);

  const score: ScoreOutcomeResponse | null =
    recommendation?.score_outcomes?.[0] ?? null;

  const isLoading = isLoadingVariables || isLoadingRecommendation;

  return useMemo(
    () => ({
      recommendation,
      mainRecommendationAction,
      exclusions,
      missingDocs,
      attachFiles,
      rules,
      ruleIds: rules.map((rule) => rule.id),
      refers,
      variablesMap,
      score,
      portfolioRuleIds,
      isLoading,
      isLoadingRecommendation,
      report,
      fullRules: fullRules?.items ?? [],
    }),
    [
      attachFiles,
      exclusions,
      isLoading,
      isLoadingRecommendation,
      mainRecommendationAction,
      missingDocs,
      recommendation,
      rules,
      refers,
      score,
      portfolioRuleIds,
      variablesMap,
      report,
      fullRules,
    ]
  );
};

type RecommendationContextData = ReturnType<typeof useRecommendationData>;

const RecommendationContext = createContext<RecommendationContextData | null>(
  null
);

export const useRecommendationContext = () => {
  const context = useContext(RecommendationContext);
  if (!context) {
    throw new Error(
      "useRecommendationContext must be used within a RecommendationContextProvider"
    );
  }
  return context;
};

type RecommendationContextProviderProps = {
  submissionId?: string;
  children: React.ReactNode;
};

export const RecommendationContextProvider = ({
  submissionId,
  children,
}: RecommendationContextProviderProps) => {
  const value = useRecommendationData(submissionId);
  return (
    <RecommendationContext.Provider value={value}>
      {children}
    </RecommendationContext.Provider>
  );
};
