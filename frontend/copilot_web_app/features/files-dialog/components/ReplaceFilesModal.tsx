import React, { useEffect, useState } from "react";
import { Box, Typography, useTheme } from "@mui/material";
import Report from "@legacy/models/Report";
import { ModelFileFileTypeEnum } from "@legacy/api_clients/copilot_api_client";
import FileModel from "@legacy/models/File";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { Dialog } from "@ui-patterns/dialog";
import { FilesTable } from "@features/files";
import File from "@legacy/models/File";

const possibleInternalTypes: Set<ModelFileFileTypeEnum> = new Set([
  ModelFileFileTypeEnum.Drivers,
  ModelFileFileTypeEnum.Vehicles,
  ModelFileFileTypeEnum.Sov,
  ModelFileFileTypeEnum.AcordForm,
  ModelFileFileTypeEnum.Unknown,
]);

type ReplaceFilesModalProps = {
  onSubmit: (files: string[]) => void;
  onClose: () => void;
  report: Report;
  fileName: string;
  selectedFiles: FileModel[];
};

export const ReplaceFilesModal = ({
  onSubmit,
  onClose,
  report,
  fileName,
  selectedFiles,
}: ReplaceFilesModalProps) => {
  const [filesToReplace, setFilesToReplace] = useState<string[]>([]);
  const { isCSManager } = useAccessPermissions();
  const theme = useTheme();

  useEffect(() => {
    setFilesToReplace(selectedFiles.map((f) => f.id));
  }, [selectedFiles]);

  // Current method of uploading files is left intact from the old implementation
  // overall our way of uploading files is not working correctly
  // and should be fixed in ENG-12207

  const allFiles = report.getSubmission().files;
  const sovFiles = allFiles.filter(
    (file) => file.file_type && possibleInternalTypes.has(file.file_type)
  );
  const initialFiles = isCSManager ? allFiles : sovFiles;

  const replaceableFiles = isCSManager
    ? initialFiles
    : initialFiles.filter((file) => {
        const parentFile = file.parent_file_id
          ? report?.getFileById(file.parent_file_id)
          : null;
        return !(file.is_internal && parentFile && parentFile.isAcord());
      });

  const onSelectFile = (file: File) => {
    if (filesToReplace.some((f) => f === file.id)) {
      setFilesToReplace(filesToReplace.filter((f) => f !== file.id));
    } else {
      setFilesToReplace([...filesToReplace, file.id]);
    }
  };

  return (
    <Dialog
      title="Replace Files"
      onClose={onClose}
      maxWidth="md"
      fullWidth
      onPrimaryAction={() => {
        onSubmit(filesToReplace);
        onClose();
      }}
    >
      <Box sx={{ flexDirection: "column", display: "flex" }}>
        <Typography variant="h6">{fileName}</Typography>
        <Typography variant="subtitle1" sx={{ pt: 1 }}>
          Which files do you want to replace?
        </Typography>
        <Typography
          variant="subtitle1"
          sx={{ pt: 1, color: theme.palette.error.main }}
        >
          IMPORTANT: If you are submitting a newly formatted document, like an
          SOV or fleet list, you will need to replace the original file that
          needed formatting. If you are adding an SOV or fleet list that
          previously wasn’t processed, simply replace the file where you found
          the SOV/vehicles/driver.
        </Typography>
        <FilesTable
          report={report}
          readOnly
          onSelect={onSelectFile}
          selectedFiles={filesToReplace}
          selectableFiles={replaceableFiles.map((f) => f.id)}
        />
      </Box>
    </Dialog>
  );
};
