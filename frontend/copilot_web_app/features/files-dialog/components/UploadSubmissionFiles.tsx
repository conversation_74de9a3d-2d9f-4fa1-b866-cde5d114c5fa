import { UploadFilesDialog } from "@ui-patterns/upload-files-dialog";
import React, { useEffect, useMemo, useState } from "react";
import { FilePondFile } from "filepond";
import {
  <PERSON>ert,
  AlertTitle,
  Box,
  Button,
  Checkbox,
  Divider,
  FormControlLabel,
  Stack,
  Typography,
  useTheme,
} from "@mui/material";
import useFiles from "@legacy/hooks/useFiles";
import Report from "@legacy/models/Report";
import { useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { useSnackbar } from "notistack";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { ModelFileFileTypeEnum } from "@legacy/api_clients/copilot_api_client";
import { FileTypesDropdown } from "@features/files/components/FileTypesDropdown";
import {
  trackDataOnboardingReplaceSovUsed,
  trackSubmissionEdited,
} from "@utils/amplitude";
import { queryKeyFactory } from "@queries/queryKey";
import FileModel from "@legacy/models/File";
import UploadResult from "@legacy/models/UploadResult";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { ReplaceFilesModal } from "./ReplaceFilesModal";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { EditFileTag } from "@features/blades/FileInfoBlade";
import uniq from "lodash/uniq";
import without from "lodash/without";
import { useIsPdsPage } from "@utils/useIsPdsPage";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import CopilotFile from "@legacy/models/File";

const FileRow = ({
  report,
  file,
  divider = false,
  isInternal,
  setIsInternal,
  copilotFileType,
  setCopilotFileType,
  clientFileType,
  setClientFileType,
  defaultFileType,
  onReplaceFiles,
  filesToReplace,
  tags,
  setTag,
  replacingSov,
}: {
  report: Report;
  file: FilePondFile;
  divider?: boolean;
  isInternal: boolean;
  setIsInternal: (value: boolean) => void;
  copilotFileType: ModelFileFileTypeEnum;
  setCopilotFileType: (value: ModelFileFileTypeEnum) => void;
  clientFileType?: string;
  setClientFileType: (value: string) => void;
  defaultFileType?: ModelFileFileTypeEnum;
  tags: Record<string, string>;
  setTag: (tag: string, value: string) => void;
  onReplaceFiles: (files: string[]) => void;
  filesToReplace: FileModel[];
  replacingSov: boolean;
}) => {
  const user = useCurrentUser();
  const isSupport = user.applicable_settings.is_support ?? undefined;
  const { internalUseFeatures } = useFeatureFlags();
  const [showModal, setShowModal] = useState(false);
  const theme = useTheme();
  const [isDefiningProps, setIsDefiningProps] = useState(false);
  const { isCSManager } = useAccessPermissions();
  const isAdmiral = useOrganizationId() === 49;

  const possibleTags = useMemo(() => {
    return (
      user.applicable_settings.client_file_types_config?.tags?.special_tags?.filter(
        (x) => x.tag_display
      ) ?? []
    );
  }, [user]);
  if (!tags["source_type"] && possibleTags.length > 0) {
    setTag("source_type", "Copilot");
  }

  const hasClientTypes =
    !!user.applicable_settings.client_file_types_config?.client_file_types;
  const isPdsPage = useIsPdsPage();

  const clientFileTypes = useMemo(() => {
    return uniq(
      Object.values(
        user.applicable_settings.client_file_types_config?.client_file_types ??
          user.applicable_settings.client_file_types_config
            ?.file_types_to_client_file_types_map ??
          {}
      )
    );
  }, [user]);

  const filesTypeField = (
    <Box whiteSpace={"nowrap"}>
      {internalUseFeatures && (
        <Box display={"grid"} gridTemplateColumns={"auto 1fr"}>
          <FormControlLabel
            control={
              <Checkbox
                checked={isInternal ?? false}
                onClick={() => setIsInternal(!isInternal)}
              />
            }
            disabled={isSupport && !isCSManager}
            label="Is internal"
          />

          <Box display="inline-flex" mr={1} width={"100%"}>
            <FileTypesDropdown
              fileType={defaultFileType ?? copilotFileType}
              onChange={(newFileType) => {
                setCopilotFileType(newFileType);
              }}
              id={file.filename}
              label="Copilot Type"
              {...(defaultFileType ? { fileTypes: [defaultFileType!] } : {})}
            />
          </Box>
        </Box>
      )}

      {hasClientTypes && !isPdsPage && (
        <Box display="inline-flex">
          <FileTypesDropdown
            fileType={clientFileType}
            fileTypes={clientFileTypes}
            onChange={(newFileType) => {
              setClientFileType(newFileType);
            }}
            id={file.filename}
            label="Document Type"
          />
        </Box>
      )}
    </Box>
  );

  const showAdditionalHiddenField =
    isAdmiral && (internalUseFeatures || (hasClientTypes && !isPdsPage));
  const additionalHiddenField = isAdmiral && filesTypeField;
  const filteredPossibleTags = possibleTags.filter(
    (x) => !isAdmiral || x.tag_key !== "file_description"
  );
  const hasAdditionalProps =
    filteredPossibleTags.length > 0 || showAdditionalHiddenField;

  const renameField = (
    <Box whiteSpace={"nowrap"}>
      <EditFileTag
        key={"file_description"}
        title={"Rename Document"}
        value={tags["file_description"] ?? ""}
        onChange={(val) => setTag("file_description", val ?? "")}
      />
    </Box>
  );

  const topField = isAdmiral ? renameField : filesTypeField;

  const name = (
    <Box sx={{ alignSelf: "flex-start" }}>
      <Typography
        color="text.secondary"
        sx={{
          flex: 1,
          mr: 2,
          wordBreak: "break-word",
          overflowWrap: "break-word",
        }}
      >
        {file.filename}
      </Typography>
    </Box>
  );

  return (
    <Box>
      {replacingSov ? (
        name
      ) : (
        <Box
          key={file.filename}
          display="grid"
          gridTemplateColumns={"40% 57%"}
          gap={"3%"}
          px={2}
          py={1}
          pb={divider ? 4 : 1}
        >
          {name}

          <Box display={"flex"} flexDirection={"column"} sx={{ width: "100%" }}>
            {topField}

            {hasAdditionalProps && (
              <Box mt={1}>
                <Button
                  variant="text"
                  size="small"
                  onClick={() => setIsDefiningProps(!isDefiningProps)}
                >
                  Define additional properties{" "}
                  {isDefiningProps ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </Button>
                {isDefiningProps && (
                  <Box
                    display="flex"
                    flexDirection="column"
                    rowGap={1}
                    sx={{ pt: 1 }}
                  >
                    {additionalHiddenField}
                    {filteredPossibleTags.map((t) => (
                      <EditFileTag
                        key={t.tag_key}
                        title={t.tag_display!}
                        value={tags[t.tag_key] ?? ""}
                        onChange={(val) => setTag(t.tag_key, val ?? "")}
                        options={t.restricted_values}
                      />
                    ))}
                  </Box>
                )}
              </Box>
            )}

            {internalUseFeatures && isInternal && (
              <Box mt={1}>
                <Typography
                  variant="body2"
                  sx={{ cursor: "pointer", color: theme.palette.error.main }}
                  onClick={() => setShowModal(true)}
                >
                  {filesToReplace.length === 0
                    ? "Click here to replace files (required)"
                    : `Files to replace: ${filesToReplace
                        .map((f) => f.name)
                        .join(", ")}`}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      )}
      {divider && <Divider />}

      {showModal && (
        <ReplaceFilesModal
          report={report}
          onSubmit={onReplaceFiles}
          onClose={() => setShowModal(false)}
          fileName={file.filename}
          selectedFiles={filesToReplace}
        />
      )}
    </Box>
  );
};

const isFleetRelatedFile = (fileType?: string) => {
  if (!fileType) return false;
  return ["vehicles", "drivers"].includes(fileType.toLowerCase());
};

type UploadSubmissionFilesProps = {
  onClose: (
    results?: (
      | UploadResult
      | FileModel
      | { file: { id: string }; error: string }
    )[]
  ) => void;
  report: Report;
  defaultFileType?: ModelFileFileTypeEnum;
  maxNumberOfFiles?: number;
  replaceSov?: CopilotFile;
};

export const UploadSubmissionFiles = ({
  onClose,
  report,
  defaultFileType,
  maxNumberOfFiles,
  replaceSov,
}: UploadSubmissionFilesProps) => {
  const [files, setFiles] = useState<FilePondFile[]>([]);
  const [unvalidatedFiles, setUnvalidatedFiles] = useState<FilePondFile[]>([]);
  const [copilotFileTypes, setCopilotFileTypes] = useState<
    Record<string, string>
  >({});
  const [clientFileTypes, setClientFileTypes] = useState<
    Record<string, string>
  >({});
  const [fileTags, setFileTags] = useState<
    Record<string, Record<string, string>>
  >({});
  const [filesIsInternal, setFilesIsInternal] = useState<
    Record<string, boolean>
  >({});
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string>();
  const [filesToReplace, setFilesToReplace] = useState<
    Record<string, string[]>
  >({});
  const { enqueueSnackbar } = useSnackbar();

  const { allowLoadingVehiclesFromPdf } = useFeatureFlags();
  const queryClient = useQueryClient();
  const user = useCurrentUser();
  const isSupport = user.applicable_settings.is_support;

  const hasClientFileTypes =
    !!user.applicable_settings.client_file_types_config?.client_file_types;

  const isPdsPage = useIsPdsPage();

  useEffect(() => {
    if (!hasClientFileTypes) return;

    const missingFiles = without(
      files.map((x) => x.filename),
      ...Object.keys(clientFileTypes)
    );

    if (!missingFiles.length) return;

    const unknownFileType =
      user.applicable_settings.client_file_types_config
        ?.file_types_to_client_file_types_map?.["UNKNOWN"];

    if (!unknownFileType) return;

    setClientFileTypes({
      ...clientFileTypes,
      ...missingFiles.reduce(
        (acc, curr) => ({ ...acc, [curr]: unknownFileType }),
        {}
      ),
    });
  }, [clientFileTypes, user, files, hasClientFileTypes]);

  // Current method of uploading files is left intact from the old implementation
  // overall our way of uploading files is not working correctly
  // and should be fixed in ENG-12207

  const allFiles = report.getSubmission().files;

  const { uploadFile } = useFiles(report.getSubmission().id);
  const onUploadFiles = async () => {
    const countOfFleetFiles = files.filter((file) =>
      isFleetRelatedFile(copilotFileTypes[file.filename])
    ).length;
    if (countOfFleetFiles > 1) {
      setError(
        "You can only upload one file containing vehicles or drivers at a time."
      );
      return;
    }

    setUploading(true);

    try {
      const results = await Promise.all(
        files.map((file) => {
          const replaceFiles = filesToReplace[file.filename] ?? [];
          const fileType =
            defaultFileType ?? copilotFileTypes[file.filename] ?? "Unknown";
          if (replaceFiles.length > 0)
            trackDataOnboardingReplaceSovUsed(report, {
              fileName: file?.filename ?? "",
              fileType,
            });

          const replacement = (() => {
            if (replaceSov) return [replaceSov.id];

            return replaceFiles.length > 0 ? replaceFiles : undefined;
          })();

          return uploadFile({
            file: file.file as File,
            fileType: replaceSov
              ? "SOV"
              : defaultFileType ?? copilotFileTypes[file.filename] ?? "Unknown",
            clientFileType: isPdsPage
              ? undefined
              : clientFileTypes[file.filename],
            tags: fileTags[file.filename],
            replaceFiles: replacement,
            isInternal: filesIsInternal[file.filename],
          });
        })
      );
      if (
        results.length === 1 &&
        "grouped_first_party_fields" in results[0] &&
        isFleetRelatedFile(results[0].file?.file_type)
      ) {
        const result = results[0];
        const isPdf =
          (result.file?.name?.toLowerCase().split(".").pop() ??
            "no-extension") === "pdf";
        const isFileTypeVehicles =
          results[0].file?.file_type?.toLowerCase() === "vehicles";
        const fieldGroups = result.grouped_first_party_fields?.fields_groups;

        const isProbablyDriversFile =
          fieldGroups?.some(
            (x) =>
              x.fields.some(
                (f) => f?.fact_subtype_id === "DRIVER_LICENSE_NUMBER"
              ) &&
              !x.fields.some(
                (f) => f?.fact_subtype_id === "VEHICLE_INFORMATION_NUMBER"
              )
          ) ?? false;

        if (isFileTypeVehicles && isProbablyDriversFile) {
          setError("The file seems to contain drivers, not vehicles.");
          setUploading(false);
        } else if (
          (isPdf && isFileTypeVehicles && !allowLoadingVehiclesFromPdf) ||
          !fieldGroups?.flatMap((x) => x.fields).length
        ) {
          setError("No fleet data has been parsed from the file.");
          setUploading(false);
        }
      } else {
        onClose(results);
      }

      trackSubmissionEdited(report, {
        item: "new file",
      });
      queryClient.invalidateQueries(queryKeyFactory.report(report.id));
    } catch (error) {
      if (axios.isAxiosError(error) && error?.response?.status === 409) {
        enqueueSnackbar("This file is already attached to the submission", {
          variant: "error",
        });
      } else {
        enqueueSnackbar("Uploading file failed", { variant: "error" });
      }
      setUploading(false);
      onClose();
    }
  };

  const onUnvalidatedUpdateFiles = (filesList: FilePondFile[]) => {
    setUnvalidatedFiles(filesList);
  };

  const onUpdateFiles = (filesList: FilePondFile[]) => {
    setFiles(filesList);
    setError(undefined);
  };

  useEffect(() => {
    if (isSupport) {
      const internalFiles = filesIsInternal;
      unvalidatedFiles.forEach((file) => {
        if (internalFiles[file.filename] === undefined)
          internalFiles[file.filename] = true;
      });
      setFilesIsInternal(internalFiles);
    }
  }, [unvalidatedFiles, filesIsInternal, isSupport]);

  const disableAction = Object.keys(filesIsInternal).some(
    (fileName) =>
      !!filesIsInternal[fileName] &&
      (filesToReplace[fileName] ?? []).length === 0
  );

  return (
    <UploadFilesDialog
      title={
        replaceSov
          ? `Replace SOV File ${replaceSov.display_name}`
          : "Add Documents"
      }
      submitButtonText="Save"
      onSubmit={onUploadFiles}
      onClose={onClose}
      onUnvalidatedUpdateFiles={onUnvalidatedUpdateFiles}
      onUpdateFiles={onUpdateFiles}
      showInProgress={uploading}
      maxNumberOfFiles={replaceSov ? 1 : maxNumberOfFiles}
      disablePrimary={disableAction}
    >
      <Divider />
      <Stack spacing={1} p={2}>
        {files.map((f, i) => (
          <FileRow
            key={f.filename}
            file={f}
            divider={i != files.length - 1}
            replacingSov={!!replaceSov}
            report={report}
            isInternal={filesIsInternal[f.filename]}
            setIsInternal={(value: boolean) =>
              setFilesIsInternal((current) => ({
                ...current,
                [f.filename]: value,
              }))
            }
            copilotFileType={
              defaultFileType ??
              (copilotFileTypes[f.filename] as ModelFileFileTypeEnum)
            }
            setCopilotFileType={(newFileType) => {
              setCopilotFileTypes((current) => ({
                ...current,
                [f.filename]: newFileType,
              }));
            }}
            clientFileType={clientFileTypes[f.filename]}
            setClientFileType={(newFileType) => {
              setClientFileTypes((current) => ({
                ...current,
                [f.filename]: newFileType,
              }));
            }}
            onReplaceFiles={(selectedFiles: string[]) => {
              setFilesToReplace({
                ...filesToReplace,
                [f.filename]: selectedFiles,
              });
            }}
            filesToReplace={(filesToReplace[f.filename] ?? [])
              .map((fileId) => allFiles.find((file) => file.id === fileId))
              .filter((file): file is FileModel => Boolean(file))}
            tags={fileTags[f.filename] ?? {}}
            setTag={(tag, value) => {
              setFileTags((current) => {
                const res = {
                  ...current,
                  [f.filename]: {
                    ...current[f.filename],
                    [tag]: value,
                  },
                };

                if (!res[f.filename][tag]) {
                  delete res[f.filename][tag];
                }

                return res;
              });
            }}
          />
        ))}
      </Stack>
      {error && (
        <Box m={2}>
          <Typography color="error" textAlign="right">
            {error}
          </Typography>
        </Box>
      )}

      {replaceSov && (
        <Box m={2}>
          <Alert severity="info">
            <AlertTitle>
              Replacing an SOV document triggers a report update
            </AlertTitle>
            This may take a few minutes. You’ll be notified once the changes are
            applied.
          </Alert>
        </Box>
      )}
    </UploadFilesDialog>
  );
};
