import { TableView } from "@features/pds-entity-table/TableView";
import { NewValidationHeader } from "@features/pds-entity-table/NewValidationHeader";
import { Box } from "@mui/material";
import { AddFieldButton } from "@features/pds-entity-table/AddFieldButton";
import { AddEntityButton } from "@features/pds-entity-table/AddEntityButton";
import { DragAndDropContext } from "@features/pds-entity-table/DragAndDropContext";
import React from "react";
import { PdsEntityTableConfig } from "@features/pds-entity-table/types";
import { PdsEntityTableConfigContextProvider } from "@features/pds-entity-table/context/PdsEntityTableConfigContext";
import { SuggestionHeader } from "@features/pds-entity-table/SuggestionHeader";
import { useOnboardingFormLoadingContext } from "@features/pds-data-model/form/FormLoadingContext";

type NewEntityTableProps = PdsEntityTableConfig & {
  id?: string;
};

export const NewEntityTable = React.memo(
  ({ id, ...config }: NewEntityTableProps) => {
    const pdsEntityTableConfig: PdsEntityTableConfig = {
      //default config
      allowAddEntity: true,
      allowDragAndDrop: true,
      allowAddField: true,
      pinColumns: true,
      sourcePerInput: true,
      allowAddingMoreThanOneValue: true,
      canRemoveBusinessEntity: true,
      tableHeight: 530,
      allowDeleteColumn: true,
      condensed: false,
      ...config,
    };
    const { doSimplifiedTimeSeriesLoading } = useOnboardingFormLoadingContext();
    return (
        <PdsEntityTableConfigContextProvider {...pdsEntityTableConfig}>
          <Box
            display="flex"
            flexDirection="row"
            justifyContent="space-between"
            id={id}
          >
            <Box display="flex" flexDirection="row">
              <NewValidationHeader
                mode={
                  doSimplifiedTimeSeriesLoading
                    ? "forCurrentStep"
                    : "noTimeSeries-forCurrentStep"
                }
              />
              {!pdsEntityTableConfig.disableSuggestions && <SuggestionHeader />}
            </Box>
            <Box
              display="flex"
              flexDirection="row"
              gap={1}
              alignItems="center"
              mb={1}
            >
              {pdsEntityTableConfig.allowAddEntity && <AddEntityButton />}
              {pdsEntityTableConfig.allowDragAndDrop && <AddFieldButton />}
            </Box>
          </Box>
          {pdsEntityTableConfig.allowDragAndDrop ? (
            <DragAndDropContext>
              <TableView />
            </DragAndDropContext>
          ) : (
            <TableView />
          )}
        </PdsEntityTableConfigContextProvider>
    );
  }
);
