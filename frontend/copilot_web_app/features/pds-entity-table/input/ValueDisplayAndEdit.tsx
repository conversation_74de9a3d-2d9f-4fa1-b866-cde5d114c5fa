import { Box, ClickAwayListener, Tooltip, useTheme } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";

import { MonitorValueChanges } from "@features/pds-entity-table/input/MonitorValueChanges";
import { DisplayValue } from "@features/pds-entity-table/input/display";
import { SourceDisplay } from "@features/pds-entity-table/input/display/SourceDisplay";
import { ValueEdit } from "@features/pds-entity-table/input/edit";
import { SourceEdit } from "@features/pds-entity-table/input/edit/SourceEdit";
import {
  useOnboardingDataContext,
  ValueHighlightType,
} from "@features/pds-data-model/context/OnboardingDataContextData";
import { useValueIndexContext } from "@features/pds-entity-table/ValueIndexContext";
import { useStore } from "zustand";
import {
  getFieldValidationIssues,
  getTriggerValueType,
} from "@features/pds-data-model/context/selectors";
import ErrorIcon from "@mui/icons-material/Error";
import WarningSharp from "@mui/icons-material/WarningSharp";
import { usePdsEntityTableConfigContext } from "@features/pds-entity-table/context/PdsEntityTableConfigContext";
import { useOnboardingFormLoadingContext } from "@features/pds-data-model/form/FormLoadingContext";
import { useNewOnboardingDataForm } from "@features/pds-data-model/form";
import { WarningTooltipMessage } from "@features/pds-entity-table/input/WarningTooltipMessage";
import { isInViewport } from "@utils/IsInViewport";
import LockIcon from "@mui/icons-material/Lock";
import { RemoveValueAction } from "@features/entity-mapping-v2/components/VerticalTable/RemoveValueAction";
import { useAccessPermissions } from "@features/product-driven-support/utils";

type ValueDisplayAndEditProps = {
  openInEdit?: boolean;
  onEditOpened?: () => void;
  extraIndicator?: React.ReactNode;
};

export const ValueDisplayAndEdit = React.memo(
  ({
    openInEdit = false,
    onEditOpened,
    extraIndicator,
  }: ValueDisplayAndEditProps) => {
    const theme = useTheme();
    const wrapperRef = useRef<HTMLDivElement>(null);
    const valueIndexContext = useValueIndexContext();
    const [highlightType, setHighlightType] =
      useState<ValueHighlightType | null>(null);
    const { entityId, fieldId, valueId } = valueIndexContext;
    const fieldName = `entities.${entityId}.fieldValues.${fieldId}.values.${valueId}`;
    const [isEditing, setIsEditing] = useState(openInEdit);
    const autoFocusOn = useRef<"input" | "source" | undefined>(undefined);
    const onboardingDataStore = useOnboardingDataContext();
    const validationIssue = useStore(
      onboardingDataStore,
      getFieldValidationIssues(fieldName)
    );
    const triggerType = useStore(
      onboardingDataStore,
      getTriggerValueType(valueIndexContext)
    );
    const setValidationIssueHighlight =
      onboardingDataStore.use.setValueHighlight();
    const { sourcePerInput, autoFileSourceUpdate, allowForValueRemoval } =
      usePdsEntityTableConfigContext();
    const { nonEditableNameAndAddress } = useOnboardingFormLoadingContext();
    const { getValues } = useNewOnboardingDataForm();
    const { isCSManager } = useAccessPermissions();
    const formData = getValues();
    const isBusinessEntityType =
      formData.entities[entityId].entity.type === "Business";
    const field = formData.fields[fieldId];
    const isNameOrAddressField =
      field.origin === "entityInformation" &&
      ["name", "address", "business mailing address"].includes(
        field.name?.toLowerCase() ?? ""
      );

    let isEditable = true;
    const isReadOnly =
      formData.entities[entityId]?.fieldValues[fieldId]?.values[valueId]
        ?.isReadOnly;

    if (
      nonEditableNameAndAddress &&
      isBusinessEntityType &&
      isNameOrAddressField
    ) {
      isEditable = false;
    } else {
      if (isReadOnly) {
        isEditable = false;
      }
    }

    if (isCSManager) {
      isEditable = true;
    }

    useEffect(() => {
      if (triggerType) {
        setHighlightType(triggerType);
        setTimeout(() => {
          setHighlightType(null);
        }, 2500);

        if (triggerType === "focus") {
          const scrollIntoView = () =>
            wrapperRef.current?.scrollIntoView({
              behavior: "smooth",
              block: "center",
              inline: "center",
            });
          scrollIntoView();
          setInterval(() => {
            const isStillValid = !!getTriggerValueType(valueIndexContext)(
              onboardingDataStore.getState()
            );
            if (isStillValid && !isInViewport(wrapperRef.current)) {
              scrollIntoView();
            } else {
              setValidationIssueHighlight(null);
            }
          }, 250);
        } else {
          setValidationIssueHighlight(null);
        }
      }
    }, [
      onboardingDataStore,
      setValidationIssueHighlight,
      triggerType,
      valueIndexContext,
    ]);

    useEffect(() => {
      if (!isEditing) {
        autoFocusOn.current = undefined;
      }
    }, [isEditing]);

    const hasErrors = (validationIssue?.errors ?? []).length > 0;
    const hasWarnings = (validationIssue?.warnings ?? []).length > 0;

    return (
      <Box
        ref={wrapperRef}
        onClick={
          !isEditing && isEditable
            ? () => {
                autoFocusOn.current = "input";
                setIsEditing(true);
                onEditOpened?.();
              }
            : undefined
        }
        sx={{
          width: "100%",
        }}
        className={
          highlightType
            ? `${
                highlightType === "validation"
                  ? "highlightErrorAnimation"
                  : "highlightSuggestionAnimation"
              }`
            : ""
        }
      >
        <Box display="flex" flexDirection="row">
          <Box
            display="flex"
            flexDirection="column"
            gap={1}
            sx={{
              "& > *": { mr: 0.5 },
            }}
          >
            {extraIndicator}
            {isReadOnly && (
              <Tooltip title="This value was pulled from the submission files, you do not need to verify it">
                <LockIcon sx={{ color: theme.palette.grey[600] }} />
              </Tooltip>
            )}
            {hasErrors && (
              <Tooltip
                title={(validationIssue?.errors ?? [])
                  .map((e) => e.message)
                  .join(", ")}
              >
                <ErrorIcon color="error" />
              </Tooltip>
            )}
            {hasWarnings && (
              <Tooltip
                title={
                  <WarningTooltipMessage
                    messages={validationIssue?.warnings ?? []}
                  />
                }
              >
                <WarningSharp color="warning" />
              </Tooltip>
            )}
            {allowForValueRemoval && (
              <RemoveValueAction
                entityId={entityId}
                fieldId={fieldId}
                feValueId={valueId}
              />
            )}
          </Box>
          {!isEditing && (
            <Box
              display="flex"
              flexDirection="column"
              flexGrow={1}
              gap={1}
              overflow="hidden"
            >
              <DisplayValue isReadOnly={!isEditable} />
              {sourcePerInput && (
                <Box
                  onClick={(e) => {
                    e.stopPropagation();
                    autoFocusOn.current = "source";
                    if (isEditable) {
                      setIsEditing(true);
                    }
                  }}
                >
                  <SourceDisplay isEditable={isEditable} />
                </Box>
              )}
            </Box>
          )}
          {isEditing && (
            <ClickAwayListener onClickAway={() => setIsEditing(false)}>
              <Box display="flex" flexDirection="column" flexGrow={1} gap={1}>
                <ValueEdit autoFocus={autoFocusOn.current === "input"} />
                {sourcePerInput && (
                  <SourceEdit autoFocus={autoFocusOn.current === "source"} />
                )}
                <MonitorValueChanges
                  autoUpdateSource={
                    (autoFileSourceUpdate || sourcePerInput) ?? false
                  }
                />
              </Box>
            </ClickAwayListener>
          )}
        </Box>
      </Box>
    );
  }
);
