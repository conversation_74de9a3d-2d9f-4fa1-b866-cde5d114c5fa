import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import { PremiseItem } from "@features/modes-display/premises-picker/types";
import CircleIcon from "@mui/icons-material/Circle";
import isNil from "lodash/isNil";
import { useStructuresContextData } from "@features/modes-display/entity-picker/context/structuresContext";
import { usePremisesPickerStore } from "@features/modes-display/premises-picker/context/PremisesPickerData";
import intersection from "lodash/intersection";

const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 2,
  maximumSignificantDigits: 2,
  notation: "compact",
});

const numberFormat = new Intl.NumberFormat("en-US", {
  style: "decimal",
});

const formatYearBuilt = (
  yearBuilt: { from: number; to: number } | null | undefined
): string => {
  if (!yearBuilt) return "Unknown";
  if (yearBuilt.from === yearBuilt.to) return `${yearBuilt.from}`;
  return `${yearBuilt.from}–${yearBuilt.to}`;
};

export type PremisesItemProps = PremiseItem & {
  variant?: "default" | "compact";
  onClick?: () => void;
};

export const PremisesItem = ({
  item,
  structuresCount,
  tiv,
  yearBuilt,
  buildingArea,
  variant = "default",
  risks,
  onClick,
}: PremisesItemProps) => {
  const theme = useTheme();
  const premises = item.entity_data?.getPremises();

  const isFni = item.named_insured === "FIRST_NAMED_INSURED";

  const structureIds = useStructuresContextData((state) =>
    state.premiseIdToStructureIds.get(premises?.id ?? "")
  );
  const activeFilterEvent = usePremisesPickerStore().use.activeFilterEvent();
  const hasActiveFilterStructures = activeFilterEvent ? activeFilterEvent.parentType === "STRUCTURE" : false;
  const activeFilterIds =
    activeFilterEvent?.parentType === "STRUCTURE"
      ? activeFilterEvent?.parentIds
      : [];
  const structureIdsArray = structureIds ? [...structureIds] : [];
  const structuresCountNew =
    structureIdsArray.length === 0 || activeFilterIds.length === 0
      ? structuresCount
      : intersection(structureIdsArray, activeFilterIds).length;

  const renderExtraInfo = (
    title: string,
    value: string | number | null | undefined,
    formatter: (value: number) => string
  ) => {
    return (
      <Box
        display="flex"
        flexDirection="row"
        gap={1}
        sx={{
          "&:not(:last-child)": {
            borderRight: `1px solid ${theme.palette.neutrals.border}`,
            pr: 1,
          },
        }}
      >
        <Typography fontWeight={600} sx={{ whiteSpace: "nowrap" }}>
          {title}
        </Typography>
        <Typography
          sx={{
            whiteSpace: "nowrap",
            color: theme.palette.neutrals.text.subdued,
          }}
        >
          {!isNil(value) &&
            (typeof value === "string" ? value : formatter(value))}
          {isNil(value) && "Unknown"}
        </Typography>
      </Box>
    );
  };

  if (structuresCountNew === 0 && hasActiveFilterStructures) {
    return null;
  }

  return (
    <Box
      border={
        variant === "compact"
          ? undefined
          : `1px solid ${theme.palette.neutrals.border}`
      }
      borderRadius={0.5}
    >
      {isFni && (
        <Box
          sx={{ backgroundColor: theme.palette.dataVisualization.blue.series1 }}
        >
          <Typography
            color={theme.palette.brand.text.default}
            fontWeight={500}
            py={0.5}
            px={2}
          >
            First Named Insured
          </Typography>
        </Box>
      )}
      <Box
        width="100%"
        display="flex"
        flexDirection="column"
        py={1}
        px={2}
        gap={1}
        sx={{
          transition: "background-color 0.3s ease",
          maxWidth: variant === "compact" ? 400 : undefined,
          "&:hover": {
            backgroundColor: theme.palette.neutral["100"],
            cursor: "pointer",
          },
          "&:active": {
            backgroundColor: theme.palette.neutral["300"],
          },
        }}
        onClick={onClick}
      >
        <Box
          display="flex"
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          gap={2}
        >
          <Typography
            fontWeight={700}
            sx={{
              display: "-webkit-box",
              "-webkit-line-clamp": "2",
              "-webkit-box-orient": "vertical",
              overflow: "hidden",
            }}
          >
            {item.getDisplayName()}
          </Typography>
          <Box
            sx={{
              backgroundColor: theme.palette.dataVisualization.blue.series1,
              color: theme.palette.brand.text.default,
              textTransform: "capitalize",
              borderRadius: 2,
              fontWeight: 600,
              lineHeight: 1.5,
              letterSpacing: 0.7,
              fontSize: 14,
              py: 0.5,
              px: 1,
              whiteSpace: "nowrap",
            }}
          >
            {structuresCountNew} STRUCT.
          </Box>
        </Box>
        <Box
          display="flex"
          flexDirection={variant === "compact" ? "column" : "row"}
          justifyContent="space-between"
          gap={2}
        >
          <Typography minWidth="33%">{item.getDisplayAddress()}</Typography>
          <Box
            display="flex"
            flex="1 1 0"
            flexDirection="row"
            gap={1}
            flexWrap="wrap"
            minWidth="50%"
            justifyContent={variant === "compact" ? undefined : "end"}
          >
            {renderExtraInfo("Built", formatYearBuilt(yearBuilt), String)}
            {renderExtraInfo("TIV", tiv, currencyFormatter.format)}

            {renderExtraInfo(
              "Bldg. Area",
              buildingArea,
              (val) => numberFormat.format(val) + " sqft"
            )}
          </Box>
        </Box>
        <Box display="flex" flexDirection="row" flexWrap="wrap" gap={1}>
          {risks.map((risk) => (
            <Box
              key={risk.label}
              display="flex"
              flexDirection="row"
              gap={0.5}
              alignItems="center"
              mr={1}
            >
              <CircleIcon
                fontSize="small"
                color={
                  risk.severity === "very high" ? "veryHighRisk" : "highRisk"
                }
              />
              <Typography fontWeight={600}>{risk.label}</Typography>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};
