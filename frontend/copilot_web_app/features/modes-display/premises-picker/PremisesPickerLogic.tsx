import {
  PremiseItem,
  PremisesShortRisk,
  PremisesSortByData,
} from "@features/modes-display/premises-picker/types";
import { useReport } from "@utils/useReport";
import { useMetrics } from "@utils/metrics";
import { GradeSummaryV2, MeanAndSumSummaryV2 } from "@legacy/models/MetricV2";
import { useStructuresContextData } from "@features/modes-display/entity-picker/context/structuresContext";
import { useMemoWithHashResult } from "@utils/useMemoWithHashResult";
import { useEntityPickerBusinesses } from "@features/modes-display/entity-picker/hooks/useEntityPickerBusinesses";
import { usePremisesPickerStore } from "@features/modes-display/premises-picker/context/PremisesPickerData";
import { useEffect, useRef } from "react";
import { useFactSubtypes } from "@legacy/hooks/useFactSubtypes";
import { FactSubtypesExpandEnum } from "@legacy/models/types/enums";
import { FilterEvent, useEventBus } from "@features/events";
import isNil from "lodash/isNil";
import { arraysShallowEqual } from "@utils/helpers";
import { useSyncSectionQueryParam } from "@features/modes-display/entity-picker/query";
import { useParentElementId } from "@features/modes-core/context/ParentIdContext";

export const PremisesPickerLogic = () => {
  const report = useReport();
  const { data: locationRiskFactSubtypes } = useFactSubtypes(
    report.submission.id,
    [FactSubtypesExpandEnum.SUMMARY_CONFIGS],
    false,
    ["LOCATION_RISKS"]
  );
  const riskSummaryConfigIds =
    locationRiskFactSubtypes
      ?.filter((fs) => fs.fact_type_id === "LETTER_GRADE")
      .map((s) => s.summary_configs?.[0]?.id)
      .filter((s): s is string => Boolean(s)) ?? [];

  const { metrics } = useMetrics(report.id, {
    id: report.id,
    getMetricsV2Request: {
      summary_config_ids: [
        "TIV_SUMMARY_CONFIG",
        "BUILDING_SIZE_SUMMARY_CONFIG",
        "YEAR_BUILT_SUMMARY_CONFIG",
        ...riskSummaryConfigIds,
      ],
    },
  });

  const tivMetric = metrics.find(
    (m): m is MeanAndSumSummaryV2 => m.summary_config_id == "TIV_SUMMARY_CONFIG"
  );
  const buildingAreaMetric = metrics.find(
    (m): m is MeanAndSumSummaryV2 =>
      m.summary_config_id == "BUILDING_SIZE_SUMMARY_CONFIG"
  );
  const yearBuiltMetric = metrics.find(
    (m): m is MeanAndSumSummaryV2 =>
      m.summary_config_id == "YEAR_BUILT_SUMMARY_CONFIG"
  );
  const businessIdToStructureIds = useStructuresContextData(
    (state) => state.businessIdToStructureIds
  );

  const businesses = useEntityPickerBusinesses(report);
  const items: PremiseItem[] = useMemoWithHashResult(() => {
    return businesses.map((item) => {
      const premisesId = item.getPremisesId();
      const structuresCount =
        businessIdToStructureIds.get(item.business_id ?? "")?.size || 1;

      const risks: PremisesShortRisk[] = metrics.flatMap((m) => {
        if (m instanceof GradeSummaryV2) {
          const grade = m.getValueForParentId(premisesId, ["D", "F"]);
          if (grade) {
            const name = m.name ?? "";
            return [
              {
                label:
                  name +
                  (name.toLowerCase().indexOf("risk") >= 0 ? "" : " Risk"),
                severity: grade === "D" ? "high" : "very high",
              },
            ];
          }
        }
        return [];
      });

      const premisesYearBuilt =
        yearBuiltMetric?.getValueForParentId(premisesId);
      const structuresYearBuilt = Array.from(
        businessIdToStructureIds.get(item.business_id ?? "") ?? []
      ).map((structureId) => {
        const structuresMetric =
          yearBuiltMetric?.dependentMetric as MeanAndSumSummaryV2 | null;
        return structuresMetric?.getValueForParentId(structureId);
      });

      const allYearBuilt = [premisesYearBuilt, ...structuresYearBuilt].filter(
        (num): num is number => !isNil(num)
      );

      return {
        item,
        structuresCount,
        tiv: tivMetric?.getValueForParentId(premisesId),
        buildingArea: buildingAreaMetric?.getValueForParentId(premisesId),
        yearBuilt:
          allYearBuilt.length === 0
            ? null
            : {
                from: Math.min(...allYearBuilt),
                to: Math.max(...allYearBuilt),
              },
        risks,
      } satisfies PremiseItem;
    });
  }, [
    buildingAreaMetric,
    businesses,
    metrics,
    businessIdToStructureIds,
    tivMetric,
    yearBuiltMetric,
  ]);

  const setActiveFilterEvent =
    usePremisesPickerStore().use.setActiveFilterEvent();
  const setSelectedItem = usePremisesPickerStore().use.setSelectedItem();
  useEventBus(
    (evt) => {
      setActiveFilterEvent(evt);
      setSelectedItem(null);
    },
    [setActiveFilterEvent, setSelectedItem],
    "ui/premises-viewer/filter-picked"
  );

  useEventBus(
    ({ anyId }) => {
      const item = items.find((i) => i.item.getAllIds().includes(anyId));
      if (item) {
        setSelectedItem(item);
      }
    },
    [items, setSelectedItem],
    "ui/premises-viewer/single-business-focus"
  );

  useEventBus(
    ({ parentId }) => {
      const item = items.find((i) =>
        i.item.getAllIds().includes(parentId ?? "")
      );
      if (item) {
        setSelectedItem(item);
      }
    },
    [items, setSelectedItem],
    "ui/premises-viewer/tab-focus"
  );

  const setAllItems = usePremisesPickerStore().use.setAllItems();
  const sectionId = useParentElementId();
  const selectedItem = usePremisesPickerStore().use.selectedItem();
  const businessQueryParam = useSyncSectionQueryParam(
    sectionId,
    "business",
    selectedItem?.item.id ?? undefined
  );
  const wasInitializedFromQueryParam = useRef<boolean>(false);
  useEffect(() => {
    setAllItems(items);
  }, [items, setAllItems]);

  useEffect(() => {
    if (wasInitializedFromQueryParam.current) {
      return;
    }
    const queryParamItem = items.find((i) =>
      i.item.getAllIds().includes(businessQueryParam ?? "")
    );
    if (queryParamItem) {
      wasInitializedFromQueryParam.current = true;
      setSelectedItem(queryParamItem);
    }
  }, [businessQueryParam, items, setSelectedItem]);

  const store = usePremisesPickerStore();
  useEffect(() => {
    const recalculateVisibleItems = (
      allItems: PremiseItem[],
      sortBy: PremisesSortByData | null,
      searchText: string,
      activeFilterEvent: FilterEvent | null
    ): PremiseItem[] => {
      const lowercaseSearch = searchText.toLowerCase();
      const postSearchTextItems = lowercaseSearch
        ? allItems.filter((item) => {
            const relevantStrings = [
              item.item.name,
              item.item.entity_data?.getPremises()?.formatted_address,
            ];
            return relevantStrings.some(
              (s) => s?.toLowerCase().includes(lowercaseSearch)
            );
          })
        : allItems;

      const activeFilterSet = new Set(activeFilterEvent?.parentIds ?? []);

      const postEventItems = activeFilterEvent
        ? postSearchTextItems.filter((item) => {
            return (
              item.item.getAllIds().some((id) => activeFilterSet.has(id)) ||
              (businessIdToStructureIds
                .get(item.item.business_id ?? "")
                ?.intersection(activeFilterSet).size ?? 0) > 0
            );
          })
        : postSearchTextItems;

      const filteredItems = postEventItems;

      if (!sortBy) {
        return filteredItems;
      }
      return [...filteredItems].sort((a, b) => {
        const aValue = a[sortBy.sortBy] ?? 0;
        const bValue = b[sortBy.sortBy] ?? 0;

        if (sortBy.order === "asc") {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    };

    return store.subscribe(
      (state) =>
        [
          state.allItems,
          state.sortBy,
          state.searchText,
          state.activeFilterEvent,
        ] as const,
      ([allItems, sortBy, searchText, activeFilterItem]) => {
        store.setState({
          visibleItemsInOrder: recalculateVisibleItems(
            allItems,
            sortBy,
            searchText,
            activeFilterItem
          ),
        });
      },
      {
        equalityFn: arraysShallowEqual as any,
        fireImmediately: true,
      }
    );
  }, [businessIdToStructureIds, store]);

  return null;
};
