import React from "react";
import { Box } from "@mui/material";
import { ELEMENT_ID } from "@features/modes/jsx/constants";
import { ModeSection } from "@features/modes-core/components/ModeSection";
import { AlwaysShowCard } from "@features/modes-core/components/AlwaysShowCard";
import {
  PremisesPickerContextProvider,
  usePremisesPickerStore,
} from "@features/modes-display/premises-picker/context/PremisesPickerData";
import { PremisesPickerLogic } from "@features/modes-display/premises-picker/PremisesPickerLogic";
import { CapabilityMarker } from "@features/capabilities/Capability";
import { PremisesListWithMap } from "@features/modes-display/premises-picker/components/PremisesListWithMap";
import { NewPremisesDetails } from "@features/modes-display/entity-picker/components/NewPremisesDetails";

const PremisesPickerWithContext = () => {
  const allItems = usePremisesPickerStore().use.allItems();
  const selectedItem = usePremisesPickerStore().use.selectedItem();
  return (
    <Box
      display="flex"
      flexDirection="column"
      flex="1 1 0"
      height="100%"
      sx={{
        "& > *": {
          flex: "1 1 0",
          display: "flex",
          flexDirection: "column",
          minHeight: 0,
          overflowY: "hidden",
        },
        "@container (max-width: 1000px)": {
          ".ppw.ppw": {
            flexDirection: "column",
            ".ppw-list": {
              flex: "6 6 0",
              py: 1,
              minHeight: 200,
            },
            ".ppw-map": {
              flex: "4 4 0",
              minHeight: 300,
            },
          },
        },
      }}
    >
      <ModeSection
        id={ELEMENT_ID.PREMISES_VIEWER}
        title="Premises"
        showEvenIfEmpty
      >
        <CapabilityMarker
          capabilities={[
            {
              type: "SUBMISSION_BUSINESS",
              anyIds: allItems.map((b) => b.item.id!),
              entityPickerId: ELEMENT_ID.PREMISES_VIEWER,
            },
          ]}
        />
        {!selectedItem && <PremisesListWithMap />}

        <NewPremisesDetails
          business={selectedItem?.item}
          mapEntities={selectedItem ? [selectedItem.item.entity_data!] : []}
          mapType="map"
        />

        <AlwaysShowCard />
        <PremisesPickerLogic />
      </ModeSection>
    </Box>
  );
};

export const PremisesPicker = () => {
  return (
    <PremisesPickerContextProvider>
      <PremisesPickerWithContext />
    </PremisesPickerContextProvider>
  );
};
