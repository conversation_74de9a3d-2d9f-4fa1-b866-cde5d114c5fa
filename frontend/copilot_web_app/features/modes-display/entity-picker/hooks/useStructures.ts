import { useMemo } from "react";
import RelationshipsObservation from "@legacy/models/observation/RelationshipsObservation";
import { StructuresContextType } from "@features/modes-display/entity-picker/context/structuresContext";
import { useStructureFacts } from "@features/reusable-queries/structures";
import { useReport } from "@utils/useReport";
import Report from "@legacy/models/Report";
import { mergeMapsWithSets } from "@features/modes-display/entity-picker/utils";
import { useMemoWithHashResult } from "@utils/useMemoWithHashResult";

const useStructuresFromFacts = ({
  report,
  enabled,
}: {
  report: Report | undefined;
  enabled: boolean;
}) => {
  const premisesIds = report?.getPremisesIdsForPropertyData();
  const businessIds = report?.getBusinessIdsForPropertyData();
  const {
    premisesFacts: { data: premisesFacts },
    businessFacts: { data: businessFacts },
  } = useStructureFacts(enabled, report);

  const premiseIdToBusinessIds = useMemoWithHashResult(() => {
    const result = new Map<string, string[]>();
    premisesIds?.forEach((premiseId) => {
      const businessIds = report?.getBusinessIdsForPremisesId(premiseId);
      if (businessIds) {
        result.set(premiseId, businessIds);
      }
    });
    return result;
  }, [premisesIds, report]);

  return useMemo(() => {
    const premisesIdToStructureIds = new Map<string, Set<string>>();
    const businessIdToStructureIds = new Map<string, Set<string>>();
    const premisesIdToPrometrixOnlyStructureIds = new Map<
      string,
      Set<string>
    >();
    const businessIdToPrometrixOnlyStructureIds = new Map<
      string,
      Set<string>
    >();

    premisesIds?.forEach((premisesId) => {
      premisesIdToStructureIds.set(premisesId, new Set());
      premisesIdToPrometrixOnlyStructureIds.set(premisesId, new Set());
    });
    businessIds?.forEach((businessId) => {
      businessIdToStructureIds.set(businessId, new Set());
      businessIdToPrometrixOnlyStructureIds.set(businessId, new Set());
    });

    premisesFacts?.forEach((fact) => {
      const premiseId = fact.parent_id;
      if (
        !(fact.observation instanceof RelationshipsObservation) ||
        !premiseId
      ) {
        return;
      }
      const observation: RelationshipsObservation = fact.observation;
      observation.children.forEach((child) => {
        const remoteId = child.remote_id;
        if (child.discovered_in?.every((x) => x === "PROMETRIX")) {
          premisesIdToPrometrixOnlyStructureIds.get(premiseId)?.add(remoteId);
        } else {
          premisesIdToStructureIds.get(premiseId)?.add(remoteId);
        }
      });
    });

    businessFacts?.forEach((fact) => {
      const businessId = fact.parent_id;
      if (
        !(fact.observation instanceof RelationshipsObservation) ||
        !businessId
      ) {
        return;
      }
      const observation: RelationshipsObservation = fact.observation;
      observation.children.forEach((child) => {
        const remoteId = child.remote_id;
        if (child.discovered_in?.every((x) => x === "PROMETRIX")) {
          businessIdToPrometrixOnlyStructureIds.get(businessId)?.add(remoteId);
        } else {
          businessIdToStructureIds.get(businessId)?.add(remoteId);
        }
      });
    });

    if (!businessFacts) {
      // For backward compatibility for old reports that dont have business-level structures
      premisesIdToStructureIds.forEach((structureIds, premiseId) => {
        const businessIds = premiseIdToBusinessIds.get(premiseId) ?? [];
        businessIds.forEach((businessId) => {
          businessIdToStructureIds.set(businessId, structureIds);
        });
      });
      premisesIdToPrometrixOnlyStructureIds.forEach(
        (structureIds, premiseId) => {
          const businessIds = premiseIdToBusinessIds.get(premiseId) ?? [];
          businessIds.forEach((businessId) => {
            businessIdToPrometrixOnlyStructureIds.set(businessId, structureIds);
          });
        }
      );
    }

    return {
      premisesIdToStructureIds,
      businessIdToStructureIds,
      premisesIdToPrometrixOnlyStructureIds,
      businessIdToPrometrixOnlyStructureIds,
    };
  }, [
    businessFacts,
    businessIds,
    premiseIdToBusinessIds,
    premisesFacts,
    premisesIds,
  ]);
};

export const useStructures = (report?: Report): StructuresContextType => {
  const defaultReport = useReport();
  const usedReport = report ?? defaultReport;

  /**
   * This hook was using metrics/summaries to get structureIds for premises,
   * but it turns out it is not feasible to use it in this hook, due to prometrix
   * Metrics do not have information about discovered in for structures (custom metric)
   * Also STRUCTURES facts were fetched anyone due to other places using useStructureFacts
   * TODO: Make summarization great again and dont fetch facts individually
   */

  const {
    businessIdToPrometrixOnlyStructureIds,
    premisesIdToPrometrixOnlyStructureIds,
    businessIdToStructureIds,
    premisesIdToStructureIds,
  } = useStructuresFromFacts({ report: usedReport, enabled: true });

  const shouldCountPrometrixData = report?.canSeePrometrix() ?? false;
  const visiblePremiseIdsToStructureIds = useMemo(() => {
    if (shouldCountPrometrixData) {
      return mergeMapsWithSets(
        premisesIdToStructureIds,
        premisesIdToPrometrixOnlyStructureIds
      );
    }
    return premisesIdToStructureIds;
  }, [
    premisesIdToPrometrixOnlyStructureIds,
    premisesIdToStructureIds,
    shouldCountPrometrixData,
  ]);
  const visibleBusinessIdsToStructureIds = useMemo(() => {
    if (shouldCountPrometrixData) {
      return mergeMapsWithSets(
        businessIdToStructureIds,
        businessIdToPrometrixOnlyStructureIds
      );
    }
    return businessIdToStructureIds;
  }, [
    businessIdToPrometrixOnlyStructureIds,
    businessIdToStructureIds,
    shouldCountPrometrixData,
  ]);

  const premisesIdToStructureCounts = useMemo(() => {
    const result = new Map<string, number>();
    visiblePremiseIdsToStructureIds.forEach((structureIds, premiseId) => {
      result.set(premiseId, structureIds.size);
    });
    return result;
  }, [visiblePremiseIdsToStructureIds]);

  return {
    businessIdToStructureIds: visibleBusinessIdsToStructureIds,
    premiseIdToStructureIds: visiblePremiseIdsToStructureIds,
    allegedStructuresCount: premisesIdToStructureCounts,
    prometrixOnlyPremiseIdToStructureIds: premisesIdToPrometrixOnlyStructureIds,
  };
};
