import { create } from "zustand";

export type StructuresContextType = {
  // premisesId -> structureIds
  premiseIdToStructureIds: Map<string, Set<string>>;
  // premiseId -> structures count
  allegedStructuresCount: Map<string, number>;

  businessIdToStructureIds: Map<string, Set<string>>;

  prometrixOnlyPremiseIdToStructureIds: Map<string, Set<string>>;
};

export const useStructuresContextData = create<StructuresContextType>(() => ({
  premiseIdToStructureIds: new Map(),
  allegedStructuresCount: new Map(),
  businessIdToStructureIds: new Map(),
  prometrixOnlyPremiseIdToStructureIds: new Map(),
}));
