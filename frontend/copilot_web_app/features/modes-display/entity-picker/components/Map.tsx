import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  Link,
  Stack,
  Typography,
} from "@mui/material";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { MarkerClusterer } from "@googlemaps/markerclusterer";
import { useSnackbar } from "notistack";
import { ErsEntity } from "@legacy/models/ErsEntity";
import { useIsGoogleServicesLoaded } from "@features/google-services";
import MeasureTool from "measuretool-googlemaps-v3";
import { EditAddress } from "@features/modes-display/entity-picker/components/EditAddress";
import { Marker } from "@googlemaps/adv-markers-utils";
import { useOrganizationId } from "@utils/auth";
import debounce from "lodash/debounce";
import {
  useInfiniteGeoEventsByBbox,
  useInfiniteGeoZonesByBbox,
} from "@queries/geoEvents";
import {
  BaseGeoEventData,
  GeoEventEarthquakeProperties,
  GeoEventFireProperties,
  GeoEventType,
  GeoEventWeatherAlertProperties,
  GeoEventWeatherProperties,
  GeoEventWeatherSubtype,
  GeoZoneFloodPropertiesData,
  GeoZoneType,
} from "@legacy/models/types/geoEvents";
import { ChipDropdown } from "@features/chip-dropdown";
import dayjs from "dayjs";
import { renderToString } from "@features/dashboard-targets/utils";
import {
  capitalizeEachFirstLetter,
  formatDateTime,
} from "@legacy/helpers/reportHelpers";
import { FmdGood } from "@mui/icons-material";
import Data = google.maps.Data;
import InfoWindow = google.maps.InfoWindow;
import GoogleMap = google.maps.Map;
import { datadogLogs } from "@datadog/browser-logs";
import { googleMapId } from "@utils/maps";

export type MapType = "map" | "streetview";

const geoColors: Record<GeoEventType | GeoZoneType | "_default", any> = {
  _default: {
    _iconUrl: "https://maps.google.com/mapfiles/ms/icons/blue-dot.png",
    fillColor: "#696969",
    fillOpacity: 0.1,
  },
  [GeoEventType.WEATHER_ALERT]: {
    _default: {
      fillColor: "#00BFFF",
      zIndex: 50,
      fillOpacity: 0.2,
      strokeColor: "#B8860B",
      strokeWeight: 0.2,
    },
  },
  [GeoEventType.WEATHER]: {
    _default: {
      fillColor: "#FFD700",
      fillOpacity: 0.2,
      strokeColor: "#B8860B",
      strokeWeight: 0.2,
    },
    [GeoEventWeatherSubtype.HAIL]: {
      _iconUrl: "https://maps.google.com/mapfiles/ms/icons/blue-dot.png",
      strokeWeight: 0.6,
      strokeColor: "#20B2AA",
      zIndex: 40,
      fillOpacity: 0,
    },
    [GeoEventWeatherSubtype.STRONG_WIND]: {
      strokeWeight: 0.2,
      fillColor: "#008B8B",
      fillOpacity: 0.1,
      zIndex: 45,
    },
    [GeoEventWeatherSubtype.HIGH_WIND]: {
      strokeWeight: 0.2,
      fillColor: "#00BFFF",
      fillOpacity: 0.1,
      zIndex: 50,
    },
    [GeoEventWeatherSubtype.TORNADO]: {
      fillColor: "#FF00FF80",
      fillOpacity: 0.1,
      strokeWeight: 0.2,
      strokeColor: "black",
      zIndex: 100,
    },
    [GeoEventWeatherSubtype.HURRICANE]: {
      fillColor: "#8B000099",
      fillOpacity: 0.2,
      strokeWeight: 0.2,
      strokeColor: "white",
      zIndex: 90,
    },
  },
  [GeoEventType.EARTHQUAKE]: {
    _default: {
      _iconUrl: "https://maps.google.com/mapfiles/ms/icons/purple-dot.png",
      fillColor: "#80008099",
      fillOpacity: 0.2,
      strokeColor: "black",
      strokeWeight: 2,
      zIndex: 70,
    },
  },
  [GeoEventType.FIRE]: {
    _default: {
      _iconUrl: "https://maps.google.com/mapfiles/ms/icons/orange-dot.png",
      fillColor: "#FF450099",
      fillOpacity: 0.2,
      strokeColor: "#8B0000",
      strokeWeight: 2,
      zIndex: 80,
    },
  },
  [GeoZoneType.FLOODZONE]: {
    _default: {
      fillColor: "#1E90FF",
      fillOpacity: 0.3,
      strokeColor: "#014361",
      strokeWeight: 0.2,
      zIndex: 60,
    },
  },
};

type MapProps = {
  entities: ErsEntity[];
  setSelectedEntityId?: (id: string | undefined) => void;
  type: MapType;
  showGeoEvents?: boolean;
  allowAddressEdit?: boolean;
  defaultZoomLevel?: number;
  defaultMapType?: "roadmap" | "satellite";
};

export const Map = ({
  entities,
  setSelectedEntityId,
  type,
  showGeoEvents = false,
  allowAddressEdit = true,
  defaultZoomLevel,
  defaultMapType = "satellite",
}: MapProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const mapRef = useRef<GoogleMap>(undefined);
  const infoWindowRef = useRef<InfoWindow>(undefined);
  const dataLayerRef = useRef<Data>(undefined);
  const isMapServiceLoaded = useIsGoogleServicesLoaded();
  const { enqueueSnackbar } = useSnackbar();
  const [newCoordinates, setNewCoordinates] = useState<{
    lat: number;
    lng: number;
  }>();

  const [selectedEventTypes, setSelectedEventTypes] = useState<string[]>([]);
  const [selectedZoneTypes, setSelectedZoneTypes] = useState<string[]>([]);
  const [selectedEventSubtypes, setSelectedEventSubtypes] = useState<string[]>(
    []
  );
  const currentYear = useMemo(() => `${new Date().getFullYear()}`, []);

  const [selectedYear, setSelectedYear] = useState<string>(currentYear);

  const updateSelectedGeoType = useCallback(
    (
      value: GeoEventType | GeoEventWeatherSubtype | GeoZoneType,
      checked: boolean
    ) => {
      const getSetter = (
        value: GeoEventType | GeoEventWeatherSubtype | GeoZoneType
      ) => {
        if (value in GeoEventType) {
          return setSelectedEventTypes;
        }
        if (
          Object.values(GeoEventWeatherSubtype).includes(
            value as GeoEventWeatherSubtype
          )
        ) {
          return setSelectedEventSubtypes;
        }
        if (value in GeoZoneType) {
          return setSelectedZoneTypes;
        }
      };
      const setter = getSetter(value);
      if (setter == null) {
        // Not possible to reach this point
        datadogLogs.logger.error(`Unknown geo event type: ${value}`);
        return;
      }
      if (checked) {
        setter((prev) => Array.from(new Set([...prev, value as string])));
      } else {
        setter((prev) => prev.filter((item) => item !== value));
      }
    },
    [setSelectedEventTypes, setSelectedEventSubtypes]
  );

  const isGeoTypeSelected = (
    value: GeoEventType | GeoEventWeatherSubtype | GeoZoneType
  ) => {
    return (
      selectedEventTypes.includes(value) ||
      selectedEventSubtypes.includes(value) ||
      selectedZoneTypes.includes(value)
    );
  };

  const clearAllGeoTypes = useCallback(() => {
    setSelectedEventTypes([]);
    setSelectedEventSubtypes([]);
    setSelectedZoneTypes([]);
  }, [setSelectedEventTypes, setSelectedEventSubtypes, setSelectedZoneTypes]);

  const selectAllGeoTypes = useCallback(() => {
    setSelectedEventTypes([
      GeoEventType.WEATHER,
      GeoEventType.FIRE,
      GeoEventType.EARTHQUAKE,
    ]);
    setSelectedEventSubtypes(Object.values(GeoEventWeatherSubtype));
    setSelectedZoneTypes(Object.values(GeoZoneType));
  }, [setSelectedEventTypes, setSelectedEventSubtypes, setSelectedZoneTypes]);

  const yearOptions = useMemo(() => {
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: currentYear - 2000 + 1 }, (_, i) =>
      (currentYear - i).toString()
    );
    return [...years.map((year) => ({ label: year, value: year }))];
  }, []);

  const datesFilter = useMemo(() => {
    return {
      startDatetime: dayjs(`${selectedYear}-01-01`)
        .startOf("year")
        .toISOString(),
      endDatetime: dayjs(`${selectedYear}-12-31`).endOf("year").toISOString(),
    };
  }, [selectedYear]);

  const orgId = useOrganizationId();
  const [eventsBbox, setEventsBbox] = useState<string>("");
  const shouldClearDataLayer = useRef(false);

  const { geoEventsData: geoEvents, isLoading: isLoadingEvents } =
    useInfiniteGeoEventsByBbox({
      bbox: eventsBbox,
      eventsTypes: selectedEventTypes as GeoEventType[],
      eventsSubtypes: selectedEventSubtypes as GeoEventWeatherSubtype[],
      limit: 100,
      callOrigin: "map",
      ...datesFilter,
    });

  const { geoZonesData: geoZones, isLoading: isLoadingZones } =
    useInfiniteGeoZonesByBbox({
      bbox: eventsBbox,
      zoneTypes: selectedZoneTypes as GeoZoneType[],
      limit: 100,
      callOrigin: "map",
    });

  const fullGeoCollection = useMemo(() => {
    return {
      features: [...geoEvents.features, ...geoZones.features],
      type: "FeatureCollection",
    };
  }, [geoEvents, geoZones]);

  const draggableMarkerRef = useRef<Marker>(undefined);

  const entity = useMemo(
    () =>
      entities.length === 1 && [3, 59].includes(orgId)
        ? entities[0]
        : undefined,
    [entities, orgId]
  );

  useEffect(() => {
    if (!isMapServiceLoaded || !ref.current) {
      return;
    }
    const coords = entities?.[0]?.getCoordinates();
    const myLatlng = new window.google.maps.LatLng(
      coords?.latitude ?? 0,
      coords?.longitude ?? 0
    );
    mapRef.current = new window.google.maps.Map(ref.current, {
      zoom: defaultZoomLevel ?? 15,
      center: myLatlng,
      gestureHandling: "cooperative",
      mapId: googleMapId,
      zoomControl: true,
      scaleControl: true,
    });

    if (showGeoEvents) {
      const updateBounds = debounce(() => {
        if (mapRef.current) {
          const bounds = mapRef.current.getBounds();
          if (bounds) {
            const topRight = bounds.getNorthEast();
            const bottomLeft = bounds.getSouthWest();
            setEventsBbox(
              `${bottomLeft.lat()},${bottomLeft.lng()},${topRight.lat()},${topRight.lng()}`
            );
          }
        }
      }, 1000);

      const boundsChangedHandler = mapRef.current.addListener(
        "bounds_changed",
        updateBounds
      );
      const dragendHandler = mapRef.current.addListener(
        "dragend",
        updateBounds
      );
      const zoomChangedHandler = mapRef.current.addListener(
        "zoom_changed",
        updateBounds
      );
      return () => {
        window.google.maps.event.removeListener(boundsChangedHandler);
        window.google.maps.event.removeListener(dragendHandler);
        window.google.maps.event.removeListener(zoomChangedHandler);
      };
    }
    // We ignore entities there since we need to center map on the creation, but don't want to recreate it in case if entities changed
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMapServiceLoaded, showGeoEvents, defaultZoomLevel]);

  useEffect(() => {
    if (!isMapServiceLoaded || !entities.length || !mapRef.current) {
      return;
    }
    const map = mapRef.current;
    const bounds = new window.google.maps.LatLngBounds();
    map.setMapTypeId(defaultMapType);

    // Need to think if we still need different logic for one entity and several
    if (entity) {
      const coords = entity.getCoordinates();
      if (!coords) return;

      const myLatlng = new window.google.maps.LatLng(
        coords?.latitude ?? 0,
        coords?.longitude ?? 0
      );

      const draggableMarker = new Marker({
        position: myLatlng,
        title: entity.getName() ?? "",
        draggable: allowAddressEdit,
        map,
      });

      draggableMarkerRef.current = draggableMarker;

      draggableMarker.addListener("dragend", (event) => {
        const position = event.latLng;

        if (position) {
          setNewCoordinates({
            lat: position.lat(),
            lng: position.lng(),
          });
          draggableMarker.position = event.latLng?.toJSON();
        }
      });

      bounds.extend(myLatlng);
      map.fitBounds(bounds);
      // Fit bounds is async, so we have to subscribe to teh event to adjust zoom
      google.maps.event.addListenerOnce(map, "bounds_changed", () => {
        map.setZoom(defaultZoomLevel ?? 18);
      });

      return () => {
        draggableMarker.map = null;
        draggableMarkerRef.current = undefined;
      };
    } else {
      const markers = entities.flatMap((entity) => {
        const coords = entity.getCoordinates();
        if (!coords) {
          return [];
        }

        const myLatlng = new window.google.maps.LatLng(
          coords?.latitude ?? 0,
          coords?.longitude ?? 0
        );
        const marker = new window.google.maps.Marker({
          position: myLatlng,
          title: entity.getName() ?? "",
        });
        marker.addListener("click", () => {
          setSelectedEntityId?.(entity.id!);
        });
        bounds.extend(myLatlng);
        return [marker];
      });

      map.fitBounds(bounds);
      // Fit bounds is async, so we have to subscribe to teh event to adjust zoom
      google.maps.event.addListenerOnce(map, "bounds_changed", () => {
        if (entities.length === 1) {
          map.setZoom(defaultZoomLevel ?? 18);
        }
      });

      const markerCluster = new MarkerClusterer({ map, markers });
      new MeasureTool(map, { unit: MeasureTool.UnitTypeId.IMPERIAL });

      return () => {
        markerCluster.clearMarkers();
      };
    }
  }, [
    isMapServiceLoaded,
    entities,
    entity,
    setSelectedEntityId,
    allowAddressEdit,
    defaultZoomLevel,
    defaultMapType,
  ]);

  useEffect(() => {
    const map = mapRef.current;
    if (!map) {
      return;
    }
    const panorama = map.getStreetView();
    if (type !== "streetview") {
      panorama.setVisible(false);
      return;
    }
    const entity = entities[0];
    if (!entity) {
      return;
    }

    const coords = entity.getCoordinates();

    const myLatlng = new window.google.maps.LatLng(
      coords?.latitude ?? 0,
      coords?.longitude ?? 0
    );

    panorama.setPosition(myLatlng);
    panorama.setVisible(true);
  }, [entities, type]);

  useEffect(() => {
    if (!isMapServiceLoaded || !mapRef.current) {
      return;
    }
    const handler = window.google.maps.event.addListener(
      mapRef.current.getStreetView(),
      "visible_changed",
      function () {
        if (
          type === "streetview" &&
          !mapRef.current?.getStreetView().getVisible() &&
          entities.length === 1
        ) {
          enqueueSnackbar(
            `StreetView seems to not be available for ${
              entities[0]?.getPremises()?.formatted_address ?? "this address"
            }`
          );
        }
      }
    );
    return () => {
      window.google.maps.event.removeListener(handler);
    };
  }, [entities, enqueueSnackbar, isMapServiceLoaded, type]);

  useEffect(() => {
    if (!isMapServiceLoaded || !showGeoEvents || !mapRef.current) {
      return;
    }
    infoWindowRef.current = new window.google.maps.InfoWindow();
    dataLayerRef.current = new window.google.maps.Data({ map: mapRef.current });

    const infoWindowHandler = window.google.maps.event.addListener(
      dataLayerRef.current,
      "click",
      (event: google.maps.Data.MouseEvent) => {
        if (!infoWindowRef.current) {
          return;
        }
        infoWindowRef.current.close();

        const feature = event.feature;
        const eventData = feature.getProperty("event_data") as
          | BaseGeoEventData
          | undefined;
        const eventDate =
          eventData?.event_datetime &&
          formatDateTime(eventData?.event_datetime);

        const zoneType = feature.getProperty("zone_type") as
          | GeoZoneType
          | undefined;
        const zoneData = feature.getProperty("data") as
          | GeoZoneFloodPropertiesData
          | undefined;

        const eventType = eventData?.event_type;
        let content = "";
        let headerText =
          eventData?.event_subtype ?? eventData?.event_type ?? zoneType;
        if (eventType === GeoEventType.WEATHER) {
          const eventNarrative = feature.getProperty(
            "event_narrative"
          ) as GeoEventWeatherProperties["event_narrative"];
          content = renderToString(
            <Typography fontSize={"13px"}>{eventNarrative}</Typography>
          );
        } else if (eventType === GeoEventType.EARTHQUAKE) {
          const place = feature.getProperty(
            "place"
          ) as GeoEventEarthquakeProperties["place"];
          const depth = feature.getProperty(
            "depth"
          ) as GeoEventEarthquakeProperties["depth"];
          const magnitude = feature.getProperty(
            "magnitude"
          ) as GeoEventEarthquakeProperties["magnitude"];
          const url = feature.getProperty(
            "url"
          ) as GeoEventEarthquakeProperties["url"];
          content = renderToString(
            <Box fontSize={"13px"}>
              <Typography>{place}</Typography>
              <Typography>Date: {eventDate}</Typography>
              <Typography>Depth: {depth} km</Typography>
              <Typography>Magnitude: {magnitude}</Typography>
              <Link
                href={url}
                target={"_blank"}
                rel="noopener noreferrer"
                sx={{ cursor: "pointer" }}
              >
                {url}
              </Link>
            </Box>
          );
        } else if (eventType === GeoEventType.FIRE) {
          const brightness = feature.getProperty(
            "brightness"
          ) as GeoEventFireProperties["brightness"];
          const confidence = feature.getProperty(
            "confidence"
          ) as GeoEventFireProperties["confidence"];
          const sensor = feature.getProperty(
            "sensor"
          ) as GeoEventFireProperties["sensor"];
          const frp = feature.getProperty(
            "frp"
          ) as GeoEventFireProperties["frp"];
          content = renderToString(
            <Box fontSize={"13px"}>
              <Typography>Date: {eventDate}</Typography>
              <Typography>Brightness: {brightness}</Typography>
              <Typography>Confidence: {confidence}</Typography>
              <Typography>Sensor: {sensor}</Typography>
              {frp != null && <Typography>FRP: {frp}</Typography>}
            </Box>
          );
        } else if (eventType === GeoEventType.WEATHER_ALERT) {
          const severity = feature.getProperty(
            "severity"
          ) as GeoEventWeatherAlertProperties["severity"];
          const headline = feature.getProperty(
            "headline"
          ) as GeoEventWeatherAlertProperties["headline"];
          const urgency = feature.getProperty(
            "urgency"
          ) as GeoEventWeatherAlertProperties["urgency"];
          const senderName = feature.getProperty(
            "senderName"
          ) as GeoEventWeatherAlertProperties["senderName"];
          content = renderToString(
            <Box fontSize={"13px"}>
              <Typography>From: {senderName}</Typography>
              <Typography>{headline}</Typography>
              <Typography>Date: {eventDate}</Typography>
              <Typography>Severity: {severity}</Typography>
              <Typography>Urgency: {urgency}</Typography>
            </Box>
          );
        }

        if (zoneType === GeoZoneType.FLOODZONE) {
          const baseFloodElevationInFeets =
            zoneData?.base_flood_elevation_in_feets;
          const floodZoneType = zoneData?.zone_type ?? "N/A";
          headerText = "Flood Zone";
          content = renderToString(
            <Box fontSize={"13px"}>
              <Typography>Zone Type: {floodZoneType}</Typography>
              <Typography>
                Base Flood Elevation (BFE):{" "}
                {baseFloodElevationInFeets == null
                  ? "N/A"
                  : `${baseFloodElevationInFeets} ft.`}
              </Typography>
            </Box>
          );
        }

        const position = event.latLng;

        // Silly Google Maps API doesn't allow to set the header content, but supports it for just Content
        const headerDiv = document.createElement("div");
        headerDiv.innerHTML = renderToString(
          <Typography variant={"h6"}>
            {capitalizeEachFirstLetter(headerText ?? "")}
          </Typography>
        );
        infoWindowRef.current.setHeaderContent(headerDiv);
        infoWindowRef.current.setContent(content);
        infoWindowRef.current.setPosition(position);
        infoWindowRef.current.open(mapRef.current);
      }
    );

    const freeClickHandler = window.google.maps.event.addListener(
      mapRef.current,
      "click",
      () => {
        infoWindowRef.current?.close();
      }
    );
    return () => {
      window.google.maps.event.removeListener(infoWindowHandler);
      window.google.maps.event.removeListener(freeClickHandler);
    };
  }, [isMapServiceLoaded, showGeoEvents]);

  useEffect(() => {
    // When filter selection changed we need to clear the entire data layer
    shouldClearDataLayer.current = true;
  }, [selectedYear]);

  useEffect(() => {
    if (
      !showGeoEvents ||
      !fullGeoCollection.features ||
      !dataLayerRef.current ||
      !mapRef.current ||
      !infoWindowRef.current
    ) {
      return;
    }

    // Clear entire data layer only when we changed filters
    if (shouldClearDataLayer.current) {
      dataLayerRef.current.forEach((feature) => {
        const zoneType = feature.getProperty("zone_type") as
          | string
          | null
          | undefined;
        // If zoneType is present we shouldn't delete the feature since it's year independent
        if (zoneType != null) return;

        dataLayerRef.current!.remove(feature);
      });
      shouldClearDataLayer.current = false;
    }

    dataLayerRef.current.forEach((feature) => {
      // We remove it in case some types were deselected
      // Geo Events block
      const eventData = feature.getProperty("event_data") as
        | BaseGeoEventData
        | undefined;
      if (eventData != null) {
        const eventType = eventData?.event_type;
        const eventSubtype = eventData?.event_subtype;
        if (
          eventType === GeoEventType.WEATHER &&
          !selectedEventSubtypes.includes(eventSubtype ?? "")
        ) {
          dataLayerRef.current!.remove(feature);
        }
        if (
          eventType !== GeoEventType.WEATHER &&
          !selectedEventTypes.includes(eventType)
        ) {
          dataLayerRef.current!.remove(feature);
        }
      }

      // Geo Zones block
      const zoneType = feature.getProperty("zone_type") as
        | GeoZoneType
        | undefined;
      if (zoneType != null && !selectedZoneTypes.includes(zoneType)) {
        dataLayerRef.current!.remove(feature);
      }
    });

    const notAddedGeoJsonFeatures = {
      features: fullGeoCollection.features.filter((geoEvent) => {
        const feature = dataLayerRef.current!.getFeatureById(geoEvent.id);
        return !feature;
      }),
      type: "FeatureCollection",
    };

    dataLayerRef.current.addGeoJson(notAddedGeoJsonFeatures);

    dataLayerRef.current.setStyle((feature) => {
      const featureType = feature.getGeometry()?.getType();
      const eventData = feature.getProperty("event_data") as
        | BaseGeoEventData
        | undefined;
      const zoneType = feature.getProperty("zone_type") as
        | GeoZoneType
        | undefined;

      const eventType = eventData?.event_type;
      const eventSubtype = eventData?.event_subtype;
      const paletteByType = geoColors[eventType ?? zoneType ?? "_default"];

      if (featureType === "Point") {
        return {
          icon: {
            url:
              paletteByType?._default?._iconUrl ?? geoColors._default._iconUrl,
            scaledSize: new window.google.maps.Size(32, 32),
          },
        };
      }

      if (!paletteByType) {
        return geoColors._default;
      }
      return paletteByType[eventSubtype ?? "_default"];
    });
  }, [
    isMapServiceLoaded,
    showGeoEvents,
    selectedEventTypes,
    selectedEventSubtypes,
    selectedZoneTypes,
    selectedYear,
    fullGeoCollection.features,
  ]);

  return (
    <Box>
      {entity && allowAddressEdit && (
        <EditAddress
          entity={entity}
          newCoordinates={newCoordinates}
          onEditCancel={() => {
            if (draggableMarkerRef.current) {
              draggableMarkerRef.current.setAttributes({
                position: new google.maps.LatLng(
                  entity.getCoordinates()!.latitude,
                  entity.getCoordinates()!.longitude
                ),
              });
            }
          }}
        />
      )}
      <Stack
        direction={"row"}
        alignItems={"stretch"}
        border={1}
        borderRadius={"4px"}
        borderColor={"#E5E5E5"}
      >
        {showGeoEvents && (
          <Stack
            direction={"column"}
            minWidth={250}
            paddingTop={1}
            paddingX={2}
            marginY={1}
          >
            <Stack
              direction={"row"}
              justifyContent={"space-between"}
              alignItems={"center"}
            >
              <Typography variant={"h6"}>Hazard Events</Typography>
              {(isLoadingEvents || isLoadingZones) && (
                <CircularProgress size={20} />
              )}
            </Stack>

            <Stack
              direction={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              mt={2}
            >
              <Typography color={"GrayText"}>Year</Typography>
              <ChipDropdown
                variant="outlined"
                color="link"
                label="Year"
                options={yearOptions}
                value={selectedYear}
                onSelect={setSelectedYear}
                chipWidth={100}
                useSelectionAsLabel
                menuMaxHeight={"400px"}
              />
            </Stack>
            <Stack direction={"column"} alignItems={"start"} mt={2}>
              <FormControl
                component="fieldset"
                variant="standard"
                sx={{ width: "100%" }}
              >
                <Stack
                  direction={"row"}
                  alignItems={"center"}
                  justifyContent={"space-between"}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isGeoTypeSelected(GeoEventWeatherSubtype.HAIL)}
                        onChange={(event) =>
                          updateSelectedGeoType(
                            GeoEventWeatherSubtype.HAIL,
                            event.target.checked
                          )
                        }
                      />
                    }
                    label={<Typography color={"GrayText"}>Hail</Typography>}
                  />
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <FmdGood htmlColor={"#5781FC"} />
                  </Box>
                </Stack>
                <Stack
                  direction={"row"}
                  alignItems={"center"}
                  justifyContent={"space-between"}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isGeoTypeSelected(
                          GeoEventWeatherSubtype.HURRICANE
                        )}
                        onChange={(event) =>
                          updateSelectedGeoType(
                            GeoEventWeatherSubtype.HURRICANE,
                            event.target.checked
                          )
                        }
                      />
                    }
                    label={
                      <Typography color={"GrayText"}>
                        Hurricane (Typhoon)
                      </Typography>
                    }
                  />
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      border: "1px #FFFFFF",
                      backgroundColor: "#B56262",
                      borderRadius: "4px",
                    }}
                  />
                </Stack>
                <Stack
                  direction={"row"}
                  alignItems={"center"}
                  justifyContent={"space-between"}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isGeoTypeSelected(
                          GeoEventWeatherSubtype.TORNADO
                        )}
                        onChange={(event) =>
                          updateSelectedGeoType(
                            GeoEventWeatherSubtype.TORNADO,
                            event.target.checked
                          )
                        }
                      />
                    }
                    label={<Typography color={"GrayText"}>Tornado</Typography>}
                  />
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      border: "1px dashed #000000",
                      backgroundColor: "#FB62FB",
                      borderRadius: "4px",
                    }}
                  />
                </Stack>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={
                        isGeoTypeSelected(GeoEventWeatherSubtype.HIGH_WIND) &&
                        isGeoTypeSelected(GeoEventWeatherSubtype.STRONG_WIND)
                      }
                      indeterminate={
                        isGeoTypeSelected(GeoEventWeatherSubtype.HIGH_WIND) !==
                        isGeoTypeSelected(GeoEventWeatherSubtype.STRONG_WIND)
                      }
                      onChange={(event) => {
                        updateSelectedGeoType(
                          GeoEventWeatherSubtype.HIGH_WIND,
                          event.target.checked
                        );
                        updateSelectedGeoType(
                          GeoEventWeatherSubtype.STRONG_WIND,
                          event.target.checked
                        );
                      }}
                    />
                  }
                  label={<Typography color={"GrayText"}>Wind</Typography>}
                />
                <Stack
                  direction={"row"}
                  alignItems={"center"}
                  justifyContent={"space-between"}
                  pl={2}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isGeoTypeSelected(
                          GeoEventWeatherSubtype.HIGH_WIND
                        )}
                        onChange={(event) =>
                          updateSelectedGeoType(
                            GeoEventWeatherSubtype.HIGH_WIND,
                            event.target.checked
                          )
                        }
                      />
                    }
                    label={
                      <Typography color={"GrayText"}>High Wind</Typography>
                    }
                  />
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: "#7ADAFA",
                      borderRadius: "4px",
                    }}
                  />
                </Stack>
                <Stack
                  direction={"row"}
                  alignItems={"center"}
                  justifyContent={"space-between"}
                  pl={2}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isGeoTypeSelected(
                          GeoEventWeatherSubtype.STRONG_WIND
                        )}
                        onChange={(event) =>
                          updateSelectedGeoType(
                            GeoEventWeatherSubtype.STRONG_WIND,
                            event.target.checked
                          )
                        }
                      />
                    }
                    label={
                      <Typography color={"GrayText"}>Strong Wind</Typography>
                    }
                  />
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: "#7AC0C0",
                      borderRadius: "4px",
                    }}
                  />
                </Stack>
                <Stack
                  direction={"row"}
                  alignItems={"center"}
                  justifyContent={"space-between"}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isGeoTypeSelected(GeoEventType.EARTHQUAKE)}
                        onChange={(event) =>
                          updateSelectedGeoType(
                            GeoEventType.EARTHQUAKE,
                            event.target.checked
                          )
                        }
                      />
                    }
                    label={
                      <Typography color={"GrayText"}>Earthquake</Typography>
                    }
                  />
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <FmdGood htmlColor={"#7E55FC"} />
                  </Box>
                </Stack>
                <Stack
                  direction={"row"}
                  alignItems={"center"}
                  justifyContent={"space-between"}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isGeoTypeSelected(GeoEventType.FIRE)}
                        onChange={(event) =>
                          updateSelectedGeoType(
                            GeoEventType.FIRE,
                            event.target.checked
                          )
                        }
                      />
                    }
                    label={<Typography color={"GrayText"}>Fire</Typography>}
                  />
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <FmdGood htmlColor={"#FF9900"} />
                  </Box>
                </Stack>
                <Typography variant={"h6"} mt={2} mb={1}>
                  Risk Zones
                </Typography>
                <Stack
                  direction={"row"}
                  alignItems={"center"}
                  justifyContent={"space-between"}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isGeoTypeSelected(GeoZoneType.FLOODZONE)}
                        onChange={(event) =>
                          updateSelectedGeoType(
                            GeoZoneType.FLOODZONE,
                            event.target.checked
                          )
                        }
                      />
                    }
                    label={<Typography color={"GrayText"}>Flood</Typography>}
                  />
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: "#74B8FB",
                      borderRadius: "4px",
                      border: "1px solid #014361",
                    }}
                  />
                </Stack>
              </FormControl>
            </Stack>
            <Stack
              marginTop={"auto"}
              height={25}
              direction={"row"}
              spacing={1}
              justifyContent={"flex-end"}
            >
              <Button onClick={clearAllGeoTypes}>Clear</Button>
              <Button onClick={selectAllGeoTypes}>Select All</Button>
            </Stack>
          </Stack>
        )}
        <Box
          flex={1}
          ref={ref}
          className="premises-viewer-element"
          minWidth={590}
          borderRadius={"0 4px 4px 0"}
        />
      </Stack>
    </Box>
  );
};

Map.displayName = "Map";
