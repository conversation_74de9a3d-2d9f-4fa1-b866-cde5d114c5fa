import React, { useMemo } from "react";
import {
  <PERSON>,
  Divider,
  Icon<PERSON>utton,
  Stack,
  Typography,
  useTheme,
} from "@mui/material";
import { ErsEntity } from "@legacy/models/ErsEntity";
import {
  Map,
  MapType,
} from "@features/modes-display/entity-picker/components/Map";
import { WeatherDataCard } from "@features/cards/weather-data";
import { PremisesViewerEntityContext } from "../context/EntityContext";
import {
  ClosedAtEvaluator,
  CrimeCardEvaluators,
  LocationRisksEvaluators,
} from "@features/modes/fact-error-state-evaluators";
import { Facts } from "@features/modes/jsx/reusables/Facts";
import { EntityContext } from "@features/modes-core/components/EntityContext";
import { DOCUMENT_TYPES, PARENT_TYPES } from "@legacy/constants/facts";
import { BusinessesAtThisLocation } from "@features/cards/businesses-at-this-location";
import { NewModeCard } from "@features/modes-core/components/NewModeCard";
import { NewsCard } from "@features/modes-display/NewsCard";
import { HasDocumentsOrFactsCondition } from "@features/modes/jsx/conditions/HasDocumentsOrFactsCondition";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { ImagesCard } from "@features/cards/images";
import { CollapsiblePanel } from "@features/fact-panel/components/CollapsiblePanel";
import { LegalFilingsCard } from "@features/modes-display/LegalFilingsCard";
import { PermitsCard } from "@features/cards/permits";
import { CertificatesCard } from "@features/cards/certificates";
import { AddressInfoLink } from "@features/info-panel/components/AddressInfoLink";
import SubmissionBusiness from "@legacy/models/SubmissionBusiness";
import { ArrowBack, PinDropOutlined } from "@mui/icons-material";
import { AdditionalInformationCard } from "@features/modes/jsx/reusables/AdditionalInformationCard";
import { OperationsCardPremisesFacts } from "@features/modes-display/entity-picker/components/OperationsCardPremisesFacts";
import { DocumentsCard } from "@features/cards/documents";
import { useOperationCardsGroups } from "@features/modes/jsx/hooks/useOperationCardsGroups";
import { useReport } from "@utils/useReport";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { OshaViolations } from "@features/modes/jsx/reusables/tables/OshaViolations";
import { ELEMENT_ID } from "@features/modes/jsx/constants";
import { EpaInspections } from "@features/modes/jsx/reusables/tables/EpaInspections";
import { Structures } from "@features/modes/jsx/reusables/Structures";
import { usePremiseAndStructureData } from "@features/modes/jsx/reusables/entity-picker/hooks/usePremiseAndStructureData";
import { ModeSection } from "@features/modes-core/components/ModeSection";
import { HasReportCondition } from "@features/modes/jsx/conditions/HasReportCondition";
import { PropertyInsuranceValuationCard } from "@features/modes-display/entity-picker/components/PropertyInsuranceValuationCard";
import {
  COPECards,
  useExposureCardData,
} from "@features/modes-display/entity-picker/components/COPECards";
import { PrometrixCard } from "@features/cards/prometrix";
import { RiskmeterCard } from "@features/cards/riskmeter";
import { usePremisesPickerStore } from "@features/modes-display/premises-picker/context/PremisesPickerData";
import { useStore } from "zustand";
import { useAdditionalInfoFacts } from "@features/cards/additional-information/useAdditionalInfoFacts";
import { Processor } from "@features/modes-display/facts-utils";
import { EmptyHHMessageWithSync } from "@features/hazard-hub/EmptyHHMessageWithSync";

const PremisesDetailsFacts = ({
  title,
  facts,
  errorStateEvaluators,
  processors,
  ignoreCustomOrdering,
}: {
  title?: string;
  facts: any;
  errorStateEvaluators?: any[];
  processors?: Processor[];
  ignoreCustomOrdering?: boolean;
}) => {
  return (
    <Facts
      title={title}
      facts={facts}
      columns={2}
      hideDivider
      factLabelFontWeight={500}
      processors={processors}
      ignoreCustomOrdering={ignoreCustomOrdering}
      errorStateEvaluators={errorStateEvaluators}
    />
  );
};

type StructuresPanelProps = {
  createFakeStructure: boolean;
  businessId: string;
};

const StructuresPanel = ({
  createFakeStructure,
  businessId,
}: StructuresPanelProps) => {
  return (
    <>
      {createFakeStructure && (
        <>
          <PremisesDetailsFacts
            title="Property Insurance Valuation"
            facts={{
              group: "PROPERTY_INSURANCE_VALUATION",
              alwaysDisplay: [
                "BPP",
                "BUILDING_VALUE",
                "CONTENT_VALUE",
                "BUSINESS_INCOME",
              ],
              allowAdditionalFields: true,
            }}
          />
          <PremisesDetailsFacts
            title="Building exposure"
            facts={{
              group: "BUILDING_EXPOSURE",
            }}
          />
          <PremisesDetailsFacts
            title="Building Integrity risk"
            facts={{ group: "BUILDING_INTEGRITY_RISK" }}
          />
          <PremisesDetailsFacts title="Roof" facts={{ group: "ROOF" }} />
          <PremisesDetailsFacts
            title="Fire risk"
            facts={{ group: "FIRE_RISK" }}
          />
          <PremisesDetailsFacts
            title="Crime Protection"
            facts={{ group: "CRIME_PROTECTION" }}
          />
          <PremisesDetailsFacts title="Ownership" facts={{ group: "OWNERS" }} />
        </>
      )}
      {!createFakeStructure && (
        <EntityContext
          parentType={PARENT_TYPES.BUSINESS}
          parentIds={[businessId].filter(Boolean)}
        >
          <Structures
            source="regular"
            columns={2}
            hideDivider
            factLabelFontWeight={500}
          />
        </EntityContext>
      )}
    </>
  );
};

type Props = {
  business?: SubmissionBusiness | null;
  mapEntities: ErsEntity[];
  onEntitySelected?: (id: string | undefined) => void;
  mapType: MapType;
};

export const NewPremisesDetails = ({
  business,
  onEntitySelected,
  mapType,
  mapEntities,
}: Props) => {
  const { internalUseFeatures } = useFeatureFlags();
  const user = useCurrentUser();
  const report = useReport();
  const theme = useTheme();
  const operationsGroups = useOperationCardsGroups();
  const organizationId = useOrganizationId();

  const isK2 = organizationId === 61;

  const { entity, source, businessParentIds, premisesParentIds } =
    useMemo(() => {
      const entity = business?.entity_data;
      return {
        entity: entity,
        businessParentIds: entity?.id ? [entity.id] : [],
        premisesParentIds: entity?.getPremises()?.id
          ? [entity!.getPremises()!.id!]
          : [],
        source: business?.file_id_sources ? business.file_id_sources : [],
      };
    }, [business]);

  const { usedOperationGroups, isGarage } = useMemo(() => {
    const coverages = report?.getSubmission().coverages;
    const isGarage = coverages?.some(
      (c) => c.coverage.name === "garageDealers"
    );

    const businesses = (report?.getBusinesses() ?? []).length;
    return {
      usedOperationGroups:
        businesses > 1
          ? ["BUSINESS_HEADER", ...operationsGroups]
          : operationsGroups,
      isGarage,
    };
  }, [report, operationsGroups]);

  const { hasValue: hasExposures } = useExposureCardData(premisesParentIds);

  const { createFakeStructure } = usePremiseAndStructureData(entity);
  const setSelectedItem = usePremisesPickerStore().use.setSelectedItem();
  const isRealEstateOnly = entity?.isOnlyNameRealEstate() ?? true;
  const isFni = business?.named_insured === "FIRST_NAMED_INSURED";
  const isSingleItemViewer = useStore(
    usePremisesPickerStore(),
    (state) => state.allItems.length === 1
  );

  const { sortedFactsWithErrorStates } = useAdditionalInfoFacts({
    report,
    organizationId,
  });

  return (
    <PremisesViewerEntityContext.Provider value={entity ?? null}>
      <EntityContext
        parentType={PARENT_TYPES.PREMISES}
        entity={entity}
        parentIds={premisesParentIds}
      >
        {entity && (
          <Box sx={{ padding: 2 }}>
            <Box
              display="flex"
              flexDirection="row"
              alignItems="center"
              mb={1}
              gap={1}
            >
              {!isSingleItemViewer && (
                <IconButton onClick={() => setSelectedItem(null)}>
                  <ArrowBack />
                </IconButton>
              )}
              <Typography
                variant="h5"
                sx={{
                  display: "-webkit-box",
                  "-webkit-line-clamp": "2",
                  "-webkit-box-orient": "vertical",
                  overflow: "hidden",
                }}
              >
                {isRealEstateOnly && business?.requested_name}{" "}
                {entity?.getName()}
              </Typography>
              {isFni && (
                <Box
                  sx={{
                    backgroundColor:
                      theme.palette.dataVisualization.blue.series1,
                  }}
                >
                  <Typography
                    color={theme.palette.brand.text.default}
                    fontWeight={500}
                    py={0.5}
                    px={2}
                  >
                    First Named Insured
                  </Typography>
                </Box>
              )}
            </Box>

            <Stack
              direction={"row"}
              spacing={1}
              alignItems={"center"}
              mb={1}
              pl={isSingleItemViewer ? 0 : 1}
            >
              <PinDropOutlined />
              <AddressInfoLink
                addressText={business?.getAddress() ?? null}
                businessId={business?.id}
                source={source}
                propagate={false}
              ></AddressInfoLink>
            </Stack>
            <Divider />
          </Box>
        )}
        <Box sx={{ overflowY: "auto" }}>
          {entity && (
            <Box
              flex="1 1 0"
              marginY={2}
              marginX={2}
              sx={{
                ".premises-viewer-element": {
                  height: "60vh",
                },
                ["@media (max-height: 1050px)"]: {
                  ".premises-viewer-element": {
                    height: "550px",
                  },
                },
              }}
            >
              <Map
                entities={mapEntities}
                setSelectedEntityId={onEntitySelected}
                type={mapType}
                showGeoEvents
              />
            </Box>
          )}

          <Box sx={{ display: entity ? undefined : "none" }}>
            {!hasExposures && (
              <EmptyHHMessageWithSync
                sx={{
                  width: "100%",
                  border: "1px solid #E5E5E5",
                  borderRadius: "4px",
                  height: 200,
                  my: 2,
                }}
              />
            )}
            <COPECards />

            <CollapsiblePanel
              sx={{ marginY: 2 }}
              title={"Property Insurance Valuation"}
              showHeaderDivider
            >
              <PropertyInsuranceValuationCard />
            </CollapsiblePanel>

            <ModeSection hideSidebarTitle>
              <CollapsiblePanel
                sx={{ marginY: 2 }}
                title={"Premises"}
                showHeaderDivider
              >
                {!createFakeStructure && (
                  <HasDocumentsOrFactsCondition
                    parentType={[PARENT_TYPES.PREMISES]}
                    groupIds={["PROPERTY_INSURANCE_VALUATION"]}
                  >
                    <PremisesDetailsFacts
                      title="Property Insurance Valuation"
                      facts={{
                        group: "PROPERTY_INSURANCE_VALUATION",
                        alwaysDisplay: [
                          "BPP",
                          "BUILDING_VALUE",
                          "CONTENT_VALUE",
                          "BUSINESS_INCOME",
                        ],
                        allowAdditionalFields: true,
                      }}
                    />
                  </HasDocumentsOrFactsCondition>
                )}
                <HasDocumentsOrFactsCondition
                  parentType={PARENT_TYPES.PREMISES}
                  groupIds={["DISTANCE_TO_HAZARDS"]}
                >
                  <PremisesDetailsFacts
                    facts={{ group: "DISTANCE_TO_HAZARDS" }}
                  />
                </HasDocumentsOrFactsCondition>
                {!createFakeStructure && (
                  <PremisesDetailsFacts
                    title="Building exposure"
                    facts={{ group: "BUILDING_EXPOSURE" }}
                  />
                )}
                <PremisesDetailsFacts
                  title="Building integrity risk"
                  facts={{ group: "BUILDING_INTEGRITY_RISK" }}
                />
                <PremisesDetailsFacts title="Roof" facts={{ group: "ROOF" }} />
                <PremisesDetailsFacts
                  title="Land details"
                  facts={{ group: "LAND_FACTS" }}
                />
                <PremisesDetailsFacts
                  title="Land use"
                  facts={{ group: "LAND_USE" }}
                />
                <HasReportCondition
                  report={(report) => !report.hasPropertyOrEquivalent()}
                  description="Have Property coverage or equivalent"
                >
                  <PremisesDetailsFacts
                    title="Estimated sale value"
                    facts={{ group: "ESTIMATED_SALE_VALUE" }}
                  />
                </HasReportCondition>
                <PremisesDetailsFacts
                  title="Fire risk"
                  facts={{ group: "FIRE_RISK" }}
                />
                <PremisesDetailsFacts
                  title="Crime Protection"
                  facts={{ group: "CRIME_PROTECTION" }}
                />
                <PremisesDetailsFacts
                  title="Owners"
                  facts={{ group: "OWNERS" }}
                />
                <PremisesDetailsFacts
                  title="Other"
                  facts={{ group: "OTHER_PROPERTY_FACTS" }}
                />
                <PremisesDetailsFacts
                  title="Premises - Other"
                  facts={{ group: "PREMISES_OTHER" }}
                />
              </CollapsiblePanel>
            </ModeSection>

            <ModeSection hideSidebarTitle id={ELEMENT_ID.STRUCTURES_TAB}>
              <CollapsiblePanel
                sx={{ marginY: 2 }}
                title={"Structures"}
                showHeaderDivider
              >
                <StructuresPanel
                  createFakeStructure={createFakeStructure}
                  businessId={business?.business_id ?? ""}
                />
              </CollapsiblePanel>
            </ModeSection>

            <EntityContext
              parentType={PARENT_TYPES.BUSINESS}
              entity={entity}
              parentIds={businessParentIds}
            >
              <ModeSection hideSidebarTitle>
                <CollapsiblePanel
                  sx={{ marginY: 2 }}
                  title={"Business Operations"}
                  defaultExpanded={false}
                  showHeaderDivider
                >
                  <PremisesDetailsFacts
                    title="Operations"
                    facts={{
                      groups: usedOperationGroups,
                      excludeSubtypeIds: internalUseFeatures
                        ? undefined
                        : ["MULTI_LABEL_NAICS_CODES"],
                    }}
                    errorStateEvaluators={[ClosedAtEvaluator]}
                  />
                  <OperationsCardPremisesFacts hideDivider />
                  <PremisesDetailsFacts
                    title="Financials"
                    facts={{
                      groups: ["OPERATIONS_FINANCIALS"],
                      excludeSynonyms: [["TOTAL_SALES", "ANNUAL_SALES_RANGE"]],
                    }}
                  />
                  <PremisesDetailsFacts
                    title="Operations - Other"
                    facts={{
                      groups: isGarage
                        ? ["GARAGE_OPERATIONS_OTHER", "OPERATIONS_OTHER"]
                        : ["OPERATIONS_OTHER"],
                    }}
                  />
                  <HasDocumentsOrFactsCondition
                    parentType={[PARENT_TYPES.BUSINESS]}
                    documentIds={[DOCUMENT_TYPES.LICENSE]}
                  >
                    <NewModeCard title="Licenses" hideDivider>
                      <DocumentsCard
                        documentType={DOCUMENT_TYPES.LICENSE}
                        expand={["body"]}
                        hideWhenEmpty={true}
                      />
                    </NewModeCard>
                  </HasDocumentsOrFactsCondition>
                </CollapsiblePanel>
              </ModeSection>
            </EntityContext>

            <HasDocumentsOrFactsCondition
              parentType={PARENT_TYPES.PREMISES}
              groupIds={["LOCATION_RISKS"]}
            >
              <CollapsiblePanel
                sx={{ marginY: 2 }}
                title={"CAT Scores"}
                id={ELEMENT_ID.NATURAL_HAZARDS_RISKS}
                showHeaderDivider
              >
                <PremisesDetailsFacts
                  facts={{ group: "LOCATION_RISKS" }}
                  processors={["orderByCATScore"]}
                  ignoreCustomOrdering={true}
                  errorStateEvaluators={LocationRisksEvaluators}
                />
              </CollapsiblePanel>
            </HasDocumentsOrFactsCondition>

            <ModeSection hideSidebarTitle>
              <CollapsiblePanel
                sx={{ marginY: 2 }}
                title={"Weather Data"}
                defaultExpanded={false}
                showHeaderDivider
              >
                {isK2 && <WeatherDataCard />}
              </CollapsiblePanel>
            </ModeSection>

            <EntityContext
              parentType={PARENT_TYPES.BUSINESS}
              entity={entity}
              parentIds={businessParentIds}
            >
              <ModeSection hideSidebarTitle>
                <CollapsiblePanel
                  sx={{ marginY: 2 }}
                  title="Images"
                  showHeaderDivider
                >
                  <ImagesCard hideDivider />
                </CollapsiblePanel>
              </ModeSection>
            </EntityContext>

            {sortedFactsWithErrorStates.length !== 0 && (
              <EntityContext
                parentType={PARENT_TYPES.BUSINESS}
                entity={entity}
                parentIds={businessParentIds}
              >
                <ModeSection hideSidebarTitle>
                  <CollapsiblePanel
                    sx={{ marginY: 2 }}
                    title={"Additional Submission Info"}
                    defaultExpanded={false}
                    showHeaderDivider
                  >
                    <AdditionalInformationCard
                      hideDivider
                      factLabelFontWeight={500}
                    />
                  </CollapsiblePanel>
                </ModeSection>
              </EntityContext>
            )}

            <EntityContext
              parentType={PARENT_TYPES.BUSINESS}
              entity={entity}
              parentIds={businessParentIds}
            >
              <HasDocumentsOrFactsCondition
                parentType={PARENT_TYPES.BUSINESS}
                groupIds={["CERTIFICATES"]}
                documentIds={[DOCUMENT_TYPES.CERTIFICATE]}
              >
                <CollapsiblePanel
                  sx={{ marginY: 2 }}
                  title={"Certificates"}
                  defaultExpanded={false}
                  showHeaderDivider
                >
                  <NewModeCard hideDivider>
                    <CertificatesCard />
                  </NewModeCard>
                </CollapsiblePanel>
              </HasDocumentsOrFactsCondition>
            </EntityContext>

            <HasDocumentsOrFactsCondition
              parentType={PARENT_TYPES.PREMISES}
              documentIds={[DOCUMENT_TYPES.PERMIT]}
            >
              <CollapsiblePanel
                sx={{ marginY: 2 }}
                title={"Premises Building Permits"}
                defaultExpanded={false}
                showHeaderDivider
              >
                <NewModeCard hideDivider>
                  <PermitsCard />
                </NewModeCard>
              </CollapsiblePanel>
            </HasDocumentsOrFactsCondition>

            <EntityContext
              parentType={PARENT_TYPES.BUSINESS}
              entity={entity}
              parentIds={businessParentIds}
            >
              <ModeSection hideSidebarTitle>
                <HasDocumentsOrFactsCondition
                  parentType={PARENT_TYPES.BUSINESS}
                  documentIds={[DOCUMENT_TYPES.LEGAL_FILING]}
                  showAnyway={user.cross_organization_access}
                  showAnywayReason="User has cross organization access"
                >
                  <CollapsiblePanel
                    sx={{ marginY: 2 }}
                    title={"Legal Filings"}
                    defaultExpanded={false}
                    showHeaderDivider
                  >
                    <NewModeCard hideDivider>
                      <LegalFilingsCard />
                    </NewModeCard>
                  </CollapsiblePanel>
                </HasDocumentsOrFactsCondition>
              </ModeSection>
            </EntityContext>

            <EntityContext
              parentType={PARENT_TYPES.BUSINESS}
              entity={entity}
              parentIds={businessParentIds}
            >
              <ModeSection hideSidebarTitle>
                <CollapsiblePanel
                  sx={{ marginY: 2 }}
                  title={"Violations"}
                  defaultExpanded={false}
                  showHeaderDivider
                >
                  <OshaViolations
                    id={ELEMENT_ID.OSHA_VIOLATIONS_ENTITY_PICKER}
                  />
                  <EpaInspections />
                </CollapsiblePanel>
              </ModeSection>
            </EntityContext>

            <ModeSection hideSidebarTitle id={ELEMENT_ID.CRIME_SCORE}>
              <CollapsiblePanel
                sx={{ marginY: 2 }}
                title={"Crime Score"}
                defaultExpanded={true}
                showHeaderDivider
              >
                <PremisesDetailsFacts
                  facts={{ factSubtypeIds: ["OVERALL_CRIME_GRADE"] }}
                />

                <PremisesDetailsFacts
                  facts={{ group: "CRIME_CARD" }}
                  processors={["orderByCrimeScoreAndName"]}
                  ignoreCustomOrdering={true}
                  errorStateEvaluators={CrimeCardEvaluators}
                />
              </CollapsiblePanel>
            </ModeSection>

            <EntityContext
              parentType={PARENT_TYPES.BUSINESS}
              entity={entity}
              parentIds={businessParentIds}
            >
              <HasDocumentsOrFactsCondition
                parentType={PARENT_TYPES.BUSINESS}
                documentIds={[DOCUMENT_TYPES.NEWS]}
                showAnyway={user.cross_organization_access}
                showAnywayReason="User has cross organization access"
              >
                <CollapsiblePanel
                  sx={{ marginY: 2 }}
                  title={"News"}
                  defaultExpanded={false}
                  showHeaderDivider
                >
                  <NewModeCard hideDivider>
                    <NewsCard />
                  </NewModeCard>
                </CollapsiblePanel>
              </HasDocumentsOrFactsCondition>
            </EntityContext>

            <EntityContext
              parentType={PARENT_TYPES.BUSINESS}
              entity={entity}
              parentIds={businessParentIds}
            >
              <ModeSection>
                <CollapsiblePanel
                  sx={{ marginY: 2 }}
                  title={"Businesses at this Location"}
                  defaultExpanded={false}
                  showHeaderDivider
                >
                  <BusinessesAtThisLocation showEmpty={false} hideDivider />
                </CollapsiblePanel>
              </ModeSection>

              <ModeSection hideSidebarTitle>
                <CollapsiblePanel
                  sx={{ marginY: 2 }}
                  title={"Prometrix"}
                  defaultExpanded={true}
                  showHeaderDivider
                >
                  {report.canSeePrometrix() && <PrometrixCard hideDivider />}
                </CollapsiblePanel>
              </ModeSection>
            </EntityContext>

            <ModeSection hideSidebarTitle>
              <CollapsiblePanel
                sx={{ marginY: 2 }}
                title={"RiskMeter"}
                defaultExpanded={true}
                showHeaderDivider
              >
                {report.canSeeRiskmeter() && <RiskmeterCard />}
              </CollapsiblePanel>
            </ModeSection>
          </Box>
        </Box>
      </EntityContext>
    </PremisesViewerEntityContext.Provider>
  );
};
