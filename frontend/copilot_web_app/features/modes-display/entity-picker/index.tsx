import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { Box, Divider } from "@mui/material";
import { SearchAndFilters } from "@features/modes-display/entity-picker/components/SearchAndFilters";
import SubmissionBusiness from "@legacy/models/SubmissionBusiness";
import { MapType } from "@features/modes-display/entity-picker/components/Map";
import { trackPremisesViewerTabChanged } from "@utils/amplitude";
import { TabsWithMap } from "@features/modes-display/entity-picker/components/TabsWithMap";
import { ErsEntity } from "@legacy/models/ErsEntity";
import { EVENT_BUS, useEventBus } from "@features/events";
import { useMemoArray } from "@utils/useMemoArray";
import { SubmissionBusinessEntityRoleEnum } from "@legacy/api_clients/copilot_api_client";
import { useReport } from "@utils/useReport";
import { CollapsibleSection } from "@features/collapsible-section";
import { ModeSection } from "@features/modes-core/components/ModeSection";
import { ELEMENT_ID } from "@features/modes/jsx/constants";
import { EntityDetailsCard } from "@features/cards/entity-details";
import { TAB_DELIMITER } from "@features/modes-display/entity-picker/components/EntityPickerTab";
import { useEntityPickerBusinesses } from "@features/modes-display/entity-picker/hooks/useEntityPickerBusinesses";
import {
  useQueryParam,
  useSyncSectionQueryParam,
} from "@features/modes-display/entity-picker/query";
import { CapabilityMarker } from "@features/capabilities/Capability";
import { SIDEBAR_GROUP } from "@features/modes-core/constants";
import { NewPremisesDetails } from "@features/modes-display/entity-picker/components/NewPremisesDetails";

type EntityPickerProps = {
  title: string;
  entityRoleFilter?: [SubmissionBusinessEntityRoleEnum];
  children?: React.ReactNode;
  id?: string;
  naked?: boolean;
  businessIds?: string[];
  group?: SIDEBAR_GROUP;
  sidebarTitle?: string;
};

export const EntityPicker = ({
  entityRoleFilter,
  title,
  children,
  id = ELEMENT_ID.PREMISES_VIEWER,
  naked,
  businessIds,
  group,
  sidebarTitle,
}: EntityPickerProps) => {
  const [visibleBusinessIdsRaw, setVisibleBusinessIds] = useState<string[]>([]);
  const [mapType, setMapType] = useState<MapType>("map");

  // setVisibleBusinessIds state is getting updated too often with same values
  // due to that, we re-render tabs and map too often
  // useMemoArray will return old ref if array items has not changed
  const visibleBusinessIds = useMemoArray(visibleBusinessIdsRaw);

  useEffect(() => {
    EVENT_BUS.emit("ui/premises-viewer/mounted", null);
  }, []);

  const report = useReport()!;
  const businesses = useEntityPickerBusinesses(
    report,
    entityRoleFilter,
    businessIds
  );

  const businessQueryParam = useQueryParam(id, "business");
  const [selectedSubmissionBusinessId, setSelectedSubmissionBusinessId] =
    useState<string | undefined>(() => {
      if (
        !businessQueryParam ||
        !businesses.some((b) => b.id === businessQueryParam)
      )
        return undefined;
      return businessQueryParam;
    });

  useSyncSectionQueryParam(id, "business", selectedSubmissionBusinessId);

  useEffect(() => {
    if (
      ((naked && businesses?.length) || businesses?.length === 1) &&
      !selectedSubmissionBusinessId
    ) {
      setSelectedSubmissionBusinessId(businesses[0].id!);
    }
  }, [
    businesses,
    setSelectedSubmissionBusinessId,
    selectedSubmissionBusinessId,
    naked,
  ]);

  useEffect(() => {
    if (!selectedSubmissionBusinessId && mapType !== "map") {
      setMapType("map");
    }
  }, [mapType, selectedSubmissionBusinessId]);

  const selectedBusiness: SubmissionBusiness | undefined = useMemo(
    () => businesses?.find((b) => b.id === selectedSubmissionBusinessId),
    [businesses, selectedSubmissionBusinessId]
  );

  const selectedEntityInArray = useMemo(() => {
    return selectedBusiness
      ? [selectedBusiness.entity_data].filter((ed): ed is ErsEntity =>
          Boolean(ed)
        )
      : [];
  }, [selectedBusiness]);

  const visibleEntities: ErsEntity[] = useMemo(() => {
    return (
      businesses?.filter((b) => visibleBusinessIds.includes(b.id ?? "")) ?? []
    )
      .map((v) => v.entity_data)
      .filter((ed): ed is ErsEntity => Boolean(ed));
  }, [businesses, visibleBusinessIds]);

  useEffect(() => {
    if (visibleBusinessIds.length === 1 && !selectedSubmissionBusinessId) {
      setSelectedSubmissionBusinessId(visibleBusinessIds[0]);
    }
  }, [
    visibleBusinessIds,
    selectedSubmissionBusinessId,
    setSelectedSubmissionBusinessId,
    businesses.length,
  ]);

  const sectionId = id;
  useEventBus(
    () => {
      document
        .getElementById(sectionId)
        ?.scrollIntoView({ behavior: "smooth" });
    },
    [sectionId],
    [
      "ui/premises-viewer/tab-focus",
      "ui/premises-viewer/single-business-focus",
      "ui/premises-viewer/filter-picked",
    ]
  );

  useEventBus(
    ({ parentId }) => {
      if (parentId) {
        setSelectedSubmissionBusinessId(
          report.getBusinessById(parentId)?.id ?? undefined
        );
      }
    },
    [report],
    "ui/premises-viewer/tab-focus"
  );

  const onMapEntitySelected = useCallback(
    (entityId?: string) => {
      const visibleBusinesses: SubmissionBusiness[] =
        businesses?.filter((b) => visibleBusinessIds.includes(b.id ?? "")) ??
        [];
      const business = visibleBusinesses.find(
        (vb) => vb.entity_data?.id === entityId
      );
      if (business) {
        setSelectedSubmissionBusinessId(business.id ?? undefined);
      }
    },
    [businesses, visibleBusinessIds]
  );

  if (!businesses) return null;

  const onTabChange = (title?: string) => {
    const selectedRowTitle = title ?? "";
    trackPremisesViewerTabChanged(report, {
      tab: selectedRowTitle.split(TAB_DELIMITER)?.[0],
    });
  };

  const enableNewView = report.hasPropertyCoverage() && !naked;

  return (
    <ModeSection
      id={sectionId}
      title={title}
      type={!enableNewView ? "self-managed" : undefined}
      showEvenIfEmpty
      group={group}
      sidebarTitle={sidebarTitle}
    >
      <CapabilityMarker
        capabilities={[
          {
            type: "SUBMISSION_BUSINESS",
            anyIds: businesses.map((b) => b.id!),
            entityPickerId: sectionId,
          },
        ]}
      />
      {businesses.length === 1 && !naked && !enableNewView && (
        <EntityDetailsCard entity={businesses[0]} />
      )}
      <CollapsibleSection title={title} isCollapsible={false}>
        <Box
          sx={{
            border: undefined,
            mt: 2,
          }}
        >
          {businesses.length > 1 && (
            <SearchAndFilters
              allBusinesses={businesses}
              selectedSubmissionBusinessId={selectedSubmissionBusinessId}
              setSelectedSubmissionBusinessId={setSelectedSubmissionBusinessId}
              visibleBusinessIds={visibleBusinessIds}
              setVisibleBusinessIds={setVisibleBusinessIds}
              mapType={mapType}
              setMapType={setMapType}
              report={report}
              carrouselOnly={!!naked}
            />
          )}
          {!naked && <Divider />}
          <Box px={naked ? 0 : 2}>
            {enableNewView ? (
              <NewPremisesDetails
                business={selectedBusiness}
                mapEntities={
                  selectedBusiness ? selectedEntityInArray : visibleEntities
                }
                onEntitySelected={onMapEntitySelected}
                mapType={mapType}
              />
            ) : (
              <TabsWithMap
                sectionId={sectionId}
                entity={selectedBusiness?.entity_data}
                mapEntities={
                  selectedBusiness ? selectedEntityInArray : visibleEntities
                }
                mapType={mapType}
                onTabChange={onTabChange}
                tabsStyle={{
                  mt: 2,
                  mb: 3,
                }}
                onEntitySelected={onMapEntitySelected}
                listenToFocusEvents
                alwaysShowMap
                naked={naked}
              >
                {children}
              </TabsWithMap>
            )}
          </Box>
        </Box>
      </CollapsibleSection>
    </ModeSection>
  );
};
