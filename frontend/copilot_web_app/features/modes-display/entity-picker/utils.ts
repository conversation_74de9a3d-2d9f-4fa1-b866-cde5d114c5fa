export const mergeMapsWithSets = <K, V>(
  map1: Map<K, Set<V>>,
  map2: Map<K, Set<V>>
): Map<K, Set<V>> => {
  const mergedMap = new Map<K, Set<V>>(map1);

  for (const [key, set2] of map2.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, new Set(set2));
    } else {
      const set1 = mergedMap.get(key)!;
      for (const value of set2) {
        set1.add(value);
      }
    }
  }

  return mergedMap;
};
