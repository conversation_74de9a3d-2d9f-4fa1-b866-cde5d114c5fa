import React, { useMemo, useRef, useState } from "react";
import { EditorState, LexicalEditor } from "lexical";
import {
  Box,
  Chip,
  CircularProgress,
  MenuItem,
  SxProps,
  Typography,
  useTheme,
} from "@mui/material";
import { RichTextEditor } from "@ui-patterns/rich-text-editor";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { OnMentionNodeSelect } from "@ui-patterns/rich-text-editor/plugins/UserMentionPlugin";
import { Tooltip } from "@ui-patterns/tooltip";
import { SubmissionNote } from "@legacy/api_clients/copilot_api_client";
import { trackNoteDeleted, trackUrlOpened } from "@utils/amplitude";
import { useSnackbar } from "notistack";
import { useRemoveSubmissionNote } from "@queries/notes";
import Report from "@legacy/models/Report";
import { useOrganizationId, useUserId } from "@utils/auth";
import { useUsersByOrganizationQuery } from "@queries/user";
import { NoteFooterText } from "@features/notes/components/NoteFooterText";
import { RecommendationContextProvider } from "@features/recommendation/hooks/useRecommendationData";
import copilotApiClient from "@legacy/clients/copilotApiClient";

const NoteReferrals = ({ referredIds }: { referredIds: number[] }) => {
  const orgId = useOrganizationId();
  const { data: orgUsers } = useUsersByOrganizationQuery(orgId, false);
  const referredUserLabels = useMemo(() => {
    return referredIds
      .map((id) => {
        const user = orgUsers?.find((user) => user.id === id);
        return user ? `${user.name} (${user.email})` : null;
      })
      .filter(Boolean);
  }, [orgUsers, referredIds]);

  if (referredUserLabels.length < 1) {
    return null;
  }

  const joinedReferrals = referredUserLabels.join(", ");
  const message = `This submission was referred to ${joinedReferrals}`;
  return (
    <Box sx={{ width: "100%" }}>
      <Chip sx={{ mb: 1, maxHeight: "15px" }} size="small" label="Refer" />
      <Typography variant="body2" color="text.secondary">
        <strong>{message}</strong>
      </Typography>
    </Box>
  );
};

const NoteClosedReferrals = ({ userIds }: { userIds: number[] }) => {
  const orgId = useOrganizationId();
  const { data: orgUsers } = useUsersByOrganizationQuery(orgId, false);
  const labels = useMemo(() => {
    return userIds
      .map((id) => {
        const user = orgUsers?.find((user) => user.id === id);
        return user ? `${user.name} (${user.email})` : null;
      })
      .filter(Boolean);
  }, [orgUsers, userIds]);

  if (labels.length < 1) {
    return null;
  }

  const message = `This referral to ${labels.join(", ")} was marked as Closed`;
  return (
    <Box sx={{ width: "100%" }}>
      <Chip sx={{ mb: 1, maxHeight: "15px" }} size="small" label="Refer" />
      <Typography variant="body2" color="text.secondary">
        <strong>{message}</strong>
      </Typography>
    </Box>
  );
};

type NoteTooltipMenuProps = {
  onEditClick: () => void;
  onDeleteClick?: () => void;
  sx?: SxProps;
};
const NoteTooltipMenu = ({
  onEditClick,
  onDeleteClick,
  sx,
}: NoteTooltipMenuProps) => {
  return (
    <Box sx={{ py: 1, ...sx }}>
      <MenuItem onClick={onEditClick}>Edit</MenuItem>
      <MenuItem onClick={onDeleteClick} disabled={!onDeleteClick}>
        Delete
      </MenuItem>
    </Box>
  );
};

type Props = {
  report: Report;
  note?: SubmissionNote | null;
  afterDelete?: (noteId: string) => void;
  onMentionNodeSelect?: OnMentionNodeSelect;
  isBeingEdited?: boolean;
  type?: "note" | "message";
  onChange?: (editorState: EditorState, noteHtml: string) => void;
  onSave?: (editor: LexicalEditor) => void;
  canEdit?: boolean;
} & Omit<NoteTooltipMenuProps, "onDeleteClick">;

export const NotesDisplay = ({
  report,
  note,
  afterDelete,
  onEditClick,
  onMentionNodeSelect,
  isBeingEdited,
  type = "message",
  onChange,
  onSave,
  canEdit = true,
}: Props) => {
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  const editorStateRef = useRef<EditorState | null>(null);
  const [hovered, setHovered] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { mutateAsync: removeSubmissionNote } = useRemoveSubmissionNote();
  const userId = useUserId();

  const noteIsActive = hovered || isBeingEdited;

  const onDeleteClick = async () => {
    setIsDeleting(true);
    trackNoteDeleted(report, {});
    try {
      await removeSubmissionNote({
        submissionId: report.getSubmission().id,
        noteId: note!.id!,
      });
      afterDelete?.(note!.id!);
    } catch {
      enqueueSnackbar("Error deleting currentNote", { variant: "error" });
    } finally {
      setIsDeleting(false);
    }
  };

  const numReferredUsers = note?.referred_to_user_ids?.length ?? 0;
  const hasReferrals = numReferredUsers > 0;
  const hasReferralClosings =
    (note?.referrals_closed_to_user_ids?.length ?? 0) > 0;
  const editingIsDisabled = hasReferrals && userId !== note!.author_id;

  const isReviewFileClick = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>
  ) => {
    const target = e.target as HTMLElement;
    const parent = target.parentElement;

    return target.textContent === "Review File" && parent?.tagName === "A";
  };

  const downloadReviewFile = async (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>
  ) => {
    const target = e.target as HTMLElement;
    const parent = target.parentElement;
    const url = parent?.getAttribute("href");
    const searchParams = new URLSearchParams(url?.split("?")?.[1]);
    const s3Key = searchParams.get("s3_key");
    const name = searchParams.get("name");

    const res = await copilotApiClient.getS3FileUrl({
      reportId: report.id,
      s3Key: s3Key!,
      name: name!,
      eventName: "review file download",
    });

    window.open(res.url, "_blank");
  };

  const handleContentClick = (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>
  ) => {
    if (isBeingEdited || type !== "note") return;

    const anchor = (event.target as HTMLElement).closest("a");
    if (anchor && anchor instanceof HTMLAnchorElement) {
      event.preventDefault();
      window.open(anchor.href, "_blank");

      trackUrlOpened(report, {
        url: anchor.href,
        urlType: "note",
      });
      return;
    }

    onEditClick();
  };

  const editorCached = useMemo(() => {
    return (
      <RichTextEditor
        editable={canEdit && isBeingEdited && type === "note"}
        options={{ mentions: true }}
        onMentionNodeSelect={onMentionNodeSelect}
        editorStateRef={editorStateRef}
        initialState={note?.text}
        contentParentSx={{ padding: 0, pb: type === "note" ? 1 : 0 }}
        variant={type}
        toolbarPlacement={type === "note" && isBeingEdited ? "above" : "none"}
        onChange={
          onChange
            ? (_, noteHtml: string) =>
                onChange(editorStateRef.current!, noteHtml)
            : undefined
        }
        onSave={onSave}
      />
    );
    // We ignoring the exhaustive-deps rule here because we want to avoid the loss of editor carret state,
    // which is lost when editor gets rerendered, we can't wrap onChange into useCallback since we create an array of such components
    // And we don't really want to change initial state after initialization since editor update it for us
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [note?.id, isBeingEdited, type, onMentionNodeSelect, onSave]);

  return (
    <RecommendationContextProvider submissionId={report?.getSubmission().id}>
      <Box
        sx={{ my: 2 }}
        onContextMenu={(e) => {
          if (isReviewFileClick(e)) {
            e.preventDefault();
            e.stopPropagation();
          }
        }}
        onAuxClick={async (e) => {
          if (isReviewFileClick(e)) {
            e.preventDefault();
            await downloadReviewFile(e);
          }
        }}
        onClick={async (e) => {
          if (isReviewFileClick(e)) {
            e.preventDefault();
            await downloadReviewFile(e);
          }
        }}
      >
        <Box
          sx={{
            width: "90%",
            height: "100%",
            mx: "auto",
            borderRadius: 1,
            background:
              noteIsActive && type === "message"
                ? theme.colors.mainGrayBackground
                : "transparent",
          }}
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
        >
          <Box
            sx={{
              mx: 2,
              py: 1,
              display: "flex",
            }}
          >
            <Box
              display="flex"
              sx={{ width: "95%", maxWidth: "95%", flexDirection: "column" }}
            >
              {hasReferrals && (
                <NoteReferrals referredIds={note!.referred_to_user_ids!} />
              )}
              {hasReferralClosings && (
                <NoteClosedReferrals
                  userIds={note!.referrals_closed_to_user_ids!}
                />
              )}
              <Box onClick={handleContentClick}>{editorCached}</Box>
              {note && (type === "message" || !isBeingEdited) && (
                <NoteFooterText note={note} />
              )}
            </Box>
            {isDeleting ? (
              <CircularProgress size={20} />
            ) : hovered &&
              type === "message" &&
              (!note?.is_generated_note || note.is_editable) ? (
              <Tooltip
                title={
                  !editingIsDisabled ? (
                    <NoteTooltipMenu
                      onEditClick={onEditClick}
                      onDeleteClick={
                        note?.is_generated_note ? undefined : onDeleteClick
                      }
                    />
                  ) : undefined
                }
                styleOverrides={{
                  backgroundColor: theme.palette.background.paper,
                  border: `1px solid ${theme.colors.mainBlueContrast}`,
                  borderRadius: 8,
                  padding: 0,
                  width: 150,
                }}
              >
                <MoreVertIcon
                  color={editingIsDisabled ? "disabled" : undefined}
                  sx={{
                    cursor: editingIsDisabled ? "auto" : "pointer",
                    borderRadius: 1,
                    "&:hover": {
                      background: !editingIsDisabled
                        ? theme.palette.action.hover
                        : undefined,
                    },
                  }}
                />
              </Tooltip>
            ) : (
              <></>
            )}
          </Box>
        </Box>
      </Box>
    </RecommendationContextProvider>
  );
};
