import { Box, CircularProgress, Divider, useTheme } from "@mui/material";
import { useReportQuery } from "@queries/report";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { $getRoot, EditorState, LexicalEditor } from "lexical";
import debounce from "lodash/debounce";
import { useSnackbar } from "notistack";
import {
  trackNoteAdded,
  trackNoteEdited,
  trackReferred,
} from "@utils/amplitude";
import { RichTextEditor } from "@ui-patterns/rich-text-editor";
import { MentionNode } from "@ui-patterns/rich-text-editor/nodes/MentionNode";
import {
  useAddSubmissionNote,
  useGetSubmissionNotes,
  useUpdateSubmissionNote,
} from "@queries/notes";
import { SubmissionNote } from "@legacy/api_clients/copilot_api_client";
import { ShareReportDialog } from "./components/ShareReportDialog";
import { NotesDisplay } from "./components/NotesDisplay";
import { UserPermissionWarningContainer } from "./components/UserPermissionWarningContainer";
import { UserReferralSelect } from "./components/UserReferralSelect";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import {
  generateNotificationData,
  getMentionedUsersWithoutPermissions,
  userHasReportPermissions,
} from "@ui-patterns/rich-text-editor/util";
import { useUsersByOrganizationQuery } from "@queries/user";
import {
  useGetUsersSubmissionsNotificationsQuery,
  useUpsertUsersSubmissionsNotificationsMutation,
} from "@queries/submission";
import impersonationService from "@legacy/services/impersonationService";
import User from "@legacy/models/User";
import { useMemoArray } from "@utils/useMemoArray";
import dynamic from "next/dynamic";
import { sentryCaptureException } from "@legacy/services/sentryService";

type SavedNotification = { text: string; id?: string };

const NotesExportDynamic = dynamic(
  () =>
    import("@features/notes-view/NotesExport").then((mod) => mod.NotesExport),
  { ssr: false }
);

type Props = {
  reportId: string;
  isReferring?: boolean;
  mentionedUsers?: number[];
  prefilledText?: string;
};

export const Notes = ({
  reportId,
  prefilledText,
  mentionedUsers = [],
  isReferring = true,
}: Props) => {
  const theme = useTheme();
  const { data: report } = useReportQuery({ id: reportId });
  const currentUser = useCurrentUser();
  const { enqueueSnackbar } = useSnackbar();
  const [hasChanged, setHasChanged] = useState(false);
  const [isLoadedFromLocalStorage, setLoadedFromLocalStorage] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [referralIsActive, setReferralIsActive] = useState(isReferring);
  const [shouldClearState, setShouldClearState] = useState(false);
  const editorStateRef = useRef<EditorState | null>(null);
  const submission = report?.getSubmission();
  const [mentionNode, setMentionNode] = useState<MentionNode>();
  const [editor, setLexicalEditor] = useState<LexicalEditor>();
  const [dialogIsOpen, setDialogIsOpen] = useState(false);
  const { mutateAsync: updateSubmissionNote } = useUpdateSubmissionNote();
  const { mutateAsync: addSubmissionNote } = useAddSubmissionNote();
  const [currentNote, setCurrentNote] = useState<SubmissionNote>({
    text: prefilledText,
  });
  const disableEditing = !report?.hasEditPermissions();

  const [noteIdForDialog, setNoteIdForDialog] = useState<string | undefined>();
  const [usersMentionedDuringEdit, setUsersMentionedDuringEdit] =
    useState<number[]>(mentionedUsers);
  const [usersToRefer, setUsersToRefer] = useState<number[]>();
  const { data: notes, isLoading: isLoadingNotes } = useGetSubmissionNotes(
    submission?.id
  );

  const { data: notificationsCount } = useGetUsersSubmissionsNotificationsQuery(
    report?.submission?.id ?? ""
  );
  const { mutateAsync: updateNotificationsCount } =
    useUpsertUsersSubmissionsNotificationsMutation(
      report?.submission?.id ?? ""
    );

  const isImpersonating = !!impersonationService.getUserToImpersonate();

  const orgId = useOrganizationId();
  const { data: users, isLoading: isLoadingUsers } =
    useUsersByOrganizationQuery(orgId, false, ["role"]);
  const reportPermissions = useMemoArray(report?.permissions ?? []);

  // Local storage key for the editor state
  const localStorageKey = `notes-editor-state-report-${reportId}-v2`;

  const reportPermissionGranteeIds = useMemo(() => {
    return new Set(
      reportPermissions
        .map((permission) => permission.grantee_user_id)
        .filter(Boolean)
    ) as Set<number>;
  }, [reportPermissions]);

  const reportOrganizationPermissionLevel =
    report?.organization_permission_level;

  const [dismissedWarningUsers, setDismissedWarningUsers] = useState<
    Set<number>
  >(new Set());
  const [warningUsers, setWarningUsers] = useState<User[]>([]);

  const onUserPermissionWarningDismissed = (user: User) => {
    setWarningUsers((prevState) => prevState.filter((u) => u.id !== user.id));
    setDismissedWarningUsers((prevState) => {
      const newSet = new Set(prevState);
      newSet.add(user.id);
      return newSet;
    });
  };

  const contentRef = useRef(null);

  // Hook executed on mount to load the current note state from local storage
  useEffect(() => {
    try {
      const currentState = JSON.parse(
        localStorage.getItem(localStorageKey)!
      ) as SavedNotification | null;

      if (currentState) {
        setCurrentNote({
          text: currentState.text,
          id: currentState.id,
        });
        setLoadedFromLocalStorage(true);
      }
    } catch (e) {
      sentryCaptureException(e);
    }
  }, [localStorageKey]);

  useEffect(() => {
    if (
      !isLoadingNotes &&
      !!notes &&
      !isLoadingUsers &&
      !!users &&
      !!reportPermissionGranteeIds
    ) {
      const warningUsers = getMentionedUsersWithoutPermissions({
        notes: notes,
        orgUsers: users,
        reportPermissionGranteeIds: reportPermissionGranteeIds,
        reportOrganizationPermissionLevel: reportOrganizationPermissionLevel,
      });
      setWarningUsers(
        warningUsers.filter((u) => !dismissedWarningUsers.has(u.id))
      );
    }
  }, [
    notes,
    isLoadingNotes,
    users,
    isLoadingUsers,
    reportPermissionGranteeIds,
    reportOrganizationPermissionLevel,
    dismissedWarningUsers,
  ]);

  useEffect(() => {
    if (!notificationsCount || isImpersonating) return;

    const hadUnread =
      notificationsCount.seen_notes! < notificationsCount.notes_count!;

    if (hadUnread) {
      updateNotificationsCount({ seen_notes: notificationsCount.notes_count });
    }
  }, [updateNotificationsCount, notificationsCount, isImpersonating]);

  const isEditingExistingNote = !!currentNote.id;

  const clearEditorState = useCallback(() => {
    setCurrentNote({ text: "" });
    setShouldClearState(true);
    setUsersMentionedDuringEdit([]);
    setUsersToRefer(undefined);
    setReferralIsActive(false);

    // Clear the editor state from local storage
    localStorage.removeItem(localStorageKey);
    setLoadedFromLocalStorage(false);
  }, [localStorageKey]);

  const onStateClear = () => {
    setShouldClearState(false);
  };

  const afterNoteDelete = (noteId: string) => {
    if (currentNote.id === noteId) {
      clearEditorState();
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onChangeHandler = useCallback(
    debounce(() => {
      if (editorStateRef.current) {
        const text = editorStateRef.current.read(() =>
          $getRoot().getTextContent()
        );
        if (!text && !submission?.notes) {
          setHasChanged(false);
          localStorage.removeItem(localStorageKey);
        } else {
          // Format the current editor state to a string
          const contentString = JSON.stringify(editorStateRef.current.toJSON());

          // Save state to local storage
          localStorage.setItem(
            localStorageKey,
            JSON.stringify({ text: contentString, id: currentNote.id })
          );

          if (isLoadedFromLocalStorage) {
            // If the content was loaded from local storage, it means there is a pending change
            setHasChanged(true);
          } else {
            // Otherwise, compare the current note with the editor state
            setHasChanged((currentNote?.text ?? "") !== contentString);
          }
        }
      }
    }, 250),
    [currentNote.text, currentNote.id, setHasChanged, hasChanged]
  );

  const onSave = useCallback(
    async (editor: LexicalEditor) => {
      if (submission && report && editor && currentNote) {
        const { usersToNotify, noteHtml } = generateNotificationData({
          editor: editor,
          usersMentionedDuringEdit: usersMentionedDuringEdit,
          reportName: report.name,
          currentUserEmail: currentUser.email,
        });
        if (isEditingExistingNote) {
          trackNoteEdited(report, {
            mentionsUsed: usersToNotify.length > 0,
            type: "note",
          });
        } else {
          trackNoteAdded(report, {
            mentionsUsed: usersToNotify.length > 0,
            type: "note",
          });
        }

        setIsSaving(true);
        try {
          const noteText = JSON.stringify(editor.getEditorState().toJSON());
          isEditingExistingNote
            ? await updateSubmissionNote({
                noteId: currentNote!.id!,
                text: noteText,
                submissionId: submission.id,
                noteHtml: noteHtml,
                usersToNotify: usersToNotify,
              })
            : await addSubmissionNote({
                text: noteText,
                submissionId: submission.id,
                noteHtml: noteHtml,
                usersToNotify: usersToNotify,
                usersToRefer: usersToRefer,
              });
          if (referralIsActive) {
            trackReferred(report, {
              users: (usersToRefer ?? [])
                .map(
                  (u) =>
                    (users ?? []).find((user) => user.id === u)?.email ?? ""
                )
                .join(", "),
            });
          }
          clearEditorState();
        } catch (ex) {
          sentryCaptureException(ex);
          enqueueSnackbar("Error updating notes", { variant: "error" });
        } finally {
          setIsSaving(false);
        }
      }
    },
    [
      submission,
      report,
      enqueueSnackbar,
      updateSubmissionNote,
      addSubmissionNote,
      setIsSaving,
      currentNote,
      currentUser,
      isEditingExistingNote,
      usersMentionedDuringEdit,
      usersToRefer,
      referralIsActive,
      users,
      clearEditorState,
    ]
  );

  const currentUserIsReportEditor = report?.hasEditPermissions();

  const onMentionNodeSelect = useCallback(
    ({
      lexicalEditor,
      node,
    }: {
      lexicalEditor: LexicalEditor;
      node: MentionNode;
    }) => {
      const user = users?.find((user) => user.id == node.getUserId());
      const hasReportPermissions = userHasReportPermissions({
        user,
        reportPermissionGranteeIds,
        reportOrganizationPermissionLevel,
      });

      if (currentUserIsReportEditor && !hasReportPermissions) {
        setMentionNode(node);
        setDialogIsOpen(true);
        setLexicalEditor(lexicalEditor);
      } else if (currentUserIsReportEditor) {
        setUsersMentionedDuringEdit((prevState) => [
          ...prevState,
          node.getUserId(),
        ]);
      }
    },
    [
      setMentionNode,
      setLexicalEditor,
      setDialogIsOpen,
      users,
      reportPermissionGranteeIds,
      reportOrganizationPermissionLevel,
      currentUserIsReportEditor,
    ]
  );

  const onNoteEdit = (selectedNote: SubmissionNote) => {
    setCurrentNote(selectedNote);
  };

  if (!report || !submission) {
    return null;
  }
  const disableSaveButton =
    (!hasChanged && !referralIsActive) ||
    (referralIsActive && !usersToRefer?.length);

  return (
    <Box
      style={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
      }}
    >
      <Box
        display="flex"
        justifyContent="flex-end"
        width={"100%"}
        pr={2}
        pt={1}
      >
        <NotesExportDynamic contentRef={contentRef} report={report} />
      </Box>
      {isLoadingNotes && (
        <Box sx={{ display: "flex", justifyContent: "center", width: "100%" }}>
          <CircularProgress />
        </Box>
      )}
      {!isLoadingNotes && (
        <>
          <div
            ref={contentRef}
            style={{
              height: "40vh",
              overflow: "auto",
              flexGrow: 1,
            }}
          >
            {!notes && (
              <Box sx={{ color: theme.colors.secondaryGrayText, mx: 2 }}>
                No notes added.
              </Box>
            )}
            {warningUsers.length > 0 && (
              <UserPermissionWarningContainer
                users={warningUsers}
                reportId={reportId}
                currentUserIsReportEditor={currentUserIsReportEditor ?? false}
                onUserPermissionWarningDismissed={
                  onUserPermissionWarningDismissed
                }
              />
            )}
            {notes?.map((note) => (
              <NotesDisplay
                key={note.id}
                isBeingEdited={currentNote.id === note.id}
                report={report}
                note={note}
                afterDelete={afterNoteDelete}
                onEditClick={() => onNoteEdit(note)}
                onMentionNodeSelect={(onMentionNodeSelectProps) => {
                  setNoteIdForDialog(note.id);
                  onMentionNodeSelect(onMentionNodeSelectProps);
                }}
                canEdit={!disableEditing}
              />
            ))}
          </div>
          <Divider />
          <Box
            display={disableEditing ? "none" : "flex"}
            sx={{
              background: theme.colors.mainGrayBackground,
              width: "100%",
              maxHeight: "50%",
            }}
          >
            <div
              style={{
                width: "90%",
                marginLeft: "auto",
                marginRight: "auto",
                height: "100%",
              }}
            >
              <RichTextEditor
                editable
                options={{
                  mentions: true,
                }}
                placeholder={
                  referralIsActive
                    ? "Add an optional message to this referral"
                    : undefined
                }
                onMentionNodeSelect={(onMentionNodeSelectProps) => {
                  setNoteIdForDialog(currentNote.id);
                  onMentionNodeSelect(onMentionNodeSelectProps);
                }}
                editorStateRef={editorStateRef}
                initialState={currentNote.text}
                onChange={onChangeHandler}
                enableSaveFunctionality
                disableSaveButton={disableSaveButton}
                shouldClearState={shouldClearState}
                onStateClear={onStateClear}
                saveActionStyle={isEditingExistingNote ? "button" : "icon"}
                customActionIsActive={referralIsActive}
                onCustomAction={() => {
                  setReferralIsActive((isActive) => !isActive);
                  setUsersToRefer(undefined);
                }}
                customActionText={isEditingExistingNote ? "" : "Refer"}
                onSave={onSave}
                isLoading={isSaving}
                toolbarPlacement="above"
                wrapperSx={{
                  maxHeight: "30vh",
                  overflow: "auto",
                  position: "relative",
                  marginBottom: 2,
                }}
                contentParentSx={{
                  display: "flex",
                  justifyContent: "space-between",
                  background: theme.palette.background.paper,
                }}
                toolbarSx={{ height: "65px" }}
              >
                {referralIsActive && (
                  <Box mb={1}>
                    <UserReferralSelect setSelectedUserIds={setUsersToRefer} />
                  </Box>
                )}
              </RichTextEditor>
            </div>
          </Box>
          {dialogIsOpen && (
            <ShareReportDialog
              isEditingNote={noteIdForDialog === currentNote.id}
              editor={editor}
              reportId={reportId}
              mentionNode={mentionNode}
              onClose={() => {
                setDialogIsOpen(false);
                setNoteIdForDialog(undefined);
              }}
              onShareSuccess={(currentText, mentionedUserId) => {
                if (noteIdForDialog === currentNote.id) {
                  setCurrentNote({ ...currentNote, text: currentText });
                }

                setUsersMentionedDuringEdit((prevState) => [
                  ...prevState,
                  mentionedUserId,
                ]);
              }}
            />
          )}
        </>
      )}
    </Box>
  );
};
