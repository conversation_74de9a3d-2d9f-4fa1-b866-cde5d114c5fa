import { ReportExportDownloadButton } from "@features/files/components/ReportExportDownloadButton";
import { GetHazardHubDataButton } from "@features/hazard-hub";
import { DisplayNotesIcon } from "@features/notes/components/DisplayNotesIcon";
import { BookmarkButton } from "@features/report-list/components/BookmarkButton";
import { IntegrationErrorBanner } from "@features/submission-overview/components/IntegrationErrorBanner";
import { LinkButton } from "@features/submission-overview/components/LinkButton";
import { RetryIMSButton } from "@features/submission-overview/components/RetryIMSButton";
import { ORGANIZATION } from "@utils/constants";
import {
  formatLocalDate,
  getBusinessName,
} from "@legacy/helpers/reportHelpers";
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  ClickAwayListener,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { useUpdateReport } from "@queries/report";
import { theme } from "@ui-patterns/theme";
import { formatCurrency } from "@utils/text";
import sum from "lodash/sum";
import dynamic from "next/dynamic";
import React, { useEffect, useMemo, type JSX } from "react";
import Report from "../../../src/models/Report";
import { DebugButton } from "./DebugButton";
import { LastRecommendationChip } from "@features/submission-overview/components/LastRecommendationChip";
import { Pin } from "@ui-patterns/icons/pin";
import { RenewalIcon } from "@ui-patterns/renewal-icon";
import { RushIcon } from "@ui-patterns/rush-icon";
import { useCurrentUser } from "@utils/auth";
import { BrokerageContactModal } from "@features/agents-modal/BrokerageContactModal";
import { useUserSessionPreferencesState } from "@features/modes-core/hooks/useUserSessionPreferences";
import { DisplayEmailsIcon } from "@features/modes/jsx/reusables/DisplayEmailsIcon";
import { trackEmailInteraction } from "@utils/amplitude";
import { useGetUsersSubmissionsNotificationsQuery } from "@queries/submission";
import { ActionsHorizMenu } from "@ui-patterns/actions-horiz-menu";
import { useRouter } from "next/router";
import { useIsNWVerifiedShell } from "@legacy/hooks/useIsNWVerifiedShell";
import { useIsDocumentIngestion } from "@features/files/hooks/useIsDocumentIngestion";
import { useSubmissionBladesStore } from "@features/blades/submissionBladesStore";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { CopyTextButton } from "@features/submission-overview/components/CopyTextButton";
import { SubmissionClientId } from "@features/submission-overview/components/SubmissionClientId";
import { HashTag } from "@ui-patterns/icons/hash-tag";
import { useRestartProcessing } from "@features/product-driven-support/submission-processing/utils";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { RecommendedActionButton } from "@features/report-list/components/new-report-list/RecommendedActionButton";
import { RowWithIcon } from "@features/submission-overview/components/RowWithIcon";
import { OutlinedMail } from "@ui-patterns/icons/outlined-mail";
import { OutlinedPremium } from "@ui-patterns/icons/outlined-premium";
import { OutlinedCalendar } from "@ui-patterns/icons/outlined-calendar";
import { Broker } from "@ui-patterns/icons/broker";
import { SubmissionReprocessingBanner } from "@features/submission-overview/components/SubmissionReprocessingBanner";
import { OutlinedUser } from "@ui-patterns/icons/outlined-user";
import { useAssigneesQuery } from "@queries/assignees";
import { Dialog } from "@ui-patterns/dialog";
import { AssignedUnderwriter } from "@legacy/api_clients/copilot_api_client";
import { enqueueSnackbar } from "notistack";
import { useAssignedUnderwriter } from "@features/submission-information-section/hooks/useAssignedUnderwriter";
import { ConfirmAllFilesButton } from "@features/all-files/components/ConfirmAllFilesButton";
import { useParagonVerifiedShell } from "@legacy/hooks/useParagonVerifiedShell";

type Assignee = {
  value: number;
  label: string;
  name: string;
  email: string;
};

const ChangeStatusDynamic = dynamic(() =>
  import("@features/submission-overview/components/ChangeStatus").then(
    (mod) => mod.ChangeStatus
  )
);

type SubmissionHeaderProps = {
  report: Report;
  hideActions?: boolean;
};

const LabelValueCell = ({
  children,
  label,
  value,
  mr = 2,
}: {
  children?: JSX.Element;
  label?: string;
  value?: string;
  mr?: number;
}) => {
  const item = children ? (
    children
  ) : (
    <>
      {label}: {value}
    </>
  );

  return (
    <Typography variant="body2" sx={{ textWrap: "nowrap" }} mr={mr}>
      {item}
    </Typography>
  );
};

const DateCell = ({ label, date }: { label: string; date?: Date | null }) => {
  const value = date ? formatLocalDate(date, undefined, "MM/dd/yyyy") : "-";
  return <LabelValueCell label={label} value={value} />;
};

const getBrokerName = (report: Report): string => {
  const broker = report.getBroker()?.name;
  return broker ?? "-";
};

export const SubmissionHeader = ({
  report,
  hideActions = false,
}: SubmissionHeaderProps): JSX.Element | null => {
  const isBowheadReport = report.organization_id === 54;
  const isNWReport = report.organization_id === 6;
  const { mutate: updateReport, isLoading } = useUpdateReport();
  const [reportName, setReportName] = React.useState(report.name);
  const [isEditingBrokerage, setIsEditingBrokerage] = React.useState(false);
  const [isEditingAssignee, setIsEditingAssignee] = React.useState(false);
  const submission = report.getSubmission();
  const [currentValues, setCurrentValues] = React.useState(
    submission.assigned_underwriters ?? []
  );
  const user = useCurrentUser();
  const brokerageId = report.getBrokerage()?.id;
  const isDocumentIngestion = useIsDocumentIngestion();
  const { showEmailPreview, setShowEmailPreview } =
    useUserSessionPreferencesState();
  const { data: notifications } = useGetUsersSubmissionsNotificationsQuery(
    report.submission.id
  );
  const router = useRouter();
  const isNWVerifiedShell = useIsNWVerifiedShell(report);
  const {
    isParagonVerifiedShell,
    isBeingProcessed: isParagonVerifiedShellProcessed,
    message: paragonVerifiedShellMessage,
  } = useParagonVerifiedShell(report);
  const restartFileProcessing = useRestartProcessing(report.id);
  const canEditBrokerage: boolean = brokerageId ? !isNWVerifiedShell : false;
  const canEditAssignee =
    report?.hasEditPermissions() &&
    !report.isFrozen() &&
    report.isAssignedUnderwriterEditable(user);
  const { isCSManager } = useAccessPermissions();
  const isProjectSubmission = report.isProjectSubmission();
  const { brokerageOffice } = useFeatureFlags();

  const clientIds =
    report
      .getSubmission()
      ?.client_submission_ids?.map((c) => c.client_submission_id) ?? [];

  const emailPreviewAvailable =
    router.asPath.includes("overview") ||
    router.asPath.includes("all-files") ||
    router.asPath.includes("premises-viewer") ||
    router.asPath.includes("first-named-insured") ||
    router.asPath.includes("general-contractor") ||
    router.asPath.includes("loss-runs") ||
    router.asPath.includes("legal-filings") ||
    router.asPath.includes("news") ||
    router.asPath.includes("fleet") ||
    router.asPath.includes("equipment") ||
    router.asPath.includes("renewals") ||
    router.asPath.includes("exposures-breakdown") ||
    router.asPath.includes("project") ||
    router.asPath.includes("financials") ||
    router.asPath.includes("recalls") ||
    router.asPath.includes("warning-letters") ||
    router.asPath.includes("violations") ||
    router.asPath.includes("management-liability") ||
    router.asPath.includes("workers-compensation") ||
    router.asPath.includes("submission-information");

  const showEmails = () => {
    if (!showEmailPreview) {
      trackEmailInteraction(report, {
        action: "show email clicked",
        notificationsCount: notifications,
      });
      setShowEmailPreview(true);
    }
  };

  const { open: openBlade } = useSubmissionBladesStore();

  useEffect(() => {
    setReportName(report.name);
  }, [report]);

  const handleClickAway = () => {
    saveNewReportName(reportName);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.stopPropagation();
      saveNewReportName(reportName);
    }
  };

  const saveNewReportName = (value: string) => {
    const previousReportName = getBusinessName(report).toUpperCase();
    if (value && value.toUpperCase() !== previousReportName) {
      updateReport({
        id: report.id,
        updateReportRequest: {
          name: value,
        },
      });
    }
  };

  const editable =
    report.hasEditPermissions() &&
    !isLoading &&
    (!isNWReport || isCSManager || isProjectSubmission);
  const primaryInsureds = report.getPrimaryInsuredOrFirst() ?? [];
  const primaryInsured = primaryInsureds.length ? primaryInsureds[0] : null;

  const { data: users, isLoading: isLoadingUsers } = useAssigneesQuery();

  const availableAssigneeOptions: Assignee[] = useMemo(
    () =>
      (users ?? []).map((uw) => ({
        value: uw.id,
        label: `${uw.name} (${uw.email})`,
        email: uw.email,
        name: uw.name,
      })) ?? [],
    [users]
  );

  const allAssignees = useMemo(() => {
    return currentValues.map((value) => {
      const isDisabled =
        !isLoadingUsers &&
        !availableAssigneeOptions?.some((option) => option.value === value.id);
      const label = `${value.name} ${isDisabled ? "(Disabled User) " : ""}(${
        value.email
      })`;
      return {
        value: value.id,
        label,
        name: value.name,
        email: value.email,
      };
    });
  }, [currentValues, availableAssigneeOptions, isLoadingUsers]);

  const assigneeOptionsSelectedByModal = useMemo(() => {
    return availableAssigneeOptions.filter((option) =>
      currentValues.some((currentValue) => currentValue.id === option.value)
    );
  }, [currentValues, availableAssigneeOptions]);

  const handleChange = (assignees: Assignee[]) => {
    const assignedUnderwriters: AssignedUnderwriter[] = assignees.map(
      (assignee) => ({
        id: assignee.value,
        name: assignee.name,
        email: assignee.email,
        source: "manual",
      })
    );
    setCurrentValues(assignedUnderwriters);
  };

  const assignee = useMemo(() => {
    if (!allAssignees || allAssignees.length === 0) return "-";
    const firstAssignee = allAssignees[0];
    const isDisabled =
      !isLoadingUsers &&
      !users?.some((option) => option.id === firstAssignee.value);
    return `${firstAssignee.name ?? firstAssignee.email ?? "-"} ${
      isDisabled ? "(Disabled User) " : ""
    }`;
  }, [allAssignees, isLoadingUsers, users]);

  const assigneesTooltipTitle = useMemo(() => {
    if (!allAssignees || allAssignees.length === 0) {
      return null;
    }

    return allAssignees.map(({ value, name, email }, index) => {
      const isDisabled =
        !isLoadingUsers && !users?.some((option) => option.id === value);
      const display =
        name === email
          ? `${name} ${isDisabled ? "(Disabled User)" : ""}`
          : `${name} ${isDisabled ? "(Disabled User) " : ""}(${email})`;

      return (
        <React.Fragment key={email}>
          {index > 0 && <br />}
          {display}
        </React.Fragment>
      );
    });
  }, [allAssignees, isLoadingUsers, users]);

  if (!submission) {
    return null;
  }

  const effectiveDate = report.getProposedEffectiveDate();
  const premium =
    submission.target_premium ?? submission.expired_premium ?? null;

  const address = primaryInsured?.address
    ? `${primaryInsured?.getAddress()}`
    : null;

  const displayedPremium = (() => {
    function getSum() {
      if (submission.stage === "QUOTED_BOUND") {
        return {
          label: "Total Premium",
          value: sum(submission.coverages.map((x) => x.total_premium)),
        };
      } else if (
        submission.stage === "QUOTED_LOST" ||
        submission.stage === "QUOTED"
      ) {
        return {
          label: "Quoted Premium",
          value: sum(submission.coverages.map((x) => x.quoted_premium)),
        };
      } else {
        return {
          label: "Estimated Premium",
          value: sum(submission.coverages.map((x) => x.estimated_premium)),
        };
      }
    }

    const summed = getSum();
    if (!summed.value) return null;

    return (
      <RowWithIcon IconComponent={OutlinedPremium}>
        <Box display={"flex"} alignItems={"center"}>
          <LabelValueCell
            label={summed.label}
            value={formatCurrency(summed.value)}
          />
        </Box>
      </RowWithIcon>
    );
  })();

  const brokerage = report.getBrokerage()?.name;

  const completeProcessingAction = isParagonVerifiedShellProcessed ? null : (
    <Button
      size="small"
      onClick={() => {
        restartFileProcessing();
      }}
    >
      Complete processing
    </Button>
  );

  return (
    <Box>
      <SubmissionReprocessingBanner report={report} />

      {report.organization_id === ORGANIZATION.CONIFER && (
        <IntegrationErrorBanner report={report} />
      )}

      {isParagonVerifiedShell && (
        <Alert
          severity="warning"
          sx={{ mx: 2, my: 1 }}
          action={completeProcessingAction}
        >
          {paragonVerifiedShellMessage}
        </Alert>
      )}

      {isNWVerifiedShell && (
        <Alert severity="warning" sx={{ mx: 2, my: 1 }}>
          This submission is currently being processed, limited information is
          available
        </Alert>
      )}
      <Box
        sx={{
          px: 3,
          pb: "8px",
          pt: "8px",
          display: "flex",
          justifyContent: "space-between",
          "& .linkButton": {
            opacity: 0,
            transition: "opacity 0.2s",
          },
          "&:hover .linkButton": {
            opacity: 1,
          },
          borderBottom: `1px solid ${theme.palette.grey[300]}`,
          maxHeight: 100,
        }}
      >
        <Box display="flex" width={"100%"}>
          <Box
            display="flex"
            justifyContent="center"
            flexDirection="column"
            width={"100%"}
            mr={1}
          >
            <Box
              display="flex"
              alignItems="flex-end"
              justifyContent={"space-between"}
              pb={1}
            >
              <Box display="flex" alignItems="flex-end">
                <ClickAwayListener onClickAway={handleClickAway}>
                  <Typography
                    variant="subtitle1"
                    pr={2}
                    textOverflow="ellipsis"
                    whiteSpace="nowrap"
                    maxWidth={"460px"}
                    overflow="hidden"
                    contentEditable={editable}
                    suppressContentEditableWarning
                    component="p"
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setReportName(e.target.innerText)
                    }
                    onKeyPress={handleKeyDown}
                  >
                    {report.name}
                  </Typography>
                </ClickAwayListener>
                {report?.is_rush && (
                  <RushIcon
                    withLabel
                    onClick={() => {
                      openBlade({
                        containerId: "report-wrapper",
                        items: [{ type: "rushSubmissionEvidence" }],
                        reportId: report.id,
                      });
                    }}
                  />
                )}
                {report?.isRenewal() && <RenewalIcon withLabel />}
                <BookmarkButton report={report} sx={{ p: 0, mx: 0 }} />
                <DebugButton />
                <LinkButton className="linkButton" report={report} />
                <CopyTextButton
                  text={report.name}
                  notifyText={"Submission name was copied to clipboard."}
                  tooltip={"Copy Submission name"}
                />
              </Box>
              {!hideActions && (
                <Stack direction="row" spacing={1} alignItems="center">
                  {isBowheadReport && (
                    <ReportExportDownloadButton report={report} />
                  )}
                  <RetryIMSButton report={report} />
                  <LastRecommendationChip />
                  {emailPreviewAvailable && (
                    <DisplayEmailsIcon report={report} onClick={showEmails} />
                  )}
                  <DisplayNotesIcon report={report} />
                  <GetHazardHubDataButton report={report} />
                  <ChangeStatusDynamic report={report} />
                  <RecommendedActionButton report={report} />
                  {isDocumentIngestion && <ConfirmAllFilesButton />}
                  <ActionsHorizMenu report={report} />
                </Stack>
              )}
            </Box>
            {!isDocumentIngestion && (
              <Stack
                display="flex"
                alignItems="center"
                direction="row"
                flexWrap="wrap"
              >
                <RowWithIcon IconComponent={OutlinedCalendar} center>
                  <DateCell label="Effective" date={effectiveDate} />
                </RowWithIcon>

                {primaryInsured && address && (
                  <Stack
                    display="flex"
                    direction="row"
                    spacing={0.1}
                    alignItems="center"
                  >
                    <RowWithIcon
                      IconComponent={
                        primaryInsured.isAddressMailingAddress(address)
                          ? OutlinedMail
                          : Pin
                      }
                      center
                    >
                      <Tooltip title={address}>
                        <Typography
                          variant="body2"
                          sx={{
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                            whiteSpace: "nowrap",
                            maxHeight: 20,
                          }}
                        >
                          {address}
                        </Typography>
                      </Tooltip>
                    </RowWithIcon>

                    <CopyTextButton
                      text={address}
                      notifyText={"Address was copied to clipboard."}
                      tooltip={"Copy Address"}
                    />
                  </Stack>
                )}

                <Tooltip
                  title={brokerage && "Brokerage / Agency: " + brokerage}
                >
                  <Box
                    sx={{ cursor: canEditBrokerage ? "pointer" : "default" }}
                    display={"flex"}
                    alignItems={"center"}
                    onClick={() => {
                      setIsEditingBrokerage(canEditBrokerage);
                    }}
                  >
                    <RowWithIcon IconComponent={Broker} center>
                      <LabelValueCell
                        label={user.getBrokerLabel()}
                        value={getBrokerName(report)}
                      />
                    </RowWithIcon>
                  </Box>
                </Tooltip>

                {premium && (
                  <RowWithIcon IconComponent={OutlinedPremium} center>
                    <LabelValueCell
                      label="Target Premium"
                      value={formatCurrency(premium)}
                    />
                  </RowWithIcon>
                )}

                {displayedPremium}

                {brokerageOffice && (
                  <SubmissionClientId
                    report={report}
                    showOnlyIfPopulated
                    withRowContainer
                  />
                )}

                {isNWVerifiedShell && (
                  <RowWithIcon IconComponent={HashTag} center>
                    <LabelValueCell
                      label={"Submission ID"}
                      value={clientIds.join(", ")}
                    />
                  </RowWithIcon>
                )}

                <Tooltip
                  title={assigneesTooltipTitle}
                  componentsProps={{
                    tooltip: {
                      sx: {
                        maxWidth: "none",
                        whiteSpace: "nowrap",
                      },
                    },
                  }}
                >
                  <Box
                    sx={{ cursor: canEditAssignee ? "pointer" : "default" }}
                    display={"flex"}
                    alignItems={"center"}
                    onClick={() => {
                      setIsEditingAssignee(canEditAssignee);
                    }}
                  >
                    <RowWithIcon IconComponent={OutlinedUser} center>
                      <LabelValueCell
                        label={"Assignee"}
                        value={assignee ?? "-"}
                      />
                    </RowWithIcon>
                  </Box>
                </Tooltip>
              </Stack>
            )}
          </Box>
        </Box>

        {isEditingBrokerage && brokerageId && (
          <BrokerageContactModal
            onClose={() => setIsEditingBrokerage(false)}
            report={report}
            brokerageId={brokerageId}
          />
        )}
        {isEditingAssignee && (
          <AssigneeModal
            onClose={() => setIsEditingAssignee(false)}
            availableOptions={availableAssigneeOptions}
            optionsSelectedByModal={assigneeOptionsSelectedByModal}
            handleChange={handleChange}
          />
        )}
      </Box>
    </Box>
  );
};

type AssigneeModalProps = {
  onClose: () => void;
  availableOptions: Assignee[];
  optionsSelectedByModal: Assignee[];
  handleChange: (assignees: Assignee[]) => void;
};

const AssigneeModal = ({
  onClose,
  availableOptions,
  optionsSelectedByModal,
  handleChange,
}: AssigneeModalProps) => {
  const { onSubmit } = useAssignedUnderwriter();
  const [updating, setUpdating] = React.useState(false);

  const saveData = async () => {
    const uwToSave: AssignedUnderwriter[] = optionsSelectedByModal.map(
      (option) => ({
        id: option.value,
        name: option.name,
        email: option.email,
        source: "manual",
      })
    );
    try {
      setUpdating(true);
      await Promise.all([onSubmit(uwToSave)]);
      enqueueSnackbar("Assigned underwriters updated", { variant: "success" });
      setUpdating(false);
      onClose();
    } catch (e: any) {
      enqueueSnackbar(
        e?.response?.data?.detail ?? "Error updating submission underwriters",
        {
          variant: "error",
        }
      );
      setUpdating(false);
    }
  };

  return (
    <>
      <Dialog
        title="Select Assigned Underwriters"
        maxWidth="xl"
        primaryActionText="Save"
        onPrimaryAction={saveData}
        isLoading={updating}
        rawContent={
          <Box sx={{ width: 600, px: 2 }}>
            <Autocomplete
              options={availableOptions ?? []}
              onChange={(_e, option) => handleChange(option)}
              value={optionsSelectedByModal}
              multiple
              sx={{
                width: "auto",
                "& .MuiAutocomplete-inputRoot": {
                  flexDirection: "column",
                  alignItems: "flex-start",
                  "& .MuiAutocomplete-input": {
                    minWidth: "200px",
                    whiteSpace: "normal",
                  },
                },
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label=""
                  placeholder="Search for underwriter"
                  size="small"
                />
              )}
            />
          </Box>
        }
        onClose={onClose}
      />
    </>
  );
};
