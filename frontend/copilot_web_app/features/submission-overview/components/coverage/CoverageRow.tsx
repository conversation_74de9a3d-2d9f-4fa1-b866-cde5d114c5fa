import { Grid, Icon<PERSON>utton, Typography } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import React, { useEffect } from "react";
import { useCopilotFormContext } from "@utils/form";
import { EditCoveragesFormData } from "@features/submission-overview/components/coverage/types";
import { FormTextField } from "@utils/form/FormTextField";
import { useWatch } from "react-hook-form";
import { CoverageCheckbox } from "@features/submission-overview/components/coverage/CoverageCheckbox";
import { managementLiabilityCoverageNames } from "@legacy/models/Coverage";
import { useOrganizationId } from "@utils/auth";
import { ORGANIZATION } from "@utils/constants";

type CoverageRowProps = {
  index: number;
  remove: (index: number) => void;
  allowEditingLimitAndAttachment: boolean;
  isPds: boolean;
  isReadOnly: boolean;
};

export const CoverageRow = ({
  index,
  remove,
  allowEditingLimitAndAttachment,
  isPds,
  isReadOnly,
}: CoverageRowProps) => {
  const { getValues, setValue } =
    useCopilotFormContext<EditCoveragesFormData>();
  const coverage = getValues(`coverages.${index}`);
  const coverageExcess = useWatch({ name: `coverages.${index}.excess` });
  const coverageName = useWatch({ name: `coverages.${index}.coverage.name` });
  const canCoverageHaveLimitAndAttachment =
    coverageExcess ||
    (managementLiabilityCoverageNames.includes(coverageName) && !isPds);
  const orgId = useOrganizationId();
  const isAIG = orgId === ORGANIZATION.AIG;

  useEffect(() => {
    if (!canCoverageHaveLimitAndAttachment && coverage) {
      setValue(`coverages.${index}.limit`, null);
      setValue(`coverages.${index}.attachmentPoint`, null);
    }
  }, [coverage, index, canCoverageHaveLimitAndAttachment, setValue]);

  if (!coverage) return null;
  const isDisabled = coverage.coverage.coverage_types!.length < 2;

  const tooltip = coverage.coverage.coverage_types?.length
    ? "This coverage doesn't support this coverage type."
    : "This coverage doesn't support coverage types.";
  return (
    <>
      <Grid
        item
        xs={2 + (allowEditingLimitAndAttachment ? 0 : 4)}
        sx={{ alignSelf: "center" }}
      >
        <Typography variant="body1">
          {coverage.coverage.display_name}
        </Typography>
      </Grid>

      <CoverageCheckbox
        isDisabled={isDisabled || isReadOnly}
        tooltip={tooltip}
        name={`coverages.${index}.primary`}
        onChange={(checked) => {
          if (!checked && !getValues(`coverages.${index}.excess`)) {
            setValue(`coverages.${index}.excess`, true);
          }
        }}
        subName={
          coverage.coverage.name === "liability"
            ? "(Primary/General)"
            : undefined
        }
      />
      <CoverageCheckbox
        isDisabled={isDisabled || isReadOnly}
        tooltip={tooltip}
        name={`coverages.${index}.excess`}
        onChange={(checked) => {
          if (!checked && !getValues(`coverages.${index}.primary`)) {
            setValue(`coverages.${index}.primary`, true);
          }
        }}
        subName={
          coverage.coverage.name === "liability"
            ? "(Excess/Umbrella)"
            : undefined
        }
      />
      <Grid item xs={2}>
        <FormTextField
          name={`coverages.${index}.selfInsuranceRetention`}
          label="USD"
          size="small"
          placeholder="Value"
          disabled={isReadOnly}
        />
      </Grid>

      {(allowEditingLimitAndAttachment || isAIG) && (
        <Grid item xs={2}>
          {(canCoverageHaveLimitAndAttachment || isAIG) && (
            <FormTextField
              name={`coverages.${index}.limit`}
              label="USD"
              size="small"
              placeholder="Value"
              disabled={isReadOnly}
            />
          )}
        </Grid>
      )}
      {allowEditingLimitAndAttachment && (
        <Grid item xs={2}>
          {canCoverageHaveLimitAndAttachment && (
            <FormTextField
              name={`coverages.${index}.attachmentPoint`}
              label="USD"
              size="small"
              placeholder="Value"
              disabled={isReadOnly}
            />
          )}
        </Grid>
      )}
      <Grid
        xs={0.6}
        sx={{
          display: "flex",
          alignItems: "flex-end",
          justifyContent: "center",
          position: "relative",
          bottom: "1px",
        }}
      >
        <IconButton onClick={() => remove(index)} disabled={isReadOnly}>
          <DeleteIcon />
        </IconButton>
      </Grid>
    </>
  );
};
