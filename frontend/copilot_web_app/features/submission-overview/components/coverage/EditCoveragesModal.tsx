import React, { useMemo } from "react";
import { Box, Grid } from "@mui/material";
import { Dialog } from "@ui-patterns/dialog";
import Report from "@legacy/models/Report";
import { useCoveragesQuery } from "@queries/coverages";
import { trackSubmissionInfoEditCancel } from "@utils/amplitude";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { EditCoveragesFormData } from "./types";
import { mapFromFormData, mapFromRequestedCoverages } from "./utils";
import { CoverageRow } from "./CoverageRow";
import { useSaveCoverages } from "./useSaveCoverages";
import { CoveragesDropdown } from "./CoveragesDropdown";
import Coverage from "@legacy/models/Coverage";
import { zodResolver } from "@hookform/resolvers/zod";
import { EditCoveragesFormDataSchema } from "@features/submission-overview/components/coverage/schema";
import { enqueueSnackbar } from "notistack";
import { useAccessPermissions } from "@features/product-driven-support/utils";

type Props = {
  report: Report;
  isPds: boolean;
  onClose: () => void;
  isReadOnly: boolean;
  onlyNonLiabilityEditable?: boolean;
};
export const EditCoveragesModal = ({
  report,
  onClose,
  isPds,
  isReadOnly,
  onlyNonLiabilityEditable = false,
}: Props) => {
  const { data: rawAllCoverages, isLoading: isLoadingCoverages } =
    useCoveragesQuery();

  const allCoverages = useMemo(() => {
    const filteredRawAllCoverages = onlyNonLiabilityEditable
      ? rawAllCoverages?.filter((coverage) => coverage.name !== "liability")
      : rawAllCoverages;
    return (
      filteredRawAllCoverages?.map((x) => ({
        ...x,
        display_name:
          x.name === "liability" ? "Liability or Umbrella" : x.display_name,
        label:
          x.name === "liability" ? "Liability or Umbrella" : x.display_name,
      })) ?? []
    );
  }, [rawAllCoverages, onlyNonLiabilityEditable]);

  const form = useForm<EditCoveragesFormData>({
    defaultValues: {
      coverages: mapFromRequestedCoverages(report.submission.coverages),
    },
    resolver: zodResolver(EditCoveragesFormDataSchema),
    mode: "onChange",
  });

  const { fields, append, remove } = useFieldArray<EditCoveragesFormData>({
    control: form.control,
    name: "coverages",
  });

  const onAddCoverage = (coverage: Coverage) => {
    let primary = true;
    let excess = false;
    if (
      coverage.coverage_types?.length === 1 &&
      coverage.coverage_types[0] === "EXCESS"
    ) {
      primary = false;
      excess = true;
    } else if (!coverage.coverage_types?.length) {
      primary = false;
    }
    append({
      id: coverage.id!,
      coverage: coverage as any,
      primary,
      excess,
      limit: null,
      attachmentPoint: null,
      selfInsuranceRetention: null,
    });
  };

  const { isLoading: isSavingCoverages, saveCoverages } = useSaveCoverages();

  const handleClose = () => {
    trackSubmissionInfoEditCancel(report, {
      item: "coverages",
    });
    onClose();
  };

  const { isCSManager } = useAccessPermissions();
  const allowEditingLimitAndAttachment = Boolean(isCSManager && !isReadOnly);
  return (
    <FormProvider {...form}>
      <Dialog
        title="Coverage"
        primaryActionText="Save"
        onClose={handleClose}
        onPrimaryAction={form.handleSubmit(async (data) => {
          const success = await saveCoverages(mapFromFormData(data.coverages));
          if (success) {
            enqueueSnackbar("Coverage saved successfully", {
              variant: "success",
            });
            onClose();
          }
        })}
        isLoading={isSavingCoverages || isLoadingCoverages}
        disablePrimary={!form.formState.isDirty || isReadOnly}
        maxWidth="md"
      >
        <Box sx={{ width: "800px" }}>
          {!isReadOnly && (
            <CoveragesDropdown
              allCoverages={allCoverages}
              selectedIds={form.watch("coverages").map((f) => f.coverage.id!)}
              onAddCoverage={(c) => onAddCoverage(c as any)}
            />
          )}
          {form.watch("coverages").length > 0 && (
            <Grid sx={{ mt: 2 }} container spacing={3}>
              <Grid item xs={2 + (allowEditingLimitAndAttachment ? 0 : 4)} />
              <Grid item xs={1.7}>
                Primary
              </Grid>
              <Grid item xs={1.7}>
                Excess
              </Grid>
              <Grid item xs={2}>
                SIR
              </Grid>
              {allowEditingLimitAndAttachment && (
                <Grid item xs={2}>
                  Limit
                </Grid>
              )}
              {allowEditingLimitAndAttachment && (
                <Grid item xs={2}>
                  Attachment Point
                </Grid>
              )}
              <Grid item xs={0.6} />

              {fields.map((field, index) => {
                const isReadOnlyRow =
                  (isReadOnly ||
                    (onlyNonLiabilityEditable &&
                      field.coverage.name === "liability")) ??
                  false;
                return (
                  <CoverageRow
                    key={field.id}
                    index={index}
                    remove={remove}
                    allowEditingLimitAndAttachment={
                      allowEditingLimitAndAttachment
                    }
                    isPds={isPds}
                    isReadOnly={isReadOnlyRow}
                  />
                );
              })}
            </Grid>
          )}
        </Box>
      </Dialog>
    </FormProvider>
  );
};
