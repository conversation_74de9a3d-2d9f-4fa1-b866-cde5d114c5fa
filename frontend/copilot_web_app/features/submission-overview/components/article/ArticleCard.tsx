import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>on,
  <PERSON>u,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
  useTheme,
} from "@mui/material";
import React, { useState, type JSX } from "react";
import { DOCUMENT_TYPES } from "@legacy/constants/facts";
import CPExternalLink from "@legacy/components/generic/CPExternalLink";
import { NewsIcon } from "@features/submission-overview/components/article/NewsIcon";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import { DuplicateArticleModal } from "@features/submission-overview/components/article/MarkAsDuplicate";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { getDateWithoutTimezoneOffset } from "@utils/date";
import { format } from "date-fns";
import { DisplayFeedback } from "@features/submission-overview/components/article/utils";
import { UseMutateFunction } from "@tanstack/react-query";
import CPIcon from "@legacy/components/generic/CPIcon";
import {
  trackNewsPreviewClosed,
  trackNewsPreviewOpened,
} from "@utils/amplitude";
import {
  ClosePreviewReasonType,
  NewsPreview,
} from "@features/submission-overview/components/article/NewsPreview";
import Report from "@legacy/models/Report";
import {
  LegalFiling as LegalFilingDocument,
  News as NewsDocument,
} from "@legacy/api_clients/facts_api_client";
import startCase from "lodash/startCase";
import toLower from "lodash/toLower";
import { ArticleRelevancy } from "@features/submission-overview/components/article/ArticleRelevancy";
import CircleIcon from "@mui/icons-material/Circle";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const titleCase = (str: string) => {
  return startCase(toLower(str));
};

type ArticleCardProps = {
  report: Report;
  isLegalFiling: boolean;
  dateType?: string;
  date?: string | null;
  explanation?: string | null;
  documentID: string;
  documentTitle?: string;
  documentUrl?: string;
  trackLinkClick?: () => void;
  trackLinkVisit?: () => void;
  sourceType?: string;
  canBeNegative?: boolean;
  isLegalOwnerParent?: boolean;
  documentCaseNumber?: string | null;
  documentCaseStatus?: string | null;
  recreateDocument: UseMutateFunction<void, unknown, void, unknown>;
  businessIds: string[];
  newsDocument?: NewsDocument;
  filingDocument?: LegalFilingDocument;
  businessName?: string;
  businessAddress?: string;
  poBox?: string | null;
  cardBody?: JSX.Element;
  parentId?: string;
  showExplanation?: boolean;
  cardFooterChips?: JSX.Element;
};

export const ArticleCard = ({
  report,
  isLegalFiling,
  dateType,
  date,
  explanation,
  documentID,
  documentTitle,
  documentUrl,
  trackLinkClick,
  trackLinkVisit,
  sourceType,
  canBeNegative = false,
  isLegalOwnerParent = false,
  documentCaseNumber,
  documentCaseStatus,
  recreateDocument,
  businessIds,
  newsDocument,
  businessAddress,
  businessName,
  poBox,
  cardBody,
  parentId,
  showExplanation = true,
  cardFooterChips,
}: ArticleCardProps) => {
  const [isDropDownOpen, setIsDropDownOpen] = useState<boolean>(false);
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [showDuplicateModal, setShowDuplicateModal] = useState<boolean>(false);
  const [relevancySelected, setRelevancySelected] = useState<boolean | null>(
    null
  );
  const { isCSManager } = useAccessPermissions();
  const { internalUseFeatures, showNewsCategoriesDistribution } =
    useFeatureFlags();
  const theme = useTheme();

  const [contextMenuAnchorEl, setContextMenuAnchorEl] =
    React.useState<null | HTMLElement>(null);
  const contextMenuOpen = Boolean(contextMenuAnchorEl);
  const openContextMenu = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setIsDropDownOpen(true);
    setContextMenuAnchorEl(event.currentTarget);
  };
  const closeContextMenu = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setIsDropDownOpen(false);
    setContextMenuAnchorEl(null);
  };

  const openPreview = () => {
    trackNewsPreviewOpened(report, { documentId: documentID });
    setShowPreview(true);
  };

  const closePreview = (reason: ClosePreviewReasonType) => {
    trackNewsPreviewClosed(report, { documentId: documentID, method: reason });
    setShowPreview(false);
  };

  const businessNameDisplay =
    businessName || businessAddress
      ? titleCase(businessName ?? "-")
      : undefined;
  const businessAddressDisplay =
    businessName || businessAddress
      ? report.getBusiness()?.formatAddress(businessAddress) ?? "-"
      : undefined;

  const namedInsured = (() => {
    if (report.getFni()?.business_id === parentId) return "First Named Insured";
    if (report.getOnis().some((x) => x.business_id === parentId))
      return "Other Named Insured";

    return undefined;
  })();

  const actions = (
    <>
      <Menu
        anchorEl={contextMenuAnchorEl}
        open={contextMenuOpen}
        onClose={closeContextMenu}
        onClick={closeContextMenu}
      >
        <Box onClick={(e) => e.stopPropagation()}>
          <CPExternalLink
            isLegalFiling={isLegalFiling}
            url={documentUrl as string}
            onOpenModal={trackLinkClick}
            onLinkVisit={trackLinkVisit}
          >
            <MenuItem>
              <Typography color="text.secondary">Read from source</Typography>
            </MenuItem>
          </CPExternalLink>
        </Box>
        <MenuItem
          onClick={(e) => {
            e.stopPropagation();
            setShowDuplicateModal(true);
          }}
        >
          <Typography color="text.secondary">Mark as Duplicate</Typography>
        </MenuItem>
        {internalUseFeatures && (
          <MenuItem
            onClick={(e) => {
              e.stopPropagation();
              recreateDocument();
            }}
          >
            <Typography color="text.secondary">
              Move to {isLegalFiling ? "News" : "Legal Filings"}
            </Typography>
          </MenuItem>
        )}
      </Menu>
      {showDuplicateModal && (
        <DuplicateArticleModal
          businessIds={businessIds}
          id={documentID}
          documentType={
            isLegalFiling ? DOCUMENT_TYPES.LEGAL_FILING : DOCUMENT_TYPES.NEWS
          }
          onClose={() => setShowDuplicateModal(false)}
        />
      )}
    </>
  );

  const body = (
    <>
      <Box p={3} display="flex" flexDirection="column" rowGap={1.5}>
        <Box display="flex" justifyContent="space-between">
          <Box
            display="grid"
            gridTemplateColumns={"auto auto"}
            columnGap={"30px"}
          >
            <Typography color="text.secondary">
              <b>{dateType ?? "Date"}: </b>
              {date
                ? format(getDateWithoutTimezoneOffset(date), "MM/dd/yyyy")
                : "Not Available"}
            </Typography>
            {documentCaseStatus && (
              <Typography color="text.secondary">
                <b>Status: </b>
                {documentCaseStatus}
              </Typography>
            )}
          </Box>

          <Box>
            {!isLegalFiling && (
              <IconButton size="small" onClick={openPreview}>
                <VisibilityOutlinedIcon />
              </IconButton>
            )}

            <IconButton size="small" onClick={openContextMenu}>
              <MoreHorizIcon />
            </IconButton>
          </Box>
        </Box>

        <Typography variant="h5" fontWeight="500">
          <Box display="grid" gridTemplateColumns={"30px auto"}>
            <>
              {isLegalFiling ? (
                <CPIcon
                  name="legalFiling"
                  style={{ position: "relative", top: "3px" }}
                />
              ) : (
                <NewsIcon sx={{ position: "relative", top: "3px" }} />
              )}
            </>
            <Box display="inline" ml={1}>
              {documentTitle && titleCase(documentTitle)}
              <Typography
                variant="caption"
                color="text.secondary"
                component="div"
              >
                {documentCaseNumber && `Case: ${documentCaseNumber}`}
              </Typography>
            </Box>
          </Box>
        </Typography>

        {cardBody}

        {!isLegalFiling &&
          showNewsCategoriesDistribution &&
          newsDocument?.category && (
            <Stack direction="row" spacing={1} mt={1} alignItems={"center"}>
              <Typography fontWeight="bold">Category: </Typography>
              <Chip size={"small"} label={newsDocument?.category} />
            </Stack>
          )}

        <Box
          sx={{
            "& > *": {
              display: "inline",
            },
            "& > :last-child": {
              cursor: "pointer",
              color: theme.colors.primaryMenuBackground,
              fontWeight: 600,
            },
          }}
        >
          <Typography fontWeight="bold">Source: </Typography>

          <CPExternalLink
            isLegalFiling={isLegalFiling}
            url={documentUrl as string}
            onOpenModal={trackLinkClick}
            onLinkVisit={trackLinkVisit}
          >
            <Box sx={{ wordBreak: "break-all", display: "inline" }}>
              {documentUrl}
            </Box>
          </CPExternalLink>
        </Box>
        {isCSManager && <Typography>{sourceType}</Typography>}
        {canBeNegative && (
          <Typography>
            <DisplayFeedback documentId={documentID} />
          </Typography>
        )}

        <ArticleRelevancy
          report={report}
          documentID={documentID}
          businessIds={businessIds}
          documentType={
            isLegalFiling ? DOCUMENT_TYPES.LEGAL_FILING : DOCUMENT_TYPES.NEWS
          }
          selected={relevancySelected}
          setSelected={setRelevancySelected}
        />
      </Box>

      {(businessName || businessAddress) && (
        <Box
          bgcolor="#F5F5F5"
          px={3}
          py={1.5}
          display="flex"
          gap={2}
          alignItems="center"
        >
          <Box flex={1}>
            <Typography color="text.secondary" fontSize={12}>
              {businessNameDisplay}
              {isLegalOwnerParent && (
                <>
                  <CircleIcon
                    color="warning"
                    fontSize="small"
                    sx={{ mx: 1, fontSize: 10 }}
                  />
                  Lawsuit matched by the name of the Business
                </>
              )}
            </Typography>
            <Typography color="text.secondary" fontSize={12}>
              {businessAddressDisplay}
            </Typography>
          </Box>
          {poBox && (
            <Typography color="text.secondary" fontSize={12}>
              {poBox}
            </Typography>
          )}

          {cardFooterChips}
          {namedInsured && (
            <Box>
              <Chip size="small" label={namedInsured} />
            </Box>
          )}
        </Box>
      )}
      {showPreview && !isLegalFiling && newsDocument && (
        <NewsPreview news={newsDocument} onClose={closePreview} />
      )}
    </>
  );

  return (
    <Tooltip
      title={explanation ?? "No explanation found"}
      placement="right"
      disableHoverListener={!showExplanation || !isCSManager || showPreview}
      slotProps={{
        popper: {
          modifiers: [
            {
              name: "offset",
              options: {
                offset: [0, isDropDownOpen ? 100 : 0],
              },
            },
          ],
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        border="1px solid"
        borderColor="divider"
        borderRadius="4px"
        width={isLegalFiling ? "100%" : undefined}
        maxWidth={isLegalFiling ? 900 : 800}
        mt={2}
        mb={3}
      >
        {isLegalFiling ? (
          <Box style={{ cursor: "pointer" }}>
            <CPExternalLink
              isLegalFiling={true}
              url={documentUrl as string}
              onOpenModal={trackLinkClick}
              onLinkVisit={trackLinkVisit}
            >
              {body}
            </CPExternalLink>
          </Box>
        ) : (
          <>{body}</>
        )}
        {actions}
      </Box>
    </Tooltip>
  );
};
