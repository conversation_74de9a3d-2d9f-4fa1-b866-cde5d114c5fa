import {
  Box,
  Button,
  CircularProgress,
  Stack,
  TablePagination,
  Typography,
} from "@mui/material";
import React, { useEffect, useMemo, useState, type JSX } from "react";
import {
  useAggregatedDocumentsCountsQuery,
  useDocumentCountsQuery,
  usePaginatedDocumentsQuery,
} from "@queries/document";
import { DOCUMENT_TYPES, PARENT_TYPES } from "@legacy/constants/facts";
import { NewsArticle } from "@features/submission-overview/components/article/News";
import { EntitiesDropdown } from "@features/entities";
import {
  Document,
  LegalFiling as LegalFilingDocument,
  News,
} from "@legacy/api_clients/facts_api_client";
import { LegalFilingRole } from "@legacy/clients/factsApiTypes";
import { LegalFiling } from "@features/submission-overview/components/article/LegalFiling";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { QueryDataContext } from "@utils/query-data-context/useQueryDataContext";
import {
  trackArticlePageChanged,
  trackArticlePerPageChanged,
  trackLegalFilingsFiltered,
  trackLegalFilingsSearched,
  trackNewsFiltered,
  trackNewsSearched,
} from "@utils/amplitude";
import uniq from "lodash/uniq";
import { ErsEntity } from "@legacy/models/ErsEntity";
import { ModalInput } from "@features/add-document-modal/ModalInput";
import { useReport } from "@utils/useReport";
import { useDebounce } from "@utils/useDebounce";
import { useBusinessIdsFromContext } from "@features/modes-display/hooks/useBusinessIdsFromContext";
import { SortChip } from "@features/add-document-modal/SortChip";
import {
  NewsSummary,
  useNewsSummary,
  useNewsSummaryWithCategories,
} from "@features/submission-overview/components/news/NewsSummary";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import VisibilityRoundedIcon from "@mui/icons-material/VisibilityRounded";
import { ExpandParam } from "@legacy/models/types/documents";
import { ChipDropdown } from "@features/chip-dropdown";
import { ArticleSearchField } from "@features/submission-overview/components/ArticleSearchField";
import { NoItems } from "@features/no-items";
import { DistributionBar, ValueCountItem } from "./article/DistributionBar";
import sortBy from "lodash/sortBy";
import { Stars } from "@ui-patterns/icons/stars";
import { ELEMENT_ID } from "@features/modes/jsx/constants";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const NoArticlesFound = ({
  isLegalFiling = false,
}: {
  isLegalFiling: boolean;
}) => {
  const articleType = isLegalFiling ? "cases" : "articles";

  return (
    <NoItems
      title={`No ${articleType} found`}
      subtitle={`Refine your search to view other ${articleType}`}
    />
  );
};

const DOCUMENT_TYPE_DICTIONARY: Partial<Record<DOCUMENT_TYPES, string>> = {
  [DOCUMENT_TYPES.NEWS]: "news articles",
  [DOCUMENT_TYPES.LEGAL_FILING]: "legal filings",
};

const LEGAL_FILING_ROLE_NO_FILTER_LABEL = "Any Role";
/*
 * To connect:
 * - required UI labels, and
 * - backend implementation (no param for no filtering + static_common/enums/legal_filing.py enum).
 */
const LEGAL_FILING_ROLE_VALUES: Record<string, string | undefined> = {
  [LEGAL_FILING_ROLE_NO_FILTER_LABEL]: undefined,
  Plaintiff: LegalFilingRole.PLAINTIFF,
  Defendant: LegalFilingRole.DEFENDANT,
  "Not Available": LegalFilingRole.ANY_OTHER,
};

export type ArticlesProps = {
  documentType: DOCUMENT_TYPES;
  defaultRowsPerPage?: number;
  doPolling?: boolean;
  relations?: { mapping: Record<string, string[]>; list: string[] };
  showSummary?: boolean;
  movePaginationToTop?: boolean;
  summaryOnTheRight?: boolean;
  onDocumentCountChange?: (count: number) => void;
  showStaticExplanations?: boolean;
  hideDistributions?: boolean;
  setHasArticles?: any;
};

export const Articles = ({
  documentType,
  defaultRowsPerPage,
  doPolling = false,
  relations,
  showSummary = false,
  movePaginationToTop = false,
  summaryOnTheRight = false,
  showStaticExplanations = false,
  hideDistributions = false,
  onDocumentCountChange,
  setHasArticles,
}: ArticlesProps): JSX.Element | null => {
  const isLegalFiling = documentType === DOCUMENT_TYPES.LEGAL_FILING;
  const defaultPerPage = defaultRowsPerPage || (isLegalFiling ? 2 : 5);
  const perPageOptions = isLegalFiling ? [2, 5, 10, 25] : [5, 10, 25];
  const [entity, setEntity] = useState<string>("All");
  const [keyword, setKeyword] = useState<string>();
  const [page, setPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(defaultPerPage);
  const debouncedQuerySearch = useDebounce(keyword);
  const report = useReport();
  const [fetchLastMonths, setFetchLastMonths] = useState<number>(
    report.isRenewal() ? 12 : 0
  );
  const [legalFilingRole, setLegalFilingRole] = useState<string>(
    LEGAL_FILING_ROLE_NO_FILTER_LABEL
  );
  const [categoryFilter, setCategoryFilter] = useState<string | undefined>();
  const [showWithNegativeFeedback, setShowWithNegativeFeedback] =
    useState(false);
  const { isCSManager } = useAccessPermissions();
  const {
    showNewsCategoriesDistribution: showNewsCategoriesDistributionFlag,
    showNewsSummariesByCategory: showNewsSummariesByCategoryFlag,
  } = useFeatureFlags();
  const hasEditPermissions = report.hasEditPermissions();

  const {
    rowContextEntities,
    businessIdsFromRowContext,
    businessIds,
    businessIdScopeIsLimitedFromRowContext,
    isOtherBusiness,
  } = useBusinessIdsFromContext(report);
  const entities = rowContextEntities?.entities;

  const businessIdScopeIsLimitedByDropdown = entity && entity !== "All";

  const businesses = report.getBusinesses().filter((business) => {
    if (businessIdScopeIsLimitedFromRowContext) {
      return businessIdsFromRowContext.includes(business.business_id!);
    }

    return true;
  });

  const theOnlyBusinessIdOrUndefined =
    businessIds.length === 1 ? businessIds[0] : undefined;

  const uniqueBusinessIds = uniq(businessIds);
  const { documentCounts, isFetching: isFetchingCounts } =
    useDocumentCountsQuery(
      {
        parentIds: uniqueBusinessIds,
        parentType: PARENT_TYPES.BUSINESS,
        documentTypeId: documentType,
        onlyPositiveFeedback: !showWithNegativeFeedback,
        onlyRelevant: showWithNegativeFeedback,
      },
      doPolling ? 8000 : undefined
    );

  const relationsList = isOtherBusiness ? [] : relations?.list ?? [];

  const {
    documentCounts: legalParentDocumentCounts,
    isFetching: isFetchingLegalParentCounts,
  } = useDocumentCountsQuery(
    {
      parentIds: relationsList,
      parentType: PARENT_TYPES.BUSINESS,
      documentTypeId: documentType,
      onlyPositiveFeedback: !showWithNegativeFeedback,
      onlyRelevant: showWithNegativeFeedback,
    },
    doPolling ? 8000 : undefined
  );

  const { summaries, hasFeedbackArray, totalSummariesCount } =
    useNewsSummaryWithCategories(
      showSummary && showNewsSummariesByCategoryFlag,
      categoryFilter
    );

  const totalDocumentCounts = useMemo(() => {
    const newDocumentCounts: Record<string, number> = {};
    uniqueBusinessIds.forEach((id) => {
      newDocumentCounts[id] = documentCounts[id] || 0;
      relations?.mapping[id]?.forEach((e) => {
        newDocumentCounts[id] =
          newDocumentCounts[id] + (legalParentDocumentCounts[e] || 0);
      });
    });
    return newDocumentCounts;
  }, [
    uniqueBusinessIds,
    relations?.mapping,
    documentCounts,
    legalParentDocumentCounts,
  ]);

  useEffect(() => {
    if (entity && !businessIds.includes(entity)) {
      setEntity("All");
    }
  }, [businessIds, entity]);

  const { addingNewsStories, uploadLegalFilings } = useFeatureFlags();

  useEffect(() => {
    setPage(1);
  }, [entity, fetchLastMonths, showWithNegativeFeedback, legalFilingRole]);

  const documentParentIds = businessIdScopeIsLimitedFromRowContext
    ? businessIdsFromRowContext
    : businessIdScopeIsLimitedByDropdown
      ? [entity]
      : businessIds;

  const [ordering, setOrdering] = useState<{
    orderBy: string;
    descending: boolean;
  }>({ orderBy: "filing_date", descending: true });

  const allParentIds = [...documentParentIds, ...relationsList];
  const publishedAfter = useMemo(() => {
    if (fetchLastMonths) {
      const date = new Date();
      date.setMonth(date.getMonth() - fetchLastMonths);
      return date;
    } else {
      return undefined;
    }
  }, [fetchLastMonths]);

  const {
    data: aggregatedCounts,
    isLoading: isAggregatedCountsLoading,
    documentsTotalCount: totalAggregatedCount,
  } = useAggregatedDocumentsCountsQuery(!isLegalFiling, {
    parentIds: {
      parent_ids: allParentIds,
    },
    parentType: PARENT_TYPES.BUSINESS,
    onlyPositiveFeedback: !showWithNegativeFeedback,
    deduplicatedNews: isLegalFiling ? undefined : true,
    createdBefore: report.submission.frozen_as_of ?? undefined,
    documentTypeId: documentType,
    publishedAfter: publishedAfter?.toISOString(),
    onlyRelevant: showWithNegativeFeedback,
    aggregatedBy: "category",
  });

  const sortedAggregatedCounts = useMemo(() => {
    return sortBy(
      aggregatedCounts ?? [],
      (item) => item.value === "Other",
      (item) => item.value?.toLowerCase()
    );
  }, [aggregatedCounts]);

  const {
    data: documents,
    isLoading: isLoadingDocuments,
    isRefetching,
    isPreviousData,
    queryKey,
    isFetching: isFetchingDocuments,
  } = usePaginatedDocumentsQuery(
    {
      parentIds: allParentIds,
      parentType: PARENT_TYPES.BUSINESS,
      onlyPositiveFeedback: !showWithNegativeFeedback,
      deduplicatedNews: isLegalFiling ? undefined : true,
      createdBefore: report.submission.frozen_as_of ?? undefined,
      documentType,
      expand: [
        "parent_id",
        "readable_html",
        "summary",
        "explanation",
        ...(!isLegalFiling ? (["category"] as ExpandParam) : []),
        ...(isCSManager ? (["source"] as ExpandParam) : []),
      ],
      searchPhrase: debouncedQuerySearch,
      page,
      per_page: perPage,
      publishedAfter: publishedAfter?.toISOString(),
      onlyRelevant: showWithNegativeFeedback,
      allowPublishedAtNulls: false,
      newsCategory: categoryFilter,
      legalFilingRole: LEGAL_FILING_ROLE_VALUES[legalFilingRole],
      ...(isLegalFiling ? ordering : {}),
    },
    doPolling ? 8000 : undefined,
    showWithNegativeFeedback,
    true
  );
  const isLoading = isLoadingDocuments || isPreviousData || isRefetching;

  const documentCount = documents?.total_documents ?? 0;
  useEffect(() => {
    onDocumentCountChange?.(documentCount);
  }, [documentCount, onDocumentCountChange]);

  const allDocuments = useMemo(
    () => documents?.documents ?? [],
    [documents?.documents]
  );

  const { summary: fetchedSummary, hasFeedback } = useNewsSummary(showSummary);
  const summary = showSummary ? fetchedSummary : undefined;

  const hasZeroArticles = allDocuments.length === 0 && !entity;

  const hasAnyArticles = page > 1 || (documents?.total_documents ?? 0) > 0;

  const documentsToDisplay = useMemo(
    () =>
      allDocuments.filter((d: Document) => {
        if (businessIdScopeIsLimitedFromRowContext) {
          return true;
        }
        return businessIdScopeIsLimitedByDropdown
          ? d.parent_id === entity ||
              relations?.mapping[entity]?.some((e) => e === d.parent_id)
          : true;
      }),
    [
      businessIdScopeIsLimitedFromRowContext,
      businessIdScopeIsLimitedByDropdown,
      allDocuments,
      relations?.mapping,
      entity,
    ]
  );

  const categoryOptions = useMemo(() => {
    const res = sortedAggregatedCounts?.map((c) => ({
      label: c.value!,
      value: c.value!,
    }));
    return [{ label: "Any Category", value: "Any" }, ...(res ?? [])];
  }, [sortedAggregatedCounts]);

  const hasMoreThanOneCategoryOfNews = categoryOptions.length > 2;
  const showNewsCategoriesDistribution =
    showNewsCategoriesDistributionFlag && hasMoreThanOneCategoryOfNews;

  const isFetchingInPollingMode =
    doPolling &&
    (isFetchingDocuments || isFetchingCounts || isFetchingLegalParentCounts);

  if (
    !hasAnyArticles &&
    !keyword &&
    fetchLastMonths === 0 &&
    !categoryFilter &&
    legalFilingRole === LEGAL_FILING_ROLE_NO_FILTER_LABEL
  ) {
    if (setHasArticles) {
      setHasArticles(false);
    }
    return (
      <QueryDataContext.Provider value={{ queryKeys: [queryKey] }}>
        <Box display="flex" justifyContent="space-between">
          <Typography>
            No {DOCUMENT_TYPE_DICTIONARY[documentType] ?? "documents"} were
            found.
          </Typography>
          {isLoading && <CircularProgress sx={{ mx: 2 }} />}{" "}
          {isFetchingInPollingMode && <CircularProgress size={20} />}
          {addingNewsStories && documentType === DOCUMENT_TYPES.NEWS ? (
            <ModalInput
              report={report}
              businessId={theOnlyBusinessIdOrUndefined}
              buttonLabel="NEW STORY"
              documentType={DOCUMENT_TYPES.NEWS}
              variant="button"
            />
          ) : undefined}
          {uploadLegalFilings &&
          documentType === DOCUMENT_TYPES.LEGAL_FILING ? (
            <ModalInput
              report={report}
              businessId={theOnlyBusinessIdOrUndefined}
              buttonLabel="ADD FILING"
              documentType={DOCUMENT_TYPES.LEGAL_FILING}
            />
          ) : undefined}
        </Box>
      </QueryDataContext.Provider>
    );
  }

  const dropdownBusinesses = businesses
    .filter((b) => {
      const count = totalDocumentCounts[b.business_id!];
      return !!count;
    })
    .filter(
      (b) =>
        !report.getSubmission().isTransactionalLiability() ||
        b.entity_role === "TARGET" ||
        b.entity_role === "SELLER"
    )
    .sort((a, b) => {
      const countA = totalDocumentCounts[a.business_id!];
      const countB = totalDocumentCounts[b.business_id!];
      if (countA > countB) return -1;
      return 1;
    })
    .map((business) => {
      const count = totalDocumentCounts[business.business_id!];

      const documentLabel =
        count === 1 && documentType !== DOCUMENT_TYPES.NEWS
          ? DOCUMENT_TYPE_DICTIONARY[documentType]?.slice(0, -1)
          : DOCUMENT_TYPE_DICTIONARY[documentType];

      return {
        label:
          count !== undefined
            ? `${business.getNameAndAddress()} (${count} ${documentLabel})`
            : business.getNameAndAddress(),
        business_id: business.business_id!,
      };
    });

  const findEntity = (entityId: string): ErsEntity | null | undefined => {
    const reportBusinesses = report.getBusinesses();
    const business = reportBusinesses.find(
      (business) => business.business_id === entityId
    );
    let entity =
      business?.entity_data ?? entities?.find((e) => e.id === entityId);
    if (!entity && relations) {
      const origEntityId = Object.keys(relations.mapping).find(
        (e) => relations.mapping[e]?.some((relation) => relation === entityId)
      );
      entity = entities?.find((e) => e.id === origEntityId);
    }
    return entity;
  };

  const onSearch = (value: string) => {
    setKeyword(value);
    if (isLegalFiling)
      trackLegalFilingsSearched(report, { searchValue: value });
    else trackNewsSearched(report, { searchValue: value });
  };

  const onEntitySelect = (e: string) => {
    setEntity(e);
    if (isLegalFiling) {
      trackLegalFilingsFiltered(report, {
        item: "entities",
        filterSource: "filter",
      });
    } else {
      trackNewsFiltered(report, { item: "entities", filterSource: "filter" });
    }
  };

  const onDatePublishedSelect = (months: number) => {
    setFetchLastMonths(months);

    if (isLegalFiling) {
      trackLegalFilingsFiltered(report, {
        item: "date published",
        filterSource: "filter",
      });
    } else {
      trackNewsFiltered(report, {
        item: "date published",
        filterSource: "filter",
      });
    }
  };

  const onLegalFilingRoleSelect = (legalFilingRole: string) => {
    setLegalFilingRole(legalFilingRole);
    trackLegalFilingsFiltered(report, {
      item: "role",
      filterSource: "filter",
    });
  };

  const scrollToTop = () => {
    const containerId = isLegalFiling
      ? `${ELEMENT_ID.LEGAL_FILINGS}-scroll-container`
      : `${ELEMENT_ID.NEWS}-scroll-container`;
    const element = document.getElementById(containerId);
    element?.scrollTo({ top: 0, behavior: "smooth" });
  };

  const onCategorySelect = (category?: string) => {
    if (category == "Any") {
      setCategoryFilter(undefined);
    } else {
      setCategoryFilter(category);
    }
    scrollToTop();
    trackNewsFiltered(report, { item: "category", filterSource: "filter" });
  };

  const nothingToDisplay =
    (documentsToDisplay ?? []).length === 0 && !isLoading;

  if (setHasArticles) {
    setHasArticles((documentsToDisplay ?? []).length !== 0);
  }

  const pagination = (
    <>
      {documents &&
        !hasZeroArticles &&
        (documentsToDisplay ?? []).length > 0 && (
          <TablePagination
            count={documents?.total_documents ?? 0}
            page={page ? Number(page) - 1 : 0}
            rowsPerPage={perPage ? Number(perPage) : defaultPerPage}
            rowsPerPageOptions={perPageOptions}
            onRowsPerPageChange={(e) => {
              trackArticlePerPageChanged(report, {
                documentType,
                prevPerPage: perPage,
                perPage: Number(e.target.value),
              });
              setPage(1);
              setPerPage(Number(e.target.value));
            }}
            onPageChange={(_, p) => {
              trackArticlePageChanged(report, {
                documentType,
                prevPageNumber: page,
                pageNumber: p + 1,
              });
              setPage(p + 1);
            }}
            sx={{
              ".MuiToolbar-root": {
                flexWrap: "wrap",
              },
              p: 0,
              borderBottom: "none",
            }}
          />
        )}
    </>
  );

  const summaryElement =
    totalSummariesCount > 0 ? (
      <Box flex={5} mr={8}>
        {summaries?.length > 0 && (
          <>
            <Box marginX={3} marginY={1}>
              <Typography
                variant="h5"
                component="div"
                sx={{ display: "flex", alignItems: "center", gap: 1 }}
              >
                <Stars /> Content summary
              </Typography>
            </Box>
            {summaries.map((summary, index) => (
              <NewsSummary
                key={summary.id}
                summary={summary}
                hasFeedback={hasFeedbackArray[index]}
                showHeader={false}
                onCategoryClick={onCategorySelect}
              />
            ))}
          </>
        )}
      </Box>
    ) : (
      <Box flex={5} mr={8}>
        {summary && <NewsSummary summary={summary} hasFeedback={hasFeedback} />}
      </Box>
    );

  const showDistributions =
    !hideDistributions &&
    showNewsCategoriesDistribution &&
    sortedAggregatedCounts;

  return (
    <QueryDataContext.Provider value={{ queryKeys: [queryKey] }}>
      <Box display="flex">
        {!isLegalFiling && (summary || showDistributions) && (
          <Stack direction={"column"} flex={3} mr={8}>
            {showDistributions && (
              <Box>
                {isAggregatedCountsLoading && <CircularProgress />}
                <DistributionBar
                  title={"Distribution by Category"}
                  data={sortedAggregatedCounts as ValueCountItem[]}
                  totalCount={totalAggregatedCount}
                  onItemClick={(val) => onCategorySelect(val)}
                  selectedBar={categoryFilter}
                  customColors={{ Other: "#aaaaaa" }}
                  customWording={{ singular: "Article", plural: "Articles" }}
                />
              </Box>
            )}
            {(summary || summaries.length > 0) && <Box>{summaryElement}</Box>}
          </Stack>
        )}
        <Box flex={6}>
          <Box
            sx={{
              maxWidth: "100%",
              minWidth: 0,
              "& > *": {
                minWidth: 0,
                flex: "1 1 0",
              },
            }}
          >
            <Stack
              direction="row"
              spacing={2}
              alignItems="center"
              flexWrap={"wrap"}
            >
              <Box display="flex" flex={1} columnGap={2}>
                <Box display="flex" alignItems="center" columnGap={1}>
                  <Typography
                    color="link"
                    fontWeight={500}
                    fontSize={13}
                    noWrap={true}
                  >
                    Published Date
                  </Typography>

                  <ChipDropdown
                    label="Published"
                    variant="outlined"
                    useSelectionAsLabel
                    value={fetchLastMonths.toString()}
                    color="link"
                    options={[
                      {
                        label: "Last 6 months",
                        value: "6",
                      },
                      {
                        label: "Last 12 months",
                        value: "12",
                      },
                      {
                        label: "Last 24 months",
                        value: "24",
                      },
                      {
                        label: "Any Date",
                        value: "0",
                      },
                    ]}
                    onSelect={(value) => onDatePublishedSelect(Number(value))}
                  />
                </Box>

                {businesses.length > 1 && (
                  <Box display="flex" alignItems="center" columnGap={1}>
                    <Typography color="link" fontWeight={500} fontSize={13}>
                      Entities
                    </Typography>

                    <Box maxWidth={150}>
                      <EntitiesDropdown
                        entity={entity}
                        setEntity={onEntitySelect}
                        businesses={dropdownBusinesses}
                        useCase={"card"}
                        variant="chip"
                      />
                    </Box>
                  </Box>
                )}

                {isLegalFiling && (
                  <Box display="flex" alignItems="center" columnGap={1}>
                    <Typography color="link" fontWeight={500} fontSize={13}>
                      Filing Role
                    </Typography>

                    <Box maxWidth={150}>
                      <ChipDropdown
                        value={legalFilingRole}
                        variant="outlined"
                        useSelectionAsLabel
                        color="link"
                        options={Object.keys(LEGAL_FILING_ROLE_VALUES)}
                        onSelect={(value) => onLegalFilingRoleSelect(value)}
                        label="Filing Role"
                      />
                    </Box>
                  </Box>
                )}

                {!isLegalFiling && showNewsCategoriesDistribution && (
                  <Box display="flex" alignItems="center" columnGap={1}>
                    <Typography color="link" fontWeight={500} fontSize={13}>
                      Category
                    </Typography>

                    <Box maxWidth={150}>
                      <ChipDropdown
                        label="Category"
                        variant="outlined"
                        useSelectionAsLabel
                        value={categoryFilter ?? "Any"}
                        color="link"
                        options={categoryOptions}
                        onSelect={(value) => onCategorySelect(value)}
                      />
                    </Box>
                  </Box>
                )}
                {isLegalFiling && <SortChip setOrdering={setOrdering} />}
              </Box>
              {isCSManager && documentType == DOCUMENT_TYPES.NEWS && (
                <Button
                  sx={{ mt: 1, maxWidth: 300 }}
                  onClick={() =>
                    setShowWithNegativeFeedback(!showWithNegativeFeedback)
                  }
                  variant="contained"
                  size="small"
                  endIcon={
                    showWithNegativeFeedback ? (
                      <VisibilityOffIcon />
                    ) : (
                      <VisibilityRoundedIcon />
                    )
                  }
                >
                  {showWithNegativeFeedback
                    ? "Stop Showing News With Negative Feedback"
                    : "Show News With Negative Feedback"}
                </Button>
              )}
              {isLoading && <CircularProgress sx={{ mx: 2 }} />}{" "}
              <Box width="160px">
                <ArticleSearchField
                  onSearch={(value) => onSearch(value)}
                  value={keyword || ""}
                />
              </Box>
              <Box>
                {hasEditPermissions &&
                addingNewsStories &&
                documentType === DOCUMENT_TYPES.NEWS ? (
                  <ModalInput
                    report={report}
                    businessId={theOnlyBusinessIdOrUndefined}
                    buttonLabel="NEW STORY"
                    documentType={DOCUMENT_TYPES.NEWS}
                    variant="button"
                  />
                ) : undefined}
                {hasEditPermissions &&
                uploadLegalFilings &&
                documentType === DOCUMENT_TYPES.LEGAL_FILING ? (
                  <ModalInput
                    report={report}
                    businessId={theOnlyBusinessIdOrUndefined}
                    buttonLabel="ADD FILING"
                    documentType={DOCUMENT_TYPES.LEGAL_FILING}
                    variant="button"
                  />
                ) : undefined}
              </Box>
            </Stack>
            {movePaginationToTop && pagination}
            {documentsToDisplay?.map((document) => {
              const entity = findEntity(document.parent_id!);
              if (!entity) {
                return null;
              }
              const element =
                documentType === DOCUMENT_TYPES.NEWS ? (
                  <NewsArticle
                    key={document.id}
                    report={report}
                    document={document as News}
                    businessName={entity.getName() ?? ""}
                    businessAddress={
                      entity.getPremises()?.formatted_address ?? ""
                    }
                    parentId={document.parent_id}
                    poBox={entity.getPoBox()}
                    businessIds={allParentIds}
                    canBeNegative={showWithNegativeFeedback}
                    showExplanation={!showStaticExplanations}
                  />
                ) : (
                  <LegalFiling
                    key={document.id}
                    report={report}
                    document={document as LegalFilingDocument}
                    businessName={entity.getName() ?? ""}
                    businessAddress={
                      entity.getPremises()?.formatted_address ?? ""
                    }
                    businessIds={allParentIds}
                    isLegalOwnerParent={entity.id !== document.parent_id}
                    parentId={document.parent_id}
                    showExplanation={!showStaticExplanations}
                  />
                );
              if (!showStaticExplanations) {
                return element;
              } else {
                return (
                  <Box
                    key={document.id}
                    display="flex"
                    flexDirection="row"
                    sx={{ "& > *": { my: 1 } }}
                  >
                    {element}
                    <Box
                      ml={1}
                      flex="1 1 0"
                      minWidth={0}
                      sx={{
                        ml: 1,
                        flex: "1 1 0",
                        minWidth: 0,
                        maxWidth: 400,
                        border: "1px solid #ddd",
                        borderRadius: 1,
                        overflow: "hidden",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          pl: 2,
                          py: 1,
                          background: "#F5F5F5",
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{ color: "#565B66" }}
                        >
                          Explanation
                        </Typography>
                      </Box>
                      <Box p={2}>
                        <Typography sx={{ color: "#565B66" }}>
                          {(document as News).explanation ?? "No Explanation"}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                );
              }
            })}
            {!movePaginationToTop && pagination}
          </Box>
          {nothingToDisplay && (
            <NoArticlesFound isLegalFiling={isLegalFiling} />
          )}
        </Box>
        {summary && summaryOnTheRight && summaryElement}
      </Box>
    </QueryDataContext.Provider>
  );
};
