import React from "react";
import { Chip, Grid, Tooltip } from "@mui/material";
import InfoIcon from "@mui/icons-material/InfoOutlined";
import VisibilityIcon from "@mui/icons-material/VisibilityOutlined";
import RefreshIcon from "@mui/icons-material/Refresh";
import { useCurrentUser } from "@utils/auth";
import { useSnackbar } from "notistack";
import { copyToClipboard } from "@utils/text";
import HiddenFactsModal from "@legacy/components/modals/HiddenFactsModal";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import SummarizeIcon from "@mui/icons-material/Summarize";
import { useReport } from "@utils/useReport";
import { useOrganizationId } from "@utils/auth";
import { AddRelationChip } from "@features/cards/corporate-relations/components/AddRelationChip";
import { usePostIMSInfo } from "@queries/report";
import {
  usePublishInisightsRequestedEvent,
  usePublishSummarizationRequestedEvent,
} from "@queries/events";
import BugReportIcon from "@mui/icons-material/BugReport";
import { routes } from "@features/routes";
import Link from "next/link";
import { ORGANIZATION } from "@utils/constants";

export const SupportRow = () => {
  const report = useReport();
  const hasControlNumber =
    (report?.getSubmission().client_submission_ids ?? []).length > 0;
  const [showHiddenFacts, setShowHiddenFacts] = React.useState(false);
  const submission = report.getSubmission();
  const { enqueueSnackbar } = useSnackbar();
  const user = useCurrentUser();
  const orgId = useOrganizationId();
  const { mutateAsync: publishInsights } = usePublishInisightsRequestedEvent();
  // useMutation var uses Post endpoint therefore it's postIMSInfo
  const { mutate: getIMSInfo } = usePostIMSInfo(report.id);
  const { mutateAsync: publishSummarization } =
    usePublishSummarizationRequestedEvent(report.id);

  const { allowHideFacts, supportFlow, internalUseFeatures, pdsDebugger } =
    useFeatureFlags();

  const handleCopyDebugDetails = (subIdOnly = false) => {
    if (subIdOnly) {
      copyToClipboard(submission.id);
      enqueueSnackbar("Submission ID copied");
      return;
    }
    const details = `
Submission ID: ${submission.id},
Report ID: ${report.id},
User: ${user.email} (${user.id}),
Time: ${new Date().toLocaleString()}`;

    copyToClipboard(details);
    enqueueSnackbar("Debug details copied");
  };

  const refreshInsights = async () => {
    const businessIds = report.getBusinessIds();
    if (!businessIds.length) return;

    const promises = businessIds.map((businessId) => {
      return publishInsights(businessId);
    });

    await Promise.all(promises);

    enqueueSnackbar("Insights refresh requested");
  };

  const refreshSummaries = async () => {
    await publishSummarization(report.id);

    enqueueSnackbar("Summaries refresh requested");
  };

  return (
    <>
      <Grid container item xs={12}>
        <Grid
          container
          spacing={3}
          columnSpacing={{ xs: 1, xl: 3 }}
          sm={10}
          xl={9}
          sx={{
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          {(supportFlow || internalUseFeatures) && (
            <>
              <Grid item>
                <Tooltip title="Click to copy debug details">
                  <Chip
                    label="Debug details"
                    onClick={() => handleCopyDebugDetails()}
                    onDelete={() => handleCopyDebugDetails()}
                    deleteIcon={<InfoIcon />}
                  />
                </Tooltip>
              </Grid>
              <Grid item>
                <Tooltip title="Click to copy submission ID">
                  <Chip
                    label="Submission ID"
                    onClick={() => handleCopyDebugDetails(true)}
                    onDelete={() => handleCopyDebugDetails(true)}
                    deleteIcon={<InfoIcon />}
                  />
                </Tooltip>
              </Grid>
            </>
          )}

          {allowHideFacts && (
            <Grid item>
              <Chip
                label="Hidden facts"
                onClick={() => setShowHiddenFacts(true)}
                onDelete={() => setShowHiddenFacts(true)}
                deleteIcon={<VisibilityIcon />}
              />
            </Grid>
          )}

          <Grid item>
            <Tooltip title="Click to refresh summaries">
              <Chip
                label="Refresh summaries"
                onClick={refreshSummaries}
                onDelete={refreshSummaries}
                deleteIcon={<SummarizeIcon />}
              />
            </Tooltip>
          </Grid>

          <Grid item>
            <Tooltip title="Click to refresh insights">
              <Chip
                label="Refresh insights"
                onClick={refreshInsights}
                onDelete={refreshInsights}
                deleteIcon={<RefreshIcon />}
              />
            </Tooltip>
          </Grid>

          <Grid item>
            <Tooltip title="Click to add manage corporate relations">
              <AddRelationChip />
            </Tooltip>
          </Grid>

          {orgId === ORGANIZATION.PARAGON && hasControlNumber && (
            <Grid item>
              <Tooltip title="Click to get IMS Sub Info">
                <Chip
                  label="Get IMS Info"
                  onClick={() => getIMSInfo()}
                  onDelete={() => getIMSInfo()}
                  deleteIcon={<InfoIcon />}
                />
              </Tooltip>
            </Grid>
          )}
          {pdsDebugger && (
            <Grid item>
              <Tooltip title="Click to open PDS debugger">
                <Link
                  href={routes.pds.toDebugger(report.id)}
                  passHref
                  target="_blank"
                >
                  <Chip
                    label="PDS Debugger"
                    deleteIcon={<BugReportIcon />}
                    onDelete={() => null}
                  />
                </Link>
              </Tooltip>
            </Grid>
          )}
        </Grid>
      </Grid>

      {showHiddenFacts && (
        <HiddenFactsModal
          onClose={() => setShowHiddenFacts(false)}
          report={report}
        />
      )}
    </>
  );
};
