import Report from "src/models/Report";
import { Dialog } from "@ui-patterns/dialog";
import uniq from "lodash/uniq";
import {
  Autocomplete,
  Box,
  Divider,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import React, { useMemo, useState } from "react";
import { useUpdateSubmission } from "@queries/report";
import { emptyFn } from "@utils/helpers";
import { useMaybeCurrentUser } from "@utils/auth";
import {
  trackSubmissionStuck,
  trackSubmissionStuckChanged,
} from "@utils/amplitude";
import Submission from "@legacy/models/Submission";
import { generateUUID } from "@legacy/helpers/numberHelpers";
import { useCreateStuckSubmissionFeedbackMutation } from "@queries/submission";
import { AssignedReportUserDropdown } from "@features/submissions-queue/components/AssignedUserDropdown";
import {
  useCreateOrUpdateStuckDetailsMutation,
  useGetStuckDetailsQuery,
} from "@queries/stuckDetails";
import {
  StuckDetails,
  SubmissionProcessingStateEnum,
} from "@legacy/api_clients/copilot_api_client";
import { STATE_BUCKET_MAP } from "@features/pds-stuck-dialog/constants";
import { useOnStuckStore } from "@features/pds-stuck-dialog/context/onStuckAction";
import { LastAssignedPerson } from "@features/pds-stuck-dialog/components/LastAssignedPerson";
import { NaicsCodes } from "@features/submission-overview/components/NaicsCodes";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const SubmissionFileDropdown = ({
  selectedFile,
  setSelectedFile,
  submission,
}: {
  selectedFile?: string;
  setSelectedFile: (fileId: string) => void;
  submission: Submission;
}) => {
  const label = "Problematic File";
  return (
    <FormControl size="small" sx={{ width: "100%" }}>
      <InputLabel>{label}</InputLabel>
      <Select
        label={label}
        value={selectedFile}
        size="small"
        onChange={(event) => setSelectedFile(event.target.value)}
      >
        <MenuItem key="" id={generateUUID()}>
          None
        </MenuItem>
        {submission?.files?.map(({ id, name }) => (
          <MenuItem key={id} value={id}>
            {name}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

enum StuckIssueType {
  CONFIRMATION_INVALID_LOCATIONS = "Confirmation - Invalid Locations",
  CONFIRMATION_NO_MATCHING_LOCATIONS = "Confirmation - No Matching Locations",
  CONFIRMATION_PO_BOX = "Confirmation - PO Box",
  BROKER_CANT_FIND = "Broker - Can't find",
  SOV_PROCESSING_FAILED = "SOV - Processing Failed",
  VERIFICATION_FAILS = "Verification Fails",
  CONSTRUCTION_UNCLEAR_ENTITIES = "Construction - Unclear Entities",
  STUCK_IN_PROCESSING_OR_AUTOCONFIRMING = "Stuck In Processing/Autoconfirming",
  ACORD_INGESTION_ISSUES = "ACORD Ingestion Issues",
  FLEET_LIST_ISSUES = "Fleet List Issues",
  NAICS_CODE_ISSUES = "NAICS Code Issues",
  PARCEL_ADDRESS = "Parcel Address",
}

type SubmissionIsStuckDialogProps = {
  report: Report;
  onConfirm: () => void;
  onClose: () => void;
  wasCorrect: boolean;
  setWasCorrect: (wasCorrect: boolean) => void;
};

const SubmissionIsStuckDialog = ({
  report,
  onConfirm,
  onClose,
  wasCorrect,
  setWasCorrect,
}: SubmissionIsStuckDialogProps) => {
  const [resolution, setResolution] = useState("");
  const [problematicFileId, setProblematicFileId] = useState<string>();
  const [issue, setIssue] = useState("");
  const { mutate: createStuckSubmissionFeedback } =
    useCreateStuckSubmissionFeedbackMutation();

  const { isCSManager: canUnstuck } = useAccessPermissions();
  const submitStuckSubmissionFeedback = () => {
    const stuckSubmissionFeedback = {
      submission_id: report.getSubmission().id,
      file_id: problematicFileId,
      resolution: resolution,
      issue: issue,
      stuck_issue: report.getSubmission().stuck_reason,
    };
    createStuckSubmissionFeedback(stuckSubmissionFeedback);
  };

  return (
    <Dialog
      secondaryActionText="Cancel"
      primaryActionText="Unstuck"
      hideSecondary={!canUnstuck}
      hidePrimary={!canUnstuck}
      onClose={canUnstuck ? onClose : emptyFn}
      onPrimaryAction={() => {
        submitStuckSubmissionFeedback();
        onConfirm();
      }}
      fullWidth
    >
      <Typography variant="h5">
        {`Submission is stuck. ${
          canUnstuck ? "" : "Wait for Kalepa management to unblock."
        }`}
      </Typography>
      <Typography variant="h6" mt={2} mb={2}>
        Reported issue
      </Typography>
      <TextField
        multiline
        disabled
        value={report.getSubmission()?.stuck_reason}
        sx={{ width: "100%", alignSelf: "center" }}
      />
      {!report.getSubmission().is_naics_verified && (
        <>
          <Divider sx={{ my: 2 }} />
          <Typography variant="h6" mb={2}>
            NAICS Codes
          </Typography>
          <NaicsCodes />
        </>
      )}
      {canUnstuck && (
        <>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Typography variant="h6">
              Was it correct for the specialist to mark this submission as
              stuck?
            </Typography>
            <RadioGroup
              sx={{ display: "flex", flexDirection: "row" }}
              value={wasCorrect ? "yes" : "no"}
              onChange={(e) => setWasCorrect(e.target.value === "yes")}
            >
              <FormControlLabel value="yes" control={<Radio />} label="Yes" />
              <FormControlLabel value="no" control={<Radio />} label="No" />
            </RadioGroup>
          </Box>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Typography variant="h6">Feedback</Typography>
            <TextField
              title="Resolution"
              label="Resolution"
              size="small"
              fullWidth
              value={resolution}
              onChange={(e) => setResolution(e.target.value)}
            />
            <SubmissionFileDropdown
              submission={report.getSubmission()}
              selectedFile={problematicFileId}
              setSelectedFile={setProblematicFileId}
            />
            <Autocomplete
              fullWidth
              freeSolo
              filterSelectedOptions
              options={Object.values(StuckIssueType)}
              onInputChange={(event, value) => {
                setIssue(value);
              }}
              renderInput={(params) => (
                <TextField {...params} size="small" label="Issue" fullWidth />
              )}
            />
          </Box>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Typography variant="h6">Assign Specialist</Typography>
            <AssignedReportUserDropdown reportId={report.id} />
          </Box>
          <LastAssignedPerson />
        </>
      )}
    </Dialog>
  );
};

type PdsStuckDialogProps = {
  report: Report;
  onClose: () => void;
};

export const PdsStuckDialog = ({ report, onClose }: PdsStuckDialogProps) => {
  const [reason, setReason] = React.useState("");
  const [reasonBuckets, setReasonBuckets] = React.useState<string[]>(
    report?.getSubmission()?.isWaitingForBusinessConfirmation()
      ? STATE_BUCKET_MAP[SubmissionProcessingStateEnum.BusinessConfirmation]
      : []
  );
  const [wasCorrect, setWasCorrect] = React.useState(true);

  const title =
    "In addition to providing a more thorough explanation, please check the boxes for the problematic areas.";
  const submissionId = report?.getSubmission().id;

  const { mutate: updateSubmission } = useUpdateSubmission();
  const { mutate: updateStuckDetails, isLoading: isUpdatingStuckDetails } =
    useCreateOrUpdateStuckDetailsMutation();
  const { data: currentStuckDetails } = useGetStuckDetailsQuery(submissionId);
  const user = useMaybeCurrentUser();

  const [isConfirming, setIsConfirming] = useState(false);

  const bucketOptions = useMemo(() => {
    const processingState = report?.getSubmission().processing_state ?? "";
    const buckets =
      processingState in STATE_BUCKET_MAP
        ? STATE_BUCKET_MAP[processingState]
        : [];
    return buckets.map((x) => ({
      label: `${x}`,
      value: `${x}`,
    }));
  }, [report]);
  const onStuckStore = useOnStuckStore();

  if (!report) return null;

  const isCurrentlyStuck = !!report.getSubmission()?.stuck_reason;

  const onConfirm = async () => {
    setIsConfirming(true);
    const reasonWithUsername = reason ? `User: ${user?.email}. ${reason}` : "";
    if (!isCurrentlyStuck) {
      const stuckDetails: StuckDetails = {
        stuck_by: user?.id,
        stuck_at: new Date().toISOString(),
        stuck_reason_buckets: reasonBuckets,
        stuck_reason_explanation: reason,
        processing_state: report.getSubmission().processing_state,
        submission_id: submissionId,
      };
      updateStuckDetails({ submissionId, stuckDetails });
      await onStuckStore
        ?.getState()
        .onStuck()
        .finally(() => {
          setIsConfirming(false);
        });
    } else {
      const stuckDetailsItem = currentStuckDetails?.find((x) => !x.unstuck_at);
      if (stuckDetailsItem) {
        const stuckDetails = {
          id: stuckDetailsItem.id,
          submission_id: submissionId,
          was_correct_to_stuck: wasCorrect,
        };
        updateStuckDetails({ submissionId, stuckDetails });
      }

      setIsConfirming(false);
    }

    updateSubmission({
      submissionId,
      reportId: report.id,
      data: {
        stuck_reason: reasonWithUsername,
      },
    });
    trackSubmissionStuck(report, {
      stuckReason: reason,
      issue: reasonBuckets,
      stucking_user: user?.email ?? "",
    });
    trackSubmissionStuckChanged(report, {});
    onClose();
  };

  if (isCurrentlyStuck) {
    return (
      <SubmissionIsStuckDialog
        report={report}
        onConfirm={onConfirm}
        onClose={onClose}
        wasCorrect={wasCorrect}
        setWasCorrect={setWasCorrect}
      />
    );
  }

  return (
    <Dialog
      title={title}
      onClose={onClose}
      primaryActionText={
        isConfirming || isUpdatingStuckDetails ? "Confirming..." : "Confirm"
      }
      disablePrimary={
        !reason ||
        !reasonBuckets.length ||
        isConfirming ||
        isUpdatingStuckDetails
      }
      secondaryActionText="Cancel"
      onSecondaryAction={onClose}
      onPrimaryAction={onConfirm}
      maxWidth="md"
      fullWidth
      rawContent={
        <Stack display={"flex"} flexDirection="column" p={2} spacing={2}>
          <Autocomplete
            disablePortal
            multiple
            options={bucketOptions}
            value={reasonBuckets.map((x) => ({
              label: `${x}`,
              value: `${x}`,
            }))}
            limitTags={1}
            onChange={(event, values) =>
              setReasonBuckets(uniq(values.map((v) => v.value)))
            }
            renderInput={(params) => (
              <TextField {...params} size="small" label="Issue" fullWidth />
            )}
          />
          <TextField
            multiline
            size={"small"}
            rows={2}
            label={"Explanation (required):"}
            onChange={(e) => setReason(e.target.value)}
          />
        </Stack>
      }
    />
  );
};
