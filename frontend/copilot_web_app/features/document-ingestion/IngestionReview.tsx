import React, { useEffect, useMemo, useRef } from "react";
import { useFileOnboardedData } from "@queries/ingestion";
import { useRouter } from "next/router";
import { useReport } from "@utils/useReport";
import { useOnboardingDataContext } from "@features/pds-data-model/context/OnboardingDataContextData";
import { UseFormReturn } from "react-hook-form";
import { FeOnboardedFile } from "@features/pds-data-model/types";
import { useMemoArray } from "@utils/useMemoArray";
import { BeToFeMappingContext } from "@features/pds-data-model/converters/types";
import { useFinancialFactsContext } from "@features/data-onboarding/FinancialFactsContext";
import { useFactSubtypeContext } from "@features/entity-mapping/context/FactSubtypeContext";
import { NewOnboardingDataFormProvider } from "@features/pds-data-model/form/NewOnboardingDataFormProvider";
import { FixedFactsContextProvider } from "@features/data-onboarding/financials-onboarding/FixedFactsStore";
import { Box, Button, Divider } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { IngestionTabs } from "@features/document-ingestion/components/IngestionTabs";
import Link from "next/link";
import { routes } from "@features/routes";
import { useIngestionDataContext } from "@features/document-ingestion/IngestionDataContext";
import { Vertical } from "@features/document-ingestion/components/viewTypes/Vertical";
import { Horizontal } from "@features/document-ingestion/components/viewTypes/Horizontal";
import { IngestionStateResolver } from "@features/document-ingestion/components/IngestionStateResolver";
import { Financial } from "@features/document-ingestion/components/viewTypes/Financial";
import { Losses } from "@features/document-ingestion/components/viewTypes/Losses";
import { Ifta } from "@features/document-ingestion/components/viewTypes/Ifta";
import { WorkersComp } from "@features/document-ingestion/components/viewTypes/WorkersComp";
import { AddEvidenceHandler } from "@features/document-ingestion/AddEvidenceHandler";
import { useDifferentOnboardedFileMapping } from "@features/document-ingestion/hooks/useDifferentOnboardedFileMapping";
import { VerticalOnboardedFile } from "@features/document-ingestion/components/viewTypes/VerticalOnboardedFile";
import { useCustomFieldsToAdd } from "@features/document-ingestion/hooks/useCustomFieldsToAdd";
import { useIsSubmissionQueueReviewIntention } from "@features/document-ingestion/hooks/useIsSubmissionQueueReviewIntention";
import { TopActionBar } from "@features/document-ingestion/components/TopActionBar";
import { BottomDrawer } from "./components/BottomDrawer";
import { EVENT_BUS } from "@features/events";
import { useIsDocumentIngestion } from "@features/files/hooks/useIsDocumentIngestion";
import { useLeavePageWarning } from "@features/product-driven-support/state";

export const IngestionReview = () => {
  const router = useRouter();
  const report = useReport();
  const submissionId = report?.getSubmission()?.id;
  const fileId = router.query.fileId as string;
  const isSubmissionQueue = useIsSubmissionQueueReviewIntention();
  useLeavePageWarning(false, true);

  const { data: fileOnboardedData } = useFileOnboardedData(fileId);

  const setOnboardedFile = useOnboardingDataContext().use.setOnboardedFile();
  const setActiveFileId = useOnboardingDataContext().use.setActiveFileId();
  const setIsViewEditable = useIngestionDataContext().use.setIsViewEditable();

  const viewType = useIngestionDataContext().use.viewType();
  const isDocumentIngestion = useIsDocumentIngestion();

  const formMethods = useRef<UseFormReturn<FeOnboardedFile> | null>(null);
  const allReportFileIds = useMemoArray(
    report.submission.files.map((f) => f.id)
  );
  const currentFile = report.submission.getFileById(fileId);
  const isCurrentFileAcord131 = currentFile?.isAcordForm("ACORD_131");

  useEffect(() => {
    setActiveFileId(fileId);
  }, [fileId, setActiveFileId]);
  const doesFileOnboardedDataHaveData =
    (fileOnboardedData?.fields ?? []).length ||
    (fileOnboardedData?.entity_information ?? []).length;

  useEffect(() => {
    const isFileEditable =
      isSubmissionQueue ||
      currentFile?.processing_state === "WAITING_FOR_ENTITY_MAPPING" ||
      currentFile?.processing_state === "WAITING_FOR_DATA_ONBOARDING";

    setIsViewEditable(isFileEditable);
  }, [currentFile?.processing_state, isSubmissionQueue, setIsViewEditable]);

  const { snapshotFactSubtypeIds } = useFinancialFactsContext();
  const { factSubtypesMap } = useFactSubtypeContext();
  const { customOnboardedFile } = useDifferentOnboardedFileMapping({
    isEnabled:
      (!fileOnboardedData || !doesFileOnboardedDataHaveData) &&
      currentFile?.file_type === "Workers Compensation Experience Modifier",
    file: currentFile,
  });

  useEffect(() => {
    const doesFileOnboardedDataHaveData =
      (fileOnboardedData?.fields ?? []).length ||
      (fileOnboardedData?.entity_information ?? []).length;
    if (
      fileOnboardedData &&
      (!customOnboardedFile || doesFileOnboardedDataHaveData)
    ) {
      setOnboardedFile(fileOnboardedData);
    } else if (customOnboardedFile) {
      setOnboardedFile(customOnboardedFile);
    }
  }, [customOnboardedFile, fileOnboardedData, setOnboardedFile]);

  const customFieldsToAdd = useCustomFieldsToAdd(currentFile);

  const mappingContext = useMemo((): BeToFeMappingContext => {
    return {
      experiments: [],
      submissionId,
      snapshotTimeSeriesFactSubtypes: snapshotFactSubtypeIds,
      factSubtypesMap,

      // TODO: this is more complex for DI
      // table view would rather have simplified TS
      // CFS file - probably a full time series
      doSimplifiedTimeSeriesLoading: false,

      skipNameAndAddressValidation: true,
      skipValidation: true,
      useObservedValues: isDocumentIngestion,
      keepDescription: true,
      allReportFileIds,
      addCustomFields: customFieldsToAdd,
      allowToEditTransientData: isCurrentFileAcord131,
    };
  }, [
    allReportFileIds,
    customFieldsToAdd,
    factSubtypesMap,
    isCurrentFileAcord131,
    isDocumentIngestion,
    snapshotFactSubtypeIds,
    submissionId,
  ]);

  useEffect(() => {
    EVENT_BUS.emit("tracking/action/generic", {
      name: "file review started",
      props: {
        intention: isSubmissionQueue
          ? "submission-queue"
          : "document-ingestion",
      },
    });
  }, [isSubmissionQueue]);

  return (
    <>
      <Box px={3} display="flex" flexDirection="column" flexGrow={1} mb={6}>
        {!isSubmissionQueue && (
          <Box>
            <Link
              href={routes.reportSection(report.id, "all-files")}
              legacyBehavior
              passHref
            >
              <Button startIcon={<ArrowBackIcon />}>All files</Button>
            </Link>
          </Box>
        )}
        <Divider />
        <FixedFactsContextProvider>
          <NewOnboardingDataFormProvider
            context={mappingContext}
            formMethodsRef={formMethods}
            allowZeroEntites
            allowZeroFiles
            allowToLoadWithZeroData={!fileOnboardedData}
          >
            <IngestionStateResolver />
            <AddEvidenceHandler />
            <Box
              display="flex"
              flexDirection="row"
              justifyContent="space-between"
            >
              <IngestionTabs />
              <TopActionBar />
            </Box>
            {viewType === "vertical" && <Vertical />}
            {viewType === "horizontal" && <Horizontal />}
            {viewType === "financial" && <Financial />}
            {viewType === "loss-runs" && <Losses />}
            {viewType === "ifta" && <Ifta />}
            {viewType === "workers-comp" && <WorkersComp />}
            {viewType === "directors-and-officers" && <VerticalOnboardedFile />}
            <BottomDrawer />
          </NewOnboardingDataFormProvider>
        </FixedFactsContextProvider>
      </Box>
    </>
  );
};
