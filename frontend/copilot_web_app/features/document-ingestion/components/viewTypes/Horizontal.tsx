import React from "react";
import { Resizer } from "@features/entity-mapping-v2/components/Resizer";
import { VerticalTableUi } from "@features/entity-mapping-v2/components/VerticalTable/VerticalTableUi";
import { useSelectedTab } from "@features/entity-mapping-v2/utils";
import { useEntityMappingV2Store } from "@features/entity-mapping-v2/EntityMappingV2State";
import { useStore } from "zustand";
import { useCurrentFile } from "@features/document-ingestion/hooks/useCurrentFile";
import { useNewOnboardingDataForm } from "@features/pds-data-model/form";
import { FilePreview } from "@features/files/components/FilePreview";
import { EvidenceIndicator } from "@features/document-ingestion/components/EvidenceIndicator";
import { FieldsMapping } from "@features/data-onboarding/fields-mapping";
import {
  ModelFileFileTypeEnum,
  SubmissionEntityTypeEnum,
} from "@legacy/api_clients/copilot_api_client";
import { useIsSubmissionQueueReviewIntention } from "@features/document-ingestion/hooks/useIsSubmissionQueueReviewIntention";

export const Horizontal = () => {
  const tab = useSelectedTab();
  const { fileId, file, report } = useCurrentFile();
  const store = useEntityMappingV2Store();
  const isSubmissionQueue = useIsSubmissionQueueReviewIntention();

  const fieldIds: Set<string> | undefined = useStore(
    store,
    (state) => state.entityFields[tab?.feEntityId ?? ""]?.[fileId]
  );

  const fieldIdsInOrder = Array.from(fieldIds ?? []);
  const { getValues } = useNewOnboardingDataForm();
  const entityType = getValues().entities[tab?.feEntityId ?? ""]?.entity?.type;
  const isBusiness = entityType === "Business";
  const isStructure = entityType === "Structure";
  const isWorkersCompPayroll = [
    ModelFileFileTypeEnum.WorkersCompensationPayroll,
    ModelFileFileTypeEnum.WorkersCompensationExperienceModifier,
  ].includes((file?.file_type ?? "") as any);

  return (
    <>
      {tab && (
        <Resizer
          skipPaddingBottom
          leftChild={
            <>
              <VerticalTableUi
                fieldsInOrder={fieldIdsInOrder}
                tab={tab}
                disableStickyFields
                showFieldValueOnlyForCurrentFile={false}
                extraInputAction={<EvidenceIndicator />}
                blockCopySwapActions
                allowValueRemoval={!isWorkersCompPayroll}
                allowDateFormatter={isWorkersCompPayroll}
              />
              {!isSubmissionQueue && (isBusiness || isStructure) && (
                <FieldsMapping
                  allowedEntities={[
                    isBusiness
                      ? SubmissionEntityTypeEnum.Business
                      : SubmissionEntityTypeEnum.Structure,
                  ]}
                  hideSourcesIndicator
                  disableAddField
                  hideToTableNavigation
                />
              )}
            </>
          }
          rightChild={
            !!file ? (
              <FilePreview
                file={file!}
                showBack={false}
                height="calc(100% - 15px)"
                report={report}
                showEvidences
              />
            ) : undefined
          }
        />
      )}
    </>
  );
};
