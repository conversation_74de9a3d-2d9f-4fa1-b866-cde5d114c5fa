import { useMemo, useState, type JSX } from "react";
import {
  Box,
  CircularProgress,
  InputAdornment,
  Stack,
  Tab,
  Table,
  TableBody,
  TableContainer,
  TablePagination,
  Tabs,
  TextField,
  Typography,
} from "@mui/material";
import {
  FileTableHeader,
  ReportTableHeader,
} from "@features/submissions-queue/components/TableHeader";
import { ReportRow } from "@features/submissions-queue/components/ReportRow";
import Report from "@legacy/models/Report";
import SearchIcon from "@mui/icons-material/Search";
import { emptyFn } from "@utils/helpers";
import ReactDOM from "react-dom";
import {
  SubmissionQueueTabTypeEnum,
  useGetFilesQueue,
  useGetSubmissionsQueue,
} from "@queries/submissionAssignment";
import { FileRow } from "@features/submissions-queue/components/FileRow";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const DEFAULT_PAGE_SIZE = 25;
const TABS: {
  id: SubmissionQueueTabTypeEnum;
  name: string;
}[] = [
  {
    id: SubmissionQueueTabTypeEnum.main,
    name: "All",
  },
  {
    id: SubmissionQueueTabTypeEnum.files,
    name: "Files",
  },
  {
    id: SubmissionQueueTabTypeEnum.stuck,
    name: "Stuck",
  },
  {
    id: SubmissionQueueTabTypeEnum.escalated,
    name: "Escalated",
  },
  {
    id: SubmissionQueueTabTypeEnum.missingFiles,
    name: "Missing Files",
  },
  {
    id: SubmissionQueueTabTypeEnum.invalidFiles,
    name: "Invalid Files",
  },
  {
    id: SubmissionQueueTabTypeEnum.missingData,
    name: "Missing Data",
  },
  {
    id: SubmissionQueueTabTypeEnum.needsClearing,
    name: "Needs Clearing",
  },
  {
    id: SubmissionQueueTabTypeEnum.autoAssignment,
    name: "Auto Assignment",
  },
  {
    id: SubmissionQueueTabTypeEnum.shadow,
    name: "Shadow",
  },
];

export const ReportsQueue = ({
  paginationContainer,
}: {
  paginationContainer?: HTMLDivElement;
}): JSX.Element => {
  const [searchName, setSearchName] = useState<string>("");
  const [perPage, setPerPage] = useState<number>(DEFAULT_PAGE_SIZE);
  const [page, setPage] = useState<number>(1);
  const { isCSManager } = useAccessPermissions();
  const [selectedTab, setSelectedTab] = useState<string>(
    isCSManager ? "main" : "stuck"
  );

  const queueParams = useMemo(() => {
    return {
      page: page ? Number(page) : 1,
      perPage: perPage ? Number(perPage) : DEFAULT_PAGE_SIZE,
      name: searchName,
    };
  }, [searchName, page, perPage]);

  const { data: reportsPerTab, isLoading } = useGetSubmissionsQueue(
    queueParams,
    TABS.filter((x) => x.id !== SubmissionQueueTabTypeEnum.files).map(
      (t) => t.id
    )
  );

  const { data: files } = useGetFilesQueue({
    page: page ? Number(page) : 1,
    perPage: perPage ? Number(perPage) : DEFAULT_PAGE_SIZE,
  });

  const totalItems =
    selectedTab === SubmissionQueueTabTypeEnum.files
      ? files?.total_files
      : reportsPerTab[selectedTab]?.total_reports;

  const hasNoItems = !isLoading && !reportsPerTab[selectedTab]?.reports?.length;
  const withStuckReason = [
    SubmissionQueueTabTypeEnum.stuck,
    SubmissionQueueTabTypeEnum.invalidFiles,
    SubmissionQueueTabTypeEnum.missingFiles,
    SubmissionQueueTabTypeEnum.missingData,
    SubmissionQueueTabTypeEnum.escalated,
  ].includes(selectedTab as SubmissionQueueTabTypeEnum);

  return (
    <Box display="flex" flexDirection="column">
      <Stack direction="row" spacing={1} ml={6} mb={3}>
        <TextField
          label="Search by Name"
          onChange={(event) => setSearchName(event.target.value)}
          value={searchName}
          onKeyDown={emptyFn}
          InputProps={{
            sx: { width: 440 },
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon onClick={emptyFn} />
              </InputAdornment>
            ),
          }}
        />
      </Stack>
      <Tabs
        value={selectedTab}
        onChange={(_, value: string) => setSelectedTab(value)}
        variant="scrollable"
        scrollButtons="auto"
        sx={{ ml: 2 }}
      >
        {TABS.map((tab) => {
          const count =
            tab.id === SubmissionQueueTabTypeEnum.files
              ? files?.total_files
              : reportsPerTab[tab.id]?.total_reports;

          return (
            <Tab
              key={tab.id}
              value={tab.id}
              label={`${tab.name} (${count ?? 0})`}
            />
          );
        })}
      </Tabs>

      <TableContainer>
        <Table
          stickyHeader
          width={300}
          sx={{
            position: "relative",
            height: undefined,
          }}
        >
          {isLoading && (
            <Box
              position="absolute"
              width="100%"
              mt={7}
              display="flex"
              justifyContent="center"
            >
              <CircularProgress />
            </Box>
          )}
          {selectedTab === SubmissionQueueTabTypeEnum.files ? (
            <>
              <FileTableHeader />
              <TableBody>
                {files?.files?.map((file) => (
                  <FileRow
                    reportId={
                      (files.submission_id_to_report_id as any)[
                        file.submission_id
                      ]
                    }
                    key={file.id}
                    file={file}
                  />
                ))}
              </TableBody>
            </>
          ) : (
            <>
              <ReportTableHeader withStuckReason={withStuckReason} />
              <TableBody>
                {reportsPerTab[selectedTab]?.reports?.map((report: Report) => (
                  <ReportRow
                    key={report.id}
                    report={report}
                    withStuckReason={withStuckReason}
                  />
                ))}
              </TableBody>
            </>
          )}
        </Table>
      </TableContainer>
      {hasNoItems && (
        <Typography fontWeight="bold" sx={{ textAlign: "center", mt: 4 }}>
          No items found
        </Typography>
      )}
      {paginationContainer &&
        ReactDOM.createPortal(
          <TablePagination
            count={totalItems ?? 0}
            page={page ? Number(page) - 1 : 0}
            rowsPerPage={perPage ? Number(perPage) : DEFAULT_PAGE_SIZE}
            rowsPerPageOptions={[DEFAULT_PAGE_SIZE, 50, 75, 100]}
            onRowsPerPageChange={(e) => {
              setPage(1);
              setPerPage(Number(e.target.value));
            }}
            onPageChange={(_, page) => {
              setPage(page + 1);
            }}
          />,
          paginationContainer
        )}
    </Box>
  );
};
