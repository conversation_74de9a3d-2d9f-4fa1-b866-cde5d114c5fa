import {
  <PERSON>,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  IconButton,
  Link,
  Radio,
  Stack,
  TableCell,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useMemo, type JSX } from "react";

import { CellText } from "@features/item-list";
import Report from "@legacy/models/Report";
import startCase from "lodash/startCase";
import { formatDateTime, prettify } from "@legacy/helpers/reportHelpers";
import {
  useCreateSubmissionPriority,
  useGetCurrentSupportUser,
} from "@queries/submissionAssignment";
import { useGetOrganizations } from "@queries/organizations";
import { useCurrentUser } from "@utils/auth";
import {
  SubmissionMissingDataStatusEnum,
  SubmissionPriority,
} from "@legacy/api_clients/copilot_api_client";
import { routes } from "@features/routes";
import { useRouter } from "next/router";
import { useUpdateUserMutation } from "@queries/user";
import { useUpdateSubmission } from "@queries/report";
import SaveIcon from "@mui/icons-material/Save";
import { AssignedReportUserDropdown } from "@features/submissions-queue/components/AssignedUserDropdown";
import { Dialog } from "@ui-patterns/dialog";
import { SupportModeButton } from "@features/submissions-queue/components/SupportModeButton";
import { useClientStage } from "@legacy/hooks/useClientStage";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const StuckReasonCell = ({
  report,
  updateOrgIfNeeded,
}: {
  report: Report;
  updateOrgIfNeeded: () => void;
}): JSX.Element => {
  const [stuckReason, setStuckReason] = React.useState<string>();
  const [updating, setUpdating] = React.useState(false);

  const { mutate: updateSubmission } = useUpdateSubmission();

  const reportStuckReason = report.getSubmission()?.stuck_reason ?? "";

  const onStuckReasonSave = async () => {
    setUpdating(true);
    await updateOrgIfNeeded();
    updateSubmission(
      {
        submissionId: report.getSubmission().id,
        reportId: report.id,
        data: {
          stuck_reason: stuckReason,
        },
      },
      {
        onSuccess: async () => {
          setStuckReason(undefined);
          setUpdating(false);
        },
      }
    );
  };

  if (updating) {
    return (
      <CellText>
        <CircularProgress />
      </CellText>
    );
  }

  return (
    <Stack direction="row">
      <TextField
        style={{ width: "100%" }}
        multiline
        size="small"
        value={stuckReason ?? reportStuckReason}
        onDoubleClick={(e) => e.stopPropagation()}
        onChange={(e) => {
          setStuckReason(e.target.value);
        }}
      />
      {stuckReason !== undefined && (
        <IconButton onClick={onStuckReasonSave}>
          <SaveIcon />
        </IconButton>
      )}
    </Stack>
  );
};

export const ReportRow = ({
  report,
  withStuckReason = true,
}: {
  report: Report;
  withStuckReason?: boolean;
}): JSX.Element | null => {
  const [showChangeQueueViewModal, setShowChangeQueueViewModal] =
    React.useState(false);

  const { getStageName } = useClientStage();

  const submission = report.getSubmission();
  const [missingDataStatus, setMissingDataStatus] =
    React.useState<SubmissionMissingDataStatusEnum | null>(
      submission.missing_data_status as SubmissionMissingDataStatusEnum
    );
  const [isEscalated, setIsEscalated] = React.useState(
    !!submission.is_escalated
  );
  const [showModeWarning, setShowModeWarning] = React.useState(false);
  const { data: organizations } = useGetOrganizations();
  const { mutate: updatePiority, isLoading: isCreating } =
    useCreateSubmissionPriority();

  const user = useCurrentUser();
  const { data: currentSupportUser } = useGetCurrentSupportUser();
  const router = useRouter();
  const { mutateAsync: updateUser, isLoading: isUpdatingUser } =
    useUpdateUserMutation();
  const { mutate: updateSubmission, isLoading: isUpdatingSubmission } =
    useUpdateSubmission();

  const { isCSManager } = useAccessPermissions();

  const updateOrgIfNeeded = async () => {
    if (user?.organization.id !== report.organization_id) {
      await updateUser({
        userId: user.id,
        userUpdate: {
          organization_id: report.organization_id,
        },
      });
    }
  };

  const handleEngineeringStuckChange = async () => {
    await updateOrgIfNeeded();
    updateSubmission({
      submissionId: submission.id,
      reportId: report.id,
      data: {
        is_stuck_engineering: !submission.is_stuck_engineering,
      },
    });
  };

  const handleMissingDataStatusChange = async () => {
    setShowChangeQueueViewModal(false);
    await updateOrgIfNeeded();
    updateSubmission({
      submissionId: submission.id,
      reportId: report.id,
      data: {
        missing_data_status:
          missingDataStatus as SubmissionMissingDataStatusEnum,
        is_escalated: isEscalated,
      },
    });
  };

  const orgName = useMemo(() => {
    return organizations?.find((org) => org.id === report.organization_id)
      ?.name;
  }, [organizations, report.organization_id]);

  if (!submission || !organizations) {
    return null;
  }

  const handlePriorityChanged = () => {
    updatePiority({
      submissionId: submission.id,
      submissionPriority: {
        escalated_at: submission.isEscalated()
          ? null
          : new Date().toISOString(),
      },
    });
  };

  const handleRemovedChanged = () => {
    const submissionPriority: SubmissionPriority = {
      removed_from_auto_assign: !submission.isRemovedFromAutoAssign(),
    };
    if (!submission.isRemovedFromAutoAssign()) {
      submissionPriority.escalated_at = null;
    }
    updatePiority({
      submissionId: submission.id,
      submissionPriority,
    });
  };

  const onReportClick = () => {
    if (isCSManager && currentSupportUser?.can_load_submissions) {
      setShowModeWarning(true);
      return;
    }
    if (user?.organization.id !== report.organization_id) {
      updateUser(
        {
          userId: user.id,
          userUpdate: {
            organization_id: report.organization_id,
          },
        },
        {
          async onSuccess() {
            await router.push(routes.report(report.id));
          },
        }
      );
    } else {
      router.push(routes.report(report.id));
    }
  };

  const toggleStuckModal = () => {
    setShowChangeQueueViewModal(!showChangeQueueViewModal);
  };

  if (isCreating || isUpdatingUser || isUpdatingSubmission) {
    return (
      <Box display="flex" justifyContent="center">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <TableRow sx={{ display: "flex" }} onDoubleClick={onReportClick}>
      <TableCell
        width={100}
        sx={{ "&&": { paddingTop: 1 } }}
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          color="primary"
          checked={submission.isEscalated()}
          disabled={submission.isRemovedFromAutoAssign()}
          onClick={handlePriorityChanged}
        />
      </TableCell>
      <TableCell
        width={100}
        sx={{ "&&": { paddingTop: 1 } }}
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          color="primary"
          checked={submission.isRemovedFromAutoAssign()}
          onClick={handleRemovedChanged}
        />
      </TableCell>
      <TableCell width={150}>
        <Box>
          <CellText>{orgName}</CellText>
        </Box>
      </TableCell>
      <TableCell width={100}>
        <Box>
          <CellText>{report.tier ?? "-"}</CellText>
        </Box>
      </TableCell>
      <TableCell width={200}>
        <Box>
          <CellText>{formatDateTime(report.created_at)}</CellText>
        </Box>
      </TableCell>
      <TableCell width={250}>
        <Tooltip title={report.name ?? "-"}>
          <Box>
            <CellText>{report.name ?? "-"}</CellText>
          </Box>
        </Tooltip>
      </TableCell>
      <TableCell width={150}>
        <Box>
          <CellText>{getStageName(report)}</CellText>
        </Box>
      </TableCell>
      <TableCell width={300}>
        <Box>
          <AssignedReportUserDropdown reportId={report.id} isFromQueue={true} />
        </Box>
      </TableCell>
      <TableCell width={150}>
        <Box>
          <CellText>
            {startCase(
              submission.processing_state?.replace(/_/g, " ").toLowerCase() ??
                "-"
            )}
          </CellText>
        </Box>
      </TableCell>
      <TableCell width={200}>
        <Box>
          <CellText>{formatDateTime(report.last_assigned_at)}</CellText>
        </Box>
      </TableCell>
      <TableCell width={100}>
        <Box>
          <CellText>{submission.stuck_reason ? "Yes" : "No"}</CellText>
        </Box>
      </TableCell>
      {withStuckReason && (
        <>
          <TableCell width={300}>
            <Box>
              <CellText>
                <StuckReasonCell
                  report={report}
                  updateOrgIfNeeded={updateOrgIfNeeded}
                />
              </CellText>
            </Box>
          </TableCell>
          <TableCell
            width={200}
            sx={{ "&&": { paddingTop: 1 } }}
            onClick={(e) => e.stopPropagation()}
          >
            <Checkbox
              color="primary"
              checked={!!submission.is_stuck_engineering}
              onClick={handleEngineeringStuckChange}
            />
          </TableCell>
        </>
      )}
      {withStuckReason && (
        <TableCell width={200} onClick={(e) => e.stopPropagation()}>
          <Link sx={{ cursor: "pointer" }} onClick={toggleStuckModal}>
            Change Queue View
          </Link>
        </TableCell>
      )}
      {showModeWarning && (
        <Dialog onClose={() => setShowModeWarning(false)} hidePrimary={true}>
          <Typography variant="h6" py={3}>
            If you want to unstuck submissions, please switch to the appropriate
            mode
          </Typography>
          <SupportModeButton />
        </Dialog>
      )}
      {showChangeQueueViewModal && (
        <Dialog
          title="Change Queue View"
          onClose={toggleStuckModal}
          primaryActionText="Confirm"
          onSecondaryAction={toggleStuckModal}
          onPrimaryAction={handleMissingDataStatusChange}
          maxWidth="sm"
          fullWidth
        >
          <Box>
            <Typography variant="subtitle1">
              Additional information for the missing data?
            </Typography>
            <FormControlLabel
              control={
                <Radio
                  checked={!missingDataStatus}
                  onClick={() => setMissingDataStatus(null)}
                />
              }
              label="None"
            />
            {Object.values(SubmissionMissingDataStatusEnum).map((s) => (
              <Box key={s}>
                <FormControlLabel
                  control={
                    <Radio
                      checked={missingDataStatus === s}
                      onClick={() => setMissingDataStatus(s)}
                    />
                  }
                  label={prettify(s)}
                />
              </Box>
            ))}
            <Typography variant="subtitle1" mt={1}>
              Should be escalated?
            </Typography>
            <FormControlLabel
              control={
                <Radio
                  checked={isEscalated}
                  onClick={() => setIsEscalated(!isEscalated)}
                />
              }
              label="Escalate"
            />
          </Box>
        </Dialog>
      )}
    </TableRow>
  );
};
