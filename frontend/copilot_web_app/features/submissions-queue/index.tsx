import { ReportsQueue } from "@features/submissions-queue/components/ReportsQueue";
import { useCurrentUser } from "@utils/auth";
import { Box, Button, styled, Typography } from "@mui/material";
import React, { useState } from "react";
import { SpecialistsDialog } from "@features/submissions-queue/components/SpecialistsDialog";
import { SupportModeButton } from "@features/submissions-queue/components/SupportModeButton";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const Wrapper = styled("div")(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  flex: "1 1 0",
  overflow: "hidden",
  backgroundColor: theme.palette.background.paper,
}));

export const SubmissionsQueue = () => {
  const [paginationContainer, setPaginationContainer] =
    useState<HTMLDivElement>();
  const [showSpecialistsDialog, setShowSpecialistsDialog] =
    useState<boolean>(false);

  const user = useCurrentUser();
  const { isAtLeastTier2 } = useAccessPermissions();
  const { isCSManager } = useAccessPermissions();

  if (!isCSManager || !user?.cross_organization_access) {
    return null;
  }

  return (
    <Wrapper>
      <Wrapper sx={{ overflowY: "auto", position: "relative" }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography m={3} p={3} variant="h3">
            Support Submission Queue
          </Typography>
          <Box>
            {isAtLeastTier2 && <SupportModeButton />}
            <Button
              variant="outlined"
              size="small"
              onClick={() => setShowSpecialistsDialog(true)}
              sx={{ mx: 1 }}
            >
              Edit support specialists
            </Button>
          </Box>
        </Box>
        <ReportsQueue paginationContainer={paginationContainer} />
      </Wrapper>
      <Box
        sx={{ width: "100%", display: "flex", justifyContent: "flex-end" }}
        id="list-pagination"
        ref={(ref) => {
          setPaginationContainer(ref as HTMLDivElement);
        }}
      />
      {showSpecialistsDialog && (
        <SpecialistsDialog onClose={() => setShowSpecialistsDialog(false)} />
      )}
    </Wrapper>
  );
};
