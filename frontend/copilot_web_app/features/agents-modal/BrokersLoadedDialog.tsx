import React, { useEffect, useMemo, useState } from "react";
import { Dialog } from "@ui-patterns/dialog";
import { Box } from "@mui/material";
import {
  useCreateBrokerageEmployeeMutation,
  useCreateBrokerageMutation,
} from "@queries/agency";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { SearchField } from "@features/agents-modal/components/SearchField";
import { AgentModalFormData, SearchOption } from "@features/agents-modal/types";
import { AgencyAgentForm } from "@features/agents-modal/components/AgencyAgentForm";
import { FormProvider, useForm } from "react-hook-form";
import { useUpdateSubmission } from "@queries/report";
import Report from "@legacy/models/Report";
import { useSnackbar } from "notistack";
import {
  Brokerage,
  BrokerageEmployee,
} from "@legacy/api_clients/copilot_api_client";
import { agencyToOption } from "@features/agents-modal/util";
import {
  trackAgentModalSearched,
  trackBrokerageAdded,
  trackBrokerageEmployeeAdded,
  trackSubmissionEdited,
} from "@utils/amplitude";
import copilotApiClient from "@legacy/clients/copilotApiClient";
import {
  SimilarBrokerages,
  SimilarBrokeragesModal,
} from "@features/agents-modal/components/SimilarBrokeragesModal";
import {
  SimilarBrokers,
  SimilarBrokersModal,
} from "@features/agents-modal/components/SimilarBrokersModal";
import { useAccessPermissions } from "@features/product-driven-support/utils";

type Props = {
  onClose: (shouldTrack?: boolean) => void;
  report: Report;
  brokers: BrokerageEmployee[];
  brokerages: Brokerage[];
  onSave?: (brokerage: Brokerage, broker: BrokerageEmployee) => void;
};

export const BrokersLoadedDialog = ({
  onClose,
  report,
  brokers,
  brokerages,
  onSave,
}: Props) => {
  const [searchOption, setSearchOption] = useState<SearchOption | null>();
  const organizationId = useOrganizationId();
  const { enqueueSnackbar } = useSnackbar();
  const user = useCurrentUser();
  const [similarBrokerages, setSimilarBrokerages] =
    useState<SimilarBrokerages | null>(null);
  const [similarBrokers, setSimilarBrokers] = useState<SimilarBrokers | null>(
    null
  );
  const brokerageOptions = useMemo(
    () => brokerages.map(agencyToOption),
    [brokerages]
  );
  const allBrokerOptions: SearchOption[] = useMemo(() => {
    return brokers.map((broker) => ({
      type: "agent",
      label: broker.email ? `${broker.name} (${broker.email})` : broker.name!,
      value: broker.id!,
      agencyId: broker.brokerage_id!,
      email: broker.email,
      name: broker.name!,
    }));
  }, [brokers]);

  const { isCSManager } = useAccessPermissions();

  const { mutateAsync: createBrokerage, isLoading: isCreatingBrokerage } =
    useCreateBrokerageMutation();

  const { mutateAsync: createBroker, isLoading: isCreatingBroker } =
    useCreateBrokerageEmployeeMutation();
  const { mutateAsync: updateSubmission, isLoading: isUpdatingSubmission } =
    useUpdateSubmission();

  const defaultValues = useMemo(() => {
    const reportBroker =
      allBrokerOptions.find(
        (broker) => broker.value === report.getSubmission().broker_id
      ) ?? null;
    const reportBrokerage =
      brokerageOptions.find(
        (brokerage) => brokerage.value === report.getSubmission().brokerage_id
      ) ?? null;

    return {
      agency: reportBrokerage,
      agent: reportBroker,
      brokerage: reportBrokerage,
      broker: reportBroker,
      email: reportBroker?.email ?? "",
    };
  }, [brokerageOptions, allBrokerOptions, report]);

  const formMethods = useForm<AgentModalFormData>({
    mode: "onChange",
    defaultValues,
  });

  const saveData = async (
    data: AgentModalFormData,
    skipSimilarCheck = !isCSManager
  ) => {
    let agencyId = data.agency?.value;
    let agency: Brokerage;
    let agent: BrokerageEmployee;

    if (data.agency?.type === "new") {
      const newName = data.agency.newValue ?? data.agency.label;

      if (!skipSimilarCheck) {
        const res = await copilotApiClient.getOrFindSimilarBrokerages({
          name: newName,
          domains: data.agency?.domains,
        });
        if (res.existing) {
          // We "create it" and BE will just add org perm to an existing one
        } else if (res.similar && res.similar.length > 0) {
          setSimilarBrokerages({ similarBrokerages: res.similar, data });
          return;
        }
      }

      try {
        const createdBrokerage = await createBrokerage({
          organization_ids: [organizationId],
          name: newName!,
          domains: data.domains ?? undefined,
        });

        agencyId = createdBrokerage.id!;
        agency = createdBrokerage;

        trackBrokerageAdded({
          brokerageId: agencyId,
          byUserId: user.id,
          name: newName,
        });
      } catch (e: any) {
        if (e.message.endsWith("400")) {
          enqueueSnackbar("Agency with the same name or alias already exists", {
            variant: "error",
          });
        } else {
          enqueueSnackbar("Failed to create agency", {
            variant: "error",
          });
        }

        return;
      }
    }

    let agentId = data.agent?.value;
    if (data.agent && data.agent.type === "new") {
      if (!skipSimilarCheck) {
        const res = await copilotApiClient.getOrFindSimilarBrokers({
          brokerage_id: agencyId,
          name: data.agent.newValue!,
          email: data.email ?? undefined,
        });

        if (res.existing) {
          // We "create it" and BE will just add org perm to an existing one
        } else if (res.similar && res.similar.length > 0) {
          setSimilarBrokers({ similarBrokers: res.similar, data });
          return;
        }
      }

      const createdAgent = await createBroker(
        {
          brokerageEmployee: {
            brokerage_id: agencyId,
            name: data.agent.newValue!,
            email: data.email ?? undefined,
            organization_ids: [organizationId],
            roles: ["AGENT"],
          },
        },
        {
          onError(e: any) {
            enqueueSnackbar(
              e?.response?.data?.detail ?? "Failed to create agent",
              { variant: "error" }
            );
          },
        }
      );
      agentId = createdAgent.id;
      agent = createdAgent;

      trackBrokerageEmployeeAdded({
        brokerageEmployeeId: agentId!,
        byUserId: user.id,
        name: data.agent.newValue!,
        brokerageId: agencyId!,
        email: data.email ?? undefined,
      });
    }

    const dataToUpdate: Record<string, string | null> = {
      brokerage_id: agencyId ?? null,
      broker_id: agentId ?? null,
    };

    const submission = report.getSubmission();
    if (submission.brokerage_id !== agencyId) {
      dataToUpdate["brokerage_contact_id"] = null;
    }

    await updateSubmission(
      {
        submissionId: submission.id,
        reportId: report.id,
        data: dataToUpdate,
      },
      {
        onSuccess() {
          onSave && onSave(agency, agent);
        },
        onError() {
          enqueueSnackbar("Failed to update submission", { variant: "error" });
        },
      }
    );

    if (agencyId != report.getBrokerage()?.id) {
      trackSubmissionEdited(report, { item: "agency" });
    }
    if (agentId != report.getBroker()?.id) {
      trackSubmissionEdited(report, { item: "agent" });
    }

    onClose(false);
  };

  useEffect(() => {
    if (searchOption) {
      trackAgentModalSearched(report, {
        pickedSearchValue: searchOption.label,
      });
    }
  }, [report, searchOption]);

  return (
    <>
      <Dialog
        title={`Select ${user.getBrokerageLabel()}`}
        maxWidth="xl"
        primaryActionText="Save"
        disablePrimary={!searchOption && !defaultValues.brokerage}
        onPrimaryAction={formMethods.handleSubmit((data) => saveData(data))}
        isLoading={
          isUpdatingSubmission || isCreatingBroker || isCreatingBrokerage
        }
        rawContent={
          <Box sx={{ minWidth: 800, mx: 3 }}>
            <FormProvider {...formMethods}>
              <SearchField
                agents={brokers ?? []}
                agencies={brokerages ?? []}
                onChange={setSearchOption}
              />

              {(searchOption || defaultValues.brokerage) && (
                <AgencyAgentForm
                  report={report}
                  agencyOptions={brokerageOptions}
                  allAgentOptions={allBrokerOptions}
                  searchbarOption={searchOption}
                />
              )}
            </FormProvider>
          </Box>
        }
        onClose={() => onClose()}
      />

      {similarBrokerages && (
        <SimilarBrokeragesModal
          onClose={() => setSimilarBrokerages(null)}
          data={similarBrokerages}
          onSave={saveData}
        />
      )}

      {similarBrokers && (
        <SimilarBrokersModal
          onClose={() => setSimilarBrokers(null)}
          data={similarBrokers}
          onSave={saveData}
        />
      )}
    </>
  );
};
