import Report from "@legacy/models/Report";
import { Dialog } from "@ui-patterns/dialog";
import React, { useMemo, useState } from "react";
import {
  useBrokerageEmployeesQuery,
  useCreateBrokerageEmployeeMutation,
} from "@queries/agency";
import { useOrganizationId } from "@utils/auth";
import {
  trackSubmissionEdited,
  trackSubmissionInfoEditCancel,
} from "@utils/amplitude";
import { CorrespondenceContactsModalFormData, SearchOption } from "./types";
import { FormProvider, useForm } from "react-hook-form";
import { useSnackbar } from "notistack";
import { useUpdateSubmission } from "@queries/report";
import { Box } from "@mui/material";
import { BrokerCorrespondenceField } from "./components/BrokerCorrespondenceField";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { CorrespondenceContactEmailField } from "./components/CorrespondenceContactEmailField";
import {
  SimilarBrokers,
  SimilarBrokersModal,
} from "@features/agents-modal/components/SimilarBrokersModal";
import copilotApiClient from "@legacy/clients/copilotApiClient";

type Props = {
  brokerageId: string;
  onClose: () => void;
  report: Report;
};

const Body = ({ brokerageId, onClose, report }: Props) => {
  const organizationId = useOrganizationId();
  const { data: brokerageContacts } = useBrokerageEmployeesQuery({
    organizationId,
    role: "CORRESPONDENCE_CONTACT",
  });
  const { isCSManager } = useAccessPermissions();
  const { enqueueSnackbar } = useSnackbar();
  const {
    mutateAsync: createBrokerageEmployee,
    isLoading: isCreatingBrokerageEmployee,
  } = useCreateBrokerageEmployeeMutation();
  const { mutateAsync: updateSubmission, isLoading: isUpdatingSubmission } =
    useUpdateSubmission();

  const [similarBrokers, setSimilarBrokers] =
    useState<SimilarBrokers<CorrespondenceContactsModalFormData> | null>(null);

  const handleClose = () => {
    trackSubmissionInfoEditCancel(report, {
      item: "brokerage",
    });
    onClose();
  };

  const allOptions: SearchOption[] = useMemo(() => {
    return (brokerageContacts ?? []).map((cc) => ({
      type: "correspondenceContact",
      label: `${cc.name}${cc.email ? ` (${cc.email})` : ""}`,
      name: cc.name!,
      value: cc.id!,
      agencyId: cc.brokerage_id!,
      email: cc.email,
    }));
  }, [brokerageContacts]);

  const defaultValues = useMemo(() => {
    const reportBrokerageContact =
      allOptions.find(
        (bc) => bc.value === report.getSubmission().brokerage_contact_id
      ) ?? null;
    return {
      correspondenceContact: reportBrokerageContact,
      brokerageContact: reportBrokerageContact,
      email: reportBrokerageContact?.email ?? "",
    };
  }, [allOptions, report]);

  const formMethods = useForm<CorrespondenceContactsModalFormData>({
    mode: "onChange",
    defaultValues,
  });

  const saveData = async (
    data: CorrespondenceContactsModalFormData,
    skipSimilarCheck = false
  ) => {
    let ccId = data.correspondenceContact?.value;
    if (
      data.correspondenceContact &&
      data.correspondenceContact.type === "new"
    ) {
      if (data.email) {
        if (!skipSimilarCheck) {
          const res = await copilotApiClient.getOrFindSimilarBrokers({
            brokerage_id: brokerageId,
            name: data.correspondenceContact.newValue!,
            email: data.email ?? undefined,
          });

          if (res.existing) {
            // We "create it" and BE will just add org perm to an existing one
          } else if (res.similar && res.similar.length > 0) {
            setSimilarBrokers({ similarBrokers: res.similar, data });
            return;
          }
        }

        const createdCc = await createBrokerageEmployee(
          {
            brokerageEmployee: {
              roles: ["CORRESPONDENCE_CONTACT"],
              brokerage_id: brokerageId,
              name: data.correspondenceContact.newValue!,
              email: data.email ?? undefined,
              organization_ids: [organizationId],
            },
          },
          {
            onError() {
              enqueueSnackbar("Failed to create correspondence contact", {
                variant: "error",
              });
            },
          }
        );
        ccId = createdCc.id;
      } else {
        enqueueSnackbar("Please specify the user email", {
          variant: "error",
        });
        return;
      }
    }

    if (data.correspondenceContact?.type !== "stuck") {
      await updateSubmission(
        {
          submissionId: report.getSubmission().id,
          reportId: report.id,
          data: {
            brokerage_contact_id: ccId ?? null,
          },
        },
        {
          onError() {
            enqueueSnackbar("Failed to update submission", {
              variant: "error",
            });
          },
        }
      );
    }

    if (ccId != report.getBrokerageContact()?.id) {
      trackSubmissionEdited(report, { item: "correspondence contact" });
    }
    onClose();
  };

  return (
    <>
      <Dialog
        title="Select Correspondence Contact"
        maxWidth="xl"
        primaryActionText="Save"
        onPrimaryAction={formMethods.handleSubmit((data) => saveData(data))}
        isLoading={isUpdatingSubmission || isCreatingBrokerageEmployee}
        rawContent={
          <Box sx={{ minWidth: 800, mx: 3 }}>
            <FormProvider {...formMethods}>
              <BrokerCorrespondenceField
                allOptions={allOptions}
                clearable={isCSManager}
                agency={report.getBrokerage()}
              />
              {isCSManager && (
                <CorrespondenceContactEmailField report={report} />
              )}
            </FormProvider>
          </Box>
        }
        onClose={handleClose}
      />
      {similarBrokers && (
        <SimilarBrokersModal
          onClose={() => setSimilarBrokers(null)}
          data={similarBrokers}
          onSave={saveData}
        />
      )}
    </>
  );
};

export const BrokerageContactModal = ({
  brokerageId,
  onClose,
  report,
}: Props) => {
  const organizationId = useOrganizationId();
  const { data: brokerageContacts } = useBrokerageEmployeesQuery({
    organizationId,
    role: "CORRESPONDENCE_CONTACT",
  });

  if (!brokerageContacts)
    return (
      <Dialog
        title="Select Correspondence Contact"
        maxWidth="xl"
        disablePrimary
        isLoading
        onClose={onClose}
      />
    );

  return <Body brokerageId={brokerageId} onClose={onClose} report={report} />;
};
