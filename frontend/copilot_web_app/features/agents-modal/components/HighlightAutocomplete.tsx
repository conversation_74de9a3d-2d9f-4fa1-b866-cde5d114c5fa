import { SearchOption } from "@features/agents-modal/types";
import React, { useMemo, type JSX } from "react";
import uFuzzy from "@leeoniya/ufuzzy";
import { Autocomplete, InputAdornment, TextField } from "@mui/material";
import { FormAutocomplete } from "@utils/form/FormAutocomplete";
import SearchIcon from "@mui/icons-material/Search";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { useCurrentUser } from "@utils/auth";
import { trackSubmissionStuckChanged } from "@utils/amplitude";
import { useCurrentReportQuery, useUpdateSubmission } from "@queries/report";
import { useAccessPermissions } from "@features/product-driven-support/utils";

type HighlightAutocompleteProps = {
  options: SearchOption[];
  label: string;
  placeholder?: string;
  value?: SearchOption | null;
  name?: string;
  onChange?: (option: SearchOption | null) => void;
  helperText?: string;
  clearable?: boolean;
  customNotFoundMessage?: string;
  size?: "medium" | "small";
};

export const HighlightAutocomplete = ({
  options,
  value,
  name,
  onChange,
  label,
  placeholder,
  helperText,
  clearable = true,
  customNotFoundMessage = "",
  size = "medium",
}: HighlightAutocompleteProps) => {
  const { supportFlow, imsRetryEnabled } = useFeatureFlags();
  const { mutate: updateSubmission } = useUpdateSubmission();
  const report = useCurrentReportQuery();
  const user = useCurrentUser();
  const { isCSManager } = useAccessPermissions();
  const fuzzySearch = useMemo(() => new uFuzzy({}), []);

  const allLabels = useMemo(
    () => options.map((option) => option.label),
    [options]
  );

  const Component: React.ElementType = name ? FormAutocomplete : Autocomplete;

  const getNotFoundOption = (input: string) => {
    if (isCSManager || imsRetryEnabled) {
      return [
        {
          type: "new" as const,
          label: `Add "${input}."`,
          newValue: input,
          value: "",
        },
      ];
    } else if (supportFlow && !isCSManager) {
      return [
        {
          type: "stuck" as const,
          label: `Cannot find "${input}". Make submission stuck. ${customNotFoundMessage}`,
          newValue: input,
          value: "",
        },
      ];
    } else {
      return [];
    }
  };

  const handleOnChange = (option: SearchOption | null) => {
    const shouldStuck = option?.type === "stuck";
    if (onChange && !shouldStuck) {
      onChange(option);
      return;
    } else if (report && shouldStuck) {
      updateSubmission({
        submissionId: report.getSubmission().id,
        reportId: report.id,
        data: {
          stuck_reason: option?.newValue
            ? `User: ${user?.email}. Cannot find brokerage / broker: ${option?.newValue}`
            : "",
        },
      });
      trackSubmissionStuckChanged(report, {});
    }
  };

  return (
    <Component
      value={value}
      name={name!}
      onChange={(e: any, option: any) =>
        handleOnChange(option as SearchOption | null)
      }
      filterOptions={(_: any, params: any): SearchOption[] => {
        const indexes = fuzzySearch.filter(allLabels, params.inputValue) ?? [];
        const info = fuzzySearch.info(indexes, allLabels, params.inputValue);
        const order = fuzzySearch.sort(info, allLabels, params.inputValue);

        const filtered: SearchOption[] = order
          .map((index: number) => ({
            highlightRanges: info.ranges[index],
            ...options[indexes[index]],
          }))
          .map((x) => {
            if (!params.inputValue.includes(".") && !x.label.includes("@"))
              return x;

            const startIndex = x.label.indexOf(params.inputValue);
            if (startIndex === -1) return x;

            return {
              ...x,
              highlightRanges: [
                startIndex,
                startIndex + params.inputValue.length,
              ],
            };
          });
        const trimmedInputValue = params.inputValue?.replace(/\s+$/, "");

        return [
          ...filtered,
          ...(trimmedInputValue &&
          !allLabels.some((l) => l === trimmedInputValue)
            ? getNotFoundOption(trimmedInputValue)
            : []),
        ];
      }}
      selectOnFocus
      clearOnBlur
      disableClearable={!clearable}
      handleHomeEndKeys
      options={options}
      getOptionLabel={(option: SearchOption) => {
        const so = option;
        return so.newValue ?? so.label;
      }}
      renderInput={(params: any) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          helperText={helperText}
          size={size}
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      )}
      renderOption={(props: any, option: SearchOption) => {
        const highlight = option.highlightRanges
          ? uFuzzy.highlight(
              option.label,
              option.highlightRanges,
              (part, matched) =>
                matched ? (
                  <strong key={part}>{part.replace(/ /g, "\u00a0")}</strong>
                ) : (
                  part.replace(/ /g, "\u00a0")
                ),
              [] as (JSX.Element | string)[],
              (accum, part) => {
                accum.push(part);
                return accum;
              }
            )
          : option.label;
        return <li {...props}>{highlight}</li>;
      }}
    />
  );
};
