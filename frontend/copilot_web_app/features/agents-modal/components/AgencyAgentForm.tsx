import { AgentModalFormData, SearchOption } from "@features/agents-modal/types";
import { useFormContext } from "react-hook-form";
import React, { useEffect } from "react";
import { HighlightAutocomplete } from "@features/agents-modal/components/HighlightAutocomplete";
import { Stack } from "@mui/material";
import { AgentField } from "@features/agents-modal/components/AgentField";
import Report from "@legacy/models/Report";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { AgentEmailField } from "./AgentEmailField";
import { BrokerageDomainsField } from "@features/agents-modal/components/BrokerageDomainsField";
import { BrokerageNameField } from "@features/agents-modal/components/BrokerageNameField";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";

type AgencyAgentFormProps = {
  report: Report;
  allAgentOptions: SearchOption[];
  agencyOptions: SearchOption[];
  searchbarOption?: SearchOption | null;
};

export const AgencyAgentForm = ({
  report,
  allAgentOptions,
  agencyOptions,
  searchbarOption,
}: AgencyAgentFormProps) => {
  const { reset, watch } = useFormContext<AgentModalFormData>();
  const { isCSManager } = useAccessPermissions();
  const { imsRetryEnabled: canAddBrokers } = useFeatureFlags();

  const isAgentCreated = !!watch("agent")?.value;

  useEffect(() => {
    if (searchbarOption) {
      if (searchbarOption.type === "agency") {
        const agency =
          agencyOptions.find((o) => o.value === searchbarOption.value) ?? null;
        reset({
          agency,
          agent: null,
        });
      } else if (searchbarOption.type === "agent") {
        const agency =
          agencyOptions.find((o) => o.value === searchbarOption.agencyId) ??
          null;
        const agent =
          allAgentOptions.find((o) => o.value === searchbarOption.value) ??
          null;
        reset({
          agency,
          agent,
        });
      } else if (searchbarOption.type === "new") {
        reset({
          agency: {
            type: "new",
            label: searchbarOption.newValue!,
            value: "",
            domains: [],
          },
          agent: null,
        });
      }
    }
    // we dont want to trigger this effect when options changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reset, searchbarOption]);

  return (
    <Stack spacing={3} sx={{ pt: 3 }}>
      <HighlightAutocomplete
        name="agency"
        options={agencyOptions}
        label="Brokerage"
        placeholder="Search for brokerage"
        clearable={isCSManager}
      />

      {isCSManager && (
        <Stack>
          <BrokerageNameField type="agency" />
          <BrokerageDomainsField report={report} />
        </Stack>
      )}

      <AgentField allAgentOptions={allAgentOptions} clearable={isCSManager} />

      {isCSManager && (
        <Stack>
          <BrokerageNameField type="agent" />
          <AgentEmailField report={report} />
        </Stack>
      )}

      {!isCSManager && canAddBrokers && !isAgentCreated && (
        <Stack>
          <AgentEmailField report={report} />
        </Stack>
      )}
    </Stack>
  );
};
