import {
  Box,
  CircularProgress,
  styled,
  Table,
  TableBody,
  TableContainer,
  TablePagination,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import Report from "@legacy/models/Report";
import ReactDOM from "react-dom";
import { useGetSubmissionsNaicsQueue } from "@queries/naicsQueue";
import { useGetCurrentSupportUser } from "@queries/submissionAssignment";
import { TableHeader } from "@features/submissions-naics-queue/components/TableHeader";
import { ReportRow } from "@features/submissions-naics-queue/components/ReportRow";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const Wrapper = styled("div")({
  display: "flex",
  flexDirection: "column",
  flex: "1 1 0",
  overflow: "hidden",
});
const DEFAULT_PAGE_SIZE = 25;

export const SubmissionsNaicsQueue = () => {
  const [paginationContainer, setPaginationContainer] =
    useState<HTMLDivElement>();
  const [perPage, setPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [page, setPage] = useState(1);
  const { data: currentSupportUser } = useGetCurrentSupportUser();
  const { isCSManager } = useAccessPermissions();

  const { data: reportsEnvelope, isLoading } = useGetSubmissionsNaicsQueue({
    page: page ? page : 1,
    perPage: perPage ? perPage : DEFAULT_PAGE_SIZE,
  });

  if (!isCSManager && !currentSupportUser?.can_assign_naics) {
    return null;
  }

  return (
    <Wrapper>
      <Wrapper sx={{ overflowY: "auto", position: "relative" }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography m={3} p={3} variant="h3">
            NAICS Queue
          </Typography>
        </Box>
        <Box display="flex" flexDirection="column">
          <TableContainer>
            <Table
              stickyHeader
              width={300}
              sx={{
                position: "relative",
                height: undefined,
              }}
            >
              {isLoading && (
                <Box
                  position="absolute"
                  width="100%"
                  mt={7}
                  display="flex"
                  justifyContent="center"
                >
                  <CircularProgress />
                </Box>
              )}
              <TableHeader />
              <TableBody>
                {reportsEnvelope?.reports?.map((report: Report) => (
                  <ReportRow key={report.id} report={report} />
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          {!isLoading && !reportsEnvelope?.reports?.length && (
            <Typography fontWeight="bold" sx={{ textAlign: "center", mt: 4 }}>
              No items found
            </Typography>
          )}
          {paginationContainer &&
            ReactDOM.createPortal(
              <TablePagination
                count={reportsEnvelope?.total_reports ?? 0}
                page={page ? Number(page) - 1 : 0}
                rowsPerPage={perPage ? Number(perPage) : DEFAULT_PAGE_SIZE}
                rowsPerPageOptions={[DEFAULT_PAGE_SIZE, 50, 75, 100]}
                onRowsPerPageChange={(e) => {
                  setPage(1);
                  setPerPage(Number(e.target.value));
                }}
                onPageChange={(_, page) => {
                  setPage(page + 1);
                }}
              />,
              paginationContainer
            )}
        </Box>
      </Wrapper>
      <Box
        width="100%"
        display="flex"
        justifyContent="flex-end"
        id="list-pagination"
        ref={(ref) => {
          setPaginationContainer(ref as HTMLDivElement);
        }}
      />
    </Wrapper>
  );
};
