import { SearchOption } from "@features/agents-modal/types";
import copilotApiClient from "@legacy/clients/copilotApiClient";
import { useCreateBrokerageEmployeeMutation } from "@queries/agency";
import { useOrganizationId } from "@utils/auth";
import { useSnackbar } from "notistack";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { BrokerageEmployee } from "@legacy/api_clients/copilot_api_client";
import { useUpdateSubmission } from "@queries/report";
import { useReport } from "@utils/useReport";
import { SetSimilarBrokerageContactsParams } from "@features/submission-information-section/components/sub-sections/submission-details/types";

export const useUpdateBrokerageContact = () => {
  const organizationId = useOrganizationId();
  const report = useReport();
  const { enqueueSnackbar } = useSnackbar();
  const { isCSManager } = useAccessPermissions();
  const { mutateAsync: createBrokerageEmployee } =
    useCreateBrokerageEmployeeMutation();
  const { mutateAsync: updateSubmission } = useUpdateSubmission();

  const updateBrokerageContact = async (
    option: SearchOption | null,
    setSimilarBrokerageContacts: (
      params: SetSimilarBrokerageContactsParams
    ) => void,
    skipSimilarCheck = !isCSManager
  ): Promise<BrokerageEmployee | undefined> => {
    const newName = option?.newValue ?? option?.label;
    const newEmail = option?.email ?? undefined;

    if (isCSManager && !skipSimilarCheck) {
      const res = await copilotApiClient.getOrFindSimilarBrokers({
        brokerage_id: option?.agencyId,
        name: newName,
        email: newEmail,
      });
      if (res.similar && res.similar.length > 0) {
        setSimilarBrokerageContacts({
          similarBrokers: res.similar,
          data: {
            correspondenceContact: option,
            email: newEmail!,
            agency: option,
            agent: option,
          },
        });
        return;
      }
    }

    const createdBrokerageContact = await createBrokerageEmployee(
      {
        brokerageEmployee: {
          roles: ["CORRESPONDENCE_CONTACT"],
          brokerage_id: option?.agencyId,
          name: newName,
          email: newEmail,
          organization_ids: [organizationId],
        },
      },
      {
        onError(e: any) {
          enqueueSnackbar(
            e?.response?.data?.detail ??
              "Failed to create correspondence contact",
            {
              variant: "error",
            }
          );
        },
      }
    );

    if (option?.type !== "stuck") {
      await updateSubmission(
        {
          submissionId: report.getSubmission().id,
          reportId: report.id,
          data: {
            brokerage_contact_id: createdBrokerageContact.id ?? null,
          },
        },
        {
          onError(e: any) {
            enqueueSnackbar(
              e?.response?.data?.detail ?? "Failed to update submission",
              {
                variant: "error",
              }
            );
          },
        }
      );
    }

    return createdBrokerageContact;
  };
  return { updateBrokerageContact };
};
