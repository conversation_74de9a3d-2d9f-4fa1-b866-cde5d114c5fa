import { SearchOption } from "@features/agents-modal/types";
import copilotApiClient from "@legacy/clients/copilotApiClient";
import { useCreateBrokerageEmployeeMutation } from "@queries/agency";
import { trackBrokerageEmployeeAdded } from "@utils/amplitude";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { useSnackbar } from "notistack";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { BrokerageEmployee } from "@legacy/api_clients/copilot_api_client";
import { SetSimilarBrokersParams } from "@features/submission-information-section/components/sub-sections/submission-details/types";

export const useUpdateBroker = () => {
  const organizationId = useOrganizationId();
  const user = useCurrentUser();
  const { enqueueSnackbar } = useSnackbar();
  const { isCSManager } = useAccessPermissions();
  const { mutateAsync: createBroker } = useCreateBrokerageEmployeeMutation();

  const updateBroker = async (
    option: SearchOption | null,
    setSimilarBrokers: (params: SetSimilarBrokersParams) => void,
    skipSimilarCheck = !isCSManager
  ): Promise<BrokerageEmployee | undefined> => {
    const newName = option?.newValue ?? option?.label;
    const newEmail = option?.email ?? undefined;

    if (isCSManager && !skipSimilarCheck) {
      const res = await copilotApiClient.getOrFindSimilarBrokers({
        brokerage_id: option?.agencyId,
        name: newName,
        email: newEmail,
      });
      if (res.similar && res.similar.length > 0) {
        setSimilarBrokers({
          similarBrokers: res.similar,
          data: {
            agent: option,
            agency: option,
            email: "",
          },
        });
        return;
      }
    }

    if (option?.agencyId === undefined || option?.agencyId === null) {
      enqueueSnackbar("Failed to create agent", { variant: "error" });
      return;
    }

    const createdBroker = await createBroker(
      {
        brokerageEmployee: {
          brokerage_id: option.agencyId,
          name: newName,
          email: newEmail,
          organization_ids: [organizationId],
          roles: ["AGENT"],
        },
      },
      {
        onError(e: any) {
          enqueueSnackbar(
            e?.response?.data?.detail ?? "Failed to create agent",
            { variant: "error" }
          );
        },
      }
    );

    trackBrokerageEmployeeAdded({
      brokerageEmployeeId: createdBroker.id!,
      byUserId: user.id,
      name: newName ?? "",
      brokerageId: option.agencyId,
      email: newEmail,
    });

    return createdBroker;
  };
  return { updateBroker };
};
