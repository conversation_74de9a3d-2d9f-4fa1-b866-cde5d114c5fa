import { SearchOption } from "@features/agents-modal/types";
import copilotApiClient from "@legacy/clients/copilotApiClient";
import { useCreateBrokerageMutation } from "@queries/agency";
import { trackBrokerageAdded } from "@utils/amplitude";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { useSnackbar } from "notistack";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { Brokerage } from "@legacy/api_clients/copilot_api_client";
import { SetSimilarBrokeragesParams } from "@features/submission-information-section/components/sub-sections/submission-details/types";

export const useUpdateBrokerage = () => {
  const organizationId = useOrganizationId();
  const user = useCurrentUser();
  const { enqueueSnackbar } = useSnackbar();
  const { isCSManager } = useAccessPermissions();
  const { mutateAsync: createBrokerage } = useCreateBrokerageMutation();

  const updateBrokerage = async (
    option: SearchOption | null,
    setSimilarBrokerages: (params: SetSimilarBrokeragesParams) => void,
    skipSimilarCheck = !isCSManager
  ): Promise<Brokerage | undefined> => {
    const newName = option?.newValue ?? option?.label;
    if (isCSManager && !skipSimilarCheck) {
      const res = await copilotApiClient.getOrFindSimilarBrokerages({
        name: newName,
        domains: option?.domains,
      });
      if (res.similar && res.similar.length > 0) {
        setSimilarBrokerages({
          similarBrokerages: res.similar,
          data: {
            agency: option,
            agent: option,
            email: "",
          },
        });
        return;
      }
    }

    try {
      const createdBrokerage = await createBrokerage({
        organization_ids: [organizationId],
        name: newName!,
        domains: option?.domains ?? undefined,
      });

      trackBrokerageAdded({
        brokerageId: createdBrokerage.id!,
        byUserId: user.id,
        name: newName ?? "",
      });
      return createdBrokerage;
    } catch (e: any) {
      if (e.message.endsWith("400")) {
        enqueueSnackbar("Agency with the same name or alias already exists", {
          variant: "error",
        });
      } else {
        enqueueSnackbar(
          e?.response?.data?.detail ?? "Failed to create agency",
          {
            variant: "error",
          }
        );
      }

      return;
    }
  };
  return { updateBrokerage };
};
