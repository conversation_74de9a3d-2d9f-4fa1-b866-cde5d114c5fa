import { Box, SxProps } from "@mui/material";
import { TypographyWithEllipsis } from "@ui-patterns/typography/TypographyWithEllipsis";

import type { JSX } from "react";

export const LabeledField = ({
  text,
  children,
  sx,
  contentSx,
}: {
  text: string | JSX.Element | undefined;
  children: React.ReactNode;
  sx?: SxProps;
  contentSx?: SxProps;
}) => (
  <Box display="grid" gridTemplateColumns="40% 60%" alignItems="center" sx={sx}>
    <TypographyWithEllipsis variant="body1" fontWeight={500}>
      {text ?? ""}
    </TypographyWithEllipsis>
    <Box sx={contentSx}>{children}</Box>
  </Box>
);
