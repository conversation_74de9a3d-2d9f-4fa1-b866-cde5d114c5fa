import { useAccessPermissions } from "@features/product-driven-support/utils";
import { QuoteSubmissionModal } from "@features/report-list/components/quote-submission/QuoteSubmissionModal";
import { useOrganizationId } from "@utils/auth";
import { ORGANIZATION } from "@utils/constants";
import { useReport } from "@utils/useReport";

export const CoveragesEditModal = ({
  setIsEditing,
}: {
  setIsEditing: (isEditing: boolean) => void;
}) => {
  const report = useReport();
  const submission = report.getSubmission();
  const { isCSManager } = useAccessPermissions();
  const orgId = useOrganizationId();

  if (!submission) return null;

  const hasAnyQuotedAmount = submission.coverages.some(
    (c) => c.quoted_premium !== null && c.quoted_premium > 0
  );
  const hasAnyBoundAmount = submission.coverages.some(
    (c) => c.bound_premium !== null && c.bound_premium > 0
  );
  const hasAnyTotalPremiumAmount = submission.coverages.some(
    (c) =>
      c.total_premium_or_bound_premium !== null &&
      c.total_premium_or_bound_premium > 0
  );
  const hasBoundOrTotalPremium = hasAnyBoundAmount || hasAnyTotalPremiumAmount;
  // Note: Either only single coverage or hard-defined rules.
  const isNonEditableOrg = orgId === ORGANIZATION.NATIONWIDE;
  const orgGroup = report.submission.organization_group;
  const isBowheadXSorPrimary =
    orgGroup === "BOWHEAD_XS" || orgGroup === "BOWHEAD_PRIMARY";
  const isBowheadEnvironmental = orgGroup === "BOWHEAD_ENVIRONMENTAL";
  const hasNoCoverages = report.getCoverages().length === 0;
  const lastEditDate = sessionStorage.getItem(`coverages_${report.id}`);
  const lastEditLess1Hr =
    !!lastEditDate && new Date().getTime() - parseInt(lastEditDate) < 3600000;
  const isNWML = orgId === ORGANIZATION.NATIONWIDE_ML;
  const isEditable =
    !isNonEditableOrg &&
    !isBowheadXSorPrimary &&
    (hasBoundOrTotalPremium ||
      hasAnyQuotedAmount ||
      isNWML ||
      isCSManager ||
      hasNoCoverages ||
      lastEditLess1Hr);

  const type = hasBoundOrTotalPremium
    ? "bind"
    : hasAnyQuotedAmount
      ? "quote"
      : "editCoverages";

  return (
    <QuoteSubmissionModal
      reportId={report.id}
      type={type}
      source="edit-coverage"
      onClose={() => setIsEditing(false)}
      changeStage={false}
      showCoverageDates={false}
      readOnly={!isEditable}
      onlyNonLiabilityEditable={isBowheadEnvironmental}
    />
  );
};
