import { HighlightAutocomplete } from "@features/agents-modal/components/HighlightAutocomplete";
import {
  CorrespondenceContactsModalFormData,
  SearchOption,
} from "@features/agents-modal/types";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { LabeledField } from "@features/submission-information-section/components/ui/LabeledField";
import { Box, Stack } from "@mui/material";
import { useBrokerageEmployeesQuery } from "@queries/agency";
import { TypographyWithEllipsis } from "@ui-patterns/typography/TypographyWithEllipsis";
import { useOrganizationId } from "@utils/auth";
import { useReport } from "@utils/useReport";
import { useMemo, useState } from "react";
import { useFormContext } from "react-hook-form";
import { CorrespondenceContactEmailField } from "@features/agents-modal/components/CorrespondenceContactEmailField";
import { SimilarBrokersModal } from "@features/agents-modal/components/SimilarBrokersModal";
import { SetSimilarBrokerageContactsParams } from "@features/submission-information-section/components/sub-sections/submission-details/types";
import { useUpdateBrokerageContact } from "@features/submission-information-section/hooks/useUpdateBrokerageContact";
import { brokerageEmployeeToOption } from "@features/submission-information-section/helpers/formatHelpers";
import { BrokerageContactCreationModal } from "@features/submission-information-section/components/sub-sections/submission-details/fields/modals/BrokerageContactCreationModal";

export const BrokerageContactField = ({
  isEditing,
}: {
  isEditing: boolean;
}) => {
  const report = useReport();
  const { isCSManager } = useAccessPermissions();
  const organizationId = useOrganizationId();
  const { data: brokerageContacts } = useBrokerageEmployeesQuery({
    organizationId,
    role: "CORRESPONDENCE_CONTACT",
  });
  const [similarBrokerageContacts, setSimilarBrokerageContacts] =
    useState<SetSimilarBrokerageContactsParams | null>(null);
  const [newBrokerageContact, setNewBrokerageContact] =
    useState<SearchOption | null>(null);

  const { updateBrokerageContact } = useUpdateBrokerageContact();

  const { watch, setValue } = useFormContext();

  const brokerageId = watch("brokerage", { value: report.getBrokerage()?.id })
    ?.value;
  const currentBrokerage: SearchOption = watch("brokerage", {
    ...report.getBrokerage(),
    value: brokerageId,
  });

  const options: SearchOption[] = useMemo(
    () =>
      (brokerageContacts ?? [])
        .filter(
          (o) => o?.brokerage_id === currentBrokerage?.value && o?.id && o?.name
        )
        .map((cc) => brokerageEmployeeToOption(cc, "correspondenceContact")),
    [brokerageContacts, currentBrokerage]
  );

  const currentValue: string = watch("brokerageContact", {
    type: "correspondenceContact" as const,
    value: report.getBrokerageContact()?.id,
    name: report.getBrokerageContact()?.name,
    label: report.getBrokerageContact()?.name,
    email: report.getBrokerageContact()?.email,
  })?.value;
  const currentOption = useMemo(
    () => options.find((o) => o.value === currentValue),
    [currentValue, options]
  );

  const handleChange = (option: SearchOption | null) => {
    if (!option) {
      setValue("brokerageContact", { value: null });
      return;
    }
    setValue("brokerageContact", option);
  };

  const handleCreation = async (
    option: SearchOption,
    skipSimilarCheck = !isCSManager
  ) => {
    option.agencyId = brokerageId;
    const brokerageContact = await updateBrokerageContact(
      option,
      setSimilarBrokerageContacts,
      skipSimilarCheck
    );
    if (brokerageContact)
      handleChange(brokerageEmployeeToOption(brokerageContact));
  };

  const handleSimilarBrokerageContacts = async (
    data: CorrespondenceContactsModalFormData,
    skipSimilarCheck: boolean,
    createNew?: boolean
  ) => {
    const cc = data.correspondenceContact;
    if (!cc) return;
    if (createNew) {
      const option: SearchOption = {
        label: cc.newValue!,
        newValue: cc.newValue!,
        email: cc.email,
        type: "new",
        value: "",
      };
      await handleCreation(option, skipSimilarCheck);
    } else {
      const option = options.find((o) => o.value === cc?.value);
      await handleChange(option ?? null);
    }
  };

  const handleCreationModalSave = async (name: string, email: string) => {
    const newOption: SearchOption = {
      type: "new",
      newValue: name,
      value: "",
      email,
      label: name,
    };

    await handleCreation(newOption);
    setNewBrokerageContact(null);
  };

  return (
    <>
      <LabeledField
        text="Broker Correspondence"
        sx={{
          alignItems: "start",
        }}
      >
        <Box>
          {!isEditing && (
            <Box>
              <TypographyWithEllipsis variant="body1">
                {report.getBrokerageContact()?.name ?? "-"}
                {report.getBrokerageContact()?.email
                  ? ` (${report.getBrokerageContact()?.email})`
                  : ""}
              </TypographyWithEllipsis>
            </Box>
          )}
          {isEditing && (
            <Stack spacing={2}>
              <HighlightAutocomplete
                options={options ?? []}
                label=""
                placeholder="Search for Correspondence Contact"
                clearable={isCSManager}
                onChange={(option) => {
                  option?.type === "new"
                    ? setNewBrokerageContact(option)
                    : handleChange(option);
                }}
                value={currentOption ?? null}
                customNotFoundMessage="Contact must be from the same brokerage as the selected broker"
                size="small"
              />
              {isCSManager && currentOption && (
                <CorrespondenceContactEmailField
                  report={report}
                  currentOption={currentOption}
                />
              )}
            </Stack>
          )}
        </Box>
      </LabeledField>
      {similarBrokerageContacts && (
        <SimilarBrokersModal<CorrespondenceContactsModalFormData>
          onClose={() => setSimilarBrokerageContacts(null)}
          data={similarBrokerageContacts}
          onSave={handleSimilarBrokerageContacts}
        />
      )}
      {newBrokerageContact && (
        <BrokerageContactCreationModal
          onSave={handleCreationModalSave}
          onClose={() => setNewBrokerageContact(null)}
          initialName={newBrokerageContact.newValue ?? ""}
        />
      )}
    </>
  );
};
