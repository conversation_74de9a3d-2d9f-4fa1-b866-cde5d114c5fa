import { Agent<PERSON><PERSON><PERSON>ield } from "@features/agents-modal/components/AgentEmailField";
import { BrokerageNameField } from "@features/agents-modal/components/BrokerageNameField";
import { HighlightAutocomplete } from "@features/agents-modal/components/HighlightAutocomplete";
import { SimilarBrokersModal } from "@features/agents-modal/components/SimilarBrokersModal";
import { AgentModalFormData, SearchOption } from "@features/agents-modal/types";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { BrokerCreationModal } from "@features/submission-information-section/components/sub-sections/submission-details/fields/modals/BrokerCreationModal";
import { SetSimilarBrokersParams } from "@features/submission-information-section/components/sub-sections/submission-details/types";
import { LabeledField } from "@features/submission-information-section/components/ui/LabeledField";
import { brokerageEmployeeToOption } from "@features/submission-information-section/helpers/formatHelpers";
import { useUpdateBroker } from "@features/submission-information-section/hooks/useUpdateBroker";
import { Box, Stack } from "@mui/material";
import { useBrokerageEmployeesQuery } from "@queries/agency";
import { TypographyWithEllipsis } from "@ui-patterns/typography/TypographyWithEllipsis";
import { useOrganizationId } from "@utils/auth";
import { useReport } from "@utils/useReport";
import { useMemo, useState } from "react";
import { useFormContext } from "react-hook-form";
import { useOpenBrokerPanel } from "@features/blades/hooks";

export const BrokerAgentField = ({ isEditing }: { isEditing: boolean }) => {
  const report = useReport();
  const { isCSManager } = useAccessPermissions();
  const organizationId = useOrganizationId();
  const { watch, setValue } = useFormContext();
  const { updateBroker } = useUpdateBroker();
  const [similarBrokers, setSimilarBrokers] =
    useState<SetSimilarBrokersParams | null>(null);
  const [newBroker, setNewBroker] = useState<SearchOption | null>(null);

  const brokerageId = watch("brokerage", { value: report.getBrokerage()?.id })
    ?.value;
  const { data: brokers } = useBrokerageEmployeesQuery({
    organizationId,
    role: "AGENT",
    brokerageId,
  });

  const options: SearchOption[] = (brokers ?? [])
    .filter((broker) => broker.name && broker.id && broker.brokerage_id)
    .map((broker) => brokerageEmployeeToOption(broker));

  const currentBroker = watch("broker", {
    type: "agent" as const,
    value: report.getBroker()?.id,
    name: report.getBroker()?.name,
    label: report.getBroker()?.name,
    email: report.getBroker()?.email,
  })?.value;

  const currentOption = useMemo(() => {
    if (!currentBroker) return null;
    return options.find((o) => o.value === currentBroker);
  }, [currentBroker, options]);

  const handleChange = (option: SearchOption | null) => {
    if (!option) {
      setValue("broker", { value: null });
      return;
    }
    setValue("brokerage", { value: option.agencyId });
    setValue("broker", option);
  };

  const handleCreation = async (
    option: SearchOption,
    skipSimilarCheck = !isCSManager
  ) => {
    option.agencyId = brokerageId;
    const broker = await updateBroker(
      option,
      setSimilarBrokers,
      skipSimilarCheck
    );
    if (broker) handleChange(brokerageEmployeeToOption(broker));
  };

  const handleSimilarBrokers = async (
    data: AgentModalFormData,
    skipSimilarCheck: boolean,
    createNew?: boolean
  ) => {
    if (!data.agent) return;
    if (createNew) {
      const option = brokerageEmployeeToOption(data.agent);
      option.newValue = data.agent.newValue;
      handleCreation(option, skipSimilarCheck);
    } else {
      const option = options.find((o) => o.value === data.agent?.value);
      handleChange(option ?? null);
    }
  };

  const handleCreationModalSave = async (name: string, email: string) => {
    const newOption: SearchOption = {
      type: "new",
      newValue: name,
      value: "",
      email,
      label: name,
    };

    await handleCreation(newOption);
    setNewBroker(null);
  };

  const { openBrokerPanel } = useOpenBrokerPanel({
    brokerageId: report.getBrokerage()?.id,
    brokerageName: report.getBrokerage()?.name,
    brokerId: report.getBroker()?.id,
    brokerName: report.getBroker()?.name ?? undefined,
  });

  return (
    <>
      <LabeledField text="Broker / Agent" sx={{ alignItems: "start" }}>
        <Box>
          {!isEditing && (
            <TypographyWithEllipsis
              variant="body1"
              onClick={openBrokerPanel}
              sx={{ cursor: "pointer" }}
            >
              {report.getBroker()?.name ?? "-"}
              {report.getBroker()?.email
                ? ` (${report.getBroker()?.email})`
                : ""}
            </TypographyWithEllipsis>
          )}
          {isEditing && (
            <Stack spacing={2}>
              <HighlightAutocomplete
                options={options ?? []}
                label=""
                placeholder="Search by Agent details"
                clearable={isCSManager}
                onChange={(option) => {
                  option?.type === "new"
                    ? setNewBroker(option)
                    : handleChange(option);
                }}
                value={currentOption}
                size="small"
              />
              {isCSManager && currentOption && (
                <>
                  <BrokerageNameField
                    type="agent"
                    currentOption={currentOption}
                  />
                  <AgentEmailField
                    report={report}
                    currentOption={currentOption}
                  />
                </>
              )}
            </Stack>
          )}
        </Box>
      </LabeledField>
      {similarBrokers && (
        <SimilarBrokersModal
          onClose={() => setSimilarBrokers(null)}
          data={similarBrokers}
          onSave={handleSimilarBrokers}
        />
      )}
      {newBroker && (
        <BrokerCreationModal
          onSave={handleCreationModalSave}
          onClose={() => setNewBroker(null)}
          initialName={newBroker.newValue ?? ""}
        />
      )}
    </>
  );
};
