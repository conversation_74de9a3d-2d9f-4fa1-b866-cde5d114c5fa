import { BrokerageDomainsField } from "@features/agents-modal/components/BrokerageDomainsField";
import { BrokerageNameField } from "@features/agents-modal/components/BrokerageNameField";
import { HighlightAutocomplete } from "@features/agents-modal/components/HighlightAutocomplete";
import { SimilarBrokeragesModal } from "@features/agents-modal/components/SimilarBrokeragesModal";
import { AgentModalFormData, SearchOption } from "@features/agents-modal/types";
import { agencyToOption } from "@features/agents-modal/util";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { SetSimilarBrokeragesParams } from "@features/submission-information-section/components/sub-sections/submission-details/types";
import { LabeledField } from "@features/submission-information-section/components/ui/LabeledField";
import { useUpdateBrokerage } from "@features/submission-information-section/hooks/useUpdateBrokerage";
import { Box, Stack } from "@mui/material";
import { useBrokeragesQuery } from "@queries/agency";
import { TypographyWithEllipsis } from "@ui-patterns/typography/TypographyWithEllipsis";
import { useOrganizationId } from "@utils/auth";
import { useReport } from "@utils/useReport";
import { useMemo, useState } from "react";
import { useFormContext } from "react-hook-form";
import { useOpenBrokerPanel } from "@features/blades/hooks";

export const BrokerageField = ({ isEditing }: { isEditing: boolean }) => {
  const report = useReport();
  const { isCSManager } = useAccessPermissions();
  const organizationId = useOrganizationId();
  const { data: brokerages } = useBrokeragesQuery(organizationId);
  const [similarBrokerages, setSimilarBrokerages] =
    useState<SetSimilarBrokeragesParams | null>(null);
  const { updateBrokerage } = useUpdateBrokerage();

  const options = useMemo(
    () => (brokerages ?? []).map(agencyToOption),
    [brokerages]
  );

  const { watch, setValue } = useFormContext();

  const currentValue = watch("brokerage", {
    type: "agency" as const,
    value: report.getBrokerage()?.id,
    name: report.getBrokerage()?.name,
    label: report.getBrokerage()?.name,
  })?.value;
  const currentOption = useMemo(
    () => options.find((o) => o.value === currentValue),
    [currentValue, options]
  );

  const handleChange = (option: SearchOption | null) => {
    if (!option) {
      setValue("brokerage", null);
    } else {
      setValue("brokerage", option);
    }

    setValue("broker", { value: null });
    setValue("brokerageContact", { value: null });
  };

  const handleCreation = async (
    option: SearchOption,
    skipSimilarCheck = !isCSManager
  ) => {
    const brokerage = await updateBrokerage(
      option,
      setSimilarBrokerages,
      skipSimilarCheck
    );
    if (brokerage) handleChange(agencyToOption(brokerage));
  };

  const handleSimilarBrokerages = async (
    data: AgentModalFormData,
    skipSimilarCheck: boolean,
    createNew?: boolean
  ) => {
    if (!data.agency) return;
    if (createNew) {
      const option = agencyToOption(data.agency);
      option.newValue = data.agency.newValue;
      handleCreation(option, skipSimilarCheck);
    } else {
      const option = options.find((o) => o.value === data.agency?.value);
      handleChange(option ?? null);
    }
  };

  const { openBrokerPanel } = useOpenBrokerPanel({
    brokerageId: report.getBrokerage()?.id,
    brokerageName: report.getBrokerage()?.name,
  });

  return (
    <>
      <LabeledField text="Brokerage / Agency" sx={{ alignItems: "start" }}>
        <Box>
          {!isEditing && (
            <TypographyWithEllipsis
              variant="body1"
              onClick={openBrokerPanel}
              sx={{ cursor: "pointer" }}
            >
              {report.getBrokerage()?.name ?? "-"}
            </TypographyWithEllipsis>
          )}
          {isEditing && (
            <Stack spacing={2}>
              <HighlightAutocomplete
                options={options ?? []}
                label=""
                placeholder="Search for brokerage"
                clearable={isCSManager}
                onChange={(option) => {
                  option?.type === "new"
                    ? handleCreation(option)
                    : handleChange(option);
                }}
                value={currentOption}
                size="small"
              />
              {isCSManager && currentOption && (
                <>
                  <BrokerageNameField
                    type="agency"
                    currentOption={currentOption}
                  />
                  <BrokerageDomainsField
                    report={report}
                    currentOption={currentOption}
                  />
                </>
              )}
            </Stack>
          )}
        </Box>
      </LabeledField>
      {similarBrokerages && (
        <SimilarBrokeragesModal
          onClose={() => setSimilarBrokerages(null)}
          data={similarBrokerages}
          onSave={handleSimilarBrokerages}
        />
      )}
    </>
  );
};
