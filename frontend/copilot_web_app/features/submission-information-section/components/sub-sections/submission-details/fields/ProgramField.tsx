import { useAccessPermissions } from "@features/product-driven-support/utils";
import { LabeledField } from "@features/submission-information-section/components/ui/LabeledField";
import { Autocomplete, Box, TextField, Typography } from "@mui/material";
import { TypographyWithEllipsis } from "@ui-patterns/typography/TypographyWithEllipsis";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { useReport } from "@utils/useReport";
import { useFormContext } from "react-hook-form";
import { ORGANIZATION } from "@utils/constants";

export const ProgramField = ({ isEditing }: { isEditing: boolean }) => {
  const user = useCurrentUser();
  const report = useReport();
  const { isCSManager } = useAccessPermissions();
  const orgId = useOrganizationId();

  const isSupport = user.applicable_settings.is_support;
  const isBowhead = orgId === ORGANIZATION.BOWHEAD;
  const isEditable = (!isSupport || isCSManager) && !isBowhead;

  const { watch, setValue } = useFormContext();

  const handleChange = (option: string | null) => {
    setValue("program", option);
  };

  const currentOption = watch("program", report.org_group);

  return (
    <LabeledField text="Program">
      <Box>
        {!isEditing || !isEditable ? (
          <TypographyWithEllipsis variant="body1">
            {currentOption ?? "-"}
          </TypographyWithEllipsis>
        ) : (
          <Autocomplete<string, false>
            disablePortal
            fullWidth
            options={user?.getOrgGroups() ?? []}
            value={currentOption}
            limitTags={1}
            autoHighlight
            onChange={(_, v: string | null) => {
              handleChange(v);
            }}
            renderInput={(params) => <TextField {...params} size="small" />}
            renderOption={(props, option) => (
              <Box component="li" {...props} style={{ display: "block" }}>
                <Typography variant="body1">{option}</Typography>
              </Box>
            )}
          />
        )}
      </Box>
    </LabeledField>
  );
};
