import { useFlags } from "launchdarkly-react-client-sdk";

const DEFAULT_FLAG_VALUES = {
  allowLoadingVehiclesFromPdf: true,
  addingNewsStories: true,
  uploadAdditionalFiles: true,
  timezoneSettings: true,
  policyId: true,
  allowDeletingImages: true,
  firstPartySuggestions: true,
  showFeinCard: true,
  allowReportDuplication: true,
  uploadLegalFilings: true,
  moveNewsToLegalFilings: true,
  allowHideFacts: true,
  supportFlow:
    typeof window !== "undefined" &&
    window.localStorage.getItem("NEXT_PUBLIC_SHOW_SUPPORT_FLOW") === "true",
  allowProcessingLossRuns: false,
  refetchOnWindowFocus: true,
  ersComposedMode: false,
  manualLossRuns: false,
  masterAccount: false,
  showCorporateStructure: false,
  propertyInsuranceValuationV2: false,
  showPortfolioOptimization: false,
  showLossPolicyChart: false,
  submissionProcessing: false,
  showFniInfo: false,
  showGlobalImagesCard: false,
  newPdfPreview: false,
  fleetCsvExport: false,
  separatedRefer: false,
  showTotalUnits: false,
  showExplanationsSummary: false,
  debugInfo: false,
  missingFilesCard: false,
  showLossDatesCount: false,
  emailPdfAsHtml: false,
  missingDocumentsPm: false,
  allowFleetManipulation: false,
  showBfm: false,
  fleetDebugger: false,
  portfolioManagerInternal: false,
  maintenanceMode: false,
  showBrokerCorrespondenceContact: false,
  emailAttachments: false,
  showExperiments: false,
  modeVisibilityDebugging: false,
  pdsSimplifactions: false,
  pdsAllowSettingEntityRole: false,
  pdsEmValidation: false,
  mdExposures: false,
  imsRetryEnabled: false,
  forcePdsFinancialsStep: false,
  mdGoalsAndLimits: false,
  pdsDoColumnMappingSimplification: false,
  riskyEmploymentPractices: false,
  showPdsStateInTheHub: false,
  mdTargets: false,
  renewalsSection: false,
  preProdForceRedirect: false,
  pdsAllowDoConfirmDespiteIssues: false,
  showShareholders: false,
  pdsDebugger: false,
  debugFactPanel: false,
  quoteCredits: false,
  mdTargetsValidation: false,
  pdsEntityMappingV2: false,
  pdsAllowEmConfirmDespiteIssues: false,
  feNewsSummary: false,
  internalUseFeatures: true,
  emailPreviewHidden: true,
  showNewQuotePage: false,
  quotePageRandomizer: false,
  bossSubmissionDataSyncEnabled: false,
  nwR3December: false,
  pdsEmTransactions: false,
  excerptsSubmissionFilePreview: false,
  brokerageOffice: false,
  bulkSubmissionLost: false,
  nwClearingDropdown: false,
  showMvrs: false,
  showNewsCategoriesDistribution: false,
  allowCreateOrReplaceFilesForVerifiedSub: false,
  exportPdf: false,
  customizableClassifiersPage: false,
  notesV2: false,
  showRating: false,
  showNewsSummariesByCategory: false,
  useNewArchRedirectUrl: false,
  alwaysShowAvailableBboxInPreview: false,
  enableClonesForClearingProcess: false,
  myTestFlag: false,
  showDocumentSigned: false,
  selfServe: false,
  showProfitabilityData: false,
  allowSubReverting: false,
  showNewClassifiers: false,
  useNewOverviewLayout: false,
  showOverviewSummary: false,
  showArchivedTabsInSearchBar: false,
  newRecommendationPanel: false,
};

export type FeatureFlags = typeof DEFAULT_FLAG_VALUES;

export const useFeatureFlags = () => {
  const flags = useFlags() as FeatureFlags;

  return process.env.NEXT_PUBLIC_LAUNCH_DARKLY_CLIENT_ID
    ? flags
    : {
        ...flags,
        ...DEFAULT_FLAG_VALUES,
      };
};
