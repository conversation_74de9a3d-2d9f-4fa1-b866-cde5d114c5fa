import { MainLayout } from "@features/main-layout";
import { Authenticated } from "@legacy/Authenticated";
import { CreateExportTemplate } from "@features/export-templates";
import { useRouter } from "next/router";

export default function CreateExportTemplatePage() {
  const router = useRouter();
  const type = router.query.type;
  const exportTemplateType = type === "pdf" || type === "json" ? type : "pdf";

  return (
    <Authenticated internalOnly>
      <MainLayout>
        <CreateExportTemplate type={exportTemplateType} />
      </MainLayout>
    </Authenticated>
  );
}
