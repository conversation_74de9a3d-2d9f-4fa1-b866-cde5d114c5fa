import { MainLayout } from "@features/main-layout";
import { Authenticated } from "@legacy/Authenticated";
import { EditExportTemplate } from "@features/export-templates";
import { useRouter } from "next/router";

export default function EditExportTemplatePage() {
  const router = useRouter();
  const id = router.query.id as string;

  return (
    <Authenticated internalOnly>
      <MainLayout>
        <EditExportTemplate id={id} />
      </MainLayout>
    </Authenticated>
  );
}
