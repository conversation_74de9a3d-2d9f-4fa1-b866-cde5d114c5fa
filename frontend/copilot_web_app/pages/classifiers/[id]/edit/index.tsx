import { Authenticated } from "@legacy/Authenticated";

import type { JSX } from "react";
import { useRouter } from "next/router";
import { ConfigurableClassifierPage } from "@features/configurable-classifiers/new-classifiers/ConfigurableClassifierPage";

export default function EditClassifierPage(): JSX.Element {
  const router = useRouter();
  const classifierId = router.query.id as string;

  return (
    <Authenticated>
      <ConfigurableClassifierPage classifierId={classifierId} />
    </Authenticated>
  );
}
