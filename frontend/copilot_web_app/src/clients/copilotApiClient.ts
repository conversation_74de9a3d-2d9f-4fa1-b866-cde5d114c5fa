import { datadogLogs } from "@datadog/browser-logs";
import { ReportStage, UserReportsSorting } from "@utils/constants";
import { combineUrl } from "@utils/helpers";
import {
  AdditionalFileRequest,
  AdditionalFileRequestGroupedFirstPartyFields,
  AdminUser,
  AggregatedIFTADataRequestAggregationTypeEnum,
  Brokerage,
  BrokerageEmployee,
  ClassifierMetadata,
  ClassifierTestRunEnvelope,
  ClientApplications,
  Configuration,
  CreateReport,
  CustomizableClassifier,
  CustomizableClassifierTestRun,
  DefaultApi,
  DefaultApiAddSubmissionBusinessRequest,
  DefaultApiAddSubmissionNoteRequest,
  DefaultApiAdminCreateUserRequest,
  DefaultApiAdminUpdateUserRequest,
  DefaultApiBulkAddClientSubmissionIdsRequest,
  DefaultApiCalculateGroupSuggestionsRequest,
  DefaultApiCheckPdsStatusRequest,
  DefaultApiClearSubmissionRequest,
  DefaultApiCreateBrokerageEmployeeRequest,
  DefaultApiCreateEmailClassifierRequest,
  DefaultApiCreateFileRequest,
  DefaultApiCreateHubTemplateRequest,
  DefaultApiCreateOrUpdateCustomFileTypeRequest,
  DefaultApiCreateOrUpdateWorkersCompExperienceRequest,
  DefaultApiCreatePermissionRequest,
  DefaultApiCreateQualityAuditQuestionRequest,
  DefaultApiCreateReportProcessingDependencyRequest,
  DefaultApiCreateRoutingRuleRequest,
  DefaultApiCreateStuckSubmissionFeedbackRequest,
  DefaultApiCreateSuggestionsV2Request,
  DefaultApiDeleteFileRequest,
  DefaultApiDeleteHubTemplateRequest,
  DefaultApiDeleteSubmissionBusinessRequest,
  DefaultApiDownloadFilesRequest,
  DefaultApiExecuteRequest,
  DefaultApiForceSubmissionExperimentRequest,
  DefaultApiGetAggregatedCustomFileTypesRequest,
  DefaultApiGetAggregatedFileTypesRequest,
  DefaultApiGetBrokerageEmployeesRequest,
  DefaultApiGetBusinessAtLocationRequest,
  DefaultApiGetCustomFileTypesForOrgRequest,
  DefaultApiGetDescriptionsOfOperationsRequest,
  DefaultApiGetEntityMappingRequest,
  DefaultApiGetExperimentDataRequest,
  DefaultApiGetExperimentRunsRequest,
  DefaultApiGetFactSubtypesForFieldNameRequest,
  DefaultApiGetFileByIdRequest,
  DefaultApiGetFileOnboardedDataRequest,
  DefaultApiGetFileRequest,
  DefaultApiGetFilesRequest,
  DefaultApiGetLossesRequest,
  DefaultApiGetLossFileStatusesRequest,
  DefaultApiGetLossPoliciesRequest,
  DefaultApiGetOnboardingDataRequest,
  DefaultApiGetPreferenceRequest,
  DefaultApiGetPresignedUrlRequest,
  DefaultApiGetPrometrixRisksForSubmissionBusinessRequest,
  DefaultApiGetReportsPostRequest,
  DefaultApiGetS3FileUrlRequest,
  DefaultApiGetSubmissionCoverageHistoryRequest,
  DefaultApiGetSubmissionHistoryRequest,
  DefaultApiGetSubmissionMetadataRequest,
  DefaultApiGetSubmissionNotesRequest,
  DefaultApiGetWorkersCompExperienceForSubmissionRequest,
  DefaultApiGetWorkersCompStateRatingInfoForSubmissionRequest,
  DefaultApiInvokeLossRunProcessingRequest,
  DefaultApiMergeVerifiedReportsRequest,
  DefaultApiProcessLossRunFilesRequest,
  DefaultApiPutExperimentSampleDataRequest,
  DefaultApiRemoveSubmissionNoteRequest,
  DefaultApiRevertReportToStateRequest,
  DefaultApiSetSubmissionIdentifiersRequest,
  DefaultApiStartProcessingRequest,
  DefaultApiUpdateBrokerageEmployeeRequest,
  DefaultApiUpdateCoverageGroupsRequest,
  DefaultApiUpdateEmailClassificationLabelsRequest,
  DefaultApiUpdateEmailClassifierRequest,
  DefaultApiUpdateEntityMappingRequest,
  DefaultApiUpdateExperimentDefinitionRequest,
  DefaultApiUpdateFileOnboardedDataRequest,
  DefaultApiUpdateFileRequest,
  DefaultApiUpdateHubTemplateRequest,
  DefaultApiUpdateOnboardingDataRequest,
  DefaultApiUpdatePermissionRequest,
  DefaultApiUpdatePreferenceRequest,
  DefaultApiUpdateReportRequest,
  DefaultApiUpdateRequestedCoverageRequest,
  DefaultApiUpdateRoutingRuleRequest,
  DefaultApiUpdateSubmissionNoteRequest,
  DefaultApiUpdateSubmissionRequest,
  DefaultApiUpdateUserRequest,
  DefaultApiVerifyRequest,
  Email,
  EmailTemplate,
  EmailTemplateTypeEnum,
  FactSubtypeReassignmentRequest,
  FilesClearing,
  FirstPartySuggestion,
  GetAssignmentQueueCount200Response,
  Loss as ILoss,
  LossesEnvelope,
  LossPolicy,
  LossRunFileStatus,
  MetricTemplate as IMetricTemplate,
  PresignedUrl,
  RequestedCoverage as IRequestedCoverage,
  SearchResult,
  Settings,
  SplitFileRequestPageRangesInner,
  StuckDetails,
  SubmissionBusiness as SubmissionBusinessType,
  SubmissionClientId,
  SubmissionPriority,
  SubmissionsBulkDeclineRequest,
  SubmissionSendEmailRequest,
  SubmissionUser,
  SupportUser,
  TestRunClassificationTask,
  UpsertUserSubmissionNotificationsRequest,
  UserGroup,
  DefaultApiGetOrCreateClassificationLabelRequest,
  DefaultApiDeleteClassificationLabelRequest,
  DefaultApiUpdateClassificationLabelRequest,
  DefaultApiGetSupportUsersRequest,
  DefaultApiGetFileUrlByFileIdRequest,
  DefaultApiDeleteSubmissionIdentifierRequest,
  DefaultApiGetFilesQueueRequest,
  DefaultApiAssignFileToSupportUserRequest,
  DefaultApiGetFactSubtypeSelectionBenchmarkRequest,
  DefaultApiGetBenchmarkDataRequest,
  DefaultApiGetSubmissionReportIdRequest,
  DefaultApiAddBenchmarkDataRequest,
  DefaultApiDeleteBenchmarkDataRequest,
  DefaultApiUpdateBenchmarkDataRequest,
  DefaultApiGetReportsByUserRequest,
  DefaultApiFinishExternalClearingForSubmissionRequest,
  DefaultApiGetTaxonomyMappingsRequest,
  DefaultApiGetEnhancedFileByFileIdRequest,
  DefaultApiGetMetricsV2Request,
  DefaultApiUpdateCustomizableClassifierV2Request,
  DefaultApiGetCustomizableClassifiersV2Request,
  DefaultApiCreateCustomizableClassifierV2Request,
  DefaultApiDeleteClassifierVersionRequest,
  DefaultApiChangeClassifierVersionActiveRequest,
} from "../api_clients/copilot_api_client";
import MetricPreference from "../models/MetricPreference";
import InsightFeedback from "../models/InsightFeedback";
import SubmissionBusinessHistory from "../models/SubmissionBusinessHistory";
import api from "../api/http";
import {
  getClientSideHeadersFactory,
  HeadersFactory,
} from "../api/http/headers";
import { GroupedFirstPartyFields } from "../models/types/firstParty";
import Report from "../models/Report";
import RequestedCoverage from "../models/RequestedCoverage";
import File from "../models/File";
import Reports from "../models/Reports";
import User from "../models/User";
import ResolutionResults from "@legacy/models/ResolutionResults";
import { config } from "config";
import Loss from "@legacy/models/Loss";
import { AXIOS_WITH_GZIP } from "@legacy/clients/utils";
import { MetricV2 } from "@legacy/models/MetricV2";
import { MetricFactoryV2 } from "@legacy/factories/MetricFactory";
import ersApiClient from "@legacy/clients/ersApiClient";
import uniq from "lodash/uniq";
import { FROZEN_STAGES } from "@legacy/constants/hubConstants";
import {
  CoordinatesAssignment,
  Entity,
  Snapshot,
} from "@legacy/api_clients/entity_resolution_service_client_v3";
import { sentryCaptureException } from "@legacy/services/sentryService";
import MetricTemplate from "@legacy/models/MetricTemplate";
import SubmissionCoverageHistory from "@legacy/models/SubmissionCoverageHistory";
import { IFTA } from "@legacy/models/IFTA";
import SubmissionBusiness from "@legacy/models/SubmissionBusiness";
import { normalizeNaics2 } from "@utils/naics";

const endpoint = "/copilot/api/v3.0";
const API_BASE_URL = combineUrl(config.kalepaApiBaseUrl, endpoint);

class CopilotApiClient {
  private client: DefaultApi;

  constructor(private readonly headersFactory: HeadersFactory) {
    // Openapi generator forces object serialization for all json requests,
    // the below is a hack to be able to send not stringified objects
    const isJsonMimeOverwrite = () => {
      return false;
    };
    const openapiConfig = new Configuration({ basePath: API_BASE_URL });
    openapiConfig.isJsonMime = isJsonMimeOverwrite;
    const headers = this.headersFactory();
    openapiConfig.baseOptions = { headers };
    this.client = new DefaultApi(openapiConfig, API_BASE_URL, AXIOS_WITH_GZIP);
  }

  getMetricPreferences = (id: string): Promise<MetricPreference[]> => {
    const onlyBusinessMetrics = false;
    return this.client
      .getMetricPreferences({ id, onlyBusinessMetrics })
      .then((data) => {
        return (data.data.metric_preferences || []).map(
          (m) => new MetricPreference(m)
        );
      })
      .catch((e) => {
        datadogLogs.logger.error(e);
        return [];
      });
  };

  async updateMetricPreferences(
    preferenceId: string,
    metricPreference: Partial<MetricPreference>
  ): Promise<void> {
    await this.client.updateMetricPreference({
      id: preferenceId,
      metricPreference,
    });
  }

  getMetricTemplates = (userId: number): Promise<MetricTemplate[]> => {
    return this.client
      .getMetricTemplates({ userId })
      .then((data) => {
        return (data.data.metric_templates || []).map(
          (m) => new MetricTemplate(m)
        );
      })
      .catch((e) => {
        datadogLogs.logger.error(e);
        return [];
      });
  };

  async createMetricTemplate(userId: number, metricTemplate: IMetricTemplate) {
    const template = await this.client.createMetricTemplate({
      userId,
      metricTemplate,
    });
    return new MetricTemplate(template.data);
  }

  async updateMetricTemplate(
    templateId: string,
    metricTemplate: Partial<MetricTemplate>
  ): Promise<void> {
    await this.client.updateMetricTemplate({
      id: templateId,
      metricTemplate,
    });
  }

  async deleteMetricTemplate(templateId: string): Promise<void> {
    await this.client.deleteMetricTemplate({
      id: templateId,
    });
  }

  getMetricsV2 = async (
    params: DefaultApiGetMetricsV2Request
  ): Promise<MetricV2[][]> => {
    const metricsV2Result = await this.client.getMetricsV2({
      ...params,
      getMetricsV2Request: {
        ...params.getMetricsV2Request,
        only_business_metrics:
          params.getMetricsV2Request?.only_business_metrics ?? false,
      },
    });
    return (
      metricsV2Result.data?.metrics?.map((metricPair) =>
        metricPair.map((metric) =>
          MetricFactoryV2.createMetricV2({ metric: metric })
        )
      ) ?? []
    );
  };

  getClientApplications = (userId: number): Promise<ClientApplications> => {
    return this.client
      .getClientApplicationsByUser({ userId })
      .then((data) => data.data);
  };

  searchForBusinesses = (
    name: string,
    address: string
  ): Promise<ResolutionResults> => {
    return this.client
      .resolveEntities({
        forceFullSearch: true,
        submissionBusiness: [
          {
            requested_name: name,
            requested_address: address,
            requested_industries: [],
          },
        ],
      })
      .then((data) => {
        return new ResolutionResults(data.data[0]);
      });
  };

  getInsightsFeedback = (
    userId: number,
    businessId: string
  ): Promise<InsightFeedback[]> => {
    return this.client
      .getInsightFeedbackByUser({ userId, businessId })
      .then((data) => data.data.map((d) => new InsightFeedback(d)));
  };

  getTimezones = (): Promise<Record<string, number>> => {
    return this.client
      .getTimezones()
      .then((data) => data.data as Record<string, number>);
  };

  getSubmissionHistory = async (
    requestParameters: DefaultApiGetSubmissionHistoryRequest
  ): Promise<SubmissionBusinessHistory[]> => {
    const data = await this.client.getSubmissionHistory(requestParameters);
    return (data.data.submission_history || []).map(
      (s) => new SubmissionBusinessHistory(s)
    );
  };

  getSubmissionCoverageHistory = async (
    requestParameters: DefaultApiGetSubmissionCoverageHistoryRequest
  ): Promise<SubmissionCoverageHistory[]> => {
    const data =
      await this.client.getSubmissionCoverageHistory(requestParameters);
    return (data.data.submission_coverage_history || []).map(
      (s) => new SubmissionCoverageHistory(s)
    );
  };

  getClassifiersMetadata = async (
    organizationId?: number
  ): Promise<ClassifierMetadata[]> => {
    const classifiersMetadata = [];
    let page = 1;
    // eslint-disable-next-line
    while (true) {
      // We want await inside while to limit number of concurrent requests.
      // eslint-disable-next-line
      const response = await this.client.getClassifiersMetadata({
        page,
        organizationId,
      });
      const envelope = response.data;
      classifiersMetadata.push(...envelope.classifiers_metadata);
      if (!envelope.has_next) {
        break;
      }
      page += 1;
    }
    return classifiersMetadata;
  };

  getCustomizableClassifiers = ({
    organizationId,
    activeOnly,
    factSubtypeId,
    withLatestTestRun = true,
  }: {
    organizationId?: number;
    activeOnly?: boolean;
    factSubtypeId?: string;
    withLatestTestRun?: boolean;
  }): Promise<CustomizableClassifier[]> => {
    return this.client
      .getCustomizableClassifiers({
        organizationId,
        activeOnly,
        factSubtypeId,
        withLatestTestRun,
      })
      .then((data) => data.data.customizable_classifiers || []);
  };

  createCustomizableClassifier = (
    customizableClassifier: CustomizableClassifier
  ): Promise<CustomizableClassifier> => {
    return this.client
      .createCustomizableClassifier({
        customizableClassifier,
      })
      .then((data) => data.data);
  };

  updateCustomizableClassifier = (
    id: string,
    customizableClassifier: CustomizableClassifier
  ): Promise<CustomizableClassifier> => {
    return this.client
      .updateCustomizableClassifier({
        id,
        customizableClassifier,
      })
      .then((data) => data.data);
  };

  getClassifierTestRuns = ({
    classifierId,
  }: {
    classifierId: string;
  }): Promise<ClassifierTestRunEnvelope> => {
    return this.client
      .getClassifierTestRuns({ classifierId })
      .then((data) => data.data);
  };

  getTestRun = ({
    testRunId,
  }: {
    testRunId: string;
  }): Promise<CustomizableClassifierTestRun> => {
    return this.client.getTestRun({ id: testRunId }).then((data) => data.data);
  };

  createTestRun = (classifierId: string): Promise<CustomizableClassifier> => {
    return this.client
      .createTestRun({
        classifierId,
      })
      .then((data) => data.data);
  };

  postFirstPartySuggestions = (
    suggestions: FirstPartySuggestion[]
  ): Promise<FirstPartySuggestion[]> => {
    return this.client
      .createSuggestions({
        firstPartySuggestions: {
          suggestions,
        },
      })
      .then((response) => response.data?.suggestions ?? []);
  };

  getClassificationTask = (id: string): Promise<TestRunClassificationTask> => {
    return this.client.getClassificationTask({ id }).then((data) => data.data);
  };

  processAdditionalFirstPartyDataRequest = (
    fileName: string,
    groupedFirstPastyFields: GroupedFirstPartyFields,
    submission_id: string
  ): Promise<any> => {
    const request: AdditionalFileRequest = {
      file_name: fileName,
      grouped_first_party_fields:
        groupedFirstPastyFields as AdditionalFileRequestGroupedFirstPartyFields,
      submission_id,
    };
    return this.client
      .processAdditionalFirstPartyDataRequest({
        additionalFileRequest: request,
      })
      .then((data) => data.data);
  };

  createReport = (body: CreateReport, expand?: string): Promise<Report> => {
    return this.client
      .createReport({ expand, createReport: body })
      .then((data) => new Report(data.data));
  };

  updateCoverageBySubmission = (
    submissionId: string,
    coverageId: string,
    requestedCoverage?: RequestedCoverage
  ): Promise<RequestedCoverage> => {
    return this.client
      .updateRequestedCoverageBySubmission({
        submissionId,
        coverageId,
        requestedCoverage: requestedCoverage as IRequestedCoverage,
      })
      .then((data) => new RequestedCoverage(data.data));
  };

  appendRequestedCoverage = (
    submissionId: string,
    requestedCoverage?: RequestedCoverage
  ): Promise<RequestedCoverage | null> => {
    return this.client
      .appendRequestedCoverage({
        submissionId,
        requestedCoverage: requestedCoverage as IRequestedCoverage,
      })
      .then((data) => (data ? new RequestedCoverage(data.data) : null));
  };

  deleteRequestedCoverage = (
    submissionId: string,
    coverageId: string
  ): Promise<any> => {
    return this.client.deleteRequestedCoverage({ submissionId, coverageId });
  };

  updateRequestedCoverage = (
    request: DefaultApiUpdateRequestedCoverageRequest
  ) => {
    return this.client.updateRequestedCoverage(request);
  };

  private async addErsData(
    businesses: SubmissionBusinessType[],
    isSubmissionFrozen: boolean,
    submissionId: string,
    organizationId: number
  ) {
    const coordinateOverrides =
      await ersApiClient.getCoordinatesForEntityPremises(submissionId);

    const coordinateOverridesByPremisesId: Record<
      string,
      CoordinatesAssignment
    > = {};

    coordinateOverrides.forEach((coordinateOverride) => {
      const { premises_id } = coordinateOverride;
      if (!premises_id) return;

      coordinateOverridesByPremisesId[premises_id] = coordinateOverride;
    });

    if (!isSubmissionFrozen) {
      const allIds = uniq(
        businesses.map((x) => x.business_id!).filter(Boolean)
      );

      const res = await ersApiClient.bulkGetEntities(allIds, organizationId);

      const entityMap =
        res.entities?.reduce(
          (acc, val) => {
            acc[val.id!] = val;

            val.premises?.forEach((premises) => {
              const override =
                coordinateOverridesByPremisesId[premises.premises_id!];

              if (override) {
                if (!premises.premises) return;
                premises.premises.latitude = override.latitude;
                premises.premises.longitude = override.longitude;
              }
            });

            return acc;
          },
          {} as Record<string, Entity>
        ) ?? {};

      businesses.forEach((business, i) => {
        const entity = entityMap[business.business_id!];
        if (entity) {
          businesses[i] = {
            ...business,
            entity_data: entity,
          };
        }
      });
    } else {
      const snapshotIds = uniq(
        businesses.map((x) => x.ers_snapshot_id!).filter((x) => !!x)
      );

      if (!snapshotIds.length) return;

      const snapshots: Snapshot[] =
        await ersApiClient.getSnapshots(snapshotIds);

      const snapshotMap = snapshots.reduce(
        (acc, val) => {
          acc[val.id!] = val;
          val.entity?.premises?.forEach((premises) => {
            const override =
              coordinateOverridesByPremisesId[premises.premises_id!];

            if (override) {
              if (!premises.premises) return;
              premises.premises.latitude = override.latitude;
              premises.premises.longitude = override.longitude;
            }
          });

          return acc;
        },
        {} as Record<string, Snapshot>
      );

      businesses.forEach((business, i) => {
        const snapshot = snapshotMap[business.ers_snapshot_id!];
        if (snapshot) {
          businesses[i] = {
            ...business,
            entity_data: snapshot.entity,
          };
        }
      });
    }
  }

  getReport = async (
    id: string,
    organizationId: number,
    expand?: string,
    disableRedirect = false
  ): Promise<Report | undefined> => {
    const res = await this.client.getReport({
      id,
      expand,
      enrichWithErsData: false,
    });

    if ((res.data as any).redirect_url) {
      if (disableRedirect) {
        const reportId = (res.data as any).redirect_url.split("/").pop();
        return this.getReport(reportId!, organizationId, expand);
      } else {
        window.location.href = (res.data as any).redirect_url;
        return;
      }
    }

    const submission = res.data.submissions![0];
    try {
      await this.addErsData(
        submission.businesses!,
        FROZEN_STAGES.includes(submission.stage!),
        submission.id!,
        organizationId
      );
    } catch (e) {
      sentryCaptureException(e);
    }

    return new Report(res.data);
  };

  getReports = (params: {
    userId: number;
    broker_ids?: (string | null)[];
    not_broker_ids?: (string | null)[];
    brokerage_ids?: (string | null)[];
    not_brokerage_ids?: (string | null)[];
    brokerage_offices?: (string | null)[];
    not_brokerage_offices?: (string | null)[];
    broker_group_ids?: (string | null)[];
    not_broker_group_ids?: (string | null)[];
    page?: number;
    per_page?: number;
    date_type?: "PROPOSED_EFFECTIVE_DATE" | "CREATED_AT";
    after?: string;
    not_after?: string;
    before?: string;
    not_before?: string;
    after_second?: string;
    not_after_second?: string;
    before_second?: string;
    not_before_second?: string;
    sorting?: UserReportsSorting;
    descending?: boolean;
    name?: string;
    only_owned_by_me?: boolean;
    only_shared_with_me?: boolean;
    stage?: string[];
    not_stage?: string[];
    show_renewals?: string;
    coverages?: (string | null)[];
    not_coverages?: (string | null)[];
    coverage_operator?: "AND" | "OR" | string;
    not_coverage_operator?: "AND" | "OR" | string;
    assignees?: (number | null)[];
    not_assignees?: (number | null)[];
    assignee_groups?: (string | null)[];
    not_assignee_groups?: (string | null)[];
    referred_to?: (number | null)[];
    not_referred_to?: (number | null)[];
    only_unassigned?: boolean;
    only_assigned?: boolean;
    recommended_action?: string;
    recommended_v2_action?: string;
    not_recommended_v2_action?: string;
    naics_2?: (string | null)[];
    not_naics_2?: (string | null)[];
    naics_6?: (string | null)[];
    not_naics_6?: (string | null)[];
    report_ids?: (string | null)[];
    account_id?: string;
    clearing_stage?:
      | "ALL"
      | "PENDING_CLEARING"
      | "POST_CLEARING"
      | "PENDING_LIGHT_CLEARING";
    verified?: boolean;
    verified_shell?: boolean;
    only_referred?: boolean;
    only_not_referred?: boolean;
    read?: boolean | null;
    exclude_bundled?: boolean;
    with_wc_info?: boolean;
    only_rush?: boolean;
    only_non_rush?: boolean;
    client_stage_ids?: string[];
    not_client_stage_ids?: string[] | number[];
    org_groups?: (string | null)[];
    not_org_groups?: string[];
    client_clearing_statuses?: string[];
    not_client_clearing_statuses?: (string | null)[];
    // virtual param
    clientStage?: any;
    // virtual param
    notClientStage?: any;
    includeEmail?: boolean;
    sic_2?: (string | null)[];
    not_sic_2?: (string | null)[];
    sic_code?: (string | null)[];
    not_sic_code?: (string | null)[];
    score_ml_min?: number;
    score_ml_max?: number;
    pm_rules_modifier_min?: number;
    pm_rules_modifier_max?: number;
    include_unverified?: boolean;
  }): Promise<Reports> => {
    const naics_2_normalized = normalizeNaics2(params.naics_2);
    const not_naics_2_normalized = normalizeNaics2(params.not_naics_2);

    if (!params.sorting) params.descending = undefined;

    return api
      .request(api.urls.user.reports, null, {
        ...params,
        coverages: this.joinArrayParams(params.coverages),
        not_coverages: this.joinArrayParams(params.not_coverages),
        assignees: this.joinArrayParams(params.assignees),
        not_assignees: this.joinArrayParams(params.not_assignees),
        brokerage_ids: this.joinArrayParams(params.brokerage_ids),
        not_brokerage_ids: this.joinArrayParams(params.not_brokerage_ids),
        broker_ids: this.joinArrayParams(params.broker_ids),
        not_broker_ids: this.joinArrayParams(params.not_broker_ids),
        brokerage_offices: params.brokerage_offices?.length
          ? JSON.stringify(params.brokerage_offices)
          : undefined,
        not_brokerage_offices: params.not_brokerage_offices?.length
          ? JSON.stringify(params.not_brokerage_offices)
          : undefined,
        broker_group_ids: this.joinArrayParams(params.broker_group_ids),
        not_broker_group_ids: this.joinArrayParams(params.not_broker_group_ids),
        naics_2: this.joinArrayParams(naics_2_normalized),
        not_naics_2: this.joinArrayParams(not_naics_2_normalized),
        naics_6: this.joinArrayParams(params.naics_6),
        not_naics_6: this.joinArrayParams(params.not_naics_6),
        sic_2: this.joinArrayParams(params.sic_2),
        not_sic_2: this.joinArrayParams(params.not_sic_2),
        sic_code: this.joinArrayParams(params.sic_code),
        not_sic_code: this.joinArrayParams(params.not_sic_code),
        report_ids: this.joinArrayParams(params.report_ids),
        referred_to: this.joinArrayParams(params.referred_to),
        not_referred_to: this.joinArrayParams(params.not_referred_to),
        assignee_groups: this.joinArrayParams(params.assignee_groups),
        not_assignee_groups: this.joinArrayParams(params.not_assignee_groups),
        stage: this.joinArrayParams(params.stage),
        not_stage: this.joinArrayParams(params.not_stage),
        client_stage_ids: this.joinArrayParams(params.client_stage_ids),
        not_client_stage_ids: this.joinArrayParams(params.not_client_stage_ids),
        org_groups: this.joinArrayParams(params.org_groups),
        not_org_groups: this.joinArrayParams(params.not_org_groups),
        include_email: params.includeEmail,
        client_clearing_statuses: this.joinArrayParams(
          params.client_clearing_statuses
        ),
        not_client_clearing_statuses: this.joinArrayParams(
          params.not_client_clearing_statuses
        ),
      })
      .then((data) => {
        return new Reports(data);
      });
  };

  getAllReports = async ({
    organizationId,
    submissionIds,
    page,
    perPage,
    orderBy,
    descending,
    search,
    stage,
    dateFrom,
    dateTo,
    assignee,
  }: {
    organizationId: number;
    submissionIds: string[];
    page: number;
    perPage: number;
    orderBy?: string;
    descending?: boolean;
    search?: string;
    stage?: string;
    dateFrom?: string;
    dateTo?: string;
    assignee?: number;
  }) => {
    const response = await this.client.getReports({
      organizationId,
      submissionIds,
      page,
      perPage,
      orderBy: orderBy || "created_at",
      descending,
      search,
      stage,
      dateFrom,
      dateTo,
      assignee,
    });
    return response.data;
  };

  getAllReportsPost = async (request: DefaultApiGetReportsPostRequest) => {
    const response = await this.client.getReportsPost(request);
    return response.data;
  };

  async postRuleInvocations({
    reportIds,
    before,
    after,
    forceManual,
  }: {
    reportIds?: string[];
    before?: string;
    after?: string;
    forceManual?: boolean;
  }) {
    const params = {
      report_ids: reportIds,
      force_manual: forceManual,
      before,
      after,
    };
    const result = await this.client.invokeRecommendation({
      invokeRecommendationsRequest: params,
    });
    return result.data;
  }

  async validateRequestedRecommendations({
    reportIds,
  }: {
    reportIds: string[];
  }) {
    const result = await this.client.validateRequestedRecommendations({
      invokeRecommendationsRequest: { report_ids: reportIds },
    });
    return result.data;
  }

  updateSettings = (id: string, settings: Settings): Promise<Settings> => {
    return this.client
      .updateSettings({ id, settings })
      .then((data) => data.data);
  };

  getFileStats = async (organizationId: number, createdAfter?: string) => {
    const { data } = await this.client.getFileStats({
      organizationId,
      createdAfter,
    });
    return data;
  };

  getBrokerages = async (organizationId: number) => {
    const { data } = await this.client.getBrokerages({
      organizationId,
    });

    return data.brokerages;
  };

  createBrokerage = async (brokerage: Brokerage) => {
    const data = await this.client.createBrokerage({ brokerage });
    return data.data;
  };

  updateBrokerage = async (brokerageId: string, brokerage: Brokerage) => {
    const data = await this.client.updateBrokerage({
      id: brokerageId,
      brokerage,
    });
    return data.data;
  };

  getBrokerageEmployees = async (
    request: DefaultApiGetBrokerageEmployeesRequest
  ) => {
    const res = await this.client.getBrokerageEmployees(request);

    return res.data.brokerage_employees;
  };

  createBrokerageEmployee = async (
    request: DefaultApiCreateBrokerageEmployeeRequest
  ) => {
    const res = await this.client.createBrokerageEmployee(request);

    return res.data;
  };

  updateBrokerageEmployee = async (
    request: DefaultApiUpdateBrokerageEmployeeRequest
  ) => {
    const { data } = await this.client.updateBrokerageEmployee(request);
    return data;
  };

  async getDashboardURL(name: string): Promise<string | undefined> {
    const res = await this.client.generateEmbedUrl({ name });
    return res.data.url;
  }

  async postOpenReport(userId: number, reportId: string): Promise<void> {
    await this.client.addUserOpenReport({ userId, reportId });
  }

  async deleteOpenReports(userId: number, reportIds: string[]): Promise<void> {
    await this.client.deleteUserOpenReports({
      userId,
      deleteUserOpenReportsRequest: { report_ids: reportIds },
    });
  }

  async getOpenReports(
    userId: number
  ): Promise<ReadonlyArray<{ id: string; name: string }>> {
    const res = await this.client.getUserOpenReports({ userId });
    return res.data as unknown as ReadonlyArray<{ id: string; name: string }>;
  }

  updateSubmissionBusiness = (
    submissionId: string,
    id: string,
    submissionBusiness: SubmissionBusinessType
  ): Promise<SubmissionBusiness | void> => {
    return this.client
      .updateSubmissionBusiness({ submissionId, id, submissionBusiness })
      .then((data) => data.data);
  };

  async getFile(params: DefaultApiGetFileRequest): Promise<File> {
    const res = await this.client.getFile(params);
    return new File(res.data);
  }

  async getFileByParent(params: DefaultApiGetFileRequest): Promise<File> {
    const res = await this.client.getFileByParentFileId(params);
    return new File(res.data);
  }

  async getFilePresignedUrl(
    params: DefaultApiGetPresignedUrlRequest
  ): Promise<PresignedUrl> {
    return (await this.client.getPresignedUrl(params)).data;
  }

  async getFileTypes() {
    return this.client.getFileTypes();
  }

  async deleteFile(params: DefaultApiDeleteFileRequest) {
    return this.client.deleteFile(params);
  }

  async getPrometrixRisks(
    request: DefaultApiGetPrometrixRisksForSubmissionBusinessRequest
  ) {
    const res =
      await this.client.getPrometrixRisksForSubmissionBusiness(request);
    return res.data;
  }

  async createOshaViolation(submissionId: string, url: string): Promise<void> {
    await this.client.createOshaViolation({
      submissionId,
      oshaViolationRequest: { url },
    });
  }

  async deleteReport(reportId: string, reason?: string): Promise<void> {
    await this.client.deleteReport({ id: reportId, deletionReason: reason });
  }

  async getCoverages(organizationId: number, includeDisabled = false) {
    return this.client
      .getCoverages({ organizationId, includeDisabled })
      .then((res) => res.data.coverages!);
  }

  addSubmissionUser(
    userId: number,
    submissionId: string
  ): Promise<SubmissionUser> {
    return this.client
      .addSubmissionUser({
        submissionUser: { user_id: userId, submission_id: submissionId },
        submissionId,
      })
      .then((data) => data.data);
  }

  deleteSubmissionUser(userId: number, submissionId: string): Promise<void> {
    return this.client
      .deleteSubmissionUser({ userId, submissionId })
      .then((data) => data.data);
  }

  async reassignFirstParty({
    submissionId,
    request,
  }: {
    submissionId: string;
    request: FactSubtypeReassignmentRequest;
  }) {
    return this.client
      .reassignFirstPartyFactSubtype({
        factSubtypeReassignmentRequest: request,
        submissionId,
        invokeAsync: true,
      })
      .then((data) => data.data);
  }

  async getS3FileUrl(request: DefaultApiGetS3FileUrlRequest) {
    const res = await this.client.getS3FileUrl(request);
    return res.data;
  }

  async bookmarkSubmission(
    userId: number,
    submissionId: string
  ): Promise<void> {
    await this.client.bookmark({
      id: userId,
      bookmark: { submission_id: submissionId },
    });
  }

  async deleteBookmark(userId: number, submissionId: string): Promise<void> {
    await this.client.deleteBookmark({
      id: userId,
      submissionId,
    });
  }

  async bulkAddClientSubmissionIds(
    request: DefaultApiBulkAddClientSubmissionIdsRequest
  ): Promise<void> {
    await this.client.bulkAddClientSubmissionIds(request);
  }

  addClientSubmissionId(
    clientSubmissionId: string,
    submissionId: string
  ): Promise<SubmissionClientId> {
    return this.client
      .addClientSubmissionId({
        submissionClientId: {
          client_submission_id: clientSubmissionId,
          submission_id: submissionId,
          source: "MANUAL",
        },
        submissionId,
      })
      .then((data) => data.data);
  }

  deleteClientSubmissionId(
    clientSubmissionId: string,
    submissionId: string
  ): Promise<void> {
    return this.client
      .deleteClientSubmissionId({ clientSubmissionId, submissionId })
      .then((data) => data.data);
  }

  async getUsersByOrganization(
    organizationId: number,
    getAllAssigned?: boolean,
    includeSupport?: boolean,
    expand?: string[]
  ): Promise<User[]> {
    const { data } = await this.client.getUsersByOrganization({
      organizationId,
      getAllAssigned,
      expand,
    });
    if (includeSupport) {
      return data as unknown as User[];
    }
    return data.filter(
      (u) => !u.applicable_settings?.is_support
    ) as unknown as User[];
  }

  async adminGetUsers(
    organizationId: number,
    excludeCrossOrgUsers = true
  ): Promise<AdminUser[]> {
    const { data } = await this.client.adminGetUsers({
      organizationId,
      excludeCrossOrgUsers,
    });
    return data;
  }

  async adminUpdateUser(request: DefaultApiAdminUpdateUserRequest) {
    return this.client.adminUpdateUser(request);
  }

  async adminCreateUser(request: DefaultApiAdminCreateUserRequest) {
    return this.client.adminCreateUser(request);
  }

  private joinArrayParams = (elements?: (string | number | null)[]) => {
    return elements?.filter(Boolean).join(",") || undefined;
  };

  async updateSubmission(request: DefaultApiUpdateSubmissionRequest) {
    await this.client.updateSubmission(request);
  }

  addSubmissionNote(request: DefaultApiAddSubmissionNoteRequest) {
    return this.client.addSubmissionNote(request);
  }

  addSubmissionBusiness(request: DefaultApiAddSubmissionBusinessRequest) {
    return this.client.addSubmissionBusiness(request);
  }

  async getSubmissionNotes(request: DefaultApiGetSubmissionNotesRequest) {
    const { data } = await this.client.getSubmissionNotes(request);
    return data;
  }

  updateSubmissionNote(request: DefaultApiUpdateSubmissionNoteRequest) {
    return this.client.updateSubmissionNote(request);
  }

  removeSubmissionNote(request: DefaultApiRemoveSubmissionNoteRequest) {
    return this.client.removeSubmissionNote(request);
  }

  updateUser(request: DefaultApiUpdateUserRequest) {
    return this.client.updateUser(request);
  }

  async updateReportPermission(request: DefaultApiUpdatePermissionRequest) {
    await this.client.updatePermission(request);
  }

  async deletePermission(id: string) {
    await this.client.deletePermission({ id });
  }

  async createReportPermission(request: DefaultApiCreatePermissionRequest) {
    return this.client.createPermission(request);
  }

  async updateReport(request: DefaultApiUpdateReportRequest) {
    return this.client.updateReport(request);
  }

  async search(
    query: string,
    limit: number,
    filterQuery?: string
  ): Promise<SearchResult> {
    const { data } = await this.client.search({
      query,
      limit,
      filterQuery,
    });
    return data;
  }

  async updateFile(request: DefaultApiUpdateFileRequest) {
    return this.client.updateFile(request);
  }

  async getOnboardedData(request: DefaultApiGetOnboardingDataRequest) {
    const { data } = await this.client.getOnboardingData(request);
    return data;
  }

  async getEntityMapping(request: DefaultApiGetEntityMappingRequest) {
    const { data } = await this.client.getEntityMapping(request);
    return data;
  }

  async updateEntityMapping(request: DefaultApiUpdateEntityMappingRequest) {
    return this.client.updateEntityMapping(request).then((res) => res.data);
  }

  async getUnits() {
    return this.client.getUnits();
  }

  async updateOnboardingData(request: DefaultApiUpdateOnboardingDataRequest) {
    return this.client.updateOnboardingData(request).then((res) => res.data);
  }

  async createStuckSubmissionFeedback(
    request: DefaultApiCreateStuckSubmissionFeedbackRequest
  ) {
    return this.client.createStuckSubmissionFeedback(request);
  }

  async verifySubmission(request: DefaultApiVerifyRequest) {
    return this.client.verify(request);
  }

  async downloadFiles(request: DefaultApiDownloadFilesRequest) {
    return this.client.downloadFiles(request);
  }

  async downloadLoadedData(request: DefaultApiDownloadFilesRequest) {
    return this.client.downloadSubmissionLoadedData(request);
  }

  async getSubmissionBusinessTenants(
    request: DefaultApiGetBusinessAtLocationRequest
  ) {
    return this.client.getBusinessAtLocation(request);
  }

  async getLosses(
    request: DefaultApiGetLossesRequest
  ): Promise<LossesEnvelope> {
    const { data } = await this.client.getLosses(request);
    return data;
  }

  getLossPolicies(
    request: DefaultApiGetLossPoliciesRequest
  ): Promise<LossPolicy[]> {
    return this.client.getLossPolicies(request).then((data) => data.data);
  }

  getLossFileStatuses(
    request: DefaultApiGetLossFileStatusesRequest
  ): Promise<LossRunFileStatus[]> {
    return this.client.getLossFileStatuses(request).then((data) => data.data);
  }

  async invokeLossRunProcessing(
    request: DefaultApiInvokeLossRunProcessingRequest
  ) {
    return this.client.invokeLossRunProcessing(request);
  }

  async processLossRuns(request: DefaultApiProcessLossRunFilesRequest) {
    return this.client.processLossRunFiles(request);
  }

  appendLoss = (id: string, loss: ILoss): Promise<Loss> => {
    return this.client
      .appendLoss({
        submissionId: id,
        loss,
      })
      .then((data) => new Loss(data.data));
  };

  updateLoss = (id: string, loss: Partial<ILoss>): Promise<Loss> => {
    return this.client
      .updateLoss({
        id,
        loss,
      })
      .then((data) => new Loss(data.data));
  };

  deleteLoss = (id: string) => {
    return this.client.deleteLoss({
      id,
    });
  };

  async updateCoverageGroups(request: DefaultApiUpdateCoverageGroupsRequest) {
    return this.client.updateCoverageGroups(request);
  }

  async getUserEmailTemplates(
    userId: number,
    exclude?: EmailTemplateTypeEnum[]
  ): Promise<EmailTemplate[]> {
    const { data } = await this.client.getAllUserEmailTemplates({
      userId,
      exclude,
    });
    return data.templates || [];
  }

  async createEmailTemplate(emailTemplate: EmailTemplate) {
    return this.client
      .createEmailTemplate({ emailTemplate })
      .then((data) => data.data);
  }

  async deleteEmailTemplate(id: string) {
    return this.client.deleteEmailTemplate({ id }).then((data) => data.data);
  }

  async updateEmailTemplate(id: string, emailTemplate: EmailTemplate) {
    return this.client
      .updateEmailTemplate({ id, emailTemplate })
      .then((data) => data.data);
  }

  async bundleReports(reportIds: string[]) {
    return this.client.bundleReports({
      bundleReportsRequest: { report_ids: reportIds },
    });
  }

  async unbundleReports(reportIds: string[]) {
    return this.client.unbundleReports({
      unbundleReportsRequest: {
        report_ids: reportIds,
      },
    });
  }

  async getRecommendationAggregation() {
    return (await this.client.getRecommendationAggregation()).data;
  }

  async sendSubmissionDeclinedEmail(request: SubmissionSendEmailRequest) {
    return (
      await this.client.sendSubmissionDeclinedEmail({
        submissionSendEmailRequest: request,
      })
    ).data;
  }

  async sendSubmissionEmail(request: SubmissionSendEmailRequest) {
    return (
      await this.client.sendSubmissionEmail({
        submissionSendEmailRequest: request,
      })
    ).data;
  }

  async bulkDeclineSubmissions(request: SubmissionsBulkDeclineRequest) {
    return await this.client.bulkDeclineSubmissions({
      submissionsBulkDeclineRequest: request,
    });
  }

  async getHubTemplates(
    userId: number,
    templateType: "HUB" | "DASHBOARD" = "HUB"
  ) {
    return (await this.client.getHubTemplates({ userId, templateType })).data;
  }

  createHubTemplate(params: DefaultApiCreateHubTemplateRequest) {
    return this.client.createHubTemplate(params);
  }

  updateHubTemplate(params: DefaultApiUpdateHubTemplateRequest) {
    return this.client.updateHubTemplate(params);
  }

  deleteHubTemplate(params: DefaultApiDeleteHubTemplateRequest) {
    return this.client.deleteHubTemplate(params);
  }

  startPdsFileProcessing(params: DefaultApiStartProcessingRequest) {
    return this.client.startProcessing(params);
  }

  async getOrganizations() {
    const organizations = await this.client.getAllOrganizations();
    return organizations.data.organizations;
  }

  async getOrganizationById(organizationId: number, expand?: string[]) {
    const organizations = await this.client.getOrganizationById({
      organizationId,
      expand,
    });
    return organizations.data;
  }

  async deleteSubmissionBusiness(
    request: DefaultApiDeleteSubmissionBusinessRequest
  ) {
    return this.client.deleteSubmissionBusiness(request);
  }

  getReportEmails = (reportId: string): Promise<Email[]> => {
    return this.client.getEmailsForReport({ reportId }).then((data) => {
      return data.data;
    });
  };

  async clearSubmission(request: DefaultApiClearSubmissionRequest) {
    return this.client.clearSubmission(request);
  }

  async finishBusinessConfirmation(submissionId: string, fileIds: string[]) {
    return this.client.finishBusinessConfirmation({
      submissionId,
      finishBusinessConfirmationRequest: { file_ids: fileIds },
    });
  }

  async createAuditQuestion(
    request: DefaultApiCreateQualityAuditQuestionRequest
  ) {
    return this.client.createQualityAuditQuestion(request);
  }

  async createReportProcessingDependency(
    parameters: NonNullable<
      DefaultApiCreateReportProcessingDependencyRequest["createReportProcessingDependencyRequest"]
    >
  ) {
    return this.client.createReportProcessingDependency({
      createReportProcessingDependencyRequest: parameters,
    });
  }

  async mergeCorrespondence(fromReportId: string, toReportId: string) {
    return this.client.mergeCorrespondence({
      fromId: fromReportId,
      toId: toReportId,
    });
  }

  async getSubmissionsQueueCount(
    excludeAssigned: boolean
  ): Promise<GetAssignmentQueueCount200Response> {
    return this.client
      .getAssignmentQueueCount({ excludeAssigned })
      .then((data) => data.data);
  }

  async getSubmissionsQueue(params: {
    page?: number;
    perPage?: number;
    name?: string;
    stuckOnly?: boolean;
    excludeMissingData?: boolean;
    missingFilesOnly?: boolean;
    invalidFilesOnly?: boolean;
    missingDataOnly?: boolean;
    needsClearingOnly?: boolean;
    forAutoAssignmentOnly?: boolean;
    excludeAssigned?: boolean;
    shadowOnly?: boolean;
    escalatedOnly?: boolean;
    excludeEscalated?: boolean;
  }): Promise<Reports> {
    return this.client
      .getSubmissionsQueue(params)
      .then((data) => new Reports(data.data));
  }

  async getFilesQueue(request: DefaultApiGetFilesQueueRequest) {
    const res = await this.client.getFilesQueue(request);
    return res.data;
  }

  async assignFileToSupportUser(
    request: DefaultApiAssignFileToSupportUserRequest
  ) {
    const res = await this.client.assignFileToSupportUser(request);
    return res.data;
  }

  async getSubmissionsNaicsQueue(params: {
    page?: number;
    perPage?: number;
  }): Promise<Reports> {
    return this.client
      .getSubmissionsNaicsQueue(params)
      .then((data) => new Reports(data.data));
  }

  async createOrUpdateSubmissionPriority(
    submissionId: string,
    submissionPriority: SubmissionPriority
  ) {
    return this.client.createOrUpdateSubmissionPriority({
      submissionId,
      submissionPriority,
    });
  }

  async updateSupportUser(userId: string, supportUser: SupportUser) {
    return this.client.updateSupportUser({
      userId,
      supportUser,
    });
  }

  async getSupportUsers(
    params: DefaultApiGetSupportUsersRequest
  ): Promise<SupportUser[]> {
    return this.client.getSupportUsers(params).then((data) => data.data);
  }

  async getCurrentSupportUser(): Promise<SupportUser> {
    return this.client.getCurrentSupportUser().then((data) => data.data);
  }

  async getPresignedUrl(params: DefaultApiGetPresignedUrlRequest) {
    return this.client.getPresignedUrl(params).then((res) => res.data);
  }

  createFile = (request: DefaultApiCreateFileRequest) => {
    return this.client.createFile(request).then((data) => data.data);
  };

  async getUserSignupData() {
    return this.client.getUserSignupData().then((res) => res.data);
  }

  async getSubmissionsRecommendationsBreakdown() {
    const respone = await this.client.getSubmissionsRecommendationsBreakdown();
    return respone.data;
  }

  async getUserGroups(organizationId: number) {
    const response = await this.client.getUserGroups({ organizationId });
    return response.data;
  }

  async createUserGroup(userGroup: UserGroup) {
    const response = await this.client.createUserGroup({ userGroup });
    return response.data;
  }

  async deleteUserGroup(id: string) {
    const response = await this.client.deleteUserGroup({ id });
    return response.data;
  }

  async updateUserGroup(id: string, userGroup: UserGroup) {
    const response = await this.client.updateUserGroup({ id, userGroup });
    return response.data;
  }

  async addUserToGroup(groupId: string, userId: number) {
    const response = await this.client.addUserToGroup({ id: groupId, userId });
    return response.data;
  }

  async removeUserFromGroup(groupId: string, userId: number) {
    const response = await this.client.removeUserFromGroup({
      id: groupId,
      userId,
    });
    return response.data;
  }

  async calculateGroupSuggestions(
    reqData: DefaultApiCalculateGroupSuggestionsRequest
  ) {
    return this.client
      .calculateGroupSuggestions(reqData)
      .then((res) => res.data);
  }

  async getWorkersCompStateRating(
    requestParameters: DefaultApiGetWorkersCompStateRatingInfoForSubmissionRequest
  ) {
    const res =
      await this.client.getWorkersCompStateRatingInfoForSubmission(
        requestParameters
      );

    return res.data;
  }

  async getWorkersCompExperienceForSubmission(
    requestParameters: DefaultApiGetWorkersCompExperienceForSubmissionRequest
  ) {
    return this.client
      .getWorkersCompExperienceForSubmission(requestParameters)
      .then((res) => res.data);
  }

  async getIftaData({ fileId }: { fileId: string }) {
    const res = await this.client.getIftaData({
      fileId,
    });
    return res.data;
  }

  async createIftaData({ body }: { body: IFTA }): Promise<IFTA> {
    const res = await this.client.createIftaData({ iFTAData: body });
    return new IFTA(res.data);
  }

  async updateIftaData({
    id,
    body,
  }: {
    id: string;
    body: Partial<IFTA>;
  }): Promise<IFTA> {
    const res = await this.client.updateIftaData({ id, iFTAData: body });
    return new IFTA(res.data);
  }

  async deleteIftaData({ id }: { id: string }) {
    return this.client.deleteIftaData({ id });
  }

  async getIftaAggregation({
    submissionId,
    aggregationType = AggregatedIFTADataRequestAggregationTypeEnum.AllJurisdictionsTotal,
    dateFrom,
    dateTo,
    fileId,
  }: {
    submissionId: string;
    aggregationType?: AggregatedIFTADataRequestAggregationTypeEnum;
    dateFrom?: string;
    dateTo?: string;
    fileId?: string;
  }) {
    const res = await this.client.getAggregatedIftaData({
      submissionId,
      aggregatedIFTADataRequest: {
        aggregation_type: aggregationType,
        date_from: dateFrom,
        date_to: dateTo,
      },
      fileId: fileId,
    });
    return res.data;
  }

  async getIftaAggregationQuarters({
    submissionId,
    aggregationType = AggregatedIFTADataRequestAggregationTypeEnum.AllJurisdictionsTotal,
  }: {
    submissionId: string;
    aggregationType?: AggregatedIFTADataRequestAggregationTypeEnum;
  }) {
    const res = await this.client.getAggregatedIftaQuarters({
      submissionId,
      aggregationType,
    });
    return res.data;
  }

  async getShadowOrShadowedReport(reportId: string) {
    const res = await this.client.getShadowOrShadowedReport({ reportId });
    return res.data;
  }

  async getClassCodes() {
    const res = await this.client.getClassCodes();
    return res.data;
  }

  async reprocessSubmission(reportId: string) {
    const res = await this.client.restartProcessing({
      id: reportId,
      force: true,
    });
    return res.data;
  }

  async reprocessFile(fileId: string) {
    const res = await this.client.reprocessFile({
      fileId,
    });
    return res.data;
  }

  revertReportToState(params: DefaultApiRevertReportToStateRequest) {
    return this.client.revertReportToState(params);
  }

  async updateFilesClearing(
    submissionId: string,
    filesClearing: FilesClearing
  ) {
    const response = await this.client.updateFilesClearing({
      submissionId,
      filesClearing,
    });
    return response.data;
  }

  async cancelPds(reportId: string) {
    const res = await this.client.cancelProcessing({
      id: reportId,
    });
    return res.data;
  }

  async getSubmissionBusinessResolutionData(
    submissionId: string,
    fileProcessingState: Parameters<
      DefaultApi["getSubmissionBrd"]
    >[0]["fileProcessingState"]
  ) {
    const res = await this.client.getSubmissionBrd({
      submissionId,
      fileProcessingState,
    });
    return res.data;
  }

  async readSubmission(submissionId: string) {
    await this.client.readSubmission({ id: submissionId });
  }

  async unreadSubmission(submissionId: string) {
    await this.client.unreadSubmission({ id: submissionId });
  }

  async getColumnSuggenstions(params: DefaultApiCreateSuggestionsV2Request) {
    const response = await this.client.createSuggestionsV2(params);

    return response.data;
  }

  async getResolutionIdentifiers() {
    const res = await this.client.getResolutionIdentifiers();
    return res.data;
  }

  updateProcessedFileResolutionDataRow(id: string, rowIndex: number, row: any) {
    return this.client.updateProcessedFileResolutionDataRow({
      id,
      pFResolutionDataRowUpdate: { row_index: rowIndex, row },
    });
  }

  async createOrUpdateStuckDetails(
    submissionId: string,
    stuckDetails: StuckDetails
  ) {
    return this.client.createOrUpdateStuckDetails({
      submissionId,
      stuckDetails,
    });
  }

  async getStuckDetails(submissionId: string): Promise<StuckDetails[]> {
    const res = await this.client.getStuckDetails({
      submissionId,
    });
    return res.data.stuck_details ?? [];
  }

  async getFileById(
    requestParameters: DefaultApiGetFileByIdRequest
  ): Promise<File> {
    const res = await this.client.getFileById(requestParameters);
    return new File(res.data);
  }

  async getFiles(
    requestParameters: DefaultApiGetFilesRequest
  ): Promise<File[]> {
    const res = await this.client.getFiles(requestParameters);
    return res.data.map((file) => new File(file));
  }

  async getUsersSubmissionNotifications(submissionId: string) {
    const res = await this.client.getUsersSubmissionNotifications({
      id: submissionId,
    });

    return res.data;
  }

  async upsertUsersSubmissionNotifications(
    submissionId: string,
    request: UpsertUserSubmissionNotificationsRequest
  ) {
    const res = await this.client.upsertUserSubmissionNotifications({
      id: submissionId,
      upsertUserSubmissionNotificationsRequest: request,
    });

    return res.data;
  }

  async getAllExperimentDefinitions() {
    const res = await this.client.getAllExperimentDefinitions();
    return res.data;
  }

  async updateExperimentDefinition(
    req: DefaultApiUpdateExperimentDefinitionRequest
  ) {
    const res = await this.client.updateExperimentDefinition(req);
    return res.data;
  }

  async getExperimentData(req: DefaultApiGetExperimentDataRequest) {
    const res = await this.client.getExperimentData(req);
    return res.data;
  }

  async forceSubmissionExperiment(
    req: DefaultApiForceSubmissionExperimentRequest
  ) {
    const res = await this.client.forceSubmissionExperiment(req);
    return res.data;
  }

  async putExperimentSampleData(req: DefaultApiPutExperimentSampleDataRequest) {
    const res = await this.client.putExperimentSampleData(req);
    return res.data;
  }

  async getExperimentRuns(
    requestParameters: DefaultApiGetExperimentRunsRequest
  ) {
    const res = await this.client.getExperimentRuns(requestParameters);
    return res.data;
  }

  async getSubmissionReportPublicInfo(id: string, organizationId: number) {
    const res = await this.client.getSubmissionReportPublicInfo({
      id,
    });

    const submission = res.data.submissions![0];
    try {
      await this.addErsData(
        submission.businesses!,
        FROZEN_STAGES.includes(submission.stage!),
        submission.id!,
        organizationId
      );
    } catch (e) {
      sentryCaptureException(e);
    }

    return new Report(res.data);
  }

  async fixEmExperimentRuns() {
    const res = await this.client.fixEmExperimentRuns();

    return res.data;
  }

  async getOrFindSimilarBrokerages(brokerage: Brokerage) {
    const res = await this.client.getOrFindSimilarBrokerages({
      brokerage,
    });

    return res.data;
  }

  async getOrFindSimilarBrokers(brokerageEmployee: BrokerageEmployee) {
    const res = await this.client.getOrFindSimilarBrokers({
      brokerageEmployee,
    });

    return res.data;
  }

  async getSubmissionRelations(submissionId: string) {
    const res = await this.client.getSubmissionRelations({
      fromSubmissionId: submissionId,
      relationType: "RENEWAL",
    });

    return res.data;
  }

  async getReportExport(reportId: string) {
    const result = await this.client.getReportExport(
      { id: reportId },
      { responseType: "arraybuffer" }
    );
    return result.data;
  }

  async getSubmissionEmailDynamicData(submissionId: string) {
    const res = await this.client.getSubmissionEmailDynamicData({
      submissionId,
    });

    return res.data as Record<string, string>;
  }

  async getSupportEmail() {
    const result = await this.client.getSupportEmail();
    return result.data;
  }

  async getRushReportEvidence(reportId: string) {
    const result = await this.client.getRushReportEvidence({
      id: reportId,
    });

    return result.data;
  }

  async getClientSubmissionStageConfig() {
    const result = await this.client.getClientSubmissionStageConfig();
    return result.data;
  }

  async getSubmissionShareholders(submissionId: string) {
    const result = await this.client.getSubmissionShareholders({
      submissionId: submissionId,
    });
    return result.data;
  }

  async getSubmissionFilesData(submissionId: string) {
    const result = await this.client.getSubmissionFilesData({
      submissionId: submissionId,
    });
    return result.data;
  }

  async getBrokerageOffices() {
    const result = await this.client.getBrokerageOffices();
    return result.data;
  }

  async getBrokerGroups(organizationId: number) {
    const res = await this.client.getBrokerGroupsForOrganization({
      organizationId,
    });

    return res.data;
  }

  async searchBusiness(
    name: string,
    address: string,
    ersComposedMode: boolean,
    skipCache: boolean,
    identifiers: SubmissionBusiness["requested_identifiers"] = [],
    industries: string[] = [],
    categories: string[] | undefined = undefined
  ) {
    const result = await this.client.resolveEntities({
      allowComposedNodes: ersComposedMode,
      forceFullSearch: true,
      categories,
      skipCache: skipCache,
      submissionBusiness: [
        {
          requested_name: name,
          requested_address: address,
          requested_industries: industries,
          requested_identifiers: identifiers,
        },
      ],
    });

    return new ResolutionResults(result.data[0]);
  }

  async getSubmissionMetadata(params: DefaultApiGetSubmissionMetadataRequest) {
    const result = await this.client.getSubmissionMetadata(params);
    return result.data;
  }

  async setSubmissionIdentifiers(
    request: DefaultApiSetSubmissionIdentifiersRequest
  ) {
    await this.client.setSubmissionIdentifiers(request);
  }

  async deleteSubmissionIdentifier(
    request: DefaultApiDeleteSubmissionIdentifierRequest
  ) {
    await this.client.deleteSubmissionIdentifier(request);
  }

  async executeQuote(params: DefaultApiExecuteRequest) {
    const res = await this.client.execute(params);
    return res.data;
  }

  async getFactSubtypesForFieldName({
    fieldNames,
    parentType,
    limit,
  }: DefaultApiGetFactSubtypesForFieldNameRequest) {
    const res = await this.client.getFactSubtypesForFieldName({
      fieldNames,
      parentType,
      limit,
    });
    return res.data;
  }

  async closeReferral(id: string) {
    await this.client.closeSubmissionReferral({ id });
  }

  async getDescriptionsOfOperations(
    params: DefaultApiGetDescriptionsOfOperationsRequest
  ) {
    const res = await this.client.getDescriptionsOfOperations(params);
    return res.data;
  }

  async checkPdsStatus(requestParameters: DefaultApiCheckPdsStatusRequest) {
    const res = await this.client.checkPdsStatus(requestParameters);
    return res.data;
  }

  async getLobTypes() {
    const res = await this.client.getLobTypes();
    return res.data;
  }

  async getPreference(requestParameters: DefaultApiGetPreferenceRequest) {
    const res = await this.client.getPreference(requestParameters);
    return res.data;
  }

  async mergeVerifiedReports(request: DefaultApiMergeVerifiedReportsRequest) {
    const res = await this.client.mergeVerifiedReports(request);
    return res.data;
  }

  async updatePreference(requestParameters: DefaultApiUpdatePreferenceRequest) {
    const res = await this.client.updatePreference(requestParameters);
    return res.data;
  }

  async completeSubmission(requestParameters: { submissionId: string }) {
    const res = await this.client.completeSubmission(requestParameters);
    return res.data;
  }

  async getFileOnboardedData(params: DefaultApiGetFileOnboardedDataRequest) {
    const res = await this.client.getFileOnboardedData(params);
    return res.data;
  }

  async updateFileOnboardedData(
    params: DefaultApiUpdateFileOnboardedDataRequest
  ) {
    const res = await this.client.updateFileOnboardedData(params);
    return res.data;
  }

  async getRoutingRules() {
    const res = await this.client.getRoutingRules();
    return res.data;
  }

  async getEmailClassifiers() {
    const res = await this.client.getEmailClassifiers();

    return res.data;
  }

  async createEmailClassifier(request: DefaultApiCreateEmailClassifierRequest) {
    const res = await this.client.createEmailClassifier(request);
    return res.data;
  }

  async updateEmailClassifier(request: DefaultApiUpdateEmailClassifierRequest) {
    const res = await this.client.updateEmailClassifier(request);
    return res.data;
  }

  async getBusinessesBySubmission(
    submissionId: string,
    stage: ReportStage,
    organizationId: number
  ) {
    const res = await this.client.getBusinessesBySubmission({
      submissionId,
    });

    try {
      await this.addErsData(
        res.data,
        FROZEN_STAGES.includes(stage!),
        submissionId,
        organizationId
      );
    } catch (e) {
      sentryCaptureException(e);
    }

    return res.data.map((x) => new SubmissionBusiness(x as any));
  }

  async deleteEmailClassifier(id: string) {
    const res = await this.client.deleteEmailClassifier({ id });
    return res.data;
  }

  async deleteClassificationLabel(
    requestParameters: DefaultApiDeleteClassificationLabelRequest
  ) {
    const res = await this.client.deleteClassificationLabel(requestParameters);
    return res.data;
  }

  async getOrCreateClassificationLabel(
    requestParameters: DefaultApiGetOrCreateClassificationLabelRequest
  ) {
    const res =
      await this.client.getOrCreateClassificationLabel(requestParameters);
    return res.data;
  }

  async updateClassificationLabel(
    requestParameters: DefaultApiUpdateClassificationLabelRequest
  ) {
    const res = await this.client.updateClassificationLabel(requestParameters);
    return res.data;
  }

  async updateEmailClassificationLabels(
    request: DefaultApiUpdateEmailClassificationLabelsRequest
  ) {
    await this.client.updateEmailClassificationLabels(request);
  }

  async createRoutingRule(request: DefaultApiCreateRoutingRuleRequest) {
    await this.client.createRoutingRule(request);
  }

  async getClassificationLabels() {
    const res = await this.client.getClassificationLabels();
    return res.data;
  }

  async updateRoutingRule(request: DefaultApiUpdateRoutingRuleRequest) {
    await this.client.updateRoutingRule(request);
  }

  async deleteRoutingRule(id: string) {
    await this.client.deleteRoutingRule({ id });
  }

  async getInsightsDocumentTypes(organizationId: number) {
    const res = await this.client.getInsightsDocumentTypes({ organizationId });
    return res.data;
  }

  async splitPdfFile(
    fileId: string,
    pageRanges: SplitFileRequestPageRangesInner[]
  ) {
    await this.client.splitPdfFile({
      fileId,
      splitFileRequest: { page_ranges: pageRanges },
    });
  }

  async deleteCustomizableClassifier(id: string) {
    await this.client.deleteCustomizableClassifier({ id });
  }

  async createOrUpdateCustomFileType(
    requestParameters: DefaultApiCreateOrUpdateCustomFileTypeRequest
  ) {
    const res =
      await this.client.createOrUpdateCustomFileType(requestParameters);
    return res.data;
  }

  async getCustomFileTypesForOrg(
    requestParameters: DefaultApiGetCustomFileTypesForOrgRequest
  ) {
    const res = await this.client.getCustomFileTypesForOrg(requestParameters);
    return res.data;
  }

  async getAggregatedCustomFileTypes(
    requestParameters: DefaultApiGetAggregatedCustomFileTypesRequest
  ) {
    const res =
      await this.client.getAggregatedCustomFileTypes(requestParameters);
    return res.data;
  }

  async getAggregatedFileTypes(
    requestParameters: DefaultApiGetAggregatedFileTypesRequest
  ) {
    const res = await this.client.getAggregatedFileTypes(requestParameters);
    return res.data;
  }

  async getFileUrlByFileId(request: DefaultApiGetFileUrlByFileIdRequest) {
    const res = await this.client.getFileUrlByFileId(request);
    return res.data;
  }

  async getSicCodes(organizationId: number) {
    const res = await this.client.getSicCodes({ organizationId });
    return res.data?.sic_codes ?? [];
  }

  async createOrUpdateWorkersCompExperience(
    params: DefaultApiCreateOrUpdateWorkersCompExperienceRequest
  ) {
    const res = await this.client.createOrUpdateWorkersCompExperience(params);
    return res.data;
  }

  async getFactSubtypeSelectionBenchmark(
    request: DefaultApiGetFactSubtypeSelectionBenchmarkRequest
  ) {
    const res = await this.client.getFactSubtypeSelectionBenchmark(request);
    return res.data;
  }

  async getSubmissionReportId(
    requestParameters: DefaultApiGetSubmissionReportIdRequest
  ) {
    const res = await this.client.getSubmissionReportId(requestParameters);
    return res.data;
  }

  async getBenchmarkData(request: DefaultApiGetBenchmarkDataRequest) {
    const res = await this.client.getBenchmarkData(request);
    return res.data;
  }

  async addBenchmarkData(request: DefaultApiAddBenchmarkDataRequest) {
    const res = await this.client.addBenchmarkData(request);
    return res.data;
  }

  async deleteBenchmarkData(request: DefaultApiDeleteBenchmarkDataRequest) {
    const res = await this.client.deleteBenchmarkData(request);
    return res.data;
  }

  async updateBenchmarkData(request: DefaultApiUpdateBenchmarkDataRequest) {
    const res = await this.client.updateBenchmarkData(request);
    return res.data;
  }

  async getReportsByUser(requestParameters: DefaultApiGetReportsByUserRequest) {
    const res = await this.client.getReportsByUser(requestParameters);
    return new Reports(res.data);
  }

  async finishExternalClearingForSubmission(
    request: DefaultApiFinishExternalClearingForSubmissionRequest
  ) {
    const res = await this.client.finishExternalClearingForSubmission(request);
    return res.data;
  }
  async getTaxonomyMappings(params: DefaultApiGetTaxonomyMappingsRequest) {
    const res = await this.client.getTaxonomyMappings(params);
    return res.data;
  }

  async getEnhancedFileByFileId(
    params: DefaultApiGetEnhancedFileByFileIdRequest
  ) {
    const res = await this.client.getEnhancedFileByFileId(params);
    return res.data;
  }

  async updateCustomizableClassifierV2(
    request: DefaultApiUpdateCustomizableClassifierV2Request
  ) {
    const res = await this.client.updateCustomizableClassifierV2(request);
    return res.data;
  }

  async getCustomizableClassifiersV2(
    request: DefaultApiGetCustomizableClassifiersV2Request
  ) {
    const res = await this.client.getCustomizableClassifiersV2(request);
    return res.data;
  }

  async createCustomizableClassifierV2(
    request: DefaultApiCreateCustomizableClassifierV2Request
  ) {
    const res = await this.client.createCustomizableClassifierV2(request);
    return res.data;
  }

  async getLatestIntegrationLogByReportAndOperation(
    reportId: string,
    operation: string
  ) {
    const res = await this.client.getLatestIntegrationLogByReportAndOperation({
      reportId,
      operation,
    });
    return res.data;
  }

  async deleteCustomizableClassifierV2(
    request: DefaultApiDeleteClassifierVersionRequest
  ) {
    const res = await this.client.deleteClassifierVersion(request);
    return res.data;
  }

  async activateClassifierVersion(
    request: DefaultApiChangeClassifierVersionActiveRequest
  ) {
    const res = await this.client.changeClassifierVersionActive(request);
    return res.data;
  }

  async getPdsDebuggerFilesData(reportId: string) {
    const res = await this.client.getPdsDebuggerFilesData({ reportId });
    return res.data;
  }

  async getSubmissionLevelExtractedDataByReportId(reportId: string) {
    const res = await this.client.getSubmissionLevelExtractedDataByReportId({
      reportId,
    });
    return res.data;
  }
}

const copilotApiClient = new CopilotApiClient(getClientSideHeadersFactory());
export default copilotApiClient;
