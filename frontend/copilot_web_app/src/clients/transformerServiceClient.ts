import { combineUrl } from "@utils/helpers";

import {
  getClientSideHeadersFactory,
  HeadersFactory,
} from "@legacy/api/http/headers";
import { config } from "config";
import {
  EnqueueExportGenerationRequest,
  ExportApi,
  ExportCreateDTO,
  FormulasApi,
  FormulaCreateDTO,
} from "@legacy/api_clients/report-transformer-service-client";

const API_BASE_URL = combineUrl(
  config.kalepaApiBaseUrl,
  "report-transformer-service"
);

class TransformerServiceApiClient {
  private client: ExportApi;
  private formulasClient: FormulasApi;

  constructor(private readonly headersFactory: HeadersFactory) {
    const apiConfig = {
      basePath: API_BASE_URL,
      baseOptions: {
        headers: {
          Authorization: this.headersFactory().Authorization,
        },
      },

      isJsonMime: () => true,
    };
    this.client = new ExportApi(apiConfig);

    this.formulasClient = new FormulasApi(apiConfig);
  }

  async scheduleExportGeneration(request: EnqueueExportGenerationRequest) {
    const res = await this.client.scheduleExportGeneration(request);
    return res.data;
  }

  async getExportGenerationStatus(exportId: string) {
    const res = await this.client.getExportGenerationStatus(exportId);
    return res.data;
  }

  async createFormula(request: FormulaCreateDTO) {
    const res = await this.formulasClient.createFormula(request);
    return res.data;
  }

  async getEvaluatedFormula(
    formulaId: string,
    submissionId: string,
    organizationId: number
  ) {
    const res = await this.formulasClient.getEvaluatedFormula(formulaId, {
      submission_id: submissionId,
      organization_id: organizationId,
    });
    return res.data;
  }

  async getAllFormulas() {
    const res = await this.formulasClient.getAllFormulas();
    return res.data;
  }

  async getAllExports() {
    const res = await this.client.getAllExports();
    return res.data;
  }

  async createEmptyExport(exportTemplate: ExportCreateDTO) {
    const res = await this.client.createEmptyExport(exportTemplate);
    return res.data;
  }
}

export const transformerServiceClient = new TransformerServiceApiClient(
  getClientSideHeadersFactory()
);
