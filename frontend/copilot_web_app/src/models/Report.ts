import Submission from "./Submission";
import SubmissionBusiness from "./SubmissionBusiness";
import { TERMINAL_STAGES } from "../helpers/reportHelpers";
import { FROZEN_STAGES } from "../constants/hubConstants";
import { PermissionType } from "./types/enums";
import RequestedCoverage from "./RequestedCoverage";
import Deductible from "./Deductible";
import Permission from "./Permission";
import { PARENT_TYPES } from "@legacy/constants/facts";
import { PartialProperties } from "@legacy/models/types/partialProperties";
import User from "@legacy/models/User";
import { ReportStage } from "@utils/constants";
import {
  AssignedUnderwriter,
  SubmissionBusinessEntityRoleEnum,
  SubmissionBusinessNamedInsuredEnum,
  SubmissionProcessingStateEnum,
} from "@legacy/api_clients/copilot_api_client";
import { differenceInMinutes } from "date-fns";
import { memoize } from "@legacy/helpers/memoize";
import {
  coveragesHasFleetCoverage,
  coveragesHasPropertyCoverage,
} from "@utils/coverages";
import orderBy from "lodash/orderBy";
import { CoverageType } from "@legacy/models/Coverage";
import File from "./File";
import uniq from "lodash/uniq";
import { GlCode } from "@queries/glCodes";

type ReportEmailClassification = {
  explanation?: string;
  snippet?: string;
  classifier_id?: string;
  is_confirmed?: boolean;
  label: {
    label: string;
  };
};

type Correspondence = {
  emails?: Array<{
    id: string;
    created_at?: string;
    type?: string;
    email_subject?: string;
    email_attachments_count?: number;
    normalized_subject?: string;
    email_from?: string;
    email_sent_at?: string;
    classifications?: Array<ReportEmailClassification>;
  }>;
};

export type BundledReport = {
  id: string;
  name: string;
  current_user_permission_type: PermissionType;
  created_at: string;
  is_archived: boolean;
  submissions: Array<{
    coverages: RequestedCoverage[];
    account_id?: string;
    assigned_underwriters?: PartialProperties<User>[];
    is_renewal?: boolean;
    stage: ReportStage;
    proposed_effective_date?: string;
  }>;
};

export type BusinessSelectFunctionNames =
  | "generalContractor"
  | "generalContractorAndDuplicates"
  | "contractorProject"
  | "gcPrimaryInsuredOtherInsured"
  | "primaryInsuredOrFirst"
  | "primaryInsured"
  | "otherInsured"
  | "all"
  | "generalContractorAndProject";

class Report {
  public id: string;
  public readonly submission: Submission;
  name: string;
  current_user_permission_type: PermissionType;
  owner_id: number;
  permissions: Permission[];
  last_assigned_at: string | null;

  /**
   * @deprecated
   */
  submissions: Submission[];
  organization_permission_level: PermissionType | null;
  created_at: string;
  linesOfBusiness: string[];
  bundled_reports?: BundledReport[];
  email_body?: string;
  email_subject?: string;
  has_unresolved_clearing_issues?: boolean;
  organization_id: number;
  tier?: number;
  redirect_url?: string;
  is_rush?: boolean;
  org_group?: string;
  url?: string;
  routing_tags?: string[];
  correspondence?: Correspondence;
  is_deleted?: boolean;
  is_user_waiting_for_shadow?: boolean;
  full_pds?: boolean;

  private readonly businesesById: Map<string, SubmissionBusiness>;
  private readonly businessSelectCache: Map<string, string[]> = new Map();

  constructor(obj: any) {
    this.id = obj.id;
    this.submission = new Submission(obj.submissions[0]!);

    this.name = obj.name;
    this.current_user_permission_type = obj.current_user_permission_type;
    this.owner_id = obj.owner_id;
    this.permissions = obj.permissions || [];
    this.submissions = [this.submission];
    this.organization_permission_level = obj.organization_permission_level;

    this.created_at = obj.created_at;
    this.linesOfBusiness = [];
    this.bundled_reports = obj.bundled_reports?.map((report: any) => ({
      ...report,
      submissions: report.submissions?.map((submission: any) => ({
        ...submission,
        coverages: submission.coverages?.map(
          (coverage: any) => new RequestedCoverage(coverage)
        ),
      })),
    }));

    this.email_body = obj.email_body;
    this.email_subject = obj.email_subject;
    this.has_unresolved_clearing_issues = obj.has_unresolved_clearing_issues;
    this.organization_id = obj.organization_id;
    this.tier = obj.tier;
    this.redirect_url = obj.redirect_url;
    this.is_rush = obj.is_rush;

    this.businesesById = new Map();
    this.submission.businesses.forEach((business: SubmissionBusiness) => {
      business
        .getAllIds()
        .forEach((id) => this.businesesById.set(id, business));
    });
    this.last_assigned_at = obj.last_assigned_at;
    this.org_group = obj.org_group;
    this.url = obj.url;
    this.routing_tags = obj.routing_tags;
    this.correspondence = obj.correspondence;
    this.is_deleted = obj.is_deleted;
    this.is_user_waiting_for_shadow = obj.is_user_waiting_for_shadow;
    this.full_pds = obj.full_pds;
  }

  getSubmission = (): Submission => {
    return this.submissions[0];
  };

  @memoize()
  classCode(): string | null {
    const workersCompRatingInfo =
      this.getSubmission().workers_comp_rating_info ?? [];
    const maxPayroll = Math.max(
      ...workersCompRatingInfo.map((rating) => rating?.payroll ?? 0)
    );
    return (
      workersCompRatingInfo?.find((rating) => rating.payroll === maxPayroll)
        ?.class_code ?? null
    );
  }

  @memoize()
  unresolvedClearingIssues() {
    return this.getSubmission().clearing_issues.filter((x) => !x.is_resolved);
  }

  @memoize()
  unresolvedLightClearingIssues() {
    return this.unresolvedClearingIssues().filter((x) => x.is_light);
  }

  isCleared(): boolean {
    return this.submission.clearing_status === "CLEARED";
  }

  canChangeStage(): boolean {
    const clearingStatus = this.submission.clearing_status;

    return (
      clearingStatus !== "BLOCKED" && clearingStatus !== "IN_CLEARING_CONFLICT"
    );
  }

  latestEmail() {
    return orderBy(
      this.correspondence?.emails,
      (x) => x.email_sent_at,
      "desc"
    )?.[0];
  }

  isLightCleared(): boolean {
    return this.unresolvedLightClearingIssues().length === 0;
  }

  isBlocked(): boolean {
    return this.submission.clearing_status === "BLOCKED";
  }

  @memoize()
  getPrimaryNAICSCode(digits = 6): string {
    const specialCodes = ["31-33", "44-45", "48-49"];
    const prefix = "NAICS_";
    const code =
      this.getSubmission()
        ?.primary_naics_code?.replace(prefix, "")
        ?.substring(0, digits) || "";
    const specialCode = specialCodes.find((c) => c.includes(code));
    if (specialCode && code.length === 2) {
      return specialCode;
    }
    return code;
  }

  getPrimaryGlCode(): GlCode | null {
    if (!this.submission.iso_gl_code) {
      return null;
    }
    const glCode = this.submission.iso_gl_code.split("_");
    if (glCode.length < 3) {
      return null;
    }
    return {
      id: this.submission.iso_gl_code,
      name: glCode[2],
    };
  }

  hasPropertyOrEquivalent() {
    return this.hasCoverage("property") || this.hasCoverage("events");
  }

  hasCoverage(name: string, coverageType?: CoverageType) {
    return this.getCoverages().some(
      (x) =>
        x.coverage?.name === name &&
        (!coverageType || x.coverage?.coverage_types?.includes(coverageType))
    );
  }

  hasGeneralLiability() {
    return this.hasCoverage("liability");
  }

  isK2Aegis() {
    return (
      this.organization_id == 61 &&
      this.submission.organization_group == "Aegis Specialty Dealers"
    );
  }

  hasAnyOfTheMainMapCoverages() {
    const mainMapCoverages = [
      "commercialInlandMarine",
      "workersComp",
      "businessAuto",
      "managementLiability",
      "professionalLiability",
    ];
    return this.hasGeneralLiability() || this.hasAnyCoverage(mainMapCoverages);
  }

  hasAnyCoverage(names: string[]) {
    return this.getCoverages().some((x) =>
      names.includes(x.coverage?.name ?? "")
    );
  }

  getCoverages(): RequestedCoverage[] {
    return this.submission.coverages;
  }

  getDeductibles(): Deductible[] {
    return this.submission.deductibles;
  }

  getBusiness(): SubmissionBusiness | null {
    return this.submission.businesses[0] ?? null;
  }

  getBusinesses(): SubmissionBusiness[] {
    return this.submission.businesses.filter(
      (b) => b.entity_role !== "ADVISOR"
    );
  }

  getBusinessIdsWithActiveOwners(): string[] {
    return uniq(this.withActiveOwners(this.getBusinessIds()));
  }

  getOtherBusinesses(): SubmissionBusiness[] {
    return this.submission.businesses.filter(
      (b) => b.entity_role !== "PROJECT" && !b.hide_property_facts
    );
  }

  shouldDisplayPremisesViewer(): boolean {
    return (
      this.getOtherBusinesses().length > 0 &&
      !this.submission.isManagementLiabilityOnly()
    );
  }

  shouldDisplayExposureBreakdown(): boolean {
    return this.getOtherBusinesses().length > 1;
  }

  @memoize()
  getBusinessIds(): string[] {
    return this.getBusinesses()
      .filter((b) => !!b.business_id)
      .map((b) => b.business_id) as string[];
  }

  @memoize()
  getPremisesIds(): string[] {
    const businesses: SubmissionBusiness[] = this.getBusinesses();
    const premisesIds = businesses.flatMap((business) => {
      return business.getPremisesId() ?? [];
    });
    return Array.from(new Set(premisesIds));
  }

  getPremisesIdsForBusinessesIds(businessesIds: string[]): string[] {
    const businesses = this.getBusinessesByIds(businessesIds);
    const premisesIds = businesses.flatMap((business) => {
      return business.getPremisesId() ?? [];
    });
    return Array.from(new Set(premisesIds));
  }

  getPremiseIdForBusinessId(businessId: string): string | null {
    const business = this.getBusinessById(businessId);
    return business?.getPremisesId() ?? null;
  }

  @memoize()
  getPremisesIdsForPropertyData(): string[] {
    const allPremisesIds = this.getPremisesIds();
    return allPremisesIds.filter((premisesId) => {
      const business = this.getBusinessByPremiseId(premisesId);
      return business && !business.hide_property_facts;
    });
  }
  @memoize()
  getBusinessIdsForPropertyData(): string[] {
    return this.getBusinessesForPropertyData().map((b) => b.business_id!);
  }

  @memoize()
  getSubmissionBusinessIdsForPropertyData(): string[] {
    return this.getBusinesses()
      .filter((b) => !!b.business_id && !b.hide_property_facts)
      .map((b) => b.id) as string[];
  }

  @memoize()
  getBusinessesForPropertyData(): SubmissionBusiness[] {
    return this.getBusinesses().filter(
      (business) => !business.hide_property_facts
    );
  }

  @memoize()
  areAllBusinessesConfirmed(): boolean {
    return !this.getBusinesses().find((sb) => !sb.business_id);
  }

  getBusinessesByIds(ids: string[]): SubmissionBusiness[] {
    return ids
      .filter((id) => this.businesesById.has(id))
      .map((id) => this.businesesById.get(id)!);
  }

  getBusinessById(id: string): SubmissionBusiness | null {
    return this.businesesById.get(id) ?? null;
  }

  getBusinessByPremiseId(id: string): SubmissionBusiness | null | undefined {
    return this.getBusinesses().find(
      (business) => business.getPremisesId() === id
    );
  }

  getSubmissionBusinessByAnyId(id: string): SubmissionBusiness | undefined {
    return this.getBusinesses().find((b) => b.getAllIds().includes(id));
  }

  @memoize()
  getBusinessName(): string {
    const business = this.getBusiness();
    const businessName =
      this.name || business?.name || this.getRealEstateName() || "Unknown name";
    return businessName === "Real Estate/Property"
      ? this.getRealEstateName()
      : businessName;
  }

  @memoize()
  getRealEstateName(id: string | undefined = undefined): string {
    if (!id) {
      return this.name;
    }
    const business = this.businesesById.get(id);
    if (!business) {
      return this.name;
    }
    const businessAddress =
      business.address ?? business.premises?.formatted_address;
    return businessAddress ? `Real Estate: ${businessAddress}` : this.name;
  }

  @memoize()
  isRenewal(): boolean {
    return this.getSubmission().is_renewal;
  }

  getUserFacingStage(isClientStageEnabled: boolean | undefined | null) {
    return isClientStageEnabled
      ? (this.getSubmission().client_stage?.client_stage as string)
      : this.getStage();
  }

  getStage() {
    return this.submission.stage;
  }

  getFrozenDate(): string {
    return (
      this.submission.frozen_as_of ?? this.submission.stage_details?.updateDate
    );
  }

  isFrozen(): boolean {
    const stage = this.getStage();
    return FROZEN_STAGES.includes(stage);
  }

  isTerminal(): boolean {
    const stage = this.getStage();
    return TERMINAL_STAGES.includes(stage);
  }

  hasPermission(permissionTypes: PermissionType[] = []): boolean {
    return permissionTypes.includes(this.current_user_permission_type);
  }

  @memoize()
  isEditable(ignoreClearing?: boolean): boolean {
    const canEditRegardlessOfClearing =
      this.hasEditPermissions() && !this.isFrozen();
    if (ignoreClearing) {
      return canEditRegardlessOfClearing;
    }
    return (
      canEditRegardlessOfClearing &&
      this.unresolvedClearingIssues().length === 0
    );
  }

  @memoize()
  isCommentable(): boolean {
    return (
      this.hasPermission([
        PermissionType.OWNER,
        PermissionType.EDITOR,
        PermissionType.ADMIN,
        PermissionType.COMMENTER,
      ]) && !this.isFrozen()
    );
  }

  @memoize()
  hasEditPermissions(): boolean {
    return this.hasPermission([
      PermissionType.OWNER,
      PermissionType.EDITOR,
      PermissionType.ADMIN,
    ]);
  }

  @memoize()
  isOwner(): boolean {
    return this.hasPermission([PermissionType.OWNER, PermissionType.ADMIN]);
  }

  @memoize()
  getProposedEffectiveDate(): Date | null {
    const effectiveDate = this.submission.proposed_effective_date;
    if (effectiveDate) {
      return new Date(effectiveDate);
    }
    return null;
  }

  getBrokerage() {
    return this.submission.brokerage;
  }

  getBroker() {
    return this.submission.broker;
  }

  getBrokerageContact() {
    return this.submission.brokerage_contact;
  }

  isBusinessAuto(): boolean {
    const coverages = this.getCoverages();
    return (
      coverages.length > 0 &&
      coverages.every(
        (coverage) =>
          coverage.coverage.display_name?.toUpperCase() === "BUSINESS AUTO"
      )
    );
  }

  isBusinessOwnersPolicy(): boolean {
    const coverages = this.getCoverages();
    return (
      coverages.length > 0 &&
      coverages.every((coverage) => coverage.coverage.name === "businessOwners")
    );
  }

  requiresVerification(): boolean {
    return !!this.submission.is_verification_required;
  }

  isVerified(): boolean {
    return !!this.submission.is_verified;
  }

  isVerifiedShell(): boolean {
    return !!this.submission.is_verified_shell;
  }

  isProcessing(): boolean {
    return this.submission.isProcessing() && !this.isVerified();
  }

  @memoize()
  getPrimaryInsureds(): SubmissionBusiness[] {
    return this.submission.getBusinessesByNamedInsureds([
      "FIRST_NAMED_INSURED",
    ]);
  }

  @memoize()
  getFni(): SubmissionBusiness | undefined {
    const fnis = orderBy(this.getPrimaryInsureds(), (x) => x.isRealEstate());
    return fnis[0];
  }

  @memoize()
  getOnis(): SubmissionBusiness[] {
    return this.submission.getBusinessesByNamedInsureds([
      "OTHER_NAMED_INSURED",
    ]);
  }

  @memoize()
  isFniDifferentFromGc(): boolean {
    const gc = this.getGcEntity();
    const fni = this.getFni();
    return !!fni && gc?.business_id !== fni.business_id;
  }

  @memoize()
  getPrimaryInsuredOrFirst(): SubmissionBusiness[] {
    const primaryInsureds = this.getPrimaryInsureds();
    if (primaryInsureds.length > 0) {
      return [primaryInsureds[0]];
    } else {
      const firstBusiness = this.getBusiness();
      return firstBusiness ? [firstBusiness] : [];
    }
  }

  @memoize()
  hasFleetCoverage(): boolean {
    return coveragesHasFleetCoverage(this.getCoverages());
  }

  @memoize()
  hasPropertyCoverage(): boolean {
    return coveragesHasPropertyCoverage(this.getCoverages());
  }

  public getParentIdsForParentType = (
    parentType: PARENT_TYPES,
    businessSelect?: BusinessSelectFunctionNames
  ): string[] => {
    const cacheKey = parentType + (businessSelect ?? "u");
    if (!this.businessSelectCache.has(cacheKey)) {
      this.businessSelectCache.set(
        cacheKey,
        this.calculateParentIdsForParentType(parentType, businessSelect)
      );
    }
    return this.businessSelectCache.get(cacheKey)!;
  };

  private calculateParentIdsForParentType = (
    parentType: PARENT_TYPES,
    businessSelect?: BusinessSelectFunctionNames
  ): string[] => {
    if (!businessSelect || businessSelect === "all") {
      if (parentType === PARENT_TYPES.BUSINESS) return this.getBusinessIds();
      else if (parentType === PARENT_TYPES.PREMISES)
        return this.getPremisesIds();
    }

    const entityMap: Record<BusinessSelectFunctionNames, SubmissionBusiness[]> =
      {
        all: this.getSubmission().businesses,
        generalContractor: [
          this.getSubmission().findBusinessByEntityRole(
            SubmissionBusinessEntityRoleEnum.GeneralContractor
          )!,
        ].filter(Boolean),
        contractorProject: this.getSubmission()
          .getBusinessesByEntityRoles([
            SubmissionBusinessEntityRoleEnum.Project,
          ])!
          .filter(Boolean),
        generalContractorAndDuplicates: this.getBusinesses()
          .map((business) => {
            if (
              SubmissionBusinessEntityRoleEnum.GeneralContractor ===
                business.entity_role ||
              SubmissionBusinessEntityRoleEnum.Duplicate ===
                business.entity_role
            ) {
              return business;
            }

            return null;
          })
          .filter(Boolean) as SubmissionBusiness[],
        gcPrimaryInsuredOtherInsured: this.getBusinesses()
          .map((business) => {
            if (
              SubmissionBusinessEntityRoleEnum.GeneralContractor ===
                business.entity_role ||
              SubmissionBusinessNamedInsuredEnum.FirstNamedInsured ===
                business.named_insured ||
              SubmissionBusinessNamedInsuredEnum.OtherNamedInsured ===
                business.named_insured
            ) {
              return business;
            }

            return null;
          })
          .filter(Boolean) as SubmissionBusiness[],
        primaryInsuredOrFirst: this.getPrimaryInsuredOrFirst(),
        primaryInsured: this.getPrimaryInsureds(),
        otherInsured: this.getSubmission().getBusinessesByNamedInsureds([
          "OTHER_NAMED_INSURED",
        ]),
        generalContractorAndProject: this.getBusinesses().filter(
          (business) =>
            business.entity_role ===
              SubmissionBusinessEntityRoleEnum.GeneralContractor ||
            business.entity_role === SubmissionBusinessEntityRoleEnum.Project
        ),
      };

    const entities = entityMap[businessSelect!] ?? [];

    switch (parentType) {
      case PARENT_TYPES.BUSINESS:
        return entities.map((entity) => entity.business_id!).filter(Boolean);
      case PARENT_TYPES.PREMISES:
        return entities
          .map((entity) => entity.getPremisesId()!)
          .filter(Boolean);
      case PARENT_TYPES.SUBMISSION:
        return this.submissions.map((submission) => submission.id);
      default:
        return [];
    }
  };

  getBusinessesForBusinessSelect = (
    businessSelect?: BusinessSelectFunctionNames
  ) => {
    const parentIds = this.getParentIdsForParentType(
      PARENT_TYPES.BUSINESS,
      businessSelect
    );
    return this.getBusinessesByIds(parentIds);
  };

  isShell(): boolean {
    return !this.getBusinesses()?.length;
  }

  minutesSinceBusinessesCreated(): number | undefined {
    const businesses = this.getBusinesses();
    if (businesses.length === 0) {
      return undefined;
    }
    const businessesCreatedAt = businesses
      .map((b) => (b.created_at ? new Date(b.created_at) : new Date()))
      .sort();
    return differenceInMinutes(new Date(), businessesCreatedAt[0]);
  }

  isBossReport(): boolean {
    return this.submission.origin === "API" && this.organization_id === 6;
  }

  isPdsReport(): boolean {
    return !!this.submission.is_auto_processed;
  }

  getVerifiedShellMessage(): string | null {
    if (!!this.submission.is_verified_shell) {
      return this.isPdsReport() &&
        this.submission.processing_state !==
          SubmissionProcessingStateEnum.Cancelled
        ? "This submission is currently being processed and will be available soon"
        : "This submission is not available because it was created for reporting purposes only";
    } else {
      return null;
    }
  }

  isContractorProject(): boolean {
    return (
      this.submission.getBusinessesByEntityRoles(["GENERAL_CONTRACTOR"])
        .length > 0 &&
      this.submission.getBusinessesByEntityRoles(["PROJECT"]).length === 1
    );
  }

  isProjectSubmission(): boolean {
    return (
      this.submission.getBusinessesByEntityRoles(["GENERAL_CONTRACTOR"])
        .length > 0 &&
      this.submission.getBusinessesByEntityRoles(["PROJECT"]).length > 0
    );
  }

  @memoize()
  getProjectEntity(): SubmissionBusiness | undefined {
    return this.getBusinesses().find((x) => x.entity_role === "PROJECT");
  }

  @memoize()
  getGcEntity(): SubmissionBusiness | undefined {
    return this.getBusinesses().find(
      (x) => x.entity_role === "GENERAL_CONTRACTOR"
    );
  }

  getGeneratedDescriptionOfOperations(): string | null | undefined {
    if (this.isContractorProject()) {
      return this.submission.email_project_description;
    }
    return this.submission.generated_description_of_operations;
  }

  hasGeneratedDescription(): boolean {
    return !!this.getGeneratedDescriptionOfOperations();
  }

  getDescriptionOfOperations(): string {
    return (
      this.getGeneratedDescriptionOfOperations() ??
      this.getSubmission().description_of_operations ??
      ""
    );
  }

  isAssignedUnderwriterEditable(currentUser: User): boolean {
    return this.getSubmission().isAssignedUnderwriterEditable(currentUser);
  }

  get2DigitNaics(): string | null {
    const primaryNaics = this.getSubmission().primary_naics_code;

    return primaryNaics?.split("NAICS_")[1].substring(0, 2) ?? null;
  }

  getFileById(id: string): File | undefined {
    return this.getSubmission().getFileById(id);
  }

  withActiveOwners(parentIds?: (string | undefined)[]) {
    if (!parentIds) return [];

    const ids = parentIds.filter(Boolean) as string[];

    const activeOwnerIds =
      this.getBusinessesByIds(ids).flatMap(
        (business) => business.entity_data?.getActiveOwnerIds() ?? []
      ) ?? [];
    return uniq([...ids, ...activeOwnerIds]);
  }

  isParagonE3(assignedUnderwriters?: AssignedUnderwriter[]): boolean {
    const submissionUnderwriters =
      this?.submission.assigned_underwriters ?? assignedUnderwriters;

    const isParagonE3 = submissionUnderwriters?.some(
      (uw) => uw.groups?.some((g) => g.name?.toUpperCase() === "PSP E3")
    );

    return !!isParagonE3;
  }

  canSeePrometrix(): boolean {
    return this.isParagonE3() || this.organization_id === 3;
  }

  canSeeRiskmeter(): boolean {
    return this.isParagonE3();
  }

  getBusinessIdsForPremisesId = (premisesId: string) => {
    return this.getBusinesses()
      .filter((b) => b.getPremisesId() === premisesId)
      .map((b) => b.business_id)
      .filter((id): id is string => Boolean(id));
  };
}

export default Report;
