import { Coordinates } from "./types/coordinates";
import { PartialProperties } from "./types/partialProperties";
import {
  ERSInnerPremises,
  RequestedProperties,
  SubmissionBusinessEntityRoleEnum,
  SubmissionBusinessEntityTypeEnum,
  SubmissionBusinessNamedInsuredEnum,
  SubmissionBusinessProjectInsuranceTypeEnum,
} from "../api_clients/copilot_api_client";
import { ErsEntity } from "./ErsEntity";
import { memoize } from "@legacy/helpers/memoize";
import { matchesAlphaNumeric } from "@utils/text";

class SubmissionBusiness {
  id: string | null;
  requested_name: string | null;
  requested_address: string | null;
  requested_phone_number: string | null;
  requested_industries: string[] | null;
  requested_identifiers: RequestedProperties["requested_identifiers"];
  is_user_confirmed: boolean | null;
  sic_codes: string[] | null;
  aliases: string[] | null;
  selected_alias: string | null;
  snapshot_id: string | null;
  created_at: string | null;
  entity_data: ErsEntity | null;
  business_id: string | null;
  kalepa_id: string | null;
  original_business_id: string | null;
  original_name: string | null;
  name: string | null;
  premises: ERSInnerPremises | null;
  address: string | null;
  industries: string[];
  /**
   * @deprecated don't use this outside creation of report (it's fallback for old SOV template)
   */
  entity_type?: SubmissionBusinessEntityTypeEnum;
  entity_role?: SubmissionBusinessEntityRoleEnum;
  named_insured?: SubmissionBusinessNamedInsuredEnum;
  description_of_operations: string | null;
  project_insurance_type?: SubmissionBusinessProjectInsuranceTypeEnum;
  hide_property_facts?: boolean;
  hide?: boolean;
  additional_info?: any;
  file_id_sources?: string[];

  constructor(obj: PartialProperties<SubmissionBusiness>) {
    this.id = obj.id || null;

    this.requested_name = obj.requested_name || null;
    this.requested_address = obj.requested_address || null;
    this.requested_phone_number = obj.requested_phone_number || null;
    this.requested_industries = obj.requested_industries || null;
    this.is_user_confirmed = obj.is_user_confirmed || null;
    this.sic_codes = obj.sic_codes || null;
    this.aliases = obj.aliases || [];
    this.selected_alias = obj.selected_alias || null;
    this.snapshot_id = obj.snapshot_id || null;
    this.created_at = obj.created_at || null;
    this.entity_data = null;
    if (obj.entity_data) {
      // This is so we can use SubmissionBusiness class as obj,
      // i.e. new SubmissionBusiness(new SubmissionBusiness(obj)) works.
      this.entity_data = obj.entity_data?.entity
        ? new ErsEntity(obj.entity_data.entity)
        : new ErsEntity(obj.entity_data);
    }

    // TODO: Use one or the other.
    this.business_id = obj.business_id || null;
    this.kalepa_id = this.business_id;

    this.original_business_id =
      obj.original_business_id || obj.business_id || null;

    this.original_name = this.entity_data?.getName() || null;
    this.name = this.getUserFacingName() || this.requested_name;
    this.premises = this.entity_data?.getPremises() || null;
    this.address = this.premises?.formatted_address || this.requested_address;
    this.industries =
      this.entity_data?.getIndustries() || this.requested_industries || [];
    this.requested_identifiers = obj.requested_identifiers;

    if (this.entity_data?.id) {
      this.business_id = this.entity_data?.id;
      this.kalepa_id = this.entity_data?.id;
    }
    this.entity_type = obj.entity_type;
    this.entity_role = obj.entity_role;
    this.named_insured = obj.named_insured;
    this.description_of_operations = obj.description_of_operations || null;
    this.project_insurance_type = obj.project_insurance_type;
    this.hide_property_facts = obj.hide_property_facts;
    this.hide = obj.hide;
    this.additional_info = obj.additional_info;
    this.file_id_sources = obj.file_id_sources;
  }

  static createLightweight(
    name: string,
    address: string | null,
    id: string
  ): SubmissionBusiness {
    return new SubmissionBusiness({
      original_name: name,
      address,
      business_id: id,
      kalepa_id: id,
    });
  }

  hasIndustry(industry: string): boolean {
    return this.industries.includes(industry);
  }

  isRealEstate(): boolean {
    return (
      this.hasIndustry("Real Estate") || !!this.entity_data?.isRealEstate()
    );
  }

  @memoize()
  getAllIds(): string[] {
    return (this.entity_data?.getAllPremisesIds() ?? [])
      .concat(
        this.id ?? "",
        this.business_id ?? "",
        this.original_business_id ?? ""
      )
      .filter(Boolean);
  }

  getState(): string | null {
    return this.premises?.state || null;
  }

  getCountry(): string | null {
    return this.premises?.country || null;
  }

  getCoordinates(): Coordinates | null {
    if (this.premises?.latitude && this.premises?.longitude) {
      return {
        latitude: this.premises.latitude,
        longitude: this.premises.longitude,
      };
    }
    return null;
  }

  getPremisesId(): string | null {
    return this.premises?.id || null;
  }

  getUserFacingName(): string | null {
    if (this.selected_alias) {
      return this.selected_alias;
    }
    let originalName: string | null | undefined = this.original_name;
    if (originalName === "Real Estate/Property") originalName = "Location";
    originalName = originalName
      ?.split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
    if (
      originalName &&
      this.requested_name &&
      !matchesAlphaNumeric(originalName, this.requested_name)
    ) {
      return `${originalName} (${this.requested_name})`;
    }
    return originalName || this.requested_name;
  }

  formatAddress(address: string | null | undefined): string | null {
    if (!address) return null;
    const stateZipPattern = /^\b[A-Z]{1,4} \d{1,10}\b$/;
    const shortCountryPattern = /^\b[A-Z]{1,3}\b$/;
    const shortAbbreviationPattern = /^\b[A-Z]{1,2}\b$/;
    const fragments = address.split(", ");

    let found = false;
    const processedFragments = fragments.map((fragment) => {
      if (!found && stateZipPattern.test(fragment)) {
        found = true;
        return fragment;
      } else if (
        (found && shortCountryPattern.test(fragment)) ||
        shortAbbreviationPattern.test(fragment)
      ) {
        return fragment;
      } else {
        return fragment
          .split(" ")
          .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join(" ");
      }
    });

    return this.formatMailingAddress(processedFragments.join(", "));
  }

  getAddress(): string | null {
    return this.formatAddress(
      this.premises?.formatted_address || this.requested_address
    );
  }

  formatMailingAddress(address: string | null | undefined): string | null {
    if (!address) return null;
    const poBoxRegex = /^po box/i;
    if (poBoxRegex.test(address)) {
      return address?.replace(poBoxRegex, "PO Box");
    }
    return address;
  }

  isAddressMailingAddress(address: string | null | undefined) {
    if (!address) return false;
    return this.formatMailingAddress(address)?.startsWith("PO Box");
  }

  getCounty(): string | null {
    const result = this.premises?.county;
    return result
      ? result
          .split(" ")
          .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join(" ")
      : null;
  }

  getNameAndAddress(): string {
    const poBox = this.getPoBox();
    const address = this.getAddress();
    if (!address && !poBox) {
      return this.name || "";
    }
    return `${this.name} (${address ?? ""} ${poBox ?? ""})`;
  }

  getAliases(): string[] {
    return this.entity_data?.getOtherNames() || [];
  }

  getPoBox(): string | null | undefined {
    const poBox = this.entity_data?.getPoBox();
    return poBox
      ?.replace("POST OFFICE BOX", "PO Box")
      ?.replace(" - ZIP CODE", ",");
  }

  getDisplayName(): string {
    let name = "";
    if (this.entity_data?.isOnlyNameRealEstate() ?? true) {
      name += (this.requested_name ?? "") + " ";
    }
    return name + this.entity_data?.getName();
  }

  getDisplayAddress(): string | null {
    return this.formatAddress(
      this.entity_data?.getPremises()?.formatted_address
    );
  }
}

export default SubmissionBusiness;
