import React, { ReactElement } from "react";
import { useCurrentReportQuery } from "@queries/report";
import File from "@legacy/models/File";
import { trackFileNameClicked } from "@utils/amplitude";
import { useEmailBody } from "@features/files/components/RootEmailPreview";
import { saveHtmlAsFile } from "@utils/text";
import { useFeatureFlags } from "@features/feature-flags/useFeatureFlags";
import { Tooltip } from "@mui/material";
import { useAccessPermissions } from "@features/product-driven-support/utils";
import { downloadFile } from "@utils/downloadUtil";
import copilotApiClient from "@legacy/clients/copilotApiClient";

export const FileNameComponent = ({
  displayName,
  onClick,
  openDisabled = false,
  tooltip = "",
}: {
  displayName: string;
  onClick: () => void;
  warningLabel?: string | null;
  openDisabled?: boolean;
  tooltip?: string;
}): ReactElement => {
  return (
    <Tooltip title={tooltip}>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          cursor: openDisabled ? undefined : "pointer",
          wordBreak: "break-all",
        }}
        onClick={openDisabled ? undefined : onClick}
        role="button"
        tabIndex={0}
      >
        {displayName}
      </div>
    </Tooltip>
  );
};

type FileNameProps = {
  file: File;
  submissionId: string;
  onOpen?: () => void;
  openDisabled?: boolean;
};

const FileName = ({
  file,
  submissionId,
  onOpen,
  openDisabled = false,
}: FileNameProps): ReactElement => {
  const { id, name, file_type } = file;
  const { internalUseFeatures } = useFeatureFlags();

  const report = useCurrentReportQuery();
  const email = useEmailBody(report!.id);
  const { emailPdfAsHtml } = useFeatureFlags();
  const { isAtLeastTier2 } = useAccessPermissions();

  const openFile = async () => {
    if (report) {
      trackFileNameClicked(report, {
        fileName: name,
        fileType: file_type ?? "unknown",
        lossRunStatus: file.getLossRunStatus(),
      });
    }

    onOpen?.();

    try {
      if (name === "email_body.pdf" && emailPdfAsHtml) {
        if (email?.email_body) {
          saveHtmlAsFile(email.email_body!, "email_body.html");
        }
      } else {
        const result = await copilotApiClient.getFile({ id, submissionId });
        const url = result?.presigned_url;
        if (url) {
          downloadFile(url, file.display_name);
        }
      }
    } catch (err) {
      // maybe one day we will improve that error handling...
    }
  };

  return (
    <FileNameComponent
      displayName={name}
      onClick={openFile}
      openDisabled={openDisabled}
      tooltip={
        internalUseFeatures && !isAtLeastTier2 ? file?.getInternalTooltip() : ""
      }
    />
  );
};

export default FileName;
