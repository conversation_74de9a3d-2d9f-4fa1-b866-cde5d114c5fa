import React, { useEffect, useState, type JSX } from "react";
import { useUpdateBrokerageMutation } from "@queries/agency";
import styled from "@emotion/styled";
import Report from "../../models/Report";
import { brandColor } from "../../css/style-utils";
import CPIcon from "../generic/CPIcon";
import { VerificationCheckResult } from "../../api_clients/copilot_api_client";
import { useCurrentUser } from "@utils/auth";
import { useSubmissionVerificationMutation } from "@queries/submission";
import { Dialog } from "@ui-patterns/dialog";
import { Box, Typography } from "@mui/material";
import { sentryCaptureException } from "@legacy/services/sentryService";
import { trackSubmissionVerified } from "@utils/amplitude";
import { emptyFn } from "@utils/helpers";
import { useExperimentsStore } from "@features/experiments/state";
import { useExperimentDataMutation } from "@queries/experiments";
import { useQueryClient } from "@tanstack/react-query";
import { BrokersModal } from "@features/agents-modal/BrokersModal";
import { SupportUserPicker } from "@legacy/components/report/SupportUserPicker";
import { useAccessPermissions } from "@features/product-driven-support/utils";

const Container = styled.div(
  ({
    justifyContent,
  }: {
    justifyContent: "center" | "end" | "flex-end" | "flex-start" | "start";
  }) => ({
    display: "flex",
    justifyContent,
    alignItems: "center",
    color: brandColor.warningRed,
    cursor: "pointer",
    gap: 1,
  })
);

const VerifiedContainer = styled.div({
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  color: brandColor.darkGreen,
  fontWeight: "bold",
});

const VerificationButton = ({
  report,
  refetch,
  verifyButton,
  justifyContent = "center",
  disabled = false,
  showConfirmation = false,
  onCancel = emptyFn,
}: {
  report: Report;
  refetch: () => void;
  verifyButton?: JSX.Element;
  justifyContent?: "center" | "end" | "flex-end" | "flex-start" | "start";
  disabled?: boolean;
  showConfirmation?: boolean;
  onCancel?: () => void;
}): JSX.Element | null => {
  const [showConfirmModal, setShowConfirmModal] = useState(showConfirmation);
  const [verifyFor, setVerifyFor] = useState<number | null>(null);
  const [showConfirmAgencyModal, setShowConfirmAgencyModal] = useState(false);
  const [showAgencyModal, setShowAgencyModal] = useState(false);
  const [errors, setErrors] = useState<VerificationCheckResult[]>([]);
  const runningExperiments = useExperimentsStore((state) => state.experiments);
  const experimentMutation = useExperimentDataMutation();
  const queryClient = useQueryClient();

  const finishRunningExperiments = async () => {
    const promises = [];

    for (const run of runningExperiments) {
      for (const sample of run.samples ?? []) {
        if (sample.is_match === undefined || sample.is_match === null) {
          promises.push(
            experimentMutation.mutateAsync({
              expId: run.experiment?.name ?? "",
              submissionId: report.getSubmission().id,
              sampleId: sample.id ?? "",
              experimentSampleUpdateRequest: {
                user_input: {
                  value: "",
                },
                is_match: false,
              },
            })
          );
        }
      }
      await Promise.all(promises);
      await queryClient.invalidateQueries(["experiments"]);
    }
  };

  const { mutate: updateBrokerage } = useUpdateBrokerageMutation();
  const verifySubmissionMutation = useSubmissionVerificationMutation(
    report.getSubmission().id,
    report.id
  );

  const user = useCurrentUser();
  const { isCSManager } = useAccessPermissions();

  useEffect(() => {
    setShowConfirmModal(showConfirmation);
  }, [showConfirmation]);

  const forceVerifySubmission = async () => {
    await finishRunningExperiments();
    verifySubmissionMutation.mutate(
      { force: true, verifyForUserId: verifyFor ?? undefined },
      {
        onSuccess: () => {
          refetch();
          closeErrors();
          trackSubmissionVerified(report, {});
        },
      }
    );
  };

  const verifySubmission = async () => {
    await finishRunningExperiments();
    verifySubmissionMutation
      .mutateAsync({ force: false, verifyForUserId: verifyFor ?? undefined })
      .then(({ data }) => {
        const failedChecks: VerificationCheckResult[] = [];
        const results = data.check_results ?? [];
        results.forEach((r) => {
          if (r.status === "ERROR") {
            failedChecks.push(r);
          }
        });
        if (failedChecks.length === 0) {
          refetch();
          closeErrors();
        } else {
          setErrors(failedChecks);
        }
      })
      .catch((e) => {
        const alreadyVerified = e.message?.endsWith("409");
        if (alreadyVerified) {
          refetch();
        } else {
          sentryCaptureException(e);
        }
      });
    setShowAgencyModal(false);
  };

  const closeErrors = () => {
    setErrors([]);
  };

  const onClick = () => {
    setShowConfirmModal(true);
  };

  const checkAgency = () => {
    setShowConfirmModal(false);
    const agency = report.getBrokerage();
    if (!agency || agency.is_confirmed) {
      verifySubmission();
    } else {
      setShowConfirmAgencyModal(true);
    }
  };

  const updateAgency = () => {
    const agency = report.getBrokerage();

    updateBrokerage({
      brokerageId: agency?.id as string,
      brokerage: {
        is_confirmed: true,
      },
    });
    verifySubmission();
    closeModals();
  };

  const closeModals = () => {
    setShowConfirmModal(false);
    setShowConfirmAgencyModal(false);
    onCancel();
  };

  if (!report.requiresVerification()) {
    return null;
  }

  if (report.getSubmission().is_verified) {
    return (
      <VerifiedContainer>
        <CPIcon name="checkGreen" /> Fully Verified
      </VerifiedContainer>
    );
  }

  if (report.getSubmission().is_manual_verified) {
    return (
      <VerifiedContainer>
        <CPIcon name="checkGreen" /> Verified by Specialist
      </VerifiedContainer>
    );
  }

  if (!!report.getSubmission().stuck_reason) {
    return (
      <Container justifyContent={justifyContent}>
        <CPIcon name="warning" />
        Cannot verify - submission is stuck
      </Container>
    );
  }

  const hasHardErrors = errors.filter((e) => e.is_hard_check).length > 0;
  return (
    <>
      <Container justifyContent={justifyContent}>
        {isCSManager && (
          <SupportUserPicker
            setSelectedUserId={setVerifyFor}
            selectedUserId={verifyFor}
          />
        )}
        <Box onClick={disabled ? emptyFn : onClick}>
          {verifyButton ? (
            verifyButton
          ) : (
            <>
              <CPIcon name="warningRed" /> Verification Required
            </>
          )}
        </Box>
      </Container>
      {showConfirmModal && (
        <Dialog
          title="Confirmation"
          maxWidth="xl"
          primaryActionText="Mark as Verified"
          onPrimaryAction={checkAgency}
          secondaryActionText="Cancel"
          onSecondaryAction={closeModals}
          onClose={closeModals}
        >
          <Typography>
            Do you want to mark this submission as verified?
          </Typography>
        </Dialog>
      )}
      {showConfirmAgencyModal && (
        <Dialog
          title="Confirmation"
          maxWidth="xl"
          primaryActionText="Yes"
          onPrimaryAction={updateAgency}
          secondaryActionText="No"
          onSecondaryAction={() => {
            closeModals();
            setShowAgencyModal(true);
          }}
          onClose={closeModals}
        >
          <Typography>
            {report.getBrokerage()?.name} is a new {user.getBrokerageLabel()},
            do you want to add one?
          </Typography>
        </Dialog>
      )}
      {showAgencyModal && (
        <BrokersModal
          onClose={() => setShowAgencyModal(false)}
          report={report}
          onSave={verifySubmission}
        />
      )}
      {errors.length > 0 && (
        <Dialog
          title="Error(s) encountered during verification"
          maxWidth="xl"
          primaryActionText="Yes, proceed to verify"
          hidePrimary={hasHardErrors}
          onPrimaryAction={forceVerifySubmission}
          secondaryActionText="Cancel"
          onSecondaryAction={closeErrors}
          onClose={closeErrors}
          isLoading={verifySubmissionMutation.isLoading}
        >
          <Typography>
            The following error(s) were encountered during verification.
            {!hasHardErrors && " Do you want to verify anyway?"}
            {hasHardErrors &&
              " Critical errors need to be fixed before verifying."}
          </Typography>
          <ul>
            {errors.map((e, i) => (
              <li key={`Error_${i}`}>
                <Typography
                  fontWeight={e.is_hard_check ? 700 : 400}
                  color={e.is_hard_check ? "error" : "text.primary"}
                >
                  {e.is_hard_check ? "(CRITICAL - MUST BE FIXED) " : ""}
                  {e.error_message}
                </Typography>
              </li>
            ))}
          </ul>
        </Dialog>
      )}
    </>
  );
};

export default VerificationButton;
