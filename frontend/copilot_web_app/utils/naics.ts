export const normalizeNaics2 = (naics2: (string | null)[] | undefined) => {
  const naics_2_normalized: (string | null)[] = [];
  (naics2 ?? []).forEach((code) => {
    if (code === "NAICS_31-33") {
      naics_2_normalized.push(...["NAICS_31", "NAICS_32", "NAICS_33"]);
    }
    if (code === "NAICS_44-45") {
      naics_2_normalized.push(...["NAICS_44", "NAICS_45"]);
    }
    if (code === "NAICS_48-49") {
      naics_2_normalized.push(...["NAICS_48", "NAICS_49"]);
    } else {
      naics_2_normalized.push(code);
    }
  });
  return naics_2_normalized;
};

export const denormalizeNaics2 = (codes: (string | null)[] | undefined) => {
  const input = new Set(codes ?? []);
  const output: string[] = [];

  const mappings = [
    {
      group: ["NAICS_31", "NAICS_32", "NAICS_33"],
      collapsed: "NAICS_31-33",
    },
    {
      group: ["NAICS_44", "NAICS_45"],
      collapsed: "NAICS_44-45",
    },
    {
      group: ["NAICS_48", "NAICS_49"],
      collapsed: "NAICS_48-49",
    },
  ];

  const used = new Set<string>();

  for (const { group, collapsed } of mappings) {
    if (group.every((code) => input.has(code))) {
      output.push(collapsed);
      group.forEach((code) => used.add(code));
    }
  }

  for (const code of input) {
    if (code && !used.has(code)) {
      output.push(code);
    }
  }

  return [...new Set(output)];
};
