import invert from "lodash/invert";
import { ReportStage as hubReportStage } from "../src/constants/hubConstants";

export type ReportStage = hubReportStage;

export enum ORGANIZATION {
  KALEPA_TEST = 3,
  NATIONWIDE = 6,
  KALEPA_AUTOMATED_TESTS = 8,
  KALEPA_CLIENT_DEMO = 9,
  ARCH = 10,
  PARAGON = 37,
  ADMIRAL = 49,
  BOWHEAD = 54,
  NATIONWIDE_ML = 57,
  CONIFER = 58,
  K2 = 61,
  DOCUMENT_INGESTION = 62,
  SECURA = 64,
  AIG = 66,
}
export const STAGE_ID_TO_NAME: Record<ReportStage, string> = {
  CLEARING_ISSUE: "Clearing Issue",
  INDICATED: "Indicated",
  ON_MY_PLATE: "In Progress",
  WAITING_FOR_OTHERS: "Awaiting Reply",
  QUOTED: "Quoted",
  DECLINED: "Declined",
  QUOTED_LOST: "Lost",
  QUOTED_BOUND: "Bound",
  BLOCKED: "Blocked",
  EXPIRED: "Expired",
  COMPLETED: "Completed",
  CANCELED: "Canceled",
};

export function getStageName(stage: ReportStage | string): string {
  return STAGE_ID_TO_NAME[stage as ReportStage] ?? stage;
}

export const STAGE_NAME_TO_ID = invert(STAGE_ID_TO_NAME);

export enum UserReportsSorting {
  CREATED_AT = "CREATED_AT",
  EFFECTIVE_AT = "EFFECTIVE_AT",
  DECLINED_AT = "DECLINED_AT",
  AGENT_NAME = "AGENT_NAME",
  AGENCY_NAME = "AGENCY_NAME",
  ASSIGNEE = "ASSIGNEE",
  BROKER_NAME = "BROKER_NAME",
  BROKERAGE_NAME = "BROKERAGE_NAME",
  REPORT_NAME = "REPORT_NAME",
  STAGE = "STAGE",
  RECOMMENDATION = "RECOMMENDATION_V2",
  RECOMMENDATION_SCORE = "RECOMMENDATION_SCORE",
  BOOKMARKED = "BOOKMARKED",
  NAICS = "NAICS",
  EXPIRED_PREMIUM = "EXPIRED_PREMIUM",
  TARGET_PREMIUM = "TARGET_PREMIUM",
  TOTAL_PREMIUM_OR_BOUND_PREMIUM = "TOTAL_PREMIUM_OR_BOUND_PREMIUM",
  SALES = "SALES",
  READ = "READ",
  BOUND_PREMIUM = "BOUND_PREMIUM",
  QUOTED_PREMIUM = "QUOTED_PREMIUM",
  PAYROLL = "PAYROLL",
  FNI_STATE = "FNI_STATE",
  X_MOD_SCORES = "X_MOD_SCORES",
  ESTIMATED_PREMIUM = "ESTIMATED_PREMIUM",
  ADJUSTED_TIV = "ADJUSTED_TIV",
}

export const UNIQUE_CLIENT_ID_SUBMISSION_DELETION_MESSAGE =
  "The submission cannot be deleted because it currently has a unique submission client id, that needs to be removed first";

export const TOTAL_PREMIUM_TOOLTIP_EXPLANATION_TEXT =
  "Total premium is the bound premium, along with any post-binding adjustments, if any.";
