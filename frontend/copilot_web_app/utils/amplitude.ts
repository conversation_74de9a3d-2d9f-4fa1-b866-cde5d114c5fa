import { useEffect } from "react";
import { Router } from "next/router";
import {
  groupIdentify,
  identify,
  Identify,
  init as initAmplitude,
  reset,
  setGroup,
  setUserId,
  track,
} from "@amplitude/analytics-browser";
import isNil from "lodash/isNil";
import lowerCase from "lodash/lowerCase";

import { useGetUserGroupsQuery } from "@queries/user";
import { useCurrentUserQuery } from "@queries/auth";
import { isServer } from "./isServer";
import impersonationService from "../src/services/impersonationService";
import Report from "../src/models/Report";
import { config } from "config";
import { isDateString, isUUID } from "@legacy/helpers/validate";
import { useFlightControlStore } from "@utils/state/flightControlStore";
import {
  GetUsersSubmissionNotifications200Response,
  ModelFileFileTypeEnum,
} from "@legacy/api_clients/copilot_api_client";
import { RuleTypeEnum } from "@legacy/api_clients/recommendations-client";
import { useGetOrganizationByProvidedId } from "@queries/organizations";

const freeLocalStorageIfFull = () => {
  if (typeof window === "undefined") return;

  try {
    const twoHundredKb = new Array(200_000).join("a");

    localStorage.setItem("test-free-space", twoHundredKb);
    localStorage.removeItem("test-free-space");
  } catch (e) {
    Object.keys(localStorage)
      .filter((x) => x.startsWith("tracked-visited"))
      .forEach((x) => localStorage.removeItem(x));
  }
};

freeLocalStorageIfFull();

const trackingStore = {
  getVisitedReport: (reportId: string) => {
    const item = localStorage.getItem(`tracked-visited-${reportId}`);
    if (!item) return null;

    return {
      reportId,
      coverages: JSON.parse(item).coverages,
    };
  },
  setVisitedReport: (reportId: string, coverages: string[]) => {
    const setItem = () => {
      localStorage.setItem(
        `tracked-visited-${reportId}`,
        JSON.stringify({ coverages })
      );
    };

    try {
      setItem();
    } catch (_) {
      freeLocalStorageIfFull();

      try {
        setItem();
      } catch (_) {
        return false;
      }
    }

    return true;
  },
};

export const getRouteHistory = () =>
  (JSON.parse(sessionStorage.getItem("route-history")!) as string[]) || [];

const setRouteHistory = (history: string[]) =>
  sessionStorage.setItem("route-history", JSON.stringify(history));
const withoutQueryString = (url: string) => url.split("?")[0];

export const trackPageChangeHistory = () => {
  if (!getRouteHistory().length) {
    setRouteHistory([
      withoutQueryString(window.location.pathname),
      ...getRouteHistory(),
    ]);
  }

  Router.events.on("routeChangeStart", (url: string) => {
    const previousUrl = getRouteHistory()[0];
    if (
      !previousUrl ||
      withoutQueryString(url) === withoutQueryString(previousUrl)
    ) {
      return;
    }

    setRouteHistory(
      [withoutQueryString(url), ...getRouteHistory()].slice(0, 2)
    );
  });
};

export const initializeAmplitude = (): void => {
  if (
    !isServer() &&
    config.amplitudeApiKey &&
    !impersonationService.getUserToImpersonate()
  ) {
    initAmplitude(config.amplitudeApiKey, undefined, {
      flushQueueSize: 100,
      serverUrl: "/amplitude-passthrough/",
    });
  }
};

export const useSetAmplitudeUser = (): void => {
  const { data: currentUser } = useCurrentUserQuery();
  const { data: groups } = useGetUserGroupsQuery(
    currentUser?.organization_id ?? 1,
    { enabled: !!currentUser?.organization_id }
  );
  const { data: organization } = useGetOrganizationByProvidedId(
    currentUser?.organization_id,
    currentUser?.user.cross_organization_access
  );
  const group = groups?.find((group) =>
    (group.users ?? []).some((u) => u.id === currentUser?.user_id)
  );

  // Set up ampiutude analytics user
  useEffect(() => {
    if (!currentUser?.user_id) {
      return;
    }

    reset();

    // min user id length is 5
    const userId = currentUser.user_id.toString().padStart(5, "0");
    setUserId(userId);

    const hasCrossOrgAccess = currentUser.user.cross_organization_access;
    if (hasCrossOrgAccess) {
      setGroup("organization", "Kalepa Support");
      const identity = new Identify();
      identity.set("organization name", "Kalepa Support");
      groupIdentify("organization", "Kalepa Support", identity);
    } else {
      setGroup("organization", currentUser.user.organization.name);
    }
    const identity = new Identify();
    identity.set("user id", userId);
    sessionStorage.setItem("amplitude-user-id", userId);

    if (currentUser.user.email) {
      identity.set("user email", currentUser.user.email);
    }
    if (currentUser.user.organization.name) {
      identity.set("organization", currentUser.user.organization.name);
    }
    if (currentUser.user.organization.id && organization) {
      const groupIdentity = new Identify();
      groupIdentity.set("organization name", organization.name ?? "-");
      groupIdentity.set("organization id", currentUser.user.organization.id);
      groupIdentity.set(
        "renewal creation interval",
        organization.renewal_creation_interval ?? "-"
      );
      groupIdentity.set("appetite", organization.appetite ?? "-");
      groupIdentity.set("description", organization.description ?? "-");
      groupIdentity.set("email domain", organization.email_domain ?? "-");
      groupIdentity.set("for analysis", organization.for_analysis ?? "-");
      groupIdentity.set(
        "identity provider",
        organization.identity_provider ?? "-"
      );
      groupIdentity.set(
        "shared notification address",
        organization.shared_notification_address ?? "-"
      );
      groupIdentify(
        "organization",
        organization.name ?? currentUser.user.organization.name,
        groupIdentity
      );
    }
    if (currentUser.role) {
      identity.set("role", currentUser.role);
    }
    if (group?.name) {
      identity.set("group", group.name);
      if (group.id) identity.set("group id", group.id.toString());
    }
    if (!isNil(currentUser.user.isReadOnlyAccount())) {
      identity.set("user read only", currentUser.user.isReadOnlyAccount());
    }
    if (!isNil(currentUser.user.is_trusted)) {
      identity.set(
        "user type",
        currentUser.user.is_trusted ? "trusted" : "untrusted"
      );
    }

    const forAnalysis =
      currentUser.email === "<EMAIL>" ||
      currentUser.user.organization.for_analysis;
    if (!isNil(forAnalysis)) {
      identity.set("for analysis", forAnalysis);
    }

    identify(identity);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser?.user_id, currentUser?.user.organization.id, organization]);
};

const incrementUserProp = (prop: string) => {
  const userId = sessionStorage.getItem("amplitude-user-id");
  if (!userId) return;

  const identifyObj = new Identify();
  identifyObj.set("user id", userId);
  identifyObj.add(prop, 1);

  identify(identifyObj);
};

export const trackEvent = (name: string, eventProps?: any) => {
  const getPageName = (page: string | undefined) => {
    if (!page) return null;

    if (page.startsWith("/report")) {
      return "flight control";
    } else if (page.startsWith("/portfolio-manager")) {
      if (page === "/portfolio-manager/") return "portfolio manager";
      return "rule view";
    } else if (page === "/" || page.startsWith("/?")) {
      return "hub";
    } else {
      return page;
    }
  };

  const latestPage =
    getRouteHistory()[0] ?? withoutQueryString(window.location.pathname);

  const currentPage = getPageName(latestPage);
  const previousPage = getPageName(getRouteHistory()[1]);

  const processEventProps = (props?: any) => {
    if (!props) {
      return {};
    }

    // boolean values to yes/no
    const keys = Object.keys(props);
    keys.forEach((key) => {
      if (typeof props[key] === "boolean") {
        props[key] = props[key] ? "yes" : "no";
      }
    });

    const processValue = (key: string, value: string) =>
      !isUUID(value) && !isDateString(value) && key !== "name";

    return Object.fromEntries(
      keys.map((key) => [
        lowerCase(key),
        typeof props[key] === "string" && processValue(key, props[key])
          ? props[key].toLowerCase()
          : props[key],
      ])
    );
  };

  const eventData = {
    place: currentPage,
    "previous page": previousPage,
    ...processEventProps(eventProps),
  };
  if (config.debugAmplitudeEvents) {
    // eslint-disable-next-line no-console
    console.log("Amplitude event:", name, eventData);
  }

  track(name, eventData);
};

export const trackHubPageView = (
  view: "kanban" | "list",
  visibleColumns: string[]
) => {
  trackEvent("hub page view", {
    "default view": view,
    "visible columns": visibleColumns,
  });
};

export const trackFinancialTableExpanded = ({
  reportId,
  title,
  action,
}: {
  reportId: string;
  title: string;
  action: string;
}) => {
  trackEvent("financial table interaction", {
    reportId,
    action,
    title,
  });
};

export const trackHubFiltersOpen = () => {
  trackEvent("hub filters open");
};

type TrackHubFiltersData = {
  resultsNo?: number;
  sortOrder: "newest first" | "oldest first";
  sortBy?: string | null;
  filterBy?: string;
  search: boolean;
  stage?: string;
  noOfCombinedFilters: number;
  coverageOperator?: string;
};
export const trackHubFiltersUsed = (data: TrackHubFiltersData) => {
  trackEvent("hub filters used", data);
};

export const trackHubFilterChipClosed = (filter: string) => {
  trackEvent("hub filter chips closed", { filter });
};

export const trackHubViewChanged = (view: "kanban" | "list") => {
  trackEvent("hub view changed", { view });
};

type TrackHubSortedData = {
  from?: "filters" | "table header";
  sortOrder?: "newest first" | "oldest first";
  sortBy?: string | null;
  tab?: string;
};

export const trackHubSorted = (data: TrackHubSortedData) => {
  trackEvent("hub sorted", data);
};

export const trackHubSwitchBetweenCategories = (
  category:
    | "in flight"
    | "upcoming revewals"
    | "bound"
    | "lost"
    | "declined"
    | "clearing issues"
) => {
  trackEvent("hub switch between categories", { category });
};

type SubmissionDetailData = Record<string, unknown>;

function trackSubmissionEvent<T extends SubmissionDetailData>(
  func: (data: T) => void
) {
  return (report: Report, data: T) => {
    if (!report) {
      return;
    }
    const activeBusiness =
      useFlightControlStore.getState().activeBusiness ??
      report.getBusinesses().length === 1
        ? report.getBusinesses()[0]
        : null;
    const tab = useFlightControlStore.getState().activeTab;

    func({
      createdAt: report.created_at,
      name: report.name,
      ownerId: report.owner_id,
      stage: report.getStage(),
      proposedEffectiveDate: report.getSubmission().proposed_effective_date,
      isRenewal: report.getSubmission().is_renewal,
      recommendationAction: report.getSubmission().recommendation_v2_action,
      copilotScore: report.getSubmission().recommendation_v2_score,
      declinedDate: report.getSubmission().declined_date,
      declinedUserId: report.getSubmission().declined_user_id,
      reasonForDeclining: report.getSubmission().reason_for_declining,
      dueDate: report.getSubmission().due_date,
      policyExpirationDate: report.getSubmission().policy_expiration_date,
      lobId: report.getSubmission().line_of_business,
      brokerId: report.getBroker()?.id,
      brokerageId: report.getBrokerage()?.id,
      isNaicsVerified: report.getSubmission().is_naics_verified,
      isVerificationRequired: report.getSubmission().is_verification_required,
      isVerified: report.getSubmission().is_verified,
      receivedDate: report.getSubmission().received_date,
      isMetricsSetManually: report.getSubmission().is_metrics_set_manually,
      processingState: report.getSubmission().processing_state,
      primaryNaicsCode: report
        .getSubmission()
        .primary_naics_code?.replace("NAICS_", ""),
      "2DigitsFromPrimaryNaics": report?.get2DigitNaics(),

      reportId: report.id,
      submissionId: report.getSubmission().id,
      coverage: report.getCoverages().map((x) => x.getCoverageName()),
      locationsNumber: (report.getSubmission().businesses || []).length,
      locationType: !activeBusiness
        ? undefined
        : activeBusiness.isRealEstate()
          ? "premises"
          : "business",
      tab: !tab ? undefined : tab,
      stuckReason: report.getSubmission().stuck_reason,

      ...data,
    });
  };
}

type SubmissionPageViewData = SubmissionDetailData & {
  from: string;
  report: Report;
};
export const trackSubmissionPageView = trackSubmissionEvent(
  (data: SubmissionPageViewData) => {
    const eventData = { ...data } as Omit<SubmissionPageViewData, "report">;
    delete eventData["report"];

    trackEvent("submission page view", eventData);

    let visitedReport = trackingStore.getVisitedReport(data.report.id);
    if (!visitedReport) {
      if (!trackingStore.setVisitedReport(data.report.id, [])) {
        return;
      }

      incrementUserProp("submissions viewed");
      visitedReport = trackingStore.getVisitedReport(data.report.id);
    }

    const notTrackedCoverages = data.report
      .getCoverages()
      .map((x) => x.coverage.display_name!)
      .filter((x) => !visitedReport!.coverages.includes(x));

    if (!notTrackedCoverages.length) return;

    notTrackedCoverages.forEach((x) => {
      incrementUserProp(`preferred coverage ${x}`);
    });

    trackingStore.setVisitedReport(data.report.id, [
      ...visitedReport?.coverages,
      ...notTrackedCoverages,
    ]);
  }
);

type SubmissionSummaryViewEditorClickedData = SubmissionDetailData & {
  subsection?: string;
};
export const trackSubmissionSummaryViewEditorClicked = trackSubmissionEvent(
  (data: SubmissionSummaryViewEditorClickedData) => {
    trackEvent("submission summary view editor clicked", data);
  }
);

export const trackLossRunsProcessingInvokedManually = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("submission had loss run processing manually invoked", data);
  }
);

export type SummaryPreferenceAction = "addition" | "subtraction";

type SubmissionSummaryViewEditedData = SubmissionDetailData & {
  summaryType: string;
  action?: SummaryPreferenceAction;
};
export const trackSubmissionSummaryViewEdited = trackSubmissionEvent(
  (data: SubmissionSummaryViewEditedData) => {
    trackEvent("submission summary view edited", data);
  }
);

export const trackSubmissionHistoryView = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("submission history view", data);
  }
);

export const trackSubmissionHistoryAction = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("submission history action", data);
  }
);

export const trackNewsClicked = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("news clicked", data);
  }
);

type ArticleFilteredData = SubmissionDetailData & {
  item: "date published" | "entities" | "category" | "role";
  filterSource: "filter";
};

export const trackNewsFiltered = trackSubmissionEvent(
  (data: ArticleFilteredData) => {
    trackEvent("news filtered", data);
  }
);

export const trackLegalFilingsFiltered = trackSubmissionEvent(
  (data: ArticleFilteredData) => {
    trackEvent("legal filings filtered", data);
  }
);

type ArticleSearchedData = SubmissionDetailData & {
  searchValue: string;
};

export const trackNewsSearched = trackSubmissionEvent(
  (data: ArticleSearchedData) => {
    trackEvent("news searched", data);
  }
);

export const trackLegalFilingsSearched = trackSubmissionEvent(
  (data: ArticleSearchedData) => {
    trackEvent("legal filings searched", data);
  }
);

type DocumentData = SubmissionDetailData & {
  documentId?: string;
};

export const trackNewsPreviewOpened = trackSubmissionEvent(
  (data: DocumentData) => {
    trackEvent("news preview opened", data);
  }
);

type NewsPreviewClosedData = DocumentData & {
  method?: string;
};

export const trackNewsPreviewClosed = trackSubmissionEvent(
  (data: NewsPreviewClosedData) => {
    trackEvent("news preview closed", data);
  }
);

export const trackLegalFilingsClicked = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("legal filings clicked", data);
  }
);

type UrlOpenedData = SubmissionDetailData & {
  url: string | undefined;
  urlType:
    | "news"
    | "legal filings"
    | "notebook"
    | "business website"
    | "license"
    | "recall"
    | "fact info panel"
    | "violation"
    | "enforcement case"
    | "note";
};
export const trackUrlOpened = trackSubmissionEvent((data: UrlOpenedData) => {
  trackEvent("url opened", data);
});

type FactDetailsOpenedData = SubmissionDetailData & {
  factSubtype: string;
  parentType: string;
  factType: string;
  from?: string;
  factLabel?: string;
};
export const trackFactDetailsOpened = trackSubmissionEvent(
  (data: FactDetailsOpenedData) => {
    trackEvent("fact details opened", data);
  }
);

type FactUpdatedData = SubmissionDetailData & {
  factType: string;
  factSubtype: string;
  parentType: string;
  item: string;
  isRelevant?: "yes" | "no";
};
export const trackFactUpdated = trackSubmissionEvent(
  (data: FactUpdatedData) => {
    trackEvent("fact updated", data);
  }
);

export type SubmissionStatusChangeSource =
  | "bulk action"
  | "drag and drop"
  | "dropdown"
  | "email modal"
  | "clearing drawer"
  | "submission preview"
  | "recommended action";
type SubmissionStatusChangedData = SubmissionDetailData & {
  method: SubmissionStatusChangeSource;
  newStatus?: string;
  previousStatus?: string;
  lostReasons?: string;
};
export const trackSubmissionStatusChangeInitiated = trackSubmissionEvent(
  (data: SubmissionStatusChangedData) => {
    trackEvent("submission status change initiated", data);

    incrementUserProp("status change initiated");
  }
);
export const trackSubmissionStatusChanged = trackSubmissionEvent(
  (data: SubmissionStatusChangedData) => {
    trackEvent("submission status changed", data);

    incrementUserProp("status changed");
  }
);

type BulkSubmissionStatusChnagedData = {
  newStatus: string;
  reports: Report[];
  source:
    | "bulk action"
    | "drag and drop"
    | "dropdown"
    | "clearing drawer"
    | "recommended action";
};
export const trackBulkSubmissionStatusChanged = ({
  newStatus,
  reports,
  source,
}: BulkSubmissionStatusChnagedData) => {
  if (reports.length === 1) {
    trackSubmissionStatusChanged(reports[0], {
      method: source,
      newStatus,
      previousStatus: reports[0].getSubmission().stage,
    });
  } else {
    trackEvent("bulk submission status changed", {
      newStatus,
      noUpdatedSubmissions: reports.length,
    });
  }
};

type SubmissionEditedData = SubmissionDetailData & {
  item: string;
  coverage?: string[];
};
export const trackSubmissionEdited = trackSubmissionEvent(
  (data: SubmissionEditedData) => {
    trackEvent("submission edited", data);
  }
);

type TrackCorporateRelationsGraphIntegrationData = SubmissionDetailData & {
  action: "bubble clicked" | "relation clicked";
};
export const trackCorporateRelationsGraphIntegration = trackSubmissionEvent(
  (data: TrackCorporateRelationsGraphIntegrationData) => {
    trackEvent("corporate relations graph interaction", data);
  }
);

type FeedbackProvidedData = {
  isRelevant?: boolean;
  item?: "news" | "legal filings";
  factSubtype?: string;
  label?: string;
  notes?: boolean;
  evidence?: boolean;
};
export const trackFeedbackProvided = trackSubmissionEvent(
  (data: FeedbackProvidedData) => {
    trackEvent("feedback provided", data);

    incrementUserProp("feedback provided");
  }
);

export const trackLoggedOut = () => {
  trackEvent("logged out", {});
};

type ErrorEncounteredData = {
  actionType: "read" | "write";
  actionId: string;
};
export const trackErrorEncountered = (data: ErrorEncounteredData) => {
  trackEvent("error encountered", data);
};

export const trackNewFillingAddedManually = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("new filing added manually", data);
  }
);

export const trackNewRecallAddedManually = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("new recall added manually", data);
  }
);

type OshaViolationAddedData = {
  url: string;
};

export const trackNewOshaViolationAddedManually = trackSubmissionEvent(
  (data: OshaViolationAddedData) => {
    trackEvent("new osha violation added manually", data);
  }
);

export const trackNewWarningLetterAddedManually = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("new warning letter added manually", data);
  }
);

export const trackNewNewsAddedManually = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("new news added manually", data);
  }
);

export const trackArticleMovedToLegalFillings = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("article moved to legal filings", data);
  }
);

export const trackArticleMovedToNews = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("article moved to news", data);
  }
);

type TableInteractionData = {
  tableType: string;
  action: string;
  filter?: string;
  sorting?: string;
  column?: string;
};
export const trackTableInteraction = (data: TableInteractionData) => {
  trackEvent("table interaction", data);
};

type ChartInteractionData = {
  chartType: string;
  action: string;
  value?: string;
};
export const trackChartInteraction = (data: ChartInteractionData) => {
  trackEvent("chart interaction", data);
};

export const trackRecommendationDetailsChecked = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("recommendation details checked", data);
  }
);

export const trackHazardHubIngestionRequested = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("hazard hub ingestion requested", data);
  }
);

type FileNameData = {
  fileName: string;
  fileType: string;
  lossRunStatus: string;
};

export const trackFileNameClicked = trackSubmissionEvent(
  (data: FileNameData) => {
    trackEvent("file name clicked", data);
  }
);

type LossRunSummariesToggledData = {
  enabled: boolean;
};
export const trackLossRunSummariesToggled = trackSubmissionEvent(
  (data: LossRunSummariesToggledData) => {
    trackEvent("loss run summary files toggled", data);
  }
);

type ArticlePageChangedData = {
  documentType: string;
  pageNumber: number;
  prevPageNumber: number;
};

export const trackArticlePageChanged = trackSubmissionEvent(
  (data: ArticlePageChangedData) => {
    trackEvent(`${data.documentType} page changed`, data);
  }
);

type ArticlePerPageChangedData = {
  documentType: string;
  perPage: number;
  prevPerPage: number;
};

export const trackArticlePerPageChanged = trackSubmissionEvent(
  (data: ArticlePerPageChangedData) => {
    trackEvent(`${data.documentType?.toLowerCase()} per page changed`, data);
  }
);

export const trackSubmissionDuplicated = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("submission duplicated", data);
  }
);

export const trackSubmissionDeleted = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("submission deleted", data);
  }
);

type SubmissionActionInitiatedData = SubmissionDetailData & {
  method: "hub action" | "flight control action";
  action: string;
};
export const trackSubmissionActionInitiated = trackSubmissionEvent(
  (data: SubmissionActionInitiatedData) => {
    trackEvent("submission action initiated", data);
  }
);

type ImportantHighlihtUsedData = SubmissionDetailData & {
  item: string;
  recommendation?: string;
};

export const trackImportantHighlightUsed = trackSubmissionEvent(
  (data: ImportantHighlihtUsedData) => {
    trackEvent("important highlights used", data);
  }
);

type AgentModalSearchData = SubmissionDetailData & {
  pickedSearchValue: string;
};

export const trackAgentModalSearched = trackSubmissionEvent(
  (data: AgentModalSearchData) => {
    trackEvent("Broker/Brokerage modal searched", data);
  }
);

export const trackBrokerEmailEdited = trackSubmissionEvent(() => {
  trackEvent("Broker email edited", {});
});

export const trackBrokerageDomainsEdited = trackSubmissionEvent(() => {
  trackEvent("Brokerage domains edited", {});
});

export const trackCorrespondenceContactEmailEdited = trackSubmissionEvent(
  () => {
    trackEvent("Correspondence contact email edited", {});
  }
);

export const trackReportViewedFromNotesEmail = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("Report viewed from notes email", data);
  }
);

type NotesButtonClickedData = SubmissionDetailData & {
  notificationsCount: GetUsersSubmissionNotifications200Response | undefined;
};
export const trackNotesButtonClicked = trackSubmissionEvent(
  (data: NotesButtonClickedData) => {
    const notes = data.notificationsCount?.notes_count;
    const unreadNotes = !data.notificationsCount
      ? undefined
      : data.notificationsCount.notes_count! -
        data.notificationsCount.seen_notes!;

    const newData = {
      ...data,
      notificationsCount: undefined,

      notes,
      unreadNotes,
    };

    trackEvent("Notes button clicked", newData);
  }
);

type NoteModifiedData = SubmissionDetailData & {
  mentionsUsed: boolean;
  type: "note" | "message";
};
export const trackNoteAdded = trackSubmissionEvent((data: NoteModifiedData) => {
  trackEvent("Note added", data);
});

export const trackNoteEdited = trackSubmissionEvent(
  (data: NoteModifiedData) => {
    trackEvent("Note edited", data);
  }
);

export const trackNoteDeleted = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("Note deleted", data);
  }
);

type HubColumnsEditedData = {
  columnAdded?: string;
  columnRemoved?: string;
};

export const trackHubColumnsEdited = (data: HubColumnsEditedData) => {
  trackEvent("hub columns edited", data);
};

type SubmissionEmailSentData = {
  source: "hub" | "flight control" | "clearing drawer";
  type: string;
};
export const trackSubmissionEmailSent = trackSubmissionEvent(
  (data: SubmissionEmailSentData) => {
    trackEvent("submission email sent", data);
  }
);

type SubmissionSidebarUsedData = SubmissionDetailData & {
  item: string;
  recommendation?: string;
};
export const trackSubmissionSidebarUsed = trackSubmissionEvent(
  (data: SubmissionSidebarUsedData) => {
    trackEvent("submission sidebar used", data);
  }
);

type SubmissionSidebarSearchData = SubmissionDetailData & {
  searchValue: string;
};
export const trackSubmissionSidebarSearch = trackSubmissionEvent(
  (data: SubmissionSidebarSearchData) => {
    trackEvent("submission sidebar search", data);
  }
);

type SubmissionSidebarSectionToggled = {
  section: string;
};
export const trackSubmissionSidebarSectionExpanded = trackSubmissionEvent(
  (data: SubmissionSidebarSectionToggled) => {
    trackEvent("submission sidebar section expanded", data);
  }
);
export const trackSubmissionSidebarSectionCollapsed = trackSubmissionEvent(
  (data: SubmissionSidebarSectionToggled) => {
    trackEvent("submission sidebar section collapsed", data);
  }
);

export const trackSubmissionUrlCopied = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("submission url copied", data);
  }
);

type MapInteractionData = SubmissionDetailData & {
  action: string;
  mapType: string;
};
export const trackMapInteraction = trackSubmissionEvent(
  (data: MapInteractionData) => {
    trackEvent("map interaction", data);
  }
);

export const trackImagesGalleryUsed = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("images gallery used", data);
  }
);

export const trackSubmissionPreviewEvent = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("submission preview action", data);
  }
);

type FilePreviewData = SubmissionDetailData & {
  item: string;
  place?: string;
};

export const trackFilePreview = trackSubmissionEvent(
  (data: FilePreviewData) => {
    trackEvent("document gallery used", data);
  }
);

export const trackNotificationsPageView = () => {
  trackEvent("notifications page view");
};

export const trackNotificationPreferenceChange = (
  notification: string,
  enabled: boolean
) => {
  const eventName = enabled
    ? "notification turned on"
    : "notification turned off";
  trackEvent(eventName, { notification });
};

type TrackRecommendationUsedData = SubmissionDetailData & {
  recommendation?: string;
};

export const trackRecommendationUsed = trackSubmissionEvent(
  (data: TrackRecommendationUsedData) => {
    trackEvent("recommendations used", data);
  }
);

type TrackReferredData = SubmissionDetailData & {
  users: string;
};

export const trackReferred = trackSubmissionEvent((data: TrackReferredData) => {
  trackEvent("submission referred", data);
});

export const trackNaicsVerified = trackSubmissionEvent((data) => {
  trackEvent("NAICS verified", data);
});

type TrackNaicsExpandeddData = SubmissionDetailData & {
  state: "hidden" | "showed";
};

export const trackNaicsExpanded = trackSubmissionEvent(
  (data: TrackNaicsExpandeddData) => {
    trackEvent("naics description toggled", data);
  }
);

type TrackGlCodesToggledData = SubmissionDetailData & {
  state: "hidden" | "showed";
};

export const trackGlCodesToggled = trackSubmissionEvent(
  (data: TrackGlCodesToggledData) => {
    trackEvent("gl iso codes toggled", data);
  }
);

type TrackEmailInteractionData = SubmissionDetailData & {
  notificationsCount: GetUsersSubmissionNotifications200Response | undefined;
  action: string;
};

export const trackEmailInteraction = trackSubmissionEvent(
  (data: TrackEmailInteractionData) => {
    const messages = data.notificationsCount?.email_count;
    const unreadMessages = !data.notificationsCount
      ? undefined
      : data.notificationsCount.email_count! -
        data.notificationsCount.seen_emails!;

    const newData = {
      ...data,
      notificationsCount: undefined,
      messages,
      unreadMessages,
    };

    trackEvent("submission email interaction", newData);
  }
);

type TrackSubmissionInfoEditData = SubmissionDetailData & {
  item: string;
};

export const trackSubmissionInfoEdit = trackSubmissionEvent(
  (data: TrackSubmissionInfoEditData) => {
    trackEvent("submission details edit clicked", data);
  }
);

export const trackSubmissionInfoEditCancel = trackSubmissionEvent(
  (data: TrackSubmissionInfoEditData) => {
    trackEvent("submission details edit cancelled", data);
  }
);

export const trackSubmissionVerified = trackSubmissionEvent((data) => {
  trackEvent("submission verified", data);
});

type PremisesViewerMapChangesData = SubmissionDetailData & {
  action: "show map" | "show street view";
};
export const trackPremisesViewerMapChanges = trackSubmissionEvent(
  (data: PremisesViewerMapChangesData) => {
    trackEvent("premises viewer map changes", data);
  }
);
export type FilterSource =
  | "guideline"
  | "rule"
  | "chart"
  | "table"
  | "card"
  | "chip"
  | "exposure breakdown map"
  | "overview map"
  | "overview card"
  | "fni";
type PremisesViewerFilteredTrackingData = SubmissionDetailData & {
  filterType: string;
  searchValue?: string;
  filterSource: FilterSource;
};
export const trackPremisesViewerFiltered = trackSubmissionEvent(
  (data: PremisesViewerFilteredTrackingData) => {
    trackEvent("premises viewer filtered", data);
  }
);

type PremisesViewerTabChangedData = SubmissionDetailData & {
  tab: string;
};
export const trackPremisesViewerTabChanged = trackSubmissionEvent(
  (data: PremisesViewerTabChangedData) => {
    trackEvent("premises viewer tab changed", data);
  }
);

export const trackPremisesViewerFiltersCleared = trackSubmissionEvent(
  (data) => {
    trackEvent("premises viewer clear filters button clicked", data);
  }
);

type TrackRuleTestedData = {
  ruleId: string;
  ruleName: string;
  recommendation?: string;
  success: boolean;
};

export const trackRuleTested = (data: TrackRuleTestedData) => {
  trackEvent("Rule tested", data);
};

type TrackReRunGuidelinesButtonClickedData = {
  ruleId: string;
  ruleName: string;
  recommendation?: string;
};
export const trackReRunGuidelinesButtonClicked = (
  data: TrackReRunGuidelinesButtonClickedData
) => {
  trackEvent("pick submissions for re-running guidelines button clicked", data);
};

type TrackGuidelinesManuallyReRanData = {
  ruleId: string;
  ruleName: string;
  recommendation?: string;
  submissionsNumber: number;
};
export const trackGuidelinesManuallyReRan = (
  data: TrackGuidelinesManuallyReRanData
) => {
  trackEvent("guidelines manually re-ran", data);
};

type TrackFileReclassidiefData = SubmissionDetailData & {
  originalClassification: ModelFileFileTypeEnum;
  newClassification: ModelFileFileTypeEnum;
};
export const trackFileReclassified = trackSubmissionEvent(
  (data: TrackFileReclassidiefData) => {
    trackEvent("file reclassified", data);
  }
);

export const trackSubmissionStuckChanged = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("submission stuck changed", data);
  }
);

type TrackRuleEditedData = {
  numberOfConditions: number;
  numberOfGroups: number;
  recommendation: string;
  exclusions: string;
  ruleId: string;
  ruleName: string;
  ruleType: RuleTypeEnum;
};

export const trackRuleEdited = (data: TrackRuleEditedData) => {
  trackEvent("rule edited", data);
};

export const trackRuleCreated = (data: TrackRuleEditedData) => {
  trackEvent("new rule created", data);
};

type LightboxUsedData = {
  action: string;
};

export const trackLightboxUsed = trackSubmissionEvent(
  (data: LightboxUsedData) => {
    trackEvent("lightbox used", data);
  }
);

type TrackQuickSummaryOpenedData = {
  relation: "business at this location" | "corporate relation";
};

export const trackQuickSummaryOpened = trackSubmissionEvent(
  (data: TrackQuickSummaryOpenedData) => {
    trackEvent("quick summary opened", data);
  }
);

type DataOnboardingReplaceSovUsedData = {
  fileName: string;
  fileType: string;
};
export const trackDataOnboardingReplaceSovUsed = trackSubmissionEvent(
  (data: DataOnboardingReplaceSovUsedData) => {
    trackEvent("replace SOV used", data);
  }
);

export const trackGuidelineManagerPageView = () => {
  trackEvent("portfolio manager page view", {});
};

export const trackManagementDashboardPageView = () => {
  trackEvent("management dashboard page view", {});
};

type DashboardEventsFilter = {
  numberOfFilters: number;
  filters: string[];
  calculateBy: string;
  dateRange: string;
};

export const trackManagementDashboardFiltersUsed = (
  data: DashboardEventsFilter
) => {
  trackEvent("management dashboard filters used", data);
};

type TrackRuleProps = {
  ruleId: string;
  ruleName: string;
};

export const trackRulePageView = (data: TrackRuleProps) => {
  trackEvent("rule page view", data);
};

export const trackMoreInsightsOnRulesClicked = () => {
  trackEvent("more insights on rules clicked", {});
};

export const trackRuleDeleted = (data: TrackRuleProps) => {
  trackEvent("rule deleted", data);
};

export const trackRuleDuplicated = (data: TrackRuleProps) => {
  trackEvent("rule duplicated", data);
};

type NewRulesGroupCreatedData = {
  name: string;
};
export const trackNewRulesGroupCreated = (data: NewRulesGroupCreatedData) => {
  trackEvent("new rules group created", data);
};

export const trackAddNewRuleClicked = () => {
  trackEvent("add new rule clicked", {});
};

type RulesTableInteractionData = {
  action:
    | "search used"
    | "filters expanded"
    | "filters collapsed"
    | "filter used"
    | "tab changed"
    | "rule activated"
    | "rule deactivated"
    | "rule assigned to group"
    | "group assignee removed"
    | "expiration date set up"
    | "rule duplicated"
    | "rule deleted";
  filteredBy?: string[];
};

export const trackRulesTableInteraction = (data: RulesTableInteractionData) => {
  trackEvent("rules table interaction", data);
};

export const trackGlobalSearch = (
  searchValue: string,
  resultsFound: number
) => {
  trackEvent("global search used", {
    searchValue,
    resultsFound,
  });
};

type NumericCardExpandData = {
  type: string;
};

export const trackNumericCardExpanded = trackSubmissionEvent(
  (data: NumericCardExpandData) => {
    trackEvent("numeric card expanded", data);
  }
);

export const trackNumericCardCollapsed = trackSubmissionEvent(
  (data: NumericCardExpandData) => {
    trackEvent("numeric card collapsed", data);
  }
);

export const trackBinaryFactSubtypeAdded = (
  factSubtypeId: string,
  name: string,
  grouping?: string
) => {
  trackEvent("binary fact subtype added", {
    factSubtypeId,
    name,
    grouping,
  });
};

export const trackMultiLabelClassifierGroupAdded = (
  factSubtypeId: string,
  name: string
) => {
  trackEvent("multi label classifier group added", {
    factSubtypeId,
    name,
  });
};

export const trackNotesInNewTabOpened = trackSubmissionEvent(
  ({ from }: { from: string }) => {
    trackEvent("notes opened in a new tab", { from });
  }
);

export const trackMapInteractionEvent = (
  mapType: "exposure breakdown" | "overview",
  action: string
) => {
  trackEvent("map interaction", {
    mapType,
    action,
  });
};

export const trackDashboardChartInteractionEvent = (
  chartType: string,
  action: string,
  extraProps?: Record<string, any>
) => {
  trackEvent("dashboard chart interaction", {
    chartType,
    action,
    extraProps,
  });
};

export const trackDashboardToHubFilterUsed = (
  filterKey: string,
  filterValue: string
) => {
  trackEvent("hub filtered from management dashboard", {
    filterKey,
    filterValue,
  });
};

type PdsValidationErrorData = {
  processingState: string;
  submissionId: string;
  reportId: string;
  message: string;
  factSubtypeIds?: string[];
};
export const trackPdsValidationError = (params: PdsValidationErrorData) => {
  trackEvent("PDS validation error", params);
};

type PdsFailedToOpenFiles = {
  reportId: string;
  submissionId: string;
  processingState: string;
  supportUserEmail: string;
  fileNames: string[];
  fileTypes: string[];
};

export const trackPdsFailedToOpenAllFiles = (params: PdsFailedToOpenFiles) => {
  trackEvent("PDS failed to open all files", params);
};
export const trackPdsFailedToSupplementalFiles = (
  params: PdsFailedToOpenFiles
) => {
  trackEvent("PDS failed to open supplemental files", params);
};

type BrokerageAddedData = {
  brokerageId: string;
  name: string;
  byUserId: number;
};

export const trackBrokerageAdded = (params: BrokerageAddedData) => {
  trackEvent("brokerage added", params);
};

type BrokerageEmployeeAddedData = {
  brokerageEmployeeId: string;
  name: string;
  email?: string;
  brokerageId: string;
  byUserId: number;
};

export const trackBrokerageEmployeeAdded = (
  params: BrokerageEmployeeAddedData
) => {
  trackEvent("brokerage employee added", params);
};

type ErisaCardInteraction = {
  action: string;
};
export const trackErisaCardInteraction = (params: ErisaCardInteraction) => {
  trackEvent("erisa card interaction", params);
};

type ErisaPlansSorted = {
  sortBy: string;
};
export const trackErisaPlansSorted = (params: ErisaPlansSorted) => {
  trackEvent("erisa plans sorted", params);
};

type ErisaPlansSearched = {
  searchValue: string;
};
export const trackErisaPlansSearched = (params: ErisaPlansSearched) => {
  trackEvent("erisa plans searched", params);
};

export const trackErisaPlansFiltered = () => {
  trackEvent("erisa plans filtered", {});
};

export const trackUnknownFactsPreviewButtonClicked = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("unknown facts preview button clicked", data);
  }
);

export const trackUnkownFactsDisplayButtonClicked = trackSubmissionEvent(
  (data: SubmissionDetailData) => {
    trackEvent("unknown facts display button clicked", data);
  }
);

type SubmissionStuckData = SubmissionDetailData & {
  stuckReason: string;
  issue: string[];
  stucking_user: string;
};

export const trackSubmissionStuck = trackSubmissionEvent(
  (data: SubmissionStuckData) => {
    trackEvent("submission stuck", data);
  }
);

type SubmissionEmailEditedData = SubmissionDetailData & {
  changeType: "template selection" | "edit";
};

export const trackSubmissionEmailEdited = trackSubmissionEvent(
  (data: SubmissionEmailEditedData) => {
    trackEvent("submission email edited", data);
  }
);

type LocationHiddenShownToggleData = SubmissionDetailData & {
  locationsHidden: number;
  locationsShown: number;
};

export const trackLocationHiddenShownToggle = trackSubmissionEvent(
  (data: LocationHiddenShownToggleData) => {
    trackEvent("location hidden/shown toggle", data);
  }
);

export const trackAddZerosClicked = () => {
  trackEvent("add 000s clicked");
};

type RiskMeterRequestedData = SubmissionDetailData & {
  riskTypes: string[];
};

export const trackRiskMeterRequested = trackSubmissionEvent(
  (data: RiskMeterRequestedData) => {
    trackEvent("risk meter requested", data);
  }
);

type NotesSectionChangeData = SubmissionDetailData & {
  section: "notes" | "messages";
};

export const trackNotesSectionChange = trackSubmissionEvent(
  (data: NotesSectionChangeData) => {
    trackEvent("notes section change", data);
  }
);

type PrometrixRequestedData = SubmissionDetailData & {
  risks: string[];
};

export const trackPrometrixRequested = trackSubmissionEvent(
  (data: PrometrixRequestedData) => {
    trackEvent("prometrix requested", data);
  }
);

type SubmissionQueueAssignmentsData = {
  reportId: string;
  assignee: string;
  assigner: string;
};

export const trackSubmissionQueueAssignments = (
  params: SubmissionQueueAssignmentsData
) => trackEvent("Submission in queue assigned", params);
