import { useQuery, UseQueryResult } from "@tanstack/react-query";

import { queryKeyFactory } from "./queryKey";
import { transformerServiceClient } from "@legacy/clients/transformerServiceClient";
import { useOrganizationId } from "@utils/auth";
import { ExportDTO } from "@legacy/api_clients/report-transformer-service-client";

export const useExportTemplates = (): UseQueryResult<ExportDTO[]> => {
  const organizationId = useOrganizationId();

  return useQuery(
    queryKeyFactory.exportTemplates(organizationId),
    () => transformerServiceClient.getAllExports(),
    { enabled: !!organizationId }
  );
};
