import { routes } from "@features/routes";
import {
  ActionTypeEnum,
  ApiError,
  GetRulesPage_RuleResponse_ as GetRulesPageRuleResponse,
  PatchRuleRequest,
  RecommendationActionEnum,
  RecommendationResponse,
  RuleGroupRequest,
  RuleRequest,
  RuleResponse,
  RulesBulkActionRequest,
  RuleSorting,
  RuleTypeEnum,
  StatisticsFieldsEnum,
  type PortfolioRuleTypeEnum,
  type RecommendationFeedbackRequestModel,
  type GroupTypeEnum,
  type TriggeredRulesRequest,
  type ExpandEnum,
} from "@legacy/api_clients/recommendations-client";
import { default as copilotApiClient } from "@legacy/clients/copilotApiClient";
import { recommendationsClient } from "@legacy/clients/recommnedationsClient";
import { useOrganizationId } from "@utils/auth";
import axios, { AxiosError } from "axios";
import { useRouter } from "next/router";
import { useCallback } from "react";
import {
  useMutation,
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from "@tanstack/react-query";
import { queryKeyFactory } from "./queryKey";
import { useSnackbar } from "notistack";

type RulesListParams = {
  search?: string;
  isActive?: boolean;
  activeSince?: string;
  recommendationAction?: RecommendationActionEnum;
  hasExclusion?: boolean;
  sorting?: RuleSorting;
  descending?: boolean;
  page?: number;
  size?: number;
  groupId?: string;
  ruleType?: RuleTypeEnum;
  actions?: ActionTypeEnum[];
  actionsOperator?: "AND" | "OR";
  ruleIds?: string[];
  expand?: Array<ExpandEnum>;
};

export const useRulesQuery = (
  params: RulesListParams,
  options?: Omit<
    UseQueryOptions<
      GetRulesPageRuleResponse,
      unknown,
      GetRulesPageRuleResponse,
      any[]
    >,
    "queryKey" | "queryFn"
  >
) => {
  const organizationId = useOrganizationId();

  return useQuery(
    queryKeyFactory.recommendationsV2.ruleList(organizationId, params),
    () =>
      recommendationsClient.client.getRules({
        organizationId,
        expand: params.expand || ["NUMBER_OF_INVOCATIONS"],
        search: params.search,
        isActive: params.isActive,
        activeSince: params.activeSince,
        recommendationAction: params.recommendationAction,
        hasExclusion: params.hasExclusion,
        groupId: params.groupId,
        ruleType: params.ruleType,
        sorting: params.sorting,
        descending: params.descending,
        // @ts-expect-error invalid BE type
        actions: params.actions,
        actionsOperator: params.actionsOperator,
        page: params.page,
        size: params.size,
        ruleIds: params.ruleIds ? params.ruleIds.join(",") : undefined,
      }),
    options
  );
};

export const useRuleQuery = (
  ruleId: string | undefined,
  options: Omit<
    UseQueryOptions<RuleResponse, AxiosError>,
    "queryKey" | "queryFn"
  > = {}
) => {
  const organizationId = useOrganizationId();
  options.enabled = Boolean(ruleId);

  const query = useQuery(
    queryKeyFactory.recommendationsV2.rule(ruleId!),
    () =>
      recommendationsClient.client.getRule({ ruleId: ruleId!, organizationId }),
    options
  );

  return {
    ...query,
    isLoading: query.isLoading && query.fetchStatus !== "idle",
  };
};

export const useRuleInvocationQuery = (ruleId: string, enabled = false) => {
  const organizationId = useOrganizationId();

  return useQuery(
    queryKeyFactory.recommendationsV2.ruleInvocations(ruleId),
    () =>
      recommendationsClient.client.getRuleInvocations({
        organizationId,
        ruleId,
      }),
    { enabled }
  );
};

export const useRecommendationVariablesQuery = (
  enabled = true,
  params?: { ruleIds?: string[]; submissionIds?: string[] }
) => {
  const organizationId = useOrganizationId();
  const allParams = !!params
    ? { ...params, organizationId: organizationId }
    : { organizationId: organizationId };
  return useQuery(
    queryKeyFactory.recommendationsV2.variables(organizationId, params ?? {}),
    () => recommendationsClient.client.getAllVariables(allParams),
    {
      enabled,
      staleTime: Number.POSITIVE_INFINITY,
    }
  );
};

export const useSubmissionRecommendationsQuery = (
  submissionId: string | undefined
) => {
  const organizationId = useOrganizationId();

  return useQuery(
    queryKeyFactory.recommendationsV2.submissionRecommendations(
      submissionId ?? ""
    ),
    () =>
      recommendationsClient.client.getRecommendationForSubmission({
        submissionId: submissionId!,
        organizationId,
      }),

    { enabled: Boolean(submissionId) }
  );
};

export const useDryRunMutation = () =>
  useMutation({
    mutationFn: ({
      ruleId,
      submissionId,
    }: {
      ruleId: string;
      submissionId: string;
    }) =>
      recommendationsClient.client.requestAsyncDryRun({ ruleId, submissionId }),
  });

export const useDryRunQuery = (
  ruleId: string,
  submissionId: string,
  options: Omit<
    UseQueryOptions<
      RecommendationResponse | null,
      AxiosError,
      RecommendationResponse,
      string[]
    >,
    "queryKey" | "queryFn"
  > = {}
) =>
  useQuery(
    queryKeyFactory.recommendationsV2.dryRun(ruleId, submissionId),
    () => recommendationsClient.client.getAsyncDryRun({ ruleId, submissionId }),
    {
      ...options,
      enabled: Boolean(ruleId && submissionId && options.refetchInterval),
    }
  );

export const useUpdateRuleMutation = (
  ruleId: string,
  optimisticUpdate = true
) => {
  const queryClient = useQueryClient();
  const organizationId = useOrganizationId();
  const ruleQueryKey = queryKeyFactory.recommendationsV2.rule(ruleId);

  return useMutation({
    mutationFn: (rule: PatchRuleRequest) =>
      recommendationsClient.client.patchRule({
        ruleId,
        organizationId,
        requestBody: rule,
      }),
    onMutate: async (rule: PatchRuleRequest) => {
      await queryClient.cancelQueries({ queryKey: ruleQueryKey });
      const previousRule = queryClient.getQueryData<RuleResponse>(ruleQueryKey);

      if (previousRule) {
        queryClient.setQueryData(ruleQueryKey, { ...previousRule, ...rule });
      }

      return { previousRule };
    },
    onError: (_, __, context) => {
      if (optimisticUpdate)
        queryClient.setQueryData(ruleQueryKey, context?.previousRule);
    },
    onSettled: (response) => {
      if (!response && !optimisticUpdate) return;

      queryClient.invalidateQueries(
        queryKeyFactory.recommendationsV2.ruleList(organizationId)
      );
      queryClient.invalidateQueries(ruleQueryKey);
    },
  });
};

export const useCreateRuleMutation = () => {
  const router = useRouter();
  const organizationId = useOrganizationId();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (rule: RuleRequest) =>
      recommendationsClient.client.createRule({
        organizationId,
        requestBody: rule,
      }),
    onSuccess: (rule) => {
      queryClient.invalidateQueries(
        queryKeyFactory.recommendationsV2.ruleList(organizationId)
      );
      router.push(routes.portfolioManager.details(rule.id));
    },
  });
};

export const useRuleBulkActionMutation = () => {
  const organizationId = useOrganizationId();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: RulesBulkActionRequest) =>
      recommendationsClient.client.performRulesBulkAction({
        organizationId,
        requestBody: request,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries(
        queryKeyFactory.recommendationsV2.ruleList(organizationId)
      );
    },
  });
};

export const useDuplicateRuleMutation = () => {
  const organizationId = useOrganizationId();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      recommendationsClient.client.duplicateRule({
        organizationId,
        ruleId: id,
        requestBody: { name },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries(
        queryKeyFactory.recommendationsV2.ruleList(organizationId)
      );
    },
  });
};

type GetRulesStatisticsParams = Omit<
  Parameters<typeof recommendationsClient.client.getRulesStatistics>[0],
  "organizationId"
>;

export const useRecommendationStatistics = ({
  expand,
  only,
  options,
}: {
  expand?: Array<StatisticsFieldsEnum>;
  only?: Array<StatisticsFieldsEnum>;
  options?: GetRulesStatisticsParams;
} = {}) => {
  const organizationId = useOrganizationId();

  return useQuery(
    queryKeyFactory.recommendationsV2.rulesStatistics(
      organizationId,
      expand,
      only,
      options
    ),
    () =>
      recommendationsClient.client.getRulesStatistics({
        organizationId,
        expand,
        only,
        ...options,
      }),
    { staleTime: 30000 }
  );
};

export const useSubmissionsRecommendationsBreakdown = () =>
  useQuery(
    queryKeyFactory.recommendationsV2.submissionsRecommendationsBreakdown,
    () => copilotApiClient.getSubmissionsRecommendationsBreakdown(),
    { staleTime: 30000 }
  );

export const useRuleGroupsQuery = () => {
  const organizationId = useOrganizationId();

  return useQuery(
    queryKeyFactory.recommendationsV2.ruleGroups(),
    () => recommendationsClient.client.getRulesGroups({ organizationId }),
    {
      staleTime: Number.POSITIVE_INFINITY,
    }
  );
};

export const useVariableGroupsQuery = () => {
  const organizationId = useOrganizationId();
  return useQuery(
    queryKeyFactory.recommendationsV2.variableGroups(organizationId),
    () =>
      recommendationsClient.client.getGroups({
        organizationId: organizationId,
      }),
    {
      staleTime: 60 * 60 * 1000,
    }
  );
};

export const useRuleGroupCreateMutation = () => {
  const organizationId = useOrganizationId();
  const invalidateRuleGroups = useInvalidateRuleGroups();

  return useMutation({
    mutationFn: (ruleGroup: RuleGroupRequest) =>
      recommendationsClient.client.createRuleGroup({
        organizationId,
        requestBody: ruleGroup,
      }),
    onSettled: async () => {
      await invalidateRuleGroups();
    },
  });
};

export const useRuleGroupUpdateMutation = (groupId: string) => {
  const organizationId = useOrganizationId();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ruleGroup: RuleGroupRequest) =>
      recommendationsClient.client.updateRuleGroup({
        organizationId,
        groupId,
        requestBody: ruleGroup,
      }),
    onSettled: () => {
      queryClient.invalidateQueries(
        queryKeyFactory.recommendationsV2.ruleGroups()
      );
    },
  });
};

export const useRuleGroupDeleteMutation = (groupId: string) => {
  const organizationId = useOrganizationId();
  const invalidateRuleGroups = useInvalidateRuleGroups();

  return useMutation({
    mutationFn: () =>
      recommendationsClient.client.deleteRuleGroup({ organizationId, groupId }),
    onSettled: async () => {
      await invalidateRuleGroups();
    },
  });
};

export const useInvalidateGroupsCount = () => {
  const organizationId = useOrganizationId();
  const queryClient = useQueryClient();

  return useCallback(
    () =>
      queryClient.invalidateQueries(
        queryKeyFactory.recommendationsV2.rulesStatistics(
          organizationId,
          [],
          ["RULE_GROUPS_COUNT"]
        )
      ),
    [queryClient, organizationId]
  );
};

const useInvalidateRuleGroups = () => {
  const queryClient = useQueryClient();
  const invalidateRuleCount = useInvalidateGroupsCount();

  return useCallback(
    () =>
      Promise.all([
        queryClient.invalidateQueries(
          queryKeyFactory.recommendationsV2.ruleGroups()
        ),
        invalidateRuleCount(),
      ]),
    [queryClient, invalidateRuleCount]
  );
};

export const usePortfolioValueQuery = (ruleId: string) => {
  const organizationId = useOrganizationId();

  return useQuery(
    queryKeyFactory.recommendationsV2.rulePortfolioValue(ruleId),
    () =>
      recommendationsClient.client.getRulePortfolioValue({
        ruleId,
        organizationId,
      }),
    { enabled: Boolean(ruleId) }
  );
};

export const usePortfolioValuesQuery = ({
  enabled = true,
  ...params
}: {
  page?: number;
  perPage?: number;
  portfolioRuleType?: PortfolioRuleTypeEnum;
  groupType?: GroupTypeEnum;
  enabled?: boolean;
}) => {
  const organizationId = useOrganizationId();
  return useQuery(
    queryKeyFactory.recommendationsV2.portfolioValues(params),
    () =>
      recommendationsClient.client.getOrganizationPortfolioValues({
        organizationId,
        ...params,
      }),
    {
      keepPreviousData: true,
      staleTime: Number.POSITIVE_INFINITY,
      enabled,
      retry: (failureCount, error) => {
        if (failureCount > 2) {
          return false;
        }

        if (
          axios.isAxiosError(error) &&
          [404].includes(error.response?.status ?? 0)
        ) {
          return false;
        }

        if (error instanceof ApiError && [404].includes(error.status)) {
          return false;
        }

        return true;
      },
    }
  );
};

export const useRecommendationFeedback = (recommendationId?: string) => {
  const organizationId = useOrganizationId();
  return useQuery(
    queryKeyFactory.recommendationsV2.recommendationFeedback(recommendationId),
    async () => {
      try {
        return await recommendationsClient.client.getFeedback({
          recommendationId: recommendationId!,
          organizationId,
        });
      } catch (error) {
        if (error && typeof error === "object" && "status" in error) {
          if (error.status === 404) {
            return null;
          }
        }
        throw error;
      }
    },
    {
      enabled: Boolean(recommendationId),
      staleTime: Number.POSITIVE_INFINITY,
    }
  );
};

export const useRecommendationFeedbackMutation = () => {
  const organizationId = useOrganizationId();
  const queryClient = useQueryClient();
  const snackbar = useSnackbar();
  return useMutation({
    mutationFn: ({
      recommendationId,
      body,
    }: {
      recommendationId: string;
      body: RecommendationFeedbackRequestModel;
    }) =>
      recommendationsClient.client.postFeedback({
        recommendationId,
        organizationId,
        requestBody: body,
      }),
    onMutate: async (params) => {
      queryClient.setQueryData(
        queryKeyFactory.recommendationsV2.recommendationFeedback(
          params.recommendationId
        ),
        params.body
      );
    },
    onSettled: (_, __, params) =>
      queryClient.invalidateQueries(
        queryKeyFactory.recommendationsV2.recommendationFeedback(
          params.recommendationId
        )
      ),
    onSuccess: () => {
      snackbar.enqueueSnackbar({
        variant: "success",
        message: "Thanks for the Feedback!",
      });
    },
    onError: (_, params) => {
      void queryClient.resetQueries(
        queryKeyFactory.recommendationsV2.recommendationFeedback(
          params.recommendationId
        )
      );
    },
  });
};

export const useTriggeredRulesQuery = (params: TriggeredRulesRequest) => {
  return useQuery(
    queryKeyFactory.recommendationsV2.triggeredRules(params),
    () =>
      recommendationsClient.client.getTriggeredRecommendations({
        requestBody: params,
      }),
    {
      enabled: params.submission_ids.length > 0,
    }
  );
};
