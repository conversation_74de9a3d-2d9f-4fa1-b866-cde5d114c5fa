import {
  useMutation,
  useQueries,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { datadogLogs } from "@datadog/browser-logs";
import axios from "axios";
import copilotApiClient from "../src/clients/copilotApiClient";
import { queryKeyFactory } from "./queryKey";
import { ErsEntity } from "../src/models/ErsEntity";
import Report from "@legacy/models/Report";
import { trackSubmissionEdited } from "@utils/amplitude";
import {
  DefaultApiClearSubmissionRequest,
  DefaultApiDeleteSubmissionBusinessRequest,
  DefaultApiDeleteSubmissionIdentifierRequest,
  DefaultApiGetSubmissionHistoryRequest,
  DefaultApiGetWorkersCompExperienceForSubmissionRequest,
  DefaultApiGetWorkersCompStateRatingInfoForSubmissionRequest,
  DefaultApiRevertReportToStateRequest,
  DefaultApiSetSubmissionIdentifiersRequest,
  StuckSubmissionFeedback,
  SubmissionBusiness,
  SubmissionStageEnum,
  UpsertUserSubmissionNotificationsRequest,
} from "@legacy/api_clients/copilot_api_client";
import { useSnackbar } from "notistack";
import {
  AddFileParams,
  addFilesToSubmission,
} from "@legacy/state/actions/files";
import File from "../src/models/File";
import SubmissionBusinessModel from "../src/models/SubmissionBusiness";
import { GroupedFirstPartyFields } from "@legacy/models/types/firstParty";
import { createSubmissionFromResolutionResults } from "@legacy/state/actions/report";
import { useOrganizationId } from "@utils/auth";
import { useMemo } from "react";

type SubmissionUserParams = {
  submissionId: string;
  userId: number;
  reportId: string;
  shouldTrack?: boolean;
};
export const useDeleteSubmissionUser = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (params: SubmissionUserParams) =>
      copilotApiClient.deleteSubmissionUser(params.userId, params.submissionId),
    {
      onError: (error) => {
        datadogLogs.logger.error(String(error));
      },
      onSettled: async (_, __, params) => {
        await queryClient.invalidateQueries(
          queryKeyFactory.submissionUsers(params.submissionId)
        );
        await queryClient.invalidateQueries(
          queryKeyFactory.report(params.reportId)
        );
        await queryClient.invalidateQueries(queryKeyFactory.reports({}));
        const report = queryClient.getQueriesData(
          queryKeyFactory.report(params.reportId)
        )[0]?.[1] as Report | undefined;
        if (report && params.shouldTrack) {
          trackSubmissionEdited(report, {
            item: "assignee",
          });
        }
      },
    }
  );
};

export const useAddSubmissionUser = () => {
  const queryClient = useQueryClient();
  const snackbar = useSnackbar();
  return useMutation(
    async (params: SubmissionUserParams) => {
      try {
        await copilotApiClient.addSubmissionUser(
          params.userId,
          params.submissionId
        );
      } catch (err) {
        // if Conflict is returned it means that user is already assigned
        // and, we don't need to rise an error
        if (axios.isAxiosError(err) && err.response?.status === 409) {
          const isGroupAssignmentError = (err.response?.data as any)?.detail
            ?.toLowerCase()
            .includes("not in the correct group");
          if (isGroupAssignmentError) {
            snackbar.enqueueSnackbar((err.response?.data as any)?.detail, {
              preventDuplicate: true,
              variant: "error",
            });
          } else {
            return;
          }
        }
        throw err;
      }
    },
    {
      onError: (error) => {
        datadogLogs.logger.error(String(error));
      },
      onSettled: async (_, __, params) => {
        await queryClient.invalidateQueries(
          queryKeyFactory.submissionUsers(params.submissionId)
        );
        await queryClient.invalidateQueries(
          queryKeyFactory.report(params.reportId)
        );
        await queryClient.invalidateQueries(queryKeyFactory.reports({}));
        const report = queryClient.getQueriesData(
          queryKeyFactory.report(params.reportId)
        )[0]?.[1] as Report | undefined;
        if (report && params.shouldTrack) {
          trackSubmissionEdited(report, {
            item: "assignee",
          });
        }
      },
    }
  );
};

export const useDeleteClientSubmissionId = (
  submissionId: string,
  reportId: string
) => {
  const queryClient = useQueryClient();
  const queryKey = queryKeyFactory.clientSubmissionIds(submissionId);

  return useMutation(
    (clientSubmissionId: string) =>
      copilotApiClient.deleteClientSubmissionId(
        clientSubmissionId,
        submissionId
      ),
    {
      onError: (error) => {
        datadogLogs.logger.error(String(error));
      },
      onSettled: async () => {
        await Promise.all([
          queryClient.invalidateQueries(queryKey),
          queryClient.invalidateQueries(queryKeyFactory.report(reportId)),
        ]);
      },
    }
  );
};

export const useAddClientSubmissionIds = (
  submissionId: string,
  reportId: string
) => {
  const snackbar = useSnackbar();
  const queryClient = useQueryClient();
  const queryKey = queryKeyFactory.clientSubmissionIds(submissionId);

  return useMutation(
    (clientSubmissionIds: string[]) =>
      copilotApiClient.bulkAddClientSubmissionIds({
        submissionClientId: clientSubmissionIds.map((cid) => {
          return {
            submission_id: submissionId,
            client_submission_id: cid,
            source: "MANUAL",
          };
        }),
        submissionId,
      }),
    {
      onError: (error) => {
        datadogLogs.logger.error(String(error));
        if (axios.isAxiosError(error) && error.response?.status === 409) {
          snackbar.enqueueSnackbar(
            "The client ID already exists on a different submission.",
            { preventDuplicate: true, variant: "error" }
          );
        }
        if (axios.isAxiosError(error) && error.response?.status === 400) {
          snackbar.enqueueSnackbar((error.response?.data as any)?.detail, {
            preventDuplicate: true,
            variant: "error",
          });
        }
      },
      onSettled: async () => {
        await Promise.all([
          queryClient.invalidateQueries(queryKey),
          queryClient.invalidateQueries(queryKeyFactory.report(reportId)),
        ]);
      },
    }
  );
};

export const useSubmissionBusinessTenants = (
  submissionId?: string | null,
  businessId?: string | null
) => {
  const queryKey = queryKeyFactory.submissionBusinessTenants(
    submissionId ?? "",
    businessId ?? ""
  );
  const query = useQuery(
    queryKey,
    () =>
      copilotApiClient.getSubmissionBusinessTenants({
        submissionId: submissionId!,
        id: businessId!,
      }),
    {
      enabled: Boolean(submissionId && businessId),
      select(data) {
        return data.data.entities?.map((entity) => new ErsEntity(entity)) ?? [];
      },
    }
  );
  return {
    ...query,
    isLoading: query.isLoading && query.fetchStatus !== "idle",
  };
};

export const useSubmissionVerificationMutation = (
  submissionId: string,
  reportId: string
) => {
  const queryClient = useQueryClient();
  return useMutation(
    ({
      force,
      verifyForUserId,
    }: {
      force?: boolean;
      verifyForUserId?: number;
    }) =>
      copilotApiClient.verifySubmission({
        submissionId,
        body: {},
        force,
        verifyForUserId,
      }),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(queryKeyFactory.report(reportId));
        await queryClient.invalidateQueries(
          queryKeyFactory.currentSupportUser()
        );
      },
    }
  );
};

export const useCreateStuckSubmissionFeedbackMutation = () => {
  return useMutation((stuckSubmissionFeedback: StuckSubmissionFeedback) =>
    copilotApiClient.createStuckSubmissionFeedback({
      stuckSubmissionFeedback: stuckSubmissionFeedback,
    })
  );
};

type UpdateSubmissionBusinessParams = {
  submissionId: string;
  reportId: string;
  id: string;
  submissionBusiness: SubmissionBusiness;
};
export const useUpdateSubmissionBusinessMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    ({
      id,
      submissionBusiness,
      submissionId,
    }: UpdateSubmissionBusinessParams) =>
      copilotApiClient.updateSubmissionBusiness(
        submissionId,
        id,
        submissionBusiness
      ),
    {
      onSettled: async (_, __, { reportId, submissionId }) => {
        await queryClient.invalidateQueries(queryKeyFactory.report(reportId));
        await queryClient.invalidateQueries(
          queryKeyFactory.submissionBusinesses(submissionId)
        );
      },
    }
  );
};

export const useDeleteSubmissionBusinessMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (
      request: DefaultApiDeleteSubmissionBusinessRequest & { reportId: string }
    ) => copilotApiClient.deleteSubmissionBusiness(request),
    {
      onSettled: (_, __, { reportId }) => {
        queryClient.invalidateQueries(queryKeyFactory.report(reportId));
      },
    }
  );
};

export const useClearSubmissionMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (request: DefaultApiClearSubmissionRequest & { reportId: string }) =>
      copilotApiClient.clearSubmission(request),
    {
      onSettled: async (_, __, { reportId }) => {
        await Promise.all([
          queryClient.invalidateQueries(queryKeyFactory.report(reportId)),
          queryClient.invalidateQueries(queryKeyFactory.reports({})),
        ]);
      },
    }
  );
};

export const useGetWorkersCompStateRatingQuery = (
  requestParameters: DefaultApiGetWorkersCompStateRatingInfoForSubmissionRequest
) => {
  return useQuery(
    queryKeyFactory.workersCompStateRating(requestParameters),
    () => copilotApiClient.getWorkersCompStateRating(requestParameters),
    {
      enabled: Boolean(requestParameters.submissionId),
    }
  );
};

export const useGetWorkersCompExperienceForSubmissionQuery = (
  requestParameters: DefaultApiGetWorkersCompExperienceForSubmissionRequest
) => {
  const query = useQuery(
    queryKeyFactory.workersCompStateExperience(requestParameters),
    () =>
      copilotApiClient.getWorkersCompExperienceForSubmission(requestParameters),
    {
      enabled: Boolean(requestParameters.submissionId),
      staleTime: Number.POSITIVE_INFINITY,
    }
  );
  return {
    ...query,
    isLoading: query.isLoading && query.fetchStatus !== "idle",
  };
};

export const useGetClassCodesQuery = () => {
  return useQuery(queryKeyFactory.classCodes, () =>
    copilotApiClient.getClassCodes()
  );
};

export const useReprocessSubmissionMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (reportId: string) => copilotApiClient.reprocessSubmission(reportId),
    {
      onSettled: async (_, __, reportId) => {
        await queryClient.invalidateQueries(queryKeyFactory.report(reportId));
      },
    }
  );
};

export const useRevertReportToStateMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (params: DefaultApiRevertReportToStateRequest) =>
      copilotApiClient.revertReportToState(params),
    {
      onSettled: async (_, __, params) => {
        await queryClient.invalidateQueries(queryKeyFactory.report(params.id));
      },
    }
  );
};

export const useCancelPdsMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (reportId: string) => copilotApiClient.cancelPds(reportId),
    {
      onSettled: async (_, __, reportId) => {
        await queryClient.invalidateQueries(queryKeyFactory.report(reportId));
      },
    }
  );
};

export const useReadSubmissionMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (submissionId: string) => copilotApiClient.readSubmission(submissionId),
    {
      onSettled: async () => {
        await Promise.all([
          queryClient.invalidateQueries(["report"]),
          queryClient.invalidateQueries(queryKeyFactory.reports({})),
        ]);
      },
    }
  );
};

export const useUnreadSubmissionMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (submissionId: string) => copilotApiClient.unreadSubmission(submissionId),
    {
      onSettled: async () => {
        await Promise.all([
          queryClient.invalidateQueries(["report"]),
          queryClient.invalidateQueries(queryKeyFactory.reports({})),
        ]);
      },
    }
  );
};

export const useGetUsersSubmissionsNotificationsQuery = (
  submissionId: string
) => {
  return useQuery(
    queryKeyFactory.userSubmissionsNotifications(submissionId),
    () => copilotApiClient.getUsersSubmissionNotifications(submissionId),
    {
      enabled: Boolean(submissionId),
    }
  );
};

export const useUpsertUsersSubmissionsNotificationsMutation = (
  submissionId: string
) => {
  const queryClient = useQueryClient();
  return useMutation(
    (request: UpsertUserSubmissionNotificationsRequest) =>
      copilotApiClient.upsertUsersSubmissionNotifications(
        submissionId,
        request
      ),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(
          queryKeyFactory.userSubmissionsNotifications(submissionId)
        );
      },
    }
  );
};

export const useGetSubmissionReportPublicInfoQuery = (
  submissionId: string | undefined
) => {
  const organizationId = useOrganizationId();

  return useQuery(
    queryKeyFactory.submissionReportPublicInfo(submissionId),
    () =>
      copilotApiClient.getSubmissionReportPublicInfo(
        submissionId!,
        organizationId
      ),
    {
      enabled: Boolean(submissionId),
    }
  );
};

export const useGetSubmissionRelations = (submissionId: string) => {
  return useQuery(
    queryKeyFactory.submissionRelations(submissionId),
    () => copilotApiClient.getSubmissionRelations(submissionId),
    {
      enabled: Boolean(submissionId),
      staleTime: Number.POSITIVE_INFINITY,
    }
  );
};

export const useSubmissionHistory = (
  params: DefaultApiGetSubmissionHistoryRequest
) => {
  const query = useQuery(
    queryKeyFactory.submissionHistory(params),
    () => copilotApiClient.getSubmissionHistory(params),
    {
      enabled: Boolean(params.submissionId),
    }
  );
  return {
    ...query,
    isLoading: query.isLoading && query.fetchStatus !== "idle",
  };
};

export const useSubmissionCoverageHistory = (
  submissionId: string,
  cacheKey?: string
) => {
  return useQuery(
    cacheKey
      ? [cacheKey]
      : queryKeyFactory.submissionCoverageHistory(submissionId),
    () => copilotApiClient.getSubmissionCoverageHistory({ submissionId })
  );
};

export const useGetSubmissionShareholders = (submissionId: string) => {
  const query = useQuery(
    queryKeyFactory.submissionShareholders(submissionId),
    () => copilotApiClient.getSubmissionShareholders(submissionId),
    {
      enabled: Boolean(submissionId),
    }
  );
  return {
    ...query,
    isLoading: query.isLoading && query.fetchStatus !== "idle",
  };
};

export const useGetClientSubmissionStageConfig = () => {
  return useQuery(
    queryKeyFactory.submissionStageConfig,
    () => copilotApiClient.getClientSubmissionStageConfig(),
    {
      staleTime: 1000 * 60 * 60 * 12,
    }
  );
};

export const useAddFilesToSubmissions = () => {
  return useMutation((params: AddFileParams) => addFilesToSubmission(params));
};

export const useCreateSubmissionFromResolutionResults = () => {
  return useMutation(
    ({
      userId,
      name,
      resolutionResults,
      callback,
      groupedFirstPartyFields,
      file,
      fields,
      reportId,
    }: {
      userId: number;
      name: string;
      resolutionResults: SubmissionBusinessModel[];
      callback?: (report: Report) => void;
      groupedFirstPartyFields?: GroupedFirstPartyFields;
      file?: File | null;
      fields?: ReadonlyArray<Record<string, string>>;
      reportId?: string;
    }) =>
      createSubmissionFromResolutionResults({
        userId,
        name,
        resolutionResults,
        callback,
        groupedFirstPartyFields,
        file,
        fields,
        reportId,
      })
  );
};

export const useSetSubmissionIdentifiers = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (request: DefaultApiSetSubmissionIdentifiersRequest) =>
      copilotApiClient.setSubmissionIdentifiers(request),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(queryKeyFactory.report());
      },
    }
  );
};

export const useDeleteSubmissionIdentifier = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (request: DefaultApiDeleteSubmissionIdentifierRequest) =>
      copilotApiClient.deleteSubmissionIdentifier(request),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(queryKeyFactory.report());
      },
    }
  );
};

export const useCloseSubmissionReferralMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (referralId: string) => copilotApiClient.closeReferral(referralId),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(queryKeyFactory.report());
      },
    }
  );
};

export const useGetDescriptionsOfOperationsQuery = (submissionId: string) => {
  return useQuery(
    queryKeyFactory.submissionDescriptionsOfOperations(submissionId),
    () => copilotApiClient.getDescriptionsOfOperations({ submissionId })
  );
};

export const useCompleteSubmissionMutation = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (submissionId: string) =>
      copilotApiClient.completeSubmission({ submissionId }),

    {
      onSettled: async () => {
        await queryClient.invalidateQueries(queryKeyFactory.report());
      },
    }
  );
};

export const useGetBulkPrometrixRisks = (
  submissionId: string,
  submissionBusinessIds: string[]
) => {
  const res = useQueries({
    queries: submissionBusinessIds.map((id) => ({
      queryKey: queryKeyFactory.prometrixRisks({ submissionId, id }), // Ensure queryKey is an array
      queryFn: () => copilotApiClient.getPrometrixRisks({ submissionId, id }),
    })),
  });

  return useMemo(() => {
    return {
      isLoading: res.some((r) => r.isLoading),
      data: res
        .map((r, i) => ({
          submissionBusinessId: submissionBusinessIds[i],
          risks: r.data?.risks,
        }))
        .filter((r) => r.risks?.length),
    };
  }, [res, submissionBusinessIds]);
};

export const useGetSubmissionBusinesses = (
  submissionId: string,
  submissionStage: SubmissionStageEnum,
  organizationId: number
) => {
  return useQuery(
    queryKeyFactory.submissionBusinesses(submissionId),
    () =>
      copilotApiClient.getBusinessesBySubmission(
        submissionId,
        submissionStage,
        organizationId
      ),
    {
      enabled: Boolean(submissionId),
    }
  );
};
