import { useQuery } from "@tanstack/react-query";
import copilotApiClient from "../src/clients/copilotApiClient";
import { queryKeyFactory } from "./queryKey";

export const useSearchQuery = (
  query?: string | null,
  filterQuery?: string | null
) => {
  const result_query = useQuery(
    queryKeyFactory.cloudSearch(query ?? "", filterQuery ?? ""),
    () => copilotApiClient.search(query!, 100, filterQuery ?? undefined),
    {
      enabled: !!query && query.length > 1,
      staleTime: 5 * 60 * 1_000,
    }
  );
  return {
    ...result_query,
    isLoading: result_query.isLoading && result_query.fetchStatus !== "idle",
  };
};
