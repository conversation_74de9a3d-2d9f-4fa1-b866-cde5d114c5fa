import {
  DefaultApiGetAggregatedCustomFileTypesRequest,
  DefaultApiGetAggregatedFileTypesRequest,
  DefaultApiGetCustomFileTypesForOrgRequest,
  DefaultApiGetEnhancedFileByFileIdRequest,
  DefaultApiGetExperimentRunsRequest,
  DefaultApiGetFactSubtypeSelectionBenchmarkRequest,
  DefaultApiGetFileOnboardedDataRequest,
  DefaultApiGetFilesQueueRequest,
  DefaultApiGetMetricsV2Request,
  DefaultApiGetPreferenceRequest,
  DefaultApiGetPresignedUrlRequest,
  DefaultApiGetPrometrixRisksForSubmissionBusinessRequest,
  DefaultApiGetSubmissionHistoryRequest,
  DefaultApiGetTaxonomyMappingsRequest,
  DefaultApiGetWorkersCompExperienceForSubmissionRequest,
  DefaultApiGetWorkersCompStateRatingInfoForSubmissionRequest,
  EmailTemplateTypeEnum,
  SubmissionSendEmailRequest,
} from "@legacy/api_clients/copilot_api_client";
import { PARENT_TYPES } from "@legacy/constants/facts";
import { UseLossesQueryProps } from "@queries/losses";
import {
  type GroupTypeEnum,
  StatisticsFieldsEnum,
  TriggeredRulesRequest,
} from "@legacy/api_clients/recommendations-client";
import {
  DefaultApiGetAggregatedDocumentsCountsRequest,
  DefaultApiGetAggregatedDocumentsCountsWithPostRequest,
  DefaultApiGetDocumentCountsRequest,
  DefaultApiGetDocumentCountsWithPostRequest,
  DefaultApiGetDocumentRequest,
  DefaultApiGetExplanationsForObservationRequest,
  DefaultApiGetFactsWithConflictingObservationsWithPostRequest,
  DefaultApiGetFeedbacksRequest,
  DefaultApiGetGroupsRequest,
  DefaultApiGetMyExplanationFeedbackRequest,
  FullMetadataRequest,
  ParentType,
} from "@legacy/api_clients/facts_api_client";
import {
  ChartDimensionRequest,
  QueryDataRequest,
  QueryFilter,
} from "@legacy/api_clients/redshift-api-client";
import { AffectedReportsParams } from "./types";
import {
  DefaultApiGetEventsByBboxRequest,
  DefaultApiGetZonesByBboxRequest,
} from "@legacy/api_clients/entity_resolution_service_client_v3";

export const queryKeyFactory = {
  agencies: (organizationId: number) => ["agencies", organizationId],
  brokerages: (organizationId: number) => ["brokerages", organizationId],
  brokerGroups: (organizationId: number) => ["brokerGroups", organizationId],
  filesQueue: (request?: DefaultApiGetFilesQueueRequest) =>
    ["filesQueue", request?.page, request?.perPage].filter(Boolean),
  brokerageOffices: () => ["brokerageOffices"],
  classificationLabels: ["classificationLabels"],
  geoEventsByBbox: (
    { bbox, ...params }: DefaultApiGetEventsByBboxRequest,
    mode?: "weather" | "non-weather"
  ) => ["geoEventsByBbox", bbox, params, mode],
  geoZonesByBbox: ({ bbox, ...params }: DefaultApiGetZonesByBboxRequest) => [
    "geoZonesByBbox",
    bbox,
    params,
  ],
  prometrixRisks: (
    request: DefaultApiGetPrometrixRisksForSubmissionBusinessRequest
  ) => ["prometrixRisks", request],
  emailClassifiers: ["emailClassifiers"],
  submissionBusinesses: (submissionId: string) => [
    "submission",
    submissionId,
    "businesses",
  ],
  submissionEmailDynamicData: (submissionId: string) => [
    "submission",
    submissionId,
    "emailDynamicData",
  ],
  fileUrlByFileId: (fileId: string) => ["fileUrlByFileId", fileId],
  entityRelationsGraph: (entityId: string | string[]) => [
    "entityRelationsGraph",
    entityId,
  ],
  submissionRelations: (submissionId: string) => [
    "submission",
    submissionId,
    "relations",
  ],
  submissionHistory: ({
    submissionId,
    ...other
  }: DefaultApiGetSubmissionHistoryRequest) => [
    "submission",
    submissionId,
    other,
  ],
  submissionCoverageHistory: (submissionId: string) => [
    "submission-coverage-history",
    submissionId,
  ],
  submissionShareholders: (submissionId: string) => [
    "submission",
    submissionId,
    "shareholders",
  ],
  submissionDescriptionsOfOperations: (submissionId: string) => [
    "submission",
    submissionId,
    "descriptionsOfOperations",
  ],
  submissionFilesData: (submissionId: string) => [
    "submission",
    submissionId,
    "filesData",
  ],
  submissionStageConfig: ["submission-stage-config"],
  submissionReportPublicInfo: (submissionId?: string) => [
    "submission",
    submissionId,
  ],
  brokerageEmployess: (
    organizationId?: number,
    brokerageId?: string,
    role?: string
  ) =>
    ["brokerageEmployess", organizationId, brokerageId, role].filter(Boolean),
  auditQuestions: (submissionId: string) => ["auditQuestions", submissionId],
  clientApplications: (userId: number) => ["clientApplications", userId],
  factSubtype: (id?: string) => ["factSubtype", id],
  factSubtypesForFieldName: (fieldNames: string[], parentType: string) => [
    "factSubtypeForFieldName",
    fieldNames,
    parentType,
  ],
  partialFactSubtypes: () => ["partialFactSubtypes"],
  factGroups: (params: DefaultApiGetGroupsRequest) => ["factGroups", params],
  sources: (ids?: string[]) => ["sources", ids],
  hubTemplates: (userId: number, templateType?: "HUB" | "DASHBOARD") => [
    "hubTemplates",
    userId,
    templateType,
  ],
  timezones: () => ["timezones"],
  supportEmail: (userId: number) => ["supportEmail", userId],
  lobs: () => ["lobs"],
  organizations: () => ["organizations"],
  sicCodes: (orgId: number) => ["sicCodes", orgId],
  organizationById: (orgId?: number) => ["organizationById", orgId],
  threads: (submissionId: string) => ["notebook", "threads", submissionId],
  metrics: (reportId: string) => ["metrics", reportId],
  metricsV2: (params: DefaultApiGetMetricsV2Request) => ["metricsV2", params],
  metricPreferences: (reportId: string) => ["metricPreferences", reportId],
  metricTemplates: (userId: number) => ["metricTemplates", userId],
  settings: () => ["settings"],
  naicsCode: (code: string) => ["naicsCode", code],
  dashboardURL: (name: string) => ["dashboardURL", name],
  openReports: (userId: number) => ["open-reports", userId],
  getReportExport: (reportId?: string) => ["getReportExport", reportId],
  userSubmissionsNotifications: (submissionId?: string) =>
    ["userSubmissionsNotifications", submissionId].filter(Boolean),
  document: (documentId?: string, params?: DefaultApiGetDocumentRequest) => [
    "documents",
    documentId,
    params,
  ],
  aggregatedDocumentsCounts: ({
    parentIds,
    ...rest
  }:
    | DefaultApiGetAggregatedDocumentsCountsRequest
    | DefaultApiGetAggregatedDocumentsCountsWithPostRequest) => [
    "aggregatedDocumentsCounts",
    parentIds,
    rest,
  ],
  documentCounts: ({
    parentIds,
    ...rest
  }:
    | DefaultApiGetDocumentCountsRequest
    | DefaultApiGetDocumentCountsWithPostRequest) => [
    "documentCounts",
    parentIds,
    rest,
  ],
  documentCountsAll: () => ["documentCounts"],
  factsWithConflictingObservations: (
    request: DefaultApiGetFactsWithConflictingObservationsWithPostRequest
  ) => ["facts", "conflicting", request] as string[],
  documents: (
    parentIds: string[],
    documentTypes: string[],
    page?: number,
    pageSize?: number,
    expand?: string[],
    searchPhrase?: string,
    orderBy?: string,
    descending?: string,
    publishedAfter?: string,
    onlyPositiveFeedback?: string,
    allowPublishedAtNulls?: string,
    newsCategory?: string,
    legalFilingRole?: string
  ) =>
    [
      "documents",
      parentIds,
      documentTypes,
      page,
      pageSize,
      expand,
      searchPhrase,
      orderBy,
      descending,
      publishedAfter,
      onlyPositiveFeedback,
      newsCategory,
      allowPublishedAtNulls,
      legalFilingRole,
    ].filter(Boolean),
  documentsAll: () => ["documents"],
  documentFeedbacks: (params?: DefaultApiGetFeedbacksRequest) => [
    "document-feedbacks",
    params?.id,
    params,
  ],
  bulkGetEntities: (entityIds: string[]) => ["bulkGetEntities", entityIds],
  report: (id?: string, expand?: string) =>
    ["report", id, expand ?? ""].filter(Boolean),
  notes: (submissionId?: string) => ["notes", submissionId],
  allReportsSearch: () => ["reports", "search"],
  reports: (querySearchParams: Record<string, any>) => [
    "reports",
    "search",
    querySearchParams,
  ],
  affectedReports: (submissionIds: string[], params: AffectedReportsParams) => [
    "affectedReports",
    submissionIds,
    params,
  ],
  shadow: (reportId: string) => ["shadow", reportId],
  rushReportEvidence: (reportId: string) => ["rushReportEvidence", reportId],
  resolutionIdentifiers: ["resolutionIdentifiers"],
  allReportSearchInfinite: () => ["reports", "search", "infinite"],
  reportsInfinite: (querySearchParams: Record<string, any>) => [
    "reports",
    "search",
    "infinite",
    querySearchParams,
  ],
  reportEmails: (reportId: string) => ["reportEmails", reportId],
  reportsBySubmissionId: (submissionId?: string) => [
    "submission",
    "reports-by-submission",
    submissionId,
  ],
  workersComp: (submissionId: string) => [
    "submission",
    "workers-comp",
    submissionId,
  ],
  workersCompStateRating: (
    requestParameters: DefaultApiGetWorkersCompStateRatingInfoForSubmissionRequest
  ) => ["submission", "workers-comp-state-rating", requestParameters],
  workersCompStateExperienceInvalidation: (submissionId?: string | null) =>
    ["submission", "workers-comp-state-experience", submissionId].filter(
      Boolean
    ),
  workersCompStateExperience: (
    requestParameters: DefaultApiGetWorkersCompExperienceForSubmissionRequest
  ) => [
    "submission",
    "workers-comp-state-experience",
    requestParameters.submissionId,
    requestParameters,
  ],
  classCodes: ["class-codes"],
  classifier: (classifierId: string) => ["classifier", classifierId],
  customizableClassifiers: (orgId: number, factSubtypeId: string) => [
    "classification-task",
    orgId,
    factSubtypeId,
  ],
  testRuns: (classifierId: string) => ["test-runs", classifierId],
  displaySubtypeFeedbacks: (submissionId: string) => [
    "display-subtype-feedbacks",
    submissionId,
  ],
  feedback: (params: DefaultApiGetMyExplanationFeedbackRequest) => [
    "feedback",
    params,
  ],
  executionEvents: (submissionId: string) => ["execution-events", submissionId],
  crashReports: (parentIds: ReadonlyArray<string>) => [
    "documents",
    "crashReports",
    parentIds,
  ],
  file: (submissionId: string, attachmentId: string) => [
    "file",
    submissionId,
    attachmentId,
  ],
  enhancedFile: (params: DefaultApiGetEnhancedFileByFileIdRequest) => [
    "enhanced-file",
    params,
  ],
  fileByParent: (submissionId: string, attachmentId: string) => [
    "fileByParent",
    submissionId,
    attachmentId,
  ],
  submissionBusinessResolutionData: (
    submissionId: string,
    fileProcessingState: string
  ) => [
    "submission",
    submissionId,
    "businessResolutionData",
    fileProcessingState,
  ],
  firstPartyFieldGroupSuggestions: (submissionId: string) => [
    "submission",
    submissionId,
    "first-party-field-group-suggestions",
  ],
  fileTypes: () => ["file-types"],
  fileStats: (organizationId: number, createdAfter?: string) => [
    "file-stats",
    organizationId,
    createdAfter,
  ],
  filesPresignedUrl: (params?: DefaultApiGetPresignedUrlRequest) => [
    "presignedUrls",
    params?.presignedUrlRequest?.submission_id,
    params,
  ],
  units: () => ["units"],
  processedFile: (fileId: string) => ["processedFile", fileId],
  onboardedFile: (submissionId: string, fileId: string) => [
    "onboardedFile",
    submissionId,
    fileId,
  ],
  entityMapping: (submissionId: string) => ["entityMapping", submissionId],
  onboardedData: (submissionId: string) => ["onboardedData", submissionId],
  routingRules: ["routingRules"],
  relatedFacts: (
    submissionId: string,
    factSubtypeId: string,
    discoveredIn?: Array<
      "SOV" | "SOV_PDF" | "FMCSA_CRASH" | "FMCSA_INSPECTION"
    >,
    fileIds?: string[]
  ) => [
    "related-facts",
    { submissionId, factSubtypeId, discoveredIn, fileIds },
  ],
  facts: (
    reportId: string,
    group?: string | string[] | null,
    factSubtypeIds?: string[] | null,
    submissionId?: string,
    parentIds?: string[],
    parentType?: PARENT_TYPES,
    organisationId?: number,
    expand?: string[]
  ) =>
    [
      "facts",
      reportId,
      organisationId,
      group,
      factSubtypeIds,
      submissionId,
      parentIds,
      parentType,
      expand,
    ].filter((item) => item !== undefined),
  factCount: (
    parentIds?: string | string[],
    parentType?: ParentType,
    groups?: string | string[] | null,
    factSubtypeIds?: string[] | null,
    submissionId?: string,
    sourceTypeId?: string
  ) =>
    [
      "facts",
      "count",
      parentIds,
      parentType,
      groups,
      factSubtypeIds,
      submissionId,
      sourceTypeId,
    ].filter((item) => item !== undefined),
  observations: (
    parentId?: string,
    parentType?: string,
    factSubtypeId?: string,
    factSubtypeIds?: string[],
    other?: Record<string, any>
  ) =>
    [
      "facts",
      "observations",
      parentId,
      parentType,
      factSubtypeIds,
      other,
    ].filter(Boolean),
  observation: (observationId?: string | null) => [
    "facts",
    "observation",
    observationId,
  ],
  observationExplanations: (
    observationId: string,
    params?: DefaultApiGetExplanationsForObservationRequest
  ) => ["observations", observationId, "explanations", params].filter(Boolean),
  explanationsSummary: (observationId?: string) => [
    "observations",
    observationId,
    "explanationsSummary",
  ],
  modes: () => ["modes"],
  losses: (useLossesQueryProps: UseLossesQueryProps) => [
    "losses",
    useLossesQueryProps,
  ],
  lossPolicies: (submissionId: string) => ["lossPolicies", submissionId],
  lossFileStatuses: (submissionId: string) => [
    "lossFileStatuses",
    submissionId,
  ],
  lossesSummary: (submissionId: string) => ["lossesSummary", submissionId],
  iftaData: (fileId: string) => ["iftaData", fileId],
  iftaAggregation: (
    submissionId: string,
    quarter?: number,
    year?: number,
    fileId?: string
  ) => ["iftaAggregation", submissionId, quarter, year, fileId],
  iftaAggregationQuarters: (submissionId: string) => [
    "iftaAggregationQuarters",
    submissionId,
  ],
  currentUser: (isImpersonating?: boolean) =>
    isImpersonating === undefined
      ? ["auth", "currentUser"]
      : ["auth", "currentUser", isImpersonating],
  knockToken: () => ["auth", "knockToken"],
  userSignup: () => ["userSignup"],
  coverages: (organizationId: number, includeDisabled: boolean) => [
    "coverages",
    organizationId,
    includeDisabled,
  ],
  assignees: (organizationId: number, getAllAssigned?: boolean) => [
    "assignees",
    organizationId,
    getAllAssigned,
  ],
  newCustomizableClassifiers: (
    organizationId: number,
    factSubtypeId?: string
  ) => ["customizableClassifiers", organizationId, factSubtypeId],
  submissionUsers: (submissionId: string) => ["submissionUser", submissionId],
  clientSubmissionIds: (submissionId: string) => [
    "clientSubmissionIds",
    submissionId,
  ],
  organizationUsers: (
    organizationId: number,
    includeSupport = false,
    expand?: string[],
    getAllAssigned?: boolean
  ) => [
    "organizationUsers",
    organizationId,
    includeSupport,
    expand,
    getAllAssigned,
  ],
  adminGetUsers: (organizationId: number, excludeCrossOrgUsers: boolean) => [
    "adminUsers",
    organizationId,
    excludeCrossOrgUsers,
  ],
  getUserGroups: (organizationId: number) => ["userGroups", organizationId],
  cloudSearch: (query: string, filterQuery: string) => [
    "cloudSearch",
    query,
    filterQuery,
  ],
  submissionBusinessTenants: (submissionId: string, businessId: string) => [
    "submission",
    submissionId,
    "business",
    businessId,
    "tenants",
  ],
  userEmailTemplates: (userId: number, exclude?: EmailTemplateTypeEnum[]) =>
    ["userEmailTemplates", userId, exclude].filter(Boolean),
  submissionDeclinedEmail: (
    submissionId: string,
    userId: number,
    params?: SubmissionSendEmailRequest
  ) =>
    ["submissionDeclinedEmail", submissionId, userId, params].filter(Boolean),
  submissionEmail: (
    submissionId: string,
    userId: number,
    params: SubmissionSendEmailRequest
  ) => ["sendEmail", submissionId, userId, params].filter(Boolean),
  images: (parentIds: string[], perPage?: number) => [
    "images",
    { parentIds, perPage },
  ],
  classificationTasks: () => ["classificationTask"],
  processesStatus: (submissionId: string) => ["processesStatus", submissionId],
  stuckDetails: (submissionId?: string) => ["stuckDetails", submissionId],
  supportUsers: () => ["supportUsers"],
  currentSupportUser: () => ["currentSupportUser"],
  submissionsQueueCount: () => ["submissionsQueueCount"],
  submissionsQueue: (params?: any) =>
    ["submissionsQueue", params].filter(Boolean),
  submissionsNaicsQueue: (params?: any) =>
    ["submissionsNaicsQueue", params].filter(Boolean),
  executeRater: (body: any) => ["executeRater", body],
  recommendationsV2: {
    ruleList: (organizationId: number, params?: any) =>
      ["recommendations", "rules", "list", organizationId, params].filter(
        Boolean
      ),
    rule: (ruleId: string) => ["recommendations", "rules", "item", ruleId],
    ruleInvocations: (ruleId: string) => [
      "recommendations",
      "rules",
      "item",
      ruleId,
      "invocations",
    ],
    variables: (
      organizationId: number,
      params: { ruleIds?: string[]; submissionIds?: string[] }
    ) => ["recommendations", "variables", organizationId, params],
    recommendationsList: (organizationId: number) => [
      "recommendations",
      "list",
      organizationId,
    ],
    submissionRecommendations: (submissionId: string) => [
      "recommendations",
      "item",
      { submissionId },
    ],
    aggregations: () => ["recommendations", "aggregations"],
    dryRun: (ruleId: string, submissionId: string) => [
      "recommendations",
      "dryRun",
      ruleId,
      submissionId,
    ],
    rulesStatistics: (
      organizationId: number,
      expand: ReadonlyArray<StatisticsFieldsEnum> = [],
      only: ReadonlyArray<StatisticsFieldsEnum> = [],
      params?: any
    ) => [
      "recommendations",
      "statistics",
      organizationId,
      expand,
      only,
      params,
    ],
    ruleGroups: () => ["recommendations", "ruleGroups"],
    variableGroups: (organizationId: number) => [
      "recommendations",
      "variableGroups",
      organizationId,
    ],
    submissionsRecommendationsBreakdown: [
      "recommendations",
      "submissionsRecommendationsBreakdown",
    ],
    rulePortfolioValue: (ruleId: string) => [
      "recommendations",
      "rulePortfolioValue",
      ruleId,
    ],
    portfolioValues: (params: {
      page?: number;
      perPage?: number;
      groupType?: GroupTypeEnum;
    }) => ["recommendations", "portfolioValues", params],
    recommendationFeedback: (recommendationId?: string) => [
      "recommendations",
      recommendationId,
      "feedback",
    ],
    triggeredRules: (params: TriggeredRulesRequest) => [
      "recommendations",
      "triggered-rules",
      params,
    ],
  },
  dashboards: {
    list: (organizationId: number, userId: number) => [
      "dashboards",
      "list",
      organizationId,
      userId,
    ],
    details: (dashboardId: string) => ["dashboards", "details", dashboardId],
    chartData: (
      chartId: string,
      filters: QueryFilter[],
      dimensions: ChartDimensionRequest[]
    ) => [
      "dashboards",
      "details",
      "chart",
      chartId,
      "data",
      filters,
      dimensions,
    ],
    query: (queryDataRequest: QueryDataRequest | Record<string, never>) => [
      "dashboards",
      "query",
      queryDataRequest,
    ],
    tableQuery: (
      queryDataRequest: QueryDataRequest | Record<string, never>
    ) => ["dashboards", "tableQuery", queryDataRequest],
  },
  experiments: {
    all: () => ["experiments", "all"],
    data: (expIdOrName: string, submissionId: string) => [
      "experiments",
      "data",
      expIdOrName,
      submissionId,
    ],
    runs: (requestParameters: DefaultApiGetExperimentRunsRequest) => [
      "experiments",
      "runs",
      requestParameters,
    ],
  },
  lastSuccessfulProcessExecution: (submissionId: string) => [
    "lastSuccessfulProcessExecution",
    submissionId,
  ],
  lobTypes: () => ["lobTypes"],
  preferences: (params: DefaultApiGetPreferenceRequest) => [
    "preference",
    params,
  ],
  fileOnboardedData: (params: DefaultApiGetFileOnboardedDataRequest) => [
    "file-onboarded-data",
    params.submissionId,
    params.fileId,
  ],
  customFileTypes: (params: DefaultApiGetCustomFileTypesForOrgRequest) => [
    "customFileTypes",
    params.organizationId,
    params,
  ],
  aggregatedFileTypes: (params: DefaultApiGetAggregatedFileTypesRequest) => [
    "aggregated-file-types",
    params.organizationId,
    params,
  ],
  aggregatedCustomFileTypes: (
    params: DefaultApiGetAggregatedCustomFileTypesRequest
  ) => ["custom-aggregated-file-types", params.organizationId, params],
  subtypeMatchingMetadataBulk: () => ["subtypeMatchingMetadataBulk"],
  factSubtypeSelectionBenchmark: (
    params: DefaultApiGetFactSubtypeSelectionBenchmarkRequest
  ) => ["factSubtypeSelectionBenchmark", params.benchmarkId],
  metadata: {
    submission: (submissionId: string) => [
      "metadata",
      "submission",
      submissionId,
    ],
    factsAndDocuments: (req?: FullMetadataRequest) => {
      return ["metadata", "factsAndDocuments", req];
    },
  },
  benchmarkData: () => ["subtypesBenchmarkData"],
  taxonomiesMappings: (params: DefaultApiGetTaxonomyMappingsRequest) => [
    "taxonomiesMappings",
    params,
  ],
  getLatestIntegrationLogByReportAndOperation: (
    reportId: string,
    operation: string
  ) => ["integrationLogs", reportId, operation],
  exportTemplates: (organisationId: number) => [
    "exportTemplates",
    organisationId,
  ],
  pdsDebuggerFilesData: (reportId: string) => [
    "pdsDebuggerFilesData",
    reportId,
  ],
  submissionLevelExtractedData: (reportId: string) => [
    "submissionLevelExtractedData",
    reportId,
  ],
};
