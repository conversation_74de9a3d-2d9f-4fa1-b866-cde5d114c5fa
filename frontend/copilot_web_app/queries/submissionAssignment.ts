import copilotApiClient from "@legacy/clients/copilotApiClient";
import { useCurrentUser, useMaybeCurrentUser } from "@utils/auth";
import {
  useMutation,
  useQueries,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { queryKeyFactory } from "./queryKey";
import {
  DefaultApiGetSupportUsersRequest,
  DefaultApiAssignFileToSupportUserRequest,
  DefaultApiGetFilesQueueRequest,
  SubmissionPriority,
  SupportUser,
} from "@legacy/api_clients/copilot_api_client";
import axios from "axios";
import { useSnackbar } from "notistack";
import { differenceInMinutes } from "date-fns";
import Reports from "@legacy/models/Reports";
import { useRouter } from "next/router";

export enum SubmissionQueueTabTypeEnum {
  stuck = "stuck",
  files = "files",
  missingFiles = "missing_files",
  invalidFiles = "invalid_files",
  missingData = "missing_data",
  needsClearing = "needs_clearing",
  autoAssignment = "auto_assignment",
  main = "main",
  shadow = "shadow",
  escalated = "escalated",
}

export const useGetSubmissionsQueue = (
  params: Parameters<typeof copilotApiClient.getSubmissionsQueue>[0],
  tabs: SubmissionQueueTabTypeEnum[]
) => {
  const user = useCurrentUser();
  const queryClient = useQueryClient();

  const buildParams = (tab: string) => ({
    ...params,
    stuckOnly: tab === "stuck",
    excludeMissingData: tab === "stuck",
    missingFilesOnly: tab === "missing_files",
    invalidFilesOnly: tab === "invalid_files",
    missingDataOnly: tab === "missing_data",
    needsClearingOnly: tab === "needs_clearing",
    forAutoAssignmentOnly: tab === "auto_assignment",
    shadowOnly: tab === "shadow",
    escalatedOnly: tab === "escalated",
    excludeEscalated: tab === "stuck",
  });

  const queriesResult = useQueries({
    queries: tabs.map((tab) => {
      const queryParams = buildParams(tab);
      return {
        queryKey: queryKeyFactory.submissionsQueue(queryParams),
        queryFn: () => copilotApiClient.getSubmissionsQueue(queryParams),
        refetchInterval: 30 * 1000,
        enabled: user.hasCrossOrganizationAccess(),
      };
    }),
  });

  const reportsPerTab: Record<string, Reports | undefined> = {};
  tabs.forEach((tab) => {
    reportsPerTab[tab] = queryClient.getQueryData(
      queryKeyFactory.submissionsQueue(buildParams(tab))
    );
  });

  return {
    data: reportsPerTab,
    isLoading: queriesResult.some(
      (r) => r.isLoading && r.fetchStatus !== "idle"
    ),
  };
};

export const useGetFilesQueue = (request: DefaultApiGetFilesQueueRequest) => {
  const user = useCurrentUser();

  const query = useQuery(
    queryKeyFactory.filesQueue(request),
    () => copilotApiClient.getFilesQueue(request),
    {
      enabled: user.hasCrossOrganizationAccess(),
    }
  );
  return {
    ...query,
    isLoading: query.isLoading && query.fetchStatus !== "idle",
  };
};

export const useAssignFileToSupportUser = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (request: DefaultApiAssignFileToSupportUserRequest) =>
      copilotApiClient.assignFileToSupportUser(request),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(queryKeyFactory.filesQueue());
      },
    }
  );
};

export const useCreateSubmissionPriority = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({
      submissionId,
      submissionPriority,
    }: {
      submissionId: string;
      submissionPriority: SubmissionPriority;
    }) =>
      copilotApiClient.createOrUpdateSubmissionPriority(submissionId, {
        ...submissionPriority,
        submission_id: submissionId,
      }),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(queryKeyFactory.submissionsQueue());
        await queryClient.invalidateQueries(queryKeyFactory.supportUsers());
      },
    }
  );
};

export const useUpdateSupportUser = () => {
  const queryClient = useQueryClient();
  const snackbar = useSnackbar();

  return useMutation(
    ({
      supportUserId,
      supportUser,
    }: {
      supportUserId: string;
      supportUser: SupportUser;
    }) => copilotApiClient.updateSupportUser(supportUserId, supportUser),
    {
      onError: (error) => {
        if (axios.isAxiosError(error) && error.response?.status === 409) {
          snackbar.enqueueSnackbar(
            "The report is currently being worked on by another user",
            { preventDuplicate: true, variant: "error" }
          );
        }
      },
      onSettled: async () => {
        await queryClient.invalidateQueries(queryKeyFactory.submissionsQueue());
        await queryClient.invalidateQueries(queryKeyFactory.supportUsers());
        await queryClient.invalidateQueries(
          queryKeyFactory.currentSupportUser()
        );
      },
    }
  );
};

export const useGetSupportUsers = (
  params: DefaultApiGetSupportUsersRequest = {}
) => {
  const user = useMaybeCurrentUser();
  const query = useQuery(
    queryKeyFactory.supportUsers(),
    () => copilotApiClient.getSupportUsers(params),
    {
      enabled: !!user?.cross_organization_access,
    }
  );
  return {
    ...query,
    isLoading: query.isLoading && query.fetchStatus !== "idle",
  };
};

export const useGetSubmissionsQueueCount = () => {
  const user = useMaybeCurrentUser();
  const query = useQuery(
    queryKeyFactory.submissionsQueueCount(),
    () => copilotApiClient.getSubmissionsQueueCount(true),
    {
      enabled: !!user?.cross_organization_access,
      refetchInterval: 5 * 60 * 1000,
    }
  );
  return {
    ...query,
    isLoading: query.isLoading && query.fetchStatus !== "idle",
  };
};

export const useGetCurrentSupportUser = () => {
  const user = useMaybeCurrentUser();
  const queryClient = useQueryClient();
  const previousData = queryClient.getQueryData<SupportUser>(
    queryKeyFactory.currentSupportUser()
  );
  const router = useRouter();
  const goToHub = () => router.push(`/`);

  const query = useQuery(
    queryKeyFactory.currentSupportUser(),
    () => copilotApiClient.getCurrentSupportUser(),
    {
      enabled: !!user?.cross_organization_access,
      refetchInterval: 5 * 60 * 1000,
      staleTime: 5 * 60 * 1000,
      onSettled: () => {
        const redirectAfterMinutes = 10;
        if (
          previousData?.last_action_at &&
          differenceInMinutes(
            new Date(),
            new Date(previousData.last_action_at)
          ) >= redirectAfterMinutes
        ) {
          // NW ML subs can take a lot of time and we don't want to redirect the user
          if (user?.organization?.id !== 57) {
            goToHub();
          }
        }
      },
    }
  );
  return {
    ...query,
    isLoading: query.isLoading && query.fetchStatus !== "idle",
  };
};

export const useGetSupportUserOnAction = () => {
  const user = useMaybeCurrentUser();
  const queryClient = useQueryClient();

  return async () => {
    if (!user?.cross_organization_access) {
      return undefined;
    }

    const supportUser = await copilotApiClient.getCurrentSupportUser();
    queryClient.setQueryData(queryKeyFactory.currentSupportUser(), supportUser);
    return supportUser;
  };
};
