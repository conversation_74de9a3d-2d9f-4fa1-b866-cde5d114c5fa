import { queryKeyFactory } from "@queries/queryKey";
import { useQuery } from "@tanstack/react-query";
import copilotApiClient from "@legacy/clients/copilotApiClient";

export const useGetPdsDebuggerFilesDataQuery = (reportId: string) => {
  return useQuery(queryKeyFactory.pdsDebuggerFilesData(reportId), () =>
    copilotApiClient.getPdsDebuggerFilesData(reportId)
  );
};

export const useGetSubmissionLevelExtractedDataByReportIdQuery = (
  reportId: string
) => {
  return useQuery(queryKeyFactory.submissionLevelExtractedData(reportId), () =>
    copilotApiClient.getSubmissionLevelExtractedDataByReportId(reportId)
  );
};
