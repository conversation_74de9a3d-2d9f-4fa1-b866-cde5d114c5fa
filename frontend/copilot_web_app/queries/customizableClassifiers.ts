import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import copilotApiClient from "@legacy/clients/copilotApiClient";
import {
  DefaultApiChangeClassifierVersionActiveRequest,
  DefaultApiCreateCustomizableClassifierV2Request,
  DefaultApiDeleteClassifierVersionRequest,
  DefaultApiUpdateCustomizableClassifierV2Request,
} from "@legacy/api_clients/copilot_api_client";
import { useOrganizationId } from "@utils/auth";
import { queryKeyFactory } from "@queries/queryKey";
import { useSnackbar } from "notistack";
import axios from "axios";

export const useCreateCustomizableClassifierMutation = () => {
  const { enqueueSnackbar } = useSnackbar();

  return useMutation({
    mutationFn: (
      requestParameters: DefaultApiCreateCustomizableClassifierV2Request
    ) => copilotApiClient.createCustomizableClassifierV2(requestParameters),
    onError: (error) => {
      if (axios.isAxiosError(error) && error.response?.status === 422) {
        enqueueSnackbar((error.response?.data as any)?.detail, {
          preventDuplicate: true,
          variant: "error",
        });
      }
    },
  });
};

export const useDeleteCustomizableClassifierV2 = (factSubtypeId: string) => {
  const queryClient = useQueryClient();
  const organizationId = useOrganizationId();

  return useMutation(
    (requestParameters: DefaultApiDeleteClassifierVersionRequest) =>
      copilotApiClient.deleteCustomizableClassifierV2(requestParameters),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(
          queryKeyFactory.newCustomizableClassifiers(
            organizationId,
            factSubtypeId
          )
        );
        await queryClient.invalidateQueries(
          queryKeyFactory.newCustomizableClassifiers(organizationId)
        );
      },
    }
  );
};

export const useUpdateCustomizableClassifierMutation = () => {
  const queryClient = useQueryClient();
  const organizationId = useOrganizationId();

  return useMutation(
    (requestParameters: DefaultApiUpdateCustomizableClassifierV2Request) =>
      copilotApiClient.updateCustomizableClassifierV2(requestParameters),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(
          queryKeyFactory.newCustomizableClassifiers(organizationId)
        );
      },
    }
  );
};

export const useActivateClassifierVersionMutation = (
  factSubtypeId?: string
) => {
  const queryClient = useQueryClient();
  const organizationId = useOrganizationId();

  return useMutation(
    (requestParameters: DefaultApiChangeClassifierVersionActiveRequest) =>
      copilotApiClient.activateClassifierVersion(requestParameters),
    {
      onSettled: async () => {
        await queryClient.invalidateQueries(
          queryKeyFactory.newCustomizableClassifiers(
            organizationId,
            factSubtypeId
          )
        );
      },
    }
  );
};

export const useCustomizableClassifiersV2 = (factSubtypeId?: string) => {
  const organizationId = useOrganizationId();
  return useQuery(
    queryKeyFactory.newCustomizableClassifiers(organizationId, factSubtypeId),
    () =>
      copilotApiClient.getCustomizableClassifiersV2({
        orgId: organizationId,
        factSubtypeId,
      })
  );
};
