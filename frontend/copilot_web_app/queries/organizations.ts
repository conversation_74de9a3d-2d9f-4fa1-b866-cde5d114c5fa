import copilotApiClient from "@legacy/clients/copilotApiClient";
import { useCurrentUser, useOrganizationId } from "@utils/auth";
import { useQuery } from "@tanstack/react-query";
import { queryKeyFactory } from "./queryKey";

export const useGetOrganizations = () => {
  const user = useCurrentUser();

  return useQuery(
    queryKeyFactory.organizations(),
    () => copilotApiClient.getOrganizations(),
    { staleTime: Infinity, enabled: user.isInternalUser() }
  );
};

export const useGetOrganizationById = () => {
  const orgId = useOrganizationId();

  return useQuery(
    queryKeyFactory.organizationById(orgId),
    () => copilotApiClient.getOrganizationById(orgId),
    { staleTime: Infinity }
  );
};

export const useGetOrganizationByProvidedId = (
  orgId?: number,
  disable?: boolean
) => {
  return useQuery(
    queryKeyFactory.organizationById(orgId),
    () =>
      copilotApiClient.getOrganizationById(orgId!, [
        "description",
        "appetite",
        "identity_provider",
        "email_domain",
        "shared_notification_address",
        "for_analysis",
      ]),
    { staleTime: Infinity, enabled: !!orgId && !disable }
  );
};
