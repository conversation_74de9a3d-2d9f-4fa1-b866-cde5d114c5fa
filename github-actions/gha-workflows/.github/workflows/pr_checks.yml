name: "PR checks"

on:
  workflow_call:
  pull_request:
    types: [opened, reopened, edited, synchronize]

jobs:
  check_pr_title:
    name: "Check PR title"
    runs-on: ubuntu-latest
    steps:
      - name: Execute the check
        id: check
        uses: Kalepa/gha-actions/python-exec@main
        with:
          stdin: ${{ github.event.pull_request.title }}
          pip-packages: nltk
          run: |
            import re
            import sys

            import nltk
            from nltk.tokenize import sent_tokenize, word_tokenize

            nltk.download("punkt")
            nltk.download("punkt_tab")
            nltk.download("averaged_perceptron_tagger")
            nltk.download('averaged_perceptron_tagger_eng')

            ERR_GHA_PREFIX = "::error:: "
            GH_REVERT_PREFIX = 'Revert "'
            MAX_MESSAGE_LENGTH = 120
            TICKET_ID_PATTERN = r"\[\w{2,}-\d+\]$"

            stdinput = "\n".join(sys.stdin.readlines()).strip()
            print(f"Analyzing '{stdinput}'")


            def is_imperative(tagged_sent: list, deep_check: bool = False) -> tuple[bool, str, str]:
                """
                Determine if the sentence is written in the imperative mood.

                Args:
                tagged_sent (list): A list of tuples with tokens and their POS tags.
                deep_check (bool, optional): Whether to perform the check with additional context. Defaults to False.

                Returns:
                tuple[bool, str, str]: A tuple containing a boolean indicating if the sentence
                                        is imperative, followed by the first word and its tag.
                """
                if not tagged_sent:
                    return False, "", ""
                first_word, first_tag = tagged_sent[0]
                if first_word.endswith("es"):
                    return False, first_word, "CUS1"
                if first_word in ["bug", "error"]:
                    return False, first_word, "CUS2"
                expected_tags = ["VB", "VBP"]
                if first_tag not in expected_tags and not deep_check:
                    deep_tagged = nltk.pos_tag(["This", "change", "will"] + [token for token, _ in tagged_sent])[3:]
                    deep_result = is_imperative(deep_tagged, True)
                    if deep_result[0]:
                        return deep_result

                return first_tag in expected_tags, first_word, first_tag + ("" if not deep_check else "-DEEP")


            def check_pr_title(message: str) -> tuple[bool, str]:
                """
                Check if the PR title follows the predefined rules.

                Args:
                message (str): The PR title to check.

                Returns:
                tuple[bool, str]: A tuple where the first element is a boolean indicating
                                    whether the message is valid, and the second is a message string.
                """
                if message.startswith(GH_REVERT_PREFIX):
                    return True, "Revert PRs don't need to follow our PR naming rules."

                if len(message) > MAX_MESSAGE_LENGTH:
                    return False, "Your PR title is too long. It shouldn't be longer than 120 characters."

                if not re.search(TICKET_ID_PATTERN, message):
                    return False, f"Your PR title does not end with a valid ticket ID (e.g., '{message} [ENG-12345]')."

                sentences = sent_tokenize(message)
                if not sentences:
                    return False, "Your PR title is empty."
                if len(sentences) > 1:
                    return False, "Your PR title has multiple sentences."

                sentence = sentences[0]
                tokens = word_tokenize(sentence)
                if not tokens:
                    return False, "The sentence is empty."

                if not tokens[0].istitle():
                    return False, f'"{tokens[0]}" does not start with a capital letter.'

                tagged = nltk.pos_tag([token.lower() for token in tokens])
                is_imperative_mood, word, word_type = is_imperative(tagged)
                if not is_imperative_mood:
                    return (
                        True,
                        (
                            f'WARNING: "{word}" is not a present tense verb ({word_type}). To be fully compliant with our'
                            ' guidelines, you should describe your changes in imperative mood, e.g. "Make xyzzy do frotz" instead'
                            ' of "[This patch] makes xyzzy do frotz" or "[I] changed xyzzy to do frotz", as if you are giving'
                            " orders to the codebase to change its behavior."
                        ),
                    )

                return True, f"Your PR title is great ({word_type})."


            check_result, message = check_pr_title(stdinput)
            GITHUB_OUTPUT["check_message"] = message
            GITHUB_OUTPUT["check_executed"] = "true"
            if not check_result:
                print(ERR_GHA_PREFIX + f"\n{ERR_GHA_PREFIX}".join(message.splitlines()))
                GITHUB_OUTPUT["invalid_title"] = "true"
                sys.exit(65)

            print(f"✅ {message}")


      - name: Create a PR comment
        if: |
          always() &&
          steps.check.outcome != 'cancelled' &&
          steps.check.outcome != 'skipped' &&
          fromJSON(steps.check.outputs.result).check_executed == 'true'
        uses: marocchino/sticky-pull-request-comment@52423e01640425a022ef5fd42c6fb5f633a02728
        continue-on-error: true
        with:
          recreate: true
          header: title-check
          message: |
            ### 🕵️ Pull Request Title Check

            ${{ fromJSON(steps.check.outputs.result).invalid_title == 'true' && '❌' || '✅' }} ${{ fromJSON(steps.check.outputs.result).check_message }}

            ℹ️ Check out our [PR and Commit Message Formatting Guide](https://kalepa.atlassian.net/wiki/spaces/EN/pages/2419392557/SOC2+Compliance+and+Development+Practices+Guide#PR-and-Commit-Message-Formatting) for more details.

  check_pr_template_compliance:
    name: "Check PR template compliance"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check if PR template exists
        id: template_check
        run: |
          if [[ -f "docs/pull_request_template.md" ]]; then
            echo "template_exists=true" >> $GITHUB_OUTPUT
            echo "✅ PR template found at docs/pull_request_template.md"
          else
            echo "template_exists=false" >> $GITHUB_OUTPUT
            echo "ℹ️ No PR template found at docs/pull_request_template.md - skipping compliance check"
          fi

      - name: Validate PR template compliance
        if: steps.template_check.outputs.template_exists == 'true'
        id: validate
        uses: Kalepa/gha-actions/python-exec@main
        with:
          stdin: ${{ github.event.pull_request.body }}
          run: |
            import re
            import sys

            ERR_GHA_PREFIX = "::error:: "

            def validate_pr_template_compliance(pr_body: str) -> tuple[bool, list[str]]:
                """
                Validate PR template compliance by checking:
                1. All checkboxes are checked
                2. Tests Performed section has content if it exists

                Args:
                    pr_body (str): The PR description body

                Returns:
                    tuple[bool, list[str]]: (is_valid, list_of_errors)
                """
                errors = []

                if not pr_body or not pr_body.strip():
                    errors.append("PR description is empty")
                    return False, errors

                unchecked_pattern = r'- \[ \]'
                unchecked_matches = re.findall(unchecked_pattern, pr_body)

                if unchecked_matches:
                    errors.append(f"Found {len(unchecked_matches)} unchecked checkbox(es). All checkboxes must be checked before merging.")

                tests_section_pattern = r'###?\s*Tests?\s+Performed\s*(?:\n(.*?)(?=\n###|\n##|\Z)|$)'
                tests_match = re.search(tests_section_pattern, pr_body, re.DOTALL | re.IGNORECASE)

                if tests_match:
                    tests_content = tests_match.group(1) if tests_match.group(1) is not None else ""
                    tests_content = tests_content.strip()
                    content_without_checkboxes = re.sub(r'- \[[x ]\].*?(?:\n|$)', '', tests_content).strip()
                    content_without_headers = re.sub(r'####?\s*(Common|Custom)\s*(?:\n|$)', '', content_without_checkboxes, flags=re.IGNORECASE).strip()
                    final_content = re.sub(r'\s+', ' ', content_without_headers).strip()

                    if not final_content:
                        errors.append("Tests Performed section exists but has no descriptive content. Please describe what tests were performed.")

                return len(errors) == 0, errors

            pr_body = "\n".join(sys.stdin.readlines()).strip()
            print(f"Analyzing PR description (length: {len(pr_body)} characters)")

            is_valid, errors = validate_pr_template_compliance(pr_body)

            GITHUB_OUTPUT["is_valid"] = "true" if is_valid else "false"
            GITHUB_OUTPUT["error_count"] = str(len(errors))
            GITHUB_OUTPUT["errors"] = "\n".join(errors) if errors else ""

            if not is_valid:
                print(ERR_GHA_PREFIX + "PR template compliance check failed:")
                for error in errors:
                    print(ERR_GHA_PREFIX + f"  • {error}")
                sys.exit(1)
            else:
                print("✅ PR template compliance check passed")

      - name: Create PR compliance comment
        if: |
          always() &&
          steps.template_check.outputs.template_exists == 'true' &&
          steps.validate.outcome != 'cancelled' &&
          steps.validate.outcome != 'skipped'
        uses: marocchino/sticky-pull-request-comment@52423e01640425a022ef5fd42c6fb5f633a02728
        continue-on-error: true
        with:
          recreate: true
          header: compliance-check
          message: |
            ### 📋 PR Template Compliance Check

            ${{ fromJSON(steps.validate.outputs.result).is_valid == 'true' && '✅' || '❌' }} **Template Compliance**: ${{ fromJSON(steps.validate.outputs.result).is_valid == 'true' && 'All requirements met' || format('Failed with {0} error(s)', fromJSON(steps.validate.outputs.result).error_count) }}

            ${{ fromJSON(steps.validate.outputs.result).is_valid != 'true' && format('**Errors found:**
            {0}', fromJSON(steps.validate.outputs.result).errors) || '' }}

            ℹ️ This check ensures all checkboxes in the PR template are completed and required sections have content.
