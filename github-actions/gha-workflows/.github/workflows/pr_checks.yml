name: "PR checks"

on:
  workflow_call:

jobs:
  check_pr_title:
    name: "Check PR title"
    runs-on: ubuntu-latest
    steps:
      - name: Execute the check
        id: check
        uses: Kalepa/gha-actions/python-exec@main
        with:
          stdin: ${{ github.event.pull_request.title }}
          pip-packages: nltk
          run: |
            import re
            import sys

            import nltk
            from nltk.tokenize import sent_tokenize, word_tokenize

            nltk.download("punkt")
            nltk.download("punkt_tab")
            nltk.download("averaged_perceptron_tagger")
            nltk.download('averaged_perceptron_tagger_eng')

            ERR_GHA_PREFIX = "::error:: "
            GH_REVERT_PREFIX = 'Revert "'
            MAX_MESSAGE_LENGTH = 120
            TICKET_ID_PATTERN = r"\[\w{2,}-\d+\]$"

            stdinput = "\n".join(sys.stdin.readlines()).strip()
            print(f"Analyzing '{stdinput}'")


            def is_imperative(tagged_sent: list, deep_check: bool = False) -> tuple[bool, str, str]:
                """
                Determine if the sentence is written in the imperative mood.

                Args:
                tagged_sent (list): A list of tuples with tokens and their POS tags.
                deep_check (bool, optional): Whether to perform the check with additional context. Defaults to False.

                Returns:
                tuple[bool, str, str]: A tuple containing a boolean indicating if the sentence
                                        is imperative, followed by the first word and its tag.
                """
                if not tagged_sent:
                    return False, "", ""
                first_word, first_tag = tagged_sent[0]
                if first_word.endswith("es"):
                    return False, first_word, "CUS1"
                if first_word in ["bug", "error"]:
                    return False, first_word, "CUS2"
                expected_tags = ["VB", "VBP"]
                if first_tag not in expected_tags and not deep_check:
                    deep_tagged = nltk.pos_tag(["This", "change", "will"] + [token for token, _ in tagged_sent])[3:]
                    deep_result = is_imperative(deep_tagged, True)
                    if deep_result[0]:
                        return deep_result

                return first_tag in expected_tags, first_word, first_tag + ("" if not deep_check else "-DEEP")


            def check_pr_title(message: str) -> tuple[bool, str]:
                """
                Check if the PR title follows the predefined rules.

                Args:
                message (str): The PR title to check.

                Returns:
                tuple[bool, str]: A tuple where the first element is a boolean indicating
                                    whether the message is valid, and the second is a message string.
                """
                if message.startswith(GH_REVERT_PREFIX):
                    return True, "Revert PRs don't need to follow our PR naming rules."

                if len(message) > MAX_MESSAGE_LENGTH:
                    return False, "Your PR title is too long. It shouldn't be longer than 120 characters."

                if not re.search(TICKET_ID_PATTERN, message):
                    return False, f"Your PR title does not end with a valid ticket ID (e.g., '{message} [ENG-12345]')."

                sentences = sent_tokenize(message)
                if not sentences:
                    return False, "Your PR title is empty."
                if len(sentences) > 1:
                    return False, "Your PR title has multiple sentences."

                sentence = sentences[0]
                tokens = word_tokenize(sentence)
                if not tokens:
                    return False, "The sentence is empty."

                if not tokens[0].istitle():
                    return False, f'"{tokens[0]}" does not start with a capital letter.'

                tagged = nltk.pos_tag([token.lower() for token in tokens])
                is_imperative_mood, word, word_type = is_imperative(tagged)
                if not is_imperative_mood:
                    return (
                        True,
                        (
                            f'WARNING: "{word}" is not a present tense verb ({word_type}). To be fully compliant with our'
                            ' guidelines, you should describe your changes in imperative mood, e.g. "Make xyzzy do frotz" instead'
                            ' of "[This patch] makes xyzzy do frotz" or "[I] changed xyzzy to do frotz", as if you are giving'
                            " orders to the codebase to change its behavior."
                        ),
                    )

                return True, f"Your PR title is great ({word_type})."


            check_result, message = check_pr_title(stdinput)
            GITHUB_OUTPUT["check_message"] = message
            GITHUB_OUTPUT["check_executed"] = "true"
            if not check_result:
                print(ERR_GHA_PREFIX + f"\n{ERR_GHA_PREFIX}".join(message.splitlines()))
                GITHUB_OUTPUT["invalid_title"] = "true"
                sys.exit(65)

            print(f"✅ {message}")


      - name: Create a PR comment
        if: |
          always() &&
          steps.check.outcome != 'cancelled' &&
          steps.check.outcome != 'skipped' &&
          fromJSON(steps.check.outputs.result).check_executed == 'true'
        uses: marocchino/sticky-pull-request-comment@52423e01640425a022ef5fd42c6fb5f633a02728
        continue-on-error: true
        with:
          message: |
            ### 🕵️ Pull Request Title Check

            ${{ fromJSON(steps.check.outputs.result).invalid_title == 'true' && '❌' || '✅' }} ${{ fromJSON(steps.check.outputs.result).check_message }}

            ℹ️ Check out our [PR and Commit Message Formatting Guide](https://kalepa.atlassian.net/wiki/spaces/EN/pages/2419392557/SOC2+Compliance+and+Development+Practices+Guide#PR-and-Commit-Message-Formatting) for more details.
