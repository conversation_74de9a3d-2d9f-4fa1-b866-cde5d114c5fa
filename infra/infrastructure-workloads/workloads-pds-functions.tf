module "workload_pds_functions" {
  source = "./modules/workload"
  providers = {
    aws.us_east_1     = aws.us_east_1
    aws.dns_us_east_1 = aws.dns_us_east_1
  }
  workload_name      = "pds-functions"
  repository         = "pds-functions"
  tfc_workspace_name = "pds-functions"
  regions = [
    "us-east-1",
  ]

  ecs_config = {
    iam_policies_json_by_region = {
      us-east-1 = []
    }
    iam_policies_arns_by_region = {
      us-east-1 = []
    }
    nginx_sidecar_image = local.config.nginx_sidecar_blue_image
    ecr_container_names = [
      "pds-functions"
    ]
  }

  lambda_config = {
    allow_egress_to_alb = true
    rds_access          = false
    iam_policies_json_by_region = {
      us-east-1 = []
    }
    iam_policies_arns_by_region = {
      us-east-1 = []
    }
    public_internet_access_22    = false
    public_internet_access_443   = true
    public_internet_access_80    = false
    public_internet_access_8444  = false
    public_internet_access_993   = false
    public_internet_access_465   = false
    public_internet_access_587   = false
    public_internet_access_22225 = false
    public_internet_access_28601 = false
    uses_websocket_api           = false
    allow_ecr_access             = true
    accessible_environment_secrets = {
      google-drive-spreadsheet-credentials = "/google/drive-spreadsheet/credentials"
      azure-excel-sov-app-credentials      = "/azure/excel-sov-app/credentials"
    }
  }
  uses_serverless = true
  uses_elasticache = [
    local.elasticache_clusters_for_regions["cache"],
  ]
  common_workloads_variables = local.common_workloads_variables
  shared_environment_secrets = module.secrets.shared_secrets_by_account[data.aws_caller_identity.current.account_id]
}
