locals {
  # An AWS Secret will be created in every deployment environment for every
  # secret defined in this file, with an appropriate suffix. For example,
  # if the entry here is "/some/secret", three AWS Secrets will be created:
  # "/some/secret/dev", "/some/secret/stage", and "/some/secret/prod". Each
  # of them will be shared win an appropriate workload account. If you want
  # to share with additional AWS accounts, add the account ID or OU ARN to
  # the `additional_entities_to_share_with` list.
  config_global_secrets = {
    "/github/api-key" = {
      description                       = "github.com API KEY"
      additional_entities_to_share_with = []
    }
    "/amplitude/api-key" = {
      description                       = "amplitude.com API KEY"
      additional_entities_to_share_with = []
    }
    "/launch-darkly/api-key" = {
      description                       = "launchdarkly.com API KEY"
      additional_entities_to_share_with = []
    }
    "/sensible/api-key" = {
      description                       = "Sensible API KEY"
      additional_entities_to_share_with = []
    }
    "/sensible/file-key" = {
      description                       = "Sensible File KEY"
      additional_entities_to_share_with = []
    }
    "/azure/kalepa-form-recognizer-us-central/endpoint" = {
      description                       = "Endpoint to kalepa-form-recognizer-us-central resource in Azure"
      additional_entities_to_share_with = []
    }
    "/azure/kalepa-form-recognizer-us-central/api-key" = {
      description                       = "API key to kalepa-form-recognizer-us-central resource in Azure"
      additional_entities_to_share_with = []
    }
    "/azure/supplemental-forms-construction-gc/endpoint" = {
      description                       = "Endpoint to suppformsconstruction resource in Azure"
      additional_entities_to_share_with = []
    }
    "/azure/supplemental-forms-construction-gc/api-key" = {
      description                       = "API key to suppformsconstruction resource in Azure"
      additional_entities_to_share_with = []
    }
    "/azure/read/endpoint" = {
      description                       = "Endpoint to Azure Read API (OCR)"
      additional_entities_to_share_with = []
    }
    "/azure/read/api-key" = {
      description                       = "API key to Azure Read API (OCR)"
      additional_entities_to_share_with = []
    }
    "/azure/blob/fr-data-us-central/conn-string" = {
      description                       = "Connection string to frdatauscent blob storage in Azure"
      additional_entities_to_share_with = []
    }
    "/slack/token" = {
      description                       = "Slack token allowing to interact with Slack API"
      additional_entities_to_share_with = []
    }
    "/label-studio/token" = {
      description                       = "Label studio API token to interact with its API"
      additional_entities_to_share_with = []
    }
    "/label-studio/webhook-token" = {
      description                       = "Token to authenticate incoming webhooks from Label studio"
      additional_entities_to_share_with = []
    }
    "/gmail/forwarder/credentials" = {
      description                       = "Gmail credentials for email forwarder"
      additional_entities_to_share_with = []
    }
    "/mode/bridge-connector" = {
      description                       = "Mode Bridge connection key and details"
      additional_entities_to_share_with = []
    }
    "/cloudmersive/free/api-key" = {
      description                       = "Cloudmersive Free Tier API Key"
      additional_entities_to_share_with = []
    }
    "/sam/api-key" = {
      description                       = "SAM Gov API Key -> https://kalepa.1password.com/vaults/etsgqezglwvmd5hw6ttvjbsgmm/allitems/w55uj6hum5xdhrui35pv3erl74"
      additional_entities_to_share_with = []
    }
    "/arch/api-config" = {
      description                       = "Arch API config with credentials"
      additional_entities_to_share_with = []
    }
    "/openai/api-key" = {
      description                       = "API Key to Open AI"
      additional_entities_to_share_with = []
    }
    "/azure/openai/api-key" = {
      description                       = "API Key to Open AI on Azure"
      additional_entities_to_share_with = []
    }
    "/usps/user-id" = {
      description                       = "User ID to use with US Postal service web tool kit API"
      additional_entities_to_share_with = []
    }
    "/terraform-cloud-token/organization" = {
      description                       = "Organization-wide token to authenticate with Terraform Cloud"
      additional_entities_to_share_with = []
    }
    "/sftp/ssh-key/kalepa-user" = {
      description                       = "SSH Keys of kalepa-env user for SFTP"
      additional_entities_to_share_with = []
    }
    "/sftp/ssh-key/auto-ally-user" = {
      description                       = "SSH Keys of auto-ally-env user for SFTP"
      additional_entities_to_share_with = []
    }
    "/sftp/ssh-key/arch-user" = {
      description                       = "SSH Keys of arch-env user for SFTP"
      additional_entities_to_share_with = []
    }
    "/sftp/ssh-key/admiral-user" = {
      description                       = "SSH Keys of arch-env user for SFTP"
      additional_entities_to_share_with = []
    }
    "/sftp/ssh-key/paragon-user" = {
      description                       = "SSH Keys of paragon-env user for SFTP"
      additional_entities_to_share_with = []
    }
    "/sftp/ssh-key/nationwide-user" = {
      description                       = "SSH Keys of nationwide-env user for SFTP"
      additional_entities_to_share_with = []
    }
    "/sftp/ssh-key/kalepa-power" = {
      description                       = "SSH Keys of kalepa-power user for SFTP"
      additional_entities_to_share_with = []
    }
    "/proxy/fmcsa" = {
      description                       = "Proxy credentials for exclusive FMCSA proxy"
      additional_entities_to_share_with = []
    }
    "/fmcsa/api-key" = {
      description                       = "FMCSA API web key"
      additional_entities_to_share_with = []
    }
    "/proxy/unicourt" = {
      description                       = "Proxy for unicourt.com"
      additional_entities_to_share_with = []
    }
    "/paragon/ims/credentials" = {
      description                       = "Bag for Paragon IMS credentials, for various environments"
      additional_entities_to_share_with = []
    }
    "/paragon/rest/credentials" = {
      description                       = "Bag for Paragon Rest API credentials, for various environments"
      additional_entities_to_share_with = []
    }
    "/serper/api-key" = {
      description                       = "API key for serper-dev"
      additional_entities_to_share_with = []
    }
    "/proxy/news-worker" = {
      description                       = "Proxy credentials for news-worker"
      additional_entities_to_share_with = []
    }
    "/proxied-ingress/ssh-key" = {
      description                       = "Secret holding private SSH key required to set up proxied ingress SSH tunnels"
      additional_entities_to_share_with = []
    }
    "/sendgrid/webhook-key" = {
      description                       = "Secret holding public verification key used for sendgrid webhooks"
      additional_entities_to_share_with = []
    }
    "/here/api-key" = {
      description                       = "API key for HERE. Managed at https://platform.here.com/admin/apps"
      additional_entities_to_share_with = []
    }
    "/datadog/app-key" = {
      description                       = "Datadog APP Key"
      additional_entities_to_share_with = []
    }
    "/auth0/client-id" = {
      description = "Auth0 Client ID"
      additional_entities_to_share_with = [
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_prod,
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_stage,
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_dev
      ]
    }
    "/auth0/client-secret" = {
      description = "Auth0 Client Secret"
      additional_entities_to_share_with = [
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_prod,
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_stage,
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_dev
      ]
    }
    "/auth0-m2m/client-id" = {
      description = "Auth0 M2M Client ID"
      additional_entities_to_share_with = [
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_prod,
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_stage,
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_dev
      ]
    }
    "/auth0-m2m/client-secret" = {
      description = "Auth0 M2M Client Secret"
      additional_entities_to_share_with = [
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_prod,
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_stage,
        data.terraform_remote_state.organization.outputs.account_ids_by_key.workloads_dev
      ]
    }
    "/redshift/users/mode" = {
      description                       = "Secret holding Redshift 'mode' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/redshift/users/dms" = {
      description                       = "Secret holding Redshift 'dms' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/redshift/users/qs-management-dashboard" = {
      description                       = "Secret holding Redshift 'qs-management-dashboard' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/redshift/users/power-engineer" = {
      description                       = "Secret holding Redshift 'power-engineer' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/redshift/users/redshift-api" = {
      description                       = "Secret holding Redshift 'redshift-api' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/redshift/users/recommendations" = {
      description                       = "Secret holding Redshift 'recommendations' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/redshift/users/ingestion" = {
      description                       = "Secret holding Redshift 'ingestion' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/redshift/users/warehouse-builder" = {
      description                       = "Secret holding Redshift 'warehouse-builder' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/redshift/users/insights" = {
      description                       = "Secret holding Redshift 'insights' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/redshift/users/copilot-workers" = {
      description                       = "Secret holding Redshift 'copilot_workers' user and cluster credentials"
      additional_entities_to_share_with = []
    }
    "/nationwide/kalepa-certificates" = {
      description                       = "Secret holding root, intermediate and client certificates and keys for Kalepa-to-Nationwide communication"
      additional_entities_to_share_with = []
    }
    "/nationwide/api-config" = {
      description                       = "Nationwide API config with credentials"
      additional_entities_to_share_with = []
    }
    "/nationwide/smtp-relay-credentials" = {
      description                       = "Nationwide username, password and hostnames for SMTP relay"
      additional_entities_to_share_with = []
    }
    "/perplexity/api-key" = {
      description                       = "https://www.perplexity.ai/ API Key"
      additional_entities_to_share_with = []
    }
    "/anthropic/api-key" = {
      description                       = "https://console.anthropic.com/ API Key"
      additional_entities_to_share_with = []
    }
    "/google-ai-studio/api-key" = {
      description                       = "https://ai.google.dev/ API Key"
      additional_entities_to_share_with = []
    }
    "/workers-comp-bureau-ca/primary-key" = {
      description                       = "https://api.wcirb.com/WCUnderwriting-SOAP/ Primary Key"
      additional_entities_to_share_with = []
    }
    "/workers-comp-bureau-ca/secondary-key" = {
      description                       = "https://api.wcirb.com/WCUnderwriting-SOAP/ Secondary Key"
      additional_entities_to_share_with = []
    }
    "/paragon/psp/hazard-hub/api-key" = {
      description                       = "API Key for Paragon HH"
      additional_entities_to_share_with = []
    }
    "/kalepi/users/kalepa" = {
      description                       = "Secret holding KalePi's 'kalepa' user password"
      additional_entities_to_share_with = []
    }
    "/kalepapp/slack/signing-secret" = {
      description                       = "Slack signing secret for Kalepapp"
      additional_entities_to_share_with = []
    }
    "/kalepapp/github/secret" = {
      description                       = "GitHub secret for Kalepapp"
      additional_entities_to_share_with = []
    }
    "/kalepapp/slack/token" = {
      description                       = "Slack token for Kalepapp"
      additional_entities_to_share_with = []
    }
    "/conifer/sftp/credentials" = {
      description                       = "SFTP credentials for Conifer"
      additional_entities_to_share_with = []
    }
    "/conifer/finys/credentials" = {
      description                       = "Bag for Conifer Finys credentials, for various environments"
      additional_entities_to_share_with = []
    }
    "/arize/space-id" = {
      description                       = "Arize Space ID"
      additional_entities_to_share_with = []
    }
    "/arize/api-key" = {
      description                       = "Arize API Key"
      additional_entities_to_share_with = []
    }
    "/coherent/copilot/api-key" = {
      description                       = "Coherent API Key"
      additional_entities_to_share_with = []
    }
    "/autoblocks/ingestion-key" = {
      description                       = "Autoblocks Ingestion Key"
      additional_entities_to_share_with = []
    }
    "/osha/api-v4-key" = {
      description                       = "OSHA API v4 Keys for on demand calls (comma separated) - https://dataportal.dol.gov/api-keys"
      additional_entities_to_share_with = []
    }
    "/osha/batch-api-v4-key" = {
      description                       = "OSHA API v4 Keys for batch calls (comma separated)  - https://dataportal.dol.gov/api-keys"
      additional_entities_to_share_with = []
    }
    "/aru/surefyre/credentials" = {
      description                       = "Bag for ARU SureFyre credentials, for various environments"
      additional_entities_to_share_with = []
    }
    "/aru/hazard-hub/api-key" = {
      description                       = "API Key for ARU HH"
      additional_entities_to_share_with = []
    }
    "/paragon/prometrix/credentials" = {
      description                       = "Bag for Paragon Prometrix credentials, for various environments"
      additional_entities_to_share_with = []
    }
    "/admiral-bot/slack-token" = {
      description                       = "Slack token for Admiral Bot"
      additional_entities_to_share_with = []
    }
    "/admiral-bot/slack-signing-secret" = {
      description                       = "Slack signing secret for Admiral Bot"
      additional_entities_to_share_with = []
    }
    "/fleet/license-key" = {
      description                       = "Fleet license key"
      additional_entities_to_share_with = []
    }
    "/github/init-mdm-user/token" = {
      description                       = "API Token for the GitHub user used by initial MDM ops on endpoints"
      additional_entities_to_share_with = []
    }
    "/fleet/api-key" = {
      description                       = "Fleet API Key"
      additional_entities_to_share_with = []
    }

    "/paragon/riskmeter/credentials" = {
      description                       = "Bag for Paragon Riskmeter credentials, for various environments"
      additional_entities_to_share_with = []
    }
    "/google/document-ai/credentials" = {
      description                       = "Credentials to Google Document AI Cloud"
      additional_entities_to_share_with = []
    }
    "/together-ai/api-key" = {
      description                       = "Together AI API Key"
      additional_entities_to_share_with = []
    }
    "/x-ai/api-key" = {
      description                       = "X AI API Key"
      additional_entities_to_share_with = []
    }
    "/google/drive-spreadsheet/credentials" = {
      description                       = "Google Drive credentials for Google Sheets"
      additional_entities_to_share_with = []
    }
    "/reducto/api-key" = {
      description                       = "Reducto API Key"
      additional_entities_to_share_with = []
    }
    "/azure/excel-sov-app/credentials" = {
      description                       = "Azure App Credentials used to manage Excel files"
      additional_entities_to_share_with = []
    }
  }
}
